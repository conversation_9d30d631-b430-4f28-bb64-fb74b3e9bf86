package org.jeecg.common.system.api;

import java.util.Map;

public interface ISysJobService {
    /** 开启新的定时任务
     * @param triggerKey
     * @param cronExpression
     * @param variables
     */
    void startJob(String triggerKey,String groupId, String cronExpression, Map<String,Object> variables);

    /** 重启定时任务
     * @param triggerKey
     * @param cronExpression
     * @param variables
     */
    void restartJob(String triggerKey,String groupId, String cronExpression, Map<String,Object> variables);

    /** 移除定时任务
     * @param triggerKey
     */
    void removeJob(String triggerKey,String groupId);

    /** 暂停定时任务
     * @param triggerKey
     */
    void pauseTrigger(String triggerKey,String groupId);

    /** 恢复定时任务
     * @param triggerKey
     */
    void resumeTrigger(String triggerKey,String groupId);
}
