package org.jeecg.common.message.websocket;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.common.util.common.RedisUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.message.websocket.factory.WebSocketHandlerFactory;
import org.jeecg.common.message.websocket.handler.IWebSocketHandler;
import org.jeecg.common.message.webssh.handler.WebSSHHandler;
import org.jeecg.common.mq.RedisMqInit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.websocket.OnClose;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * <AUTHOR>
 * @Date 2019/11/29 9:41
 * @Description: 此注解相当于设置访问URL
 */
@Component
@Slf4j
@ServerEndpoint("/websocket/{userId}") //此注解相当于设置访问URL
public class WebSocket {

    private static WebSocketHandlerFactory webSocketHandlerFactory;

    private Session session;

    private IWebSocketHandler webSocketHandler;

    private static final CopyOnWriteArraySet<WebSocket> webSockets = new CopyOnWriteArraySet<>();

    private static final Map<String, Map<String, Session>> userSessions = new ConcurrentHashMap<>();

    @Getter
    private static final Map<String, Session> sessionPool = new HashMap<>();

    @Autowired
    public void setWebSocketHandlerFactory(WebSocketHandlerFactory webSocketHandlerFactory) {
        WebSocket.webSocketHandlerFactory = webSocketHandlerFactory;
    }

    @OnOpen
    public void onOpen(Session session, @PathParam(value = "userId") String userId) {
        //System.out.println("      ------------------------ WebSocket.onOpen() -------------------------------     ");
        try {
            this.session = session;
            webSockets.add(this);
            sessionPool.put(userId, session);
            userSessions.computeIfAbsent(userId, k -> new ConcurrentHashMap<>()).put(session.getId(), session);
            RedisMqInit.getRedisUtils().hset(CacheConstant.SYS_WS_CACHE, userId, RedisMqInit.getCONSUMER());
            // MqInitConfig.getRedisStreamUtil().createStream(CacheConstant.STREAM_PREFIX + "websocket:" + consumer, consumer);
            if (userId.contains("_")) {
                //此处是在前端未修改的情况下的临时获取消息类型的方法，之后将改为使用@PathParam获取
                String[] split = userId.split("_");
                String code = split[0];
                String messageType = split[1];
                webSocketHandler = webSocketHandlerFactory.getWebSocketHandler(messageType);
                if (webSocketHandler != null) {
                    webSocketHandler.onOpenHandler(code, session);
                }
            }
            log.info("【websocket消息】有新的连接，总数为:" + webSockets.size());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @OnClose
    public void onClose(Session session, @PathParam("userId") String userId) {
        //System.out.println("      ------------------------ WebSocket.onClose() -------------------------------     ");
        try {
            if (webSocketHandler != null) {
                webSocketHandler.onCloseHandler(this.session);
            }
            webSockets.remove(this);
            log.info("【websocket消息】连接断开，总数为:" + webSockets.size());
        } catch (Exception e) {
        }
        // 当连接关闭时，将连接从用户映射中移除
        Map<String, Session> userConnections = userSessions.get(userId);
        if (userConnections != null) {
            userConnections.remove(session.getId());
            if (userConnections.isEmpty()) {
                userSessions.remove(userId);
            }
        }
    }

    @OnMessage
    public void onMessage(String message, Session session) {
        //接收到前端心跳检测后向前端发送消息
        if (StringUtils.equals("HeartBeat", message)) {
            session.getAsyncRemote().sendText(message);
        }
        if (webSocketHandler != null) {
            try {
                webSocketHandler.onMessageHandler(message, this.session);
            } catch (Exception e) {
                log.error("WebSocket消息处理异常", e);
                try {
                    session.getBasicRemote().sendText(e.getMessage());
                    webSocketHandler.onCloseHandler(this.session);
                    this.session.close();
                } catch (IOException ex) {
                    throw new RuntimeException(ex);
                }
            }
        }
    }

    // 此为广播消息
    public static void sendAllMessage(String message) {
        log.info("【WebSocket消息】广播消息：" + message);
        for (WebSocket webSocket : webSockets) {
            if (webSocket.webSocketHandler instanceof WebSSHHandler) {
                continue;
            }
            try {
                if (webSocket.session.isOpen()) {
                    synchronized (webSocket.session) {
                        webSocket.session.getBasicRemote().sendText(message);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    public static void sendMessage(Session session, String message) {
        try {
            log.info("【websocket消息】 单点消息:" + message);
            session.getAsyncRemote().sendText(message);
        } catch (Exception e) {
            log.error("WebSocket消息发送异常", e);
        }
    }

    // 此为单点消息
    public static void sendOneMessage(String userId, String message) {
        Session session = sessionPool.get(userId);
        if (session != null && session.isOpen()) {
            sendMessage(session, message);
        } else {
            final RedisUtils redisUtils = RedisMqInit.getRedisUtils();
            if (redisUtils.hHasKey(CacheConstant.SYS_WS_CACHE, userId)) {
                final String key = String.valueOf(RedisMqInit.getRedisUtils().hget(CacheConstant.SYS_WS_CACHE, userId));
                JSONObject data = new JSONObject();
                data.put("userId", userId);
                data.put("message", getMessage(message));
                RedisMqInit.getRedisMq()
                        .publish(CacheConstant.STREAM_PREFIX + "websocket:" + key, data);
            }
        }
    }

    // 此为单点消息(多人)
    public static void sendMoreMessage(String[] userIds, String message) {
        for (String userId : userIds) {
            sendOneMessage(userId, message);
        }

    }

    // 此为单点消息(多人)
    public static void sendMoreMessageByUserMap(String[] userIds, String message) {
        for (String userId : userIds) {
            sendOneMessageByUserMap(userId, message);
        }

    }

    //此为单点消息通过Map<String, Map<String, Session>> 发送消息
    public static void sendOneMessageByUserMap(String userId, String message) {
        Map<String, Session> userConnections = userSessions.get(userId);
        if (userConnections != null) {
            for (Session userSession : userConnections.values()) {
                try {
                    userSession.getAsyncRemote().sendText(message);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } else {
            final RedisUtils redisUtils = RedisMqInit.getRedisUtils();
            if (redisUtils.hHasKey(CacheConstant.SYS_WS_CACHE, userId)) {
                final String key = String.valueOf(RedisMqInit.getRedisUtils().hget(CacheConstant.SYS_WS_CACHE, userId));
                JSONObject data = new JSONObject();
                data.put("userId", userId);
                data.put("message", getMessage(message));
                RedisMqInit.getRedisMq()
                        .publish(CacheConstant.STREAM_PREFIX + "websocket:" + key, data);
            }
        }
    }

    private static Object getMessage(String message) {
        try {
            return JSON.parseObject(message);
        } catch (Exception e) {
            return message;
        }
    }

}
