package org.jeecg.common.mq;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.common.mq.stream.Streams;
import org.springframework.data.redis.connection.stream.MapRecord;
import org.springframework.data.redis.stream.StreamListener;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @date 2023/10/10
 */
public interface RedisMq {

    AtomicBoolean atomicBoolean = new AtomicBoolean(false);

    /**
     * 发布事件（发布消息）
     *
     * @param streams 事件
     * @param content 消息体
     */
    default void publish(Streams streams, JSONObject content) {
        publish(streams.getValue().getName(), content);
    }

    /**
     * 发布事件（发布消息）
     *
     * @param streamKey 事件
     * @param content   消息体
     */
    void publish(String streamKey, JSONObject content);

    /**
     * 订阅方法
     *
     * @param listener // 事件监听
     * @param streams  // 话题
     */
    void subscribe(StreamListener<String, MapRecord<String, String, String>> listener, Streams streams); //订阅
}
