package org.jeecg.common.system.base.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.util.ExcelUtilJeecg;
import org.jeecg.common.system.util.ZipEncryUtilsJeecg;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.entity.enmus.ExcelType;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Description: Controller基类
 * @Author: <EMAIL>
 * @Date: 2019-4-21 8:13
 * @Version: 1.0
 */
@Slf4j
public class JeecgController<T, S extends IService<T>> {
    @Autowired
    protected  S service;
    @Autowired
    private ZipEncryUtilsJeecg zipEncryUtils;

    /**
     * 导出excel
     *
     * @param request
     */
    protected ModelAndView exportXls(HttpServletRequest request, T object, Class<T> clazz, String title) {
        // Step.1 组装查询条件
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        QueryWrapper<T> queryWrapper = QueryGenerator.initQueryWrapper(object, request.getParameterMap());

        // Step.2 获取导出数据
        List<T> pageList = service.list(queryWrapper);
        List<T> exportList = null;

        // 过滤选中数据
        String selections = request.getParameter("selections");
        if (oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            exportList = pageList.stream().filter(item -> selectionList.contains(getId(item))).collect(Collectors.toList());
        } else {
            exportList = pageList;
        }

        // Step.3 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, title); //此处设置的filename无效 ,前端会重更新设置一下
        mv.addObject(NormalExcelConstants.CLASS, clazz);
        try {
            mv.addObject(NormalExcelConstants.PARAMS, new ExportParams(title + "报表", title));
        } catch (Exception e) {
            e.printStackTrace();
        }
        mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
        return mv;
    }

    //导出加密
    protected void exportXlsZip(HttpServletRequest request, HttpServletResponse response, T object, Class<T> clazz, String title, String pwd, Boolean isEncry) {

        QueryWrapper<T> queryWrapper = QueryGenerator.initQueryWrapper(object, request.getParameterMap());
        if (title.equals("终端组织机构报表")) {
            queryWrapper.ne("name", "全部单位");
        }
        // Step.2 获取导出数据
        List<T> pageList = service.list(queryWrapper);
        List<T> exportList = null;

        // 过滤选中数据
        String selections = request.getParameter("selections");
        if (oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            exportList = pageList.stream().filter(item -> selectionList.contains(getId(item))).collect(Collectors.toList());
        } else {
            exportList = pageList;
        }
        Collections.reverse(exportList);
        zipPwd(response, clazz, title, pwd, isEncry, exportList);
    }

    protected List<T> selectionsFilter(HttpServletRequest request,List<T> list){
        // 过滤选中数据
        String selections = request.getParameter("selections");
        if (oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            list = list.stream().filter(item -> selectionList.contains(getId(item))).collect(Collectors.toList());
        }
        Collections.reverse(list);
        return list;
    }

    protected void saveXls(HttpServletRequest request, HttpServletResponse response
            , Class<T> clazz, String title, String pwd, Boolean isEncry, QueryWrapper<T> queryWrapper){
        // 获取导出数据
        List<T> pageList = service.list(queryWrapper);
        List<T> saveList = null;
        // 过滤选中数据
        String selections = request.getParameter("selections");
        if (oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            saveList = pageList.stream().filter(item -> selectionList.contains(getId(item))).collect(Collectors.toList());
        } else {
            saveList = pageList;
        }
        Collections.reverse(saveList);//实现对list的降序排序
        zipPwd(response, clazz, title, pwd, isEncry, saveList);
    }

    /**
     * Excel数据带有数据库字段条件限制的导出(加密格式)
     *
     * @param request
     * @param response
     * @param clazz
     * @param title
     * @param pwd
     * @param isEncry
     */
    protected void exportXlsZip(HttpServletRequest request, HttpServletResponse response
            , Class<T> clazz, String title, String pwd, Boolean isEncry, QueryWrapper<T> queryWrapper) {
        // 获取导出数据
        List<T> pageList = service.list(queryWrapper);
        List<T> exportList = null;
        // 过滤选中数据
        String selections = request.getParameter("selections");
        if (oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            exportList = pageList.stream().filter(item -> selectionList.contains(getId(item))).collect(Collectors.toList());
        } else {
            exportList = pageList;
        }
        Collections.reverse(exportList);
        zipPwd(response, clazz, title, pwd, isEncry, exportList);
    }

    protected void zipPwd(HttpServletResponse response, Class<T> clazz, String title, String pwd, Boolean isEncry, List<T> exportList) {
        ExportParams exportParams1 = new ExportParams();
        exportParams1.setSheetName(title);
        Map<String, Object> deptDataMap = new HashMap<>(4);
        // title的参数为ExportParams类型
        deptDataMap.put("title", exportParams1);
        // 模版导出对应得实体类型
        deptDataMap.put("entity", clazz);
        // sheet中要填充得数据
        Collections.reverse(exportList);
        deptDataMap.put("data", exportList);

        List<Map<String, Object>> sheetsList = new ArrayList<>();
        sheetsList.add(deptDataMap);
        // 执行方法
        Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
        if (isEncry == null || pwd == null) {
            ExcelUtilJeecg.downLoadExcel(title + ".xls", response, workbook);
        } else if (isEncry) {
            try {
                zipEncryUtils.excelEncry(title + ".xls", response, workbook, pwd);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("导出加密文件报错！", e);
            }
        } else {
            ExcelUtilJeecg.downLoadExcel(title + ".xls", response, workbook);
        }
    }

    protected void zipPwdWithExportFields(HttpServletResponse response, Class<T> clazz, String title, String pwd, Boolean isEncry, List<T> exportList, String exportFieldStr) {
        ExportParams exportParams = new org.jeecgframework.poi.excel.entity.ExportParams();
        exportParams.setSheetName(title);
        String[] exportFields = null;
        if (StringUtils.isNotEmpty(exportFieldStr)) {
            exportFields = exportFieldStr.split(",");
        }

        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, clazz, exportList, exportFields);
        if (isEncry == null || pwd == null) {
            ExcelUtilJeecg.downLoadExcel(title + ".xls", response, workbook);
        } else if (isEncry) {
            try {
                zipEncryUtils.excelEncry(title + ".xls", response, workbook, pwd);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("导出加密文件报错！", e);
            }
        } else {
            ExcelUtilJeecg.downLoadExcel(title + ".xls", response, workbook);
        }
    }

    protected ModelAndView exportXlsDept(HttpServletRequest request, T object, Class<T> clazz, String title) {
        // Step.1 组装查询条件
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        QueryWrapper<T> queryWrapper = QueryGenerator.initQueryWrapper(object, request.getParameterMap());

        // Step.2 获取导出数据
        queryWrapper.ne("name", "全部单位");
        List<T> pageList = service.list(queryWrapper);
        List<T> exportList = null;

        // 过滤选中数据
        String selections = request.getParameter("selections");
        if (oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            exportList = pageList.stream().filter(item -> selectionList.contains(getId(item))).collect(Collectors.toList());
        } else {
            exportList = pageList;
        }

        // Step.3 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, title); //此处设置的filename无效 ,前端会重更新设置一下
        mv.addObject(NormalExcelConstants.CLASS, clazz);
        try {
            mv.addObject(NormalExcelConstants.PARAMS, new ExportParams(title + "报表", title));
        } catch (Exception e) {
            e.printStackTrace();
        }
        mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
        return mv;

    }

    /**
     * 根据权限导出excel，传入导出字段参数
     *
     * @param request
     */
    protected ModelAndView exportXls(HttpServletRequest request, T object, Class<T> clazz, String title, String exportFields) {
        try {
            ModelAndView mv = this.exportXls(request, object, clazz, title);
        } catch (Exception e) {
            e.printStackTrace();
        }
        ModelAndView mv = this.exportXls(request, object, clazz, title);
        mv.addObject(NormalExcelConstants.EXPORT_FIELDS, exportFields);
        return mv;
    }

    /**
     * 获取对象ID
     *
     * @return
     */
    private String getId(T item) {
        try {
            return PropertyUtils.getProperty(item, "id").toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    protected Result<?> importExcel(HttpServletRequest request, HttpServletResponse response, Class<T> clazz) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(2);
            params.setNeedSave(true);
            try {
                List<T> list = ExcelImportUtil.importExcel(file.getInputStream(), clazz, params);
                if (list.size() == 0) {
                    return Result.error("文件不能为空");
                }
                //update-begin-author:taoyan date:20190528 for:批量插入数据
                long start = System.currentTimeMillis();
                service.saveBatch(list);
                //400条 saveBatch消耗时间1592毫秒  循环插入消耗时间1947毫秒
                //1200条  saveBatch消耗时间3687毫秒 循环插入消耗时间5212毫秒
                log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
                //update-end-author:taoyan date:20190528 for:批量插入数据
                return Result.ok("文件导入成功！数据行数：" + list.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.error("文件导入失败！");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @return
     */
    protected Result<?> importExcelNotDuplicates(HttpServletRequest request, Class<T> clazz, String filed) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(2);
            params.setNeedSave(true);
            try {
                List<T> list = ExcelImportUtil.importExcel(file.getInputStream(), clazz, params);
                if (list.size() == 0) {
                    return Result.error("文件不能为空");
                }
                List<String> tempList = new ArrayList<>();
                for (T t : list) {
                    Class<?>[] interfaces = t.getClass().getInterfaces();
                    filed = filed.substring(0, 1).toUpperCase() + filed.substring(1);
                    String methodName = underlineToHump(filed, "_");
                    Method getMethod = clazz.getMethod(methodName);
                    String result = getMethod.invoke(interfaces) + "";
                    List<T> list1 = service.list(new QueryWrapper<T>().eq(filed, result));
                    List<String> collect = tempList.stream().filter(x -> x.equals(result)).collect(Collectors.toList());
                    if (list1.size() > 0 || collect.size() > 0) {
                        return Result.error(methodName + "重复！");
                    }
                    tempList.add(result);
                }
                //update-begin-author:taoyan date:20190528 for:批量插入数据
                long start = System.currentTimeMillis();
                service.saveBatch(list);
                //400条 saveBatch消耗时间1592毫秒  循环插入消耗时间1947毫秒
                //1200条  saveBatch消耗时间3687毫秒 循环插入消耗时间5212毫秒
                log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
                //update-end-author:taoyan date:20190528 for:批量插入数据
                return Result.ok("文件导入成功！数据行数：" + list.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.error("文件导入失败！");
    }

    /**
     * 按照任意字符将字符串转为小驼峰形式
     *
     * @param str
     * @param c   某些字符需要进行转义 例如 \\.
     * @return
     */
    private String underlineToHump(String str, String c) {
        Pattern UNDERLINE_PATTERN = Pattern.compile(c + "([a-z])");
        //正则匹配下划线及后一个字符，删除下划线并将匹配的字符转成大写
        Matcher matcher = UNDERLINE_PATTERN.matcher(str);
        StringBuffer sb = new StringBuffer(str);
        if (matcher.find()) {
            sb = new StringBuffer();
            //将当前匹配的子串替换成指定字符串，并且将替换后的子串及之前到上次匹配的子串之后的字符串添加到StringBuffer对象中
            //正则之前的字符和被替换的字符
            matcher.appendReplacement(sb, matcher.group(1).toUpperCase());
            //把之后的字符串也添加到StringBuffer对象中
            matcher.appendTail(sb);
        } else {
            //去除除字母之外的前面带的下划线
            return sb.toString().replaceAll(c, "");
        }
        return underlineToHump(sb.toString(), c);
    }
}
