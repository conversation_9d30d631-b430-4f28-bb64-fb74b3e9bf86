package org.jeecg.common.mq.subscribe.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.jeecg.common.mq.stream.Streams;
import org.springframework.data.redis.connection.stream.MapRecord;
import org.springframework.data.redis.stream.StreamListener;

/**
 * <AUTHOR>
 * @date 2023/12/19
 */
@Getter
@Setter
@AllArgsConstructor
public class SubscribeMessage {
    private StreamListener<String, MapRecord<String, String, String>> listener;

    private Streams streams;
}
