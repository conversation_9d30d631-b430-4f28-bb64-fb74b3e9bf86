package org.jeecg.common.mq.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/10/20
 */
public class AnalyzeContent {

    public static JSONObject getContent(Map<String,String> value){
        String jsonString = value.get("content");
        //jsonString = StringEscapeUtils.unescapeJava(jsonString);
        //jsonString = jsonString.substring(1,jsonString.length()-1);
        return JSON.parseObject(jsonString);
    }
}
