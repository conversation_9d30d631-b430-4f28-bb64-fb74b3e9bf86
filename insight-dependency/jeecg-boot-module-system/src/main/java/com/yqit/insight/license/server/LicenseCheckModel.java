package com.yqit.insight.license.server;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 自定义需要校验的License参数
 */
@Data
public class LicenseCheckModel implements Serializable {

    private static final long serialVersionUID = 8600137500316662317L;

    /**
     * license是否存在
     */
    private boolean exist = true;
    /**
     * 正式版/试用版
     */
    private boolean formal = false;
    /**
     * 产品名称
     */
    private String product;
    /**
     * 版本号
     */
    private String version;
    /**
     * 产品license类型，暂定为标准版、企业定制版
     */
    private String licensee;
    /**
     * 客户端数量
     */
    private Integer clientNum;
    /**
     * cpu数量：如有要求，cpus为1,2,3,4……等整型数值；否则unlimited
     */
    private List<Integer> cpus;
    /**
     * 可被允许的IP地址
     */
    private List<String> ipAddress;

    /**
     * 可被允许的MAC地址
     */
    private List<String> macAddress;

    /**
     * 可被允许的CPU序列号
     */
    private List<String> cpuSerial;

    /**
     * 可被允许的主板序列号
     */
    private List<String> mainBoardSerial;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date installedExpiration, notAfter;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date creatTime = new Date();


    private String info;

    private Long days;

    private Integer deviceNumber;

    /**
     * 平台标识
     */
    private String platformIdentify;
    /**
     * git版本分支名称
     */
    private String branch;
    /**
     * git版本打包时间
     */
    private String buildTime;
    /**
     * git版本提交ID
     */
    private String commitId;
    /**
     * git版本提交时间
     */
    private String commitTime;
    /**
     * ip
     */
    private String ipList;
    /**
     * mac
     */
    private String macList;

}
