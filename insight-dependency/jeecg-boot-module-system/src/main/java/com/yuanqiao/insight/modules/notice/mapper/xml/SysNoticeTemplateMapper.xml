<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.modules.notice.mapper.SysNoticeTemplateMapper">

    <select id="findAll"
            resultType="com.yuanqiao.insight.modules.notice.entity.SysNoticeTemplate">

        select *
        from sys_notice_template
        where delflag = 0

    </select>

    <select id="findProByAssetsId" resultType="com.yuanqiao.insight.modules.notice.entity.JsonValue">
        select id, display_name as code, assets_category_id as parentId, 'product' as type  from momg_product
        <where>
            <if test="assetsId!= null and assetsId.trim()!=''">
                and assets_category_id = #{assetsId}
            </if>

        </where>


    </select>

    <select id="findDevByProId" resultType="com.yuanqiao.insight.modules.notice.entity.JsonValue">
        select id, name as code, 'deviceInfo' as type from momg_device_info
        <where>
            <if test="proId!= null and proId.trim()!=''">
                and product_id = #{proId}
            </if>

        </where>
    </select>


    <select id="findLevel" resultType="com.yuanqiao.insight.modules.notice.entity.JsonValue">
        select alarm_level    as type,
               level_name as code
        from momg_alarm_level
    </select>


    <select id="findAllAssets" resultType="com.yuanqiao.insight.modules.notice.entity.JsonValue">
        select id, category_name as code, 'category' as type
        from cmdb_assets_category
        where delflag = 0
          and is_monitorable = 1
          and parent_id = '0'
    </select>


    <select id="findAllById" resultType="com.yuanqiao.insight.modules.notice.entity.SysNoticeTemplate">

        select snt.*,
               snc.config_type as type1,
               snc.config_name as noticeConfigName
        from sys_notice_template snt
                 left join sys_notice_config snc on
            snt.notice_config_id = snc.id
        where snt.id = #{id}
    </select>


    <select id="findOpenIdByUserId" resultType="string">
        select open_id
        from sys_user_extend
        where user_id = #{userId}

    </select>
    <select id="findTemplateByBusiness"
            resultType="com.yuanqiao.insight.modules.notice.entity.JsonValue">
        select id as type, template_name as code
        from sys_notice_template
        where delflag = 0
          and business like #{business}
    </select>
    <select id="findAssetsByAssetsId" resultType="com.yuanqiao.insight.modules.notice.entity.JsonValue">
        select id, category_name as code, 'category' as type
        from cmdb_assets_category
        where delflag = 0
          and is_monitorable = 1
          and parent_id = #{parentId}
    </select>

    <delete id="delUser">

        delete
        from sys_user_extend
        where user_id = #{userId}

    </delete>
</mapper>
