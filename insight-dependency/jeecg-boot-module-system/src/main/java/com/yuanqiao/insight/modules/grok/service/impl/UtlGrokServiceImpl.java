package com.yuanqiao.insight.modules.grok.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.common.util.common.RedisUtils;
import com.yuanqiao.insight.modules.grok.entity.UtlGrok;
import com.yuanqiao.insight.modules.grok.entity.UtlGrokFormula;
import com.yuanqiao.insight.modules.grok.mapper.UtlGrokFormulaMapper;
import com.yuanqiao.insight.modules.grok.mapper.UtlGrokMapper;
import com.yuanqiao.insight.modules.grok.service.IUtlGrokService;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.Collection;

/**
 * @Description: gork表达式
 * @Author: jeecg-boot
 * @Date: 2024-02-19
 * @Version: V1.0
 */
@Service
public class UtlGrokServiceImpl extends ServiceImpl<UtlGrokMapper, UtlGrok> implements IUtlGrokService {

    @Autowired
    private UtlGrokMapper utlGrokMapper;
    @Autowired
    private UtlGrokFormulaMapper utlGrokFormulaMapper;
    @Autowired
    private RedisUtils redisUtils;

    @Override
    @Transactional
    public void delMain(String id) {
        final UtlGrok utlGrok = utlGrokMapper.selectById(id);
        utlGrokFormulaMapper.delete(new LambdaQueryWrapper<UtlGrokFormula>().eq(UtlGrokFormula::getGrokId, id));
        utlGrokMapper.deleteById(id);
        redisUtils.del(CacheConstant.SYS_GROK_CACHE + ":" + utlGrok.getGrokCode());
    }

    @Override
    @Transactional
    public void delBatchMain(Collection<? extends Serializable> idList) {
        for (Serializable id : idList) {
            delMain(id.toString());
        }
    }

}
