package com.yuanqiao.insight.modules.notice.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.yuanqiao.insight.modules.notice.entity.SysNoticeConfig;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 通知
 * @Author: jeecg-boot
 * @Date:   2021-05-27
 * @Version: V1.0
 */
@Mapper
@Component
public interface SysNoticeConfigMapper extends MPJBaseMapper<SysNoticeConfig> {
    List<SysNoticeConfig> findALL();

}
