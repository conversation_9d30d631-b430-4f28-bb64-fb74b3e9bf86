package com.yuanqiao.insight.modules.grok.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: gork表达式
 * @Author: jeecg-boot
 * @Date:   2024-02-19
 * @Version: V1.0
 */
@Data
@TableName("utl_grok")
@ApiModel(value="utl_grok对象", description="gork表达式")
public class UtlGrok implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
	@ApiModelProperty(value = "id")
	private String id;
	/**名称*/
	@ApiModelProperty(value = "名称")
	private String grokName;
	/**唯一标识*/
	@ApiModelProperty(value = "唯一标识")
	private String grokCode;
	/**说明*/
	@ApiModelProperty(value = "说明")
	private String description;
	/**删除状态*/
	@ApiModelProperty(value = "删除状态")
	private String delFlag;
	/**创建人*/
	@ApiModelProperty(value = "创建人")
	private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "创建时间")
	private Date createTime;
	/**更新人*/
	@ApiModelProperty(value = "更新人")
	private String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "更新时间")
	private Date updateTime;
}
