package com.yuanqiao.insight.modules.notice.entity;

import lombok.Data;

@Data
public class SendMsg {

    private String smtp;

    private String sendName;

    private String password;

    private String port;

    private String Sender;


    private String webhook;

    private String secret;


    private String AccessKeyId;

    private String AccessKeySecret;

    private String https;

    private String autograph;

    private String noticeConfigId;

    private String appId;

    private String appSecret;

    private String token;

    private String domainUrl;

    private String secretKey;

    private String function;

    private String sendUrl;

    private String getOpenIdUrl;

    /** 是否使用ssl链接*/
    private Boolean sslEnable;
}
