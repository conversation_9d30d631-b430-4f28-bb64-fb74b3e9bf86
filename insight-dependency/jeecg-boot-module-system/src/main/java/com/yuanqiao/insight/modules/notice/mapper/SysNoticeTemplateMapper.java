package com.yuanqiao.insight.modules.notice.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanqiao.insight.modules.notice.entity.JsonValue;
import com.yuanqiao.insight.modules.notice.entity.SysNoticeTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 通知模板
 * @Author: jeecg-boot
 * @Date: 2021-05-28
 * @Version: V1.0
 */
@Component
@Mapper
public interface SysNoticeTemplateMapper extends BaseMapper<SysNoticeTemplate> {

    List<SysNoticeTemplate> findAll();

    List<JsonValue> findProByAssetsId(@Param("assetsId") String assetsId);

    List<JsonValue> findDevByProId(@Param("proId") String proId);

    List<JsonValue> findLevel();

    List<JsonValue> findAllAssets();

    SysNoticeTemplate findAllById(@Param("id") String id);

    String findOpenIdByUserId(@Param("userId") String userId);

    void delUser(@Param("userId") String userId);

    List<JsonValue> findTemplateByBusiness(@Param("business") String business);

    List<JsonValue> findAssetsByAssetsId(@Param("parentId") String parentId);
}
