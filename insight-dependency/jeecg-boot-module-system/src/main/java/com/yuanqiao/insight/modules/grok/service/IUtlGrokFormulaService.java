package com.yuanqiao.insight.modules.grok.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuanqiao.insight.modules.grok.entity.UtlGrokFormula;

/**
 * @Description: gork表达式
 * @Author: jeecg-boot
 * @Date:   2024-02-19
 * @Version: V1.0
 */
public interface IUtlGrokFormulaService extends IService<UtlGrokFormula> {
    void add(UtlGrokFormula utlGrokFormula);

    void modify(UtlGrokFormula utlGrokFormula);

    void delete(String id);
}
