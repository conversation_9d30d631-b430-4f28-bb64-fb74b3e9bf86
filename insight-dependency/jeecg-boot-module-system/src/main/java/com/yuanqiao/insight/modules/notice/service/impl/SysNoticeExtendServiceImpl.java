package com.yuanqiao.insight.modules.notice.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.modules.notice.entity.SysNoticeExtend;
import com.yuanqiao.insight.modules.notice.mapper.SysNoticeExtendMapper;
import com.yuanqiao.insight.modules.notice.service.ISysNoticeExtendService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 通知配置附加字段
 * @Author: jeecg-boot
 * @Date: 2021-05-28
 * @Version: V1.0
 */
@Service
public class SysNoticeExtendServiceImpl extends ServiceImpl<SysNoticeExtendMapper, SysNoticeExtend> implements ISysNoticeExtendService {

    @Override
    public List<SysNoticeExtend> findExtendByConfigId(String noticeConfigId) {
        return baseMapper.findExtendByConfigId(noticeConfigId);
    }

    @Override
    public List<String> findOpenIdByList(ArrayList<String> sentToJson) {
        return baseMapper.findOpenIdByList(sentToJson);
    }

    @Override
    public JSONObject getSendMsg(String noticeConfigId) {
        List<SysNoticeExtend> extendList = baseMapper.selectList(new LambdaQueryWrapper<SysNoticeExtend>()
                .eq(SysNoticeExtend::getNoticeConfigId, noticeConfigId));
        final Map<String, String> map = extendList.stream().peek(item -> {
                    if(StringUtils.isEmpty(item.getFieldValue())){
                        item.setFieldValue("");
                    }
                }
        ).collect(Collectors.toMap(SysNoticeExtend::getFieldCode, SysNoticeExtend::getFieldValue));
        return JSON.parseObject(JSON.toJSONString(map));
    }
}
