package com.yuanqiao.insight.modules.grok.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuanqiao.insight.modules.grok.entity.UtlGrok;

import java.io.Serializable;
import java.util.Collection;

/**
 * @Description: gork表达式
 * @Author: jeecg-boot
 * @Date:   2024-02-19
 * @Version: V1.0
 */
public interface IUtlGrokService extends IService<UtlGrok> {

	/**
	 * 删除一对多
	 */
	public void delMain (String id);

	/**
	 * 批量删除一对多
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);

}
