package org.jeecg.modules.quartz.job;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.modules.equipment.entity.EquipmentPlanExecution;
import com.yuanqiao.insight.modules.equipment.service.IEquipmentPlanExecutionService;
import com.yuanqiao.insight.modules.flowable.constant.FlowableConstant;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.common.engine.impl.identity.Authentication;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.FillRuleUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.jetbrains.annotations.NotNull;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 设备执行计划
 */
@Slf4j
@Data
public class EquipmentJob implements Job {

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        Map<String, Object> variables = jobExecutionContext.getMergedJobDataMap().getWrappedMap();
//        创建流程
        RepositoryService repositoryService = SpringContextUtils.getBean(RepositoryService.class);
        ISysBaseAPI iSysBaseAPI = SpringContextUtils.getBean(ISysBaseAPI.class);
        RuntimeService runtimeService = SpringContextUtils.getBean(RuntimeService.class);

        String dealUser = (String) variables.get("dealUser");
        String checkUser = (String) variables.get("checkUser");
        String category = (String) variables.get("category");
        String equipmentPlanId = (String) variables.get("equipmentPlanId");
        List<String> equipmentIdList = (List) variables.get("equipmentIdList");
        JSONObject designConfig = (JSONObject) variables.get("designConfig");


        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionKey("equipment").latestVersion().singleResult();
        LoginUser userById = iSysBaseAPI.getUserByName(dealUser);
        variables.put(FlowableConstant.BUSINESS_TITLE, "【" + userById.getRealname() + "】" + processDefinition.getName());
//        设置流程发起人
        Authentication.setAuthenticatedUserId(dealUser);
        for (String equipmentId : equipmentIdList) {
            ProcessInstance processInstance = startProcess(variables, runtimeService, processDefinition, userById, equipmentId);
//        创建业务表数据
            EquipmentPlanExecution equipmentPlanExecution = createBiz(dealUser, checkUser, category, equipmentId, processInstance, equipmentPlanId, designConfig);

            //        3、添加执行计划id，方便监听器查找到执行计划
            addVariableLink(runtimeService, processInstance, equipmentPlanExecution);
        }


    }

    private void addVariableLink(RuntimeService runtimeService, ProcessInstance processInstance, EquipmentPlanExecution equipmentPlanExecution) {
        //        监听器：
//        org.jeecg.modules.system.listener.EquipmentDealListener
//        org.jeecg.modules.system.listener.EquipmentCheckListener
        runtimeService.setVariable(processInstance.getId(), "equipmentPlanExecutionId", equipmentPlanExecution.getId());
    }

    @NotNull
    private EquipmentPlanExecution createBiz(String dealUser, String checkUser, String category, String equipmentId, ProcessInstance processInstance, String equipmentPlanId, JSONObject designConfig) {
        IEquipmentPlanExecutionService iEquipmentPlanExecutionService = SpringContextUtils.getBean(IEquipmentPlanExecutionService.class);
        EquipmentPlanExecution equipmentPlanExecution = new EquipmentPlanExecution();
        equipmentPlanExecution.setActId(processInstance.getId());
        equipmentPlanExecution.setEquipmentPlanId(equipmentPlanId);
        equipmentPlanExecution.setEquipmentId(equipmentId);
        equipmentPlanExecution.setExecutorSignature(dealUser);
        equipmentPlanExecution.setScrutatorSignature(checkUser);
        equipmentPlanExecution.setCategory(category);
        equipmentPlanExecution.setState("unfinished");
        equipmentPlanExecution.setCreateTime(new Date());
        equipmentPlanExecution.setDesignConfig(designConfig);

//        生成编码
        JSONObject formData = new JSONObject();
        formData.put("code-generation-bind", category);


        String codeGeneration = (String) FillRuleUtil.executeRule("biz_code_generation", formData);
        if (StringUtils.isEmpty(codeGeneration)) {
            throw new RuntimeException(("获取编码异常！"));
        }
        equipmentPlanExecution.setSerialNumber(codeGeneration);
        iEquipmentPlanExecutionService.save(equipmentPlanExecution);
        return equipmentPlanExecution;
    }

    private ProcessInstance startProcess(Map<String, Object> variables, RuntimeService runtimeService, ProcessDefinition processDefinition, LoginUser userById, String equipmentId) {
        variables.put("equipmentId", equipmentId);
        ProcessInstance processInstance = runtimeService.createProcessInstanceBuilder()
                .processDefinitionId(processDefinition.getId())
                .name(String.format("【%s】%s", userById.getRealname(), processDefinition.getName()))
                .variables(variables)
                .start();
        return processInstance;
    }


}
