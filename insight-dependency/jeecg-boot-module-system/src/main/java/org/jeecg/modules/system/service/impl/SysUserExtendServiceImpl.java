package org.jeecg.modules.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.system.entity.SysUserExtend;
import org.jeecg.modules.system.mapper.SysUserExtendMapper;
import org.jeecg.modules.system.service.ISysUserExtendService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/13
 */
@Service
public class SysUserExtendServiceImpl extends ServiceImpl<SysUserExtendMapper, SysUserExtend> implements ISysUserExtendService {
    @Override
    public List<SysUserExtend> findUserExtend() {
        return baseMapper.findUserExtend();
    }
}
