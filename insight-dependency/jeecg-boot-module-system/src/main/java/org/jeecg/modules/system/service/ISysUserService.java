package org.jeecg.modules.system.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseService;
import com.yuanqiao.insight.acore.depart.entity.SysDepart;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.system.vo.SysUserCacheInfo;
import org.jeecg.modules.system.entity.SysUsers;
import org.jeecg.modules.system.model.SysUserSysDepartModel;
import org.jeecg.modules.system.vo.UserVo;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-20
 */
public interface ISysUserService extends MPJBaseService<SysUsers> {

    /**
     * 重置密码
     *
     * @param username
     * @param oldpassword
     * @param newpassword
     * @param confirmpassword
     * @return
     */
    public Result<?> resetPassword(String username, String oldpassword, String newpassword, String confirmpassword);

    /**
     * 修改密码
     *
     * @param sysUser
     * @return
     */
    public Result<?> changePassword(SysUsers sysUser);

    /**
     * 删除用户
     *
     * @param userId
     * @return
     */
    public boolean deleteUser(String userId);

    /**
     * 批量删除用户
     *
     * @param userIds
     * @return
     */
    public boolean deleteBatchUsers(String userIds);

    public SysUsers getUserByName(String username);

    /**
     * 添加用户和用户角色关系
     *
     * @param user
     * @param roles
     */
    public void addUserWithRole(SysUsers user, String roles);


    /**
     * 修改用户和用户角色关系
     *
     * @param user
     * @param roles
     */
    public void editUserWithRole(SysUsers user, String roles);

    /**
     * 获取用户的授权角色
     *
     * @param username
     * @return
     */
    public List<String> getRole(String username);

    /**
     * 查询用户信息包括 部门信息
     *
     * @param username
     * @return
     */
    public SysUserCacheInfo getCacheUser(String username);

    /**
     * 根据部门Id查询
     *
     * @param
     * @return
     */
    public IPage<SysUsers> getUserByDepId(Page<SysUsers> page, String departId, String username);

    /**
     * 根据部门Ids查询
     *
     * @param
     * @return
     */
    public IPage<SysUsers> getUserByDepIds(Page<SysUsers> page, List<String> departIds, String username);

    /**
     * 根据部门ID查询
     *
     * @param departId 部门id
     * @return
     */
    public List<SysUsers> getUserByDepId(String departId);

    /**
     * 根据 userIds查询，查询用户所属部门的名称（多个部门名逗号隔开）
     *
     * @param
     * @return
     */
    public Map<String, String> getDepNamesByUserIds(List<String> userIds);

    /**
     * 根据部门 Id 和 QueryWrapper 查询
     *
     * @param page
     * @param departId
     * @param queryWrapper
     * @return
     */
    public IPage<SysUsers> getUserByDepartIdAndQueryWrapper(Page<SysUsers> page, String departId, QueryWrapper<SysUsers> queryWrapper);

    /**
     * 根据 orgCode 查询用户，包括子部门下的用户
     *
     * @param orgCode
     * @param userParams 用户查询条件，可为空
     * @param page       分页参数
     * @return
     */
    IPage<SysUserSysDepartModel> queryUserByOrgCode(String orgCode, SysUsers userParams, IPage page);

    /**
     * 根据角色Id查询
     *
     * @param
     * @param realname
     * @return
     */
    public IPage<SysUsers> getUserByRoleId(Page<SysUsers> page, String roleId, String username, String realname);

    /**
     * 通过用户名获取用户角色集合
     *
     * @param username 用户名
     * @return 角色集合
     */
    Set<String> getUserRolesSet(String username);

    /**
     * 通过用户名获取用户权限集合
     *
     * @param username 用户名
     * @return 权限集合
     */
    Set<String> getUserPermissionsSet(String username);

    /**
     * 根据用户名设置部门ID
     *
     * @param username
     * @param orgCode
     */
    void updateUserDepart(String username, String orgCode);

    /**
     * 根据手机号获取用户名和密码
     */
    public SysUsers getUserByPhone(String phone);


    /**
     * 根据邮箱获取用户
     */
    public SysUsers getUserByEmail(String email);


    /**
     * 添加用户和用户部门关系
     *
     * @param user
     * @param selectedParts
     */
    void addUserWithDepart(SysUsers user, String selectedParts);

    /**
     * 编辑用户和用户部门关系
     *
     * @param user
     * @param departs
     */
    void editUserWithDepart(SysUsers user, String departs);

    /**
     * 校验用户是否有效
     *
     * @param sysUser
     * @return
     */
    Result<JSONObject> checkUserIsEffective(SysUsers sysUser);

    /**
     * 查询被逻辑删除的用户
     */
    List<SysUsers> queryLogicDeleted();

    /**
     * 查询被逻辑删除的用户（可拼装查询条件）
     */
    List<SysUsers> queryLogicDeleted(LambdaQueryWrapper<SysUsers> wrapper);

    /**
     * 还原被逻辑删除的用户
     */
    boolean revertLogicDeleted(List<String> userIds, SysUsers updateEntity);

    /**
     * 彻底删除被逻辑删除的用户
     */
    boolean removeLogicDeleted(List<String> userIds);

    /**
     * 更新手机号、邮箱空字符串为 null
     */
    @Transactional(rollbackFor = Exception.class)
    boolean updateNullPhoneEmail();

    /**
     * 保存第三方用户信息
     *
     * @param sysUser
     */
    void saveThirdUser(SysUsers sysUser);

    /**
     * 根据部门Ids查询
     *
     * @param
     * @return
     */
    List<SysUsers> queryByDepIds(List<String> departIds, String username);

    /**
     * 查询所有用户名和用户ID
     *
     * @return
     */
    Map<String, String> getUserMap();

    Map<String, String> getUserIdAndNmaeMap();

    /**
     * 查询用户列表
     *
     * @return
     */
    List<SysUsers> getList();


    /**
     * 通过账号获取用户的列表
     *
     * @param loginName 账号
     * @return
     */
    List<LoginUser> getUsersByLoginUser(String loginName);

    /**
     * 根据用户username查询realname
     *
     * @param ownerId
     * @return
     */
    String selectRealnameByUsername(String ownerId);

    Map<String, String> selectRealnameByUsername();

    String getUserid(String userid);


    void addUserByDepart(SysDepart sysDepart);

    SysUsers getByUId(String s);

    SysUsers getUserByNameSpec(String s);

    List<SysUsers> getUserByIds(List<String> sentTo);

    int syncUser(cn.hutool.json.JSONObject user);

	List<UserVo> list(String org);

    void syncZJGUser(List<SysUsers> sysUsers);

    void syncZyUser(JSONArray user, boolean isFirst);

    List<String> selectNumbers(String prefix);

    void deleteUserByuserName(String userId);

    void deleteUserByuserNames(List<String> userIds);

    /**
     * 直接删除用户，关联删除
     *
     * @param userIds
     * @return
     */
    boolean deleteDirectly(List<String> userIds);

    IPage<SysUsers> getUsersWithDeptIds(Page<SysUsers> page, SysUsers sysUser);
}
