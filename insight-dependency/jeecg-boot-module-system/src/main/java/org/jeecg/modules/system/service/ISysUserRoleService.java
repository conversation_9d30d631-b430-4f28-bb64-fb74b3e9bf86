package org.jeecg.modules.system.service;

import com.github.yulichang.base.MPJBaseService;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.system.entity.SysUserRole;
import org.jeecg.modules.system.vo.SysUserRoleVO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 用户角色表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-21
 */
public interface ISysUserRoleService extends MPJBaseService<SysUserRole> {

    List<LoginUser> getUsersByRoleCode(String id);

    List<String> getUserRoleByUserId(String userid);

    void grantRole(SysUserRoleVO sysUserRoleVO);

    Map<String, String> getRoleByUserIds(List<String> userIds);
}
