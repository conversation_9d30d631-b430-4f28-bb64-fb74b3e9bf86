package org.jeecg.modules.util;

import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @title: ZipEncryConfig
 * @projectName jeecg-boot-parent
 * @description: TODO
 * @date 2021/8/19-9:17
 */
@Component
public class ZipEncryConfig {
//    @Value(value = "${zip.isEncry}")
    public boolean isEncry;
//    @Value(value = "${zip.encryPwd}")
    public String encryPwd;

    public boolean isEncry() {
        return isEncry;
    }

    public void setEncry(boolean encry) {
        isEncry = encry;
    }

    public String getEncryPwd() {
        return encryPwd;
    }

    public void setEncryPwd(String encryPwd) {
        this.encryPwd = encryPwd;
    }
}
