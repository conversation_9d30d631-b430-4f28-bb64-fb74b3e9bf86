<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.system.mapper.SysRoleMapper">

    <select id="hasButtonRole" resultType="java.lang.Integer">
        SELECT count(1)
        FROM sys_role sr
                 LEFT JOIN sys_user_role sur on sur.role_id = sr.id
                 LEFT JOIN sys_role_permission srp on sur.role_id = sr.id
                 LEFT JOIN sys_permission sp on sp.id = srp.permission_id
        where sur.user_id = #{userId}
          and sp.perms = #{role}
    </select>
</mapper>
