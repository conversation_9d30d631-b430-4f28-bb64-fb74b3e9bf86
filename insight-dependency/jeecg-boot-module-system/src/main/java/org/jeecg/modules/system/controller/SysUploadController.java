package org.jeecg.modules.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.oss.entity.OSSFile;
import org.jeecg.modules.oss.service.IOSSFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;

/**
 * minio文件上传示例
 */
@Slf4j
@RestController
@RequestMapping("/sys/upload")
public class SysUploadController {
    @Autowired
    private IOSSFileService ossFileService;

    //todo 需要前端配合下，这两个接口还有用吗，没按设想的来用统一的上传、下载的接口

    /**
     * 上传
     * @param request
     */
    @PostMapping(value = "/uploadMinio")
    public Result<?> uploadMinio(HttpServletRequest request) {
        Result<?> result = new Result<>();
        String bizPath = request.getParameter("biz");
        if(oConvertUtils.isEmpty(bizPath)){
            bizPath = "";
        }
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        MultipartFile file = multipartRequest.getFile("file");// 获取上传文件对象
        String orgName = file.getOriginalFilename();// 获取文件名
        orgName = CommonUtils.getFileName(orgName);
        String file_url = CommonUtils.upload(file,bizPath, CommonConstant.UPLOAD_TYPE_MINIO);
        if(oConvertUtils.isEmpty(file_url)){
            return Result.error("上传失败,请检查配置信息是否正确!");
        }
        //保存文件信息
        OSSFile minioFile = new OSSFile();
        minioFile.setFileName(orgName);
        minioFile.setUrl(file_url);
        ossFileService.save(minioFile);
        result.setMessage(file_url);
        result.setSuccess(true);
        return result;
    }



    /**
     * 模板配置文件上传
     * @param request
     * @param file
     * @param attachments
     * @return
     */
    @PostMapping(value = "/uploadFile")
    public Result<?> uploadFile(HttpServletRequest request,@RequestParam("file") MultipartFile file,@RequestParam("attachments") String attachments) {
        Result<?> result = new Result<>();

        if(oConvertUtils.isEmpty(attachments)){
            attachments = "";
        }
        //MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
       // MultipartFile file = multipartRequest.getFile("file");// 获取上传文件对象
        String orgName = file.getOriginalFilename();// 获取文件名
        orgName = CommonUtils.getFileName(orgName);
        String file_url = CommonUtils.upload(file,attachments, CommonConstant.UPLOAD_TYPE_MINIO);
        if(oConvertUtils.isEmpty(file_url)){
            return Result.error("上传失败,请检查配置信息是否正确!");
        }
        //保存文件信息
//        OSSFile minioFile = new OSSFile();
//        minioFile.setFileName(orgName);
//        minioFile.setUrl(file_url);
//        ossFileService.save(minioFile);
        JSONArray attachmentsArray = new JSONArray();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("name",orgName);
        jsonObject.put("url",file_url);
        jsonObject.put("path",file_url);
        attachmentsArray.add(jsonObject);

        return Result.OK(attachmentsArray);
    }


}
