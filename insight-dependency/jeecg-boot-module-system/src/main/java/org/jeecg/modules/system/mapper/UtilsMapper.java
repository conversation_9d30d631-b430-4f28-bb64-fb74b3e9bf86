package org.jeecg.modules.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.poi.ss.formula.functions.T;

import java.util.List;

/**
 * <AUTHOR>
 * @title: UtilsMapper
 * @projectName jeecg-boot-parent
 * @description: TODO
 * @date 2021/3/3-15:02
 */
public interface UtilsMapper extends BaseMapper<T> {
    @Select("${sql}")
    List<String> getCode(@Param("sql") String sql);
}
