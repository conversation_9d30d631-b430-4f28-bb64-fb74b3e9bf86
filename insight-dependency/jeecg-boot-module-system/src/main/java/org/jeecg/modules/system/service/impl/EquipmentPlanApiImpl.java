package org.jeecg.modules.system.service.impl;

import com.yuanqiao.insight.modules.equipment.entity.EquipmentPlan;
import com.yuanqiao.insight.modules.equipment.service.IEquipmentPlanService;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.api.IEquipmentPlanAPI;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description: 底层共通业务API，提供其他独立模块调用
 * @Author: scott
 * @Date:2019-4-20
 * @Version:V1.0
 */
@Slf4j
@Service
public class EquipmentPlanApiImpl implements IEquipmentPlanAPI {
    @Autowired
    private IEquipmentPlanService iEquipmentPlanService;

    /**
     * 获取设备计划的自定义表单配置
     *
     * @return
     */
    @Override
    public String getEquipmentPlanConfig(String equipmentPlanId) {
        EquipmentPlan byId = iEquipmentPlanService.getById(equipmentPlanId);
        if (byId == null || byId.getDesignConfig() == null) {
            return "";
        }
        return byId.getDesignConfig().toJSONString();
    }
}
