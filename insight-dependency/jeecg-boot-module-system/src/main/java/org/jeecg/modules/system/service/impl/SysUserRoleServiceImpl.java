package org.jeecg.modules.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.yulichang.base.MPJBaseServiceImpl;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.system.controller.SysRoleController;
import org.jeecg.modules.system.entity.SysRole;
import org.jeecg.modules.system.entity.SysUserRole;
import org.jeecg.modules.system.mapper.SysRoleMapper;
import org.jeecg.modules.system.mapper.SysUserMapper;
import org.jeecg.modules.system.mapper.SysUserRoleMapper;
import org.jeecg.modules.system.service.ISysUserRoleService;
import org.jeecg.modules.system.vo.SysUserDepVo;
import org.jeecg.modules.system.vo.SysUserRoleVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 用户角色表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-21
 */
@Service
public class SysUserRoleServiceImpl extends MPJBaseServiceImpl<SysUserRoleMapper, SysUserRole> implements ISysUserRoleService {

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    private SysRoleMapper sysRoleMapper;
    @Autowired
    private SysUserMapper sysUserMapper;

    @Override
    public List<LoginUser> getUsersByRoleCode(String id) {
        return super.baseMapper.getUsersByRoleCode(id);
    }

    @Override
    public List<String> getUserRoleByUserId(String userid) {
        return sysUserRoleMapper.getRoleCodeByuserId(userid);
    }

    @Override
    public void grantRole(SysUserRoleVO sysUserRoleVO) {
        String sysRoleId = sysUserRoleVO.getRoleId();
        for (String sysUserId : sysUserRoleVO.getUserIdList()) {
            QueryWrapper<SysUserRole> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("role_id", sysRoleId).eq("user_id", sysUserId);
            SysUserRole sysUserRole = new SysUserRole(sysUserId, sysRoleId);
            List<String> roles = SysRoleController.getThreePowersRole();
            List<String> grantRoles = super.baseMapper.getRoleCodeByUserId(sysUserId);
            SysRole sysRole = sysRoleMapper.selectById(sysRoleId);
            if ((CollUtil.isNotEmpty(roles) && grantRoles.stream().anyMatch(roles::contains)) || roles.contains(sysRole.getRoleCode())) {
                super.baseMapper.delete(new LambdaQueryWrapper<SysUserRole>()
                        .eq(SysUserRole::getUserId, sysUserId));
            }
            SysUserRole one = baseMapper.selectOne(queryWrapper);
            if (one == null) {
                baseMapper.insert(sysUserRole);
            }
        }
    }
    @Override
    public Map<String, String> getRoleByUserIds(List<String> userIds) {
        List<SysUserRoleVO> list = sysUserMapper.getRoleByUserIds(userIds);

        Map<String, String> res = new HashMap<>();

        list.forEach(item -> {
            String userId = item.getUserId();
            String roleName = item.getRoleName();
            String roleCode = item.getRoleCode();

            // 初始化或追加角色名称
            String existingRoleNames = res.getOrDefault(userId + "_names", "");
            res.put(userId + "_names", existingRoleNames.isEmpty() ? roleName : existingRoleNames + "," + roleName);

            // 初始化或追加角色代码
            String existingRoleCodes = res.getOrDefault(userId + "_codes", "");
            res.put(userId + "_codes", existingRoleCodes.isEmpty() ? roleCode : existingRoleCodes + "," + roleCode);
        });

        return res;
    }


}
