package org.jeecg.modules.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.yuanqiao.insight.acore.depart.entity.SysDepart;
import com.yuanqiao.insight.acore.depart.mapper.SysDepartMapper;
import com.yuanqiao.insight.common.constant.enums.ResultCode;
import com.yuanqiao.insight.common.util.common.LocalCacheUtils;
import com.yuanqiao.insight.common.util.dbType.DataSourceTypeUtils;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.system.vo.SysUserCacheInfo;
import org.jeecg.common.util.FillRuleUtil;
import org.jeecg.common.util.PasswordUtil;
import org.jeecg.common.util.UUIDGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.base.service.BaseCommonService;
import org.jeecg.modules.system.entity.*;
import org.jeecg.modules.system.mapper.*;
import org.jeecg.modules.system.model.SysUserSysDepartModel;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.system.vo.SysUserDepVo;
import org.jeecg.modules.system.vo.UserVo;
import org.jeecg.modules.util.ChineseCharToEnUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * @Author: scott
 * @Date: 2018-12-20
 */
@Service
@Slf4j
public class SysUserServiceImpl extends MPJBaseServiceImpl<SysUserMapper, SysUsers> implements ISysUserService {

    @Autowired
    private SysUserMapper userMapper;
    @Autowired
    private SysPermissionMapper sysPermissionMapper;
    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    private SysUserDepartMapper sysUserDepartMapper;
    @Autowired
    private ISysBaseAPI sysBaseAPI;
    @Autowired
    private SysDepartMapper sysDepartMapper;
    @Autowired
    private SysRoleMapper sysRoleMapper;
    @Autowired
    private SysDepartRoleUserMapper departRoleUserMapper;
    @Autowired
    private SysDepartRoleMapper sysDepartRoleMapper;
    @Resource
    private BaseCommonService baseCommonService;
    private static final LocalCacheUtils cacheUtils = LocalCacheUtils.getInstance();

    @Override
    @CacheEvict(value = {CacheConstant.SYS_USERS_CACHE}, allEntries = true)
    public Result<?> resetPassword(String username, String oldpassword, String newpassword, String confirmpassword) {
        SysUsers user = userMapper.getUserByName(username);
        String passwordEncode = PasswordUtil.encrypt(username, oldpassword, user.getSalt());
        if (!user.getPassword().equals(passwordEncode)) {
            return Result.error("旧密码输入错误!");
        }
        if (oConvertUtils.isEmpty(newpassword)) {
            return Result.error("新密码不允许为空!");
        }
        if (!newpassword.equals(confirmpassword)) {
            return Result.error("两次输入密码不一致!");
        }
        String password = PasswordUtil.encrypt(username, newpassword, user.getSalt());
        this.userMapper.update(new SysUsers().setPassword(password), new LambdaQueryWrapper<SysUsers>().eq(SysUsers::getId, user.getId()));
        return Result.ok("密码重置成功!");
    }

    @Override
    @CacheEvict(value = {CacheConstant.SYS_USERS_CACHE}, allEntries = true)
    public Result<?> changePassword(SysUsers sysUser) {
        String salt = oConvertUtils.randomGen(8);
        sysUser.setSalt(salt);
        String password = sysUser.getPassword();
        String passwordEncode = PasswordUtil.encrypt(sysUser.getUsername(), password, salt);
        sysUser.setPassword(passwordEncode);
        this.userMapper.updateById(sysUser);
        return Result.ok("密码修改成功!");
    }

    @Override
    @CacheEvict(value = {CacheConstant.SYS_USERS_CACHE}, allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteUser(String userId) {
        //1.删除用户
        this.removeById(userId);
        return false;
    }

    @Override
    @CacheEvict(value = {CacheConstant.SYS_USERS_CACHE}, allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBatchUsers(String userIds) {
        //1.删除用户
        this.removeByIds(Arrays.asList(userIds.split(",")));
        return false;
    }

    @Override
    public SysUsers getUserByName(String username) {
        return userMapper.getUserByName(username);
    }


    @Override
    @Transactional
    public void addUserWithRole(SysUsers user, String roles) {
        this.save(user);
        if (oConvertUtils.isNotEmpty(roles)) {
            String[] arr = roles.split(",");
            for (String roleId : arr) {
                SysUserRole userRole = new SysUserRole(user.getId(), roleId);
                sysUserRoleMapper.insert(userRole);
            }
        }
    }

    @Override
    @CacheEvict(value = {CacheConstant.SYS_USERS_CACHE}, allEntries = true)
    @Transactional
    public void editUserWithRole(SysUsers user, String roles) {
        this.updateById(user);
        //先删后加
        sysUserRoleMapper.delete(new QueryWrapper<SysUserRole>().lambda().eq(SysUserRole::getUserId, user.getId()));
        if (oConvertUtils.isNotEmpty(roles)) {
            String[] arr = roles.split(",");
            for (String roleId : arr) {
                SysUserRole userRole = new SysUserRole(user.getId(), roleId);
                sysUserRoleMapper.insert(userRole);
            }
        }
    }


    @Override
    public List<String> getRole(String username) {
        return sysUserRoleMapper.getRoleByUserName(username);
    }

    /**
     * 通过用户名获取用户角色集合
     *
     * @param username 用户名
     * @return 角色集合
     */
    @Override
    public Set<String> getUserRolesSet(String username) {
        // 查询用户拥有的角色集合
        List<String> roles = sysUserRoleMapper.getRoleByUserName(username);
        log.info("-------通过数据库读取用户拥有的角色Rules------username： " + username + ",Roles size: " + (roles == null ? 0 : roles.size()));
        return new HashSet<>(roles);
    }

    /**
     * 通过用户名获取用户权限集合
     *
     * @param username 用户名
     * @return 权限集合
     */
    @Override
    public Set<String> getUserPermissionsSet(String username) {
        Set<String> permissionSet = new HashSet<>();
        List<SysPermission> permissionList = sysPermissionMapper.queryByUser(username);
        for (SysPermission po : permissionList) {
//			// TODO URL规则有问题？
//			if (oConvertUtils.isNotEmpty(po.getUrl())) {
//				permissionSet.add(po.getUrl());
//			}
            if (oConvertUtils.isNotEmpty(po.getPerms())) {
                permissionSet.add(po.getPerms());
            }
        }
        log.info("-------通过数据库读取用户拥有的权限Perms------username： " + username + ",Perms size: " + (permissionSet == null ? 0 : permissionSet.size()));
        return permissionSet;
    }

    @Override
    public SysUserCacheInfo getCacheUser(String username) {
        SysUserCacheInfo info = new SysUserCacheInfo();
        info.setOneDepart(true);
//		SysUser user = userMapper.getUserByName(username);
//		info.setSysUserCode(user.getUsername());
//		info.setSysUserName(user.getRealname());


        LoginUser user = sysBaseAPI.getUserByName(username);
        if (user != null) {
            info.setSysUserCode(user.getUsername());
            info.setSysUserName(user.getRealname());
            info.setSysOrgCode(user.getOrgCode());
        }

        //多部门支持in查询
        List<SysDepart> list = sysDepartMapper.queryUserDeparts(user.getId());
        List<String> sysMultiOrgCode = new ArrayList<String>();
        if (list == null || list.size() == 0) {
            //当前用户无部门
            //sysMultiOrgCode.add("0");
        } else if (list.size() == 1) {
            sysMultiOrgCode.add(list.get(0).getOrgCode());
        } else {
            info.setOneDepart(false);
            for (SysDepart dpt : list) {
                sysMultiOrgCode.add(dpt.getOrgCode());
            }
        }
        info.setSysMultiOrgCode(sysMultiOrgCode);

        return info;
    }

    // 根据部门Id查询
    @Override
    public IPage<SysUsers> getUserByDepId(Page<SysUsers> page, String departId, String username) {
        return userMapper.getUserByDepId(page, departId, username);
    }

    @Override
    public IPage<SysUsers> getUserByDepIds(Page<SysUsers> page, List<String> departIds, String username) {
        return userMapper.getUserByDepIds(page, departIds, username);
    }

    @Override
    public Map<String, String> getDepNamesByUserIds(List<String> userIds) {
        List<SysUserDepVo> list = this.baseMapper.getDepNamesByUserIds(userIds);

        Map<String, String> res = new HashMap<String, String>();
        list.forEach(item -> {
            res.merge(item.getUserId(), item.getDepartName(), (a, b) -> a + "," + b);
        });
        return res;
    }

    /**
     * 根据部门ID查询
     *
     * @param departId 部门id
     * @return
     */
    @Override
    public List<SysUsers> getUserByDepId(String departId) {
        return this.baseMapper.selectUserByDepId(departId);
    }

    @Override
    public IPage<SysUsers> getUserByDepartIdAndQueryWrapper(Page<SysUsers> page, String departId, QueryWrapper<SysUsers> queryWrapper) {
        LambdaQueryWrapper<SysUsers> lambdaQueryWrapper = queryWrapper.lambda();

        lambdaQueryWrapper.eq(SysUsers::getDelFlag, CommonConstant.DEL_FLAG_0);
        lambdaQueryWrapper.inSql(SysUsers::getId, "SELECT user_id FROM sys_user_depart WHERE dep_id = '" + departId + "'");

        return userMapper.selectPage(page, lambdaQueryWrapper);
    }

    @Override
    public IPage<SysUserSysDepartModel> queryUserByOrgCode(String orgCode, SysUsers userParams, IPage page) {
        List<SysUserSysDepartModel> list = baseMapper.getUserByOrgCode(page, orgCode, userParams);
        Integer total = baseMapper.getUserByOrgCodeTotal(orgCode, userParams);

        IPage<SysUserSysDepartModel> result = new Page<>(page.getCurrent(), page.getSize(), total);
        result.setRecords(list);

        return result;
    }

    // 根据角色Id查询
    @Override
    public IPage<SysUsers> getUserByRoleId(Page<SysUsers> page, String roleId, String username, String realname) {
        return userMapper.getUserByRoleId(page, roleId, username, realname);
    }


    @Override
    @CacheEvict(value = {CacheConstant.SYS_USERS_CACHE}, key = "#username")
    public void updateUserDepart(String username, String orgCode) {
        baseMapper.updateUserDepart(username, orgCode);
    }


    @Override
    public SysUsers getUserByPhone(String phone) {
        return userMapper.getUserByPhone(phone);
    }


    @Override
    public SysUsers getUserByEmail(String email) {
        return userMapper.getUserByEmail(email);
    }

    @Override
    @Transactional
    public void addUserWithDepart(SysUsers user, String selectedParts) {
//		this.save(user);  //保存角色的时候已经添加过一次了
        if (oConvertUtils.isNotEmpty(selectedParts)) {
            String[] arr = selectedParts.split(",");
            for (String deaprtId : arr) {
                SysUserDepart userDeaprt = new SysUserDepart(user.getId(), deaprtId);
                sysUserDepartMapper.insert(userDeaprt);
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {CacheConstant.SYS_USERS_CACHE}, allEntries = true)
    public void editUserWithDepart(SysUsers user, String departs) {
        this.updateById(user);  //更新角色的时候已经更新了一次了，可以再跟新一次
        String[] arr = {};
        if (oConvertUtils.isNotEmpty(departs)) {
            arr = departs.split(",");
        }
        //查询已关联部门
        List<SysUserDepart> userDepartList = sysUserDepartMapper.selectList(new QueryWrapper<SysUserDepart>().lambda().eq(SysUserDepart::getUserId, user.getId()));
        if (userDepartList != null && userDepartList.size() > 0) {
            for (SysUserDepart depart : userDepartList) {
                //修改已关联部门删除部门用户角色关系
                if (!Arrays.asList(arr).contains(depart.getDepId())) {
                    List<SysDepartRole> sysDepartRoleList = sysDepartRoleMapper.selectList(new QueryWrapper<SysDepartRole>().lambda().eq(SysDepartRole::getDepartId, depart.getDepId()));
                    List<String> roleIds = sysDepartRoleList.stream().map(SysDepartRole::getId).collect(Collectors.toList());
                    if (roleIds != null && roleIds.size() > 0) {
                        departRoleUserMapper.delete(new QueryWrapper<SysDepartRoleUser>().lambda().eq(SysDepartRoleUser::getUserId, user.getId()).in(SysDepartRoleUser::getDroleId, roleIds));
                    }
                }
            }
        }
        //先删后加
        sysUserDepartMapper.delete(new QueryWrapper<SysUserDepart>().lambda().eq(SysUserDepart::getUserId, user.getId()));
        if (oConvertUtils.isNotEmpty(departs)) {
            for (String departId : arr) {
                SysUserDepart userDepart = new SysUserDepart(user.getId(), departId);
                sysUserDepartMapper.insert(userDepart);
            }
        }
    }


    /**
     * 校验用户是否有效
     *
     * @param sysUser
     * @return
     */
    @Override
    public Result<JSONObject> checkUserIsEffective(SysUsers sysUser) {
        Result<JSONObject> result = new Result<>();
        //情况1：根据用户信息查询，该用户不存在
        if (sysUser == null) {
            result.error500("该用户不存在，请联系管理员注册");
            baseCommonService.addLog("用户登录失败，用户不存在！", CommonConstant.LOG_TYPE_1, null);
            return result;
        }
        //情况2：根据用户信息查询，该用户已注销
        //update-begin---author:王帅   Date:20200601  for：if条件永远为falsebug------------
        if (CommonConstant.DEL_FLAG_1.equals(sysUser.getDelFlag())) {
            //update-end---author:王帅   Date:20200601  for：if条件永远为falsebug------------
            baseCommonService.addLog("用户登录失败，用户名:" + sysUser.getUsername() + "已注销！", CommonConstant.LOG_TYPE_1, null);
            result.error500("该用户已注销");
            return result;
        }
        //情况3：根据用户信息查询，该用户已冻结
        if (CommonConstant.USER_FREEZE.equals(sysUser.getStatus())) {
            baseCommonService.addLog("用户登录失败，用户名:" + sysUser.getUsername() + "已冻结！", CommonConstant.LOG_TYPE_1, null);
            result.error500("该用户已冻结");
            return result;
        }
        return result;
    }

    @Override
    public List<SysUsers> queryLogicDeleted() {
        return this.queryLogicDeleted(null);
    }

    @Override
    public List<SysUsers> queryLogicDeleted(LambdaQueryWrapper<SysUsers> wrapper) {
        if (wrapper == null) {
            wrapper = new LambdaQueryWrapper<>();
        }
        wrapper.eq(SysUsers::getDelFlag, CommonConstant.DEL_FLAG_1);
        return userMapper.selectLogicDeleted(wrapper);
    }

    @Override
    public boolean revertLogicDeleted(List<String> userIds, SysUsers updateEntity) {
        String ids = String.format("'%s'", String.join("','", userIds));
        return userMapper.revertLogicDeleted(ids, updateEntity) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeLogicDeleted(List<String> userIds) {
        String ids = String.format("'%s'", String.join("','", userIds));
        // 1. 删除用户
        int line = userMapper.deleteLogicDeleted(ids);
        // 2. 删除用户部门关系
        line += sysUserDepartMapper.delete(new LambdaQueryWrapper<SysUserDepart>().in(SysUserDepart::getUserId, userIds));
        //3. 删除用户角色关系
        line += sysUserRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().in(SysUserRole::getUserId, userIds));
        return line != 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateNullPhoneEmail() {
        userMapper.updateNullByEmptyString("email");
        userMapper.updateNullByEmptyString("phone");
        return true;
    }

    @Override
    public void saveThirdUser(SysUsers sysUser) {
        //保存用户
        String userid = UUIDGenerator.generate();
        sysUser.setId(userid);
        baseMapper.insert(sysUser);
        //获取第三方角色
        SysRole sysRole = sysRoleMapper.selectOne(new LambdaQueryWrapper<SysRole>().eq(SysRole::getRoleCode, "third_role"));
        //保存用户角色
        SysUserRole userRole = new SysUserRole();
        userRole.setRoleId(sysRole.getId());
        userRole.setUserId(userid);
        sysUserRoleMapper.insert(userRole);
    }

    @Override
    public List<SysUsers> queryByDepIds(List<String> departIds, String username) {
        return userMapper.queryByDepIds(departIds, username);
    }

    @Override
    public Map<String, String> getUserMap() {
        List<SysUsers> list = userMapper.getUserMap();
        Map<String, String> map = new HashMap<>();
        list.forEach(itme -> {
            map.put(itme.getId(), itme.getRealname());
        });
        return map;
    }

    @Override
    public Map<String, String> getUserIdAndNmaeMap() {
        List<SysUsers> list = userMapper.getUserMap();
        Map<String, String> map = new HashMap<>();
        list.forEach(itme -> {
            map.put(itme.getId(), itme.getRealname());
        });
        return map;
    }

    /**
     * 获取有效用户
     * "VALID" IS '1_有效用户 0_无效用户';
     *
     * @return
     */
    @Override
    public List<SysUsers> getList() {
        return userMapper.getUserList();
    }

    @Override
    public List<LoginUser> getUsersByLoginUser(String username) {

        return userMapper.getUsersByLoginUser(username);

    }

    @Override
    public String selectRealnameByUsername(String ownerId) {
        return userMapper.selectRealnameByUsername(ownerId);
    }

    @Override
    public Map<String, String> selectRealnameByUsername() {
        List<SysUsers> list = userMapper.getUserMap();
        Map<String, String> map = new HashMap<>();
        list.forEach(itme -> {
            map.put(itme.getUsername(), itme.getRealname());
        });
        return map;
    }

    @Override
    public String getUserid(String userid) {
        return userMapper.getUserid(userid);
    }


    @Override
    public void addUserByDepart(SysDepart sysDepart) {
        SysUsers user = null;//
        String selectedRoles = "1430057091121102850";//角色
        String selectedDeparts = "";//部门
        user = new SysUsers();
        String departName = sysDepart.getDepartName();
        if (-1 != departName.indexOf("(")) {
            departName = departName.substring(0, departName.indexOf("("));
        }
        String userName = ChineseCharToEnUtil.getPinYinHeadChar(departName).toLowerCase();
        SysUsers sysUser = getUserByName(userName);
        if (sysUser != null) {
            userName = userName + new Random().nextInt(9999);
        }
        if (userName.length() > 10 && userName.substring(0, 3).equals("sys")) {
            userName = userName.substring(3);
        }
        user.setUsername(userName);
        user.setRealname(sysDepart.getDepartName());
        user.setPassword("123456");
        //部门
        selectedDeparts = sysDepart.getId();
        try {
            addUser(user, selectedRoles, selectedDeparts);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public SysUsers getByUId(String s) {
        return super.baseMapper.getByUId(s);
    }

    @Override
    public SysUsers getUserByNameSpec(String s) {
        return baseMapper.getUserByNameSpec(s);
    }

    private void addUser(SysUsers user, String selectedRoles, String selectedDeparts) throws Exception {
        user.setCreateTime(new Date());//设置创建时间
        String salt = oConvertUtils.randomGen(8);
        user.setSalt(salt);
        String passwordEncode = PasswordUtil.encrypt(user.getUsername(), user.getPassword(), salt);
        user.setPassword(passwordEncode);
        user.setStatus(1);
        user.setDelFlag(CommonConstant.DEL_FLAG_0);
        addUserWithRole(user, selectedRoles);
        addUserWithDepart(user, selectedDeparts);
    }

    @Override
    public List<SysUsers> getUserByIds(List<String> sentTo) {
        return userMapper.getUserByIds(sentTo);
    }

    @Override
    public int syncUser(cn.hutool.json.JSONObject user) {
        String operType = user.getStr("oper");
        ResultCode verificationCode = SysUsers.verificationUser(user);
        ResultCode resultCode = ResultCode.CODE_1;
        if (resultCode != verificationCode) {
            return verificationCode.getCode();
        }
        String userId = user.getStr("id");
        String did = user.getStr("did");
        switch (operType) {
            case "add":
                if (isNotExistDepart(did)) {
                    //校验增加用户上级机构是否存在 如不存在 return -401
                    resultCode = ResultCode.CODE_401;
                } else if (isExistUser(userId)) {
                    //校验增加用户信任号是否重复  如重复 return -402
                    resultCode = ResultCode.CODE_402;
                } else {
                    //执行新增用户信息的数据库操作  return 1
                    SysUsers sysUsers = SysUsers.build(user);
                    sysUsers.setDelFlag(0);
                    sysUsers.setStatus(1);
                    final List<SysUsers> userList = super.baseMapper.selectList(new LambdaQueryWrapper<SysUsers>().like(SysUsers::getUsername, sysUsers.getUsername() + "%"));
                    final String num = maxNum(userList);
                    String account = String.format("%s%s", sysUsers.getUsername(), num);
                    sysUsers.setUsername(account);
                    sysUsers.generatePwd(account);

                    super.baseMapper.insert(sysUsers);
                    //部门关联
                    final String[] departIds = did.split(",");
                    if (oConvertUtils.isNotEmpty(departIds)) {
                        for (String departId : departIds) {
                            SysUserDepart userDepart = new SysUserDepart(sysUsers.getId(), departId);
                            sysUserDepartMapper.insert(userDepart);
                        }
                    }
                    //分配权限
                    String roleCode = String.valueOf(cacheUtils.getValueByKey("data_dict_key_caUpdate_roleCode"));
                    final SysRole sysRole = sysRoleMapper.selectOne(new LambdaQueryWrapper<SysRole>().eq(SysRole::getRoleCode, roleCode));
                    SysUserRole sysUserRole = new SysUserRole(userId, sysRole.getId());
                    sysUserRoleMapper.insert(sysUserRole);
                }
                break;
            case "mod":
                //修改操作
                if (!isExistUser(userId)) {
                    //校验修改用户信任号对应的用户是否存在 如不存在 return -501
                    resultCode = ResultCode.CODE_501;
                } else if (isNotExistDepart(did)) {
                    //校验修改用户时上级机构是否存在  如不存在 return -502
                    resultCode = ResultCode.CODE_502;
                } else {
                    //执行修改用户信息的数据库操作  return 1
                    SysUsers sysUsers = SysUsers.build(user);
                    super.baseMapper.updateById(sysUsers);
                    //修改部门
                    sysUserDepartMapper.delete(new QueryWrapper<SysUserDepart>().lambda().eq(SysUserDepart::getUserId, sysUsers.getId()));
                    final String[] departIds = did.split(",");
                    if (oConvertUtils.isNotEmpty(departIds)) {
                        for (String departId : departIds) {
                            SysUserDepart userDepart = new SysUserDepart(sysUsers.getId(), departId);
                            sysUserDepartMapper.insert(userDepart);
                        }
                    }
                }

                break;
            case "del":
                //取消授权操作
                //校验取消授权用户信任号对应的用户是否存在 如不存在 return -601
                if (!isExistUser(userId)) {
                    resultCode = ResultCode.CODE_601;
                }
                break;
            case "deluser":
                //删除用户操作
                if (!isExistUser(userId)) {
                    //校验删除用户时用户信任号对应的用户是否存在 如不存在 return -701
                    resultCode = ResultCode.CODE_701;
                } else {
                    super.baseMapper.deleteLogic(userId);
                }
                break;
            default:
                resultCode = ResultCode.CODE_803;
        }

        return resultCode.getCode();
    }

    @Autowired
    private DataSourceTypeUtils dataSourceTypeUtils;

    @Override
    public List<UserVo> list(String deptId) {
        if (dataSourceTypeUtils.getDBType().equals("postgresql")){
            return baseMapper.listByPostgresql(deptId);
        }else {
            return baseMapper.list(deptId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncZJGUser(List<SysUsers> sysUsers) {
        final String regularRole = String.valueOf(Optional.of(cacheUtils.getValueByKey(com.yuanqiao.insight.common.constant.CommonConstant.DATA_DICT_KEY + "ZJGSettings_regularRole")).get());
        List<String> ids = super.baseMapper.selectDELIds();
        if (CollUtil.isEmpty(ids)) {
            ids = new ArrayList<>();
        }
        int bound = 100;
        int length = ids.size();
        int page = length / bound;
        int remainder = length % bound;
        if (remainder > 0) {
            page += 1;
        }
        //防止sql超长
        for (int i = 0; i < page; i++) {
            List<String> perList = ids.stream().skip((long) i * bound).limit(bound).collect(Collectors.toList());
            baseMapper.deleteByIdS(perList);
            //删除关联部门
            sysUserDepartMapper.delete(new LambdaQueryWrapper<SysUserDepart>().in(SysUserDepart::getUserId, perList));
            //删除关联角色
            sysUserRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().in(SysUserRole::getUserId, perList));
        }
        for (SysUsers sysUser : sysUsers) {
            super.baseMapper.insert(sysUser);
            //部门关联
            List<String> organIds = new ArrayList<>();
            organIds.add(sysUser.getDepartIds());
            relatedDepart(organIds, sysUser);
            //分配权限
            final SysRole sysRole = sysRoleMapper.selectOne(new LambdaQueryWrapper<SysRole>().eq(SysRole::getRoleCode, regularRole));
            sysUserRoleMapper.insert(new SysUserRole(sysUser.getId(), sysRole.getId()));
        }

    }

    private boolean isExistUser(String userId) {
        final LambdaQueryWrapper<SysUsers> wrapper = new LambdaQueryWrapper<SysUsers>()
                .eq(SysUsers::getId, userId)
                .eq(SysUsers::getDelFlag, 0);
        return baseMapper.selectCount(wrapper) > 0;
    }

    private boolean isNotExistDepart(String departId) {
        final LambdaQueryWrapper<SysDepart> wrapper = new LambdaQueryWrapper<SysDepart>()
                .eq(SysDepart::getId, departId)
                .eq(SysDepart::getDelFlag, 0);
        return sysDepartMapper.selectCount(wrapper) <= 0;
    }

    private static String maxNum(List<SysUsers> userList) {
        int num = 1;
        if (CollUtil.isNotEmpty(userList)) {
            num = userList.stream()
                    .map(item -> Integer.parseInt(item.getUsername().substring(item.getUsername().length() - 3)))
                    .max(Comparator.comparingInt(o -> o))
                    .orElse(1) + 1;
        }
        return String.format("%03d", num);
    }

    public void syncZyUser(JSONArray user, boolean isFirst) {
        final String regularRole = String.valueOf(Optional.of(cacheUtils.getValueByKey(com.yuanqiao.insight.common.constant.CommonConstant.DATA_DICT_KEY + "zySettings_regularRole")).get());
        final String specialRole = String.valueOf(Optional.of(cacheUtils.getValueByKey(com.yuanqiao.insight.common.constant.CommonConstant.DATA_DICT_KEY + "zySettings_specialRole")).get());
        //系统管理员
        final String root = String.valueOf(cacheUtils.getValueByKey(com.yuanqiao.insight.common.constant.CommonConstant.DATA_DICT_KEY + "insight_threePowers_roleRoot"));
        //安全审计员
        final String auditadm = String.valueOf(cacheUtils.getValueByKey(com.yuanqiao.insight.common.constant.CommonConstant.DATA_DICT_KEY + "insight_threePowers_roleAuditadm"));
        //安全管理员
        final String secadm = String.valueOf(cacheUtils.getValueByKey(com.yuanqiao.insight.common.constant.CommonConstant.DATA_DICT_KEY + "insight_threePowers_roleSecadm"));
        for (int i = 0; i < user.size(); i++) {
            JSONObject userObj = user.getJSONObject(i);
            final Integer type = userObj.getInteger("type");
            SysUsers sysUsers = SysUsers.build(userObj);

            switch (type) {
                case 0:
                    super.baseMapper.deleteById(sysUsers.getId());
                    //删除关联部门
                    sysUserDepartMapper.delete(new LambdaQueryWrapper<SysUserDepart>().eq(SysUserDepart::getUserId, sysUsers.getId()));
                    //删除关联角色
                    sysUserRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, sysUsers.getId()));
                    break;
                case 1:
                    if (!isFirst) {
                        final SysUsers sysUsers1 = baseMapper.selectById(sysUsers.getId());
                        if (sysUsers1 != null) {
                            super.baseMapper.updateById(sysUsers);
                        } else {
                            saveZyUser(sysUsers);
                        }
                        //部门关联
                        relatedDepart(userObj.getJSONArray("organIds").toJavaList(String.class), sysUsers);
                        //分配权限
                        grantRole(userObj, auditadm, secadm, root, specialRole, regularRole, sysUsers);
                        break;
                    }
                case 2:
                    saveZyUser(sysUsers);
                    //部门关联
                    relatedDepart(userObj.getJSONArray("organIds").toJavaList(String.class), sysUsers);
                    //分配权限
                    grantRole(userObj, auditadm, secadm, root, specialRole, regularRole, sysUsers);
                    break;
                default:
            }
        }
    }

    private void saveZyUser(SysUsers sysUsers) {
        Object o = FillRuleUtil.executeRule("work_no_rule", new JSONObject());
        sysUsers.setWorkNo(String.valueOf(o));
        super.baseMapper.insert(sysUsers);
    }

    @Override
    public List<String> selectNumbers(String prefix) {
        return baseMapper.selectNumbers(prefix);
    }

    /**
     * 删除用户
     *
     * @param userId
     */
    @Override
    public void deleteUserByuserName(String userId) {
        LambdaQueryWrapper<SysUsers> sysUsersLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sysUsersLambdaQueryWrapper.eq(SysUsers::getUsername, userId);
        baseMapper.delete(sysUsersLambdaQueryWrapper);
    }

    /**
     * 批量删除用户
     *
     * @param userIds
     */
    @Override
    public void deleteUserByuserNames(List<String> userIds) {
        LambdaQueryWrapper<SysUsers> sysUsersLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sysUsersLambdaQueryWrapper.in(SysUsers::getUsername, userIds);
        baseMapper.delete(sysUsersLambdaQueryWrapper);
    }

    private void relatedDepart(List<String> organIds, SysUsers sysUsers) {
        sysUserDepartMapper.delete(new LambdaQueryWrapper<SysUserDepart>().eq(SysUserDepart::getUserId, sysUsers.getId()));
        if (CollUtil.isNotEmpty(organIds)) {
            for (String departId : organIds) {
                SysUserDepart userDepart = new SysUserDepart(sysUsers.getId(), departId.replace("-", ""));
                sysUserDepartMapper.insert(userDepart);
            }
        }
    }

    private void grantRole(JSONObject userObj, String auditadm, String secadm, String root, String specialRole, String regularRole, SysUsers sysUsers) {
        sysUserRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, sysUsers.getId()));
        final Integer isManager = userObj.getInteger("isManager");
        String roleCode = "";
        switch (isManager) {
            case 1:
                //安全审计员
                roleCode = auditadm;
                break;
            case 2:
                //安全管理员
                roleCode = secadm;
                break;
            case 4:
                //系统管理员
                roleCode = root;
                break;
            case 3:
            case 5:
            case 6:
            case 7:
                //安全审计员+安全管理员
                //安全审计员+系统管理员
                //安全管理员+系统管理员
                //安全审计员+安全管理员+系统管理员
                //使用专门的预制角色
                roleCode = specialRole;
                break;
            case 0:
            default:
                //普通用户
                roleCode = regularRole;
        }
        //角色关联
        final SysRole sysRole = sysRoleMapper.selectOne(new LambdaQueryWrapper<SysRole>().eq(SysRole::getRoleCode, roleCode));
        sysUserRoleMapper.insert(new SysUserRole(sysUsers.getId(), sysRole.getId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDirectly(List<String> userIds) {
        String ids = String.format("'%s'", String.join("','", userIds));
        // 1. 删除用户
        int line = userMapper.deleteDirectly(ids);
        // 2. 删除用户部门关系
        line += sysUserDepartMapper.delete(new LambdaQueryWrapper<SysUserDepart>().in(SysUserDepart::getUserId, userIds));
        //3. 删除用户角色关系
        line += sysUserRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().in(SysUserRole::getUserId, userIds));
        return line != 0;
    }

    @Override
    public IPage<SysUsers> getUsersWithDeptIds(Page<SysUsers> page, SysUsers sysUser) {
        String dbType = dataSourceTypeUtils.getDBType();
        if (dbType.equals("dm")) {
            return userMapper.getUsersWithDeptIdsForDM(page, sysUser);
        }else if (dbType.equals("highgo")) {
            return userMapper.getUsersWithDeptIdsByHg(page, sysUser);
        }else {
            return userMapper.getUsersWithDeptIds(page, sysUser);
        }
    }

}
