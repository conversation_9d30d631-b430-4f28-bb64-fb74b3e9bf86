{"priority": 600, "template": {"settings": {"index": {"lifecycle": {"name": "metrics-lifecycle-policy"}, "codec": "best_compression", "number_of_shards": "1", "number_of_replicas": "1"}}, "mappings": {"dynamic_templates": [{"tags": {"path_match": "tag.*", "mapping": {"ignore_above": 512, "type": "keyword"}, "match_mapping_type": "string"}}, {"metrics_long": {"mapping": {"index": false, "type": "float"}, "match_mapping_type": "long"}}, {"metrics_double": {"mapping": {"index": false, "type": "float"}, "match_mapping_type": "double"}}, {"sysUptime_as_keyword": {"mapping": {"type": "keyword"}, "match": "sysUptime"}}], "properties": {"@timestamp": {"format": "yyyy-MM-dd HH:mm:ss", "type": "date"}, "measurement_name": {"type": "keyword"}, "tag": {"type": "object"}}}}, "index_patterns": ["all_value*", "metrics-cloud-*", "metrics-vmware-*", "metrics-database-*", "metrics-firewalls-*", "metrics-gatekeeper-*", "metrics-gateway-*", "metrics-middleware-*", "metrics-rest-*", "metrics-router-*", "metrics-safedevice-*", "metrics-server-*", "metrics-storageproducts-*", "metrics-switch-*", "metrics-terminal-*", "metrics-vcenter-*", "metrics-zgcloud-*", "rest_heartbeat*", "server_heartbeat*"], "data_stream": {"hidden": false}}