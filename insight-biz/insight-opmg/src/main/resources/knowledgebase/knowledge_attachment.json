{"description": "Extract attachment information from arrays", "processors": [{"foreach": {"field": "attachments", "processor": {"attachment": {"target_field": "_ingest._value.attachment", "field": "_ingest._value.data", "indexed_chars": -1, "resource_name": "_ingest._value.originalFilename"}}}}, {"foreach": {"field": "attachments", "processor": {"html_strip": {"ignore_missing": true, "field": "_ingest._value.attachment.content"}}}}, {"script": {"lang": "painless", "source": "ArrayList indexAttachments = new ArrayList();ctx.attachments.stream().forEach(x -> {if(x.attachment.content != null && x.attachment.content_length > 0){x.isIndex = true;indexAttachments.add(1);}else{x.isIndex = false;indexAttachments.add(0);}});ctx.indexFile = indexAttachments.join(\",\");"}}]}