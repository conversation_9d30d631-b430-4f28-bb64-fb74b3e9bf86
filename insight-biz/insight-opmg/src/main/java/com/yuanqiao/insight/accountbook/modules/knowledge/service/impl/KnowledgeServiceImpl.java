package com.yuanqiao.insight.accountbook.modules.knowledge.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.accountbook.modules.knowledge.entity.Knowledge;
import com.yuanqiao.insight.accountbook.modules.knowledge.mapper.KnowledgeMapper;
import com.yuanqiao.insight.accountbook.modules.knowledge.service.IKnowledgeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 事件、问题知识库
 * @Author: jeecg-boot
 * @Date:   2020-09-18
 * @Version: V1.0
 */
@Slf4j
@Service
public class KnowledgeServiceImpl extends ServiceImpl<KnowledgeMapper, Knowledge> implements IKnowledgeService {

    @Autowired
    private KnowledgeMapper knowledgeMapper;

    /**
     * 模糊查询
     *
     * @param title 标题
     * @return
     */
    @Override
    public List<Knowledge> getFuzzyQueryByTitle(String title) {
        log.info("title={}",title);
        log.info("com.yuanqiao.insight.accountbook.modules.knowledge.service.impl.KnowledgeServiceImpl.getFuzzyQueryByTitle(titile={})",title);
        return knowledgeMapper.getFuzzyQueryByTitle(title);
    }

    @Override
    public List<Knowledge> getByTypes(List<String> types) {
        log.info("types={}",types);
        log.info("com.yuanqiao.insight.accountbook.modules.knowledge.service.impl.KnowledgeServiceImpl.getByTypes(types={})",types);
        return this.baseMapper.getByTypes(types);
    }
}
