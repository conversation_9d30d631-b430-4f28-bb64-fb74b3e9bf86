package com.yuanqiao.insight.accountbook.modules.devopsipplanextend.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.accountbook.modules.devopsipplanextend.entity.DevopsIpPlanExtend;
import com.yuanqiao.insight.accountbook.modules.devopsipplanextend.mapper.DevopsIpPlanExtendMapper;
import com.yuanqiao.insight.accountbook.modules.devopsipplanextend.service.IDevopsIpPlanExtendService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: IP规划附加字段
 * @Author: jeecg-boot
 * @Date: 2024-02-28
 * @Version: V1.0
 */
@Service
public class DevopsIpPlanExtendServiceImpl extends ServiceImpl<DevopsIpPlanExtendMapper, DevopsIpPlanExtend> implements IDevopsIpPlanExtendService {
    @Autowired
    private DevopsIpPlanExtendMapper devopsIpPlanExtendMapper;

    @Override
    public List<DevopsIpPlanExtend> findCodeInfo(String planId) {
        return devopsIpPlanExtendMapper.findCodeInfo(planId);
    }
}
