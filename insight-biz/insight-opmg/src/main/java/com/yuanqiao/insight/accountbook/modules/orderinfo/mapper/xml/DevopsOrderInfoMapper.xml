<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.accountbook.modules.orderinfo.mapper.DevopsOrderInfoMapper">
    <select id="queryByIds" resultType="com.yuanqiao.insight.accountbook.modules.orderinfo.entity.DevopsOrderInfo">
        select * from devops_order_info where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="queryAllocDate" resultType="com.yuanqiao.insight.accountbook.modules.orderinfo.entity.DevopsOrderInfo">
        SELECT * FROM devops_order_info
        <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
            WHERE alloc_time between #{startDate} and #{endDate}
        </if>

    </select>
    <select id="queryAllocTimelyDate"
            resultType="com.yuanqiao.insight.accountbook.modules.orderinfo.entity.DevopsOrderInfo">
        SELECT * FROM devops_order_info WHERE response_second &lt; 3600 and order_state = 1
        <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
            and alloc_time between #{startDate} and #{endDate}
        </if>
    </select>

    <select id="queryHandleDate" resultType="com.yuanqiao.insight.accountbook.modules.orderinfo.entity.DevopsOrderInfo">
        SELECT * FROM devops_order_info WHERE order_state = 2
        <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
            and handle_end_time between #{startDate} and #{endDate}
        </if>
    </select>

    <select id="queryHandleTimelyDate"
            resultType="com.yuanqiao.insight.accountbook.modules.orderinfo.entity.DevopsOrderInfo">
        SELECT * FROM devops_order_info WHERE handle_second &lt; 3600 and order_state = 2
        <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
            and handle_end_time between #{startDate} and #{endDate}
        </if>
    </select>

    <select id="queryAllDate2" resultType="com.yuanqiao.insight.accountbook.modules.orderinfo.entity.DevopsOrderInfo">
        SELECT * FROM devops_order_info
        <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
            WHERE create_time between #{startDate} and #{endDate}
        </if>
    </select>

    <select id="queryAllDate" resultType="com.yuanqiao.insight.accountbook.modules.orderinfo.entity.HandOrderVO">
        select count(u.create_time) as value,s.date as name from
        (select date_add(#{date},interval @i:=@i+1 day) as date
        from (
        select 1
        <foreach item="index" collection="countArr">
            union all select 1
        </foreach>
        union all select 1) as tmp,
        (select @i:= -1) t
        ) s left join devops_order_info u on s.date + '00:00:00' &lt;= u.create_time &lt;= s.date + '59:59:59'
        GROUP BY s.date

    </select>

    <select id="typeCount" resultType="com.yuanqiao.insight.accountbook.modules.orderinfo.entity.TypeCountVo">
        select
        order_description as name ,
        ROUND(count(id)/(select count(id) from devops_order_info ), 2) as value ,
        @rank := '%' as `unit`
        from
        devops_order_info
        <where>
            <if test="endDate!= null and endDate.trim()!=''  and startDate!= null  and startDate.trim()!='' ">
                create_time between #{startDate} and #{endDate}
            </if>
        </where>

        group by
        order_description
        order by
        value desc
    </select>
    <select id="getTodoUser"
            resultType="com.yuanqiao.insight.accountbook.modules.orderinfo.entity.DevopsOrderInfo">
        SELECT * FROM devops_order_info WHERE  handler_user_id =#{username}  and order_state = 1
    </select>


    <!--<select id="dayCount" resultType="com.yuanqiao.insight.accountbook.modules.orderinfo.entity.HandOrderVO">-->
                  <!--select-->
      <!--date_format( create_time, '%Y-%m-%d' ) name ,count(id) value-->
     <!--from-->
      <!--operate_history_info-->
        <!--<where>-->
            <!--<if test="endDate!= null and endDate.trim()!=''  and startDate!= null  and startDate.trim()!='' ">-->
                <!--create_time between #{startDate} and #{endDate}-->
            <!--</if>-->
        <!--</where>-->
      <!--group by name-->

    <!--</select>-->



</mapper>
