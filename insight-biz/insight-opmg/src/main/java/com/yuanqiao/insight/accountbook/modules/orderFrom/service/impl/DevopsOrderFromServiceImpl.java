package com.yuanqiao.insight.accountbook.modules.orderFrom.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.accountbook.modules.orderFrom.entity.DevopsOrderFrom;
import com.yuanqiao.insight.accountbook.modules.orderFrom.mapper.DevopsOrderFromMapper;
import com.yuanqiao.insight.accountbook.modules.orderFrom.service.DevopsOrderFromService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;


@Slf4j
@Service
public class DevopsOrderFromServiceImpl extends ServiceImpl<DevopsOrderFromMapper, DevopsOrderFrom> implements Job,DevopsOrderFromService {

    @Autowired
    private DevopsOrderFromService devopsOrderFromService;
    private static final ExecutorService pools = Executors.newCachedThreadPool();

    @Override
    public void addOrderInfo(String userName) {
        DevopsOrderFrom devopsOrderFrom = new DevopsOrderFrom();
        devopsOrderFrom.setImplementUser(userName);
        devopsOrderFrom.setIsHandle(1);
        devopsOrderFromService.save(devopsOrderFrom);
    }


    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        JobDataMap mergedJobDataMap = jobExecutionContext.getMergedJobDataMap();
        String parameter = mergedJobDataMap.getString("parameter");
        pools.execute(new TaskIdRunnable(parameter));
    }

    class TaskIdRunnable implements Runnable {

        private final String parameter;
        public TaskIdRunnable(String parameter) {
            this.parameter = parameter;
        }
        @Override
        public void run() {
            log.info("======人工巡检任务执行====="+parameter);

            devopsOrderFromService.addOrderInfo(parameter);
        }
    }
}
