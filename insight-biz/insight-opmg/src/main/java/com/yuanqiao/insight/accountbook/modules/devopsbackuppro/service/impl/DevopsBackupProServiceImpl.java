package com.yuanqiao.insight.accountbook.modules.devopsbackuppro.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.accountbook.modules.devopsBackupTask.entity.DevopsBackupTask;
import com.yuanqiao.insight.accountbook.modules.devopsBackupTask.service.IDevopsBackupTaskService;
import com.yuanqiao.insight.accountbook.modules.devopsbackuppro.entity.DevopsBackupPro;
import com.yuanqiao.insight.accountbook.modules.devopsbackuppro.mapper.DevopsBackupProMapper;
import com.yuanqiao.insight.accountbook.modules.devopsbackuppro.service.IDevopsBackupProService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 备份策略表
 * @Author: jeecg-boot
 * @Date: 2021-03-22
 * @Version: V1.0
 */
@Slf4j
@Service
public class DevopsBackupProServiceImpl extends ServiceImpl<DevopsBackupProMapper, DevopsBackupPro> implements IDevopsBackupProService {

    @Autowired
    private IDevopsBackupTaskService devopsBackupTaskService;

    @Override
    public Map<String, String> getState() {
        List<DevopsBackupPro> list = this.list();
        log.info("list={}", list);
        log.info("com.yuanqiao.insight.accountbook.modules.devopsbackuppro.service.impl.DevopsBackupProServiceImpl.getState(list={})", list);
        Map<String, String> effectMap = new HashMap<>(list.size());
        list.forEach(item -> {
            effectMap.put(item.getId(), item.getExecType());
        });
        return effectMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTask(DevopsBackupPro devopsBackupPro, List<DevopsBackupTask> devopsBackupTask) {
        this.updateById(devopsBackupPro);
        for (DevopsBackupTask backupTask : devopsBackupTask) {
            devopsBackupTaskService.updateAndRestTask(backupTask);
        }
    }
}
