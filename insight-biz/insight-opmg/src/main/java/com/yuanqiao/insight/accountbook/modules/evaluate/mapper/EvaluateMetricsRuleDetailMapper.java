package com.yuanqiao.insight.accountbook.modules.evaluate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateMetricsRuleDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName EvaluateMetricsRuleDetailMapper
 * @description: 评估指标规则详情Mapper
 * @datetime 2025年 07月 28日
 * @version: 1.0
 */
@Mapper
public interface EvaluateMetricsRuleDetailMapper extends BaseMapper<EvaluateMetricsRuleDetail> {
    
    /**
     * 根据指标ID查询规则详情列表
     */
    @Select("SELECT * FROM devops_evaluate_metrics_rule_detail WHERE metrics_id = #{metricsId} AND status = 1 ORDER BY sort_order")
    List<EvaluateMetricsRuleDetail> selectByMetricsId(@Param("metricsId") String metricsId);
    
    /**
     * 根据指标ID删除规则详情
     */
    @Delete("DELETE FROM devops_evaluate_metrics_rule_detail WHERE metrics_id = #{metricsId}")
    int deleteByMetricsId(@Param("metricsId") String metricsId);
    
    /**
     * 根据规则配置ID删除规则详情
     */
    @Delete("DELETE FROM devops_evaluate_metrics_rule_detail WHERE rule_config_id = #{ruleConfigId}")
    int deleteByRuleConfigId(@Param("ruleConfigId") String ruleConfigId);
}
