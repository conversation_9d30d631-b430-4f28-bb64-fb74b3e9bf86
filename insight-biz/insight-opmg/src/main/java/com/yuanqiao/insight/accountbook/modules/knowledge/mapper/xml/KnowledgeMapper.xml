<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.accountbook.modules.knowledge.mapper.KnowledgeMapper">

    <select id="getFuzzyQueryByTitle" resultType="com.yuanqiao.insight.accountbook.modules.knowledge.entity.Knowledge">
      select * from ump_knowledge where title like '%${title}%'
    </select>


    <select id="getByTypes" resultType="com.yuanqiao.insight.accountbook.modules.knowledge.entity.Knowledge">
        select * from ump_knowledge where type in
        <foreach collection="types" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>

    </select>
</mapper>
