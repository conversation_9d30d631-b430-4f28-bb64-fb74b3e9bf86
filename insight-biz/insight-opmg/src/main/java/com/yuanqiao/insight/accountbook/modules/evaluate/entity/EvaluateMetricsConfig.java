package com.yuanqiao.insight.accountbook.modules.evaluate.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName EvaluateMetricsConfig
 * @description: 评估指标配置表（字段配置+规则配置统一管理）
 * @datetime 2025年 07月 28日
 * @version: 1.0
 */
@Data
@TableName("devops_evaluate_metrics_config")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="devops_evaluate_metrics_config对象", description="评估指标配置表")
public class EvaluateMetricsConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "指标ID")
    private String metricsId;

    @ApiModelProperty(value = "Tab2字段配置JSON（k-form-design完整JSON）")
    private String fieldJson;

    @ApiModelProperty(value = "Tab3规则配置JSON（k-form-design完整JSON）")
    private String ruleJson;

    @ApiModelProperty(value = "配置版本号")
    private String version;

    @ApiModelProperty(value = "状态：1-启用，0-禁用")
    private Integer status;

    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;

    @Dict(dictTable = "sys_users", dicText = "realname", dicCode = "username")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;

    @Dict(dictTable = "sys_users", dicText = "realname", dicCode = "username")
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;

    // 非数据库字段，用于返回解析后的数据
    @TableField(exist = false)
    @ApiModelProperty(value = "评估字段列表")
    private List<EvaluationFieldInfo> evaluationFields;

    @TableField(exist = false)
    @ApiModelProperty(value = "关联的指标信息")
    private EvaluateMetricsInfo metricsInfo;
}
