package com.yuanqiao.insight.accountbook.modules.devopsipmanage.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanqiao.insight.accountbook.modules.devopsipmanage.entity.DevopsIpManage;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: ip地址白名单
 * @Author: jeecg-boot
 * @Date:   2021-03-17
 * @Version: V1.0
 */
@Component
public interface DevopsIpManageMapper extends BaseMapper<DevopsIpManage> {

    /**
     * 通过IP查询
     * @param ip IP地址
     * @return
     */
    List<DevopsIpManage> queryListByIp(String ip);

    /**
     * 通过IPS查询数据
     * @param ips
     * @return
     */
    List<DevopsIpManage> queryListByIps(@Param("ips") List<String> ips);

    /**
     * 通过MAC查询
     * @param mac MAC地址
     * @return
     */
    List<DevopsIpManage> queryListByMac(String mac);

    /**
     * 通过IP和MAC查询
     * @param ip   IP地址
     * @param mac  MAC地址
     * @return
     */
    List<DevopsIpManage> queryListByIpAndMac(@Param("ip") String ip,@Param("mac") String mac);


    List<String> findRealnames (@Param("list")List<String> list);

}
