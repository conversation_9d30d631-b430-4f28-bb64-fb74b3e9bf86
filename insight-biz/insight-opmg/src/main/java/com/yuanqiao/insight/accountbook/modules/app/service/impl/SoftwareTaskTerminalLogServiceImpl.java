package com.yuanqiao.insight.accountbook.modules.app.service.impl;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.yuanqiao.insight.accountbook.modules.app.entity.SoftwareTaskTerminalLog;
import com.yuanqiao.insight.accountbook.modules.app.mapper.SoftwareTaskTerminalLogMapper;
import com.yuanqiao.insight.accountbook.modules.app.service.ISoftwareTaskTerminalLogService;
import org.springframework.stereotype.Service;

/**
 * @Description: 任务管理-终端升级日志
 * @Author: jeecg-boot
 * @Date:   2024-11-16
 * @Version: V1.0
 */
@Service
public class SoftwareTaskTerminalLogServiceImpl extends MPJBaseServiceImpl<SoftwareTaskTerminalLogMapper, SoftwareTaskTerminalLog> implements ISoftwareTaskTerminalLogService {

}
