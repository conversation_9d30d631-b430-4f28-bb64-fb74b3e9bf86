package com.yuanqiao.insight.accountbook.modules.arrangementlocation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;

/**
 * @Description: 值班记录表
 * @Author: jeecg-boot
 * @Date:   2021-03-12
 * @Version: V1.0
 */
@Data
@TableName("devops_arrangement_location")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="devops_arrangement_location对象", description="值班记录地点表")
public class DevopsArrangementLocation implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**排班id*/
	@Excel(name = "排班id", width = 15)
    @ApiModelProperty(value = "排班id")
    private String arrangementId;

	/**上班打卡地点*/
	@Excel(name = "上班打卡地点", width = 15)
    @ApiModelProperty(value = "上班打卡地点")
    private String upWorkLocation;
	/**下班打卡地点*/
	@Excel(name = "下班打卡地点", width = 15)
    @ApiModelProperty(value = "下班打卡地点")
    private String downWorkLocation;

}
