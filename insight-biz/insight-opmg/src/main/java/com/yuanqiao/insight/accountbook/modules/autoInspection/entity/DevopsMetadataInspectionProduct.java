package com.yuanqiao.insight.accountbook.modules.autoInspection.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;

/**
 * @Description: 巡检任务和产品关系表
 * @Author: jeecg-boot
 * @Date:   2021-03-13
 * @Version: V1.0
 */
@Data
@TableName("devops_metadata_inspection_product")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="devops_metadata_inspection_product对象", description="巡检任务和产品关系表")
public class DevopsMetadataInspectionProduct implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**创建人*//*
    @ApiModelProperty(value = "创建人")
    private String createBy;
	*//**创建日期*//*
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	*//**更新人*//*
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	*//**更新日期*//*
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	*//**所属部门*//*
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;*/
	/**巡检任务id*/
	@Excel(name = "巡检任务id", width = 15)
    @ApiModelProperty(value = "巡检任务id")
    private String taskId;
	/**产品id*/
	@Excel(name = "产品id", width = 15)
    @ApiModelProperty(value = "产品id")
    private String productId;
}
