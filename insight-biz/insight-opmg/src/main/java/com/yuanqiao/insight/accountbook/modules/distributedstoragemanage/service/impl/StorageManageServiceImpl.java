package com.yuanqiao.insight.accountbook.modules.distributedstoragemanage.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.accountbook.modules.distributedstoragemanage.entity.DistributedStorageCluster;
import com.yuanqiao.insight.accountbook.modules.distributedstoragemanage.mapper.DistributedStorageClusterMapper;
import com.yuanqiao.insight.accountbook.modules.distributedstoragemanage.service.StorageManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


@Service
@Slf4j
public class StorageManageServiceImpl extends ServiceImpl<DistributedStorageClusterMapper, DistributedStorageCluster> implements StorageManageService {

}
