package com.yuanqiao.insight.accountbook.modules.app.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.yuanqiao.insight.accountbook.modules.app.entity.Policy;
import com.yuanqiao.insight.accountbook.modules.app.entity.PolicyScope;
import com.yuanqiao.insight.accountbook.modules.app.mapper.PolicyMapper;
import com.yuanqiao.insight.accountbook.modules.app.service.IPolicyScopeService;
import com.yuanqiao.insight.accountbook.modules.app.service.IPolicyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 软件策略
 * @Author: jeecg-boot
 * @Date:   2024-11-14
 * @Version: V1.0
 */
@Service
public class PolicyServiceImpl extends MPJBaseServiceImpl<PolicyMapper, Policy> implements IPolicyService {

    @Autowired
    private IPolicyScopeService policyScopeService;

    @Override
    public void savePolicy(Policy policy) {
        int insert = super.baseMapper.insert(policy);
        if(insert > 0){
            saveScopes(policy.getId(), policy.getPolicyScopes());
        }

    }

    @Override
    public void updatePolicy(Policy policy) {
        int flag = super.baseMapper.updateById(policy);
        if(flag > 0){
            policyScopeService.remove(new LambdaQueryWrapper<PolicyScope>().eq(PolicyScope::getPolicyId, policy.getId()));
            saveScopes(policy.getId(), policy.getPolicyScopes());
        }
    }

    private void saveScopes(String policyId,String scopes) {
        JSONObject scopesJson = JSON.parseObject(scopes);
        List<PolicyScope> policyScopes = new ArrayList<>();
        scopesJson.forEach((String key, Object value) -> {
            PolicyScope policyScope = new PolicyScope();
            policyScope.setScopeType(key);
            policyScope.setRelateId(value.toString());
            policyScope.setPolicyId(policyId);
            policyScope.setIsException(0);
            policyScope.setDelFlag(0);
            policyScopes.add(policyScope);
        });
        policyScopeService.saveBatch(policyScopes);
    }

    @Override
    public void removePolicys(List<String> ids) {
        LambdaUpdateWrapper<PolicyScope> set = new LambdaUpdateWrapper<PolicyScope>().in(PolicyScope::getPolicyId, ids).set(PolicyScope::getDelFlag, 1);
        policyScopeService.update(set);
        super.baseMapper.update(null,new LambdaUpdateWrapper<Policy>().in(Policy::getId, ids).set(Policy::getDelFlag, 1));
    }
}
