package com.yuanqiao.insight.accountbook.modules.software.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yuanqiao.insight.acore.validation.AddGroup;
import com.yuanqiao.insight.acore.validation.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.IsLikeQueryColumn;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 软件管理表
 * @Author: jeecg-boot
 * @Date:   2021-03-11
 * @Version: V1.0
 */

@Data
@TableName("devope_software_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="devops_software_info对象", description="软件管理表")
public class DevopsSoftwareInfo implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    @NotBlank(message = "id 不能为Null",groups = {UpdateGroup.class})
    private String id;
	/**软件名称*/
	@IsLikeQueryColumn
	@Excel(name = "软件名称", width = 15)
    @ApiModelProperty(value = "软件名称")
    @NotBlank(message="软件名称不能为空",groups = {AddGroup.class, UpdateGroup.class})
    private String softwareName;
	/**描述*/
	@Excel(name = "描述信息", width = 15)
    @ApiModelProperty(value = "描述")
//    @Pattern(regexp = "0?(13|14|15|17|18|19)[0-9]{9}", message = "手机号格式不正确")
//    @Phone(groups = {AddGroup.class,UpdateGroup.class})
//    @Email()
    private String softwareDescribe;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
}
