package com.yuanqiao.insight.accountbook.modules.evaluate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateMetricsFieldConfig;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName IEvaluateMetricsFieldConfigService
 * @description: 评估指标字段配置服务接口
 * @datetime 2025年 07月 28日
 * @version: 1.0
 */
public interface IEvaluateMetricsFieldConfigService extends IService<EvaluateMetricsFieldConfig> {
    
    /**
     * 保存指标字段配置
     * @param metricsId 指标ID
     * @param formConfig 表单配置JSON
     * @return 是否保存成功
     */
    boolean saveFieldConfig(String metricsId, String formConfig);
    
    /**
     * 根据指标ID获取字段配置
     * @param metricsId 指标ID
     * @return 字段配置
     */
    EvaluateMetricsFieldConfig getByMetricsId(String metricsId);
    
    /**
     * 获取指标的评估字段列表
     * @param metricsId 指标ID
     * @return 评估字段列表
     */
    List<EvaluationFieldInfo> getEvaluationFields(String metricsId);
    
    /**
     * 更新指标字段配置
     * @param metricsId 指标ID
     * @param formConfig 表单配置JSON
     * @return 是否更新成功
     */
    boolean updateFieldConfig(String metricsId, String formConfig);
    
    /**
     * 删除指标字段配置
     * @param metricsId 指标ID
     * @return 是否删除成功
     */
    boolean removeByMetricsId(String metricsId);
}
