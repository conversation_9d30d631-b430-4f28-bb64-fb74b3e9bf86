package com.yuanqiao.insight.accountbook.modules.evaluate.controller;

import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateMetricsRuleConfig;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluationRuleInfo;
import com.yuanqiao.insight.accountbook.modules.evaluate.service.IEvaluateMetricsRuleConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName EvaluateMetricsRuleConfigController
 * @description: 评估指标规则配置控制器
 * @datetime 2025年 07月 28日
 * @version: 1.0
 */
@Api(tags = "评估指标规则配置")
@RestController
@RequestMapping("/evaluate/metricsRuleConfig")
@Slf4j
public class EvaluateMetricsRuleConfigController extends JeecgController<EvaluateMetricsRuleConfig, IEvaluateMetricsRuleConfigService> {

    @Autowired
    private IEvaluateMetricsRuleConfigService ruleConfigService;

    @AutoLog(value = "保存指标评估规则配置")
    @ApiOperation(value = "保存指标评估规则配置", notes = "Tab3保存评估规则配置")
    @PostMapping(value = "/save/{metricsId}")
    public Result<?> saveRuleConfig(@PathVariable("metricsId") String metricsId,
                                  @RequestBody Map<String, Object> requestData) {
        try {
            String ruleConfig = (String) requestData.get("ruleConfig");
            if (ruleConfig == null || ruleConfig.trim().isEmpty()) {
                return Result.error("评估规则配置不能为空");
            }

            boolean result = ruleConfigService.saveRuleConfig(metricsId, ruleConfig);
            if (result) {
                return Result.OK("保存成功！");
            } else {
                return Result.error("保存失败！");
            }
        } catch (Exception e) {
            log.error("保存指标评估规则配置失败", e);
            return Result.error("保存失败：" + e.getMessage());
        }
    }

    @AutoLog(value = "获取指标评估规则配置")
    @ApiOperation(value = "获取指标评估规则配置", notes = "获取指标评估规则配置")
    @GetMapping(value = "/get/{metricsId}")
    public Result<EvaluateMetricsRuleConfig> getRuleConfig(@PathVariable("metricsId") String metricsId) {
        try {
            EvaluateMetricsRuleConfig config = ruleConfigService.getByMetricsId(metricsId);
            return Result.OK(config);
        } catch (Exception e) {
            log.error("获取指标评估规则配置失败", e);
            return Result.error("获取失败：" + e.getMessage());
        }
    }

    @AutoLog(value = "获取评估规则列表")
    @ApiOperation(value = "获取评估规则列表", notes = "获取指标的评估规则列表")
    @GetMapping(value = "/rules/{metricsId}")
    public Result<List<EvaluationRuleInfo>> getEvaluationRules(@PathVariable("metricsId") String metricsId) {
        try {
            List<EvaluationRuleInfo> rules = ruleConfigService.getEvaluationRules(metricsId);
            return Result.OK(rules);
        } catch (Exception e) {
            log.error("获取评估规则列表失败", e);
            return Result.error("获取失败：" + e.getMessage());
        }
    }

    @AutoLog(value = "更新指标评估规则配置")
    @ApiOperation(value = "更新指标评估规则配置", notes = "更新指标评估规则配置")
    @PutMapping(value = "/update/{metricsId}")
    public Result<?> updateRuleConfig(@PathVariable("metricsId") String metricsId,
                                    @RequestBody Map<String, Object> requestData) {
        try {
            String ruleConfig = (String) requestData.get("ruleConfig");
            boolean result = ruleConfigService.updateRuleConfig(metricsId, ruleConfig);
            if (result) {
                return Result.OK("更新成功！");
            } else {
                return Result.error("更新失败！");
            }
        } catch (Exception e) {
            log.error("更新指标评估规则配置失败", e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }

    @AutoLog(value = "删除指标评估规则配置")
    @ApiOperation(value = "删除指标评估规则配置", notes = "删除指标评估规则配置")
    @DeleteMapping(value = "/delete/{metricsId}")
    public Result<?> deleteRuleConfig(@PathVariable("metricsId") String metricsId) {
        try {
            boolean result = ruleConfigService.removeByMetricsId(metricsId);
            if (result) {
                return Result.OK("删除成功！");
            } else {
                return Result.error("删除失败！");
            }
        } catch (Exception e) {
            log.error("删除指标评估规则配置失败", e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }
}
