package com.yuanqiao.insight.accountbook.modules.evaluate.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateMetricsFieldConfig;
import com.yuanqiao.insight.accountbook.modules.evaluate.service.IEvaluateMetricsFieldConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.util.UUIDGenerator;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName EvaluateMetricsFieldConfigServiceImpl
 * @description: 评估指标字段配置服务实现类
 * @datetime 2025年 07月 28日
 * @version: 1.0
 */
@Service
@Slf4j
public class EvaluateMetricsFieldConfigServiceImpl extends ServiceImpl<EvaluateMetricsFieldConfigMapper, EvaluateMetricsFieldConfig> implements IEvaluateMetricsFieldConfigService {

    @Override
    public boolean saveFieldConfig(String metricsId, String formConfig) {
        try {
            // 解析表单配置，统计字段信息
            JSONObject configJson = JSON.parseObject(formConfig);
            JSONArray fieldList = configJson.getJSONArray("list");
            
            int fieldCount = 0;
            int evaluationFieldCount = 0;
            
            if (fieldList != null) {
                fieldCount = fieldList.size();
                for (int i = 0; i < fieldList.size(); i++) {
                    JSONObject field = fieldList.getJSONObject(i);
                    JSONObject options = field.getJSONObject("options");
                    if (options != null && options.getBooleanValue("isEvaluationField")) {
                        evaluationFieldCount++;
                    }
                }
            }
            
            // 检查是否已存在配置
            EvaluateMetricsFieldConfig existConfig = baseMapper.selectByMetricsId(metricsId);
            
            if (existConfig != null) {
                // 更新现有配置
                existConfig.setFormConfig(formConfig);
                existConfig.setFieldCount(fieldCount);
                existConfig.setEvaluationFieldCount(evaluationFieldCount);
                existConfig.setUpdateTime(new Date());
                return this.updateById(existConfig);
            } else {
                // 创建新配置
                EvaluateMetricsFieldConfig newConfig = new EvaluateMetricsFieldConfig();
                newConfig.setId(UUIDGenerator.generate());
                newConfig.setMetricsId(metricsId);
                newConfig.setFormConfig(formConfig);
                newConfig.setFieldCount(fieldCount);
                newConfig.setEvaluationFieldCount(evaluationFieldCount);
                newConfig.setCreateTime(new Date());
                return this.save(newConfig);
            }
        } catch (Exception e) {
            log.error("保存指标字段配置失败", e);
            return false;
        }
    }

    @Override
    public EvaluateMetricsFieldConfig getByMetricsId(String metricsId) {
        return baseMapper.selectByMetricsId(metricsId);
    }

    @Override
    public List<EvaluationFieldInfo> getEvaluationFields(String metricsId) {
        List<EvaluationFieldInfo> evaluationFields = new ArrayList<>();
        
        try {
            EvaluateMetricsFieldConfig config = baseMapper.selectByMetricsId(metricsId);
            if (config == null || StringUtils.isBlank(config.getFormConfig())) {
                return evaluationFields;
            }
            
            JSONObject configJson = JSON.parseObject(config.getFormConfig());
            JSONArray fieldList = configJson.getJSONArray("list");
            
            if (fieldList != null) {
                for (int i = 0; i < fieldList.size(); i++) {
                    JSONObject field = fieldList.getJSONObject(i);
                    JSONObject options = field.getJSONObject("options");
                    
                    if (options != null && options.getBooleanValue("isEvaluationField")) {
                        EvaluationFieldInfo fieldInfo = new EvaluationFieldInfo();
                        fieldInfo.setValue(field.getString("model"));
                        fieldInfo.setLabel(field.getString("label"));
                        fieldInfo.setFieldType(field.getString("type"));
                        fieldInfo.setDescription(field.getString("help"));
                        
                        // 检查是否必填
                        JSONArray rules = field.getJSONArray("rules");
                        boolean required = false;
                        if (rules != null) {
                            for (int j = 0; j < rules.size(); j++) {
                                JSONObject rule = rules.getJSONObject(j);
                                if (rule.getBooleanValue("required")) {
                                    required = true;
                                    break;
                                }
                            }
                        }
                        fieldInfo.setRequired(required);
                        
                        evaluationFields.add(fieldInfo);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取评估字段列表失败", e);
        }
        
        return evaluationFields;
    }

    @Override
    public boolean updateFieldConfig(String metricsId, String formConfig) {
        return saveFieldConfig(metricsId, formConfig);
    }

    @Override
    public boolean removeByMetricsId(String metricsId) {
        int result = baseMapper.deleteByMetricsId(metricsId);
        return result > 0;
    }
}
