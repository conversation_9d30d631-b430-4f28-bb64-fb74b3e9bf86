package com.yuanqiao.insight.accountbook.modules.evaluate.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @ClassName EvaluationRuleInfo
 * @description: 评估规则信息
 * @datetime 2025年 07月 28日
 * @version: 1.0
 */
@Data
@ApiModel(value="EvaluationRuleInfo", description="评估规则信息")
public class EvaluationRuleInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "评估字段key")
    private String fieldKey;

    @ApiModelProperty(value = "评估字段标签")
    private String fieldLabel;

    @ApiModelProperty(value = "规则类型")
    private String ruleType;

    @ApiModelProperty(value = "比较操作符")
    private String comparisonOperator;

    @ApiModelProperty(value = "规则值")
    private String ruleValue;

    @ApiModelProperty(value = "打分配置")
    private String scoreConfig;

    @ApiModelProperty(value = "权重")
    private BigDecimal weight;
}
