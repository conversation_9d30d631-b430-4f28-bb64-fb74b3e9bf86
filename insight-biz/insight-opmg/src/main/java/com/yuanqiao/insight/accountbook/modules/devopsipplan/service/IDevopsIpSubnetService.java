package com.yuanqiao.insight.accountbook.modules.devopsipplan.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuanqiao.insight.accountbook.modules.devopsipplan.model.DevopsIpVo;
import com.yuanqiao.insight.service.devopsip.entity.DevopsIpSubnet;

import java.util.List;

/**
 * @Description: 子网管理
 * @Author: jeecg-boot
 * @Date: 2024-02-26
 * @Version: V1.0
 */

public interface IDevopsIpSubnetService extends IService<DevopsIpSubnet> {

    List<DevopsIpVo> queryListByGroupId(String subnetGroupId);

    List<DevopsIpVo> queryListBy();

}
