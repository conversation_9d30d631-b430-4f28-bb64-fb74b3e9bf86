package com.yuanqiao.insight.accountbook.modules.evaluate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateMetricsScoreRecord;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName IEvaluateMetricsScoreService
 * @description: 评估指标打分服务接口
 * @datetime 2025年 07月 28日
 * @version: 1.0
 */
public interface IEvaluateMetricsScoreService extends IService<EvaluateMetricsScoreRecord> {
    
    /**
     * 执行评估打分
     * @param projectId 项目ID
     * @param metricsId 指标ID
     * @param fieldValues 字段值Map
     * @param evaluator 评估人
     * @return 打分结果
     */
    Map<String, Object> executeEvaluation(String projectId, String metricsId, 
                                         Map<String, Object> fieldValues, String evaluator);
    
    /**
     * 获取项目指标的打分记录
     * @param projectId 项目ID
     * @param metricsId 指标ID
     * @return 打分记录列表
     */
    List<EvaluateMetricsScoreRecord> getScoreRecords(String projectId, String metricsId);
    
    /**
     * 获取项目指标的总分
     * @param projectId 项目ID
     * @param metricsId 指标ID
     * @return 总分信息
     */
    Map<String, Object> getTotalScore(String projectId, String metricsId);
    
    /**
     * 重新计算打分
     * @param projectId 项目ID
     * @param metricsId 指标ID
     * @param evaluator 评估人
     * @return 重新计算结果
     */
    Map<String, Object> recalculateScore(String projectId, String metricsId, String evaluator);
}
