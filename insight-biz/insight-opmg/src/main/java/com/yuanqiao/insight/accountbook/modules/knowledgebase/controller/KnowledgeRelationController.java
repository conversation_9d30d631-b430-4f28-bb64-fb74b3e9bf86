package com.yuanqiao.insight.accountbook.modules.knowledgebase.controller;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.accountbook.modules.knowledgebase.entity.ProcessInstanceRelation;
import com.yuanqiao.insight.accountbook.modules.knowledgebase.utils.ESClient;
import com.yuanqiao.insight.accountbook.modules.knowledgebase.utils.KnowledgeRelationUtil;
import com.yuanqiao.insight.acore.system.mapper.ConfigureDictMapper;
import com.yuanqiao.insight.acore.system.vo.ConfigureDictModel;
import com.yuanqiao.insight.modules.flowable.mapper.HistoricProcessInstanceMapper;
import com.yuanqiao.insight.modules.flowable.service.ProcessInstanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.flowable.engine.HistoryService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.engine.impl.HistoricProcessInstanceQueryProperty;
import org.flowable.engine.impl.persistence.entity.HistoricProcessInstanceEntityImpl;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.system.entity.SysUsers;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Api(tags = "知识库关联")
@RestController
@RequestMapping("/kbase/relation")
public class KnowledgeRelationController {

    @Autowired
    private ESClient esClient;

    @Autowired
    protected HistoryService historyService;

    @Autowired
    ConfigureDictMapper configureDictMapper;

    @Autowired
    private ProcessInstanceService processInstanceService;

    public static final String KNOWLEDGE_PROCESS = "itilProcessKey";

    @Autowired
    HistoricProcessInstanceMapper historicProcessInstanceMapper;

    @Autowired
    ISysUserService iSysUserService;

    @Autowired
    KnowledgeRelationUtil knowledgeRelationUtil;

    final String PRIORITY = "priorityLevel";

    @AutoLog(value = "查看关联工单")
    @ApiOperation(value = "查看关联工单", notes = "查看关联工单")
    @GetMapping
    public Result<?> listRelation(@RequestParam String knowledgeId,
                                  @RequestParam String processDefinitionKey,
                                  @RequestParam(required = false) String title,
                                  @RequestParam(required = false) String searchClassification,//中文
                                  @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                  @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                  HttpServletRequest req) throws IOException {
        pageNo = pageNo < 1 ? 1 : pageNo;
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        int offSize = (pageNo - 1) * pageSize;
        String classification = "";//工单类型，暂时弃用
        String storageKey = knowledgeRelationUtil.getStorageKeyFromConfigureDict(processDefinitionKey);
        List<String> mustnotList = new ArrayList<>(esClient.listRelation(knowledgeId,storageKey).keySet());
        List<HistoricProcessInstanceEntityImpl> list1 = historicProcessInstanceMapper.list(loginUser.getUsername(), title, processDefinitionKey, classification, searchClassification, mustnotList, offSize, pageSize);
        //这个count是单独查询的
        int count = historicProcessInstanceMapper.getCount(loginUser.getUsername(), title, processDefinitionKey, classification, searchClassification, mustnotList);

        List<ProcessInstanceRelation> result = wrapper(list1,classification);
        Page<ProcessInstanceRelation> page = new Page<>(pageNo, pageSize, count);
        page.setRecords(result);

        return Result.OK(page);
    }

    //显示知识关联的流程
    @GetMapping(value = "/list")
    public Result<?> listRelationByKnowledgeId(@RequestParam String knowledgeId,
                                               @RequestParam String processDefinitionKey) throws IOException {
        HistoricProcessInstanceQuery query = historyService.createHistoricProcessInstanceQuery();
        String storageKey = knowledgeRelationUtil.getStorageKeyFromConfigureDict(processDefinitionKey);
        HashMap<String, String> map = esClient.listRelation(knowledgeId, storageKey);
        if (map.isEmpty()) {
            return Result.OK(new ArrayList<>());
        }
        query.processInstanceIds(map.keySet());
        query.orderBy(HistoricProcessInstanceQueryProperty.START_TIME).desc();
        List<HistoricProcessInstance> list1 = query.list();

        List<ProcessInstanceRelation> relations = wrapper(list1,"classification");//todo 工单类型，暂时弃用
        relations.forEach(processInstanceRelation -> {
            processInstanceRelation.setRelationId(map.get(processInstanceRelation.getId()));
        });
        return Result.OK(relations);
    }

    List<ProcessInstanceRelation> wrapper(List<? extends HistoricProcessInstance> list, String classification){
        HashMap<String,String> userNameMap = new HashMap<>();
        list.forEach(instance -> {
            if (!userNameMap.containsKey(instance.getStartUserId())){
                SysUsers user = iSysUserService.getUserByName(instance.getStartUserId());
                if (user != null){
                    String realname = user.getRealname();
                    userNameMap.put(instance.getStartUserId(),realname);
                }
            }
        });

        List<ProcessInstanceRelation> relations = new ArrayList<>();
        for (HistoricProcessInstance instance : list) {
            ProcessInstanceRelation processInstanceRelation = new ProcessInstanceRelation();
            Map<String, Object> variables = processInstanceService.getVariables(instance.getId());
            processInstanceRelation.setId(instance.getId());//编号
            processInstanceRelation.setTitle(instance.getName());//名称
            processInstanceRelation.setStartUser(userNameMap.get(instance.getStartUserId()));//发起人
            processInstanceRelation.setStartTime(instance.getStartTime());//开始时间
            processInstanceRelation.setPriority((String) variables.get(PRIORITY));
            processInstanceRelation.setClassification((String) variables.get(classification));//事件、问题、工单都对应着各自的classification字段
            relations.add(processInstanceRelation);
        }
        return relations;
    }

    /**
     * 添加
     */
    //是否还要判断关联的knowledgeId、enventId是否已经存在？？？
    @AutoLog(value = "添加关联工单")
    @ApiOperation(value = "添加关联工单", notes = "添加关联工单")
    @PostMapping
    public Result<?> addRelation(@RequestParam String knowledgeId, @RequestParam String processInstanceIds, @RequestParam String processDefinitionKey) throws IOException {
        //鉴权，管理员或者有该主题权限的人才能编辑
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String storageKey = knowledgeRelationUtil.getStorageKeyFromConfigureDict(processDefinitionKey);
        if (StringUtils.isBlank(storageKey)){
            return Result.error("");
        }
        for (String processInstanceId : processInstanceIds.split(",")){
            //后端存的一定是value里的不会变的字段
            esClient.addRelation(knowledgeId,loginUser.getId(),processInstanceId,storageKey);
        }
        return Result.OK("添加成功！");
    }


    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "删除关联工单")
    @ApiOperation(value = "删除关联工单", notes = "删除关联工单")
    @DeleteMapping
    public Result<?> deleteRelation(@RequestParam(name = "id", required = true) String id) throws IOException {
        //鉴权，管理员或者有该主题权限的人才能编辑
        esClient.deleteRelation(id);
        return Result.OK("删除成功!","删除成功!");
    }


    @AutoLog(value = "批量删除关联工单")
    @ApiOperation(value = "删除关联工单", notes = "删除关联工单")
    @DeleteMapping(value = "/batch")
    public Result<?> deleteRelationBatch(@RequestParam(name = "id", required = true) String ids) throws IOException {
        //鉴权，管理员或者有该主题权限的人才能编辑
        for (String id : ids.split(",")){
            esClient.deleteRelation(id);
        }
        return Result.OK("删除成功!","删除成功!");
    }

    @GetMapping(value = "/getDict")
    public Result<?> getDict(){
        List<ConfigureDictModel> configureDictModels = configureDictMapper.queryDictItemsByCode(KNOWLEDGE_PROCESS);
        return Result.OK(configureDictModels);
    }


    @GetMapping(value = "/check")
    public Result<?> check(@RequestParam(name = "processInstanceId", required = true) String processInstanceId){
        HistoricProcessInstanceQuery query = historyService.createHistoricProcessInstanceQuery();
        query.processInstanceId(processInstanceId);
        HistoricProcessInstance instance = query.list().get(0);
        JSONObject json = new JSONObject();
        json.put("processDefinitionName",instance.getProcessDefinitionName());
        json.put("processDefinitionKey",instance.getProcessDefinitionKey());
        json.put("processDefinitionId",instance.getProcessDefinitionId());
        return Result.OK(json);
    }
}
