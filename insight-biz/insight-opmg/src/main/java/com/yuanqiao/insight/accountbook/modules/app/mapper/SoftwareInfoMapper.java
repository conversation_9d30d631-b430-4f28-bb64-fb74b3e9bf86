package com.yuanqiao.insight.accountbook.modules.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanqiao.insight.accountbook.modules.app.entity.SoftwareInfo;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 软件管理表
 * @Author: jeecg-boot
 * @Date:   2021-03-11
 * @Version: V1.0
 */
public interface SoftwareInfoMapper extends BaseMapper<SoftwareInfo> {

    void updateSoftwareDownloadNum(@Param("softwareId") String softwareId, @Param("downloadNum") Integer downloadNum);

}
