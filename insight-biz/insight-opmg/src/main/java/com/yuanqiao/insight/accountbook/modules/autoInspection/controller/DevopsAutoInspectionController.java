package com.yuanqiao.insight.accountbook.modules.autoInspection.controller;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cronutils.model.CronType;
import com.cronutils.model.definition.CronDefinitionBuilder;
import com.cronutils.parser.CronParser;
import com.google.common.collect.Lists;
import com.yuanqiao.insight.accountbook.modules.autoInspection.entity.DevopsAutoInspection;
import com.yuanqiao.insight.accountbook.modules.autoInspection.entity.DevopsAutoInspectionDevice;
import com.yuanqiao.insight.accountbook.modules.autoInspection.entity.ResourceInfo;
import com.yuanqiao.insight.accountbook.modules.autoInspection.service.IDevopsAutoInspectionDeviceService;
import com.yuanqiao.insight.accountbook.modules.autoInspection.service.IDevopsAutoInspectionService;
import com.yuanqiao.insight.accountbook.modules.devopsbackuppro.entity.DevopsBackupPro;
import com.yuanqiao.insight.cmdb.modules.assets.service.IAssetsService;
import com.yuanqiao.insight.cmdb.modules.category.entity.CmdbAssetsCategory;
import com.yuanqiao.insight.cmdb.modules.category.mapper.CmdbAssetsCategoryMapper;
import com.yuanqiao.insight.modules.notice.entity.SysNoticeTemplate;
import com.yuanqiao.insight.modules.notice.service.ISysNoticeTemplateService;
import com.yuanqiao.insight.monitoring.modules.topo.entity.TopoInfo;
import com.yuanqiao.insight.monitoring.modules.topo.service.ITopoInfoService;
import com.yuanqiao.insight.service.device.entity.DeviceInfo;
import com.yuanqiao.insight.service.device.service.IDeviceInfoService;
import com.yuanqiao.insight.service.product.entity.Product;
import com.yuanqiao.insight.service.product.service.IProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoDict;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.util.ExcelUtilJeecg;
import org.jeecg.common.system.util.ZipEncryUtilsJeecg;
import org.jeecg.common.system.vo.DictModel;
import com.yuanqiao.insight.acore.system.service.ISysDictService;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.umpPwdManage.entity.UmpPwdManage;
import org.jeecg.modules.umpPwdManage.service.IUmpPwdManageService;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 智能巡检任务表
 * @Author: jeecg-boot
 * @Date: 2021-03-12
 * @Version: V1.0
 */
@Api(tags = "智能巡检任务表")
@RestController
@RequestMapping("/autoInspection/devopsAutoInspection")
@Slf4j
public class DevopsAutoInspectionController extends JeecgController<DevopsAutoInspection, IDevopsAutoInspectionService> {
    @Autowired
    private IDevopsAutoInspectionService devopsAutoInspectionService;
    @Autowired
    private ISysDictService sysDictService;
    @Autowired
    private IDeviceInfoService deviceInfoServicel;
    @Autowired
    private IDevopsAutoInspectionDeviceService devopsAutoInspectionDeviceService;
    @Autowired
    private ITopoInfoService iTopoInfoService;
    @Autowired
    private IUmpPwdManageService umpPwdManageService;
    @Autowired
    private CmdbAssetsCategoryMapper cmdbAssetsCategoryMapper;
    @Autowired
    private IProductService productService;
    @Autowired
    private ISysNoticeTemplateService sysNoticeTemplateService;
    @Autowired
    private ISysUserService userService;

    @Autowired
    private IAssetsService assetsService;
    /**
     * 分页列表查询
     *
     * @param devopsAutoInspection
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "智能巡检任务-分页列表查询")
    @ApiOperation(value = "智能巡检任务表-分页列表查询", notes = "智能巡检任务表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(DevopsAutoInspection devopsAutoInspection,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {

        Result<IPage<DevopsAutoInspection>> result = new Result<>();
        String taskName = devopsAutoInspection.getTaskName();
        devopsAutoInspection.setTaskName(null);
        QueryWrapper<DevopsAutoInspection> queryWrapper = QueryGenerator.initQueryWrapper(devopsAutoInspection, req.getParameterMap());
        if (StringUtils.isNotBlank(taskName)) {
            queryWrapper.like("task_name", taskName);
        }
        Page<DevopsAutoInspection> page = new Page<DevopsAutoInspection>(pageNo, pageSize);
        IPage<DevopsAutoInspection> pageList = devopsAutoInspectionService.page(page, queryWrapper);

        if (CollUtil.isNotEmpty(pageList.getRecords())) {
            //巡检类型
            List<DictModel> inspectionType = sysDictService.queryDictItemsByCode("inspection_type");
            //通知模板
            List<SysNoticeTemplate> noticeTemplate = sysNoticeTemplateService.list(new QueryWrapper<SysNoticeTemplate>());

            Map<String, String> inspectionMap = inspectionType.stream().collect(Collectors.toMap(DictModel::getValue, DictModel::getText));
            Map<String, String> noticeTemplateMap = noticeTemplate.stream().collect(Collectors.toMap(SysNoticeTemplate::getId, SysNoticeTemplate::getTemplateName));
            Map<String, String> userMap = userService.selectRealnameByUsername();

            pageList.getRecords().forEach(item -> {
                List<DevopsAutoInspectionDevice> devopsAutoInspectionDevices = devopsAutoInspectionDeviceService.getDeviceByTaskId(item.getId());
                if (null != devopsAutoInspectionDevices && 0 < devopsAutoInspectionDevices.size()) {
                    item.setIds(devopsAutoInspectionDevices.stream().map(DevopsAutoInspectionDevice::getDeviceId).collect(Collectors.joining(",")));
                }
                item.setInspectionDictText(inspectionMap.get(item.getInspectionType()));
                item.setPushAddressText(noticeTemplateMap.get(item.getPushAddress()));
                item.setCreateByName(userMap.get(item.getCreateBy()));
                if (item.getExecuteCounts() == null) {
                    item.setExecuteCounts(0);
                }
            });
        }
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }


    /**
     * 添加
     *
     * @param devopsAutoInspection
     * @return
     */
    @AutoLog(value = "智能巡检任务-添加")
    @ApiOperation(value = "智能巡检任务表-添加", notes = "智能巡检任务表-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody DevopsAutoInspection devopsAutoInspection) {
        QueryWrapper<DevopsAutoInspection> queryWrapper = new QueryWrapper<>();
        String currentTaskName = devopsAutoInspection.getTaskName();
        queryWrapper.eq("task_name", currentTaskName);
        // 找出与当前巡检任务相同name的数据
        List<DevopsAutoInspection> list = devopsAutoInspectionService.list(queryWrapper);
        if (CollUtil.isNotEmpty(list)) {
            return Result.error("任务名称重复！");
        }
        devopsAutoInspectionService.saveAutoInspectionAndDevice(devopsAutoInspection);
        return Result.OK();
    }


    /**
     * 编辑
     *
     * @param devopsAutoInspection
     * @return
     */
    @AutoLog(value = "智能巡检任务-编辑")
    @ApiOperation(value = "智能巡检任务表-编辑", notes = "智能巡检任务表-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody DevopsAutoInspection devopsAutoInspection) {
        List<DevopsAutoInspection> list = devopsAutoInspectionService.list(new QueryWrapper<DevopsAutoInspection>().ne("id", devopsAutoInspection.getId()).eq("task_name", devopsAutoInspection.getTaskName()));
        if (CollUtil.isNotEmpty(list)) {
            return Result.error("任务名称重复！");
        }

        devopsAutoInspectionService.updateAutoInspectionAndDevice(devopsAutoInspection);
        return Result.OK("编辑成功!");
    }

    @GetMapping(value = "/execute")
    @ApiOperation(value = "智能巡检任务表-立即执行任务")
    public Result<Object> execute(@RequestParam(name = "id") String id) throws Exception {
        devopsAutoInspectionService.execute(id);
        return Result.ok("执行任务成功");
    }

    /**
     * 暂停定时任务
     *
     * @param id
     * @return
     */
    //@RequiresRoles("admin")
    @GetMapping(value = "/pause")
    @ApiOperation(value = "智能巡检任务-暂停任务")
    public Result<Object> pauseJob(@RequestParam(name = "id") String id) {
        final DevopsAutoInspection devopsAutoInspection = devopsAutoInspectionService.getById(id);
        devopsAutoInspectionService.pause(devopsAutoInspection);
        return Result.ok("暂停巡检任务成功");
    }

    /**
     * 启动定时任务
     *
     * @param id
     * @return
     */
    //@RequiresRoles("admin")
    @GetMapping(value = "/resume")
    @ApiOperation(value = "智能巡检任务表-恢复任务")
    public Result<Object> resumeJob(@RequestParam(name = "id") String id) {
        final DevopsAutoInspection devopsAutoInspection = devopsAutoInspectionService.getById(id);
        devopsAutoInspectionService.resumeJob(devopsAutoInspection);
        //scheduler.resumeJob(JobKey.jobKey(job.getJobClassName().trim()));
        return Result.ok("恢复巡检任务成功");
    }

    /**
     * Cron校验
     *
     * @param cronExpression
     * @return
     */
    @AutoLog(value = "智能巡检任务-Cron校验")
    @ApiOperation(value = "智能巡检任务表-Cron校验", notes = "智能巡检任务表-Cron校验")
    @GetMapping(value = "/cronCheckSix")
    public Result<?> cronCheckSix(String cronExpression) {
        try {
            // 初始化CronTrigger同时完成Cron校验，校验不通过则抛出异常，终止后续执行
            // CronTrigger -》 CronSequenceGenerator -》 parse()
            new CronTrigger(cronExpression);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
        return Result.OK();
    }


    /**
     * Cron校验
     *
     * @param cronExpression
     * @return
     */
    @AutoLog(value = "智能巡检任务-Cron校验")
    @ApiOperation(value = "智能巡检任务表-Cron校验", notes = "智能巡检任务表-Cron校验")
    @GetMapping(value = "/cronCheck")
    public Result<?> cronCheck(String cronExpression) {
        try {
            CronParser parser = new CronParser(CronDefinitionBuilder.instanceDefinitionFor(CronType.QUARTZ));
            parser.parse(cronExpression);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
        return Result.OK();
    }


    /**
     * 巡检任务绑定设备/topinfo
     *
     * @param map
     * @return
     */
    @PostMapping(value = "/bindDevice")
    @AutoLog(value = "智能巡检任务表-绑定设备")
    @ApiOperation(value = "智能巡检任务表-绑定设备", notes = "智能巡检任务表-绑定设备")
    public Result<?> bindDevice(@RequestBody Map<Object, Object> map) {
        String id = (String) map.get("id");
        String type = (String) map.get("type");
        String deviceIds = (String) map.get("deviceId");
        String productId = (String) map.get("productId");
        String topInfoName = (String) map.get("topInfoName");
        Boolean isSelectAll = (Boolean) map.get("isSelectAll");
        String inspectionType = (String) map.get("inspectionType"); // 巡检类型
        if (StringUtils.isEmpty(id)) {
            return Result.error("未选择绑定的设备");
        }

        // case1: 默认按照传过来的deviceIds绑定
        List<String> deviceIdList = Arrays.asList(deviceIds.split(","));
        //TODO::START 以下代码无意义,前端直接传ids就行
        // case2: 查全部(注意逻辑限制，例如查询未删除的，查询拓扑类型是应用拓扑的等等)
        if (inspectionType.equals("1") && isSelectAll && StringUtils.isEmpty(deviceIds)) {
            List<DeviceInfo> deviceInfoList = this.getDeviceInfoList(type, productId);
            deviceIdList = deviceInfoList.stream().map(u -> u.getId()).collect(Collectors.toList());
        } else if (inspectionType.equals("2") && isSelectAll && StringUtils.isEmpty(deviceIds)) {
            List<TopoInfo> topoInfoList = this.getTopoInfoList(topInfoName);
            deviceIdList = topoInfoList.stream().map(u -> u.getId()).collect(Collectors.toList());
        }
        //TODO::END

        List<DevopsAutoInspectionDevice> inspectionDeviceList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(deviceIdList)) {
            for (String deviceId : deviceIdList) {
                DevopsAutoInspectionDevice inspectionDevice = new DevopsAutoInspectionDevice();
                inspectionDevice.setDeviceId(deviceId);
                inspectionDevice.setTaskId(id);
                inspectionDeviceList.add(inspectionDevice);
            }
            devopsAutoInspectionDeviceService.saveBatch(inspectionDeviceList);
        }
        return Result.OK("绑定成功");
    }


    /**
     * 巡检任务删除绑定的某个设备
     *
     * @param id
     * @param deviceId
     * @return
     */
    @DeleteMapping(value = "/unbindDevice")
    @AutoLog(value = "智能巡检任务表-删除绑定某个设备")
    @ApiOperation(value = "智能巡检任务表-删除绑定某个设备", notes = "智能巡检任务表-删除绑定某个设备")
    public Result<?> unbindDevice(@RequestParam(name = "id", required = true) String id, @RequestParam(name = "deviceId", required = true) String deviceId) {
        List<String> list = new ArrayList<>();
        list.add(deviceId);
        devopsAutoInspectionDeviceService.delBatchDevops(id, list);
        return Result.OK();
    }


    /**
     * 巡检最外层接口
     *
     * @param id
     * @param inspectionType
     * @param req
     * @param pageNo
     * @param pageSize
     * @return
     */
    @GetMapping(value = "/getBindDeviceList")
    @AutoLog(value = "智能巡检任务表-获取巡检任务绑定的设备")
    @ApiOperation(value = "智能巡检任务表-获取巡检任务绑定的设备", notes = "智能巡检任务表-获取巡检任务绑定的设备")
    public Result<?> getBindDeviceList(String id, String inspectionType, HttpServletRequest req,
                                       @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {

        List<DevopsAutoInspectionDevice> inspectionDeviceList = devopsAutoInspectionDeviceService.getDeviceByTaskId(id);
        List<String> inspectionDeviceIdList = inspectionDeviceList.stream().map(u -> u.getDeviceId()).collect(Collectors.toList());
        QueryWrapper<DeviceInfo> queryWrapper4DeviceInfo = new QueryWrapper<>();
        QueryWrapper<TopoInfo> queryWrapper4topInfo = new QueryWrapper<>();
        if (!CollectionUtils.isEmpty(inspectionDeviceIdList)) {
            queryWrapper4DeviceInfo.in("id", inspectionDeviceIdList);
            queryWrapper4topInfo.in("id", inspectionDeviceIdList);
        }
        Result<IPage<ResourceInfo>> result = new Result<>();
        // 查询绑定过巡检任务的设备/应用拓扑 默认是查询全部的
        IPage<ResourceInfo> iPage = devopsAutoInspectionService.getResourceList(true, inspectionType, queryWrapper4DeviceInfo, queryWrapper4topInfo, pageNo, pageSize);

        // inspectionDeviceIdList为空表示当前巡检任务未绑定设备/拓扑，直接将分页数据置空
        if (CollectionUtils.isEmpty(inspectionDeviceIdList)) {
            iPage.setRecords(new ArrayList<>());
            iPage.setTotal(0);
            iPage.setSize(0);
            iPage.setPages(0);
            iPage.setCurrent(1);
        }
        result.setResult(iPage);
        return result;
    }


    /**
     * 巡检最里层弹出层列表数据
     *
     * @param id          巡检任务id
     * @param type        "product" or null
     * @param productId   可能是产品id值 也可能是分类id值   (type==null && productId存在)-->按照分类查询，分类Id的值为productId
     * @param isSelectAll 是否全选
     * @return
     */
    @AutoLog(value = "智能巡检任务表-获取设备信息接口")
    @ApiOperation(value = "智能巡检任务表-获取设备信息接口", notes = "智能巡检任务表-获取设备信息接口")
    @GetMapping(value = "/getResourceList")
    public Result<?> getResourceList(String id, String inspectionType, String name, String type, String productId, Boolean isSelectAll, String exclusionIds, HttpServletRequest req,
                                     @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        //TODO::exclusionIds可能会超长
        Result<IPage<ResourceInfo>> result = new Result<>();
        DeviceInfo deviceInfo = new DeviceInfo();
        type = StringUtils.isEmpty(type) && StringUtils.isNotEmpty(productId) ? "category" : "product";
        String categoryId = type.equals("category") ? productId : "";
        if (type.equals("product")) {
            deviceInfo.setProductId(productId);
        }
        final JSONArray exclusion = JSON.parseArray(exclusionIds);

        if ("1".equals(inspectionType)) { // 巡检类型-资源巡检：1
            QueryWrapper<DeviceInfo> queryWrapper = QueryGenerator.initQueryWrapper(deviceInfo, req.getParameterMap());
            queryWrapper.eq("delflag", 0);
            // 依据产品分类/产品过滤
            if (type.equals("category") && StringUtils.isNotEmpty(categoryId)) {
                // 产品分类及子分类
                List<String> pid = new ArrayList<>();
                pid.add(categoryId);
                List<CmdbAssetsCategory> productCategoryList = findFollowUpNodes(pid);
                List<String> categoty_id_list = productCategoryList.stream().map(u -> u.getId()).collect(Collectors.toList());
                categoty_id_list.add(categoryId);

                // 当前产品分类下的产品数据
                List<Product> current_category_product_list = productService.queryProductByCondition(categoryId);
                List<String> product_id_list = current_category_product_list.stream().map(u -> u.getId()).collect(Collectors.toList());

                // 动态判断渲染查询条件
                if (!CollectionUtils.isEmpty(product_id_list)) {
                    queryWrapper.and(wrapper -> wrapper.in("category_id", categoty_id_list).or().in("product_id", product_id_list));
                } else if (!CollectionUtils.isEmpty(categoty_id_list)) {
                    queryWrapper.in("category_id", categoty_id_list);
                }
            }
            // 过滤巡检任务已选择的设备数据
            if (StringUtils.isNotEmpty(id)) {
                List<DevopsAutoInspectionDevice> inspectionDeviceList = devopsAutoInspectionDeviceService.getDeviceByTaskId(id);
                List<String> inspectionDeviceIdList = inspectionDeviceList.stream().map(u -> u.getDeviceId()).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(inspectionDeviceIdList)) {
                    queryWrapper.notIn("id", inspectionDeviceIdList);
                }
            } else {
                if (CollUtil.isNotEmpty(exclusion)) {
                    queryWrapper.notIn("id", exclusion);
                }
            }

            IPage<ResourceInfo> pageList = devopsAutoInspectionService.getResourceList(isSelectAll, inspectionType, queryWrapper, null, pageNo, pageSize);
            result.setResult(pageList);

            // 巡检类型-应用巡检：2
        } else if ("2".equals(inspectionType)) {
            TopoInfo topoInfo = new TopoInfo();
            QueryWrapper<TopoInfo> queryWrappers = QueryGenerator.initQueryWrapper(topoInfo, req.getParameterMap());
            queryWrappers.eq("topo_type", "1");
            if (StringUtils.isNotEmpty(name)) {
                queryWrappers.like("topo_name", name);
            }
            // 已经选择过的拓扑数据，在列表中不再展示
            List<DevopsAutoInspectionDevice> inspectionDeviceList = devopsAutoInspectionDeviceService.getDeviceByTaskId(id);
            List<String> inspectionDeviceIdList = inspectionDeviceList.stream().map(u -> u.getDeviceId()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(inspectionDeviceIdList)) {
                queryWrappers.notIn("id", inspectionDeviceIdList);
            } else {
                if (CollUtil.isNotEmpty(exclusion)) {
                    queryWrappers.notIn("id", exclusion);
                }
            }
            IPage<ResourceInfo> pageList = devopsAutoInspectionService.getResourceList(null, inspectionType, null, queryWrappers, pageNo, pageSize);
            result.setResult(pageList);
        }
        return result;
    }


    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "智能巡检任务-通过id删除")
    @ApiOperation(value = "智能巡检任务表-通过id删除", notes = "智能巡检任务表-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id") String id) {
        devopsAutoInspectionService.deleteAndStopJob(id);
        return Result.OK("删除成功!");
    }


    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "智能巡检任务-批量删除")
    @ApiOperation(value = "智能巡检任务表-批量删除", notes = "智能巡检任务表-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        List<String> idLists = Arrays.asList(ids.split(","));
        for (String id : idLists) {
            devopsAutoInspectionService.deleteAndStopJob(id);
        }
        return Result.OK("批量删除成功!");
    }


    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "智能巡检任务-通过id查询")
    @ApiOperation(value = "智能巡检任务表-通过id查询", notes = "智能巡检任务表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        DevopsAutoInspection devopsAutoInspection = devopsAutoInspectionService.getById(id);
        //巡检类型
        List<DictModel> inspectionType = sysDictService.queryDictItemsByCode("inspection_type");
        //任务执行类型
        List<DictModel> taskexecuteType = sysDictService.queryDictItemsByCode("taskexecute_type");

        Map<String, String> inspectionMap = inspectionType.stream().collect(Collectors.toMap(DictModel::getValue, DictModel::getText));
        devopsAutoInspection.setInspectionDictText(inspectionMap.get(devopsAutoInspection.getInspectionType()));

        List<DevopsAutoInspectionDevice> devopsAutoInspectionDevices = devopsAutoInspectionDeviceService.getDeviceByTaskId(id);
        if (null != devopsAutoInspectionDevices && 0 < devopsAutoInspectionDevices.size()) {
            devopsAutoInspection.setIds(devopsAutoInspectionDevices.stream().map(DevopsAutoInspectionDevice::getDeviceId).collect(Collectors.joining(",")));
        }

        if (devopsAutoInspection == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(devopsAutoInspection);
    }

    @Autowired
    private ZipEncryUtilsJeecg zipEncryUtils;

    /**
     * 导出excel
     *
     * @param request
     * @param devopsAutoInspection
     */
    @AutoDict
    @AutoLog(value = "智能巡检任务-导出")
    @ApiOperation(value = "智能巡检任务-导出", notes = "智能巡检任务-导出")
    @RequestMapping(value = "/exportXls")
    public void exportXls(HttpServletRequest request, HttpServletResponse response, DevopsAutoInspection devopsAutoInspection) {

        List<DevopsAutoInspection> inspectionList = devopsAutoInspectionService.list(new QueryWrapper<DevopsAutoInspection>());
        inspectionList = inspectionList.stream().peek(i -> {
            if (i.getExecuteCounts() == null) {
                i.setExecuteCounts(0);
            }
        }).collect(Collectors.toList());

        UmpPwdManage zip = umpPwdManageService.getZip();
        String fileName = assetsService.exportXlsRename("任务管理");
        super.exportXlsZip(request, response, devopsAutoInspection, DevopsAutoInspection.class, fileName, zip.getZipPwd(), zip.getIsEncry());
    }


    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, DevopsAutoInspection.class);
    }


    /**
     * 查询某个产品下的所有的设备， 或者查询某个分类下的所有的产品的所有设备
     *
     * @param type      产品所属分类  可能是null   type==null && productId != null  ---> type = "category"
     * @param productId 产品id
     * @return
     */
    private List<DeviceInfo> getDeviceInfoList(String type, String productId) {

        QueryWrapper<DeviceInfo> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotEmpty(productId)) {
            type = StringUtils.isEmpty(type) ? "category" : "product";
            String categoryId = type.equals("category") ? productId : "";

            DeviceInfo deviceInfo = new DeviceInfo();
            if (type.equals("product")) {
                deviceInfo.setProductId(productId); // 用来初始化queryWrapper查询条件用
            }
            queryWrapper = QueryGenerator.initQueryWrapper(deviceInfo, null);

            if (type.equals("category")) {
                // 产品分类及子分类
                List<String> pid_list = new ArrayList<>();
                pid_list.add(categoryId);
                List<CmdbAssetsCategory> productCategoryList = findFollowUpNodes(pid_list);
                List<String> categoty_id_list = productCategoryList.stream().map(u -> u.getId()).collect(Collectors.toList());
                categoty_id_list.add(categoryId);

                // 当前产品分类下的产品数据
                List<Product> current_category_product_list = productService.queryProductByCondition(categoryId);
                List<String> product_id_list = current_category_product_list.stream().map(u -> u.getId()).collect(Collectors.toList());

                // 动态判断渲染查询条件
                if (!CollectionUtils.isEmpty(product_id_list)) {
                    queryWrapper.and(wrapper -> wrapper.in("category_id", categoty_id_list).or().in("product_id", product_id_list));
                } else if (!CollectionUtils.isEmpty(categoty_id_list)) {
                    queryWrapper.in("category_id", categoty_id_list);
                }
            }
        }
        queryWrapper.eq("delflag", 0); // 查询未删除的全部
        // 设备数据
        List<DeviceInfo> deviceInfoList = deviceInfoServicel.list(queryWrapper);
        return deviceInfoList;
    }

    /**
     * 查询所有的应用拓扑数据（带name限制）
     *
     * @param name
     * @return
     */
    private List<TopoInfo> getTopoInfoList(String name) {
        QueryWrapper<TopoInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("topo_type", "1");
        if (StringUtils.isNotEmpty(name)) {
            queryWrapper.like("name", name);
        }
        List<TopoInfo> list = iTopoInfoService.list(queryWrapper);
        return list;
    }

    /**
     * 资产根据父节点找子节点
     *
     * @param ids
     * @return
     */
    private List<CmdbAssetsCategory> findFollowUpNodes(List<String> ids) {
        // 根据父节点查询所有的子节点
        List<CmdbAssetsCategory> list = cmdbAssetsCategoryMapper.selectByParentIds(ids);
        // 如果当前父节点的所有子节点不为空
        if (!CollectionUtils.isEmpty(list)) {
            List<String> sonIds = list.stream().map(u -> u.getId()).collect(Collectors.toList());
            list.addAll(cmdbAssetsCategoryMapper.selectByParentIds(sonIds));
            return list;
        } else {
            return Lists.newArrayList();
        }
    }
}
