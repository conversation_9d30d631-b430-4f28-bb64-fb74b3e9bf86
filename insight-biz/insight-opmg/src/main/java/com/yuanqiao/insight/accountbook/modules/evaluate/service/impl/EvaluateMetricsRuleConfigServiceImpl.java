package com.yuanqiao.insight.accountbook.modules.evaluate.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateMetricsRuleConfig;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateMetricsRuleDetail;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluationRuleInfo;
import com.yuanqiao.insight.accountbook.modules.evaluate.mapper.EvaluateMetricsRuleConfigMapper;
import com.yuanqiao.insight.accountbook.modules.evaluate.service.IEvaluateMetricsRuleConfigService;
import com.yuanqiao.insight.accountbook.modules.evaluate.service.IEvaluateMetricsRuleDetailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.util.UUIDGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName EvaluateMetricsRuleConfigServiceImpl
 * @description: 评估指标规则配置服务实现类
 * @datetime 2025年 07月 28日
 * @version: 1.0
 */
@Service
@Slf4j
public class EvaluateMetricsRuleConfigServiceImpl extends ServiceImpl<EvaluateMetricsRuleConfigMapper, EvaluateMetricsRuleConfig> implements IEvaluateMetricsRuleConfigService {

    @Autowired
    private IEvaluateMetricsRuleDetailService ruleDetailService;

    @Override
    @Transactional
    public boolean saveRuleConfig(String metricsId, String ruleConfig) {
        try {
            // 解析规则配置，统计规则信息
            JSONObject configJson = JSON.parseObject(ruleConfig);
            JSONArray ruleList = configJson.getJSONArray("list");
            
            int ruleCount = 0;
            if (ruleList != null) {
                ruleCount = ruleList.size();
            }
            
            // 检查是否已存在配置
            EvaluateMetricsRuleConfig existConfig = baseMapper.selectByMetricsId(metricsId);
            
            String configId;
            if (existConfig != null) {
                // 更新现有配置
                existConfig.setRuleConfig(ruleConfig);
                existConfig.setRuleCount(ruleCount);
                existConfig.setUpdateTime(new Date());
                this.updateById(existConfig);
                configId = existConfig.getId();
            } else {
                // 创建新配置
                EvaluateMetricsRuleConfig newConfig = new EvaluateMetricsRuleConfig();
                configId = UUIDGenerator.generate();
                newConfig.setId(configId);
                newConfig.setMetricsId(metricsId);
                newConfig.setRuleConfig(ruleConfig);
                newConfig.setRuleCount(ruleCount);
                newConfig.setCreateTime(new Date());
                this.save(newConfig);
            }
            
            // 保存规则详情
            return saveRuleDetails(metricsId, ruleConfig);
            
        } catch (Exception e) {
            log.error("保存指标评估规则配置失败", e);
            return false;
        }
    }

    @Override
    public EvaluateMetricsRuleConfig getByMetricsId(String metricsId) {
        return baseMapper.selectByMetricsId(metricsId);
    }

    @Override
    public List<EvaluationRuleInfo> getEvaluationRules(String metricsId) {
        List<EvaluationRuleInfo> evaluationRules = new ArrayList<>();
        
        try {
            EvaluateMetricsRuleConfig config = baseMapper.selectByMetricsId(metricsId);
            if (config == null || StringUtils.isBlank(config.getRuleConfig())) {
                return evaluationRules;
            }
            
            JSONObject configJson = JSON.parseObject(config.getRuleConfig());
            JSONArray ruleList = configJson.getJSONArray("list");
            
            if (ruleList != null) {
                for (int i = 0; i < ruleList.size(); i++) {
                    JSONObject rule = ruleList.getJSONObject(i);
                    JSONArray batchList = rule.getJSONArray("list");
                    
                    if (batchList != null) {
                        EvaluationRuleInfo ruleInfo = new EvaluationRuleInfo();
                        
                        for (int j = 0; j < batchList.size(); j++) {
                            JSONObject item = batchList.getJSONObject(j);
                            String model = item.getString("model");
                            String value = item.getString("value");
                            
                            switch (model) {
                                case "field_key":
                                    ruleInfo.setFieldKey(value);
                                    break;
                                case "rule_type":
                                    ruleInfo.setRuleType(value);
                                    break;
                                case "comparison_operator":
                                    ruleInfo.setComparisonOperator(value);
                                    break;
                                case "rule_value":
                                    ruleInfo.setRuleValue(value);
                                    break;
                                case "score_config":
                                    ruleInfo.setScoreConfig(value);
                                    break;
                            }
                        }
                        
                        evaluationRules.add(ruleInfo);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取评估规则列表失败", e);
        }
        
        return evaluationRules;
    }

    @Override
    @Transactional
    public boolean saveRuleDetails(String metricsId, String ruleConfig) {
        try {
            // 先删除现有的规则详情
            ruleDetailService.removeByMetricsId(metricsId);
            
            // 解析并保存新的规则详情
            JSONObject configJson = JSON.parseObject(ruleConfig);
            JSONArray ruleList = configJson.getJSONArray("list");
            
            if (ruleList != null) {
                List<EvaluateMetricsRuleDetail> detailList = new ArrayList<>();
                
                for (int i = 0; i < ruleList.size(); i++) {
                    JSONObject rule = ruleList.getJSONObject(i);
                    JSONArray batchList = rule.getJSONArray("list");
                    
                    if (batchList != null) {
                        EvaluateMetricsRuleDetail detail = new EvaluateMetricsRuleDetail();
                        detail.setId(UUIDGenerator.generate());
                        detail.setMetricsId(metricsId);
                        detail.setSortOrder(i);
                        detail.setStatus(1);
                        detail.setWeight(BigDecimal.ONE);
                        detail.setScoreType("fixed");
                        detail.setCreateTime(new Date());
                        
                        for (int j = 0; j < batchList.size(); j++) {
                            JSONObject item = batchList.getJSONObject(j);
                            String model = item.getString("model");
                            String value = item.getString("value");
                            
                            switch (model) {
                                case "field_key":
                                    detail.setFieldKey(value);
                                    break;
                                case "rule_type":
                                    detail.setRuleType(value);
                                    break;
                                case "comparison_operator":
                                    detail.setComparisonOperator(value);
                                    break;
                                case "rule_value":
                                    detail.setRuleValue(value);
                                    break;
                                case "score_config":
                                    detail.setScoreConfig(value);
                                    break;
                            }
                        }
                        
                        detailList.add(detail);
                    }
                }
                
                return ruleDetailService.saveBatch(detailList);
            }
            
            return true;
        } catch (Exception e) {
            log.error("保存规则详情失败", e);
            return false;
        }
    }

    @Override
    public boolean updateRuleConfig(String metricsId, String ruleConfig) {
        return saveRuleConfig(metricsId, ruleConfig);
    }

    @Override
    @Transactional
    public boolean removeByMetricsId(String metricsId) {
        // 删除规则详情
        ruleDetailService.removeByMetricsId(metricsId);
        
        // 删除规则配置
        int result = baseMapper.deleteByMetricsId(metricsId);
        return result > 0;
    }
}
