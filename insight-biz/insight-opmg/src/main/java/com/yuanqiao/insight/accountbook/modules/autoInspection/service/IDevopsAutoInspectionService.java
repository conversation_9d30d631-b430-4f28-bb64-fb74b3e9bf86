package com.yuanqiao.insight.accountbook.modules.autoInspection.service;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yuanqiao.insight.accountbook.modules.autoInspection.entity.DevopsAutoInspection;
import com.yuanqiao.insight.accountbook.modules.autoInspection.entity.ResourceInfo;
import com.yuanqiao.insight.monitoring.modules.topo.entity.TopoInfo;
import com.yuanqiao.insight.service.device.entity.DeviceInfo;

import java.util.Map;

/**
 * @Description: 智能巡检任务表
 * @Author: jeecg-boot
 * @Date:   2021-03-12
 * @Version: V1.0
 */
public interface IDevopsAutoInspectionService extends IService<DevopsAutoInspection> {

    /**
     * 获取任务状态
     * @return
     */
    Map<String,String> getTaskStatus();

    /**
     * 获取巡检类型
     * @return
     */
    Map<String, String> getInspectionType();

    /**
     * 根据条件获取绑定的数据
     * @param isSelectAll
     * @param inspectionType
     * @param queryWrapper
     * @param queryWrappers
     * @param pageNo
     * @param pageSize
     * @return
     */
    IPage<ResourceInfo> getResourceList(Boolean isSelectAll, String inspectionType, QueryWrapper<DeviceInfo> queryWrapper , QueryWrapper<TopoInfo> queryWrappers, Integer pageNo, Integer pageSize);

    /**
     * 保存智能巡检任务和任务与设备关联信息
     * @param devopsAutoInspection
     */
    void saveAutoInspectionAndDevice(DevopsAutoInspection devopsAutoInspection);

    /**
     * 修改智能巡检任务和设备关联信息
     * @param devopsAutoInspection
     */
    void updateAutoInspectionAndDevice(DevopsAutoInspection devopsAutoInspection);

    void execute(String id) throws Exception;

    void pause(DevopsAutoInspection devopsAutoInspection);

    void resumeJob(DevopsAutoInspection devopsAutoInspection);

    void deleteAndStopJob(String id);
}
