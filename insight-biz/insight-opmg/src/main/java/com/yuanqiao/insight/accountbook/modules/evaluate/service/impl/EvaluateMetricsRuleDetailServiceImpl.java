package com.yuanqiao.insight.accountbook.modules.evaluate.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateMetricsRuleDetail;
import com.yuanqiao.insight.accountbook.modules.evaluate.service.IEvaluateMetricsRuleDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName EvaluateMetricsRuleDetailServiceImpl
 * @description: 评估指标规则详情服务实现类
 * @datetime 2025年 07月 28日
 * @version: 1.0
 */
@Service
@Slf4j
public class EvaluateMetricsRuleDetailServiceImpl extends ServiceImpl<EvaluateMetricsRuleDetailMapper, EvaluateMetricsRuleDetail> implements IEvaluateMetricsRuleDetailService {

    @Override
    public List<EvaluateMetricsRuleDetail> listByMetricsId(String metricsId) {
        return baseMapper.selectByMetricsId(metricsId);
    }

    @Override
    public boolean removeByMetricsId(String metricsId) {
        int result = baseMapper.deleteByMetricsId(metricsId);
        return result > 0;
    }

    @Override
    public boolean saveBatch(List<EvaluateMetricsRuleDetail> ruleDetails) {
        return super.saveBatch(ruleDetails);
    }
}
