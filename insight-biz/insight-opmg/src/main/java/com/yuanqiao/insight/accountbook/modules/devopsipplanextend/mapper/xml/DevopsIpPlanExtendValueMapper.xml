<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.accountbook.modules.devopsipplanextend.mapper.DevopsIpPlanExtendValueMapper">
    <delete id="delByPlanId">
        delete
        from devops_ip_plan_extend_value
        where plan_id = #{planId}
    </delete>
    <delete id="delByPlanIdList">
        delete from devops_ip_plan_extend_value
        where
        plan_id in
        <foreach collection="list" item="planIdList" open="(" close=")" separator=",">
            #{planIdList}
        </foreach>
    </delete>
</mapper>