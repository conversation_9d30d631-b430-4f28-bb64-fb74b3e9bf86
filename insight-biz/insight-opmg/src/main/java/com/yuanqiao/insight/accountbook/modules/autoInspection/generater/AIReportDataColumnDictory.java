package com.yuanqiao.insight.accountbook.modules.autoInspection.generater;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @title: AIReportDataColumnDictory   分类巡检信息列表表头Map
 * @projectName jeecg-boot-parent
 * @description: TODO
 * @date 2021/5/17-19:29
 */
public class AIReportDataColumnDictory {

    /**
     * 数据表各类型资源展示定义字典集合对象
     */
    private Map<String, Map<String, String>> dataColumnMap = new ConcurrentHashMap<String, Map<String, String>>();
    /**
     * 公共的map
     */
    private Map<String, String> dasicsMap = new LinkedHashMap<String, String>();

    /**
     * 通过类型获取分类巡检表头MAP
     *
     * @param typeName 类型
     * @return
     */
    public Map<String, String> getDataColumnMapByType(String typeName) {
        return dataColumnMap.get(typeName);
    }

    /**
     * 添加分类巡检表头
     *
     * @param typeName 类型
     * @param typeMap  表头map
     */
    public void addDataColumnMapByType(String typeName, Map<String, String> typeMap) {
        dataColumnMap.put(typeName, typeMap);
    }

    private void setDataColumnMap(Map<String, Map<String, String>> dataColumnMap) {
        this.dataColumnMap = dataColumnMap;
    }


    public Map<String, Map<String, String>> getDataColumnMap() {
        return dataColumnMap;
    }


    public Map<String, String> getDasicsMap() {
        Map<String, String> dasicsMap = new LinkedHashMap<String, String>();

        dasicsMap.put("basicInfo.displayName", "巡检设备名称");
        dasicsMap.put("basicInfo.statusInfo", "状态信息");
        dasicsMap.put("basicInfo.alarmInfo", "告警信息");

        return dasicsMap;
    }

    public void setDasicsMap(Map<String, String> dasicsMap) {

        this.dasicsMap = dasicsMap;
    }
}
