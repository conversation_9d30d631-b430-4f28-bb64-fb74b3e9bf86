package com.yuanqiao.insight.accountbook.modules.devopsipplan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanqiao.insight.accountbook.modules.devopsipplan.entity.DevopsIpPlan;
import com.yuanqiao.insight.accountbook.modules.devopsipplan.model.DevopsIpVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Description: IP规划
 * @Author: jeecg-boot
 * @Date: 2024-02-26
 * @Version: V1.0
 */
@Mapper
public interface DevopsIpPlanMapper extends BaseMapper<DevopsIpPlan> {
    List<DevopsIpVo> queryListBySegmentId(String segmentId);

    List<DevopsIpPlan> queryListByIpAndEnabled(String ip);

    List<DevopsIpPlan> queryListByIpAndScan(String ip);

    List<DevopsIpPlan> queryListByIpAndNoEnabled(String ip,String segmentId);

    List<DevopsIpPlan> queryListByMacAndEnabled(String mac);

    List<DevopsIpPlan> queryListByIpAndMacAndEnabled(String ip, String mac);

    DevopsIpPlan queryByIp(String ip);

}
