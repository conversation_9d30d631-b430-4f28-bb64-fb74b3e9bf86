package com.yuanqiao.insight.accountbook.modules.devopsipaudit.controller;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.accountbook.modules.devopsipaudit.entity.DevopsIpAuditResult;
import com.yuanqiao.insight.accountbook.modules.devopsipaudit.entity.DevopsIpAuditResultDetail;
import com.yuanqiao.insight.accountbook.modules.devopsipaudit.service.IDevopsIpAuditResultDetailService;
import com.yuanqiao.insight.accountbook.modules.devopsipaudit.service.IDevopsIpAuditResultService;
import com.yuanqiao.insight.common.util.dbType.DataSourceTypeUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.system.entity.SysUsers;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 审计结果表
 * @Author: jeecg-boot
 * @Date: 2024-03-01
 * @Version: V1.0
 */
@Api(tags = "审计结果表")
@RestController
@RequestMapping("/devops/ip/auditResult")
@Slf4j
public class DevopsIpAuditResultController extends JeecgController<DevopsIpAuditResult, IDevopsIpAuditResultService> {
    @Autowired
    private IDevopsIpAuditResultService devopsIpAuditResultService;
    @Autowired
    private IDevopsIpAuditResultDetailService devopsIpAuditResultDetailService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private DataSourceTypeUtils dataSourceTypeUtils;

    /**
     * 分页列表查询
     *
     * @param devopsIpAuditResult
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "审计结果表-分页列表查询")
    @ApiOperation(value = "审计结果表-分页列表查询", notes = "审计结果表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<DevopsIpAuditResult>> queryPageList(DevopsIpAuditResult devopsIpAuditResult,
                                                            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                            HttpServletRequest req) {
        QueryWrapper<DevopsIpAuditResult> queryWrapper = new QueryWrapper<>();
        Page<DevopsIpAuditResult> page = new Page<DevopsIpAuditResult>(pageNo, pageSize);
        if (StringUtils.isNotBlank(devopsIpAuditResult.getAuditName())) {
            queryWrapper.like("audit_name", devopsIpAuditResult.getAuditName());
        }
        if (devopsIpAuditResult.getAlarm() != null) {
            queryWrapper.eq("alarm", devopsIpAuditResult.getAlarm());
        }
        if (dataSourceTypeUtils.getDBType().equals("highgo")){
            if (devopsIpAuditResult.getFirstTime() != null && devopsIpAuditResult.getLastTime() != null) {
                DateTime firstDate = DateUtil.parse(devopsIpAuditResult.getFirstTime());
                DateTime lastDate = DateUtil.parse(devopsIpAuditResult.getLastTime());
                queryWrapper.between("start_time", firstDate, lastDate);
                queryWrapper.between("end_time", firstDate, lastDate);
            }
        }else {
            if (devopsIpAuditResult.getFirstTime() != null && devopsIpAuditResult.getLastTime() != null) {
                queryWrapper.between("start_time", devopsIpAuditResult.getFirstTime(), devopsIpAuditResult.getLastTime());
                queryWrapper.between("end_time", devopsIpAuditResult.getFirstTime(), devopsIpAuditResult.getLastTime());
            }
        }
        queryWrapper.orderByDesc("create_time");
        IPage<DevopsIpAuditResult> pageList = devopsIpAuditResultService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param devopsIpAuditResult
     * @return
     */
    @AutoLog(value = "审计结果表-添加")
    @ApiOperation(value = "审计结果表-添加", notes = "审计结果表-添加")
    //@RequiresPermissions("org.jeecg.modules.demo:devops_ip_audit_result:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody DevopsIpAuditResult devopsIpAuditResult) {
        devopsIpAuditResultService.save(devopsIpAuditResult);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param devopsIpAuditResult
     * @return
     */
    @AutoLog(value = "审计结果表-编辑")
    @ApiOperation(value = "审计结果表-编辑", notes = "审计结果表-编辑")
    //@RequiresPermissions("org.jeecg.modules.demo:devops_ip_audit_result:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody DevopsIpAuditResult devopsIpAuditResult) {
        devopsIpAuditResultService.updateById(devopsIpAuditResult);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "审计结果表-通过id删除")
    @ApiOperation(value = "审计结果表-通过id删除", notes = "审计结果表-通过id删除")
    //@RequiresPermissions("org.jeecg.modules.demo:devops_ip_audit_result:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        QueryWrapper<DevopsIpAuditResultDetail> queryWrapper = new QueryWrapper<DevopsIpAuditResultDetail>();
        queryWrapper.eq("audit_result_id", id);
        devopsIpAuditResultDetailService.remove(queryWrapper);
        devopsIpAuditResultService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "审计结果表-批量删除")
    @ApiOperation(value = "审计结果表-批量删除", notes = "审计结果表-批量删除")
    //@RequiresPermissions("org.jeecg.modules.demo:devops_ip_audit_result:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        List<String> list = Arrays.asList(ids.split(","));
        for (String id : list) {
            QueryWrapper<DevopsIpAuditResultDetail> queryWrapper = new QueryWrapper<DevopsIpAuditResultDetail>();
            queryWrapper.eq("audit_result_id", id);
            devopsIpAuditResultDetailService.remove(queryWrapper);
        }
        this.devopsIpAuditResultService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询审计结果详情
     *
     * @return
     */
    @GetMapping(value = "/queryById")
    public Result<?> queryById(DevopsIpAuditResultDetail devopsIpAuditResultDetail, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                               @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        QueryWrapper<DevopsIpAuditResultDetail> queryWrapper = new QueryWrapper<>();
        Page<DevopsIpAuditResultDetail> page = new Page<>(pageNo, pageSize);
        if (StringUtils.isEmpty(devopsIpAuditResultDetail.getId())) {
            return Result.error("审计结果id为空");
        }
        queryWrapper.eq("audit_result_id", devopsIpAuditResultDetail.getId());
        if (StringUtils.isNotBlank(devopsIpAuditResultDetail.getIpAddress())) {
            queryWrapper.like("ip_address", devopsIpAuditResultDetail.getIpAddress());
        }
        if (StringUtils.isNotBlank(devopsIpAuditResultDetail.getIpState())) {
            queryWrapper.eq("ip_state", devopsIpAuditResultDetail.getIpState());
        }
        if (devopsIpAuditResultDetail.getIsApply() != null) {
            queryWrapper.eq("is_apply", devopsIpAuditResultDetail.getIsApply());
        }
        if (devopsIpAuditResultDetail.getIsEquals() != null) {
            queryWrapper.eq("is_equals", devopsIpAuditResultDetail.getIsEquals());
        }
        if (devopsIpAuditResultDetail.getIsOnline() != null) {
            queryWrapper.eq("is_online", devopsIpAuditResultDetail.getIsOnline());
        }
        queryWrapper.orderByAsc("sort");

        IPage<DevopsIpAuditResultDetail> pageList = devopsIpAuditResultDetailService.page(page, queryWrapper);
        if (null != pageList.getRecords() && pageList.getRecords().size() > 0) {
            pageList.getRecords().forEach(item -> {
                if ("0".equals(item.getIpState())) {
                    item.setIpStateText("未使用");
                } else if ("1".equals(item.getIpState())) {
                    item.setIpStateText("已使用");
                }
                if ("0".equals(item.getOccupied())) {
                    item.setOccupiedText("非法占用");
                } else if ("1".equals(item.getOccupied())) {
                    item.setOccupiedText("非法篡改");
                } else if ("2".equals(item.getOccupied())) {
                    item.setOccupiedText("未获取到MAC");
                }
                if (item.getIsScan() == 0) {
                    item.setIsScanText("未扫描");
                } else {
                    if (item.getIsEquals() != null) {
                        if (item.getIsEquals() == 0) {
                            item.setIsEqualsText("不一致");
                        } else if (item.getIsEquals() == 1) {
                            item.setIsEqualsText("一致");
                        }
                    }
                    if (item.getIsApply() != null) {

                        if (item.getIsApply() == 0) {
                            item.setIsApplyText("已申请");
                        } else if (item.getIsApply() == 1) {
                            item.setIsApplyText("未申请");
                        }
                    }
                    if (item.getIsOnline() != null) {

                        if (item.getIsOnline() == 0) {
                            item.setIsOnlineText("不在线");
                        } else if (item.getIsOnline() == 1) {
                            item.setIsOnlineText("在线");
                        }
                    }
                }
            });
        }
        List<SysUsers> usersList = sysUserService.list(new QueryWrapper<SysUsers>().eq("del_flag", 0));
        pageList.getRecords().stream().forEach(item -> item.setCreateBy(getRealName(usersList, item.getCreateBy())));
        return Result.OK(pageList);
    }

    private String getRealName(List<SysUsers> usersList, String username) {
        List<SysUsers> collect = usersList.stream().filter(user -> user.getUsername().equals(username)).collect(Collectors.toList());
        if (!collect.isEmpty()) {
            return collect.get(0).getRealname();
        }
        return null;
    }

    /**
     * 导出excel
     *
     * @param request
     * @param devopsIpAuditResult
     */
    //@RequiresPermissions("org.jeecg.modules.demo:devops_ip_audit_result:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DevopsIpAuditResult devopsIpAuditResult) {
        return super.exportXls(request, devopsIpAuditResult, DevopsIpAuditResult.class, "审计结果表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    //@RequiresPermissions("devops_ip_audit_result:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, DevopsIpAuditResult.class);
    }

    @GetMapping(value = "/exportWord")
    public void exportWord(HttpServletRequest request, HttpServletResponse response, DevopsIpAuditResult
            devopsIpAuditResult) {
        devopsIpAuditResultService.generateCharts(devopsIpAuditResult.getId(), response);
    }
}
