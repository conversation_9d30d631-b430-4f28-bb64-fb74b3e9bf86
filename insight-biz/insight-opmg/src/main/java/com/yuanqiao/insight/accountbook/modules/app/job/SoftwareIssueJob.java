package com.yuanqiao.insight.accountbook.modules.app.job;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yuanqiao.insight.accountbook.modules.app.entity.*;
import com.yuanqiao.insight.accountbook.modules.app.enums.TaskStatus;
import com.yuanqiao.insight.accountbook.modules.app.enums.TaskType;
import com.yuanqiao.insight.accountbook.modules.app.queue.EventQueueProcessor;
import com.yuanqiao.insight.accountbook.modules.app.queue.SoftwareIssueCache;
import com.yuanqiao.insight.accountbook.modules.app.queue.SoftwareTerminalIssueThread;
import com.yuanqiao.insight.accountbook.modules.app.service.*;
import com.yuanqiao.insight.acore.depart.entity.SysDepart;
import com.yuanqiao.insight.common.util.common.RedisUtils;
import com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice;
import com.yuanqiao.insight.monitoring.modules.terminal.service.ITerminalDeviceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.modules.system.entity.SysUsers;
import org.jeecg.modules.system.service.ISysDepartService;
import org.jeecg.modules.system.service.ISysUserService;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
public class SoftwareIssueJob implements Job {

    @Autowired
    private IPolicyScopeService policyScopeService;
    @Autowired
    private ITerminalDeviceService terminalDeviceService;
    @Autowired
    private ISoftwareTaskService softwareTaskService;
    @Autowired
    private ISoftwarePatchInfoService softwarePatchInfoService;
    @Autowired
    private ISoftwareTaskTerminalService softwareTaskTerminalService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private ISysDepartService sysDepartService;
    @Autowired
    private ISoftwareTaskExtendService softwareTaskExtendService;
    @Autowired
    private RedisUtils redisUtils;

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        log.info("SoftwareIssueJob开始执行");
        JobDataMap dataMap = context.getJobDetail().getJobDataMap();
        SoftwareTask softwareTask = JSON.parseObject(dataMap.getString("softwareTask"), SoftwareTask.class);
        softwareTask.setActualStartTime(new Date());

        //查询出所有需要执行升级的终端
        String policyId = softwareTask.getPolicyId();
        String taskId = softwareTask.getId();

        List<TerminalDevice> terminalDeviceList;
        terminalDeviceList = new ArrayList<>();
        List<PolicyScope> scopes = policyScopeService.list(new LambdaQueryWrapper<PolicyScope>()
                .eq(PolicyScope::getPolicyId, policyId));
        scopes.forEach(scope -> {
            switch (scope.getScopeType()) {
                case "department":
                    List<TerminalDevice> t1 = terminalDeviceService.list(new LambdaQueryWrapper<TerminalDevice>()
                            .in(TerminalDevice::getDeptId, Arrays.asList(scope.getRelateId().split(","))));
                    if (CollUtil.isNotEmpty(t1)) {
                        terminalDeviceList.addAll(t1);
                    }
                    break;
                case "user":
                    //人员，暂时不添加

                    break;
                case "terminalDevice":
                    List<TerminalDevice> t3 = terminalDeviceService.list(new LambdaQueryWrapper<TerminalDevice>()
                            .in(TerminalDevice::getId, Arrays.asList(scope.getRelateId().split(","))));
                    if (CollUtil.isNotEmpty(t3)) {
                        terminalDeviceList.addAll(t3);
                    }
                    break;
                default:
                    break;
            }
        });
        softwareTask.setTaskStatus(TaskStatus.TERMINATE.getCode());
        if (CollUtil.isNotEmpty(terminalDeviceList)) {
            log.info("分配任务的终端数量={}", terminalDeviceList.size());
            softwareTask.setDeviceTotal(terminalDeviceList.size());
            List<SoftwareTaskTerminal> softwareTaskTerminalList = new ArrayList<>();
            JSONArray osType = new JSONArray();
            JSONArray cpuArch = new JSONArray();
            boolean isFile;
            if (!softwareTask.getTaskType().equals(TaskType.FILE_UP.getCode())) {
                SoftwarePatchInfo softwarePatchInfo = softwarePatchInfoService.getById(softwareTask.getSoftwarePatchId());
                softwareTask.setSoftwarePatchInfo(softwarePatchInfo);
                //生效范围，从升级包信息中获取
                osType.addAll(Arrays.asList(softwarePatchInfo.getPatchOs().split(",")));
                cpuArch.addAll(Arrays.asList(softwarePatchInfo.getFrawork().split(",")));
                isFile = false;
            } else {
                List<SoftwareTaskExtend> extendList = softwareTaskExtendService.list(new LambdaQueryWrapper<SoftwareTaskExtend>().
                        eq(SoftwareTaskExtend::getTaskId, taskId));
                softwareTask.setExtendList(extendList);
                isFile = true;
            }


            AtomicInteger count = new AtomicInteger();
            terminalDeviceList.forEach(terminalDevice -> {
                SoftwareTaskTerminal softwareTaskTerminal = new SoftwareTaskTerminal();
                softwareTaskTerminal.setTaskId(taskId);
                softwareTaskTerminal.setUniqueCode(terminalDevice.getUniqueCode());
                softwareTaskTerminal.setTerminalName(terminalDevice.getName());
                softwareTaskTerminal.setStatus(terminalDevice.getStatus());
                if (StringUtils.isNotBlank(terminalDevice.getDeptId())) {
                    SysDepart dept = sysDepartService.getById(terminalDevice.getDeptId());
                    softwareTaskTerminal.setDeptName(dept.getDepartName());
                }
                if (StringUtils.isNotBlank(terminalDevice.getBindUser())) {
                    SysUsers userByName = sysUserService.getUserByName(terminalDevice.getUsername());
                    softwareTaskTerminal.setUserName(userByName.getRealname());
                }
                softwareTaskTerminal.setCpuType(terminalDevice.getCpuType()).setOsType(terminalDevice.getOsType())
                        .setCpuArch(terminalDevice.getCpuArch()).setIp(terminalDevice.getIp());

                //检查升级包是否符合
                if (isFile || (osType.contains(terminalDevice.getOsType()) && cpuArch.contains(terminalDevice.getCpuArch()))) {
                    //符合生效范围，默认状态未执行
                    softwareTaskTerminal.setExecuteStatus("0");
                } else {
                    //不符合生效范围，设置为升级包不匹配
                    count.getAndIncrement();
                    softwareTaskTerminal.setExecuteStatus("-1");
                }
                softwareTaskTerminalList.add(softwareTaskTerminal);
            });
            //记录终端操作
            softwareTaskTerminalService.saveBatch(softwareTaskTerminalList);

            if (count.get() != terminalDeviceList.size()) {
                softwareTask.setTaskStatus(TaskStatus.RUNNING.getCode());
            } else {
                softwareTask.setActualEndTime(new Date());
                return;
            }
            List<SoftwareTaskTerminal> collect = softwareTaskTerminalList.stream()
                    .filter(softwareTaskTerminal -> "0".equals(softwareTaskTerminal.getExecuteStatus()))
                    .filter(softwareTaskTerminal -> softwareTaskTerminal.getStatus() == 1)
                    .collect(Collectors.toList());
            //任务下发，根据任务持续时长分批次
            log.info("开始下发任务");
            try {
                if (CollUtil.isNotEmpty(collect)) {
                    log.info("根据任务持续时长分批次");
                    issueTask(collect, softwareTask);
                }
            } catch (Exception e) {
                log.error("下发任务创建失败", e);
            }
        }
        softwareTaskService.updateById(softwareTask);
    }

    private void issueTask(List<SoftwareTaskTerminal> softwareTaskTerminalList, SoftwareTask softwareTask) throws Exception {
        JSONObject patchInfo = softwareTask.getPatchInfo();
        String file = patchInfo.getString("file");
        Long fileSize = CommonUtils.getFileSize(file);
        long totalSecond = DateUtil.between(softwareTask.getPlannedStartTime(), softwareTask.getPlannedEndTime(), DateUnit.SECOND);
        int size = softwareTaskTerminalList.size();
        String taskId = softwareTask.getId();
        long round = Math.round(fileSize / (300D * 1024D));
        long min = Math.min(totalSecond / size, round);
        EventQueueProcessor eventQueueProcessor = new EventQueueProcessor(taskId, min);
        Map<String, Object> taskCache = new HashMap<>();
        taskCache.put(SoftwareIssueCache.TOTAL_TIME_KEY, totalSecond);
        taskCache.put(SoftwareIssueCache.EXECUTION_TIME_KEY, round);
        taskCache.put(SoftwareIssueCache.EFFECTIVE_NUMBER_KEY, size);
        redisUtils.hmset(String.format(SoftwareIssueCache.TASK_CACHE_KEY, taskId), taskCache);
        SoftwareIssueCache.processorCache.put(taskId, eventQueueProcessor);
        for (SoftwareTaskTerminal softwareTaskTerminal : softwareTaskTerminalList) {
            SoftwareTask task = new SoftwareTask();
            BeanUtil.copyProperties(softwareTask, task);
            task.setUniqueCode(softwareTaskTerminal.getUniqueCode());
            eventQueueProcessor.addEvent(new SoftwareTerminalIssueThread(task));
        }
    }

    public static void main(String[] args) {

        double fileSize = 500D * 1024D * 1024D;
        double downloadSpeed = 300D * 1024D;

        // 计算下载时长
        double timeInSeconds = fileSize / downloadSpeed;
        System.out.println(timeInSeconds);
        // 转换为分钟和秒
        int minutes = (int) timeInSeconds / 60;
        int seconds = (int) timeInSeconds % 60;

        System.out.printf("下载时长: %d 分 %d 秒%n", minutes, seconds);
    }

}
