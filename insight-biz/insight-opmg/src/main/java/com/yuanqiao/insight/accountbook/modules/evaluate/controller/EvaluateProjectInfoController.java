package com.yuanqiao.insight.accountbook.modules.evaluate.controller;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.accountbook.modules.evaluate.docGenerate.DocExportUtil;
import com.yuanqiao.insight.accountbook.modules.evaluate.docGenerate.PdfExportUtil;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateMaterialInfo;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateProjectInfo;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateReportInfo;
import com.yuanqiao.insight.accountbook.modules.evaluate.service.IEvaluateProjectInfoService;
import com.yuanqiao.insight.accountbook.modules.evaluate.service.impl.EvaluateMaterialInfoServiceImpl;
import com.yuanqiao.insight.accountbook.modules.evaluate.service.impl.EvaluateReportInfoServiceImpl;
import com.yuanqiao.insight.common.util.common.VersionSerialUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoDict;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.modules.system.entity.SysAnnouncement;
import org.jeecg.modules.system.service.impl.SysUserServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName EvaluateProjectInfoController
 * @description: TODO
 * @datetime 2025年 05月 29日 19:28
 * @version: 1.0
 */
@Api(tags = "评估项目信息")
@RestController
@RequestMapping("/evaluate/projectInfo")
@Slf4j
public class EvaluateProjectInfoController extends JeecgController<EvaluateProjectInfo, IEvaluateProjectInfoService> {
    @Autowired
    private EvaluateReportInfoServiceImpl evaluateReportInfoService;

    @Value(value = "${excel.evaluateReportTemplate}")
    private String evaluateReportTemplate;


    @AutoDict
    @AutoLog(value = "评估项目信息-分页列表查询")
    @ApiOperation(value = "评估项目信息-分页列表查询", notes = "评估项目信息-分页列表查询")
    @GetMapping(value = "/pageList")
    public Result<?> queryPageList(EvaluateProjectInfo evaluateProjectInfo,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {

        QueryWrapper<EvaluateProjectInfo> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(evaluateProjectInfo.getProjectName())) {
            queryWrapper.like("project_name", evaluateProjectInfo.getProjectName());
        }

        Page<EvaluateProjectInfo> page = new Page<>(pageNo, pageSize);
        IPage<EvaluateProjectInfo> pageList = service.page(page, queryWrapper);

       /* List<EvaluateProjectInfo> records = pageList.getRecords();

        for (EvaluateProjectInfo item : records) {
            if (StringUtils.isNotBlank(item.getSender())) {
                String realname = sysUserService.selectRealnameByUsername(item.getSender());
                item.setSender_dictText(realname);
            }
        }
*/
        return Result.OK(pageList);
    }


    @AutoLog(value = "评估项目信息-列表查询")
    @ApiOperation(value = "评估项目信息-列表查询", notes = "评估项目信息-列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryList(EvaluateProjectInfo evaluateProjectInfo) {
        QueryWrapper<EvaluateProjectInfo> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(evaluateProjectInfo.getProjectName())) {
            queryWrapper.like("project_name", evaluateProjectInfo.getProjectName());
        }
        return Result.OK(service.list(queryWrapper));
    }

    @AutoLog(value = "评估项目信息-新增")
    @ApiOperation(value = "评估项目信息-新增", notes = "评估项目信息-新增")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody EvaluateProjectInfo evaluateProjectInfo) {
        service.save(evaluateProjectInfo);
        return Result.OK("添加成功！");
    }

    @AutoLog(value = "评估项目信息-编辑")
    @ApiOperation(value = "评估项目信息-编辑", notes = "评估项目信息-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody EvaluateProjectInfo evaluateProjectInfo) {
        service.updateById(evaluateProjectInfo);
        return Result.OK("编辑成功!");
    }

    @AutoLog(value = "评估项目信息-通过id删除")
    @ApiOperation(value = "评估项目信息-通过id删除", notes = "评估项目信息-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id") String id) {
        List<EvaluateReportInfo> reportList = evaluateReportInfoService.list(new QueryWrapper<EvaluateReportInfo>().eq("project_id", id));
        if (CollUtil.isNotEmpty(reportList)) {
            evaluateReportInfoService.remove(new QueryWrapper<EvaluateReportInfo>().in("id", reportList.stream().map(EvaluateReportInfo::getId).collect(Collectors.toList())));
            List<String> filePathList = reportList.stream().map(EvaluateReportInfo::getFilePath).collect(Collectors.toList());
            CommonUtils.deleteFile(filePathList);
        }
        service.removeById(id);
        return Result.OK("删除成功!");
    }

    @AutoLog(value = "评估项目信息-批量删除")
    @ApiOperation(value = "评估项目信息-批量删除", notes = "评估项目信息-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids") String ids) {
        List<EvaluateReportInfo> reportList = evaluateReportInfoService.list(new QueryWrapper<EvaluateReportInfo>().in("project_id", Arrays.asList(ids.split(","))));
        if (CollUtil.isNotEmpty(reportList)) {
            evaluateReportInfoService.remove(new QueryWrapper<EvaluateReportInfo>().in("id", reportList.stream().map(EvaluateReportInfo::getId).collect(Collectors.toList())));
            List<String> filePathList = reportList.stream().map(EvaluateReportInfo::getFilePath).collect(Collectors.toList());
            CommonUtils.deleteFile(filePathList);
        }
        this.service.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    @AutoLog(value = "评估项目信息-导出报告")
    @ApiOperation(value = "评估项目信息-导出报告", notes = "评估项目信息-导出报告")
    @GetMapping(value = "/exportReport")
    public void exportReport(@RequestParam(name = "id") String id, HttpServletResponse response, String exportType) {
        //获取当前项目最近一次版本号
        List<EvaluateReportInfo> lastRecordList = evaluateReportInfoService.list(new QueryWrapper<EvaluateReportInfo>().eq("project_id", id).orderByDesc("create_time").last("limit 1"));
        String versionStr = "0.0";
        if (CollUtil.isNotEmpty(lastRecordList) && lastRecordList.get(0).getVersionSerial() != null) {
            String originVersionStr = lastRecordList.get(0).getVersionSerial();
            versionStr = originVersionStr.substring(originVersionStr.indexOf("V") + 1);

        }
        String nextVersion = "V" + VersionSerialUtil.autoUpgradeVersion(versionStr);
        Map<String, Object> exportReportMap = service.getEvaluateReportById(id);
        exportReportMap.put("nextVersion", nextVersion);

        // 判断目录是否存在
        File directory = new File("resource/evaluate/resultReport");
        if (!directory.exists()) {
            // 不存在则创建目录
            directory.mkdirs();
        }

        String filePath = "";
        if ("word".equals(exportType)) {
            filePath = DocExportUtil.createReportDocTemplate(exportReportMap, evaluateReportTemplate, response);
        } else if ("pdf".equals(exportType)) {
            filePath = PdfExportUtil.createReportPdf(exportReportMap, response);
        }

        EvaluateReportInfo reportInfo = new EvaluateReportInfo();
        reportInfo.setProjectId(id);
        reportInfo.setFilePath(filePath);
        reportInfo.setVersionSerial(nextVersion);
        evaluateReportInfoService.save(reportInfo);

    }


}
