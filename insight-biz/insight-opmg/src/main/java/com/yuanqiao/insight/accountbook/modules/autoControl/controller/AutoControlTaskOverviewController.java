package com.yuanqiao.insight.accountbook.modules.autoControl.controller;

import cn.hutool.core.date.DateUtil;
import com.yuanqiao.insight.accountbook.modules.autoControl.service.IAutoControlSceneService;
import com.yuanqiao.insight.accountbook.modules.autoControl.service.IAutoControlScriptService;
import com.yuanqiao.insight.accountbook.modules.autoControl.service.IAutoControlTaskService;
import com.yuanqiao.insight.accountbook.modules.app.service.ISoftwareInfoService;
import com.yuanqiao.insight.service.device.service.IDeviceInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName 自动化控制作业总览
 * @description: TODO
 * @datetime 2024年 02月 29日 16:04
 * @version: 1.0
 */
@Slf4j
@Api(tags="自动化控制作业总览")
@RestController
@RequestMapping("/autoControl/task/overview")
public class AutoControlTaskOverviewController {
    @Autowired
    private  IAutoControlTaskService autoControlTaskService;
    @Autowired
    private IAutoControlSceneService autoControlSceneService;
    @Autowired
    private IAutoControlScriptService autoControlScriptService;
    @Autowired
    private IDeviceInfoService deviceInfoService;
    @Autowired
    private ISoftwareInfoService devopsSoftwareInfoService;

    @AutoLog(value = "自动化控制作业总览-数据统计")
    @ApiOperation(value="自动化控制作业总览-数据统计", notes="自动化控制作业总览-数据统计")
    @GetMapping(value = "/statistics")
    public Result<?> Statistics() {
        HashMap<String, Object> statisticsMap = new HashMap<>();
        // 作业数量
        statisticsMap.put("taskNumber", autoControlTaskService.queryTaskNumber());
        // 场景数量
        statisticsMap.put("sceneData", autoControlSceneService.querySceneData());
        // 脚本数量
        statisticsMap.put("scriptNumber", autoControlScriptService.queryScriptNumber());
        // 网关数量
        statisticsMap.put("adaptersNumber", deviceInfoService.queryAdaptersNumber());
        // 软件数量
        statisticsMap.put("softwareNumber", devopsSoftwareInfoService.querySoftwareNumber());
        // 设备数量
        statisticsMap.put("deviceNumber", deviceInfoService.queryDeviceNumber());

        return Result.OK(statisticsMap);
    }


    @AutoLog(value = "自动化控制作业总览-作业数量趋势分析")
    @ApiOperation(value = "自动化控制作业总览-作业数量趋势分析", notes = "自动化控制作业总览-作业趋势分析")
    @GetMapping(value = "/taskTrend")
    public Result<?> taskTrend(Date startDate, Date endDate) {
        if (startDate == null || endDate == null) {
            startDate = DateUtil.offsetDay(DateUtil.beginOfDay(new Date()), -30);
            endDate = DateUtil.endOfDay(new Date());
        }
        List<Map<String, Object>> mapList = autoControlTaskService.taskTrend( startDate,  endDate);
        return Result.OK(mapList);
    }



}
