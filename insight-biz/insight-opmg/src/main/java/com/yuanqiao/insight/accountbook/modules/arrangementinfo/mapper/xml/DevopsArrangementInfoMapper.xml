<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.accountbook.modules.arrangementinfo.mapper.DevopsArrangementInfoMapper">

    <select id="getTimeMap"
            resultType="com.yuanqiao.insight.accountbook.modules.arrangementinfo.entity.DevopsArrangementInfo">
        select * from devops_arrangement_info
    </select>

    <update id="updateBatchByIds" parameterType="java.util.List">
        update devops_arrangement_info set status=1 where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectBySchedualId" resultType="java.lang.String" parameterType="java.util.List">
        select id from devops_arrangement_info where schedual_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <delete id="delBySchedualId"  parameterType="java.util.List">
        delete from devops_arrangement_info where schedual_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getDevopsSchedualInfoMaps" resultType="com.yuanqiao.insight.accountbook.modules.arrangementinfo.entity.DevopsArrangementInfo">
        select dai.*,dsi.name as schedual_name  from devops_arrangement_info as dai left join devops_schedual_info as dsi on dai.schedual_id = dsi.id where dai.id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


</mapper>