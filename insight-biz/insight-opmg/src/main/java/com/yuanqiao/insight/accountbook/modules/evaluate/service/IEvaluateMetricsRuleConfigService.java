package com.yuanqiao.insight.accountbook.modules.evaluate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateMetricsRuleConfig;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluationRuleInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName IEvaluateMetricsRuleConfigService
 * @description: 评估指标规则配置服务接口
 * @datetime 2025年 07月 28日
 * @version: 1.0
 */
public interface IEvaluateMetricsRuleConfigService extends IService<EvaluateMetricsRuleConfig> {
    
    /**
     * 保存指标评估规则配置
     * @param metricsId 指标ID
     * @param ruleConfig 规则配置JSON
     * @return 是否保存成功
     */
    boolean saveRuleConfig(String metricsId, String ruleConfig);
    
    /**
     * 根据指标ID获取规则配置
     * @param metricsId 指标ID
     * @return 规则配置
     */
    EvaluateMetricsRuleConfig getByMetricsId(String metricsId);
    
    /**
     * 获取指标的评估规则列表
     * @param metricsId 指标ID
     * @return 评估规则列表
     */
    List<EvaluationRuleInfo> getEvaluationRules(String metricsId);
    
    /**
     * 保存评估规则详情（解析JSON并保存到详情表）
     * @param metricsId 指标ID
     * @param ruleConfig 规则配置JSON
     * @return 是否保存成功
     */
    boolean saveRuleDetails(String metricsId, String ruleConfig);
    
    /**
     * 更新指标评估规则配置
     * @param metricsId 指标ID
     * @param ruleConfig 规则配置JSON
     * @return 是否更新成功
     */
    boolean updateRuleConfig(String metricsId, String ruleConfig);
    
    /**
     * 删除指标评估规则配置
     * @param metricsId 指标ID
     * @return 是否删除成功
     */
    boolean removeByMetricsId(String metricsId);
}
