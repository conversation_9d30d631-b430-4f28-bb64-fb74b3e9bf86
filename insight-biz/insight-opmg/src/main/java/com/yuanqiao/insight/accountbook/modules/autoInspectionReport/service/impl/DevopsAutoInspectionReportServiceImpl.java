package com.yuanqiao.insight.accountbook.modules.autoInspectionReport.service.impl;


import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.accountbook.modules.autoInspection.entity.DevopsAutoInspection;
import com.yuanqiao.insight.accountbook.modules.autoInspection.service.IDevopsAutoInspectionService;
import com.yuanqiao.insight.accountbook.modules.autoInspectionReport.entity.DevopsAutoInspectionReport;
import com.yuanqiao.insight.accountbook.modules.autoInspectionReport.mapper.DevopsAutoInspectionReportMapper;
import com.yuanqiao.insight.accountbook.modules.autoInspectionReport.service.IDevopsAutoInspectionReportService;
import com.yuanqiao.insight.acore.config.SendMail3;
import com.yuanqiao.insight.common.constant.CommonConstant;
import com.yuanqiao.insight.modules.notice.entity.SendMsg;
import com.yuanqiao.insight.modules.notice.entity.SysNoticeExtend;
import com.yuanqiao.insight.modules.notice.entity.SysNoticeTemplate;
import com.yuanqiao.insight.modules.notice.mapper.SysNoticeExtendMapper;
import com.yuanqiao.insight.modules.notice.service.ISysNoticeConfigService;
import com.yuanqiao.insight.modules.notice.service.ISysNoticeExtendService;
import com.yuanqiao.insight.modules.notice.service.ISysNoticeTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.mq.RedisMq;
import org.jeecg.common.mq.stream.Streams;
import org.jeecg.modules.fileinfo.entity.FileInfo;
import org.jeecg.modules.fileinfo.service.IFileInfoService;
import org.jeecg.modules.system.entity.SysAnnouncement;
import org.jeecg.modules.system.entity.SysUsers;
import org.jeecg.modules.system.service.ISysAnnouncementSendService;
import org.jeecg.modules.system.service.ISysAnnouncementService;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * @Description: 自动检查报告
 * @Author: jeecg-boot
 * @Date: 2021-03-22
 * @Version: V1.0
 */
@Slf4j
@Service
public class DevopsAutoInspectionReportServiceImpl extends ServiceImpl<DevopsAutoInspectionReportMapper, DevopsAutoInspectionReport> implements IDevopsAutoInspectionReportService {

    @Autowired
    private IFileInfoService fileInfoService;
    @Autowired
    private IDevopsAutoInspectionService devopsAutoInspectionService;
    @Autowired
    private ISysNoticeTemplateService sysNoticeTemplateService;
    @Autowired
    private ISysNoticeConfigService sysNoticeConfigService;
    @Autowired
    private SysNoticeExtendMapper sysNoticeExtendMapper;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private ISysNoticeExtendService sysNoticeExtendService;
    @Autowired
    private RedisMq redisMq;
    @Autowired
    private ISysAnnouncementService announcementService;
    @Autowired
    private ISysAnnouncementSendService announcementSendService;

    /**
     * 通过智能巡检任务添加智能巡检报告
     *
     * @param devopsAutoInspection 智能巡检任务
     */
    @Override
    @Transactional
    public void addDevopsAutoInspectionReport(DevopsAutoInspection devopsAutoInspection, String[] filePath) {
        Map<String, String> userMap = sysUserService.selectRealnameByUsername();
        int counts = 1;
        //读取此时巡检报告数
        if (null != devopsAutoInspection.getExecuteCounts()) {
            //counts = devopsAutoInspection.getExecuteCounts() + 1;
            counts = this.count(new QueryWrapper<DevopsAutoInspectionReport>().eq("task_id", devopsAutoInspection.getId())) + 1;
        }
        devopsAutoInspection.setTaskEndTime(new Date());
        devopsAutoInspection.setExecuteCounts(counts);
        devopsAutoInspectionService.updateById(devopsAutoInspection);

        //添加巡检报告
        DevopsAutoInspectionReport devopsAutoInspectionReport = new DevopsAutoInspectionReport();
        String froupId = fileInfoService.getFileGroup(Arrays.asList(filePath));
        devopsAutoInspectionReport.setTaskId(devopsAutoInspection.getId());
        devopsAutoInspectionReport.setTaskstartTime(devopsAutoInspection.getTaskStartTime());
        devopsAutoInspectionReport.setTaskendTime(new Date());
        devopsAutoInspectionReport.setFileId(froupId);
        devopsAutoInspectionReport.setReportType(devopsAutoInspection.getInspectionType());

        List<String> fileIdList = new ArrayList<>();
        fileIdList.add(froupId);
        QueryWrapper<FileInfo> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(FileInfo::getFileGroup, fileIdList);
        List<FileInfo> list = fileInfoService.list(wrapper);
        if (!CollectionUtils.isEmpty(list)) {
            for (FileInfo fileInfo : list) {
                if (CommonConstant.TASK_FILE_SUFFIX.equals(fileInfo.getSuffix())) {
                    devopsAutoInspectionReport.setFileUrl(fileInfo.getFileUrl());
                    devopsAutoInspectionReport.setFileName(fileInfo.getFileName());
                    devopsAutoInspectionReport.setTaskCreateUser(userMap.get(devopsAutoInspection.getCreateBy()));
                }
            }
        }
        this.save(devopsAutoInspectionReport);

        //TODO 添加发邮件功能
        if (StringUtils.isNotEmpty(devopsAutoInspection.getPushType()) && devopsAutoInspection.getPushType().equals("Y") && StringUtils.isNotEmpty(devopsAutoInspection.getPushAddress())) {
            sendAutoInspectionMessage(devopsAutoInspection, filePath);
        }

        log.info("AAAA巡检任务已完成！");
    }

    /**
     * 发送智能巡检的消息
     *
     * @param devopsAutoInspection 智能巡检任务
     * @param filePath             报告地址
     */
    public void sendAutoInspectionMessage(DevopsAutoInspection devopsAutoInspection, String[] filePath) {
        SysNoticeTemplate sysNoticeTemplate = sysNoticeTemplateService.getOne(new QueryWrapper<SysNoticeTemplate>().in("id", devopsAutoInspection.getPushAddress()));
        String template = sysNoticeTemplate.getTemplate(); //模板内容
        JSONObject templateJson = JSONObject.parseObject(template); // 模板内容
        String finishTime = DateUtil.now();
        JSONObject parJson = new JSONObject();
        parJson.put("taskName", devopsAutoInspection.getTaskName());
        parJson.put("finishTime", finishTime);
        templateJson.put("par", parJson);
        templateJson.put("title", "巡检任务执行完成");
        SysAnnouncement announcement = new SysAnnouncement("巡检任务执行完成", String.format("%s执行完成，完成时间：%s", devopsAutoInspection.getTaskName(), finishTime));
        templateJson.put("announcement", announcement);
        JSONObject noticeConfig = sysNoticeConfigService.getNoticeConfig(sysNoticeTemplate.getNoticeConfigId());// 通知配置
        noticeConfig.getJSONObject("sendMsg").put("attachments", filePath);
        JSONObject msgData = new JSONObject();
        msgData.putAll(noticeConfig);
        msgData.put("template", templateJson);
        msgData.put("isPar", true);
        redisMq.publish(Streams.NOTICE, msgData);
        //添加系统通知信息
    }

    /**
     * 获取收件人发送信息
     *
     * @param templateJson
     * @throws Exception
     */
    public void send(JSONObject templateJson, SendMsg sendMsg, String[] filePath) throws Exception {
        String subject = templateJson.getString("subject");//主题
        String content = templateJson.getString("content");//正文
        content = generateWelcome(templateJson.getJSONObject("par"), content);
        JSONArray receiveEmailArray = templateJson.getJSONArray("sendTo");//收件人
        List<String> filePathList = Arrays.asList(filePath);
        List<SysUsers> userList = sysUserService.getUserByIds(receiveEmailArray.toJavaList(String.class));
        for (SysUsers receiveUser : userList) {
            if (StringUtils.isEmpty(receiveUser.getEmail())) {
                continue;
            }
            SendMail3.sendEmail(sendMsg.getSender(), sendMsg.getPassword(), sendMsg.getSmtp(), receiveUser.getEmail(), sendMsg.getSendName(), subject, content, sendMsg.getPort(), sendMsg.getSslEnable(), filePathList);
        }
    }

    /**
     * 模板变量替换
     *
     * @param template
     * @return
     */
    private String generateWelcome(JSONObject jsonObject, String template) {
        if (org.apache.commons.lang3.StringUtils.isBlank(template)) {
            return "";
        }
        for (Object s : jsonObject.keySet()) {
            final String value = jsonObject.getString(s.toString());
            if (org.apache.commons.lang3.StringUtils.isNotBlank(value)) {
                template = template.replaceAll("\\$\\{".concat(s.toString()).concat("\\}"), value);
            }
        }
        return template;
    }

    /**
     * 获取邮箱的必须参数
     *
     * @return
     */
    public SendMsg getSendMsg(String noticeConfigId) {

        SendMsg sendMag = new SendMsg();
        List<SysNoticeExtend> extendByConfigId = sysNoticeExtendMapper.findExtendByConfigId(noticeConfigId);
        for (SysNoticeExtend extend : extendByConfigId) {
            if ("url".equals(extend.getFieldCode())) {
                sendMag.setSmtp(extend.getFieldValue());
            }
            if ("username".equals(extend.getFieldCode())) {
                sendMag.setSendName(extend.getFieldValue());
            }
            if ("password".equals(extend.getFieldCode())) {
                sendMag.setPassword(extend.getFieldValue());
            }
            if ("port".equals(extend.getFieldCode())) {
                sendMag.setPort(extend.getFieldValue());
            }
            if ("from".equals(extend.getFieldCode())) {
                sendMag.setSender(extend.getFieldValue());
            }
            if ("sslEnable".equals(extend.getFieldCode())) {
                sendMag.setSslEnable(Boolean.valueOf(extend.getFieldValue()));
            }

        }

        return sendMag;
    }


}
