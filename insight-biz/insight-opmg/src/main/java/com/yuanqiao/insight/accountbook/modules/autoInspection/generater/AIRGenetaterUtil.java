package com.yuanqiao.insight.accountbook.modules.autoInspection.generater;

import com.itextpdf.text.Font;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.yuanqiao.insight.accountbook.modules.autoInspection.entity.DevopsAutoInspection;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.common.util.DateUtils;
import org.jeecg.common.util.MultipartFileUtil;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.*;

/**
 * <AUTHOR>
 * @title: AIRGenetaterUtil          智能巡检生成报告
 * @projectName jeecg-boot-parent
 * @description: TODO
 * @date 2021/5/15-14:18
 */
@Slf4j
public class AIRGenetaterUtil {
    public String exportAIResultToExcel(DevopsAutoInspection autoInspection, AITask aITask, String filePath,String fileName) {

        FileOutputStream fos = null;
        HSSFWorkbook wb = null;

        try {
            // 建立excel文件工作区对象
            wb = new HSSFWorkbook();
            HSSFPalette palette = wb.getCustomPalette();
            //设置单元格颜色填充方式      0代表偶数行rgb(5,82,124)，1代表奇数行rgb(23,65,100)
            palette.setColorAtIndex((short) 10, (byte) (5), (byte) (82), (byte) (124));
            palette.setColorAtIndex((short) 11, (byte) (23), (byte) (65), (byte) (100));
            palette.setColorAtIndex((short) 12, (byte) (46), (byte) (73), (byte) (88));
            palette.setColorAtIndex((short) 13, (byte) (2), (byte) (136), (byte) (209));
            // 创建一个页签
            String sheetName = "巡检报告";
            HSSFSheet sheet = wb.createSheet(sheetName);
            //设置默认行高和列宽
            sheet.setDefaultRowHeight((short) 1000);
            sheet.setDefaultColumnWidth(40);


            // 创建报告标题样式
            HSSFCellStyle reportHeaderStyle = createReportHeaderStyle(wb);

            // 创建报告概要信息列表内容样式
            HSSFCellStyle generalInfoStyle = createGeneralInfoStyle(wb);

            // 创建报告告警统计信息列表表头行样式
            HSSFCellStyle warningInfoHeaderStyle = createWarningInfoHeaderStyle(wb);
            // 创建报告告警统计信息列表内容行样式  偶数行和奇数行样式
            HSSFCellStyle warningInfoOddContentStyle = createWarningInfoContentStyle(wb, 0);
            HSSFCellStyle warningInfoEvenContentStyle = createWarningInfoContentStyle(wb, 1);

            // 创建报告分类巡检信息列表分类名称行样式
            HSSFCellStyle aiInfoTypeNameStyle = createAIInfoTypeNameStyle(wb);
            // 创建报告分类巡检信息列表表头行样式
            HSSFCellStyle aiInfoHeaderStyle = createAIInfoHeaderStyle(wb);
            // 创建报告分类巡检信息列表内容行样式    偶数行和奇数行样式
            HSSFCellStyle aiInfoContentOddStyle = createAIInfoContentStyle(wb, 0);
            HSSFCellStyle aiInfoContentEvenStyle = createAIInfoContentStyle(wb, 1);


            ArrayList<Integer> columnNumList = new ArrayList<>();
            //设备状态列名集合
            Map<String, AIRGeneraterTypeResult> generaterResult = aITask.getAIRGeneraterResult().getGeneraterResult();
            columnNumList.add(generaterResult.get("total").getDeviceNumMap().size() + 1);
            //不同产品指标列名集合
            Map<String, Map<String, String>> dataColumnMap = aITask.getAiReportDataColumnDictory().getDataColumnMap();
            for (Map.Entry<String, Map<String, String>> entry : dataColumnMap.entrySet()) {
                columnNumList.add(entry.getValue().size());
            }
            int maxColumnNum = Collections.max(columnNumList);


            // 创建报告标题行
            HSSFRow reportHeaderRow = sheet.createRow(0);
            reportHeaderRow.setHeight((short) 1000);
            HSSFCell reportHeaderCell = reportHeaderRow.createCell(0);
            reportHeaderCell.setCellValue("巡检任务详情表");
            reportHeaderCell.setCellStyle(reportHeaderStyle);
            // 标题行单元格设置
            // 合并单元格CellRangeAddress构造参数依次表示起始行，截至行，起始列， 截至列
            CellRangeAddress region = new CellRangeAddress(0, 0, 0, maxColumnNum);
            sheet.addMergedRegion(region);
            setRegionStyle(sheet, region, reportHeaderStyle);


            // 创建报告概要信息列表
            createGeneInfoTable(sheet, generalInfoStyle, aITask, autoInspection, maxColumnNum);


            // 创建告警信息统计列表，并返回结束行号
            int nextRowNum = createWarningInfoTable(sheet, warningInfoHeaderStyle, warningInfoOddContentStyle, aITask, maxColumnNum);


            // 创建报告分类巡检信息列表
            createAIInfoTable(sheet, aiInfoTypeNameStyle, aiInfoHeaderStyle, aiInfoContentOddStyle, aiInfoContentEvenStyle, aITask, nextRowNum, maxColumnNum);
//            // 将组织得到的报告内容写入excel文件
            File reportFile = new File(filePath + fileName);
            System.out.println(reportFile.getParentFile());
            if (!reportFile.getParentFile().exists()){
                reportFile.getParentFile().mkdir();
            }
            fos = new FileOutputStream(reportFile);
            wb.write(fos);
            MultipartFile file = MultipartFileUtil.fileToMultipartFile(reportFile,"","text/plain");
            String filePathResult = CommonUtils.upload(file, filePath, null);
            return filePathResult;
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        } finally {
            try {
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            try {
                if (wb != null) {
                    wb.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return "";
    }

    /**
     * 创建报告概要信息列表
     *
     * @param sheet
     * @param generalInfoStyle
     * @param aITask
     */
    private void createGeneInfoTable(HSSFSheet sheet, HSSFCellStyle generalInfoStyle, AITask aITask, DevopsAutoInspection autoInspection, Integer maxColumnNum) {
        HSSFRow row1 = sheet.createRow(1);
        row1.setHeight((short) 1000);
        Map<String, AIRGeneraterTypeResult> geneTypeResultMap = aITask.getAIRGeneraterResult().getGeneraterResult();
        AIRGeneraterTypeResult airGeneraterTypeResult = geneTypeResultMap.get("total");
        Map<String, Integer> deviceNumMap = airGeneraterTypeResult.getDeviceNumMap();
        String row1Context = "本次巡检内容：" + aITask.getDeviceNames();

        StringBuilder row3Sb = new StringBuilder("共巡检设备数量：" + deviceNumMap.get("总数") + "，其中");
        deviceNumMap.forEach((key, value) -> {
            if (!key.equals("总数")) {
                row3Sb.append(key + "：" + value + ", ");
            }
        });

        String row1Text = "巡检任务名称：" + autoInspection.getTaskName() +
                "  任务制定人：" + autoInspection.getCreateByName() + "  巡检日期：" + DateUtils.formatDateHMS(autoInspection.getTaskStartTime()) + "\n" +
                row1Context + "\n" + row3Sb.toString().substring(0, row3Sb.toString().length() - 2);
        HSSFCell row1Cell = row1.createCell(0);

        row1Cell.setCellValue(row1Text);
        row1Cell.setCellStyle(generalInfoStyle);
        CellRangeAddress region = new CellRangeAddress(1, 3, 0, maxColumnNum);
        sheet.addMergedRegion(region);
        setRegionStyle(sheet, region, generalInfoStyle);

    }

    /**
     * 创建告警信息统计列表
     *
     * @param sheet
     * @param warningInfoHeaderStyle
     * @return
     */
    private int createWarningInfoTable(HSSFSheet sheet, HSSFCellStyle warningInfoHeaderStyle, HSSFCellStyle warningInfoContentOddStyle, AITask aITask, Integer maxColumnNum) {
        Map<String, AIRGeneraterTypeResult> geneTypeResultMap = aITask.getAIRGeneraterResult().getGeneraterResult();
        AIRGeneraterTypeResult airGeneraterTypeResult = geneTypeResultMap.get("total");
        Map<String, Integer> allStatusColumnMap = airGeneraterTypeResult.getDeviceNumMap();
        int endRowNum = 5;

        //第四行 -- 列名
        HSSFRow headRow = sheet.createRow(4);

        HSSFCell headCell0 = headRow.createCell(0);
        headCell0.setCellValue("巡检产品名称");
        headCell0.setCellStyle(warningInfoHeaderStyle);

        CellRangeAddress region0 = new CellRangeAddress(4, 4, 0, 1);
        sheet.addMergedRegion(region0);
        setRegionStyle(sheet, region0, warningInfoHeaderStyle);

        int i = 1;
        Iterator<Map.Entry<String, Integer>> iterator = allStatusColumnMap.entrySet().iterator();
        while (iterator.hasNext() || i < maxColumnNum) {
            Map.Entry<String, Integer> next = null;
            String key = "--";
            try {
                next = iterator.next();
                key = next.getKey();
            } catch (Exception e) {
            }
            i++;
            HSSFCell headCell1 = headRow.createCell(i);
            headCell1.setCellValue(key);
            headCell1.setCellStyle(warningInfoHeaderStyle);
        }


        //第五行 -- 列值
        HSSFCellStyle warningInfoContentStyle = warningInfoContentOddStyle;
        Iterator<String> iter = geneTypeResultMap.keySet().iterator();
        while (iter.hasNext()) {
            String key = iter.next();
            AIRGeneraterTypeResult geneTypeResult = geneTypeResultMap.get(key);
            if (key.equals("total")) {
                continue;
            }
            Map<String, Integer> productStatusColumnMap = geneTypeResult.getDeviceNumMap();
            HSSFRow contentRow = sheet.createRow(endRowNum);
            HSSFCell cell0 = contentRow.createCell(0);
            if (key.equals("数据库")) {
                cell0.setCellValue(geneTypeResult.getTypeName() + "（个）");
                cell0.setCellStyle(warningInfoContentStyle);
            } else {
                cell0.setCellValue(geneTypeResult.getTypeName() + "（台 ）");
                cell0.setCellStyle(warningInfoContentStyle);
            }

            CellRangeAddress region2 = new CellRangeAddress(endRowNum, endRowNum, 0, 1);
            sheet.addMergedRegion(region2);
            setRegionStyle(sheet, region2, warningInfoContentStyle);

            int a = 1;
            Iterator<Map.Entry<String, Integer>> iter1 = productStatusColumnMap.entrySet().iterator();
            while (iter1.hasNext() || a < maxColumnNum) {
                Map.Entry<String, Integer> next = null;
                String value = "--";
                try {
                    next = iter1.next();
                    value = next.getValue() + "";
                } catch (Exception e) {
                }
                a++;
                HSSFCell headCell1 = contentRow.createCell(a);
                headCell1.setCellValue(value);
                headCell1.setCellStyle(warningInfoContentOddStyle);
            }


            endRowNum++;
        }
        return endRowNum;
    }

    /**
     * 创建报告分类巡检信息列表
     *
     * @param sheet
     * @param aiInfoTypeNameStyle
     * @param aiInfoHeaderStyle
     * @param aITask
     * @param startRowNum
     */
    private void createAIInfoTable(HSSFSheet sheet, HSSFCellStyle aiInfoTypeNameStyle, HSSFCellStyle aiInfoHeaderStyle, HSSFCellStyle aiInfoContentOddStyle, HSSFCellStyle aiInfoContentEvenStyle, AITask aITask, int startRowNum, int maxColumnNum) {
        Map<String, AIRGeneraterTypeResult> geneTypeResultMap = aITask.getAIRGeneraterResult().getGeneraterResult();
        Iterator<String> iter = geneTypeResultMap.keySet().iterator();

        while (iter.hasNext()) {
            String key = iter.next();
            if (key.equals("total")) {
                continue;
            }
            AIRGeneraterTypeResult geneTypeResult = geneTypeResultMap.get(key);
            HSSFRow typeRow = sheet.createRow(startRowNum);
            HSSFCell typeCell0 = typeRow.createCell(0);
            typeCell0.setCellValue(geneTypeResult.getTypeName());
            typeCell0.setCellStyle(aiInfoTypeNameStyle);

            CellRangeAddress region1 = new CellRangeAddress(startRowNum, startRowNum, 0, maxColumnNum);
            sheet.addMergedRegion(region1);
            setRegionStyle(sheet, region1, aiInfoTypeNameStyle);

            startRowNum++;
            Map<String, String> dataColumnMap = aITask.getAiReportDataColumnDictory().getDataColumnMapByType(key);
            Iterator<String> columnKeyIter = dataColumnMap.keySet().iterator();
            // 生成列头
            HSSFRow headerRow = sheet.createRow(startRowNum);
            int i = 0;
            while (columnKeyIter.hasNext() || i <= maxColumnNum) {
                String columnKey = "--";
                String columnValue = "--";
                try {
                    columnKey = columnKeyIter.next();
                    columnValue = dataColumnMap.get(columnKey);
                } catch (Exception e) {
                }
                HSSFCell headerCell = headerRow.createCell(i);
                headerCell.setCellValue(columnValue);
                headerCell.setCellStyle(aiInfoHeaderStyle);
                if (columnValue.equalsIgnoreCase("巡检设备名称")) {
                    CellRangeAddress region2 = new CellRangeAddress(startRowNum, startRowNum, 0, 1);
                    sheet.addMergedRegion(region2);
                    setRegionStyle(sheet, region2, aiInfoHeaderStyle);
                    i = i + 1;
                }
                i++;
            }
            startRowNum++;


            java.util.List<Map<String, String>> resultList = geneTypeResult.getAiDetailedResult();
            // 按行组织输出详细巡检结果
            int count = 0;
            for (Map<String, String> result : resultList) {
                Iterator<String> dataKeyIter = dataColumnMap.keySet().iterator();
                HSSFRow dataRow = sheet.createRow(startRowNum);
                HSSFCellStyle aiInfoContentStyle;
                if (count % 2 == 0) {
                    aiInfoContentStyle = aiInfoContentOddStyle;
                } else {
                    aiInfoContentStyle = aiInfoContentEvenStyle;
                }
                count++;
                int j = 0;
                while (dataKeyIter.hasNext() || j <= maxColumnNum) {
                    String dataKey = "--";
                    String dataValue = "--";
                    try {
                        dataKey = dataKeyIter.next();
                        dataValue = StringUtils.isNotEmpty(result.get(dataKey)) ? result.get(dataKey) : "--";
                    } catch (Exception e) {
                    }
                    HSSFCell headerCell = dataRow.createCell(j);
                    try {
                        headerCell.setCellValue(dataValue);
                    } catch (Exception e) {
                        headerCell.setCellValue("-- 数据超限 --");
                    }
                    headerCell.setCellStyle(aiInfoContentStyle);
                    if (dataKey.equalsIgnoreCase("basicInfo.displayName")) {
                        CellRangeAddress region3 = new CellRangeAddress(startRowNum, startRowNum, 0, 1);
                        sheet.addMergedRegion(region3);
                        setRegionStyle(sheet, region3, aiInfoContentStyle);
                        j = j + 1;
                    }
                    j++;
                }
                startRowNum++;
            }

        }
    }

    // *****************************************************************************************************************
    // *****************************************************************************************************************

    /**
     * 智能巡检创建PDF文档
     *
     * @return
     * @throws Exception
     */
    public void createPDF(AITask aITask, DevopsAutoInspection autoInspection, String filePath, String[] photoPath) throws Exception {
        FileOutputStream fos = null;
        Document doc = null;
        try {

            // 设置纸张大小和背景色
            Rectangle rect = new Rectangle(PageSize.A4);
            // 创建文档实例
            doc = new Document(rect);
            // 添加中文字体
            BaseFont bfChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

            // 设置字体样式
            Font textFont = new Font(bfChinese, 11, Font.NORMAL, BaseColor.BLACK); // 正常
            Font lineFont = new Font(bfChinese, 11, Font.NORMAL, BaseColor.BLACK); // 正常
            Font firsetTitleFont = new Font(bfChinese, 22, Font.NORMAL, BaseColor.BLACK); // 一级标题
            Font secondTitleFont = new Font(bfChinese, 15, Font.BOLD, BaseColor.BLACK); // 二级标题

            // 将组织得到的报告内容写入pdf文件
            fos = new FileOutputStream(new File(filePath));
            // 创建输出流
            PdfWriter.getInstance(doc, fos);
            doc.open();
            doc.newPage();

            //PDF文档内容开始
            // 标题及下划线
            Paragraph p1 = new Paragraph();
            p1 = new Paragraph("巡检报告", firsetTitleFont);
            p1.setLeading(10);

            p1.setAlignment(Element.ALIGN_CENTER);
            doc.add(p1);
            String line = "-------------------------------";
            line = line + line + line + line + "--";
            p1 = new Paragraph(line, lineFont);
            p1.setLeading(10);
            doc.add(p1);
            p1 = new Paragraph(" ", lineFont);
            doc.add(p1);

            PdfPTable tablePhoto = new PdfPTable(3);
            int[] width = {250, 40, 250};
            tablePhoto.setWidths(width);
            PdfPCell detailsCell = new PdfPCell(p1);
            //设置第一幅图片
            Image image1 = Image.getInstance(photoPath[0]);
            image1.setBorder(0);
            detailsCell.addElement(image1);
            detailsCell.setBorderWidth(0);
            detailsCell.setBorderColor(null);
            tablePhoto.addCell(detailsCell);
            PdfPCell detailsCel2 = new PdfPCell(p1);
            detailsCel2.addElement(new Phrase("", textFont));
            detailsCel2.setBorderWidth(0);
            tablePhoto.addCell(detailsCel2);

            //设置第二幅图片
            Image image2 = Image.getInstance(photoPath[1]);
            detailsCell = new PdfPCell(p1);
            detailsCell.addElement(image2);
            detailsCell.setBorderWidth(0);
            detailsCell.setBorderColor(null);
            tablePhoto.addCell(detailsCell);
            doc.add(tablePhoto);

            //创建概要信息表格
            createDatailsInfoTable(doc, textFont, secondTitleFont, autoInspection, aITask);

            // 创建告警信息统计列表，
            PdfPTable table = new PdfPTable(6);
            table.setWidthPercentage(100);
//                    table.setTotalWidth(new float[]{96, 96, 96, 96, 96, 100}); // 设置列宽
//                    table.setLockedWidth(true); // 锁定列宽
            String[] title = {"巡检指标名称", "正常", "一般警告", "严重警告", "未连接", "数量"};
            createDetailsTable(table, title, 5, 6, aITask);
            doc.add(table);

            // 创建报告分类巡检信息列表
            table = new PdfPTable(10);
            table.setWidthPercentage(100);
//                    table.setTotalWidth(new float[]{50, 50, 70, 110, 50, 50, 50, 50, 50, 50}); // 设置列宽
//                    table.setLockedWidth(true); // 锁定列宽
            createCellByType(table, aITask);
            doc.add(table);

        } catch (DocumentException e) {
            log.error("AIRGenetaterUtil类的createPDF方法发生异常", e);
        } catch (IOException e) {
            throw new Exception("将巡检内容写入本地PDF文件过程中发生异常！", e);
        } finally {
            if (doc != null) {
                doc.close();
            }
            if (fos != null) {
                try {
                    fos.flush();
                    fos.close();
                } catch (IOException e) {
                }
                fos = null;
            }
        }
    }

    /**
     * 创建分类资源表格PDF
     *
     * @param table
     * @throws IOException
     * @throws DocumentException
     */
    private void createCellByType(PdfPTable table,
                                  AITask aITask) throws DocumentException, IOException {
        // 添加中文字体
        BaseFont bfChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        // 设置字体样式
        Font font = new Font(bfChinese, 11, Font.NORMAL, BaseColor.BLACK); // 正常
        BaseColor color = null;
        //设置表头
        Map<String, AIRGeneraterTypeResult> geneTypeResultMap = aITask
                .getAIRGeneraterResult().getGeneraterResult();
        for (String key : geneTypeResultMap.keySet()) {
            if (key.equals("total")) {
                continue;
            }
            AIRGeneraterTypeResult geneTypeResult = geneTypeResultMap.get(key);
            color = new BaseColor(2, 136, 209);
            createDetailsCell(color, geneTypeResult.getTypeName(), font, table, 10);
            Map<String, String> dataColumnMap = aITask.getAiReportDataColumnDictory().getDataColumnMapByType(key);
            Iterator<String> columnKeyIter = dataColumnMap.keySet().iterator();
            // 生成列头
            color = new BaseColor(46, 73, 88);
            while (columnKeyIter.hasNext()) {
                String columnKey = columnKeyIter.next();
                createDetailsCell(color, dataColumnMap.get(columnKey), font, table, 1);
            }

            List<Map<String, String>> resultList = geneTypeResult.getAiDetailedResult();
            // 按行组织输出详细巡检结果
            int i = 0;
            for (Map<String, String> result : resultList) {

                Iterator<String> dataKeyIter = dataColumnMap.keySet().iterator();
                if (i % 2 == 0) {
                    color = new BaseColor(5, 82, 124);
                } else {
                    color = new BaseColor(23, 65, 100);
                }
                while (dataKeyIter.hasNext()) {
                    String dataKey = dataKeyIter.next();
                    createDetailsCell(color, result.get(dataKey), font, table, 1);
                }
                ++i;
            }
        }
    }

    /**
     * 创建详情表格PDF
     *
     * @param table
     * @param row
     * @param cols
     * @throws IOException
     * @throws DocumentException
     */
    private void createDetailsTable(PdfPTable table, String[] title, int row, int cols, AITask aITask) throws DocumentException, IOException {
        // 添加中文字体
        BaseFont bfChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        // 设置字体样式
        Font font = new Font(bfChinese, 11, Font.NORMAL, BaseColor.BLACK); // 正常
        BaseColor color = null;
        //设置表头
        // 生成列头
        for (int j = 0; j < cols; j++) {
            color = new BaseColor(46, 73, 88);
            createDetailsCell(color, title[j], font, table, 1);
        }

        Map<String, AIRGeneraterTypeResult> geneTypeResultMap = aITask.getAIRGeneraterResult().getGeneraterResult();
        Iterator<String> iter = geneTypeResultMap.keySet().iterator();
        int i = 0;

        while (iter.hasNext()) {
            String key = iter.next();
            if (key.equals("total")) {
                continue;
            }
            AIRGeneraterTypeResult geneTypeResult = geneTypeResultMap.get(key);
            createDetailsCell(color, String.valueOf(geneTypeResult.getTypeName()), font, table, 1);
            createDetailsCell(color, String.valueOf(geneTypeResult.getNormalNum()), font, table, 1);
            createDetailsCell(color, String.valueOf(geneTypeResult.getWarningNum()), font, table, 1);
            createDetailsCell(color, String.valueOf(geneTypeResult.getErrorNum()), font, table, 1);
            createDetailsCell(color, String.valueOf(geneTypeResult.getUnconnNum()), font, table, 1);
            createDetailsCell(color, String.valueOf(geneTypeResult.getTotalNum()), font, table, 1);

            ++i;
        }
    }

    /**
     * 添加单元格PDF
     *
     * @param color 单元格背景色
     * @param key   单元格内容
     * @param font  单元格字体
     * @param table 表格
     * @param n     合并单元格数
     */
    private void createDetailsCell(BaseColor color, String key, Font font, PdfPTable table, int n) {
        PdfPCell cell = new PdfPCell(new Phrase(key, font));
        cell.setMinimumHeight(15); // 设置单元格高度
        cell.setUseAscender(true); // 设置可以居中
        cell.setHorizontalAlignment(Element.ALIGN_CENTER); // 设置水平居中
        cell.setVerticalAlignment(Element.ALIGN_MIDDLE); // 设置垂直居中
        cell.setColspan(n);
        table.addCell(cell);
    }

    /**
     * 创建概要信息表格PDF
     *
     * @param doc
     * @param textFont
     * @param secondTitleFont
     * @param aITask
     * @throws DocumentException
     */
    private void createDatailsInfoTable(Document doc, Font textFont, Font secondTitleFont, DevopsAutoInspection autoInspection, AITask aITask) throws Exception {
        PdfPTable detailsTable = new PdfPTable(1);
        detailsTable.setWidthPercentage(100);
//        detailsTable.setTotalWidth(new float[]{580}); // 设置列宽
//        detailsTable.setLockedWidth(true); // 锁定列宽
        //巡检报告概要信息
        Paragraph p1 = new Paragraph("巡检任务详情表", secondTitleFont);
        p1.setLeading(50);
        p1.setAlignment(Element.ALIGN_CENTER);
        PdfPCell detailsCell = new PdfPCell(p1);
        detailsCell.setHorizontalAlignment(Element.ALIGN_CENTER); // 设置水平居中
        detailsCell.setVerticalAlignment(Element.ALIGN_MIDDLE); // 设置垂直居中
        detailsCell.setMinimumHeight(30); // 设置单元格高度
        detailsCell.setUseAscender(true); // 设置可以居中

        detailsTable.addCell(detailsCell);
        BaseColor detaileTextColor = new BaseColor(23, 65, 100);

        String context1 = "巡检任务名称：" + autoInspection.getTaskName() + "  任务制定人：" + autoInspection.getCreateByName() + "  巡检时间：" + DateUtils.formatDateHMS(autoInspection.getTaskStartTime());
        createDetailsCell_P(context1, detaileTextColor, detailsTable, textFont);

        // 遍历自动巡检报告对象，统计相关数据
        Map<String, AIRGeneraterTypeResult> geneTypeResultMap = aITask.getAIRGeneraterResult().getGeneraterResult();
        AIRGeneraterTypeResult airGeneraterTypeResult = geneTypeResultMap.get("total");
        String context2 = " 本次巡检内容包括：" + aITask.getDeviceNames().substring(0, aITask.getDeviceNames().length() - 1);
        createDetailsCell_P(context2, detaileTextColor, detailsTable, textFont);

        String context3 = " 共巡检设备数量：" + airGeneraterTypeResult.getTotalNum() + "  其中正常：" + airGeneraterTypeResult.getNormalNum() + "  一般告警：" + airGeneraterTypeResult.getWarningNum() + "  严重告警：" + airGeneraterTypeResult.getErrorNum() + "  未连接：" + airGeneraterTypeResult.getUnconnNum();
        createDetailsCell_P(context3, detaileTextColor, detailsTable, textFont);
        doc.add(detailsTable);
    }

    /**
     * 创建概要段落PDF
     *
     * @param context
     * @param color
     * @param detailsTable
     * @param font
     */
    private void createDetailsCell_P(String context, BaseColor color, PdfPTable detailsTable, Font font) {

        Paragraph p1 = new Paragraph(context, font);
        p1.setLeading(30);
        PdfPCell detailsCell = new PdfPCell(p1);
        detailsCell.setMinimumHeight(30); // 设置单元格高度
        detailsCell.setUseAscender(true);
        detailsCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        detailsTable.addCell(detailsCell);
    }

    /**
     * 创建报告图表样式
     *
     * @param wb
     * @return
     */
    private void createReportEchartsStyle(HSSFWorkbook wb, HSSFSheet sheet) {
        HSSFRow headRow = sheet.createRow(1);
        headRow.setHeight((short) 3500);
        HSSFCell headCell = headRow.createCell(0);
        headCell.setCellValue("");
        HSSFCellStyle cellStyle = wb.createCellStyle();

        // 指定单元格内容居中对齐
//        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);

        // 指定单元格垂直居中对齐
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 指定当单元格内容显示不下时自动换行
        cellStyle.setWrapText(true);

        // 设置单元格字体
        HSSFFont font = wb.createFont();
        font.setBold(true);
        font.setFontName("宋体");
        font.setColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());  //字体颜色
        font.setFontHeight((short) 300);//888
        cellStyle.setFont(font);
        headCell.setCellStyle(cellStyle);

        CellRangeAddress region = new CellRangeAddress(1, 7, 0, 10);
        sheet.addMergedRegion(region);
        setRegionStyle(sheet, region, cellStyle);

        //设置边框样式
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);

        //设置边框颜色
        cellStyle.setTopBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        cellStyle.setBottomBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        cellStyle.setLeftBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        cellStyle.setRightBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());

    }

    /**
     * 创建报告标题样式
     *
     * @param wb
     * @return
     */
    private HSSFCellStyle createReportHeaderStyle(HSSFWorkbook wb) {
        HSSFCellStyle cellStyle = wb.createCellStyle();

        // 指定单元格内容居中对齐
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 指定单元格垂直居中对齐
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 指定当单元格内容显示不下时自动换行
        cellStyle.setWrapText(true);

        // 设置单元格字体
        HSSFFont font = wb.createFont();
        font.setBold(true);//
        font.setFontName("宋体");
        font.setColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());  //字体颜色
        font.setFontHeightInPoints((short) 15);
        cellStyle.setFont(font);

        // 设置边框样式
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);

        // 设置边框颜色
        cellStyle.setTopBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        cellStyle.setBottomBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        cellStyle.setLeftBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        cellStyle.setRightBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());

        return cellStyle;
    }

    /**
     * 创建报告概要信息列表内容样式
     *
     * @param wb
     * @return
     */
    private HSSFCellStyle createGeneralInfoStyle(HSSFWorkbook wb) {
        HSSFCellStyle cellStyle = wb.createCellStyle();

        // 指定单元格内容居中对齐
//        cellStyle.setAlignment(HorizontalAlignment.CENTER);

        // 指定单元格垂直居中对齐
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 指定当单元格内容显示不下时自动换行
        cellStyle.setWrapText(true);

        // 设置单元格字体
        HSSFFont font = wb.createFont();
        font.setBold(true);//0x2bc
        font.setFontName("宋体");
        font.setColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());  //字体颜色
        font.setFontHeightInPoints((short) 12);
        cellStyle.setFont(font);

        // 设置边框样式
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);

        // 设置边框颜色
        cellStyle.setTopBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        cellStyle.setBottomBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        cellStyle.setLeftBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        cellStyle.setRightBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());

        return cellStyle;
    }

    /**
     * 创建报告告警统计信息列表表头行样式
     *
     * @param wb
     * @return
     */
    private HSSFCellStyle createWarningInfoHeaderStyle(HSSFWorkbook wb) {
        HSSFCellStyle cellStyle = wb.createCellStyle();

        // 指定单元格内容居中对齐
        cellStyle.setAlignment(HorizontalAlignment.CENTER);

        // 指定单元格垂直居中对齐
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 指定当单元格内容显示不下时自动换行
        cellStyle.setWrapText(true);

        // 设置单元格字体
        HSSFFont font = wb.createFont();
        font.setBold(true);
        font.setFontName("宋体");
        font.setColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());  //字体颜色
        font.setFontHeightInPoints((short) 12);
        cellStyle.setFont(font);

        // 设置边框样式
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);

        // 设置边框颜色
        cellStyle.setTopBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        cellStyle.setBottomBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        cellStyle.setLeftBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        cellStyle.setRightBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());

        //单元格背景色
        cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        //单元格填充效果
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        return cellStyle;
    }

    /**
     * 创建报告告警统计信息列表内容行样式
     *
     * @param wb
     * @return
     */
    private HSSFCellStyle createWarningInfoContentStyle(HSSFWorkbook wb, int flag) {
        HSSFCellStyle cellStyle = wb.createCellStyle();

        // 指定单元格内容居中对齐
        cellStyle.setAlignment(HorizontalAlignment.CENTER);

        // 指定单元格垂直居中对齐
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 指定当单元格内容显示不下时自动换行
        cellStyle.setWrapText(true);

        // 设置单元格字体
        HSSFFont font = wb.createFont();
        font.setBold(true);
        font.setFontName("宋体");
        font.setColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());  //字体颜色
        font.setFontHeightInPoints((short) 10);
        cellStyle.setFont(font);

        // 设置边框样式
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);

        // 设置边框颜色
        cellStyle.setTopBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        cellStyle.setBottomBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        cellStyle.setLeftBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        cellStyle.setRightBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());

        return cellStyle;
    }

    /**
     * 创建报告分类巡检信息列表分类名称行样式
     *
     * @param wb
     * @return
     */
    private HSSFCellStyle createAIInfoTypeNameStyle(HSSFWorkbook wb) {
        HSSFCellStyle cellStyle = wb.createCellStyle();
        // 指定单元格内容居中对齐
        cellStyle.setAlignment(HorizontalAlignment.CENTER);

        // 指定单元格垂直居中对齐
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 指定当单元格内容显示不下时自动换行
        cellStyle.setWrapText(true);

        // 设置单元格字体
        HSSFFont font = wb.createFont();
        font.setBold(true);
        font.setFontName("宋体");
        font.setColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());  //字体颜色
        font.setFontHeightInPoints((short) 14);
        cellStyle.setFont(font);

        // 设置边框样式
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);

        // 设置边框颜色
        cellStyle.setTopBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        cellStyle.setBottomBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        cellStyle.setLeftBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        cellStyle.setRightBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());

        //单元格背景色
        cellStyle.setFillForegroundColor(IndexedColors.GREY_40_PERCENT.getIndex());
        //单元格填充效果
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        return cellStyle;
    }

    /**
     * 创建报告分类巡检信息列表表头行样式
     *
     * @param wb
     * @return
     */
    private HSSFCellStyle createAIInfoHeaderStyle(HSSFWorkbook wb) {
        HSSFCellStyle cellStyle = wb.createCellStyle();

        // 指定单元格内容居中对齐
        cellStyle.setAlignment(HorizontalAlignment.CENTER);

        // 指定单元格垂直居中对齐
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 指定当单元格内容显示不下时自动换行
        cellStyle.setWrapText(true);

        // 设置单元格字体
        HSSFFont font = wb.createFont();
        font.setBold(true);
        font.setFontName("宋体");
        font.setColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());  //字体颜色
        font.setFontHeightInPoints((short) 12);
        cellStyle.setFont(font);

        // 设置边框样式
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);

        // 设置边框颜色
        cellStyle.setTopBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        cellStyle.setBottomBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        cellStyle.setLeftBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        cellStyle.setRightBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());

        //单元格背景色
        cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        //单元格填充效果
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        return cellStyle;
    }

    /**
     * 创建报告分类巡检信息列表内容行样式
     *
     * @param wb
     * @return
     */
    private HSSFCellStyle createAIInfoContentStyle(HSSFWorkbook wb, int flag) {
        HSSFCellStyle cellStyle = wb.createCellStyle();


        // 指定单元格内容居中对齐
        cellStyle.setAlignment(HorizontalAlignment.CENTER);

        // 指定单元格垂直居中对齐
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 指定当单元格内容显示不下时自动换行
        cellStyle.setWrapText(true);

        // 设置单元格字体
        HSSFFont font = wb.createFont();
        font.setBold(true);
        font.setFontName("宋体");
        font.setColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());  //字体颜色
        font.setFontHeightInPoints((short) 10);
        cellStyle.setFont(font);

        // 设置边框样式
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);

        // 设置边框颜色
        cellStyle.setTopBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        cellStyle.setBottomBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        cellStyle.setLeftBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
        cellStyle.setRightBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());

        return cellStyle;
    }

    /**
     * 调整合并单元格后设置边框线
     *
     * @param sheet  Excel表单对象
     * @param region 合并单元格对象
     * @param cs     单元格样式
     */
    public static void setRegionStyle(HSSFSheet sheet, CellRangeAddress region,
                                      HSSFCellStyle cs) {

        for (int i = region.getFirstRow(); i <= region.getLastRow(); i++) {

            HSSFRow row = sheet.getRow(i);
            if (row == null)
                row = sheet.createRow(i);
            for (int j = region.getFirstColumn(); j <= region.getLastColumn(); j++) {
                HSSFCell cell = row.getCell(j);
                if (cell == null) {
                    cell = row.createCell(j);
                    cell.setCellValue("");
                }
                cell.setCellStyle(cs);

            }
        }
    }
}
