package com.yuanqiao.insight.accountbook.modules.connecttest.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.common.constant.CommonConstant;
import com.yuanqiao.insight.common.util.ipmi.IpmiInfo;
import com.yuanqiao.insight.common.util.jdbc.JdbcConnectInfo;
import com.yuanqiao.insight.common.util.jdbc.JdbcTestUtil;
import com.yuanqiao.insight.common.util.jmx.JmxInfo;
import com.yuanqiao.insight.common.util.jmx.JmxTestUtil;
import com.yuanqiao.insight.common.util.ping.PingInfo;
import com.yuanqiao.insight.common.util.ping.PingTestUtil;
import com.yuanqiao.insight.common.util.snmp.SnmpInfo;
import com.yuanqiao.insight.common.util.snmp.SnmpTestUtil;
import com.yuanqiao.insight.common.util.tcp.TcpInfo;
import com.yuanqiao.insight.common.util.tcp.TcpTestUtil;
import com.yuanqiao.insight.common.util.telnet.TelnetInfo;
import com.yuanqiao.insight.common.util.telnet.TelnetUtils;
import com.yuanqiao.insight.service.ipmi.IpmiControlService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.mq.RedisMq;
import org.jeecg.common.mq.stream.Streams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: ConnecTestController
 * @projectName jeecg-boot-parent
 * @description: TODO
 * @date 2021/3/13-14:28
 */
@Api(tags = "连接测试")
@RestController
@RequestMapping("/connect")
@Slf4j
public class ConnectTestController {

    @AutoLog(value = "测试工具-SNMP连接测试")
    @ApiOperation(value = "测试工具-SNMP连接测试", notes = "测试工具-SNMP连接测试")
    @GetMapping(value = "/testSnmp")
    public Result<?> snmpTest(SnmpInfo snmpInfo) {
        log.info("snmpInfo={}", snmpInfo);
        log.info("com.yuanqiao.insight.accountbook.modules.connecttest.controller.ConnectTestController.snmpTest(snmpInfo={})", snmpInfo);
        //SNMP V3 命令行测试
        //  snmpwalk -v 3 -u yuanqiao -l authPriv -a md5 -A yq123456 -x des -X 123456yq ************ ".*******.*******.0"
        Result<String> result = new Result<>();
        Object connectionStatus = SnmpTestUtil.getConnectionStatus(snmpInfo);
        result.setResult(connectionStatus);
        return result;
    }

    @AutoLog(value = "测试工具-TCP连接测试")
    @ApiOperation(value = "测试工具-TCP连接测试", notes = "测试工具-TCP连接测试")
    @GetMapping(value = "/testTcp")
    public Result<?> tcpTest(TcpInfo tcpInfo) {
        log.info("tcpInfo={}", tcpInfo);
        log.info("com.yuanqiao.insight.accountbook.modules.connecttest.controller.ConnectTestController.tcpTest(com.yuanqiao.insight.accountbook.modules.connecttest.entity.TcpInfo)(tcpInfo={})", tcpInfo);
        Result<String> result = new Result<>();
        String connectionStatus = TcpTestUtil.getConnectionStatus(tcpInfo);
        result.setResult(connectionStatus);
        return result;
    }

    @Autowired
    private RedisMq redisMq;

    @AutoLog(value = "测试工具-PING测试")
    @ApiOperation(value = "测试工具-PING测试", notes = "测试工具-PING测试")
    @GetMapping(value = "/testPing")
    public Result<?> tcpTest(PingInfo pingInfo, HttpServletRequest request, HttpServletResponse response) {
        log.info("pingInfo={}", pingInfo);
        log.info("com.yuanqiao.insight.accountbook.modules.connecttest.controller.ConnectTestController.tcpTest(pingInfo={})", pingInfo);
        InputStream is = null;
        InputStreamReader isr = null;
        BufferedReader br = null;
        String line = null;
        try {
            Process process = PingTestUtil.getConnectionStatus(pingInfo);
            is = process.getInputStream();
            isr = new InputStreamReader(is, "gbk");
            br = new BufferedReader(isr);
            while (null != (line = br.readLine())) {
                if (line.isEmpty()) {
                    continue;
                }
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("messageType", "pingResult");
                JSONObject dataJsonObject = new JSONObject();
                dataJsonObject.put("respondStr", line + " <br />");
                jsonObject.put("data", dataJsonObject);
                //WebSocket.sendAllMessage(jsonObject.toJSONString());
                redisMq.publish(Streams.WEBSOCKET_PUBLICIZE, jsonObject);
            }
            return Result.OK();
        } catch (IOException e) {
            return Result.error(e.getMessage());
        } finally {
            try {
                br.close();
                isr.close();
                is.close();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        /*Result<String> result = new Result<>();
        // 两个线程的线程池
        ExecutorService executor = Executors.newFixedThreadPool(2);
        //jdk1.8之前的实现方式
        CompletableFuture<String> future = CompletableFuture.supplyAsync(new Supplier<String>() {
            @Override
            public String get() {
                System.out.println("开始执行任务!");
                try {
                    //模拟耗时操作
                    PingTestUtil pingTestUtil = new PingTestUtil();
                    String connectionStatus = pingTestUtil.getConnectionStatus(pingInfo);
                    System.out.println("我是一个特别耗时的任务");
                    PrintWriter out=response.getWriter();
                    out.println(connectionStatus);
                    out.flush();
                    out.close();

                } catch (Exception e) {
                    e.printStackTrace();
                }
                return "耗时任务结束完毕!";
            }
        }, executor);

        //采用lambada的实现方式
        future.thenAccept(e -> System.out.println(e + " ok"));
        System.out.println("不等上面了，我先跑了");
        return result;*/
    }

    @AutoLog(value = "测试工具-JMX测试")
    @ApiOperation(value = "测试工具-JMX测试", notes = "测试工具-JMX测试")
    @GetMapping(value = "/testJmx")
    public Result<?> jmxTest(JmxInfo jmxInfo, HttpServletRequest request, HttpServletResponse response) {
        log.info("jmxInfo={}", jmxInfo);
        log.info("com.yuanqiao.insight.accountbook.modules.connecttest.controller.ConnectTestController.jmxTest(jmxInfo={})", jmxInfo);
        Result<String> result = new Result<>();
        String connectionStatus = JmxTestUtil.getConnectionStatus(jmxInfo);
        result.setResult(connectionStatus);
        return result;
    }

    @AutoLog(value = "测试工具-JDBC测试")
    @ApiOperation(value = "测试工具-JDBC测试", notes = "测试工具-JDBC测试")
    @GetMapping(value = "/testJdbc")
    public Result<?> jdbcTest(JdbcConnectInfo jdbcConnectInfo, HttpServletRequest request, HttpServletResponse response) {
        log.info("jdbcInfo={}", jdbcConnectInfo);
        log.info("com.yuanqiao.insight.accountbook.modules.connecttest.controller.ConnectTestController.jdbcTest(jdbcInfo={})", jdbcConnectInfo);
        Result<String> result = new Result<>();
        String connectionStatus = JdbcTestUtil.getConnectionStatus(jdbcConnectInfo);
        result.setResult(connectionStatus);
        return result;
    }

    @AutoLog(value = "测试工具-自定义sql执行")
    @ApiOperation(value = "测试工具-自定义sql执行", notes = "测试工具-自定义sql执行")
    @GetMapping(value = "/executeSql")
    public Result<?> executeSql(JdbcConnectInfo jdbcConnectInfo, HttpServletRequest request, HttpServletResponse response) {
        List<String> columnNameList = new ArrayList<>();
        JSONArray tableData = new JSONArray();
        String ip = jdbcConnectInfo.getIp();
//        String ip =  "***************";
        if (null != ip) {
            String port = jdbcConnectInfo.getPort();
            String type = jdbcConnectInfo.getType();
            String db = jdbcConnectInfo.getDb();
            String name = jdbcConnectInfo.getDbUName();
            String pwd = jdbcConnectInfo.getDbPwd();
            String sql = jdbcConnectInfo.getSql();
//            String port = "3306";
//            String type = "mysql";
//            String db = "insight-dev";
//            String name = "root";
//            String pwd = "123456";
//            String sql = "select * from momg_alarm_history";
            String url = "";
            String driver = "";
            //out.println(type);
            if ("oracle".equals(type)) {
                driver = CommonConstant.ORACLE_DRIVER;
                url = "jdbc:oracle:thin:@" + ip + ":" + port + "/" + db;
            } else if ("db2".equals(type)) {
                driver = CommonConstant.DB2_DRIVER;
                url = "jdbc:db2://" + ip + ":" + port + "/" + db;
            } else if ("mysql".equals(type)) {
                driver = CommonConstant.MYSQL_DRIVER;
                url = "jdbc:mysql://" + ip + ":" + port + "/" + db + "?socketTimeout=2000&serverTimezone=UTC";
            } else if ("sqlserver".equals(type)) {
                driver = CommonConstant.SQLSERVER_DRIVER;
                url = "jdbc:jtds:sqlserver://" + ip + ":" + port + ";DatabaseName=" + db;
            } else if ("sybase".equals(type)) {
                driver = CommonConstant.SYBASE_DRIVER;
                url = "jdbc:jtds:sybase://" + ip + ":" + port + ";DatabaseName=" + db;
            } else if ("kb".equals(type)) {
                driver = CommonConstant.KB_DRIVER;
                url = "jdbc:kingbase://" + ip + ":" + port + "/" + db;
            } else if ("kb8".equals(type)) {
                driver = CommonConstant.KB8_DRIVER;
                url = "jdbc:kingbase8://" + ip + ":" + port + "/" + db;
            } else if ("dm".equals(type)) {
                driver = CommonConstant.DM_DRIVER;
                url = "jdbc:dm://" + ip + ":" + port + "/" + db;
            } else if ("st".equals(type)) {
                driver = CommonConstant.ST_DRIVER;
                url = "jdbc:oscar://" + ip + ":" + port + "/" + db;
            } else if ("highgo".equals(type)) {
                driver = CommonConstant.HIGHGO_DRIVER;
                url = "jdbc:highgo://" + ip + ":" + port + "/" + db;
            } else if ("gbase".equals(type)) {
                driver = CommonConstant.GBASE_DRIVER;
                url = "jdbc:gbase://" + ip + ":" + port + "/" + db;
            }

            try {
                Class.forName(driver);//.newInstance();
                DriverManager.setLoginTimeout(1); //秒数
                Connection conn = DriverManager.getConnection(url, name, pwd);
                try {
                    PreparedStatement preparedStatement = conn.prepareStatement(sql);

                    if (sql.contains("select")) {
                        ResultSet resultSet = preparedStatement.executeQuery();
                        ResultSetMetaData metaData = resultSet.getMetaData();
                        // 表头
                        for (int i = 1; i <= metaData.getColumnCount(); i++) {
                            String columnName = metaData.getColumnName(i);
                            columnNameList.add(columnName);
                        }
                        // 获取表数据
                        while (resultSet.next()) {
                            JSONObject jsonObject = new JSONObject();
                            for (String tableHeadName : columnNameList) {
                                String data = resultSet.getString(tableHeadName);
                                jsonObject.put(tableHeadName, data);
                            }
                            tableData.add(jsonObject);
                        }
                        JSONObject reslutData = new JSONObject();
                        reslutData.put("tableHead", columnNameList);
                        reslutData.put("tableData", tableData);
                        return Result.OK(reslutData);
                    } else {
                        preparedStatement.execute();
                        int updateCount = preparedStatement.getUpdateCount();
                        if (updateCount > 0) {
                            return Result.error("修改成功");
                        }
                    }
                    preparedStatement.close();
                    conn.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    conn.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
                return Result.error("操作失败," + e.getMessage());
            }
        }
        return Result.OK("操作失败");
    }


    @AutoLog(value = "测试工具-ipmi测试连接返回数据")
    @ApiOperation(value = "测试工具-ipmi测试连接返回数据", notes = "测试工具-ipmi测试连接返回数据")
    @GetMapping(value = "/testIpmi")
    public Result<?> ipmiTest(IpmiInfo ipmiInfo, HttpServletRequest request, HttpServletResponse response) throws Exception {
        log.info("ipmiInfo={}", ipmiInfo);
        log.info("com.yuanqiao.insight.accountbook.modules.connecttest.controller.ConnectTestController.jdbcTest(jdbcInfo={})", ipmiInfo);
        Result<String> result = new Result<>();
        Map<String, Object> resMap = new IpmiControlService().getRespondByIPMIMap(ipmiInfo.getIp(), ipmiInfo.getUsername(), ipmiInfo.getPassword());
        String resStr = resMap.toString().replaceAll(",", "<br/>");
        result.setResult(resStr);
        return result;
    }

    @AutoLog(value = "测试工具-Telnet测试连接返回数据")
    @ApiOperation(value = "测试工具-Telnet测试连接返回数据", notes = "测试工具-Telnet测试连接返回数据")
    @GetMapping(value = "/testTelnet")
    public Result<?> telnetTest(TelnetInfo telnetInfo, HttpServletRequest request, HttpServletResponse response) throws Exception {
        log.info("telnetInfo={}", telnetInfo);
        log.info("com.yuanqiao.insight.accountbook.modules.connecttest.controller.ConnectTestController.testTelnet(telnetInfo={})", telnetInfo);
        Result<String> result = new Result<>();
        String respondStr = TelnetUtils.doTelnetExecute(telnetInfo).getString("resultMsg");
        result.setResult(respondStr);
        return result;
    }


}
