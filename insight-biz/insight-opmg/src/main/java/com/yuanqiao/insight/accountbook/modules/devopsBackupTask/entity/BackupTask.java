package com.yuanqiao.insight.accountbook.modules.devopsBackupTask.entity;

import com.yuanqiao.insight.accountbook.modules.devopsBackupScanTask.entity.DevopsBackupScanTask;
import com.yuanqiao.insight.accountbook.modules.devopsbackuppro.entity.DevopsBackupPro;
import com.yuanqiao.insight.accountbook.modules.devopsbackupresource.entity.DevopsBackupResource;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: BackupTask             备份任务类
 * @projectName jeecg-boot-parent
 * @description: TODO
 * @date 2021/5/24-15:54
 */
@Data
public class BackupTask {
    // 默认的文件路径
    private String aIfilePath;
    // 备份任务表
    private DevopsBackupTask devopsBackupTask;
    //数据资源源
    private DevopsBackupResource datasourceResource;
    //目的端资源
    private DevopsBackupResource datadestdResource;
    //备份策略表
    private DevopsBackupPro devopsBackupPro;
    //任务类型
    private String type;
    //扫描任务类
    private DevopsBackupScanTask devopsBackupScanTask;

}
