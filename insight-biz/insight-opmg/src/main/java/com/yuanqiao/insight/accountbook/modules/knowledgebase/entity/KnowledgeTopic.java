package com.yuanqiao.insight.accountbook.modules.knowledgebase.entity;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 知识库主题
 */
@Data
@Accessors(chain = true)
@ApiModel(value="knowledge_topic对象", description="知识库主题")
public class KnowledgeTopic implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@ApiModelProperty(value = "主键")
	private String id;
	/**主题名称*/
	@Excel(name = "主题名称", width = 15)
	@ApiModelProperty(value = "主题名称")
	private String topicName;
	/**主题编号*/
	@Excel(name = "主题编号", width = 15)
	@ApiModelProperty(value = "主题编号")
	private String topicCode;
	/**描述*/
	@Excel(name = "描述", width = 15)
	@ApiModelProperty(value = "描述")
	private String topicDescription;
	/**序号*/
	@ApiModelProperty(value = "序号")
	private Integer topicSerial;
	/**创建人*/
	@ApiModelProperty(value = "创建人")
	private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "创建日期")
	private java.util.Date createTime;
	/**更新人*/
	@ApiModelProperty(value = "更新人")
	private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "更新日期")
	private java.util.Date updateTime;
	/**
	 * 是否公开
	 */
	@Excel(name = "是否私有", width = 15)
	@ApiModelProperty(value = "是否公开")
	private String isPublic;//1 是，公开 0 部分可见
	/**主题权限*/
	@ApiModelProperty(value = "主题权限")
	private JSONObject authority;

	/**父级节点*/
	// @Excel(name = "父级节点", width = 15, dictTable ="cmdb_assets_category",dicText = "category_name",dicCode = "id")
	@ApiModelProperty(value = "父级节点")
	private String parentId;
	/**是否有子节点*/
//	@Excel(name = "是否有子节点", width = 15, dicCode = "yn")
	@ApiModelProperty(value = "是否有子节点（0否;1是）")
	private String isLeaf;

	private List<KnowledgeTopic> childs = new ArrayList<>();

	boolean userWithPermission;

	boolean canDisplay;

	KnowledgeTopic parent;

	//主题和流程的双向关联。一主题可以对多个流程
	private String processDefinitionKeys;
	private String processDefinitionNames;

//	//@Excel(name = "删除标识", width = 15)
//    @ApiModelProperty(value = "删除标识")
//    private Integer delflag;
//
//	//@Excel(name = "是否可监控标识（0不可监控;1可监控）", width = 15)
//	@ApiModelProperty(value = "是否可监控标识（0不可监控;1可监控）")
//	private Integer isMonitorable;
//
//	@TableField(exist = false)
//	private List<Product> children;
//
//	@TableField(exist = false)
//	private String parentName,planNumber;
//
//	@TableField(exist = false)
//	private List<KnowledgeTopic> childrenList;
//
//	@TableField(exist = false)
//	private JSONObject statData;
}
