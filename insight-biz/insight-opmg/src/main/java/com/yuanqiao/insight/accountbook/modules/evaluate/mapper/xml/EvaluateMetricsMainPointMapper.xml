<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.accountbook.modules.evaluate.mapper.EvaluateMetricsMainPointMapper">

    <!-- 根据指标ID查询要点列表 -->
    <select id="selectByMetricsId" resultType="com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateMetricsMainPoint">
        SELECT 
            id,
            metrics_id,
            point_name,
            field_data,
            create_by,
            create_time,
            update_by,
            update_time
        FROM devops_evaluate_metrics_main_point
        WHERE metrics_id = #{metricsId}
        ORDER BY create_time ASC
    </select>

    <!-- 根据指标ID删除要点 -->
    <delete id="deleteByMetricsId">
        DELETE FROM devops_evaluate_metrics_main_point
        WHERE metrics_id = #{metricsId}
    </delete>

</mapper>
