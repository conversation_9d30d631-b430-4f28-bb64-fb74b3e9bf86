package com.yuanqiao.insight.accountbook.modules.operatehistory.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.accountbook.modules.operatehistory.entity.OperateHistoryInfo;
import com.yuanqiao.insight.accountbook.modules.operatehistory.mapper.OperateHistoryInfoMapper;
import com.yuanqiao.insight.accountbook.modules.operatehistory.service.IOperateHistoryInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 操作历史表
 * @Author: jeecg-boot
 * @Date:   2021-03-11
 * @Version: V1.0
 */
@Slf4j
@Service
public class OperateHistoryInfoServiceImpl extends ServiceImpl<OperateHistoryInfoMapper, OperateHistoryInfo> implements IOperateHistoryInfoService {
    @Override
    public List<OperateHistoryInfo> getListsByBusinessId(String businssId) {
        log.info("businssId={}",businssId);
        log.info("com.yuanqiao.insight.accountbook.modules.operatehistory.service.impl.OperateHistoryInfoServiceImpl.getListsByBusinessId(businssId={})",businssId);
        return this.baseMapper.getListsByBusinessId(businssId);
    }

    @Override
    public List<OperateHistoryInfo> getListsByCreateTime(String startTime, String endTime) {
        return this.baseMapper.getListsByCreateTime(startTime,endTime);
    }
}
