package com.yuanqiao.insight.accountbook.modules.autoInspection.generater;

import com.yuanqiao.insight.accountbook.modules.autoInspection.entity.DevopsAutoInspection;
import com.yuanqiao.insight.accountbook.modules.autoInspectionReport.service.IDevopsAutoInspectionReportService;
import com.yuanqiao.insight.accountbook.modules.autoInspectionReport.service.impl.DevopsAutoInspectionReportServiceImpl;
import com.yuanqiao.insight.acore.utils.SpringContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.DateUtils;
import org.jeecg.modules.system.entity.SysUsers;
import org.jeecg.modules.system.service.ISysUserService;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: APPAIRGenerater          定义应用自动巡检报告生成组件
 * @projectName jeecg-boot-parent
 * @description: TODO
 * @date 2021/5/19-9:42
 */
@Slf4j
public class APPAIRGenerater implements AIRGenerater {
    private final IDevopsAutoInspectionReportService devopsAutoInspectionReportService;
    private final ISysUserService sysUserService;

    public APPAIRGenerater() {
        this.devopsAutoInspectionReportService = SpringContextUtil.getApplicationContext().getBean(DevopsAutoInspectionReportServiceImpl.class);
        this.sysUserService = SpringContextUtil.getApplicationContext().getBean(ISysUserService.class);
    }

    /**
     * 生成报告
     *
     * @param autoInspection 自动巡检任务类
     * @param aITask
     * @throws
     */
    @Override
    public void report(DevopsAutoInspection autoInspection, AITask aITask) throws Exception {
        final SysUsers user = sysUserService.getUserByName(autoInspection.getCreateBy());
        if(user!=null){
            autoInspection.setCreateByName(user.getRealname());
        }
        String tempStr1 = DateUtils.formatDateHMS(autoInspection.getTaskStartTime()).replaceAll(":", "");
        String tempStr2 = tempStr1.replaceAll("-", "");
        String fileNameSuffix = tempStr2.replaceAll(" ", "");
        String filePath = aITask.getPath() + aITask.getAIfilePath() + "/";
        //chart图片保存地址
        File file = new File(filePath);
        if (!file.isDirectory()) {
            file.mkdirs();
        }
        //文件储存位置
        filePath += autoInspection.getTaskName() + "_" + fileNameSuffix;
        //存放图片保存路径
        Map<String, Map<String, Map<String, String>>> photoPath = new HashMap<>();

        //应用生成报告util
        APPAIRGenetaterUtil appairGenetaterUtil = new APPAIRGenetaterUtil();
        appairGenetaterUtil.createPDF(aITask, autoInspection, filePath + ".pdf", photoPath);

        //添加报表
        String[] filePaths = new String[1];
        filePaths[0] = filePath + ".pdf";
        devopsAutoInspectionReportService.addDevopsAutoInspectionReport(autoInspection, filePaths);
    }
}
