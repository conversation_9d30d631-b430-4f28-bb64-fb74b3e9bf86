package com.yuanqiao.insight.accountbook.modules.orderinfo.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
public class TodoOrder implements Serializable {

    private String id;
    private String todoName;
    //0 工单 1 值班打卡
    private String todoType;
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date todoDate;
}
