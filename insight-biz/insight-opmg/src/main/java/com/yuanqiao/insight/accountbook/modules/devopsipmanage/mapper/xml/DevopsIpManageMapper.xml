<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.accountbook.modules.devopsipmanage.mapper.DevopsIpManageMapper">

    <select id="queryListByIp" resultType="com.yuanqiao.insight.accountbook.modules.devopsipmanage.entity.DevopsIpManage">
        select * from devops_ip_manage where ip_address = #{ip}
    </select>

    <select id="queryListByIps" resultType="com.yuanqiao.insight.accountbook.modules.devopsipmanage.entity.DevopsIpManage">
        select * from devops_ip_manage where ip_address in
        <foreach collection="ips" item="ip" open="(" separator="," close=")">
            #{ip}
        </foreach>
    </select>

    <select id="queryListByMac" resultType="com.yuanqiao.insight.accountbook.modules.devopsipmanage.entity.DevopsIpManage">
        select * from devops_ip_manage where mac_code = #{mac}
    </select>

    <select id="queryListByIpAndMac" resultType="com.yuanqiao.insight.accountbook.modules.devopsipmanage.entity.DevopsIpManage">
        select * from devops_ip_manage where ip_address = #{ip} and mac_code = #{mac}
    </select>


    <select id="findRealnames" resultType="string">

        select realname from  sys_users where  username in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>
</mapper>