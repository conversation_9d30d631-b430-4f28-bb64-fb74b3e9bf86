<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.accountbook.modules.autoInspection.mapper.DevopsAutoInspectionDeviceMapper">

    <select id="getDeviceByTaskId"
            resultType="com.yuanqiao.insight.accountbook.modules.autoInspection.entity.DevopsAutoInspectionDevice">
        SELECT *
        FROM devops_auto_inspection_device
        where task_id = #{taskId}
    </select>
    <select id="getProduct" resultType="com.yuanqiao.insight.service.product.entity.Product">
        SELECT mp.name, mp.display_name
        FROM momg_device_info mdi
                 LEFT JOIN momg_product mp ON mdi.product_id = mp.id
        WHERE mdi.id = #{s}
    </select>
    <select id="getProductName" resultType="java.lang.String">
        SELECT display_name
        FROM momg_product
        WHERE name = #{key};
    </select>

    <delete id="delBatchDevops">
        delete FROM devops_auto_inspection_device where task_id = #{taskId}
        <if test='deviceIds.size() > 0 and deviceIds != null'>
            and device_id in
            <foreach collection="deviceIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </delete>
</mapper>
