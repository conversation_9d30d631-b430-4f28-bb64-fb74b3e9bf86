package com.yuanqiao.insight.accountbook.modules.orderinfo.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.accountbook.modules.arrangementshift.entity.TodoArrangement;
import com.yuanqiao.insight.accountbook.modules.arrangementshift.service.IDevopsArrangementShiftService;
import com.yuanqiao.insight.accountbook.modules.evaluate.docGenerate.FileGetUtil;
import com.yuanqiao.insight.accountbook.modules.operatehistory.entity.OperateHistoryInfo;
import com.yuanqiao.insight.accountbook.modules.operatehistory.service.IOperateHistoryInfoService;
import com.yuanqiao.insight.accountbook.modules.orderinfo.entity.DevopsOrderInfo;
import com.yuanqiao.insight.accountbook.modules.orderinfo.entity.TodoOrder;
import com.yuanqiao.insight.accountbook.modules.orderinfo.service.IDevopsOrderInfoService;
import com.yuanqiao.insight.acore.utils.SpringContextUtil;
import com.yuanqiao.insight.common.constant.CommonConstant;
import com.yuanqiao.insight.common.util.common.TimeUtils;
import com.yuanqiao.insight.monitoring.modules.deviceStatis.momgDeptStatis.service.impl.MomgDeptStatisServiceImpl;
import com.yuanqiao.insight.service.momgDeviceStatis.mapper.MomgDeviceStatisMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.common.system.vo.LoginUser;
import com.yuanqiao.insight.acore.system.service.ISysDictService;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 运维工单详情表
 * @Author: jeecg-boot
 * @Date: 2021-03-11
 * @Version: V1.0
 */
@Api(tags = "运维工单详情表")
@RestController
@RequestMapping("/orderinfo/devopsOrderInfo")
@Slf4j
public class DevopsOrderInfoController extends JeecgController<DevopsOrderInfo, IDevopsOrderInfoService> {
    @Autowired
    private IDevopsOrderInfoService devopsOrderInfoService;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private IOperateHistoryInfoService operateHistoryInfoService;
    @Autowired
    private ISysDictService dictService;
    @Autowired
    private IDevopsArrangementShiftService shiftService;

    /**
     * 分页列表查询
     *
     * @param devopsOrderInfo
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "待分配工单-分页列表查询")
    @ApiOperation(value = "运维工单详情表-未分配分页列表查询", notes = "运维工单详情表-未分配分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(DevopsOrderInfo devopsOrderInfo,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        log.info("devopsOrderInfo={}", devopsOrderInfo);
        log.info("com.yuanqiao.insight.accountbook.modules.orderinfo.controller.DevopsOrderInfoController.queryPageList(devopsOrderInfo={})", devopsOrderInfo);
        Result<IPage<DevopsOrderInfo>> result = new Result();
        TimeUtils timeUtils = new TimeUtils();
        String orderName = devopsOrderInfo.getOrderName();
        if (StringUtils.isNotBlank(orderName)) {
            devopsOrderInfo.setOrderName(null);
        }
        QueryWrapper<DevopsOrderInfo> queryWrapper = QueryGenerator.initQueryWrapper(devopsOrderInfo, req.getParameterMap());
        if (StringUtils.isNotBlank(orderName)) {
            queryWrapper.like("order_name", orderName);
        }
        String startTime = devopsOrderInfo.getStartTime();
        String endTime = devopsOrderInfo.getEndTime();
        if (startTime != null && endTime != null) {
            queryWrapper.between("create_time", startTime, endTime);
        }
        queryWrapper.eq("order_state", CommonConstant.DEVOPS_ORDER_INFO_STATE_0);
        Page<DevopsOrderInfo> page = new Page<DevopsOrderInfo>(pageNo, pageSize);
        IPage<DevopsOrderInfo> pageList = devopsOrderInfoService.page(page, queryWrapper);
        //以上获取工单详情表原始数据

        //以下获得 确认人姓名
        if (null != pageList.getRecords() && pageList.getRecords().size() > 0) {
            //资产类型名称
            List<DictModel> dictModels = dictService.queryDictItemsByCode("fault_type");
            Map<String, String> assetsCategoryMap = dictModels.stream().collect(Collectors.toMap(DictModel::getValue, DictModel::getText));
            Map<String, String> userMap = userService.getUserMap();

            pageList.getRecords().forEach(item -> {
                item.setConfirmUserName(userMap.get(item.getConfirmUserId()));
                item.setOrderCategoryText(assetsCategoryMap.get(item.getOrderCategoryId()));
                item.setOrderStateName(CommonConstant.DEVOPS_ORDER_INFO_STATE_CH0);
                if (CommonConstant.DEVOPS_ORDER_INFO_SOURCE_1.equals(item.getOrderSource())) {
                    item.setOrderSourceText(CommonConstant.DEVOPS_ORDER_INFO_SOURCE_TEXT_1);
                } else if (CommonConstant.DEVOPS_ORDER_INFO_SOURCE_2.equals(item.getOrderSource())) {
                    item.setOrderSourceText(CommonConstant.DEVOPS_ORDER_INFO_SOURCE_TEXT_2);
                }
                if (item.getResponseSecond() != null) {
                    String response = timeUtils.formatTime(item.getResponseSecond() * 1000);//响应时间秒
                    item.setResponseSecondStr(response);
                }
                if (item.getHandleSecond() != null) {
                    String handle = timeUtils.formatTime(item.getHandleSecond() * 1000);//处理时间秒
                    item.setHandleSecondStr(handle);
                }

            });
        }
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    @AutoLog(value = "待处理工单-分页列表查询")
    @ApiOperation(value = "运维工单详情表-已分配分页列表查询", notes = "运维工单详情表-已分配分页列表查询")
    @GetMapping(value = "/listPend")
    public Result<?> queryPageList1(DevopsOrderInfo devopsOrderInfo,
                                    @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                    @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                    HttpServletRequest req) {
        log.info("devopsOrderInfo={}", devopsOrderInfo);
        log.info("com.yuanqiao.insight.accountbook.modules.orderinfo.controller.DevopsOrderInfoController.queryPageList1(devopsOrderInfo={})", devopsOrderInfo);
        //获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Result<IPage<DevopsOrderInfo>> result = new Result();
        TimeUtils timeUtils = new TimeUtils();
        String orderName = devopsOrderInfo.getOrderName();
        if (StringUtils.isNotBlank(orderName)) {
            devopsOrderInfo.setOrderName(null);
        }
        QueryWrapper<DevopsOrderInfo> queryWrapper = QueryGenerator.initQueryWrapper(devopsOrderInfo, req.getParameterMap());
        if (StringUtils.isNotBlank(orderName)) {
            queryWrapper.like("order_name", orderName);
        }
        String startTime = devopsOrderInfo.getStartTime();
        String endTime = devopsOrderInfo.getEndTime();
        if (startTime != null && endTime != null) {
            queryWrapper.between("alloc_time", startTime, endTime);
        }
        queryWrapper.eq("order_state", CommonConstant.DEVOPS_ORDER_INFO_STATE_1);
        queryWrapper.eq("handler_user_id", sysUser.getUsername());
        Page<DevopsOrderInfo> page = new Page<DevopsOrderInfo>(pageNo, pageSize);
        IPage<DevopsOrderInfo> pageList = devopsOrderInfoService.page(page, queryWrapper);
        //以上获取工单详情表原始数据

        //以下获得 确认人姓名 负责人姓名
        if (null != pageList.getRecords() && pageList.getRecords().size() > 0) {
            //资产类型名称
            //资产类型名称
            List<DictModel> dictModels = dictService.queryDictItemsByCode("fault_type");
            Map<String, String> assetsCategoryMap = dictModels.stream().collect(Collectors.toMap(DictModel::getValue, DictModel::getText));
            Map<String, String> userMap = userService.selectRealnameByUsername();
            Map<String, String> userMap1 = userService.getUserMap();
            pageList.getRecords().forEach(item -> {
                item.setConfirmUserName(userMap1.get(item.getConfirmUserId()));
                item.setOrderCategoryText(assetsCategoryMap.get(item.getOrderCategoryId()));
                item.setHandlerUserName(userMap.get(item.getHandlerUserId()));
                item.setOrderStateName(CommonConstant.DEVOPS_ORDER_INFO_STATE_CH1);
                if (CommonConstant.DEVOPS_ORDER_INFO_SOURCE_1.equals(item.getOrderSource())) {
                    item.setOrderSourceText(CommonConstant.DEVOPS_ORDER_INFO_SOURCE_TEXT_1);
                } else if (CommonConstant.DEVOPS_ORDER_INFO_SOURCE_2.equals(item.getOrderSource())) {
                    item.setOrderSourceText(CommonConstant.DEVOPS_ORDER_INFO_SOURCE_TEXT_2);
                }
                if (item.getResponseSecond() != null) {
                    String response = timeUtils.formatTime(item.getResponseSecond() * 1000);//响应时间秒
                    item.setResponseSecondStr(response);
                }
                if (item.getHandleSecond() != null) {
                    String handle = timeUtils.formatTime(item.getHandleSecond() * 1000);//处理时间秒
                    item.setHandleSecondStr(handle);
                }
            });
        }

        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    @AutoLog(value = "已处理工单-分页列表查询")
    @ApiOperation(value = "运维工单详情表-已处理分页列表查询", notes = "运维工单详情表-已处理分页列表查询")
    @GetMapping(value = "/listProcessed")
    public Result<?> queryPageList2(DevopsOrderInfo devopsOrderInfo,
                                    @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                    @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                    HttpServletRequest req) {
        log.info("devopsOrderInfo={}", devopsOrderInfo);
        log.info("com.yuanqiao.insight.accountbook.modules.orderinfo.controller.DevopsOrderInfoController.queryPageList2(devopsOrderInfo={})", devopsOrderInfo);
        Result<IPage<DevopsOrderInfo>> result = new Result();
        TimeUtils timeUtils = new TimeUtils();
        String orderName = devopsOrderInfo.getOrderName();
        if (StringUtils.isNotBlank(orderName)) {
            devopsOrderInfo.setOrderName(null);
        }
        QueryWrapper<DevopsOrderInfo> queryWrapper = QueryGenerator.initQueryWrapper(devopsOrderInfo, req.getParameterMap());
        if (StringUtils.isNotBlank(orderName)) {
            queryWrapper.like("order_name", orderName);
        }
        String startTime = devopsOrderInfo.getStartTime();
        String endTime = devopsOrderInfo.getEndTime();
        if (startTime != null && endTime != null) {
            queryWrapper.between("alloc_time", startTime, endTime);
        }
        queryWrapper.eq("order_state", CommonConstant.DEVOPS_ORDER_INFO_STATE_2);
        Page<DevopsOrderInfo> page = new Page<DevopsOrderInfo>(pageNo, pageSize);
        IPage<DevopsOrderInfo> pageList = devopsOrderInfoService.page(page, queryWrapper);
        //以上获取工单详情表原始数据

        //以下获得 确认人姓名 负责人姓名
        if (null != pageList.getRecords() && pageList.getRecords().size() > 0) {
            //资产类型名称
            List<DictModel> dictModels = dictService.queryDictItemsByCode("fault_type");
            Map<String, String> assetsCategoryMap = dictModels.stream().collect(Collectors.toMap(DictModel::getValue, DictModel::getText));
            Map<String, String> userMap = userService.selectRealnameByUsername();
            Map<String, String> userMap1 = userService.getUserMap();
            pageList.getRecords().forEach(item -> {
                item.setConfirmUserName(userMap1.get(item.getConfirmUserId()));
                item.setHandlerUserName(userMap.get(item.getHandlerUserId()));
                item.setOrderCategoryText(assetsCategoryMap.get(item.getOrderCategoryId()));
                item.setOrderStateName(CommonConstant.DEVOPS_ORDER_INFO_STATE_CH2);
                if (CommonConstant.DEVOPS_ORDER_INFO_SOURCE_1.equals(item.getOrderSource())) {
                    item.setOrderSourceText(CommonConstant.DEVOPS_ORDER_INFO_SOURCE_TEXT_1);
                } else if (CommonConstant.DEVOPS_ORDER_INFO_SOURCE_2.equals(item.getOrderSource())) {
                    item.setOrderSourceText(CommonConstant.DEVOPS_ORDER_INFO_SOURCE_TEXT_2);
                }
                if (item.getResponseSecond() != null) {
                    String response = timeUtils.formatTime(item.getResponseSecond() * 1000);//响应时间秒
                    item.setResponseSecondStr(response);
                }
                if (item.getHandleSecond() != null) {
                    String handle = timeUtils.formatTime(item.getHandleSecond() * 1000);//处理时间秒
                    item.setHandleSecondStr(handle);
                }
            });
        }

        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }


    /**
     * 添加
     *
     * @param devopsOrderInfo
     * @return
     */
    @AutoLog(value = "待分配工单-添加")
    @ApiOperation(value = "运维工单详情表-添加", notes = "运维工单详情表-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody DevopsOrderInfo devopsOrderInfo) {
        log.info("devopsOrderInfo={}", devopsOrderInfo);
        log.info("com.yuanqiao.insight.accountbook.modules.orderinfo.controller.DevopsOrderInfoController.add(devopsOrderInfo={})", devopsOrderInfo);
        //获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        devopsOrderInfo.setOrderState(CommonConstant.DEVOPS_ORDER_INFO_STATE_0);
        devopsOrderInfo.setOrderSource(CommonConstant.DEVOPS_ORDER_INFO_SOURCE_1);
        devopsOrderInfoService.save(devopsOrderInfo);
        //添加历史
        OperateHistoryInfo operateHistoryInfo = new OperateHistoryInfo();
        operateHistoryInfo.setBusinessId(devopsOrderInfo.getId());
        operateHistoryInfo.setCreateTime(new Date());
        operateHistoryInfo.setOperateUserId(sysUser.getUsername());
        operateHistoryInfo.setOperateContent("创建工单");
        operateHistoryInfo.setOperateType("add");
        operateHistoryInfoService.save(operateHistoryInfo);
        return Result.OK("添加成功！");
    }

    @AutoLog(value = "运维工单详情表-获取工单操作历史")
    @ApiOperation(value = "运维工单详情表-获取工单操作历史", notes = "运维工单详情表-获取工单操作历史")
    @GetMapping(value = "/getOrderHistory")
    public Result<?> getOrderHistory(String orderId) {
        log.info("orderId={}", orderId);
        log.info("com.yuanqiao.insight.accountbook.modules.orderinfo.controller.DevopsOrderInfoController.getOrderHistory(orderId={})", orderId);
        if (StringUtils.isBlank(orderId)) {
            return Result.error("工单id不能为空！");
        }
        List<OperateHistoryInfo> lists = operateHistoryInfoService.getListsByBusinessId(orderId);
        return Result.OK(lists);
    }

    /**
     * 编辑
     *
     * @param devopsOrderInfo
     * @return
     */
    @AutoLog(value = "运维工单详情表-编辑")
    @ApiOperation(value = "运维工单详情表-编辑", notes = "运维工单详情表-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody DevopsOrderInfo devopsOrderInfo) {
        log.info("devopsOrderInfo={}", devopsOrderInfo);
        log.info("com.yuanqiao.insight.accountbook.modules.orderinfo.controller.DevopsOrderInfoController.edit(devopsOrderInfo={})", devopsOrderInfo);
        devopsOrderInfoService.updateById(devopsOrderInfo);
        return Result.OK("编辑成功!");
    }


    @AutoLog(value = "运维工单-添加知识库")
    @ApiOperation(value = "运维工单详情表-批量添加工单", notes = "运维工单详情表-批量添加工单")
    @PostMapping(value = "/goAddKnowledges")
    public Result<?> goAddKnowledges(@RequestBody String ids) {
        log.info("ids={}", ids);
        log.info("com.yuanqiao.insight.accountbook.modules.orderinfo.controller.DevopsOrderInfoController.goAddKnowledges(ids={})", ids);
        Map json = (Map) JSONObject.parse(ids);
        devopsOrderInfoService.doAddKnowledges(json.get("ids").toString());

        return Result.OK("添加工单成功!");
    }

    @AutoLog(value = "待分配工单-工单分配")
    @ApiOperation(value = "运维工单详情表-工单分配", notes = "运维工单详情表-工单分配")
    @PutMapping(value = "/distributionEdit")
    public Result<?> distributionEdit(@RequestBody DevopsOrderInfo devopsOrderInfo) {
        log.info("devopsOrderInfo={}", devopsOrderInfo);
        log.info("com.yuanqiao.insight.accountbook.modules.orderinfo.controller.DevopsOrderInfoController.distributionEdit(devopsOrderInfo={})", devopsOrderInfo);
        devopsOrderInfoService.distributionEdit(devopsOrderInfo);
        return Result.OK("编辑成功!");
    }


    @AutoLog(value = "待处理工单-工单处理")
    @ApiOperation(value = "运维工单详情表-工单分配", notes = "运维工单详情表-工单分配")
    @PutMapping(value = "/handleEdit")
    public Result<?> handleEdit(@RequestBody DevopsOrderInfo devopsOrderInfo) {
        log.info("devopsOrderInfo={}", devopsOrderInfo);
        log.info("com.yuanqiao.insight.accountbook.modules.orderinfo.controller.DevopsOrderInfoController.handleEdit(devopsOrderInfo={})", devopsOrderInfo);
        devopsOrderInfoService.handleEdit(devopsOrderInfo);
        return Result.OK("编辑成功!");
    }


    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "运维工单-通过id删除")
    @ApiOperation(value = "运维工单详情表-通过id删除", notes = "运维工单详情表-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        log.info("id={}", id);
        log.info("com.yuanqiao.insight.accountbook.modules.orderinfo.controller.DevopsOrderInfoController.delete(id={})", id);
        devopsOrderInfoService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "运维工单-批量删除")
    @ApiOperation(value = "运维工单详情表-批量删除", notes = "运维工单详情表-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        log.info("ids={}", ids);
        log.info("com.yuanqiao.insight.accountbook.modules.orderinfo.controller.DevopsOrderInfoController.deleteBatch(ids={})", ids);
        this.devopsOrderInfoService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "运维工单详情表-通过id查询")
    @ApiOperation(value = "运维工单详情表-通过id查询", notes = "运维工单详情表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        log.info("id={}", id);
        log.info("com.yuanqiao.insight.accountbook.modules.orderinfo.controller.DevopsOrderInfoController.queryById(id={})", id);
        DevopsOrderInfo item = devopsOrderInfoService.getById(id);
        if (item == null) {
            return Result.error("未找到对应数据");
        }
        TimeUtils timeUtils = new TimeUtils();
        //资产类型名称
        List<DictModel> dictModels = dictService.queryDictItemsByCode("fault_type");
        Map<String, String> assetsCategoryMap = dictModels.stream().collect(Collectors.toMap(DictModel::getValue, DictModel::getText));
        Map<String, String> userMap = userService.selectRealnameByUsername();
        Map<String, String> userMap1 = userService.getUserMap();

        item.setConfirmUserName(userMap1.get(item.getConfirmUserId()));
        item.setHandlerUserName(userMap.get(item.getHandlerUserId()));
        item.setOrderCategoryText(assetsCategoryMap.get(item.getOrderCategoryId()));
        item.setOrderStateName(CommonConstant.DEVOPS_ORDER_INFO_STATE_CH2);
        if (CommonConstant.DEVOPS_ORDER_INFO_SOURCE_1.equals(item.getOrderSource())) {
            item.setOrderSourceText(CommonConstant.DEVOPS_ORDER_INFO_SOURCE_TEXT_1);
        } else if (CommonConstant.DEVOPS_ORDER_INFO_SOURCE_2.equals(item.getOrderSource())) {
            item.setOrderSourceText(CommonConstant.DEVOPS_ORDER_INFO_SOURCE_TEXT_2);
        }
        if (item.getResponseSecond() != null) {
            String response = timeUtils.formatTime(item.getResponseSecond() * 1000);//响应时间秒
            item.setResponseSecondStr(response);
        }
        if (item.getHandleSecond() != null) {
            String handle = timeUtils.formatTime(item.getHandleSecond() * 1000);//处理时间秒
            item.setHandleSecondStr(handle);
        }
        return Result.OK(item);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param devopsOrderInfo
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DevopsOrderInfo devopsOrderInfo) {
        log.info("com.yuanqiao.insight.accountbook.modules.orderinfo.controller.DevopsOrderInfoController.exportXls####");
        return super.exportXls(request, devopsOrderInfo, DevopsOrderInfo.class, "运维工单详情表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        log.info("com.yuanqiao.insight.accountbook.modules.orderinfo.controller.DevopsOrderInfoController.importExcel####");
        return super.importExcel(request, response, DevopsOrderInfo.class);
    }


    /**
     * app待办
     *
     * @param
     * @return
     */
    @GetMapping(value = "/getTodoUser")
    public Result<?> getTodoUser() {
        List<TodoOrder> todoOrders = new ArrayList<>();
        //获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String username = sysUser.getUsername();
        String userId = sysUser.getId();
        setDevopsOrder(todoOrders, username);
        setArrangement(todoOrders, userId);
        List<TodoOrder> collect = todoOrders.stream().sorted(Comparator.comparing(TodoOrder::getTodoDate).reversed().thenComparing(TodoOrder::getTodoDate)).collect(Collectors.toList());

        return Result.OK(collect);
    }

    private void setArrangement(List<TodoOrder> todoOrders, String userId) {
        //添加打卡提醒
        Date date = new Date();//请求接口时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<TodoArrangement> todoArrangements = shiftService.getTodoArrangement(userId, 0);
        arrangement(todoOrders, date, sdf, todoArrangements, "上班");
        //下班提醒
        List<TodoArrangement> todoArrangement = shiftService.getTodoArrangement(userId, 1);
        arrangement(todoOrders, date, sdf, todoArrangement, "下班");
    }

    private void arrangement(List<TodoOrder> todoOrders, Date date, SimpleDateFormat sdf, List<TodoArrangement> todoArrangements, String s) {
        for (TodoArrangement todoArrangement : todoArrangements) {
            Date arrangementDate = todoArrangement.getArrangementDate();
            String format = sdf.format(arrangementDate);
            String[] split = format.split("\\s+");
            String startTime = null;
            if (s.equals("上班")) {
                startTime = todoArrangement.getStartTime() + ":00";
            } else {
                startTime = todoArrangement.getEndTime() + ":00";
            }
            String time = split[0] + " " + startTime;
            Date date1 = null;
            try {
                date1 = (Date) sdf.parse(time);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            Date date2 = subtractTime(date1, 600000);
            //判断时间范围
            long dateTime = date.getTime();
            if (dateTime >= date2.getTime() && dateTime <= date1.getTime()) {
                TodoOrder todoOrder = new TodoOrder();
                if (s.equals("上班")) {
                    todoOrder.setTodoName("值班提醒 :" + todoArrangement.getArrangementName() + todoArrangement.getStartTime() + s + "打卡");
                } else {
                    todoOrder.setTodoName("值班提醒 :" + todoArrangement.getArrangementName() + todoArrangement.getEndTime() + s + "打卡");
                }
                todoOrder.setId(todoArrangement.getId());
                todoOrder.setTodoType("1");
                todoOrder.setTodoDate(date2);
                todoOrders.add(todoOrder);
            }
        }
    }

    private void setDevopsOrder(List<TodoOrder> todoOrders, String username) {
        //添加工单待办
        List<DevopsOrderInfo> devopsOrderInfos = devopsOrderInfoService.getTodoUser(username);
        for (DevopsOrderInfo devopsOrderInfo : devopsOrderInfos) {
            String orderName = devopsOrderInfo.getOrderName();
            Date updateTime = devopsOrderInfo.getAllocTime();
            TodoOrder todoOrder = new TodoOrder();
            todoOrder.setTodoName("工单提醒 :工单" + orderName + "待处理");
            todoOrder.setTodoDate(updateTime);
            todoOrder.setId(devopsOrderInfo.getId());
            todoOrder.setTodoType("0");
            todoOrders.add(todoOrder);
        }
    }


    /**
     * 加减对应时间后的日期
     *
     * @param date   需要加减时间的日期
     * @param amount 加减的时间(毫秒)
     * @return 加减对应时间后的日期
     */
    private Date subtractTime(Date date, int amount) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String strTime = sdf.format(date.getTime() - amount);
            Date time = sdf.parse(strTime);
            return time;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}
