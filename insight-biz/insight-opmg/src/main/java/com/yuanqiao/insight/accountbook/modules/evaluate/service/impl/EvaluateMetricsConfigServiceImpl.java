package com.yuanqiao.insight.accountbook.modules.evaluate.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateMetricsConfig;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateMetricsMainPoint;
import com.yuanqiao.insight.accountbook.modules.evaluate.mapper.EvaluateMetricsConfigMapper;
import com.yuanqiao.insight.accountbook.modules.evaluate.service.IEvaluateMetricsConfigService;
import com.yuanqiao.insight.accountbook.modules.evaluate.service.IEvaluateMetricsMainPointService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.UUIDGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 * @ClassName EvaluateMetricsConfigServiceImpl
 * @description: 评估指标配置服务实现类
 * @datetime 2025年 07月 28日
 * @version: 1.0
 */
@Service
@Slf4j
public class EvaluateMetricsConfigServiceImpl extends ServiceImpl<EvaluateMetricsConfigMapper, EvaluateMetricsConfig> implements IEvaluateMetricsConfigService {

    @Autowired
    private IEvaluateMetricsMainPointService mainPointService;

    // 配置类型常量
    private static final String CONFIG_TYPE_FIELD = "1"; // 指标字段
    private static final String CONFIG_TYPE_RULE = "2";  // 规则配置

    @Override
    @Transactional
    public boolean saveFieldConfig(String metricsId, String json) {
        return saveConfigByType(metricsId, CONFIG_TYPE_FIELD, json);
    }

    @Override
    @Transactional
    public boolean saveRuleConfig(String metricsId, String json) {
        return saveConfigByType(metricsId, CONFIG_TYPE_RULE, json);
    }

    /**
     * 根据类型保存配置
     */
    private boolean saveConfigByType(String metricsId, String configType, String json) {
        try {
            // 检查是否已存在配置
            EvaluateMetricsConfig existConfig = baseMapper.selectByMetricsIdAndType(metricsId, configType);
            
            if (existConfig != null) {
                // 更新现有配置
                existConfig.setJson(json);
                existConfig.setUpdateTime(new Date());
                return this.updateById(existConfig);
            } else {
                // 创建新配置
                EvaluateMetricsConfig newConfig = new EvaluateMetricsConfig();
                newConfig.setId(UUIDGenerator.generate());
                newConfig.setMetricsId(metricsId);
                newConfig.setConfigType(configType);
                newConfig.setJson(json);
                newConfig.setCreateTime(new Date());
                return this.save(newConfig);
            }
        } catch (Exception e) {
            log.error("保存指标配置失败，metricsId: {}, configType: {}", metricsId, configType, e);
            return false;
        }
    }

    @Override
    public List<EvaluateMetricsConfig> getByMetricsId(String metricsId) {
        return baseMapper.selectByMetricsId(metricsId);
    }

    @Override
    public EvaluateMetricsConfig getByMetricsIdAndType(String metricsId, String configType) {
        return baseMapper.selectByMetricsIdAndType(metricsId, configType);
    }

    @Override
    public String getFieldConfigJson(String metricsId) {
        EvaluateMetricsConfig config = baseMapper.selectByMetricsIdAndType(metricsId, CONFIG_TYPE_FIELD);
        return config != null ? config.getJson() : null;
    }

    @Override
    public String getRuleConfigJson(String metricsId) {
        EvaluateMetricsConfig config = baseMapper.selectByMetricsIdAndType(metricsId, CONFIG_TYPE_RULE);
        return config != null ? config.getJson() : null;
    }

    @Override
    public Map<String, Object> getFormData(String metricsId) {
        Map<String, Object> formData = new HashMap<>();
        
        try {
            // 获取指标要点数据
            List<EvaluateMetricsMainPoint> mainPoints = mainPointService.listByMetricsId(metricsId);
            
            // 将pointName作为key，fieldData作为value
            for (EvaluateMetricsMainPoint point : mainPoints) {
                if (StringUtils.isNotBlank(point.getPointName())) {
                    formData.put(point.getPointName(), point.getFieldData());
                }
            }
        } catch (Exception e) {
            log.error("获取表单数据失败", e);
        }
        
        return formData;
    }

    @Override
    public Map<String, Object> getCompleteFormInfo(String metricsId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取字段配置
            String fieldConfig = getFieldConfigJson(metricsId);
            result.put("fieldConfig", fieldConfig);
            
            // 获取规则配置
            String ruleConfig = getRuleConfigJson(metricsId);
            result.put("ruleConfig", ruleConfig);
            
            // 获取表单数据
            Map<String, Object> formData = getFormData(metricsId);
            result.put("formData", formData);
            
        } catch (Exception e) {
            log.error("获取完整表单信息失败", e);
        }
        
        return result;
    }

    @Override
    @Transactional
    public boolean updateFieldConfig(String metricsId, String json) {
        return saveConfigByType(metricsId, CONFIG_TYPE_FIELD, json);
    }

    @Override
    @Transactional
    public boolean updateRuleConfig(String metricsId, String json) {
        return saveConfigByType(metricsId, CONFIG_TYPE_RULE, json);
    }

    @Override
    @Transactional
    public boolean removeByMetricsId(String metricsId) {
        int result = baseMapper.deleteByMetricsId(metricsId);
        return result > 0;
    }

    @Override
    @Transactional
    public boolean removeByMetricsIdAndType(String metricsId, String configType) {
        int result = baseMapper.deleteByMetricsIdAndType(metricsId, configType);
        return result > 0;
    }
}
