package com.yuanqiao.insight.accountbook.modules.autoInspection.controller;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cronutils.model.CronType;
import com.cronutils.model.definition.CronDefinitionBuilder;
import com.cronutils.parser.CronParser;
import com.yuanqiao.insight.accountbook.modules.autoInspection.entity.DevopsAutoInspection;
import com.yuanqiao.insight.accountbook.modules.autoInspection.entity.DevopsMetadataInspection;
import com.yuanqiao.insight.accountbook.modules.autoInspection.entity.DevopsMetadataInspectionProduct;
import com.yuanqiao.insight.accountbook.modules.autoInspection.entity.ResourceInfo;
import com.yuanqiao.insight.accountbook.modules.autoInspection.service.IDevopsAutoInspectionService;
import com.yuanqiao.insight.accountbook.modules.autoInspection.service.IDevopsMetadataInspectionProductService;
import com.yuanqiao.insight.accountbook.modules.autoInspection.service.IDevopsMetadataInspectionService;
import com.yuanqiao.insight.modules.notice.entity.SysNoticeTemplate;
import com.yuanqiao.insight.modules.notice.service.ISysNoticeTemplateService;
import com.yuanqiao.insight.service.product.entity.Product;
import com.yuanqiao.insight.service.product.service.IProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoDict;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.util.ExcelUtilJeecg;
import org.jeecg.common.system.util.ZipEncryUtilsJeecg;
import org.jeecg.common.system.vo.DictModel;
import com.yuanqiao.insight.acore.system.service.ISysDictService;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.umpPwdManage.entity.UmpPwdManage;
import org.jeecg.modules.umpPwdManage.service.IUmpPwdManageService;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 产品模型巡检任务表
 * @Author: jeecg-boot
 * @Date: 2021-03-12
 * @Version: V1.0
 */
@Api(tags = "产品模型巡检任务表")
@RestController
@RequestMapping("/inspection/metadataInspection")
@Slf4j
public class DevopsMetadataInspectionController extends JeecgController<DevopsAutoInspection, IDevopsAutoInspectionService> {
    @Autowired
    private IDevopsMetadataInspectionService devopsProductInspectionService;
    @Autowired
    private IProductService productService;
    @Autowired
    private ISysDictService sysDictService;
    @Autowired
    private IDevopsMetadataInspectionProductService devopsMetadataInspectionProductService;
    @Autowired
    private IUmpPwdManageService umpPwdManageService;
    @Autowired
    private ISysNoticeTemplateService sysNoticeTemplateService;
    @Autowired
    private ISysUserService userService;


    /**
     * 分页列表查询
     *
     * @param devopsMetadataInspection
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "产品模型巡检任务-分页列表查询")
    @ApiOperation(value = "产品模型巡检任务表-分页列表查询", notes = "产品模型巡检任务表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(DevopsMetadataInspection devopsMetadataInspection,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {

        Result<IPage<DevopsAutoInspection>> result = new Result<>();
        String taskName = devopsMetadataInspection.getTaskName();
        devopsMetadataInspection.setTaskName(null);
        QueryWrapper<DevopsMetadataInspection> queryWrapper = QueryGenerator.initQueryWrapper(devopsMetadataInspection, req.getParameterMap());
        if (StringUtils.isNotBlank(taskName)) {
            queryWrapper.like("task_name", taskName);
        }
        Page<DevopsMetadataInspection> page = new Page<>(pageNo, pageSize);
        IPage<DevopsMetadataInspection> pageList = devopsProductInspectionService.page(page, queryWrapper);

        if (CollUtil.isNotEmpty(pageList.getRecords())) {
            //巡检类型
            List<DictModel> inspectionType = sysDictService.queryDictItemsByCode("inspection_type");
            //通知模板
            List<SysNoticeTemplate> noticeTemplate = sysNoticeTemplateService.list(new QueryWrapper<SysNoticeTemplate>());

            Map<String, String> inspectionMap = inspectionType.stream().collect(Collectors.toMap(DictModel::getValue, DictModel::getText));
            Map<String, String> noticeTemplateMap = noticeTemplate.stream().collect(Collectors.toMap(SysNoticeTemplate::getId, SysNoticeTemplate::getTemplateName));
            Map<String, String> userMap = userService.selectRealnameByUsername();

            pageList.getRecords().forEach(item -> {
                List<DevopsMetadataInspectionProduct> devopsMetadataInspectionProducts = devopsMetadataInspectionProductService.list(new QueryWrapper<DevopsMetadataInspectionProduct>().eq("task_id", item.getId()));
                if (CollUtil.isNotEmpty(devopsMetadataInspectionProducts)) {
                    item.setIds(devopsMetadataInspectionProducts.stream().map(DevopsMetadataInspectionProduct::getProductId).collect(Collectors.joining(",")));
                }
                item.setInspectionDictText(inspectionMap.get(item.getInspectionType()));
                item.setPushAddressText(noticeTemplateMap.get(item.getPushAddress()));
                item.setCreateByName(userMap.get(item.getCreateBy()));
                if (item.getExecuteCounts() == null) {
                    item.setExecuteCounts(0);
                }
            });
        }
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }


    /**
     * 添加
     *
     * @param devopsMetadataInspection
     * @return
     */
    @AutoLog(value = "产品模型巡检任务-添加")
    @ApiOperation(value = "产品模型巡检任务表-添加", notes = "产品模型巡检任务表-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody DevopsMetadataInspection devopsMetadataInspection) {
        QueryWrapper<DevopsMetadataInspection> queryWrapper = new QueryWrapper<>();
        String currentTaskName = devopsMetadataInspection.getTaskName();
        queryWrapper.eq("task_name", currentTaskName);
        // 找出与当前巡检任务相同name的数据
        List<DevopsMetadataInspection> list = devopsProductInspectionService.list(queryWrapper);
        if (CollUtil.isNotEmpty(list)) {
            return Result.error("任务名称重复！");
        }
        devopsProductInspectionService.save(devopsMetadataInspection);

        final JSONArray relatedIds = JSON.parseArray(devopsMetadataInspection.getRelatedIds());
        List<DevopsMetadataInspectionProduct> inspectionProductList = new ArrayList<>();
        for (Object relatedId : relatedIds) {
            DevopsMetadataInspectionProduct inspectionDevice = new DevopsMetadataInspectionProduct();
            inspectionDevice.setProductId(String.valueOf(relatedId));
            inspectionDevice.setTaskId(devopsMetadataInspection.getId());
            inspectionProductList.add(inspectionDevice);
        }
        devopsMetadataInspectionProductService.saveBatch(inspectionProductList);
        return Result.OK();
    }


    /**
     * 编辑
     *
     * @param devopsMetadataInspection
     * @return
     */
    @AutoLog(value = "产品模型巡检任务-编辑")
    @ApiOperation(value = "产品模型巡检任务表-编辑", notes = "产品模型巡检任务表-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody DevopsMetadataInspection devopsMetadataInspection) {
        List<DevopsMetadataInspection> list = devopsProductInspectionService.list(new QueryWrapper<DevopsMetadataInspection>().ne("id", devopsMetadataInspection.getId()).eq("task_name", devopsMetadataInspection.getTaskName()));
        if (CollUtil.isNotEmpty(list)) {
            return Result.error("任务名称重复！");
        }
        devopsProductInspectionService.updateById(devopsMetadataInspection);

        return Result.OK("编辑成功!");
    }

    /**
     * Cron校验
     *
     * @param cronExpression
     * @return
     */
    @AutoLog(value = "产品模型巡检任务-Cron校验")
    @ApiOperation(value = "产品模型巡检任务表-Cron校验", notes = "产品模型巡检任务表-Cron校验")
    @GetMapping(value = "/cronCheckSix")
    public Result<?> cronCheckSix(String cronExpression) {
        try {
            // 初始化CronTrigger同时完成Cron校验，校验不通过则抛出异常，终止后续执行
            // CronTrigger -》 CronSequenceGenerator -》 parse()
            new CronTrigger(cronExpression);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
        return Result.OK();
    }


    /**
     * Cron校验
     *
     * @param cronExpression
     * @return
     */
    @AutoLog(value = "产品模型巡检任务-Cron校验")
    @ApiOperation(value = "产品模型巡检任务表-Cron校验", notes = "产品模型巡检任务表-Cron校验")
    @GetMapping(value = "/cronCheck")
    public Result<?> cronCheck(String cronExpression) {
        try {
            CronParser parser = new CronParser(CronDefinitionBuilder.instanceDefinitionFor(CronType.QUARTZ));
            parser.parse(cronExpression);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
        return Result.OK();
    }


    /**
     * 绑定产品
     *
     * @param map
     * @return
     */
    @PostMapping(value = "/bindProduct")
    @AutoLog(value = "产品模型巡检任务表-绑定产品")
    @ApiOperation(value = "产品模型巡检任务表-绑定产品", notes = "产品模型巡检任务表-绑定产品")
    public Result<?> bindDevice(@RequestBody Map<Object, Object> map) {
        String id = (String) map.get("id");
        String productIds = (String) map.get("productIds");

        if (StringUtils.isEmpty(id)) {
            return Result.error("未选择绑定的任务");
        }
        if (StringUtils.isEmpty(productIds)) {
            return Result.error("未选择绑定的产品");
        }
        List<String> productIdList = Arrays.asList(productIds.split(","));

        List<DevopsMetadataInspectionProduct> inspectionDeviceList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(productIdList)) {
            for (String productId : productIdList) {
                DevopsMetadataInspectionProduct inspectionDevice = new DevopsMetadataInspectionProduct();
                inspectionDevice.setProductId(productId);
                inspectionDevice.setTaskId(id);
                inspectionDeviceList.add(inspectionDevice);
            }
            devopsMetadataInspectionProductService.saveBatch(inspectionDeviceList);
        }
        return Result.OK("绑定成功");
    }


    /**
     * 解绑产品
     *
     * @param id
     * @param productId
     * @return
     */
    @DeleteMapping(value = "/unbindProduct")
    @AutoLog(value = "产品模型巡检任务表-删除绑定某个产品")
    @ApiOperation(value = "产品模型巡检任务表-删除绑定某个产品", notes = "产品模型巡检任务表-删除绑定某个产品")
    public Result<?> unbindDevice(@RequestParam(name = "id", required = true) String id, @RequestParam(name = "productId", required = true) String productId) {
        devopsMetadataInspectionProductService.remove(new LambdaQueryWrapper<DevopsMetadataInspectionProduct>().eq(DevopsMetadataInspectionProduct::getTaskId, id).eq(DevopsMetadataInspectionProduct::getProductId, productId));
        return Result.OK();
    }


    /**
     * 巡检最外层接口
     *
     * @param id
     * @param req
     * @param pageNo
     * @param pageSize
     * @return
     */
    @GetMapping(value = "/getBindProductList")
    @AutoLog(value = "产品模型巡检任务表-获取巡检任务绑定的产品")
    @ApiOperation(value = "产品模型巡检任务表-获取巡检任务绑定的产品", notes = "产品模型巡检任务表-获取巡检任务绑定的产品")
    public Result<?> getBindDeviceList(String id, HttpServletRequest req,
                                       @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {

        List<DevopsMetadataInspectionProduct> inspectionProductList = devopsMetadataInspectionProductService.list(new LambdaQueryWrapper<DevopsMetadataInspectionProduct>().eq(DevopsMetadataInspectionProduct::getTaskId, id));
        List<String> inspectionProductIdList = inspectionProductList.stream().map(u -> u.getProductId()).collect(Collectors.toList());
        Page<Product> page = new Page<>(pageNo, pageSize);
        if (CollUtil.isNotEmpty(inspectionProductIdList)) {
            page = productService.page(page, new QueryWrapper<Product>().in("id", inspectionProductIdList));
        }
        return Result.OK(page);
    }


    /**
     * 巡检最里层弹出层列表数据
     *
     * @param id        巡检任务id
     * @param type      "product" or null
     * @param productId 可能是产品id值 也可能是分类id值   (type==null && productId存在)-->按照分类查询，分类Id的值为productId
     * @return
     */
    @AutoLog(value = "产品模型巡检任务表-获取产品信息接口")
    @ApiOperation(value = "产品模型巡检任务表-获取产品信息接口", notes = "产品模型巡检任务表-获取产品信息接口")
    @GetMapping(value = "/getResourceList")
    public Result<?> getResourceList(String id, String type, String productId, String exclusionIds, HttpServletRequest req,
                                     @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {

        Result<IPage<ResourceInfo>> result = new Result<>();
        Product product = new Product();
        type = StringUtils.isEmpty(type) && StringUtils.isNotEmpty(productId) ? "category" : "product";
        String categoryId = type.equals("category") ? productId : "";
        if (type.equals("product")) {
            product.setId(productId);
        } else {
            product.setAssetsCategoryId(categoryId);
        }

        QueryWrapper<Product> queryWrapper = QueryGenerator.initQueryWrapper(product, req.getParameterMap());

        // 过滤预选产品
        JSONArray exclusion = JSON.parseArray(exclusionIds);
        if (CollUtil.isNotEmpty(exclusion)) {
            queryWrapper.notIn("id", exclusion);
        }

        // 过滤巡检任务已选择的产品
        if (StringUtils.isNotEmpty(id)) {
            List<DevopsMetadataInspectionProduct> inspectionProductList = devopsMetadataInspectionProductService.list(new QueryWrapper<DevopsMetadataInspectionProduct>().eq("task_id", id));

            List<String> inspectionProductIdList = inspectionProductList.stream().map(u -> u.getProductId()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(inspectionProductIdList)) {
                queryWrapper.notIn("id", inspectionProductIdList);
            }
        }

        IPage<Product> pageList = productService.page(new Page<>(pageNo, pageSize), queryWrapper);
        result.setResult(pageList);

        return result;
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "产品模型巡检任务-通过id删除")
    @ApiOperation(value = "产品模型巡检任务表-通过id删除", notes = "产品模型巡检任务表-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id") String id) {
        devopsProductInspectionService.removeById(id);
        devopsMetadataInspectionProductService.remove(new QueryWrapper<DevopsMetadataInspectionProduct>().eq("task_id", id));
        return Result.OK("删除成功!");
    }


    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "产品模型巡检任务-批量删除")
    @ApiOperation(value = "产品模型巡检任务表-批量删除", notes = "产品模型巡检任务表-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        List<String> idLists = Arrays.asList(ids.split(","));
        devopsProductInspectionService.removeByIds(idLists);
        devopsMetadataInspectionProductService.remove(new QueryWrapper<DevopsMetadataInspectionProduct>().in("task_id", idLists));
        return Result.OK("批量删除成功!");
    }


    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "产品模型巡检任务-通过id查询")
    @ApiOperation(value = "产品模型巡检任务表-通过id查询", notes = "产品模型巡检任务表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        DevopsMetadataInspection devopsMetadataInspection = devopsProductInspectionService.getById(id);
        if (devopsMetadataInspection == null) {
            return Result.error("未找到对应数据");
        }
        //巡检类型
        List<DictModel> inspectionType = sysDictService.queryDictItemsByCode("inspection_type");
        //任务执行类型
        List<DictModel> taskexecuteType = sysDictService.queryDictItemsByCode("taskexecute_type");

        Map<String, String> inspectionMap = inspectionType.stream().collect(Collectors.toMap(DictModel::getValue, DictModel::getText));
        devopsMetadataInspection.setInspectionDictText(inspectionMap.get(devopsMetadataInspection.getInspectionType()));

        List<DevopsMetadataInspectionProduct> devopsMetadataInspectionProducts = devopsMetadataInspectionProductService.list(new QueryWrapper<DevopsMetadataInspectionProduct>().eq("task_id", id));
        if (CollUtil.isNotEmpty(devopsMetadataInspectionProducts)) {
            devopsMetadataInspection.setIds(devopsMetadataInspectionProducts.stream().map(DevopsMetadataInspectionProduct::getProductId).collect(Collectors.joining(",")));
        }
        return Result.OK(devopsMetadataInspection);
    }

    @Autowired
    private ZipEncryUtilsJeecg zipEncryUtils;

    /**
     * 导出excel
     *
     * @param req
     * @param devopsMetadataInspection
     */
    @AutoDict
    @AutoLog(value = "产品模型巡检任务-导出")
    @RequestMapping(value = "/exportXls")
    public void exportXls(HttpServletRequest req, HttpServletResponse response, DevopsMetadataInspection devopsMetadataInspection) {

        List<DevopsMetadataInspection> inspectionList = devopsProductInspectionService.list(QueryGenerator.initQueryWrapper(devopsMetadataInspection, req.getParameterMap()));
        inspectionList = inspectionList.stream().peek(i -> {
            if (i.getExecuteCounts() == null) {
                i.setExecuteCounts(0);
            }
        }).collect(Collectors.toList());

        UmpPwdManage zip = umpPwdManageService.getZip();
        String title = "产品模型巡检任务表";
        org.jeecgframework.poi.excel.entity.ExportParams exportParams1 = new org.jeecgframework.poi.excel.entity.ExportParams();
        exportParams1.setSheetName(title);
        Map<String, Object> deptDataMap = new HashMap<>(4);
        // title的参数为ExportParams类型
        deptDataMap.put("title", exportParams1);
        // 模版导出对应得实体类型
        deptDataMap.put("entity", DevopsMetadataInspection.class);
        // sheet中要填充得数据
        deptDataMap.put("data", inspectionList);

        List<Map<String, Object>> sheetsList = new ArrayList<>();
        sheetsList.add(deptDataMap);
        // 执行方法
        Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, org.jeecgframework.poi.excel.entity.enmus.ExcelType.HSSF);
        if (zip.getIsEncry() == null || zip.getZipPwd() == null) {
            ExcelUtilJeecg.downLoadExcel(title + ".xls", response, workbook);
        } else if (zip.getIsEncry()) {
            try {
                zipEncryUtils.excelEncry(title + ".xls", response, workbook, zip.getZipPwd());
            } catch (Exception e) {
                e.printStackTrace();
                log.error("导出加密文件报错！", e);
            }
        } else {
            ExcelUtilJeecg.downLoadExcel(title + ".xls", response, workbook);
        }
    }


    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, DevopsAutoInspection.class);
    }

}
