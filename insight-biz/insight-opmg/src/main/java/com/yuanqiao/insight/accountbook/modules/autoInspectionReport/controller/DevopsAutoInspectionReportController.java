package com.yuanqiao.insight.accountbook.modules.autoInspectionReport.controller;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.accountbook.modules.autoInspection.entity.DevopsAutoInspection;
import com.yuanqiao.insight.accountbook.modules.autoInspection.service.IDevopsAutoInspectionService;
import com.yuanqiao.insight.accountbook.modules.autoInspectionReport.entity.DevopsAutoInspectionReport;
import com.yuanqiao.insight.accountbook.modules.autoInspectionReport.service.IDevopsAutoInspectionReportService;
import com.yuanqiao.insight.common.constant.CommonConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.modules.fileinfo.entity.FileInfo;
import org.jeecg.modules.fileinfo.service.IFileInfoService;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.umpPwdManage.entity.UmpPwdManage;
import org.jeecg.modules.umpPwdManage.service.IUmpPwdManageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 自动检查报告
 * @Author: jeecg-boot
 * @Date: 2021-03-22
 * @Version: V1.0
 */
@Api(tags = "自动检查报告")
@RestController
@RequestMapping("/autoInspectionReport/devopsAutoInspectionReport")
@Slf4j
public class DevopsAutoInspectionReportController extends JeecgController<DevopsAutoInspectionReport, IDevopsAutoInspectionReportService> {
    @Autowired
    private IDevopsAutoInspectionReportService devopsAutoInspectionReportService;
    @Autowired
    private IFileInfoService fileInfoService;
    @Autowired
    private IDevopsAutoInspectionService devopsAutoInspectionService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private IUmpPwdManageService umpPwdManageService;

    /**
     * 分页列表查询
     * （移动端巡检列表展示）
     *
     * @param devopsAutoInspectionReport
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "自动检查报告-分页列表查询")
    @ApiOperation(value = "自动检查报告-分页列表查询", notes = "自动检查报告-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(DevopsAutoInspectionReport devopsAutoInspectionReport,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {

        Result<IPage<DevopsAutoInspectionReport>> result = new Result<>();

        QueryWrapper<DevopsAutoInspectionReport> queryWrapper = new QueryWrapper();

        queryWrapper.orderByDesc("taskstart_time");

        if (StringUtils.isNotEmpty(devopsAutoInspectionReport.getFileName())) {
            queryWrapper.like("file_name", devopsAutoInspectionReport.getFileName());
        }

        Page<DevopsAutoInspectionReport> page = new Page<DevopsAutoInspectionReport>(pageNo, pageSize);

        IPage<DevopsAutoInspectionReport> pageList = devopsAutoInspectionReportService.page(page, queryWrapper);

        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 拼接补全巡检报告的字段值
     * 该方法将不会被使用，数据库中字段已经补全
     *
     * @param list
     */
    private void extracted(List<DevopsAutoInspectionReport> list) {
        if (null != list && 0 < list.size()) {
            // 获取文件id
            List<String> fileIdList = list.stream().map(DevopsAutoInspectionReport::getFileId).collect(Collectors.toList());
            QueryWrapper<FileInfo> wrapper = new QueryWrapper();
            wrapper.lambda().in(FileInfo::getFileGroup, fileIdList);
            List<FileInfo> fileInfoList = fileInfoService.list(wrapper);
            //获取用户
            Map<String, String> userMap = sysUserService.selectRealnameByUsername();
            list.forEach(item -> {
                //获取巡检文件
                List<FileInfo> fileInfos = fileInfoList.stream().filter(f -> f.getFileGroup().equals(item.getFileId())).collect(Collectors.toList());
                //获取巡检任务
                DevopsAutoInspection devopsAutoInspection = devopsAutoInspectionService.getById(item.getTaskId());
                if (null != devopsAutoInspection) {
                    for (FileInfo fileInfo : fileInfos) {
                        if (CommonConstant.TASK_FILE_SUFFIX.equals(fileInfo.getSuffix())) {
                            item.setFileUrl(fileInfo.getFileUrl());
                            item.setFileName(fileInfo.getFileName());
                            item.setTaskCreateUser(userMap.get(devopsAutoInspection.getCreateBy()));
                        }
                    }
                }

            });
        }
    }

    /**
     * 添加
     *
     * @param devopsAutoInspectionReport
     * @return
     */
    @AutoLog(value = "自动检查报告-添加")
    @ApiOperation(value = "自动检查报告-添加", notes = "自动检查报告-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody DevopsAutoInspectionReport devopsAutoInspectionReport) {
        log.info("devopsAutoInspectionReport={}", devopsAutoInspectionReport);
        log.info("com.yuanqiao.insight.accountbook.modules.autoInspectionReport.controller.DevopsAutoInspectionReportController.add(devopsAutoInspectionReport={})", devopsAutoInspectionReport);
        if (devopsAutoInspectionReport.getCreateBy() == null) {
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            devopsAutoInspectionReport.setCreateBy(sysUser.getRealname());
        }
        devopsAutoInspectionReportService.save(devopsAutoInspectionReport);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param devopsAutoInspectionReport
     * @return
     */
    @AutoLog(value = "自动检查报告-编辑")
    @ApiOperation(value = "自动检查报告-编辑", notes = "自动检查报告-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody DevopsAutoInspectionReport devopsAutoInspectionReport) {
        log.info("devopsAutoInspectionReport={}", devopsAutoInspectionReport);
        log.info("com.yuanqiao.insight.accountbook.modules.autoInspectionReport.controller.DevopsAutoInspectionReportController.edit(devopsAutoInspectionReport={})", devopsAutoInspectionReport);
        devopsAutoInspectionReportService.updateById(devopsAutoInspectionReport);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "自动检查报告-通过id删除")
    @ApiOperation(value = "自动检查报告-通过id删除", notes = "自动检查报告-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        log.info("id={}", id);
        log.info("com.yuanqiao.insight.accountbook.modules.autoInspectionReport.controller.DevopsAutoInspectionReportController.delete(id={})", id);
        devopsAutoInspectionReportService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "自动检查报告-批量删除")
    @ApiOperation(value = "自动检查报告-批量删除", notes = "自动检查报告-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        log.info("ids={}", ids);
        log.info("com.yuanqiao.insight.accountbook.modules.autoInspectionReport.controller.DevopsAutoInspectionReportController.deleteBatch(ids={})", ids);
        //获取报告文件路径，删除源文件以及file_info表中数据记录
        List<DevopsAutoInspectionReport> inspectionReportList = devopsAutoInspectionReportService.list(new QueryWrapper<DevopsAutoInspectionReport>().in("id", Arrays.asList(ids.split(","))));
        if (CollUtil.isNotEmpty(inspectionReportList)) {
            List<String> reportFileUrlList = inspectionReportList.stream().map(DevopsAutoInspectionReport::getFileUrl).collect(Collectors.toList());
            CommonUtils.deleteFile(reportFileUrlList);
            List<String> reportFileIdList = inspectionReportList.stream().map(DevopsAutoInspectionReport::getFileId).collect(Collectors.toList());
            fileInfoService.remove(new QueryWrapper<FileInfo>().in("file_group", reportFileIdList));
        }
        this.devopsAutoInspectionReportService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "自动检查报告-通过id查询")
    @ApiOperation(value = "自动检查报告-通过id查询", notes = "自动检查报告-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        log.info("id={}", id);
        log.info("com.yuanqiao.insight.accountbook.modules.autoInspectionReport.controller.DevopsAutoInspectionReportController.queryById(id={})", id);
        DevopsAutoInspectionReport devopsAutoInspectionReport = devopsAutoInspectionReportService.getById(id);
        if (devopsAutoInspectionReport == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(devopsAutoInspectionReport);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param devopsAutoInspectionReport
     */
    @AutoLog(value = "报告列表-导出")
    @RequestMapping(value = "/exportXls")

    public void exportXls(HttpServletRequest request, HttpServletResponse response, DevopsAutoInspectionReport devopsAutoInspectionReport) {
        log.info("devopsAutoInspectionReport={}", devopsAutoInspectionReport);
        log.info("com.yuanqiao.insight.accountbook.modules.autoInspectionReport.controller.DevopsAutoInspectionReportController.exportXls(devopsAutoInspectionReport={})", devopsAutoInspectionReport);
        UmpPwdManage zip = umpPwdManageService.getZip();
//        List<DevopsAutoInspectionReport> list = devopsAutoInspectionReportService.list();
        //extracted(list);
//        super.zipPwd(response, DevopsAutoInspectionReport.class, "自动检查报告", zip.getZipPwd(), zip.getIsEncry(), list);
		super.exportXlsZip(request,response, devopsAutoInspectionReport, DevopsAutoInspectionReport.class, "自动检查报告",zip.getZipPwd(),zip.getIsEncry());
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        log.info("com.yuanqiao.insight.accountbook.modules.autoInspectionReport.controller.DevopsAutoInspectionReportController.importExcel");
        return super.importExcel(request, response, DevopsAutoInspectionReport.class);
    }

}
