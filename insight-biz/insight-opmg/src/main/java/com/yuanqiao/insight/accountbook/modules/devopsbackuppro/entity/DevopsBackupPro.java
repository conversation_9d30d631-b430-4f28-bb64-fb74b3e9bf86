package com.yuanqiao.insight.accountbook.modules.devopsbackuppro.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecg.common.aspect.annotation.IsLikeQueryColumn;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 备份策略表
 * @Author: jeecg-boot
 * @Date:   2021-03-22
 * @Version: V1.0
 */
@Data
@TableName("devops_backup_pro")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="devops_backup_pro对象", description="备份策略表")
public class DevopsBackupPro implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
	/**策略名称*/
	@IsLikeQueryColumn
	@Excel(name = "策略名称", width = 15)
    @ApiModelProperty(value = "策略名称")
    private String proName;
	/**执行类型*/
//	@Excel(name = "执行类型", width = 15, dicCode = "exec_type")
	@Dict(dicCode = "exec_type")
    @ApiModelProperty(value = "执行类型")
    private String execType;
	@TableField(exist = false)
	private String execType_dictText;
	/**执行cron码*/
	@Excel(name = "执行cron码", width = 15)
    @ApiModelProperty(value = "执行cron码")
    private String taskCron;
	/**文件类型*/
	@Excel(name = "目标文件类型", width = 15, dicCode = "wh_condense")
	@Dict(dicCode = "wh_condense")
    @ApiModelProperty(value = "文件类型")
    private String whCondense;
	@TableField(exist = false)
    private String whCondense_dictText;
	/**备份文件名称*/
	@Excel(name = "目标文件夹名称", width = 15)
    @ApiModelProperty(value = "备份文件名称")
    private String fileName;
}
