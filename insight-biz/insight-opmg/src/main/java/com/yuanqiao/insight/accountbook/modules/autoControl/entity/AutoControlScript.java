package com.yuanqiao.insight.accountbook.modules.autoControl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description: 自动化控制脚本
 * @Author: jeecg-boot
 * @Date:   2024-02-23
 * @Version: V1.0
 */
@Data
@TableName("devops_auto_control_script")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="devops_auto_control_script对象", description="自动化控制脚本")
public class AutoControlScript {

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
	private String id;
	/**脚本名称*/
	@Excel(name = "脚本名称", width = 15)
    @ApiModelProperty(value = "脚本名称")
	private String scriptName;
	/**脚本语言*/
	@Excel(name = "脚本语言", width = 15)
    @ApiModelProperty(value = "脚本语言")
	private String scriptLanguage;
	/**脚本参数*/
	@Excel(name = "脚本参数", width = 15)
    @ApiModelProperty(value = "脚本参数")
	private String scriptParam;
	/**脚本内容*/
	@Excel(name = "脚本内容", width = 15)
    @ApiModelProperty(value = "脚本内容")
	private String scriptContext;
	/**附件*/
	@Excel(name = "附件", width = 15)
    @ApiModelProperty(value = "附件")
	private String scriptFile;
	/**图标*/
	private String icon;
	/**附件*/
	@Excel(name = "描述", width = 15)
	@ApiModelProperty(value = "描述")
	private String description;
	/**创建人*/
	@Excel(name = "创建人", width = 15)
    @ApiModelProperty(value = "创建人")
	private String createBy;
	/**创建时间*/
	@Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
	private Date createTime;
	/**更新人*/
	@Excel(name = "更新人", width = 15)
    @ApiModelProperty(value = "更新人")
	private String updateBy;
	/**更新时间*/
	@Excel(name = "更新时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
	private Date updateTime;
}
