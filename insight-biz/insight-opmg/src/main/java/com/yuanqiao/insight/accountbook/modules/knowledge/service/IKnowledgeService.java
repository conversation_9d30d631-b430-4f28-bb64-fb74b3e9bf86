package com.yuanqiao.insight.accountbook.modules.knowledge.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.yuanqiao.insight.accountbook.modules.knowledge.entity.Knowledge;

import java.util.List;

/**
 * @Description: 事件、问题知识库
 * @Author: jeecg-boot
 * @Date:   2020-09-18
 * @Version: V1.0
 */
public interface IKnowledgeService extends IService<Knowledge> {
    /**
     * 模糊查询
     *
     * @param title 标题
     * @return
     */
    List<Knowledge> getFuzzyQueryByTitle(String title);

    List<Knowledge> getByTypes(List<String> types);

}
