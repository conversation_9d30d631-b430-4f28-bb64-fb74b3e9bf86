package com.yuanqiao.insight.accountbook.modules.arrangementinfo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuanqiao.insight.accountbook.modules.arrangementinfo.entity.DevopsArrangementInfo;
import com.yuanqiao.insight.accountbook.modules.schedualinfo.entity.DevopsSchedualInfo;

import java.util.List;
import java.util.Map;

/**
 * @Description: 排班表
 * @Author: jeecg-boot
 * @Date:   2021-03-12
 * @Version: V1.0
 */
public interface IDevopsArrangementInfoService extends IService<DevopsArrangementInfo> {


    /**
     * 通过班次批量添加排班
     * @param devopsSchedualInfo
     */
    void addBatchBySchedualInfo(DevopsSchedualInfo devopsSchedualInfo);

    /**
     * 批量修改排班类型
     * @param ids
     */
    void updateBatchByIds(String ids);

    /**
     * 通过班次ids  获取排班
     * @param ids
     * @return
     */

    List<String> selectBySchedualId(List<String> ids);
    /**
     * 通过班次ids  删除排班
     * @param ids
     * @return
     */
    void delBySchedualId(List<String> ids);

    /**
     * 通过班次的ids获取班次信息
     * @param ids
     * @return
     */
    Map<String,DevopsArrangementInfo> getDevopsSchedualInfoMaps(List<String> ids);
}
