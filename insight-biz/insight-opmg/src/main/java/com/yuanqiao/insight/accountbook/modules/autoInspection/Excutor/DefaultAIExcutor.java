package com.yuanqiao.insight.accountbook.modules.autoInspection.Excutor;

import cn.hutool.core.collection.CollUtil;
import com.yuanqiao.insight.accountbook.modules.autoInspection.entity.DevopsAutoInspection;
import com.yuanqiao.insight.accountbook.modules.autoInspection.entity.DevopsAutoInspectionDevice;
import com.yuanqiao.insight.accountbook.modules.autoInspection.generater.AIRGenerater;
import com.yuanqiao.insight.accountbook.modules.autoInspection.generater.AITask;
import com.yuanqiao.insight.accountbook.modules.autoInspection.generater.ProxyAIRGenerater;
import com.yuanqiao.insight.accountbook.modules.autoInspection.scanner.AIScanner;
import com.yuanqiao.insight.accountbook.modules.autoInspection.scanner.ProxyAIScanner;
import com.yuanqiao.insight.accountbook.modules.autoInspection.service.IDevopsAutoInspectionDeviceService;
import com.yuanqiao.insight.accountbook.modules.autoInspection.service.impl.DevopsAutoInspectionDeviceServiceImpl;
import com.yuanqiao.insight.acore.utils.SpringContextUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: DefaultAIExcutor
 * @projectName jeecg-boot-parent
 * @description: TODO
 * @date 2021/5/15-11:31
 */
@Slf4j
public class DefaultAIExcutor implements AIExcutor {
    private IDevopsAutoInspectionDeviceService devopsAutoInspectionDeviceService;
    //默认的结果扫描组件
    private final AIScanner aIScanner;
    //默认的生成报告组件
    private final AIRGenerater aIRGenerater;

    /**
     * 无参构造函数
     */
    public DefaultAIExcutor() {
        this.aIScanner = new ProxyAIScanner();
        this.aIRGenerater = new ProxyAIRGenerater();
        this.devopsAutoInspectionDeviceService = SpringContextUtil.getApplicationContext().getBean(DevopsAutoInspectionDeviceServiceImpl.class);
    }

    /**
     * 任务执行器接口的执行方法
     *
     * @param autoInspection 自动巡检任务类对象
     */
    @Override
    public void excute(DevopsAutoInspection autoInspection, String aIfilePath, String path) {

        try {
            //获取要巡检的设备
            List<DevopsAutoInspectionDevice> devopsAutoInspectionDevices = devopsAutoInspectionDeviceService.getDeviceByTaskId(autoInspection.getId());
            if (CollUtil.isNotEmpty(devopsAutoInspectionDevices)) {
                log.error("AAAA资源巡检任务--数据准备 开始！！！！");
                AITask aITask = new AITask();
                aITask.setTaskId(autoInspection.getId());
                aITask.setAIfilePath(aIfilePath);
                aITask.setPath(path);
                aITask.setDeviceIds(devopsAutoInspectionDevices.stream().map(DevopsAutoInspectionDevice::getDeviceId).collect(Collectors.toList()));

                //扫描组件（ProxyAIScanner -> DefaultAIScanner）
                this.aIScanner.scan(aITask);
                log.error("AAAA资源巡检任务--数据准备 完毕！！！！");
                //生成报告组件（ProxyAIRGenerater -> DefaultAIRGenerater）
                if (aITask.getAIRGeneraterResult() != null && aITask.getAIRGeneraterResult().getGeneraterResult() != null && !aITask.getAIRGeneraterResult().getGeneraterResult().isEmpty()) {
                    this.aIRGenerater.report(autoInspection, aITask);
                }
            }
        } catch (Exception e) {
            log.error("资源巡检任务执行器异常！", e);
        }
    }
}
