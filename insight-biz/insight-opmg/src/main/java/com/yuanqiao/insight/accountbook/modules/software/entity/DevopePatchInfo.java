package com.yuanqiao.insight.accountbook.modules.software.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecg.common.aspect.annotation.IsLikeQueryColumn;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 补丁管理表
 * @Author: jeecg-boot
 * @Date:   2021-03-13
 * @Version: V1.0
 */
@Data
@TableName("devope_patch_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="devope_patch_info对象", description="补丁管理表")
public class DevopePatchInfo implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**补丁名称*/
	@IsLikeQueryColumn
	@Excel(name = "补丁名称", width = 15)
    @ApiModelProperty(value = "补丁名称")
    private String patchName;
	/**补丁版本*/
	@Excel(name = "补丁版本", width = 15)
    @ApiModelProperty(value = "补丁版本")
    private String patchVersion;
	/**操作系统*/
    @Excel(name = "操作系统", width = 15,dicCode = "patch_os")
    @ApiModelProperty(value = "操作系统")
    private String patchOs;
	@TableField(exist = false)
    private String patchOsText;
	/**架构*/
	@Excel(name = "架构", width = 15)
    @ApiModelProperty(value = "架构")
    private String frawork;
	@TableField(exist = false)
	private String fraworkText;
	/**补丁文件id*/
//	@Excel(name = "补丁文件id", width = 15)
    @ApiModelProperty(value = "补丁文件id")
    private String patchFileId;
	/**安装脚本id*/
//	@Excel(name = "安装脚本id", width = 15)
    @ApiModelProperty(value = "安装脚本id")
    private String scriptFileId;
	/**补丁文件名称*/
	@Excel(name = "补丁文件名称", width = 15)
    @ApiModelProperty(value = "补丁文件名称")
    private String patchFileName;
    @TableField(exist = false)
    private String patchFileNameText;

    /**脚本文件*/
    @Excel(name = "脚本文件", width = 15)
    @ApiModelProperty(value = "脚本文件")
    private String scriptFileName;
    @TableField(exist = false)
    private String scriptFileNameText;
	/**描述*/
	@Excel(name = "描述", width = 15)
    @ApiModelProperty(value = "描述")
    private String patchDescribe;

	/**是否有效*/
	@Excel(name = "是否有效", width = 15, dicCode = "valid_status")
	@Dict(dicCode = "valid_status")
    @ApiModelProperty(value = "是否有效")
    private Integer effect;
    @TableField(exist = false)
    private String effectText;

	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;

	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;

	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;

	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;

	/**软件id*/
    @ApiModelProperty(value = "软件id")
    private String softwareId;
    //软件名称
    /**设备类型字段，0：服务器，6：桌面机*/
    @Excel(name = "设备类型", width = 15, dicCode = "resources_type")
    @Dict(dicCode = "resources_type")
    @ApiModelProperty(value = "设备类型")
    private String resourceType;
    @TableField(exist = false)
    private String resourceTypeText;

    /**补丁文件hash值(MD5)*/
    @ApiModelProperty(value = "补丁文件hash值(MD5)")
    private String fileHash;

    /**脚本文件hash值(MD5)*/
    @ApiModelProperty(value = "脚本文件hash值(MD5)")
    private String scriptHash;
}
