package com.yuanqiao.insight.accountbook.modules.evaluate.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateMetricsType;
import com.yuanqiao.insight.accountbook.modules.evaluate.mapper.EvaluateMetricsTypeMapper;
import com.yuanqiao.insight.accountbook.modules.evaluate.service.IEvaluateMetricsTypeService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName EvaluateMetricsServiceImpl
 * @description: TODO
 * @datetime 2025年 05月 29日 19:30
 * @version: 1.0
 */
@Service
public class EvaluateMetricsTypeServiceImpl extends ServiceImpl<EvaluateMetricsTypeMapper, EvaluateMetricsType> implements IEvaluateMetricsTypeService {
}
