package com.yuanqiao.insight.accountbook.modules.evaluate.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName EvaluateMetricsFieldConfig
 * @description: 评估指标字段配置表
 * @datetime 2025年 07月 28日
 * @version: 1.0
 */
@Data
@TableName("devops_evaluate_metrics_field_config")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="devops_evaluate_metrics_field_config对象", description="评估指标字段配置表")
public class EvaluateMetricsFieldConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "指标ID")
    private String metricsId;

    @ApiModelProperty(value = "表单配置JSON")
    private String formConfig;

    @ApiModelProperty(value = "字段总数")
    private Integer fieldCount;

    @ApiModelProperty(value = "评估字段数量")
    private Integer evaluationFieldCount;

    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;

    @Dict(dictTable = "sys_users", dicText = "realname", dicCode = "username")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;

    @Dict(dictTable = "sys_users", dicText = "realname", dicCode = "username")
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;

    // 非数据库字段，用于返回解析后的评估字段列表
    @TableField(exist = false)
    @ApiModelProperty(value = "评估字段列表")
    private List<EvaluationFieldInfo> evaluationFields;
}
