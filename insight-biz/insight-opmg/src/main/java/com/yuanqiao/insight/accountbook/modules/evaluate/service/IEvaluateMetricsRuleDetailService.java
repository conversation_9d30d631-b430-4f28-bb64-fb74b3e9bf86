package com.yuanqiao.insight.accountbook.modules.evaluate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateMetricsRuleDetail;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName IEvaluateMetricsRuleDetailService
 * @description: 评估指标规则详情服务接口
 * @datetime 2025年 07月 28日
 * @version: 1.0
 */
public interface IEvaluateMetricsRuleDetailService extends IService<EvaluateMetricsRuleDetail> {
    
    /**
     * 根据指标ID获取规则详情列表
     * @param metricsId 指标ID
     * @return 规则详情列表
     */
    List<EvaluateMetricsRuleDetail> listByMetricsId(String metricsId);
    
    /**
     * 根据指标ID删除规则详情
     * @param metricsId 指标ID
     * @return 是否删除成功
     */
    boolean removeByMetricsId(String metricsId);
    
    /**
     * 批量保存规则详情
     * @param ruleDetails 规则详情列表
     * @return 是否保存成功
     */
    boolean saveBatch(List<EvaluateMetricsRuleDetail> ruleDetails);
}
