package com.yuanqiao.insight.accountbook.modules.devopsipaudit.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuanqiao.insight.accountbook.modules.devopsipaudit.entity.DevopsIpAuditResult;
import com.yuanqiao.insight.accountbook.modules.devopsipaudit.model.ExportWordModel;

import javax.servlet.http.HttpServletResponse;

/**
 * @Description: 审计结果表
 * @Author: jeecg-boot
 * @Date: 2024-03-01
 * @Version: V1.0
 */
public interface IDevopsIpAuditResultService extends IService<DevopsIpAuditResult> {

    DevopsIpAuditResult getResultByAuditId(String auditId);

    ExportWordModel exportModel(String id);

    void generateCharts(String id, HttpServletResponse response);

}
