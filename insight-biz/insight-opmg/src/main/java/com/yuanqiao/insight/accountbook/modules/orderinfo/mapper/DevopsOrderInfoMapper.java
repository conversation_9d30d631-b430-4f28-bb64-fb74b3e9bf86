package com.yuanqiao.insight.accountbook.modules.orderinfo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanqiao.insight.accountbook.modules.orderinfo.entity.DevopsOrderInfo;
import com.yuanqiao.insight.accountbook.modules.orderinfo.entity.HandOrderVO;
import com.yuanqiao.insight.accountbook.modules.orderinfo.entity.TypeCountVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 运维工单详情表
 * @Author: jeecg-boot
 * @Date:   2021-03-11
 * @Version: V1.0
 */
@Component
@Mapper
public interface DevopsOrderInfoMapper extends BaseMapper<DevopsOrderInfo> {

    List<DevopsOrderInfo> queryByIds(@Param("ids") List<String> ids);

    /**
     * 按天查询分配
     * @param startDate
     * @param endDate
     * @return
     */
    List<DevopsOrderInfo> queryAllocDate(@Param("startDate") String startDate,@Param("endDate") String endDate);
    /**
     * 一个小时内分配
     * @param startDate
     * @param endDate
     * @return
     */
    List<DevopsOrderInfo> queryAllocTimelyDate(@Param("startDate") String startDate,@Param("endDate") String endDate);

    /**
     * 按天查询处理
     * @param startDate
     * @param endDate
     * @return
     */
    List<DevopsOrderInfo> queryHandleDate(@Param("startDate") String startDate,@Param("endDate") String endDate);
    /**
     * 一个小时内处理
     * @param startDate
     * @param endDate
     * @return
     */
    List<DevopsOrderInfo> queryHandleTimelyDate(@Param("startDate") String startDate,@Param("endDate") String endDate);


    List<DevopsOrderInfo> queryAllDate2(@Param("startDate") String startDate,@Param("endDate") String endDate);
    List<HandOrderVO> queryAllDate(@Param("date")String date, @Param("countArr")String[] countArr);
    @Deprecated
    List<TypeCountVo> typeCount (@Param("startDate") String startDate,@Param("endDate") String endDate);

    List<DevopsOrderInfo> getTodoUser(@Param("username") String username);

    // 前30条数据
    //IPage<HandOrderVO> dayCount(Page<?> page, @Param("startDate") String startDate, @Param("endDate") String endDate);


}
