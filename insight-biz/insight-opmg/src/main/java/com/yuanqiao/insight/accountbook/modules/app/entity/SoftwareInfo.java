package com.yuanqiao.insight.accountbook.modules.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yuanqiao.insight.acore.validation.AddGroup;
import com.yuanqiao.insight.acore.validation.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecg.common.aspect.annotation.IsLikeQueryColumn;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 软件管理表
 * @Author: jeecg-boot
 * @Date:   2021-03-11
 * @Version: V1.0
 */

@Data
@TableName("devops_software_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="devops_software_info对象", description="软件管理表")
public class SoftwareInfo implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    @NotBlank(message = "id 不能为Null",groups = {UpdateGroup.class})
    private String id;

	/**应用名称*/
	@IsLikeQueryColumn
	@Excel(name = "应用名称", width = 15)
    @ApiModelProperty(value = "应用名称")
    @NotBlank(message="应用名称不能为空",groups = {AddGroup.class, UpdateGroup.class})
    private String softwareName;

	/**应用描述*/
	@Excel(name = "应用描述", width = 15)
    @ApiModelProperty(value = "应用描述")
    private String softwareDescribe;

	/**开发商*/
	@Excel(name = "开发商", width = 15)
    @ApiModelProperty(value = "开发商")
    private String softwareProducer;

    /**软件适用平台*/
    //@Excel(name = "软件适用平台", width = 15)
    @ApiModelProperty(value = "软件适用平台")
    @Dict(dicCode = "softwarePlatform")
    private String softwarePlatform;

	/**应用分类*/
	@Excel(name = "应用分类", width = 15)
    @ApiModelProperty(value = "应用分类")
    @Dict(dicCode = "app_classify")
    private String softwareType;

    /**应用简介*/
    @Excel(name = "应用简介", width = 15)
    @ApiModelProperty(value = "应用简介")
    private String softwareBrief;

    /**应用LOGO*/
    @Excel(name = "应用LOGO", width = 15)
    @ApiModelProperty(value = "应用LOGO")
    private String softwareLogo;

    /**支持的CPU架构*/
    @Excel(name = "支持的CPU架构", width = 15)
    @ApiModelProperty(value = "支持的CPU架构")
    private String softwareCpuType;

    /**支持的操作系统*/
    @Excel(name = "支持的操作系统", width = 15)
    @ApiModelProperty(value = "支持的操作系统")
    private String softwareOsType;

    /**更新日期*/
    @Excel(name = "更新日期", width = 20, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date softwareUpdateDate;

    /**应用截图*/
    @Excel(name = "应用截图", width = 15)
    @ApiModelProperty(value = "应用截图")
    private String softwareScreenshot;

    /**最新版本*/
    @Excel(name = "最新版本", width = 15)
    @ApiModelProperty(value = "最新版本")
    private String softwareLatestVersion;

    /**升级包数量*/
    @Excel(name = "升级包数量", width = 15)
    @ApiModelProperty(value = "升级包数量")
    private Integer softwareUpgradePackageNum;

    /**下载次数*/
    @Excel(name = "下载次数", width = 15)
    @ApiModelProperty(value = "下载次数")
    private Integer softwareDownloadNum;

	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;

	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;

}
