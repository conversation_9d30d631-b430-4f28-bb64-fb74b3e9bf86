package com.yuanqiao.insight.accountbook.modules.devopsBackupTask.dispatcher;

import com.yuanqiao.insight.accountbook.modules.devopsBackupTask.actuator.BUActuator;
import com.yuanqiao.insight.accountbook.modules.devopsBackupTask.entity.BackupTask;

/**
 * <AUTHOR>
 * @title: BUDispatcher            备份任务调度器
 * @projectName jeecg-boot-parent
 * @description: TODO
 * @date 2021/5/24-15:53
 */
public interface BUDispatcher {
    /**
     * 执行备份执行任务调度方法
     *
     * @param buTask 备份执行任务配置对象
     */
     BUActuator dispatch(BackupTask buTask) throws Exception;
}
