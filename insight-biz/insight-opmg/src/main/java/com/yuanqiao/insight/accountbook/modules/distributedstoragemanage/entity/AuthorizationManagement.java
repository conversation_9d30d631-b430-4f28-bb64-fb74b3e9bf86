package com.yuanqiao.insight.accountbook.modules.distributedstoragemanage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@TableName("distributed_transfer_bind_authorization")
@ApiModel(value="distributed_transfer_bind_authorization", description="")
public class AuthorizationManagement {
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    String id;

    @ApiModelProperty(value = "")
    String datasourceId;

    @ApiModelProperty(value = "")
    private String userId;

    @ApiModelProperty(value = "")
    private String policyName;

    @ApiModelProperty(value = "")
    private String policyDetail;



    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

}
