package com.yuanqiao.insight.accountbook.modules.distributedstoragemanage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuanqiao.insight.accountbook.modules.distributedstoragemanage.entity.DistributedStorageNode;
import com.yuanqiao.insight.accountbook.modules.distributedstoragemanage.entity.StorageDatasource;

public interface IDistributedStorageDatasourceService extends IService<StorageDatasource> {
    String getNodeNameByIPAndPort(String ip,String port);
}
