package com.yuanqiao.insight.accountbook.modules.devopsipplan.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 子网组
 * @Author: jeecg-boot
 * @Date: 2024-02-26
 * @Version: V1.0
 */
@Data
@TableName("devops_ip_subnet_group")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "devops_ip_subnet_group对象", description = "子网组")
public class DevopsIpSubnetGroup implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    /**
     * 子网组名称
     */
    @Excel(name = "子网组名称", width = 15)
    @ApiModelProperty(value = "子网组名称")
    private String subnetGroupName;
    /**
     * 子网组简称
     */
    @Excel(name = "子网组简称", width = 15)
    @ApiModelProperty(value = "子网组简称")
    private String subnetGroupNickname;
    /**
     * 部门id
     */
    @Excel(name = "部门id", width = 15)
    @ApiModelProperty(value = "部门id")
    private String departId;
    /**
     * 部门名字
     */
    @Excel(name = "部门名字", width = 15)
    @ApiModelProperty(value = "部门名字")
    private String departName;
    /**
     * 位置
     */
    @Excel(name = "位置", width = 15)
    @ApiModelProperty(value = "位置")
    private String location;
    /**
     * 备注
     */
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
}
