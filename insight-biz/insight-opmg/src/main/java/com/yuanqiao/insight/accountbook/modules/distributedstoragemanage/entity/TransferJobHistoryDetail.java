package com.yuanqiao.insight.accountbook.modules.distributedstoragemanage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@TableName("distributed_transfer_job_history_detail")
@ApiModel(value="distributed_transfer_job_history_detail", description="")
@AllArgsConstructor
@NoArgsConstructor
public class TransferJobHistoryDetail {
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "")
    private String transferJobId;

    @ApiModelProperty(value = "")
    private String transferJobHistoryId;

    @ApiModelProperty(value = "")
    private String pathBeforeTransfer;

    @ApiModelProperty(value = "")
    private String pathAfterTransfer;


}
