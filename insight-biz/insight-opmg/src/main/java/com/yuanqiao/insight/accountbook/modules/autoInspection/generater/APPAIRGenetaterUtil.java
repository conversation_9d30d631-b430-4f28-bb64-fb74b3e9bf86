package com.yuanqiao.insight.accountbook.modules.autoInspection.generater;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.yuanqiao.insight.accountbook.modules.autoInspection.entity.DevopsAutoInspection;
import com.yuanqiao.insight.common.constant.CommonConstant;
import com.yuanqiao.insight.service.device.entity.DeviceInfo;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.DateUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: APPAIRGenetaterUtil    应用生成报告功能类
 * @projectName jeecg-boot-parent
 * @description: TODO
 * @date 2021/5/19-10:27
 */

@Slf4j
public class APPAIRGenetaterUtil {
    /**
     * 创建PDF文档
     *
     * @return
     * @throws Exception
     * @throws Exception
     */
    public void createPDF(AITask aITask, DevopsAutoInspection autoInspection, String filePath, Map<String, Map<String, Map<String, String>>> photoPath) throws Exception {
        FileOutputStream fos = null;
        try {
            // 设置纸张大小和背景色
            Rectangle rect = new Rectangle(PageSize.A4);
            // 创建文档实例  （PDF的文档实例）
            Document doc = new Document(rect);
            // 添加中文字体
            BaseFont bfChinese = BaseFont.createFont("STSong-Light",
                    "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

            // 设置字体样式
            Font textFont = new Font(bfChinese, 11, Font.NORMAL,
                    BaseColor.BLACK); // 正常
            BaseColor greyColor = new BaseColor(228, 228, 228);
            BaseColor greyColorApp = new BaseColor(169, 169, 169);
            // 概述-缩小版字体
            Font textFontMiddle = new Font(bfChinese, 12, Font.NORMAL,
                    BaseColor.BLACK); // 正常
            Font lineFont = new Font(bfChinese, 11, Font.NORMAL,
                    BaseColor.BLACK); // 正常
            Font firsetTitleFont = new Font(bfChinese, 22, Font.NORMAL,
                    BaseColor.BLACK); // 一级标题
            Font secondTitleFont = new Font(bfChinese, 15, Font.BOLD,
                    BaseColor.BLACK); // 二级标题

            // 将组织得到的报告内容写入pdf文件
            fos = new FileOutputStream(new File(filePath));
            // 创建输出流
            PdfWriter.getInstance(doc, fos);
            doc.open();
            doc.newPage();

            //整个PDF页  生成一个表格
            PdfPTable detailsTable = new PdfPTable(1);
            detailsTable.setTotalWidth(new float[]{580}); // 设置列宽
            detailsTable.setLockedWidth(true); // 锁定列宽
            String contentText = "应用性能巡检";
            //创建一个   段落
            Paragraph p1 = new Paragraph(contentText, firsetTitleFont);
            p1.setLeading(40);
            p1.setAlignment(Element.ALIGN_CENTER);
            // 单元格
            PdfPCell detailsCell = new PdfPCell(p1);
            detailsCell.setHorizontalAlignment(Element.ALIGN_CENTER); // 设置水平居中
            detailsCell.setVerticalAlignment(Element.ALIGN_MIDDLE); // 设置垂直居中
            detailsCell.setMinimumHeight(70); // 设置单元格高度
            detailsCell.setUseAscender(true); // 设置可以居中
            detailsTable.addCell(detailsCell);
            // 颜色加深行颜色（灰色）
            // 获取巡检日期
            String time = DateUtils.formatDateHMS(autoInspection.getCreateTime()).split(" ")[0];
            String context1 = " 巡检日期： " + time;
            Paragraph p2 = new Paragraph(context1, textFontMiddle);
            p2.setAlignment(Element.ALIGN_RIGHT);
            p2.setLeading(10);
            // 单元格
            PdfPCell detailsCell2 = new PdfPCell(p2);
            detailsCell2.setBackgroundColor(greyColor);
            detailsCell2.setMinimumHeight(20); // 设置单元格高度
            detailsCell2.setVerticalAlignment(Element.ALIGN_RIGHT); // 设置垂直居中
            detailsCell2.setHorizontalAlignment(Element.ALIGN_RIGHT);//靠右显示
            detailsTable.addCell(detailsCell2);
            //
            String taskexecuteTypeText = "";
            if (CommonConstant.TASK_EXECUTE_TYPE_1.equals(autoInspection.getTaskexecuteType())) {
                taskexecuteTypeText = CommonConstant.TASK_EXECUTE_TYPE_TEXT_1;
            } else {
                taskexecuteTypeText = CommonConstant.TASK_EXECUTE_TYPE_TEXT_2;
            }
            //
            List<DeviceInfo> deviceInfos = aITask.getDeviceInfoList();
            StringBuilder appName = new StringBuilder();
            if (null != deviceInfos && !deviceInfos.isEmpty()) {
                for (DeviceInfo deviceInfo : deviceInfos) {
                    appName.append("[").append(deviceInfo.getName()).append("]、");
                }
                appName = new StringBuilder(appName.substring(0, appName.length() - 1));
            }

            String substance = "\n 巡检概况：\n\n - 巡检类型：" + taskexecuteTypeText + " \n\n - 巡检时间：" + DateUtils.formatDateHMS(autoInspection.getTaskStartTime()) + " \n\n - 巡检应用：" + appName + " \n\n";
            Paragraph p3 = new Paragraph(substance, textFontMiddle);
            p3.setAlignment(Element.ALIGN_LEFT);
            PdfPCell detailsCell3 = new PdfPCell(p3);
            detailsCell3.setMinimumHeight(50);
            detailsCell3.setVerticalAlignment(Element.ALIGN_MIDDLE); // 设置垂直居中
            detailsTable.addCell(detailsCell3);
            //将列表放到pdf 实例里
            doc.add(detailsTable);
            //TODO 后续在此处显示
            // 创建告警信息统计列表，
            detailsTable = new PdfPTable(6);
            detailsTable.setTotalWidth(new float[]{96, 96, 96, 96, 96, 100}); // 设置列宽
            detailsTable.setLockedWidth(true); // 锁定列宽
            String[] title = {"巡检指标名称", "正常", "一般警告", "严重警告", "未连接", "数量"};

            createDetailsTable(detailsTable, title, 5, 6, aITask);
            doc.add(detailsTable);
            // 创建报告分类巡检信息列表
            detailsTable = new PdfPTable(10);
            detailsTable.setTotalWidth(new float[]{50, 50, 70, 110, 50, 50, 50, 50, 50, 50}); // 设置列宽
            detailsTable.setLockedWidth(true); // 锁定列宽
            createCellByType(detailsTable, aITask);
            doc.add(detailsTable);


            //结束
            doc.close();
        } catch (Exception e) {
            log.error("AppAIRGenetaterUtil发生异常", e);
        } finally {
            if (fos != null) {
                try {
                    fos.flush();
                    fos.close();
                } catch (IOException e) {
                }
                fos = null;
            }

        }
    }

    /**
     * 创建详情表格PDF
     *
     * @param table
     * @param row
     * @param cols
     * @throws IOException
     * @throws DocumentException
     */
    private void createDetailsTable(PdfPTable table, String[] title,
                                    int row, int cols, AITask aITask) throws DocumentException, IOException {
        // 添加中文字体
        BaseFont bfChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        // 设置字体样式
        Font font = new Font(bfChinese, 11, Font.NORMAL, BaseColor.BLACK); // 正常
        BaseColor color = null;
        //设置表头
        // 生成列头
        for (int j = 0; j < cols; j++) {
            color = new BaseColor(46, 73, 88);
            createDetailsCell(color, title[j], font, table, 1);
        }

        Map<String, AIRGeneraterTypeResult> geneTypeResultMap = aITask.getAIRGeneraterResult().getGeneraterResult();
        Iterator<String> iter = geneTypeResultMap.keySet().iterator();
        int i = 0;

        while (iter.hasNext()) {
            String key = iter.next();
            if (key.equals("total")) {
                continue;
            }
            AIRGeneraterTypeResult geneTypeResult = geneTypeResultMap.get(key);
            createDetailsCell(color, String.valueOf(geneTypeResult.getTypeName()), font, table, 1);
            createDetailsCell(color, String.valueOf(geneTypeResult.getNormalNum()), font, table, 1);
            createDetailsCell(color, String.valueOf(geneTypeResult.getWarningNum()), font, table, 1);
            createDetailsCell(color, String.valueOf(geneTypeResult.getErrorNum()), font, table, 1);
            createDetailsCell(color, String.valueOf(geneTypeResult.getUnconnNum()), font, table, 1);
            createDetailsCell(color, String.valueOf(geneTypeResult.getTotalNum()), font, table, 1);

            ++i;
        }
    }

    /**
     * 添加单元格PDF
     *
     * @param color 单元格背景色
     * @param key   单元格内容
     * @param font  单元格字体
     * @param table 表格
     * @param n     合并单元格数
     */
    private void createDetailsCell(BaseColor color, String key, Font font, PdfPTable table, int n) {
        PdfPCell cell = new PdfPCell(new Phrase(key, font));
        cell.setMinimumHeight(15); // 设置单元格高度
        cell.setUseAscender(true); // 设置可以居中
        cell.setHorizontalAlignment(Element.ALIGN_CENTER); // 设置水平居中
        cell.setVerticalAlignment(Element.ALIGN_MIDDLE); // 设置垂直居中
        cell.setColspan(n);
        table.addCell(cell);
    }

    /**
     * 创建分类资源表格PDF
     *
     * @param table
     * @throws IOException
     * @throws DocumentException
     */
    private void createCellByType(PdfPTable table,
                                  AITask aITask) throws DocumentException, IOException {
        // 添加中文字体
        BaseFont bfChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        // 设置字体样式
        Font font = new Font(bfChinese, 11, Font.NORMAL, BaseColor.BLACK); // 正常
        BaseColor color = null;
        //设置表头
        Map<String, AIRGeneraterTypeResult> geneTypeResultMap = aITask
                .getAIRGeneraterResult().getGeneraterResult();
        for (String key : geneTypeResultMap.keySet()) {
            if (key.equals("total")) {
                continue;
            }
            AIRGeneraterTypeResult geneTypeResult = geneTypeResultMap.get(key);
            color = new BaseColor(2, 136, 209);
            createDetailsCell(color, geneTypeResult.getTypeName(), font, table, 10);
            Map<String, String> dataColumnMap = aITask.getAiReportDataColumnDictory().getDataColumnMapByType(key);
            Iterator<String> columnKeyIter = dataColumnMap.keySet().iterator();
            // 生成列头
            color = new BaseColor(46, 73, 88);
            while (columnKeyIter.hasNext()) {
                String columnKey = columnKeyIter.next();
                createDetailsCell(color, dataColumnMap.get(columnKey), font, table, 1);
            }

            List<Map<String, String>> resultList = geneTypeResult.getAiDetailedResult();
            // 按行组织输出详细巡检结果
            int i = 0;
            for (Map<String, String> result : resultList) {

                Iterator<String> dataKeyIter = dataColumnMap.keySet().iterator();
                if (i % 2 == 0) {
                    color = new BaseColor(5, 82, 124);
                } else {
                    color = new BaseColor(23, 65, 100);
                }
                while (dataKeyIter.hasNext()) {
                    String dataKey = dataKeyIter.next();
                    createDetailsCell(color, result.get(dataKey), font, table, 1);
                }
                ++i;
            }
        }
    }

}
