package com.yuanqiao.insight.accountbook.modules.operatehistory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanqiao.insight.accountbook.modules.operatehistory.entity.OperateHistoryInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 操作历史表
 * @Author: jeecg-boot
 * @Date:   2021-03-11
 * @Version: V1.0
 */
public interface OperateHistoryInfoMapper extends BaseMapper<OperateHistoryInfo> {

    List<OperateHistoryInfo> getListsByBusinessId(String businssId);

    List<OperateHistoryInfo> getListsByCreateTime(@Param("startTime") String startTime, @Param("endTime") String endTime);
}
