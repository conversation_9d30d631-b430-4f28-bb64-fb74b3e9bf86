package com.yuanqiao.insight.accountbook.modules.knowledgebase.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.accountbook.modules.knowledgebase.entity.Knowledge;
import com.yuanqiao.insight.accountbook.modules.knowledgebase.entity.KnowledgeComment;
import com.yuanqiao.insight.accountbook.modules.knowledgebase.service.IAuthorityService;
import com.yuanqiao.insight.accountbook.modules.knowledgebase.utils.ESClient;
import com.yuanqiao.insight.accountbook.modules.knowledgebase.utils.KnowledgeRelationUtil;
import com.yuanqiao.insight.accountbook.modules.knowledgebase.utils.KnowledgeUtils;
import com.yuanqiao.insight.accountbook.modules.knowledgebase.utils.WordUtil;
import com.yuanqiao.insight.common.util.common.DateUtil;
import com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice;
import com.yuanqiao.insight.monitoring.modules.terminal.mapper.TerminalDeviceMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.http.entity.ContentType;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.constant.FileBizPathConstant;
import org.jeecg.common.constant.FileStorageTypePrefix;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.common.util.MinioUtil;
import org.jeecg.common.util.MultipartFileUtil;
import org.jeecg.modules.system.entity.SysUsers;
import org.jeecg.modules.system.service.ISysUserRoleService;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 知识库--知识
 * @Author: 辛杰煊
 */
@Api(tags = "知识库")
@RestController(value = "kbaseKnowledgeController")
@RequestMapping("/kbase/knowledges")
@Slf4j
public class KnowledgeController {
    @Autowired
    private ISysUserRoleService sysUserRoleService;

    @Autowired
    private IAuthorityService authorityService;

    @Autowired
    private ESClient esClient;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private TerminalDeviceMapper terminalDeviceMapper;

    @Autowired
    private KnowledgeUtils knowledgeUtils;

    @Value(value = "${jeecg.path.upload}")
    private String uploadpath;

    @Value(value = "${jeecg.uploadType}")
    private String globalUploadType;

    @Value("${knowledge.attachment:true}")
    private boolean attachment;

    @Autowired
    KnowledgeRelationUtil knowledgeRelationUtil;

    /**
     * 分页列表查询
     *
     * @param
     * @param knowledgeTopicId
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @GetMapping(value = "/list")
    public Result<?> queryKnowledgePage(@RequestParam(required = false)String title,
                                        @RequestParam(required = false)String isPrivate,
                                        @RequestParam(required = false) String knowledgeTopicId,
                                        @RequestParam(required = false) Integer knowledgeType,
                                        @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                        @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                        HttpServletRequest req) throws IOException {
        pageNo = pageNo < 1 ? 1 : pageNo;
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> withPermissionTopic;
        Page<Knowledge> userCollectByUid = new Page<>();
        if (StringUtils.isNotEmpty(knowledgeTopicId)){
            if (!authorityService.hasAuthorityByTopicId(knowledgeTopicId)){
                return Result.error("您无权访问该知识库主题");
            }
            withPermissionTopic = authorityService.getAllChildTopicByTopicId(knowledgeTopicId);
            userCollectByUid = esClient.searchKnowledgeByTitleAndIsPrivate(loginUser.getId(),pageNo,pageSize,title,withPermissionTopic,isPrivate,knowledgeType);
        }else {
            withPermissionTopic = authorityService.getWithPermissionTopic();
            userCollectByUid = esClient.searchKnowledgeByTitleAndIsPrivate(loginUser.getId(),pageNo,pageSize,title,withPermissionTopic,isPrivate,knowledgeType);
        }
        if (StringUtils.isNotBlank(title) && userCollectByUid.getTotal() != 0L){
            esClient.addKeyword(title,loginUser.getId());
        }
        knowledgeUtils.addRealnameForKnowledge(userCollectByUid);
        return Result.OK(userCollectByUid);
    }

    /**
     * 添加
     *
     * @param
     * @return
     */
    @AutoLog(value = "知识库-添加")
    @ApiOperation(value = "知识库-添加", notes = "知识库-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody Knowledge knowledge) throws IOException {
        if (!authorityService.hasAuthorityByTopicId(knowledge.getTopicId())){
            return Result.error("您无权操作该知识库主题");
        }
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        knowledge.setCreateBy(loginUser.getId());
        String uuid = UUID.randomUUID().toString().replace("-","");
        addRelation(uuid, loginUser.getId(), knowledge);
        String id = esClient.addKnowledge(knowledge, uuid);
        knowledge.setId(id);
        esClient.handleKnowledgeAttachmentNewThread(knowledge);
        return Result.ok("添加成功！");
    }

    @AutoLog(value = "知识库-索引附件-执行一次")
    @ApiOperation(value = "知识库-索引附件", notes = "知识库-索引附件")
    @PostMapping(value = "/attachment")
    public Result<?> indexAttachment(@RequestBody Knowledge knowledge) throws Exception {
        Knowledge know = esClient.queryKnowledgeByIdWithoutPlan(knowledge.getId());
        boolean isFailure = esClient.handleKnowledgeAttachment(know);
        if (isFailure){
            return Result.error("索引失败");
        }
        return Result.ok("索引成功！");
    }


    @GetMapping(value = "/getIndexAttachmentFailedKnowledge")
    public Result<?> getIndexAttachmentFailedKnowledge(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) throws Exception {
        Page<Knowledge> result = esClient.getIndexAttachmentFailedKnowledge(pageNo,pageSize);
        knowledgeUtils.addRealnameForKnowledge(result);
        return Result.ok(result);
    }

    @AutoLog(value = "知识库-添加-流程转知识库时附件一并上传到知识库中")
    @ApiOperation(value = "知识库-添加", notes = "知识库-添加-流程转知识库时附件一并上传到知识库中")
    @PostMapping(value = "/addFromFlowable")
    public Result<?> addFromFlowable(@RequestBody Knowledge knowledge) throws Exception {
        //todo 这个没考虑审批的功能
        if (!authorityService.hasAuthorityByTopicId(knowledge.getTopicId())) {
            return Result.error("您无权操作该知识库主题");
        }
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        knowledge.setCreateBy(loginUser.getId());
        String uuid = UUID.randomUUID().toString().replace("-", "");

        //下面全是对从flowable流程那获取文件的操作，并上传到minio
        JSONObject fileJson = JSONObject.parseObject(knowledge.getFiles());
        String[] fileSplit = fileJson.getString("fileUrl").split(",");
        String[] result = new String[fileSplit.length];
        if (fileSplit.length != 0) {
            for (int i = 0; i < fileSplit.length && StringUtils.isNotBlank(fileSplit[i]); i++) {
                if (fileSplit[i].startsWith("http") || fileSplit[i].startsWith(FileStorageTypePrefix.MINIO_PREFIX)) {
                    String s = MinioUtil.copyFileFromFlowableToKnowledge(fileSplit[i]);
                    result[i] = s;
                } else {
                    String filePath = fileSplit[i].replace("..", "");
                    if (filePath.startsWith(FileStorageTypePrefix.LOCAL_PREFIX)){
                        filePath = filePath.substring(FileStorageTypePrefix.LOCAL_PREFIX.length());
                    }
                    if (filePath.endsWith(",")) {
                        filePath = filePath.substring(0, filePath.length() - 1);
                    }
                    String localPath = uploadpath;
                    String downloadFilePath = localPath + File.separator + filePath;
                    File file = new File(downloadFilePath);
                    if (file.exists()) {
                        String fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
                        MultipartFile item = MultipartFileUtil.fileToMultipartFile(file, fileName, ContentType.APPLICATION_OCTET_STREAM.toString());
                        String upload = CommonUtils.upload(item, FileBizPathConstant.KNOWLEDGE_BIZ_PATH, globalUploadType);
                        result[i] = upload;
                    }else {
                        result[i] = "";
                    }
                }
            }
        }
        String filesStr = StringUtils.join(result, ",");
        fileJson.put("fileUrl",filesStr);
        knowledge.setFiles(fileJson.toString());
        addRelation(uuid, loginUser.getId(), knowledge);
        String id = esClient.addKnowledge(knowledge, uuid);
        knowledge.setId(id);
        esClient.handleKnowledgeAttachmentNewThread(knowledge);
        return Result.ok("添加成功！");
    }

    void addRelation(String uuid,String userId,Knowledge knowledge) throws IOException {
        if (StringUtils.isNotBlank(knowledge.getProcessInstanceId())){
            String definitionKey = knowledgeRelationUtil.getDefinitionKeyByProcessInstanceId(knowledge.getProcessInstanceId());
            String storageKey = knowledgeRelationUtil.getStorageKeyFromConfigureDict(definitionKey);
            esClient.addRelation(uuid, userId, knowledge.getProcessInstanceId(), storageKey);
        }
    }

    /**
     * 编辑
     *
     * @param
     * @return
     */
    @AutoLog(value = "知识库-知识-编辑")
    @ApiOperation(value = "知识库-知识--编辑", notes = "知识库-知识--编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody Knowledge knowledge) throws IOException, ParseException {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Knowledge oldKnowledge = esClient.queryKnowledgeByIdWithoutPlan(knowledge.getId());
        if (!authorityService.hasAuthority(oldKnowledge)) {
            return Result.error("您没有权限查看此知识");
        }
//        //新增：只有创建人才能编辑知识。前端控制了
//        if (StringUtils.equals(oldKnowledge.getCreateBy(),loginUser.getId())){
//            return Result.error("只有创建人才能修改知识");
//        }
        if (userCannotViewNotPublishKnowledge(oldKnowledge, loginUser)) {
            //这一个仅出现在收藏、分享、热门知识查看时。因为用户一般看不到未发布的知识
            return Result.error("当前知识被修改，等审批通过后可查看该知识");
        }
        //处理文件
        boolean isFileChange = false;
        //即使附件个数为0，新版的oldKnowledge.getFiles()也会是{"fileUrl":"","originalFilename":""}
        List<String> oldFiles = new ArrayList<>();
        List<String> newFiles = new ArrayList<>();
        if (StringUtils.isNotBlank(oldKnowledge.getFiles())) {
            if (oldKnowledge.getFiles().charAt(0) != '{') {//前后端已协调好，新版文件发送json，原来的只是用逗号隔开的字符串
                oldFiles = new ArrayList<>(Arrays.asList(oldKnowledge.getFiles().split(",")));
            } else {
                JSONObject oldFileJson = JSONObject.parseObject(oldKnowledge.getFiles());
                oldFiles = new ArrayList<>(Arrays.asList(oldFileJson.getString("fileUrl").split(",")));
            }
        }
        JSONObject fileJson = JSONObject.parseObject(knowledge.getFiles());
        if (StringUtils.isNotBlank(fileJson.getString("fileUrl"))) {
            newFiles = new ArrayList<>(Arrays.asList(fileJson.getString("fileUrl").split(",")));
            isFileChange = !(oldFiles.containsAll(newFiles) && newFiles.containsAll(oldFiles));
            oldFiles.removeAll(newFiles);
        } else if (!oldFiles.isEmpty()) {
            isFileChange = true;
        }

        CommonUtils.deleteFile(oldFiles);

        knowledge.setUpdateBy(loginUser.getId());
        esClient.editKnowledge(knowledge, isFileChange);
        esClient.handleKnowledgeAttachmentNewThread(knowledge);
        if (knowledge.isPublishForAddAndEdit()) {
            return Result.OK("请等待审批!");
        }
        return Result.OK("保存成功!");
    }

    //仅发布按钮
    //按理是一个保存的接口、一个发布的接口。保存并发布时前端先后调两个接口，只发布时调一个接口。但是es不能保证实时更新，保存并发布时间隔时间太短可能会报错、出问题。所以edit接口承担了保存并发布、保存的两个功能。
    //而这个publish承担了发布的功能
    @AutoLog(value = "知识库-知识-发布")
    @ApiOperation(value = "知识库-知识--发布", notes = "知识库-知识--发布")
    @PutMapping(value = "/publish")
    public Result<?> onlyPublish(@RequestParam(name = "knowledgeId", required = true) String knowledgeId) throws IOException, ParseException {
        if (!authorityService.hasAuthority(knowledgeId)){
            return Result.error("您没有权限查看此知识");
        }
        esClient.onlyPublishKnowledge(knowledgeId);
        return Result.OK("请等待审批!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "知识库-通过id删除")
    @ApiOperation(value = "知识库-通过id删除", notes = "知识库-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) throws IOException, ParseException {
        Knowledge knowledge = esClient.queryKnowledgeByIdWithoutPlan(id);
        if (knowledge == null) {
            return Result.OK("删除成功!");
        }
        if (!authorityService.hasAuthority(knowledge)){
            return Result.error("您没有权限操作此知识");
        }
        if (StringUtils.isNotBlank(knowledge.getFiles()) && StringUtils.isNotBlank(knowledge.getFiles().trim())) {
            List<String> fileUrls;
            String fileString = knowledge.getFiles();
            if (fileString.charAt(0) == '{'){//前后端已协调好，新版文件发送json，原来的只是用逗号隔开的字符串
                //格式{"fileUrl":"1.ppt，2.ppt","originalFilename":"中文.ppt,中文.ppt"}
                JSONObject fileJson = JSONObject.parseObject(fileString);
                fileUrls = Arrays.asList(fileJson.getString("fileUrl").split(","));
            }else {
                fileUrls = Arrays.asList(fileString.split(","));
            }
            CommonUtils.deleteFile(fileUrls);
        }
        esClient.deleteKnowledgeById(id);
        //todo 删除时的其他操作，这个打算让ai来搞一下，需要删除分享的。emmmc，deleteKnowledgeById里有删除相关项的逻辑

        return Result.OK("删除成功!","删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "知识库-批量删除")
    @ApiOperation(value = "知识库-批量删除", notes = "知识库-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) throws IOException, ParseException {
        String[] idArray = ids.split(",");
        for (String id : idArray){
            if (!authorityService.hasAuthority(id)){
                return Result.error("您没有权限操作此知识");
            }
        }
        esClient.deleteKnowledgeBatch(Arrays.asList(idArray));
        return Result.OK("批量删除成功!","批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "知识库-通过id查询")
    @ApiOperation(value = "知识库-通过id查询", notes = "知识库-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) throws IOException, ParseException {
        Knowledge knowledge = esClient.queryKnowledgeById(id);
        if (knowledge == null) {
            return Result.error("未找到对应数据");
        }
        if (!authorityService.hasAuthority(knowledge)){
            return Result.error("您没有权限查看此知识");
        }
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (userCannotViewNotPublishKnowledge(knowledge,loginUser)) {
            return Result.error("当前知识仍在审批中，等审批通过后可查看该知识");
        }
        SysUsers user = sysUserService.getByUId(knowledge.getCreateBy());
        if (user != null){
            knowledge.setCreateBy(user.getRealname());
        }
        long pageViewConut = esClient.addPageView(id);
        knowledge.setPageViewCount(pageViewConut);
        return Result.OK(knowledge);
    }

    @AutoLog(value = "知识库-找到通过es中存储的附件内容")
    @ApiOperation(value = "知识库-找到es中存储的附件内容", notes = "知识库-找到es中存储的附件内容")
    @GetMapping(value = "/content")
    public Result<?> getContentFromES(@RequestParam(name = "id", required = true) String id,
                                      @RequestParam(name = "originalFilename", required = true) String originalFilename,
                                      @RequestParam(name = "filename", required = false) String filename) throws IOException, ParseException {
        Knowledge knowledge = esClient.queryKnowledgeContentByIdAndOriginalFilename(id,originalFilename);
        if (knowledge == null) {
            return Result.error("未找到对应数据");
        }
        if (!authorityService.hasAuthority(knowledge)){
            return Result.error("您没有权限查看此知识");
        }
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (userCannotViewNotPublishKnowledge(knowledge,loginUser)) {
            return Result.error("当前知识仍在审批中，等审批通过后可查看该知识");
        }
        //获取附件的内容，可能为空字符串
        JSONObject jsonObject = (JSONObject)knowledge.getAttachment().get(0);
        return Result.OK(jsonObject.getString("content"));
    }

    @AutoLog(value = "知识库-找到通过es中存储的附件内容")
    @ApiOperation(value = "知识库-找到通过es中存储的附件内容", notes = "知识库-找到通过es中存储的附件内容")
    @GetMapping(value = "/content/oneclickhelp")
    public Result<?> getContentFromESForOneClickHelp(@RequestParam(name = "id", required = true) String id,
                                      @RequestParam(name = "originalFilename", required = true) String originalFilename,
                                      @RequestParam(name = "filename", required = false) String filename,
                                                     @RequestParam(name = "hostname") String hostname) throws IOException, ParseException {
        TerminalDevice terByCode = terminalDeviceMapper.getTerByCode(hostname);
        List<String> withPermissionTopic = authorityService.getWithPermissionTopicForOneClickHelp(terByCode.getDeptId());
        Knowledge knowledge = esClient.queryKnowledgeContentByIdAndOriginalFilename(id,originalFilename);
        if (knowledge == null) {
            return Result.error("未找到对应数据");
        }
        if (!withPermissionTopic.contains(knowledge.getTopicId())){
            return Result.error("您没有权限查看此知识");
        }
        if ((StringUtils.isNotBlank(knowledge.getState()) && !StringUtils.equals("已发布", knowledge.getState()))){
            return Result.error("当前知识仍在审批中，等审批通过后可查看该知识");
        }
        //获取附件的内容，可能为空字符串
        JSONObject jsonObject = (JSONObject)knowledge.getAttachment().get(0);
        return Result.OK(jsonObject.getString("content"));
    }


    /**
     * 通过id查询
     * 知识审批专用，无权限校验
     * @param id
     * @return
     */
    @AutoLog(value = "知识库-通过id查询-知识审批专用，无权限校验")
    @ApiOperation(value = "知识库-通过id查询", notes = "知识库-通过id查询-知识审批专用，无权限校验")
    @GetMapping(value = "/queryByIdForReview")
    public Result<?> queryByIdForReview(@RequestParam(name = "id", required = true) String id) throws IOException, ParseException {
        Knowledge knowledge = esClient.queryKnowledgeById(id);
        if (knowledge == null) {
            return Result.error("未找到对应数据");
        }
        SysUsers user = sysUserService.getByUId(knowledge.getCreateBy());
        if (user != null){
            knowledge.setCreateBy(user.getRealname());
        }
        long pageViewConut = esClient.addPageView(id);
        knowledge.setPageViewCount(pageViewConut);
        return Result.OK(knowledge);
    }

    boolean userCannotViewNotPublishKnowledge(Knowledge knowledge,LoginUser loginUser){
        String roles = sysUserRoleService.getUserRoleByUserId(loginUser.getId()).toString();
        if ((StringUtils.isNotBlank(knowledge.getState()) && StringUtils.equals("已发布", knowledge.getState()))){
            return false;
        }
        if (StringUtils.isBlank(knowledge.getUpdateBy()) ? StringUtils.equals(loginUser.getId(), knowledge.getCreateBy()) : StringUtils.equals(loginUser.getId(), knowledge.getUpdateBy())){
            return false;
        }
        if (roles.contains(org.jeecg.modules.constant.CommonConstant.ADMIN)){
            return false;
        }
        return true;

    }

    @GetMapping(value = "/checkAuthorityById")
    public Result<?> checkAuthorityById(@RequestParam(name = "id", required = true) String id) throws IOException, ParseException {
        Knowledge knowledge = esClient.queryKnowledgeByIdWithoutPlan(id);
        if (knowledge == null) {
            return Result.error("未找到对应数据");
        }
        if (!authorityService.hasAuthority(knowledge)){
            return Result.error("您没有权限查看此知识");
        }
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (userCannotViewNotPublishKnowledge(knowledge,loginUser)){
            if (StringUtils.equals("待修改",knowledge.getState())){
                return Result.error("当前知识待修改，等修改、发布、审批后可查看该知识");
            }else if (StringUtils.equals("待发布",knowledge.getState())){
                return Result.error("当前知识待发布，等发布、审批后可查看该知识");
            }
            return Result.error("当前知识仍在审批中，等审批通过后可查看该知识");
        }
        return Result.OK();
    }

    /**
     * 获取知识库热搜前5条
     *
     * @return
     */
    @RequestMapping(value = "/getHotSearch", method = RequestMethod.GET)
    public Result<?> getHotSearch() throws IOException {
        List<String> hotKeyword = esClient.getHotKeyword();
        return Result.OK(hotKeyword);
    }


    //1、打开一条知识时，显示点赞、点踩、收藏的数据
    @RequestMapping(value = "/extendsInfo", method = RequestMethod.GET)
    public Result<?> getExtendsInfo(@RequestParam(name = "knowledgeId", required = true) String knowledgeId) throws IOException {
        JSONObject json = esClient.getLikeAndUnlikeCountByKid(knowledgeId);
//        JSONObject collectCountByKid = esClient.getCollectCountByKid(knowledgeId);
//        json.putAll(collectCountByKid);
        return Result.OK(json);
    }

    //显示用户对该知识的操作，点赞、点踩、收藏
    @RequestMapping(value = "/userchosen", method = RequestMethod.GET)
    public Result<?> getUserchosen(@RequestParam(name = "knowledgeId", required = true) String knowledgeId) throws IOException {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        JSONObject json = esClient.getExtendsInfoByKidAndUid(knowledgeId,loginUser.getId());
        return Result.OK(json);
    }

    //在操作后获取统计信息
    private JSONObject getExtendsInfoAfterAction(String knowledgeId) throws IOException {
        JSONObject json = esClient.getLikeAndUnlikeCountByKid(knowledgeId);
        return json;
    }

    //我的收藏
    @RequestMapping(value = "/usercollect", method = RequestMethod.GET)
    public Result<?> getUserCollectKnowledge(@RequestParam(required = false)String title,
                                             @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                             @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) throws IOException, ParseException {
        pageNo = pageNo < 1 ? 1 : pageNo;
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Page<Knowledge> knowledgePage = new Page<>();
        if (StringUtils.isNotBlank(title)){
            knowledgePage = esClient.getUserCollectByUid(loginUser.getId(),pageNo,pageSize,title);
        }else {
            knowledgePage = esClient.getUserCollectByUid(loginUser.getId(),pageNo,pageSize,null);
        }
        knowledgeUtils.addRealnameForKnowledge(knowledgePage);
        return Result.OK(knowledgePage);
    }

    //我的评论
    @RequestMapping(value = "/usercomment", method = RequestMethod.GET)
    public Result<?> getUserCommentedKnowledge(@RequestParam(required = false)String title,
                                               @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                               @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) throws IOException, ParseException {
        pageNo = pageNo < 1 ? 1 : pageNo;
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Page<Knowledge> knowledgePage = new Page<>();
        if (StringUtils.isNotBlank(title)){
            knowledgePage = esClient.getCommentByUserIdAndSearchTitle(loginUser.getId(),pageNo,pageSize,title);
        }else {
            knowledgePage = esClient.getCommentByUserId(loginUser.getId(), pageNo, pageSize,null);
        }
        knowledgeUtils.addRealnameForKnowledge(knowledgePage);
        return Result.OK(knowledgePage);
    }

    //我的发布
    @RequestMapping(value = "/userCreated", method = RequestMethod.GET)
    public Result<?> getUserCreatedKnowledge(@RequestParam(required = false)String title,
                                             @RequestParam(required = false)String isPrivate,
                                             @RequestParam(required = false)String state,
                                             @RequestParam(required = false) Integer knowledgeType,
                                             @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                             @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) throws IOException {
        pageNo = pageNo < 1 ? 1 : pageNo;
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Page<Knowledge> knowledgePage = new Page<>();
        if (StringUtils.isNotBlank(title) || StringUtils.isNotBlank(isPrivate)){
            knowledgePage = esClient.searchKnowledgeFromCurrentUserCreate(loginUser.getId(),pageNo,pageSize,title,isPrivate,state,knowledgeType);
        }else {
            knowledgePage = esClient.getUserCreatedByUid(loginUser.getId(), pageNo, pageSize,state,knowledgeType);
        }
        knowledgeUtils.addRealnameForKnowledge(knowledgePage);
        return Result.OK(knowledgePage);
    }

    //我的分享
    @RequestMapping(value = "/usershared", method = RequestMethod.GET)
    public Result<?> getUserSharedKnowledge(@RequestParam(required = false)String title,
                                            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) throws IOException, ParseException {
        pageNo = pageNo < 1 ? 1 : pageNo;
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        esClient.deleteExpirationSharedByUid(loginUser.getId());
        Page<Knowledge> userSharedByUid = new Page<>();
        if (StringUtils.isNotBlank(title)){
            userSharedByUid = esClient.getUserSharedByUidAndSearchTitle(loginUser.getId(),pageNo,pageSize,title);
        }else {
            userSharedByUid = esClient.getUserSharedByUid(loginUser.getId(), pageNo, pageSize);
        }
        knowledgeUtils.addRealnameForKnowledge(userSharedByUid);
        return Result.OK(userSharedByUid);
    }

    //5、点赞
    @RequestMapping(value = "/like", method = RequestMethod.POST)
    public Result<?> likeKnowledge(@RequestParam(name = "knowledgeId", required = true) String knowledgeId) throws IOException {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //这时候还需要加锁，锁什么呢。。。knowledgeid+useId。锁是为了防止用户点击点赞、收藏、点踩按钮时速度太快，后端反应不过来造成数据丢失或者重复
        String syncString = knowledgeId + "_" + loginUser.getId();

        synchronized (syncString.intern()) {
            esClient.like(knowledgeId,loginUser.getId());
        }
        JSONObject json = getExtendsInfoAfterAction(knowledgeId);
        return Result.OK(json);
    }

    @RequestMapping(value = "/unlike", method = RequestMethod.POST)
    public Result<?> unlikeKnowledge(@RequestParam(name = "knowledgeId", required = true) String knowledgeId) throws IOException {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String syncString = knowledgeId + "_" + loginUser.getId();
        synchronized (syncString.intern()) {
            esClient.unlike(knowledgeId,loginUser.getId());
        }
        JSONObject json = getExtendsInfoAfterAction(knowledgeId);
        return Result.OK(json);
    }

    @RequestMapping(value = "/cancelLike", method = RequestMethod.PUT)
    public Result<?> cancelLike(@RequestParam(name = "knowledgeId", required = true) String knowledgeId) throws IOException {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        esClient.cancleLikeOrcancleUnlike(knowledgeId,loginUser.getId());
        JSONObject json = getExtendsInfoAfterAction(knowledgeId);
        return Result.OK(json);
    }

    @RequestMapping(value = "/cancelUnlike", method = RequestMethod.PUT)
    public Result<?> cancelUnlike(@RequestParam(name = "knowledgeId", required = true) String knowledgeId) throws IOException {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        esClient.cancleLikeOrcancleUnlike(knowledgeId,loginUser.getId());
        JSONObject json = getExtendsInfoAfterAction(knowledgeId);
        return Result.OK(json);
    }

    @RequestMapping(value = "/collect", method = RequestMethod.POST)
    public Result<?> collectKnowledge(@RequestParam(name = "knowledgeId", required = true) String knowledgeId) throws IOException {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String syncString = knowledgeId + "_" + loginUser.getId();
        synchronized (syncString.intern()) {
            esClient.colelct(knowledgeId,loginUser.getId());
        }
        return Result.OK();
    }

    @RequestMapping(value = "/uncollect", method = RequestMethod.POST)
    public Result<?> uncollectKnowledge(@RequestParam(name = "knowledgeId", required = true) String knowledgeId) throws IOException {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String syncString = knowledgeId + "_" + loginUser.getId();
        synchronized (syncString.intern()) {
            esClient.unColelct(knowledgeId,loginUser.getId());
        }
        return Result.OK();
    }


    @RequestMapping(value = "/uncollectBatch", method = RequestMethod.POST)
    public Result<?> uncollectKnowledgeBatch(@RequestParam String knowledgeIds) throws IOException {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        esClient.unColelctBatch(knowledgeIds,loginUser.getId());
        return Result.OK();
    }




    //新增评论
    @RequestMapping(value = "/comment", method = RequestMethod.POST)
    public Result<?> addComment(@RequestBody KnowledgeComment comment) throws IOException {
        if (StringUtils.isEmpty(comment.getUserId())){
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            comment.setUserId(loginUser.getId());
        }
        esClient.addComment(comment.getKnowledgeId(),comment.getUserId(),comment.getComment());
        return Result.OK("评论成功","评论成功");
    }

    //删除评论
    @RequestMapping(value = "/comment", method = RequestMethod.DELETE)
    public Result<?> deleteComment(String id) throws IOException {
        KnowledgeComment commentInDB = esClient.getCommentById(id);
        if (commentInDB == null){
            return Result.OK("删除成功","删除成功");
        }
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (StringUtils.equals(loginUser.getId(), commentInDB.getUserId())){
            esClient.deleteComment(id);
        }else {
            return Result.error("当前用户没有权限删除该评论！");
        }
        return Result.OK("删除成功","删除成功");
    }

    //2、显示一条知识的评论.
    @RequestMapping(value = "/list/comment", method = RequestMethod.GET)
    public Result<?> getCommentsByKnowledgeId(@RequestParam("knowledgeId")String knowledgeId,
                                              @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                              @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) throws IOException {
        pageNo = pageNo < 1 ? 1 : pageNo;
        Page<KnowledgeComment> commentPage = esClient.getCommentByKnowledgeId(knowledgeId, pageNo, pageSize);

        List<String> userIds = commentPage.getRecords().stream().map(KnowledgeComment::getUserId).distinct().collect(Collectors.toList());
        Map<String,String> userIdAndRealname = new HashMap<>();
        Map<String,String> userIdAndImage = new HashMap<>();
        userIds.forEach(id -> {
            SysUsers user = sysUserService.getByUId(id);
            if (user != null){
                userIdAndRealname.put(id,user.getRealname());
                userIdAndImage.put(id,user.getAvatar());
            }
        });
        commentPage.getRecords().forEach(comment -> {
            comment.setUserName(userIdAndRealname.get(comment.getUserId()));
            comment.setAvatar(userIdAndImage.get(comment.getUserId()));
        });

        return Result.OK(commentPage);
    }

    //搜索
    @RequestMapping(value = "/search", method = RequestMethod.GET)
    public Result<?> searchKnowledge(@RequestParam(name = "knowledgeType", required = false) Integer knowledgeType,
                                     @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                     @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                     @RequestParam(name = "keyword") String keyword,
                                     @RequestParam(name = "isFromAlarm", defaultValue = "false") Boolean isFromAlarm) throws IOException {
        pageNo = pageNo < 1 ? 1 : pageNo;
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        List<String> withPermissionTopic = authorityService.getWithPermissionTopic();
        Page<Knowledge> userCollectByUid = new Page<>();
        if (attachment){
            userCollectByUid = esClient.searchKnowledgeContentAneFileContent(loginUser.getId(), pageNo, pageSize, keyword, withPermissionTopic,knowledgeType);
        }else {
            userCollectByUid = esClient.searchKnowledge(loginUser.getId(), pageNo, pageSize, keyword,withPermissionTopic);
        }
        //告警推荐那只需要标题，不需要其他额外信息
        if (!isFromAlarm) {
            userCollectByUid.getRecords().forEach(k -> {
                try {
                    JSONObject json = esClient.getLikeAndUnlikeCountByKid(k.getId());
                    k.setLikeCount(json.getLong("like"));
                    k.setUnlikeCount(json.getLong("unlike"));
                } catch (IOException e) {
                    e.printStackTrace();
                }
            });
            knowledgeUtils.addRealnameForKnowledge(userCollectByUid);
            if (userCollectByUid.getTotal() != 0L) {
                esClient.addKeyword(keyword, loginUser.getId());
            }
        }
        return Result.OK(userCollectByUid);
    }

    //提供一个用户的历史搜索接口，返回一个JSONArray[{时间.标题},{时间.标题},{时间.标题}]
    @RequestMapping(value = "/searchHistory", method = RequestMethod.GET)
    public Result<?> getSearchHistory() throws IOException {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<Map<String,String>> searchHistory = esClient.getSearchHistory(loginUser.getId());
        return Result.OK(searchHistory);
    }
    @RequestMapping(value = "/searchHistory", method = RequestMethod.DELETE)
    public Result<?> deleteSearchHistory(@RequestParam(name = "keyword") String keyword) throws IOException {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        esClient.deleteSearchHistory(loginUser.getId(),keyword);
        return Result.OK();
    }

    //热搜知识列表，需要带知识标题，知识id
    @RequestMapping(value = "/pageView", method = RequestMethod.GET)
    public Result<?> getPageView() throws IOException {
        List<Map<String,String>> pageView = esClient.getPageViewSorted();
        return Result.OK(pageView);
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "删除关联工单")
    @ApiOperation(value = "删除关联工单", notes = "删除关联工单")
    @DeleteMapping(value = "/relation")
    public Result<?> deleteRelation(@RequestParam(name = "id", required = true) String id) throws IOException {
        //鉴权，管理员或者有该主题权限的人才能编辑
        esClient.deleteRelation(id);
        return Result.OK("删除成功!","删除成功!");
    }


    //导出，html转word
    @GetMapping(value = "/word")
    public void toWord(String id, HttpServletRequest request, HttpServletResponse response) throws Exception {
        Knowledge knowledge = esClient.queryKnowledgeById(id);
        if (knowledge == null) {
//            return Result.error("未找到对应数据");
        }
        if (!authorityService.hasAuthority(knowledge)) {
//            return Result.error("您没有权限查看此知识");
        }
        SysUsers user = sysUserService.getByUId(knowledge.getCreateBy());
        if (user != null){
            knowledge.setCreateBy(user.getRealname());
        }
        StringBuilder stringBuilder = new StringBuilder();
//        stringBuilder.append("<html " +
//                "xmlns:v=\"urn:schemas-microsoft-com:vml\" xmlns:o=\"urn:schemas-microsoft-com:office:office\" xmlns:w=\"urn:schemas-microsoft-com:office:word\" xmlns:m=\"http://schemas.microsoft.com/office/2004/12/omml\" xmlns=\"http://www.w3.org/TR/REC-html40\"" + //将版式从web版式改成页面试图
//                ">");
//        stringBuilder.append("<head>" +
//                "<!--[if gte mso 9]><xml><w:WordDocument><w:View>Print</w:View><w:TrackMoves>false</w:TrackMoves><w:TrackFormatting/><w:ValidateAgainstSchemas/><w:SaveIfXMLInvalid>false</w:SaveIfXMLInvalid><w:IgnoreMixedContent>false</w:IgnoreMixedContent><w:AlwaysShowPlaceholderText>false</w:AlwaysShowPlaceholderText><w:DoNotPromoteQF/><w:LidThemeOther>EN-US</w:LidThemeOther><w:LidThemeAsian>ZH-CN</w:LidThemeAsian><w:LidThemeComplexScript>X-NONE</w:LidThemeComplexScript><w:Compatibility><w:BreakWrappedTables/><w:SnapToGridInCell/><w:WrapTextWithPunct/><w:UseAsianBreakRules/><w:DontGrowAutofit/><w:SplitPgBreakAndParaMark/><w:DontVertAlignCellWithSp/><w:DontBreakConstrainedForcedTables/><w:DontVertAlignInTxbx/><w:Word11KerningPairs/><w:CachedColBalance/><w:UseFELayout/></w:Compatibility><w:BrowserLevel>MicrosoftInternetExplorer4</w:BrowserLevel><m:mathPr><m:mathFont m:val=\"Cambria Math\"/><m:brkBin m:val=\"before\"/><m:brkBinSub m:val=\"--\"/><m:smallFrac m:val=\"off\"/><m:dispDef/><m:lMargin m:val=\"0\"/> <m:rMargin m:val=\"0\"/><m:defJc m:val=\"centerGroup\"/><m:wrapIndent m:val=\"1440\"/><m:intLim m:val=\"subSup\"/><m:naryLim m:val=\"undOvr\"/></m:mathPr></w:WordDocument></xml><![endif]-->" +
//                "</head>");
//        stringBuilder.append("<body>");
        // 富文本内容
        stringBuilder.append("<h1>").append(knowledge.getTitle()).append("</h1>");
        String info = "创建人： " + knowledge.getCreateBy() + "       创建时间： " + DateUtil.formatDateHMS(knowledge.getCreateTime());
        stringBuilder.append("<h4>").append(info).append("</h4>");
        stringBuilder.append(knowledge.getPlan());
        String fileName = knowledge.getTitle() + ".docx";
//            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
//            response.setHeader("Content-Disposition", "attachment;filename=" + fileNameEncoder);
        response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
        WordUtil.exportHtmlToWord(request, response, stringBuilder.toString(), fileName);

    }


    @GetMapping(value = "/pdf")
    public Result<?> getHtml(String id) throws IOException, ParseException {
        Knowledge knowledge = esClient.queryKnowledgeById(id);
        if (knowledge == null) {
            return Result.error("未找到对应数据");
        }
        if (!authorityService.hasAuthority(knowledge)){
            return Result.error("您没有权限查看此知识");
        }
        SysUsers user = sysUserService.getByUId(knowledge.getCreateBy());
        if (user != null){
            knowledge.setCreateBy(user.getRealname());
        }

        String info = "创建人：" + knowledge.getCreateBy() + "       创建时间：" + DateUtil.formatDateHMS(knowledge.getCreateTime());
        StringBuilder stringBuilder = new StringBuilder().append("<html>");
        stringBuilder.append("<h1>").append(knowledge.getTitle()).append("</h1>");
        stringBuilder.append("<h4>").append(info).append("</h4>");
        stringBuilder.append(knowledge.getPlan());
        stringBuilder.append("</html>");

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("htmlString",stringBuilder.toString());
        jsonObject.put("htmlTitle",knowledge.getTitle());

        return Result.OK(jsonObject);
    }


}
