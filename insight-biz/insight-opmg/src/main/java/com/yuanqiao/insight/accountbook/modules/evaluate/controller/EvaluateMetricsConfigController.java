package com.yuanqiao.insight.accountbook.modules.evaluate.controller;

import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateMetricsConfig;
import com.yuanqiao.insight.accountbook.modules.evaluate.service.IEvaluateMetricsConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName EvaluateMetricsConfigController
 * @description: 评估指标配置控制器 - 统一增删改查接口
 * @datetime 2025年 07月 28日
 * @version: 1.0
 */
@Api(tags = "评估指标配置")
@RestController
@RequestMapping("/evaluate/metricsConfig")
@Slf4j
public class EvaluateMetricsConfigController extends JeecgController<EvaluateMetricsConfig, IEvaluateMetricsConfigService> {

    @Autowired
    private IEvaluateMetricsConfigService configService;

    // 配置类型常量
    private static final String CONFIG_TYPE_FIELD = "1"; // 指标字段
    private static final String CONFIG_TYPE_RULE = "2";  // 规则配置

    @AutoLog(value = "保存指标配置")
    @ApiOperation(value = "保存指标配置", notes = "统一保存接口，支持字段配置(configType=1)和规则配置(configType=2)")
    @PostMapping(value = "/save")
    public Result<?> saveConfig(@RequestBody Map<String, Object> params) {
        try {
            String metricsId = (String) params.get("metricsId");
            String configType = (String) params.get("configType");
            String json = (String) params.get("json");
            
            if (metricsId == null) {
                return Result.error("指标ID不能为空！");
            }
            if (configType == null) {
                return Result.error("配置类型不能为空！(1-字段配置，2-规则配置)");
            }
            
            boolean result;
            if (CONFIG_TYPE_FIELD.equals(configType)) {
                result = configService.saveFieldConfig(metricsId, json);
            } else if (CONFIG_TYPE_RULE.equals(configType)) {
                result = configService.saveRuleConfig(metricsId, json);
            } else {
                return Result.error("配置类型错误！(1-字段配置，2-规则配置)");
            }
            
            if (result) {
                return Result.OK("保存成功！");
            } else {
                return Result.error("保存失败！");
            }
        } catch (Exception e) {
            log.error("保存指标配置失败", e);
            return Result.error("保存失败：" + e.getMessage());
        }
    }

    @AutoLog(value = "获取指标配置")
    @ApiOperation(value = "获取指标配置", notes = "统一查询接口，可选配置类型(1-字段配置，2-规则配置)，不传configType则返回所有配置")
    @GetMapping(value = "/get/{metricsId}")
    public Result<?> getConfig(@PathVariable("metricsId") String metricsId,
                               @ApiParam(value = "配置类型：1-字段配置，2-规则配置", required = false) 
                               @RequestParam(value = "configType", required = false) String configType) {
        try {
            if (configType == null) {
                // 返回所有配置
                List<EvaluateMetricsConfig> configs = configService.getByMetricsId(metricsId);
                return Result.OK(configs);
            } else {
                // 返回特定类型配置
                if (!CONFIG_TYPE_FIELD.equals(configType) && !CONFIG_TYPE_RULE.equals(configType)) {
                    return Result.error("配置类型错误！(1-字段配置，2-规则配置)");
                }
                EvaluateMetricsConfig config = configService.getByMetricsIdAndType(metricsId, configType);
                return Result.OK(config);
            }
        } catch (Exception e) {
            log.error("获取指标配置失败", e);
            return Result.error("获取失败：" + e.getMessage());
        }
    }

    @AutoLog(value = "获取表单数据")
    @ApiOperation(value = "获取表单数据", notes = "获取指标的表单数据（pointName作为key，fieldData作为value）")
    @GetMapping(value = "/getFormData/{metricsId}")
    public Result<Map<String, Object>> getFormData(@PathVariable("metricsId") String metricsId) {
        try {
            Map<String, Object> formData = configService.getFormData(metricsId);
            return Result.OK(formData);
        } catch (Exception e) {
            log.error("获取表单数据失败", e);
            return Result.error("获取失败：" + e.getMessage());
        }
    }

    @AutoLog(value = "获取完整表单信息")
    @ApiOperation(value = "获取完整表单信息", notes = "获取指标的完整表单信息（配置+数据）")
    @GetMapping(value = "/getCompleteFormInfo/{metricsId}")
    public Result<Map<String, Object>> getCompleteFormInfo(@PathVariable("metricsId") String metricsId) {
        try {
            Map<String, Object> formInfo = configService.getCompleteFormInfo(metricsId);
            return Result.OK(formInfo);
        } catch (Exception e) {
            log.error("获取完整表单信息失败", e);
            return Result.error("获取失败：" + e.getMessage());
        }
    }

    @AutoLog(value = "更新指标配置")
    @ApiOperation(value = "更新指标配置", notes = "统一更新接口，支持字段配置(configType=1)和规则配置(configType=2)")
    @PutMapping(value = "/update")
    public Result<?> updateConfig(@RequestBody Map<String, Object> params) {
        try {
            String metricsId = (String) params.get("metricsId");
            String configType = (String) params.get("configType");
            String json = (String) params.get("json");
            
            if (metricsId == null) {
                return Result.error("指标ID不能为空！");
            }
            if (configType == null) {
                return Result.error("配置类型不能为空！(1-字段配置，2-规则配置)");
            }
            
            boolean result;
            if (CONFIG_TYPE_FIELD.equals(configType)) {
                result = configService.updateFieldConfig(metricsId, json);
            } else if (CONFIG_TYPE_RULE.equals(configType)) {
                result = configService.updateRuleConfig(metricsId, json);
            } else {
                return Result.error("配置类型错误！(1-字段配置，2-规则配置)");
            }
            
            if (result) {
                return Result.OK("更新成功！");
            } else {
                return Result.error("更新失败！");
            }
        } catch (Exception e) {
            log.error("更新指标配置失败", e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }

    @AutoLog(value = "删除指标配置")
    @ApiOperation(value = "删除指标配置", notes = "统一删除接口，可选配置类型(1-字段配置，2-规则配置)，不传configType则删除所有配置")
    @DeleteMapping(value = "/delete/{metricsId}")
    public Result<?> deleteConfig(@PathVariable("metricsId") String metricsId,
                                  @ApiParam(value = "配置类型：1-字段配置，2-规则配置", required = false)
                                  @RequestParam(value = "configType", required = false) String configType) {
        try {
            boolean result;
            if (configType == null) {
                // 删除所有配置
                result = configService.removeByMetricsId(metricsId);
            } else {
                // 删除特定类型配置
                if (!CONFIG_TYPE_FIELD.equals(configType) && !CONFIG_TYPE_RULE.equals(configType)) {
                    return Result.error("配置类型错误！(1-字段配置，2-规则配置)");
                }
                result = configService.removeByMetricsIdAndType(metricsId, configType);
            }
            
            if (result) {
                return Result.OK("删除成功！");
            } else {
                return Result.error("删除失败！");
            }
        } catch (Exception e) {
            log.error("删除指标配置失败", e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    // 便捷方法：获取字段配置JSON
    @AutoLog(value = "获取字段配置JSON")
    @ApiOperation(value = "获取字段配置JSON", notes = "便捷方法：直接获取字段配置JSON")
    @GetMapping(value = "/getFieldConfigJson/{metricsId}")
    public Result<String> getFieldConfigJson(@PathVariable("metricsId") String metricsId) {
        try {
            String configJson = configService.getFieldConfigJson(metricsId);
            return Result.OK(configJson);
        } catch (Exception e) {
            log.error("获取字段配置JSON失败", e);
            return Result.error("获取失败：" + e.getMessage());
        }
    }

    // 便捷方法：获取规则配置JSON
    @AutoLog(value = "获取规则配置JSON")
    @ApiOperation(value = "获取规则配置JSON", notes = "便捷方法：直接获取规则配置JSON")
    @GetMapping(value = "/getRuleConfigJson/{metricsId}")
    public Result<String> getRuleConfigJson(@PathVariable("metricsId") String metricsId) {
        try {
            String configJson = configService.getRuleConfigJson(metricsId);
            return Result.OK(configJson);
        } catch (Exception e) {
            log.error("获取规则配置JSON失败", e);
            return Result.error("获取失败：" + e.getMessage());
        }
    }
}
