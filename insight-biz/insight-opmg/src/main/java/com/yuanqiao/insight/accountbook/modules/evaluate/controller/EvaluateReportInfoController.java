package com.yuanqiao.insight.accountbook.modules.evaluate.controller;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateProjectInfo;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateReportInfo;
import com.yuanqiao.insight.accountbook.modules.evaluate.service.IEvaluateMaterialInfoService;
import com.yuanqiao.insight.accountbook.modules.evaluate.service.IEvaluateMetricsInfoService;
import com.yuanqiao.insight.accountbook.modules.evaluate.service.IEvaluateProjectInfoService;
import com.yuanqiao.insight.accountbook.modules.evaluate.service.IEvaluateReportInfoService;
import com.yuanqiao.insight.common.util.common.VersionSerialUtil;
import com.yuanqiao.insight.service.device.entity.MomgDeviceConfigureRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoDict;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName EvaluateReportInfoController
 * @description: TODO
 * @datetime 2025年 05月 29日 19:28
 * @version: 1.0
 */
@Api(tags = "评估报告信息")
@RestController
@RequestMapping("/evaluate/reportInfo")
@Slf4j
public class EvaluateReportInfoController extends JeecgController<EvaluateReportInfo, IEvaluateReportInfoService> {

    @Autowired
    private IEvaluateProjectInfoService projectInfoService;
    @Autowired
    private IEvaluateMaterialInfoService materialInfoService;
    @Autowired
    private IEvaluateMetricsInfoService metricsInfoService;

    @AutoDict
    @AutoLog(value = "评估报告信息-分页列表查询")
    @ApiOperation(value = "评估报告信息-分页列表查询", notes = "评估报告信息-分页列表查询")
    @GetMapping(value = "/pageList")
    public Result<?> queryPageList(EvaluateReportInfo evaluateReportInfo,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {

        QueryWrapper<EvaluateReportInfo> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(evaluateReportInfo.getProjectId())) {
            queryWrapper.in("project_id", evaluateReportInfo.getProjectId());
        }
        queryWrapper.orderByAsc("create_time");

        Page<EvaluateReportInfo> page = new Page<>(pageNo, pageSize);
        IPage<EvaluateReportInfo> pageList = service.page(page, queryWrapper);
        return Result.OK(pageList);
    }


    @AutoLog(value = "评估报告信息-列表查询")
    @ApiOperation(value = "评估报告信息-列表查询", notes = "评估报告信息-列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryList(EvaluateReportInfo evaluateReportInfo) {
        QueryWrapper<EvaluateReportInfo> queryWrapper = new QueryWrapper<>();

        return Result.OK(service.list(queryWrapper));
    }

    @AutoLog(value = "评估报告信息-新增")
    @ApiOperation(value = "评估报告信息-新增", notes = "评估报告信息-新增")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody EvaluateReportInfo evaluateReportInfo) {
        service.save(evaluateReportInfo);
        return Result.OK("添加成功！");
    }

    @AutoLog(value = "评估报告信息-编辑")
    @ApiOperation(value = "评估报告信息-编辑", notes = "评估报告信息-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody EvaluateReportInfo evaluateReportInfo) {
        service.updateById(evaluateReportInfo);
        return Result.OK("编辑成功!");
    }

    @AutoLog(value = "评估报告信息-通过id删除")
    @ApiOperation(value = "评估报告信息-通过id删除", notes = "评估报告信息-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id") String id) {
        EvaluateReportInfo reportInfo = service.getById(id);
        CommonUtils.deleteFile(Collections.singletonList(reportInfo.getFilePath()));
        service.removeById(id);
        return Result.OK("删除成功!");
    }

    @AutoLog(value = "评估报告信息-批量删除")
    @ApiOperation(value = "评估报告信息-批量删除", notes = "评估报告信息-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids") String ids) {
        List<String> filePaths = service.listByIds(Arrays.asList(ids.split(",")))
                .stream()
                .map(EvaluateReportInfo::getFilePath)
                .collect(Collectors.toList());
        CommonUtils.deleteFile(filePaths);
        this.service.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }


}
