package com.yuanqiao.insight.accountbook.modules.devopsbackuppro.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.yuanqiao.insight.accountbook.modules.devopsBackupTask.entity.DevopsBackupTask;
import com.yuanqiao.insight.accountbook.modules.devopsbackuppro.entity.DevopsBackupPro;

import java.util.List;
import java.util.Map;

/**
 * @Description: 备份策略表
 * @Author: jeecg-boot
 * @Date:   2021-03-22
 * @Version: V1.0
 */
public interface IDevopsBackupProService extends IService<DevopsBackupPro> {

    /**
     * 获取状态
     * @return
     */
    Map<String,String> getState();


    void updateTask(DevopsBackupPro devopsBackupPro, List<DevopsBackupTask> devopsBackupTask);
}
