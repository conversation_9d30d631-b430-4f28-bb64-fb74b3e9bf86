package com.yuanqiao.insight.accountbook.modules.autoInspection.generater;

import com.yuanqiao.insight.accountbook.modules.autoInspection.entity.DevopsAutoInspection;
import com.yuanqiao.insight.accountbook.modules.autoInspectionReport.service.IDevopsAutoInspectionReportService;
import com.yuanqiao.insight.accountbook.modules.autoInspectionReport.service.impl.DevopsAutoInspectionReportServiceImpl;
import com.yuanqiao.insight.accountbook.modules.util.ChartUtils;
import com.yuanqiao.insight.acore.utils.SpringContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.DateUtils;
import org.jeecg.modules.system.entity.SysUsers;
import org.jeecg.modules.system.service.ISysUserService;
import org.jfree.chart.ChartColor;
import org.jfree.chart.ChartFactory;
import org.jfree.chart.JFreeChart;
import org.jfree.chart.block.BlockBorder;
import org.jfree.chart.plot.CategoryPlot;
import org.jfree.chart.plot.PlotOrientation;
import org.jfree.data.category.CategoryDataset;
import org.jfree.data.category.DefaultCategoryDataset;
import org.jfree.data.general.DefaultPieDataset;
import org.jfree.ui.RectangleEdge;

import java.awt.*;
import java.util.Iterator;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: DefaultAIRGenerater      定义默认的自动巡检报告生成组件
 * @projectName jeecg-boot-parent
 * @description: TODO
 * @date 2021/5/15-11:40
 */
@Slf4j
public class DefaultAIRGenerater implements AIRGenerater {

    private final IDevopsAutoInspectionReportService devopsAutoInspectionReportService;
    private final ISysUserService sysUserService;

    public DefaultAIRGenerater() {
        this.devopsAutoInspectionReportService = SpringContextUtil.getApplicationContext().getBean(DevopsAutoInspectionReportServiceImpl.class);
        this.sysUserService = SpringContextUtil.getApplicationContext().getBean(ISysUserService.class);
    }


    /**
     * 生成报告
     *
     * @param autoInspection 自动巡检任务类
     * @throws
     */
    @Override
    public void report(DevopsAutoInspection autoInspection, AITask aITask) throws Exception {
        final SysUsers user = sysUserService.getUserByName(autoInspection.getCreateBy());
        if (user != null) {
            autoInspection.setCreateByName(user.getRealname());
        }
        String tempStr1 = DateUtils.formatDateHMS(autoInspection.getTaskStartTime()).replaceAll(":", "");
        String tempStr2 = tempStr1.replaceAll("-", "");
        String fileNameSuffix = tempStr2.replaceAll(" ", "");
        String filePath = aITask.getPath() + aITask.getAIfilePath() + "/" + tempStr1.split(" ")[0] + "/";
        String fileName = autoInspection.getTaskName() + "_" + fileNameSuffix + ".xls";

        //PDF图片生成
        // 定义数据图片的存取路径的数组
//        String[] photoPath = new String[2];
        //柱形统计
//        photoPath[0] = getBarPhotoPath(aITask, photoPathUrl, fileNameSuffix);
        //扇形统计
//        photoPath[1] = getPiePhotoPath(aITask, photoPathUrl, fileNameSuffix);

        AIRGenetaterUtil airGenetaterUtil = new AIRGenetaterUtil();

        // 将自动巡检报告内容存入本地excel文件中
        String filePathResult = airGenetaterUtil.exportAIResultToExcel(autoInspection, aITask, filePath, fileName);
        log.error("AAAA巡检任务--生成报告 完毕！！！！");

        // 将自动巡检报告内容存入本地pdf文件中
//        airGenetaterUtil.createPDF(aITask, autoInspection, filePath + ".pdf", photoPath);

        //删除临时生成的图片文件
//        CallPhantomJS.deleteScreenShort(photoPath);

        //添加报表
        String[] filePaths = new String[1];
        filePaths[0] = filePathResult;

        devopsAutoInspectionReportService.addDevopsAutoInspectionReport(autoInspection, filePaths);

    }


    /**
     * 生成柱状图
     *
     * @param aITask
     * @param photoPathUrl
     * @param fileNameSuffix
     * @return
     */
    private String getBarPhotoPath(AITask aITask, String photoPathUrl, String fileNameSuffix) {
        ChartUtils chartUtils = new ChartUtils();

        Map<String, AIRGeneraterTypeResult> generaterResult = aITask.getAIRGeneraterResult().getGeneraterResult();

        // 1. 得到数据
        CategoryDataset dataset = getDataSet(generaterResult);

        // 2. 构造chart
        JFreeChart chart = ChartFactory.createBarChart(
                "巡检告警分类情况统计", // 图表标题
                "", // 目录轴的显示标签--横轴
                "", // 数值轴的显示标签--纵轴
                dataset, // 数据集
                PlotOrientation.VERTICAL, // 图表方向：水平、
                true, // 是否显示图例(对于简单的柱状图必须
                false, // 是否生成工具
                false // 是否生成URL链接
        );

        // 3.chart图表设置
        CategoryPlot cp = chart.getCategoryPlot();
        cp.setBackgroundPaint(ChartColor.WHITE); // 背景色设置
        cp.setRangeGridlinePaint(ChartColor.GRAY); // 网格线色设置

        // 4. 处理chart中文显示问题
        chartUtils.processChart(chart);

        // 5. chart输出图片
        String barStr = chartUtils.writeChartAsImage(chart, photoPathUrl, "bar" + fileNameSuffix);

        return barStr;
    }


    /**
     * 生成扇形
     *
     * @param aITask
     * @param photoPathUrl
     * @param fileNameSuffix
     * @return
     */
    public String getPiePhotoPath(AITask aITask, String photoPathUrl, String fileNameSuffix) {

        Map<String, AIRGeneraterTypeResult> generaterResult = aITask.getAIRGeneraterResult().getGeneraterResult();

        // 1. 得到数据
        DefaultPieDataset dataset = createDataset(generaterResult);

        // 2：创建Chart
        JFreeChart chart = ChartFactory.createPieChart("巡检告警分类情况统计", dataset);

        // 3: 设置抗锯齿，防止字体显示不清楚
        ChartUtils.setAntiAlias(chart);// 抗锯齿

        // 4: 对图表进行渲染
        ChartUtils.setPieRender(chart.getPlot());
        // 设置标注无边框
        chart.getLegend().setFrame(new BlockBorder(Color.WHITE));
        // 标注位于右侧
        chart.getLegend().setPosition(RectangleEdge.RIGHT);

        //Plot plot = chart.getPlot();
        //简单标签,不绘制线条
        // plot.setSimpleLabels(true);
        //不显示数字
        // plot.setLabelGenerator(null);

        // 5. chart输出图片
        String pieStr = ChartUtils.writeChartAsImage(chart, photoPathUrl, "pie" + fileNameSuffix);

        return pieStr;
    }


    /**
     * 组装数据
     *
     * @param generaterResult
     * @return
     */
    public DefaultPieDataset createDataset(Map<String, AIRGeneraterTypeResult> generaterResult) {

        Iterator<String> iterator = generaterResult.keySet().iterator();
        String[] categories = new String[generaterResult.size() - 1];
        Object[] datas = new Object[generaterResult.size() - 1];

        int i = 0;
        while (iterator.hasNext()) {
            String type = iterator.next();
            if (type.equals("total")) {
                continue;
            }
            AIRGeneraterTypeResult geneTypeResult = generaterResult.get(type);
            categories[i] = geneTypeResult.getTypeName();
            datas[i] = geneTypeResult.getTotalNum();
            i++;
        }

        DefaultPieDataset dataset = ChartUtils.createDefaultPieDataset(categories, datas);
        return dataset;
    }


    /**
     * 获取一个演示用的组合数据集对象
     *
     * @return
     */
    private static CategoryDataset getDataSet(Map<String, AIRGeneraterTypeResult> generaterResult) {
        DefaultCategoryDataset dataset = new DefaultCategoryDataset();

        Iterator<String> iterator = generaterResult.keySet().iterator();

        while (iterator.hasNext()) {
            String type = iterator.next();
            if (type.equals("total")) {
                continue;
            }
            AIRGeneraterTypeResult geneTypeResult = generaterResult.get(type);
            dataset.addValue(geneTypeResult.getErrorNum(), "严重告警", geneTypeResult.getTypeName());
            dataset.addValue(geneTypeResult.getNormalNum(), "正常", geneTypeResult.getTypeName());
            dataset.addValue(geneTypeResult.getUnconnNum(), "未连接", geneTypeResult.getTypeName());
            dataset.addValue(geneTypeResult.getWarningNum(), "一般告警", geneTypeResult.getTypeName());

        }
        return dataset;
    }


}
