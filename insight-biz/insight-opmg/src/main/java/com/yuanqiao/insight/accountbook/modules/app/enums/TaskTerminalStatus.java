package com.yuanqiao.insight.accountbook.modules.app.enums;

import lombok.Getter;

@Getter
public enum TaskTerminalStatus {

    //未开始
    NOT_STARTED("0"),
    //进行中
    RUNNING("1"),
    //重试中
    RETRY("2"),
    //执行失败
    FAILED("3"),
    //执行成功
    SUCCESS("4"),
    //不匹配
    MISMATCH("-1");

    private final String code;

    TaskTerminalStatus(String code) {
        this.code = code;
    }
}
