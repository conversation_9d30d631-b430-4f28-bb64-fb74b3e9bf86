package com.yuanqiao.insight.accountbook.modules.devopsipsegsent.job;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.accountbook.modules.devopsipsegsent.entity.DevopsIpSegsent;
import com.yuanqiao.insight.accountbook.modules.devopsipsegsent.service.IDevopsIpSegsentService;
import com.yuanqiao.insight.acore.collector.Collector;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.acore.scheduler.Scheduler;
import com.yuanqiao.insight.acore.scheduler.SchedulerManagerInter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @title: IPSegsentSchedulerManager
 * @projectName jeecg-boot-parent
 * @description: TODO
 * @date 2021/5/29-9:24
 */
@Slf4j
@Component
public class IPSegsentSchedulerManager implements SchedulerManagerInter, CommandLineRunner {

    //监控配置
    @Autowired
    private IDevopsIpSegsentService devopsIpSegsentService;
    @Autowired
    private Scheduler scheduler;

    @Override
    public void run(String... args) throws Exception {
        //开一个线程
        Thread thread = new Thread(()->{
            //TODO   测试或添加将此处开启
            //init();
        });
        thread.start();
    }
    @Override
    public void init() {
        log.info("IPSegsentSchedulerManager ip监控配置加入定时任务开始");
        List<DevopsIpSegsent> devopsIpSegsents = devopsIpSegsentService.list();
        for (DevopsIpSegsent devopsIpSegsent:devopsIpSegsents) {
            String cron = devopsIpSegsent.getExecuteCron();
            if(!"* * * * * ? *".equals(cron)){
                IPSegsentJob ipSegsentJob = new IPSegsentJob();
                ipSegsentJob.setTaskId(devopsIpSegsent.getId());
                int len = cron.lastIndexOf(" ");
                this.addTask(devopsIpSegsent.getId(),cron.substring(0,len),ipSegsentJob);
            }
        }
        log.info("IPSegsentSchedulerManager ip监控配置加入定时任务结束");
    }

    @Override
    public void addTask(String key, String cron, Collector collector) {
        scheduler.add(key,cron,collector::execute);
    }

    @Override
    public void removeTask(String key) {

    }

    @Override
    public void setStatus() {

    }

    @Override
    public void setDeviceUp() {

    }

    @Override
    public void setDeviceDown(Device device) {

    }

    @Override
    public void publishDataEvent(String key) {

    }

    @Override
    public void publishMatrixPort(JSONObject dataJsonObject) {

    }

    @Override
    public void putIntoContainer(Device device) {

    }

    @Override
    public void setStatusCache(Device device, JSONObject jsonObject) {

    }


}
