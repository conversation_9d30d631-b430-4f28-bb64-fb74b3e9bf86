package com.yuanqiao.insight.accountbook.modules.app.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.accountbook.modules.app.entity.Policy;
import com.yuanqiao.insight.accountbook.modules.app.entity.PolicyScope;
import com.yuanqiao.insight.accountbook.modules.app.service.IPolicyScopeService;
import com.yuanqiao.insight.accountbook.modules.app.service.IPolicyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * @Description: 软件策略
 * @Author: jeecg-boot
 * @Date: 2024-11-14
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "软件策略")
@RestController
@RequestMapping("/software/policy")
public class PolicyController extends JeecgController<Policy, IPolicyService> {
    @Autowired
    private IPolicyService policyService;

    @Autowired
    private IPolicyScopeService policyScopeService;

    /**
     * 分页列表查询
     *
     * @param policy
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "软件策略-分页列表查询")
    @ApiOperation(value = "软件策略-分页列表查询", notes = "软件策略-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(Policy policy,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<Policy> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .eq(pageSize < 0, "is_effective", "1")
                .like(StringUtils.isNotBlank(policy.getName()), "name", "%" + policy.getName() + "%")
                .eq("del_flag", 0)
                .orderByDesc("create_time");
        Page<Policy> page = new Page<Policy>(pageNo, pageSize);
        IPage<Policy> pageList = policyService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param policy
     * @return
     */
    @AutoLog(value = "软件策略-添加")
    @ApiOperation(value = "软件策略-添加", notes = "软件策略-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody Policy policy) {
        policyService.savePolicy(policy);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param policy
     * @return
     */
    @AutoLog(value = "软件策略-编辑")
    @ApiOperation(value = "软件策略-编辑", notes = "软件策略-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody Policy policy) {
        policyService.updatePolicy(policy);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "软件策略-通过id删除")
    @ApiOperation(value = "软件策略-通过id删除", notes = "软件策略-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id") String id) {
        policyService.removePolicys(Collections.singletonList(id));
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "软件策略-批量删除")
    @ApiOperation(value = "软件策略-批量删除", notes = "软件策略-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids") String ids) {
        this.policyService.removePolicys(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "软件策略-通过id查询")
    @ApiOperation(value = "软件策略-通过id查询", notes = "软件策略-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        Policy policy = policyService.getById(id);
        if (policy == null) {
            return Result.error("该策略不存在");
        }
        LambdaQueryWrapper<PolicyScope> queryWrapper = new LambdaQueryWrapper<PolicyScope>().eq(PolicyScope::getPolicyId, id);
        queryWrapper.eq(PolicyScope::getDelFlag, 0);
        List<PolicyScope> list = policyScopeService.list(queryWrapper);
        Map<String, String> map = new HashMap<>(3);
        for (PolicyScope scope : list) {
            map.put(scope.getScopeType(), scope.getRelateId());
        }
        policy.setPolicyScopes(JSONObject.toJSONString(map));
        return Result.OK(policy);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param policy
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Policy policy) {
        return super.exportXls(request, policy, Policy.class, "软件策略");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Policy.class);
    }

}
