package com.yuanqiao.insight.accountbook.modules.devopsBackupTask.excutor;

import com.yuanqiao.insight.accountbook.modules.devopsBackupScanTask.entity.DevopsBackupScanTask;
import com.yuanqiao.insight.accountbook.modules.devopsBackupTask.actuator.BUActuator;
import com.yuanqiao.insight.accountbook.modules.devopsBackupTask.dispatcher.BUDispatcher;
import com.yuanqiao.insight.accountbook.modules.devopsBackupTask.dispatcher.DefaultBUDispatcher;
import com.yuanqiao.insight.accountbook.modules.devopsBackupTask.entity.BackupTask;
import com.yuanqiao.insight.accountbook.modules.devopsBackupTask.entity.DevopsBackupTask;

/**
 * <AUTHOR>
 * @title: DefaultBUExecutor       备份执行任务执行器对象
 * @projectName jeecg-boot-parent
 * @description: TODO
 * @date 2021/5/24-15:48
 */
public class DefaultBUExecutor implements BUExecutor {

    // 备份任务调度器
    private BUDispatcher dispatcher;

    public DefaultBUExecutor() {
        this.dispatcher = new DefaultBUDispatcher();
    }


    /**
     * 备份任务执行方法
     *
     * @param devopsBackupTask 备份任务类
     * @param aIfilePath       临时文件地址
     */
    @Override
    public void execute(DevopsBackupTask devopsBackupTask, String aIfilePath, String type) throws Exception {
        //新建任务
        BackupTask dackupTask = new BackupTask();
        dackupTask.setDevopsBackupTask(devopsBackupTask);
        dackupTask.setAIfilePath(aIfilePath);
        dackupTask.setType(type);
        //选择执行器
        BUActuator buActuator = dispatcher.dispatch(dackupTask);
        if (null != buActuator) {
            buActuator.run(dackupTask);
        }
    }


    /**
     * 扫描任务执行方法
     *
     * @param devopsBackupScanTask 扫描任务类
     * @param aIfilePath           临时文件地址
     * @param type                 任务类型
     * @throws Exception
     */
    @Override
    public void execute(DevopsBackupScanTask devopsBackupScanTask, String aIfilePath, String type) throws Exception {
        //新建任务
        BackupTask dackupTask = new BackupTask();
        dackupTask.setDevopsBackupScanTask(devopsBackupScanTask);
        dackupTask.setAIfilePath(aIfilePath);
        dackupTask.setType(type);
        //选择执行器
        BUActuator buActuator = dispatcher.dispatch(dackupTask);
        if (null != buActuator) {
            buActuator.run(dackupTask);
        }
    }
}
