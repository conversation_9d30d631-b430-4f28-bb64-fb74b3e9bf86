package com.yuanqiao.insight.accountbook.modules.evaluate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateMetricsScoreRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName EvaluateMetricsScoreRecordMapper
 * @description: 评估指标打分记录Mapper
 * @datetime 2025年 07月 28日
 * @version: 1.0
 */
@Mapper
public interface EvaluateMetricsScoreRecordMapper extends BaseMapper<EvaluateMetricsScoreRecord> {
    
    /**
     * 根据项目ID和指标ID查询打分记录
     */
    @Select("SELECT * FROM devops_evaluate_metrics_score_record WHERE project_id = #{projectId} AND metrics_id = #{metricsId} ORDER BY evaluate_time DESC")
    List<EvaluateMetricsScoreRecord> selectByProjectAndMetrics(@Param("projectId") String projectId, @Param("metricsId") String metricsId);
    
    /**
     * 统计项目指标的总分信息
     */
    @Select("SELECT " +
            "COUNT(*) as total_rules, " +
            "SUM(CASE WHEN rule_result = 1 THEN 1 ELSE 0 END) as passed_rules, " +
            "SUM(score) as total_score, " +
            "SUM(max_score) as total_max_score, " +
            "ROUND(SUM(score) / SUM(max_score) * 100, 2) as pass_rate " +
            "FROM devops_evaluate_metrics_score_record " +
            "WHERE project_id = #{projectId} AND metrics_id = #{metricsId}")
    Map<String, Object> countScoreByProjectAndMetrics(@Param("projectId") String projectId, @Param("metricsId") String metricsId);
    
    /**
     * 获取最新的评估记录
     */
    @Select("SELECT * FROM devops_evaluate_metrics_score_record " +
            "WHERE project_id = #{projectId} AND metrics_id = #{metricsId} " +
            "ORDER BY evaluate_time DESC LIMIT 1")
    EvaluateMetricsScoreRecord selectLatestByProjectAndMetrics(@Param("projectId") String projectId, @Param("metricsId") String metricsId);
}
