package com.yuanqiao.insight.accountbook.modules.software.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yuanqiao.insight.accountbook.modules.app.entity.SoftwarePatchInfo;
import com.yuanqiao.insight.accountbook.modules.app.service.ISoftwarePatchInfoService;
import com.yuanqiao.insight.common.util.common.LocalCacheUtils;
import com.yuanqiao.insight.common.util.common.RedisUtils;
import com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice;
import com.yuanqiao.insight.monitoring.modules.terminal.mapper.TerminalDeviceMapper;
import com.yuanqiao.insight.service.software.entity.DevopsOtaClientInfo;
import com.yuanqiao.insight.service.software.entity.DevopsOtaInstallInfo;
import com.yuanqiao.insight.service.software.entity.DevopsPatchDept;
import com.yuanqiao.insight.service.software.mapper.DevopsOtaClientInfoMapper;
import com.yuanqiao.insight.service.software.mapper.DevopsOtaInstallInfoMapper;
import com.yuanqiao.insight.service.software.mapper.DevopsPatchDeptMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.constant.CommonConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * @Description: 获取软件基本信息
 * @Author: jeecg-boot
 * @Date: 2021-03-11
 * @Version: V1.0
 */
@Api(tags = "获取软件基本信息")
@RestController
@RequestMapping("/patch/software")
@Slf4j
public class SoftwareController {
    @Autowired
    private ISoftwarePatchInfoService devopePatchInfoService;
    @Value(value = "${server.servlet.context-path}")
    private String path;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private DevopsPatchDeptMapper devopsPatchDeptMapper;
    @Autowired
    private TerminalDeviceMapper terminalDeviceMapper;

    private static int maxPool = 10;
    private static int currentCounts = 0;
    //    private static int maxCount = 3000;
    //某一设备已下发补丁的次数最大值
    private static int redisMaxCount = 3;

    //初始化独占锁
    Lock lock = new ReentrantLock();

    /**
     * 获取软件列表
     *
     * @return 软件版本信息
     * <p>
     * [{
     * "downloadPath": "/insight-api/patch/software/wps.jar", //软件下载地址
     * "installShellPath": "", //按照脚本路径
     * "name": "wps", //软件名称
     * "version": "1", //软件版本号
     * }]
     */
    //@AutoLog(value = "软件管理表-软件列表")
    @ApiOperation(value = "软件管理表-软件列表", notes = "软件管理表-软件列表---包含单位限制")
    @GetMapping(value = "/list1")
    public String list(@RequestParam(name = "frawork", defaultValue = "") String frawork, HttpServletRequest request, String uniqueId, String mac, String version, Integer clientType) {

        log.debug("clientIp ###***###*** " + getIpAddr(request));
        log.debug("accessUrl ###***###*** " + request.getRequestURL());
        log.debug("accessQueryString ###***###*** " + request.getQueryString());
        log.debug("version ###***###*** " + version);

        try {
            lock.lock();

            //遵义定制，只要收到客户端升级信息申报就不再允许该终端升级
            LocalCacheUtils localCacheUtils = LocalCacheUtils.getInstance();
            if (localCacheUtils.ifContainsKey(uniqueId) || localCacheUtils.ifContainsKey(mac)) {
//                log.debug("系统平台已接收到IP为 " + getIpAddr(request) + " -- terminalCode -- " + uniqueId + " -- mac -- " + mac + " 的客户端升级信息申报，不再允许该终端升级！");
//                return "{}";
            }

            if (version != null && version.equals("3.0.6")) {
                log.debug("该终端为3.0.6版本，仍发放升级许可 version--" + version);
            }

            log.debug("currentpool为:: " + maxPool + " -- currentCounts为:: " + currentCounts);

            //redis countKey 当前设备已分发次数
            String countKey = CommonConstant.DEVOPE_PATCH_REQUESTCOUNT + ":" + mac + "_";
            Set<String> exitCount = redisUtils.keys(countKey + "*");
            int redisCount = 0;
            if (exitCount != null) {
                redisCount = exitCount.size();
            }

            if (maxPool > 0 && redisCount < redisMaxCount) {
//            if (true) {
                log.debug("通过判断，开始进行下一步！当前 redisCount = " + redisCount + "；maxPool = " + maxPool);
                //根据单位限制哪些终端允许更新补丁
                TerminalDevice terminalDevice = null;
                try {
                    if (StringUtils.isNotEmpty(uniqueId)) {
                        terminalDevice = terminalDeviceMapper.selectOne(new QueryWrapper<TerminalDevice>().eq("unique_code", uniqueId));
                    } else if (StringUtils.isNotEmpty(mac)) {
                        terminalDevice = terminalDeviceMapper.selectOne(new QueryWrapper<TerminalDevice>().eq("unique_code", mac));
                    }
                } catch (Exception e) {
                    log.debug("查询终端 " + mac + " 时出现异常！", e);
                    return "{}";
                }
                if (terminalDevice != null && StringUtils.isNotEmpty(terminalDevice.getDeptId())) {
                    List<DevopsPatchDept> patchDeptList = devopsPatchDeptMapper.selectList(new QueryWrapper<DevopsPatchDept>().eq("dept_id", terminalDevice.getDeptId()));
                    if (patchDeptList != null && !patchDeptList.isEmpty()) {
                        //redis key
                        String patchKey = CommonConstant.DEVOPE_PATCH_INFO + ":" + frawork;
                        countKey += System.currentTimeMillis();

                        String st = (String) redisUtils.get(patchKey);
                        JSONObject patchObject = JSONObject.parseObject(st);
                        if (patchObject != null && !patchObject.isEmpty()) {
                            if (!"{}".equals(st)) {
                                maxPool -= 1;
                                currentCounts++;
                                //将当前设备请求信息存入redis
                                redisUtils.set(countKey, 1);
                                redisCount++;
                                log.debug("向IP为 " + getIpAddr(request) + " -- mac为 " + mac + " 的终端通过 Redis 分发了一个软件补丁包；redisCount + 1, 当前RedisCount的值为：" + redisCount + "； maxPool - 1，当前maxPool的值为：" + maxPool);
                                log.debug(st);
                                return st;
                            }
                        }


                        Map<String, Map<String, String>> map = new HashMap<>();
                        QueryWrapper<SoftwarePatchInfo> queryWrapper = new QueryWrapper<>();
                        queryWrapper.eq("frawork", frawork);
                        queryWrapper.eq("effect", 1);
                        List<String> patchIds = patchDeptList.stream().map(DevopsPatchDept::getPatchId).collect(Collectors.toList());
                        queryWrapper.in("id", patchIds);
                        queryWrapper.orderByAsc("create_time");
                        List<SoftwarePatchInfo> patchInfoList = devopePatchInfoService.list(queryWrapper);

                        Map<String, List<SoftwarePatchInfo>> mapList = patchInfoList.stream().filter(a -> !"0".equals(a.getEffect())).collect(Collectors.groupingBy(SoftwarePatchInfo::getPatchName));
                        Iterator<String> iteratorKey = mapList.keySet().iterator();
                        while (iteratorKey.hasNext()) {
                            String key = iteratorKey.next();
                            List<SoftwarePatchInfo> cList = mapList.get(key);
                            long devopeTime = cList.stream().mapToLong(DevopePatchInfo -> DevopePatchInfo.getCreateTime().getTime()).max().getAsLong();
                            for (SoftwarePatchInfo softwarePatchInfo : cList) {
                                if (devopeTime == softwarePatchInfo.getCreateTime().getTime()) {
                                    Map<String, String> dMap = new HashMap<>();
                                    dMap.put("downloadPath", path + "/sys/common/static/" + softwarePatchInfo.getPatchFileName());//软件下载地址
                                    if (StringUtils.isNotEmpty(softwarePatchInfo.getScriptFileName())) {
                                        dMap.put("installShellPath", path + "/sys/common/static/" + softwarePatchInfo.getScriptFileName());//脚本下载地址
                                    } else {
                                        dMap.put("installShellPath", "");//脚本下载地址
                                    }
                                    dMap.put("name", softwarePatchInfo.getPatchName());//软件名称
                                    dMap.put("version", softwarePatchInfo.getPatchVersion());//版本
                                    dMap.put("resource_type", softwarePatchInfo.getResourceType());//设备类型
                                    dMap.put("filehash", softwarePatchInfo.getFileHash());//升级包hash值
                                    dMap.put("scripthash", softwarePatchInfo.getScriptHash());//脚本hash值
                                    map.put(softwarePatchInfo.getPatchName(), dMap);
                                }
                            }
                        }
                        st = JSONObject.toJSONString(map);
                        if (!"{}".equals(st)) {
                            currentCounts++;
                            redisCount++;
                            maxPool -= 1;
                            //将当前补丁包信息存入redis
                            redisUtils.set(patchKey, st);
                            //将当前设备请求信息存入redis
                            redisUtils.set(countKey, 1);
                            log.debug("向IP为 " + getIpAddr(request) + " -- mac为 " + mac + " 的终端通过 SQL 分发了一个软件补丁包；redisCount + 1, 当前RedisCount的值为：" + redisCount + "； maxPool - 1，当前maxPool的值为：" + maxPool);
                            log.debug(st);
                        } else {
                            log.debug("未找到匹配的补丁升级包，终止当前分发任务！");
                        }
                        return st;
                    } else {
                        log.debug("IP为 " + getIpAddr(request) + " -- mac为 " + mac + " 的终端不在升级单位范围！");
                        return "{}";
                    }
                } else {
                    log.debug("数据库中未查询到终端 " + mac + " ，或该终端未绑定单位，不予下发！");
                    return "{}";
                }
            } else {
                log.debug("IP为 " + getIpAddr(request) + " -- mac为 " + mac + " 的终端不满足判断条件，终止当前分发任务！");
                return "{}";
            }
        } catch (Exception e) {
            log.debug("-----------软件补丁分发异常！", e);
            return "{}";
        } finally {
            lock.unlock();
        }
    }


    @ApiOperation(value = "软件管理表-增加缓存池大小", notes = "软件管理表-增加缓存池大小")
    @GetMapping(value = "/addPoolSize")
    public String addPoolSize(@RequestParam(name = "poolsize", defaultValue = "") int poolsize) {
        log.debug("addPoolSize ###***###*** " + poolsize);
        maxPool = maxPool + poolsize;
        return "{\"maxpool\":" + maxPool + ",\"currentCounts\":" + currentCounts + "}";
    }

    @ApiOperation(value = "软件管理表-设置缓存池大小", notes = "软件管理表-设置缓存池大小")
    @GetMapping(value = "/setPool")
    public String setPool(@RequestParam(name = "poolsize", defaultValue = "") int poolsize) {
        log.debug("setPool ###***###*** " + poolsize);
        maxPool = poolsize;
        return "{\"maxpool\":" + maxPool + ",\"currentCounts\":" + currentCounts + "}";
    }

    @ApiOperation(value = "软件管理表-获取当前缓存池容量", notes = "软件管理表-获取当前缓存池容量")
    @GetMapping(value = "/getCurrentPool")
    public int getCurrentPool() {
        log.debug("getCurrentPool ###***###*** " + maxPool);
        return maxPool;
    }

//    @ApiOperation(value = "软件管理表-设置可下发的最大值", notes = "软件管理表-设置可下发的最大值")
//    @GetMapping(value = "/setMaxCount")
//    public String setMaxCount(@RequestParam(name = "countsize", defaultValue = "") int countsize) {
//        log.debug("setMaxCount ###***###*** " + countsize);
//        maxCount = countsize;
//        return "{\"maxCount\":" + maxCount + "," + "\"currentCounts\":" + currentCounts + "}";
//    }

    @ApiOperation(value = "软件管理表-获取当前已下发值", notes = "软件管理表-获取当前已下发值")
    @GetMapping(value = "/getCurrentCount")
    public int getCurrentCount() {
        log.debug("getCurrentCount ###***###*** " + currentCounts);
        return currentCounts;
    }

    @ApiOperation(value = "软件管理表-设置Redis计数允许的最大值", notes = "软件管理表-设置Redis计数允许的最大值")
    @GetMapping(value = "/setRedisMaxCount")
    public String setRedisMaxCount(@RequestParam(name = "redisMaxCount", defaultValue = "") int maxCount) {
        log.debug("setRedisMaxCount ###***###*** " + maxCount);
        redisMaxCount = maxCount;
        return "{\"redisMaxCount\":" + redisMaxCount + "}";
    }

    @ApiOperation(value = "软件管理表-获取当前Redis计数允许的最大值", notes = "软件管理表-获取当前Redis计数允许的最大值")
    @GetMapping(value = "/getRedisMaxCount")
    public String getRedisMaxCount() {
        log.debug("getRedisMaxCount ###***###*** " + redisMaxCount);
        return "{\"redisMaxCount\":" + redisMaxCount + "}";
    }

    @ApiOperation(value = "软件管理表-获取当前设备已分发次数", notes = "软件管理表-获取当前设备已分发次数")
    @GetMapping(value = "/getRedisCountByCode")
    public String getRedisCountByCode(@RequestParam(name = "terminalCode", defaultValue = "") String terminalCode) {
        log.debug("getRedisCountByCode() ###***###*** " + terminalCode);
        String countKey = CommonConstant.DEVOPE_PATCH_REQUESTCOUNT + ":" + terminalCode + "_";
        Set<String> exitCount = redisUtils.keys(countKey + "*");
        int redisCount = 0;
        if (exitCount != null) {
            redisCount = exitCount.size();
        }
        return "{\"currentRedisCount\":" + redisCount + "}";
    }

    @ApiOperation(value = "软件管理表-清空所有redis计数键", notes = "软件管理表-清空所有redis计数键")
    @GetMapping(value = "/delRedisCountKey")
    public Result<?> delRedisCountKey() {
        try {
            Set<String> exitCount = redisUtils.keys(CommonConstant.DEVOPE_PATCH_REQUESTCOUNT + ":" + "*");
            if (exitCount != null) {
                String[] strings = exitCount.toArray(new String[exitCount.size()]);
                redisUtils.del(strings);
            }
        } catch (Exception e) {
            log.debug("清空所有redis计数键异常！", e);
            return Result.error("清空所有redis计数键异常！");
        }
        return Result.OK("清空所有redis计数键成功！");
    }

    @ApiOperation(value = "软件管理表-上报临时信息", notes = "软件管理表-上报临时信息")
    @GetMapping(value = "/reportcurrent")
    public String reportcurrent(@RequestParam(name = "reinfo", defaultValue = "") String reinfo) {
        log.debug("reinfo ###***###*** " + reinfo);
        return reinfo;
    }


    @ApiOperation(value = "软件管理表-软件列表", notes = "软件管理表-软件列表--无单位限制版")
    @GetMapping(value = "/list")
    public String list1(@RequestParam(name = "frawork", defaultValue = "") String frawork, HttpServletRequest request, String uniqueId, String mac, String version, Integer clientType) {

        log.debug("clientIp ###***###*** " + getIpAddr(request));
        log.debug("mac ###***###*** " + mac);
        log.debug("accessUrl ###***###*** " + request.getRequestURL());
        log.debug("frawork ###***###*** " + frawork);
        log.debug("version ###***###*** " + version);
        try {
            lock.lock();

            //遵义定制，只要收到客户端升级信息申报就不再允许该终端升级
            LocalCacheUtils localCacheUtils = LocalCacheUtils.getInstance();
            if (localCacheUtils.ifContainsKey(uniqueId) || localCacheUtils.ifContainsKey(mac)) {
//                log.debug("系统平台已接收到IP为 " + getIpAddr(request) + "--terminalcode--" + uniqueId + "--mac--" + mac + " 的客户端升级信息申报，不再允许该终端升级！");
//                return "{}";
            }
            if (version != null && version.equals("3.0.6")) {
                log.debug("该终端为3.0.6版本，仍发放升级许可 version--" + version);
            }

            log.debug("maxPool为:: " + maxPool + " -- currentCounts为:: " + currentCounts);

            //redis countKey
            // TODO: RedisKey中只包含设备Mac，不包含设备当前使用的补丁版本
            String countKey = CommonConstant.DEVOPE_PATCH_REQUESTCOUNT + ":" + mac + "_";
            Set<String> exitCount = redisUtils.keys(countKey + "*");
            int redisCount = 0;
            if (exitCount != null) {
                redisCount = exitCount.size();
            }

            if (maxPool > 0 && (redisCount < redisMaxCount)) {
//            if (true) {
                log.debug("通过判断，开始进行下一步！当前 redisCount = " + redisCount + "；maxPool = " + maxPool);
                //redis key
                String patchKey = CommonConstant.DEVOPE_PATCH_INFO + ":" + frawork;
                countKey += System.currentTimeMillis();

                String st = (String) redisUtils.get(patchKey);
                JSONObject patchObject = JSONObject.parseObject(st);
                if (patchObject != null && !patchObject.isEmpty()) {
                    if (!"{}".equals(st)) {
                        maxPool -= 1;
                        currentCounts++;
                        //将当前设备请求信息存入redis
                        redisUtils.set(countKey, 1);
                        redisCount++;
                        log.debug("向IP为 " + getIpAddr(request) + " 的终端通过 Redis 分发了一个软件补丁包；redisCount + 1, 当前RedisCount的值为：" + redisCount + "； maxPool - 1，当前maxPool的值为：" + maxPool);
                        log.debug(st);
                        return st;
                    }
                }


                Map<String, Map<String, String>> map = new HashMap<>();
                QueryWrapper<SoftwarePatchInfo> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("frawork", frawork);
                queryWrapper.eq("effect", 1);
                queryWrapper.orderByAsc("create_time");
                List<SoftwarePatchInfo> patchInfoList = devopePatchInfoService.list(queryWrapper);

                Map<String, List<SoftwarePatchInfo>> mapList = patchInfoList.stream().filter(a -> !"0".equals(a.getEffect())).collect(Collectors.groupingBy(SoftwarePatchInfo::getPatchName));
                Iterator<String> iteratorKey = mapList.keySet().iterator();
                while (iteratorKey.hasNext()) {
                    String key = iteratorKey.next();
                    List<SoftwarePatchInfo> cList = mapList.get(key);
                    long devopeTime = cList.stream().mapToLong(DevopePatchInfo -> DevopePatchInfo.getCreateTime().getTime()).max().getAsLong();
                    for (SoftwarePatchInfo softwarePatchInfo : cList) {
                        if (devopeTime == softwarePatchInfo.getCreateTime().getTime()) {
                            Map<String, String> dMap = new HashMap<>();
                            dMap.put("downloadPath", path + "/sys/common/static/" + softwarePatchInfo.getPatchFileName());//软件下载地址
                            if (StringUtils.isNotEmpty(softwarePatchInfo.getScriptFileName())) {
                                dMap.put("installShellPath", path + "/sys/common/static/" + softwarePatchInfo.getScriptFileName());//脚本下载地址
                            } else {
                                dMap.put("installShellPath", "");//脚本下载地址
                            }
                            dMap.put("name", softwarePatchInfo.getPatchName());//软件名称
                            dMap.put("version", softwarePatchInfo.getPatchVersion());//版本
                            dMap.put("resource_type", softwarePatchInfo.getResourceType());//设备类型
                            dMap.put("filehash", softwarePatchInfo.getFileHash());//升级包hash值
                            dMap.put("scripthash", softwarePatchInfo.getScriptHash());//脚本hash值
                            map.put(softwarePatchInfo.getPatchName(), dMap);
                        }
                    }
                }
                st = JSONObject.toJSONString(map);
                if (!"{}".equals(st)) {
                    //将当前补丁包信息存入redis
                    redisUtils.set(patchKey, st);
                    //将当前设备请求信息存入redis
                    redisUtils.set(countKey, 1);
                    redisCount++;
                    maxPool -= 1;
                    currentCounts++;
                    log.debug("向IP为 " + getIpAddr(request) + " 的终端通过 SQL 分发了一个软件补丁包；redisCount + 1, 当前RedisCount的值为：" + redisCount + "； maxPool - 1，当前maxPool的值为：" + maxPool);
                    log.debug(st);
                    return st;
                } else {
                    log.debug("未获取到匹配的补丁升级包，终止当前分发任务！");
                    return st;
                }
            } else {
                log.debug("IP为 " + getIpAddr(request) + " -- mac为 " + mac + " 不满足判断条件，终止当前分发任务！");
                return "{}";
            }
        } catch (Exception e) {
            log.debug("-----------软件补丁分发异常！", e);
            return "{}";
        } finally {
            lock.unlock();
        }
    }


    /**
     * 获取请求源客户端当前网络ip(真实IP)
     *
     * @param request
     * @return
     */
    private String getIpAddr(HttpServletRequest request) {

        String ipAddress = request.getHeader("x-forwarded-for");
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getRemoteAddr();
            if (ipAddress.equals("127.0.0.1") || ipAddress.equals("0:0:0:0:0:0:0:1")) {
                //根据网卡取本机配置的IP
                InetAddress inet = null;
                try {
                    inet = InetAddress.getLocalHost();
                } catch (UnknownHostException e) {
                    e.printStackTrace();
                }
                ipAddress = inet.getHostAddress();
            }
        }
        //对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
        if (ipAddress != null && ipAddress.length() > 15) { //"***.***.***.***".length() = 15
            if (ipAddress.indexOf(",") > 0) {
                ipAddress = ipAddress.substring(0, ipAddress.indexOf(","));
            }
        }
        return ipAddress;
    }


    @Autowired
    private DevopsOtaInstallInfoMapper devopsOtaInstallInfoMapper;

    @ApiOperation(value = "软件补丁安装信息表-信息申报", notes = "软件补丁安装信息表-信息申报")
    @PutMapping(value = "/declareInfo")
    public Result<?> declareInfo(@RequestBody JSONObject jsonObject) {
        if (jsonObject.getBoolean("result") && maxPool < 20) {
            maxPool += 1;
            log.debug("有一台终端完成升级，maxPool + 1，当前maxPool的值为：" + maxPool);
        }
        DevopsOtaInstallInfo devopsOtaInstallInfo = new DevopsOtaInstallInfo();
        devopsOtaInstallInfo.setTerminalcode(jsonObject.getString("uniqueCode"));
        devopsOtaInstallInfo.setSoftwareName(jsonObject.getString("sName"));
        devopsOtaInstallInfo.setPatch_version(jsonObject.getString("sVersion"));
        devopsOtaInstallInfo.setInstalledResult(jsonObject.getInteger("result"));
        devopsOtaInstallInfo.setTerminalVersion(jsonObject.getString("tVersion"));
        devopsOtaInstallInfo.setRemark(jsonObject.getString("remark"));
        int insert = devopsOtaInstallInfoMapper.insert(devopsOtaInstallInfo);
        if (insert > 0) {
            return Result.OK("补丁安装信息申报成功！");
        } else {
            return Result.error("补丁安装信息申报失败！");
        }
    }


    @Autowired
    private DevopsOtaClientInfoMapper devopsOtaClientInfoMapper;

    @ApiOperation(value = "客户端版本信息表-信息申报", notes = "客户端版本信息表-信息申报")
    @PutMapping(value = "/terminalInfo")
    public Result<?> terminalInfo(@RequestBody JSONObject jsonObject) {
        int insert = 0;
        int update = 0;
        DevopsOtaClientInfo clientInfo = devopsOtaClientInfoMapper.selectOne(new QueryWrapper<DevopsOtaClientInfo>().eq("terminal_code", jsonObject.getString("uniqueCode")).eq("del_flag", 0));
        if (clientInfo == null) {
            DevopsOtaClientInfo devopsOtaClientInfo = new DevopsOtaClientInfo();
            devopsOtaClientInfo.setTerminalCode(jsonObject.getString("uniqueCode"));
            devopsOtaClientInfo.setTerminalVersion(jsonObject.getString("tVersion"));
            devopsOtaClientInfo.setTerminalArch(jsonObject.getString("frawork"));
            devopsOtaClientInfo.setTerminalType(jsonObject.getString("clientType"));
            devopsOtaClientInfo.setRemark(jsonObject.getString("remark"));
            insert = devopsOtaClientInfoMapper.insert(devopsOtaClientInfo);
        } else {
            clientInfo.setTerminalCode(jsonObject.getString("uniqueCode"));
            clientInfo.setTerminalVersion(jsonObject.getString("tVersion"));
            clientInfo.setTerminalArch(jsonObject.getString("frawork"));
            clientInfo.setTerminalType(jsonObject.getString("clientType"));
            clientInfo.setRemark(jsonObject.getString("remark"));
            update = devopsOtaClientInfoMapper.updateById(clientInfo);
        }

        //遵义定制，同步更新本地缓存中的终端升级申报信息
        LocalCacheUtils localCacheUtils = LocalCacheUtils.getInstance();
        localCacheUtils.putKeyWithValue(jsonObject.getString("uniqueCode"), jsonObject);

        if (insert > 0 || update > 0) {
            if (maxPool < 20) {
                maxPool += 1;
                log.debug("有一台终端完成升级，maxPool + 1，当前maxPool的值为：" + maxPool);
            }
            return Result.OK("客户端版本信息申报成功！");
        } else {
            return Result.error("客户端版本信息申报失败！");
        }
    }
}
