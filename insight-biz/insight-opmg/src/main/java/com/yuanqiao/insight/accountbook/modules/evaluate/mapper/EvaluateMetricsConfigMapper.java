package com.yuanqiao.insight.accountbook.modules.evaluate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateMetricsConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;

import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName EvaluateMetricsConfigMapper
 * @description: 评估指标配置Mapper
 * @datetime 2025年 07月 28日
 * @version: 1.0
 */
@Mapper
public interface EvaluateMetricsConfigMapper extends BaseMapper<EvaluateMetricsConfig> {
    
    /**
     * 根据指标ID查询配置
     */
    @Select("SELECT * FROM devops_evaluate_metrics_config WHERE metrics_id = #{metricsId}")
    EvaluateMetricsConfig selectByMetricsId(@Param("metricsId") String metricsId);
    
    /**
     * 根据指标ID删除配置
     */
    @Delete("DELETE FROM devops_evaluate_metrics_config WHERE metrics_id = #{metricsId}")
    int deleteByMetricsId(@Param("metricsId") String metricsId);
    
    /**
     * 统计指标的配置信息
     */
    @Select("SELECT version, status FROM devops_evaluate_metrics_config WHERE metrics_id = #{metricsId}")
    Map<String, Object> getConfigInfo(@Param("metricsId") String metricsId);
    
    /**
     * 根据指标ID查询配置详情（包含关联的指标信息和要点数据）
     */
    EvaluateMetricsConfig selectConfigWithDetails(@Param("metricsId") String metricsId);
}
