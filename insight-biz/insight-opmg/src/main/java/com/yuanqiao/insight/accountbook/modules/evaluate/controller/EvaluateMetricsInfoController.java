package com.yuanqiao.insight.accountbook.modules.evaluate.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateMetricsInfo;
import com.yuanqiao.insight.accountbook.modules.evaluate.service.IEvaluateMetricsInfoService;
import com.yuanqiao.insight.common.util.pinYinUtils.PinYinUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoDict;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @ClassName EvaluateMetricsInfoController
 * @description: TODO
 * @datetime 2025年 05月 29日 19:28
 * @version: 1.0
 */
@Api(tags = "评估指标信息")
@RestController
@RequestMapping("/evaluate/metricsInfo")
@Slf4j
public class EvaluateMetricsInfoController extends JeecgController<EvaluateMetricsInfo, IEvaluateMetricsInfoService> {

    @AutoDict
    @AutoLog(value = "评估指标-分页列表查询")
    @ApiOperation(value = "评估指标-分页列表查询", notes = "评估指标-分页列表查询")
    @GetMapping(value = "/pageList")
    public Result<?> queryPageList(EvaluateMetricsInfo evaluateMetricsInfo,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<EvaluateMetricsInfo> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(evaluateMetricsInfo.getMetricsName())) {
            queryWrapper.like("metrics_name", evaluateMetricsInfo.getMetricsName());
        }
        if (StringUtils.isNotBlank(evaluateMetricsInfo.getMetricsTypeId())){
            queryWrapper.eq("metrics_type_id", evaluateMetricsInfo.getMetricsTypeId());
        }
        queryWrapper.orderByDesc("create_time");
        Page<EvaluateMetricsInfo> page = new Page<>(pageNo, pageSize);
        IPage<EvaluateMetricsInfo> pageList = service.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    @AutoLog(value = "评估指标-列表查询")
    @ApiOperation(value = "评估指标-列表查询", notes = "评估指标-列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryList(EvaluateMetricsInfo evaluateMetricsInfo) {
        QueryWrapper<EvaluateMetricsInfo> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(evaluateMetricsInfo.getMetricsName())) {
            queryWrapper.like("metrics_name", evaluateMetricsInfo.getMetricsName());
        }
        if (StringUtils.isNotBlank(evaluateMetricsInfo.getMetricsTypeId())){
            queryWrapper.eq("metrics_type_id", evaluateMetricsInfo.getMetricsTypeId());
        }
        return Result.OK(service.list(queryWrapper));
    }

    @AutoLog(value = "评估指标-新增")
    @ApiOperation(value = "评估指标-新增", notes = "评估指标-新增")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody EvaluateMetricsInfo evaluateMetricsInfo) {
        evaluateMetricsInfo.setMetricsCode(PinYinUtil.strToPinYin(evaluateMetricsInfo.getMetricsName()));
        service.save(evaluateMetricsInfo);
        return Result.OK("添加成功！");
    }

    @AutoLog(value = "评估指标-编辑")
    @ApiOperation(value = "评估指标-编辑", notes = "评估指标-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody EvaluateMetricsInfo evaluateMetricsInfo) {
        evaluateMetricsInfo.setMetricsCode(PinYinUtil.strToPinYin(evaluateMetricsInfo.getMetricsName()));
        service.updateById(evaluateMetricsInfo);
        return Result.OK("编辑成功!");
    }

    @AutoLog(value = "评估指标-通过id删除")
    @ApiOperation(value = "评估指标-通过id删除", notes = "评估指标-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id") String id) {
        service.removeById(id);
        return Result.OK("删除成功!");
    }

    @AutoLog(value = "评估指标-批量删除")
    @ApiOperation(value = "评估指标-批量删除", notes = "评估指标-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids") String ids) {
        this.service.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }


}
