package com.yuanqiao.insight.accountbook.modules.autoInspection.generater;

import com.baomidou.mybatisplus.annotation.TableField;
import com.yuanqiao.insight.service.device.entity.DeviceInfo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @title: AITask                 智能巡检详情
 * @projectName jeecg-boot-parent
 * @description: TODO
 * @date 2021/5/15-15:58
 */
@Data
public class AITask {
    /**
     * 设备ids
     */
    private List<String> deviceIds;

    /**
     * 自动巡检生成报告
     */
    private AIRGeneraterResult aIRGeneraterResult;

    private String aIfilePath;

    private String path;

    private String taskId;

    //巡检分类的头部
    private AIReportDataColumnDictory aiReportDataColumnDictory;

    /**
     * 设备列表
     */
    private  List<DeviceInfo> deviceInfoList;

    @TableField(exist = false)
    private String deviceNames;




}
