package com.yuanqiao.insight.accountbook.modules.app.handler;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yuanqiao.insight.accountbook.modules.app.entity.*;
import com.yuanqiao.insight.accountbook.modules.app.enums.TaskTerminalStatus;
import com.yuanqiao.insight.accountbook.modules.app.queue.EventQueueProcessor;
import com.yuanqiao.insight.accountbook.modules.app.queue.SoftwareIssueCache;
import com.yuanqiao.insight.accountbook.modules.app.service.ISoftwareTaskService;
import com.yuanqiao.insight.accountbook.modules.app.service.ISoftwareTaskTerminalLogService;
import com.yuanqiao.insight.accountbook.modules.app.service.ISoftwareTaskTerminalService;
import com.yuanqiao.insight.common.util.common.RedisUtils;
import com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice;
import com.yuanqiao.insight.monitoring.modules.terminal.service.ITerminalDeviceService;
import com.yuanqiao.insight.service.device.entity.DeviceInfo;
import com.yuanqiao.insight.service.device.service.IDeviceInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.message.websocket.handler.IWebSocketHandler;
import org.jeecg.common.message.webssh.pojo.WebsocketConst;
import org.jeecg.common.mq.RedisMq;
import org.jeecg.common.mq.stream.Streams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.websocket.Session;
import java.io.IOException;
import java.util.*;

/**
 * 与客户段通信的处理类
 */
@Slf4j
@Component
public class TerminalCommSocketHandler implements IWebSocketHandler {

    @Autowired
    private ISoftwareTaskService softwareTaskService;
    @Autowired
    private ISoftwareTaskTerminalService softwareTaskTerminalService;
    @Autowired
    private ISoftwareTaskTerminalLogService softwareTaskTerminalLogService;

    private static final RedisMq redisMq = SpringUtil.getBean(RedisMq.class);
    private static final RedisUtils redisUtils = SpringUtil.getBean(RedisUtils.class);
    private static final IDeviceInfoService deviceInfoService = SpringUtil.getBean(IDeviceInfoService.class);
    private static final ITerminalDeviceService terminalDeviceService = SpringUtil.getBean(ITerminalDeviceService.class);

    private static final TimedCache<String, String> cache = CacheUtil.newTimedCache(-1);
    private static final Map<String, String> cache2 = new HashMap<>();

    static {
        cache.schedulePrune(10 * 1000);
        cache.setListener((key, deviceCode) -> {
            if (!cache2.containsValue(deviceCode)) {
                //修改终端状态
                editTerminalStatus(deviceCode, 0);
                //删除redis设备状态
                redisUtils.del("onst:" + deviceCode);
                log.info("设备离线:{}", deviceCode);
                //通知前端设备离线
                sendTerminalStatus(deviceCode, "down");
            }
        });
    }

    @Override
    public boolean support(String support) {
        return StringUtils.equals(support, WebsocketConst.TERMINAL);
    }

    @Override
    public void onOpenHandler(String code, Session session) {
        new Thread(() -> {
            String id = session.getId();
            synchronized (id) {
                cache.put(id, code, -1);
                cache2.put(id, code);
                log.info("客户端连接成功，sessionId:{}，uniqueCode:{}", id, cache.get(id));
                //设备上线
                editTerminalStatus(code, 1);
                HashMap<String, Object> map = new HashMap<>();
                map.put("up", DateUtil.now());
                map.put("st", 1);
                redisUtils.hmset("onst:" + code, map, 600);
                sendTerminalStatus(code, "up");
            }
        }).start();
    }

    @Override
    public void onMessageHandler(String buffer, Session session) {
        new Thread(() -> {
            String id = session.getId();
            synchronized (id) {
                String uniqueCode = cache.get(id);
                log.info("接受到消息。sessionId:{}，uniqueCode:{}，data:{}", id, uniqueCode, buffer);
                JSONObject msg = JSON.parseObject(buffer);
                String command = msg.getString("command");
                JSONObject data = msg.getJSONObject("data");
                switch (command) {
                    case "init":
                        init(data, uniqueCode);
                        break;
                    case "callBack":
                        callBack(data, uniqueCode);
                        break;
                    default:
                        break;
                }
            }
        }).start();
    }

    @Override
    public void onCloseHandler(Session session) throws IOException {
        new Thread(() -> {
            String id = session.getId();
            synchronized (id) {
                String deviceCode = cache.get(id);
                cache.put(session.getId(), deviceCode, 60 * 1000);
                cache2.remove(id);
                log.info("设备断开连接:{},sessionId:{}", deviceCode, id);
            }
        }).start();
    }

    private void callBack(JSONObject data, String uniqueCode) {
        String taskId = data.getString("taskId");
        String status = data.getString("status");
        Date startTime = DateUtil.parseDateTime(data.getString("startTime"));
        String[] st = status.split("_");
        SoftwareTaskTerminal softwareTaskTerminal = new SoftwareTaskTerminal();
        String result = data.getString("result");
        if (status.startsWith("T_")) {
            //修改终端升级任务状态
            if (StringUtils.equals(st[1], "1")) {
                softwareTaskTerminal.setExecuteStatus(TaskTerminalStatus.RUNNING.getCode());
                softwareTaskTerminal.setStartTime(startTime);
            } else if (StringUtils.equals(st[1], "5")) {
                String key = String.format(SoftwareIssueCache.TASK_CACHE_KEY, taskId);
                Map<String, Object> taskCache = redisUtils.hmGet(key);
                EventQueueProcessor eventQueueProcessor = SoftwareIssueCache.processorCache.get(taskId);
                int totalTime = Integer.parseInt(taskCache.get(SoftwareIssueCache.TOTAL_TIME_KEY) + "");
                int tSize = Integer.parseInt(taskCache.get(SoftwareIssueCache.EFFECTIVE_NUMBER_KEY) + "");
                SoftwareTaskTerminalLog t1Receive = softwareTaskTerminalLogService.getOne(new LambdaQueryWrapper<SoftwareTaskTerminalLog>()
                        .eq(SoftwareTaskTerminalLog::getTaskId, taskId)
                        .eq(SoftwareTaskTerminalLog::getUniqueCode, uniqueCode)
                        .eq(SoftwareTaskTerminalLog::getExecuteStatus, "T_1_RECEIVE"));
                long t = DateUtil.between(t1Receive.getStartTime(), startTime, DateUnit.SECOND);
                redisUtils.hset(key, SoftwareIssueCache.EXECUTION_TIME_KEY, t);
                long min = Math.min(totalTime / tSize, t);
                eventQueueProcessor.setInterval(min);
            } else if (StringUtils.equals(st[1], "8")) {
                if (result.equals("error")) {
                    softwareTaskTerminal.setExecuteStatus(TaskTerminalStatus.FAILED.getCode());
                } else {
                    softwareTaskTerminal.setExecuteStatus(TaskTerminalStatus.SUCCESS.getCode());
                }
                softwareTaskTerminal.setEndTime(startTime);
            } else if (StringUtils.equals(st[1], "17")) {
                softwareTaskTerminal.setExecuteStatus(TaskTerminalStatus.RETRY.getCode());
            }
            softwareTaskTerminal.setUniqueCode(uniqueCode);
            softwareTaskTerminalService.update(softwareTaskTerminal, new LambdaQueryWrapper<SoftwareTaskTerminal>()
                    .eq(SoftwareTaskTerminal::getUniqueCode, uniqueCode)
                    .eq(SoftwareTaskTerminal::getTaskId, taskId));
        }

        //添加终端升级任务执行日志
        SoftwareTaskTerminalLog softwareTaskTerminalLog = new SoftwareTaskTerminalLog();
        softwareTaskTerminalLog.setTaskId(taskId)
                .setUniqueCode(uniqueCode)
                .setStartTime(startTime)
                .setMessage(data.getString("message"))
                .setExecuteResult(result)
                .setExecuteStatus(status)
                .setExecuteLog(data.getString("log"));
        softwareTaskTerminalLogService.save(softwareTaskTerminalLog);
    }

    private void init(JSONObject data, String uniqueCode) {
        List<String> taskIds = data.getJSONArray("taskIds").toJavaList(String.class);
        List<SoftwareTask> softwareTaskList = softwareTaskService.selectJoinList(SoftwareTask.class, new MPJLambdaWrapper<SoftwareTask>()
                .selectAll(SoftwareTask.class)
                .selectAssociation(SoftwarePatchInfo.class, SoftwareTask::getSoftwarePatchInfo)
                .selectCollection(SoftwareTaskExtend.class, SoftwareTask::getExtendList)
                .leftJoin(SoftwareTaskTerminal.class, SoftwareTaskTerminal::getTaskId, SoftwareTask::getId)
                .leftJoin(SoftwarePatchInfo.class, SoftwarePatchInfo::getId, SoftwareTask::getSoftwarePatchId)
                .leftJoin(SoftwareTaskExtend.class, SoftwareTaskExtend::getTaskId, SoftwareTask::getId)
                .eq(SoftwareTaskTerminal::getUniqueCode, uniqueCode)
                .eq(SoftwareTaskTerminal::getExecuteStatus, 0)
                .eq(SoftwareTask::getDelFlag, 0)
                .notIn(SoftwareTask::getId, taskIds));
        Set<String> ids = SoftwareIssueCache.processorCache.keySet();
        if (CollUtil.isNotEmpty(softwareTaskList)) {
            softwareTaskList.stream()
                    .filter(softwareTask -> ids.stream().anyMatch(id -> id.equals(softwareTask.getId())))
                    .forEach(softwareTask -> {
                        redisUtils.lPush(String.format(SoftwareIssueCache.TASK_TERMINAL_LIST_KEY, softwareTask.getId()), uniqueCode);
                    });
        }
    }

    /**
     * @param code   设备标识
     * @param status 状态 up上线 down离线
     */
    private static void sendTerminalStatus(String code, String status) {
        JSONObject dataObject = new JSONObject();
        dataObject.put("deviceCode", code);
        dataObject.put("status", status);
        log.info("发布终端状态改变事件......status：{}", dataObject.toJSONString());
        redisMq.publish(Streams.STATUS_CHANGE, dataObject);
    }

    private static void editTerminalStatus(String code, int status) {
        TerminalDevice terminalDevice = new TerminalDevice();
        terminalDevice.setStatus(status);
        terminalDeviceService.update(terminalDevice, new LambdaQueryWrapper<TerminalDevice>()
                .eq(TerminalDevice::getUniqueCode, code));
        //修改设备状态
        DeviceInfo deviceInfo = new DeviceInfo();
        deviceInfo.setStatus(status);
        deviceInfoService.update(deviceInfo, new LambdaQueryWrapper<DeviceInfo>()
                .eq(DeviceInfo::getDeviceCode, code));
    }
}
