package com.yuanqiao.insight.accountbook.modules.arrangementshift.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.accountbook.modules.arrangementlocation.entity.DevopsArrangementLocation;
import com.yuanqiao.insight.accountbook.modules.arrangementlocation.service.IDevopsArrangementLocationService;
import com.yuanqiao.insight.accountbook.modules.arrangementshift.entity.DevopsArrangementShift;
import com.yuanqiao.insight.accountbook.modules.arrangementshift.entity.TodoArrangement;
import com.yuanqiao.insight.accountbook.modules.arrangementshift.mapper.DevopsArrangementShiftMapper;
import com.yuanqiao.insight.accountbook.modules.arrangementshift.service.IDevopsArrangementShiftService;
import com.yuanqiao.insight.common.constant.CommonConstant;
import com.yuanqiao.insight.common.util.common.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 值班记录表
 * @Author: jeecg-boot
 * @Date: 2021-03-12
 * @Version: V1.0
 */
@Slf4j
@Service
public class DevopsArrangementShiftServiceImpl extends ServiceImpl<DevopsArrangementShiftMapper, DevopsArrangementShift> implements IDevopsArrangementShiftService {

    @Autowired
    private IDevopsArrangementLocationService locationService;
    /**
     * 批量添加值班记录
     *
     * @param arrangementInfoIds 排班的ids
     */
    @Override
    public void addBatchArrangementShift(String arrangementInfoIds) {
        log.info("arrangementInfoIds={}",arrangementInfoIds);
        log.info("com.yuanqiao.insight.accountbook.modules.arrangementshift.service.impl.DevopsArrangementShiftServiceImpl.addBatchArrangementShift(arrangementInfoIds={})",arrangementInfoIds);
        //判断是否为空
        if (StringUtils.isNotBlank(arrangementInfoIds)) {
            //String 转 string[]
            String[] ids = arrangementInfoIds.split(",");
            List<DevopsArrangementShift> oldList = this.baseMapper.selectBatchArrangementIds(Arrays.asList(ids));
            Map<String, DevopsArrangementShift> oldMap = oldList.stream().collect(Collectors.toMap(DevopsArrangementShift::getArrangementId, DevopsArrangementShift -> DevopsArrangementShift));
            //需要添加的值班记录
            List<DevopsArrangementShift> list = new ArrayList<>();
            DevopsArrangementShift devopsArrangementShift = null;
            for (int i = 0; i < ids.length; i++) {
                if(null == oldMap.get(ids[i])){
                    devopsArrangementShift = new DevopsArrangementShift();
                    devopsArrangementShift.setArrangementId(ids[i]);
                    devopsArrangementShift.setStatus(CommonConstant.ARRANGEMENT_SHIFT_STATUS_NOT_ON_DUTY);
                    list.add(devopsArrangementShift);
                }
            }
            this.saveBatch(list);
        }

    }

    @Override
    public void delByArrangementInfoIds(List<String> arrangementInfoIds) {
        log.info("arrangementInfoIds={}",arrangementInfoIds);
        log.info("com.yuanqiao.insight.accountbook.modules.arrangementshift.service.impl.DevopsArrangementShiftServiceImpl.delByArrangementInfoIds(arrangementInfoIds={})",arrangementInfoIds);
        this.baseMapper.delByArrangementInfoIds(arrangementInfoIds);
    }


    @Override
    public IPage<DevopsArrangementShift> pageList(Page<DevopsArrangementShift> page,String userId,Map<String,String[]> map) {
        log.info("page={}",page);
        log.info("com.yuanqiao.insight.accountbook.modules.arrangementshift.service.impl.DevopsArrangementShiftServiceImpl.pageList(page={})",page);
        log.info("userId={}",userId);
        log.info("com.yuanqiao.insight.accountbook.modules.arrangementshift.service.impl.DevopsArrangementShiftServiceImpl.pageList(userId={})",userId);
        log.info("map={}",map);
        log.info("com.yuanqiao.insight.accountbook.modules.arrangementshift.service.impl.DevopsArrangementShiftServiceImpl.pageList(map={})",map);

        String sql = "";
        try {
            TimeUtils timeUtils = new TimeUtils();
            StringBuffer sqlBuffer = new StringBuffer("select * from (select a.*,b.arrangement_time,b.schedual_name,b.start_date,b.end_date from (SELECT das.*,dau.realname FROM devops_arrangement_shift AS das RIGHT JOIN ( SELECT dauu.*,su.realname AS realname FROM devops_arrangement_user AS dauu LEFT JOIN sys_users su ON dauu.user_id = su.id where dauu.user_id = '");
            sqlBuffer.append(userId).append( "') AS dau ON das.arrangement_id = dau.arrangement_id) a left join (select dai.*,dsi.name as schedual_name,dsi.start_time as start_date,dsi.end_time as end_date  from devops_arrangement_info as dai left join devops_schedual_info as dsi on dai.schedual_id = dsi.id) b on a.arrangement_id = b.id) as atb ");
            if(null != map.get("shifteUserName")){
                sqlBuffer.append("left join sys_users users on users.id = atb.shifte_user");
            }
            sqlBuffer.append(" where 1=1 ");
            if(null != map.get("shifteUserName")){
                String shifteUserName = map.get("shifteUserName")[0];
                sqlBuffer.append(" and users.realname like CONCAT('%','").append(shifteUserName).append("','%')");
            }
            if(null != map.get("arrangementTime_begin") && null != map.get("arrangementTime_end")){
                String arrangementTime_begin = map.get("arrangementTime_begin")[0];
                String arrangementTime_end = map.get("arrangementTime_end")[0];
                sqlBuffer.append(" and  date(arrangement_time) between '").append(timeUtils.StrToStrYMD(arrangementTime_begin)).append("' and '").append(timeUtils.StrToStrYMD(arrangementTime_end)).append("'");
            }
            if(null != map.get("schedualName")){
                String schedualName = map.get("schedualName")[0];
                sqlBuffer.append(" and schedual_name like CONCAT('%','").append(schedualName).append("','%')");
            }
            if(null != map.get("realname")){
                String realname = map.get("realname")[0];
                sqlBuffer.append(" and realname like CONCAT('%','").append(realname).append("','%')");
                //producer_name like CONCAT('%',#{producerName},'%')
            }
            sqlBuffer.append("ORDER BY arrangement_time DESC");
            sql = sqlBuffer.toString();
            log.info(sql);

        } catch (ParseException e) {
            e.printStackTrace();
        }
        return this.baseMapper.pageList(page,sql);
    }

    @Override
    public void doWorkUp(String id) {
        log.info("id={}",id);
        log.info("com.yuanqiao.insight.accountbook.modules.arrangementshift.service.impl.DevopsArrangementShiftServiceImpl.doWorkUp(id={})",id);
        DevopsArrangementShift devopsArrangementShift = this.baseMapper.selectById(id);
        devopsArrangementShift.setStatus(CommonConstant.ARRANGEMENT_SHIFT_STATUS_ON_DUTY);
        devopsArrangementShift.setCreattTime(new Date());
        this.baseMapper.updateById(devopsArrangementShift);


    }

    @Override
    public List<DevopsArrangementShift> queryListByDateAndUserId(String userId) {
        StringBuffer sqlBuffer = new StringBuffer("select * from (select a.*,b.arrangement_time,b.schedual_name,b.schedual_id,b.start_date,b.end_date from (SELECT das.*,dau.realname FROM devops_arrangement_shift AS das RIGHT JOIN ( SELECT dauu.*,su.realname AS realname FROM devops_arrangement_user AS dauu LEFT JOIN sys_users su ON dauu.user_id = su.id where dauu.user_id = '");
        sqlBuffer.append(userId).append( "') AS dau ON das.arrangement_id = dau.arrangement_id) a left join (select dai.*,dsi.name as schedual_name,dsi.start_time as start_date,dsi.end_time as end_date  from devops_arrangement_info as dai left join devops_schedual_info as dsi on dai.schedual_id = dsi.id) b on a.arrangement_id = b.id) as atb ");
        TimeUtils timeUtils = new TimeUtils();
        sqlBuffer.append(" where  arrangement_time = '").append(timeUtils.dateFormatYMD(new Date())+"'");
        sqlBuffer.append("ORDER BY start_date");
        return  baseMapper.queryListByDateAndUserId(sqlBuffer.toString());
    }

    @Override
    public void appdoWorkUp(String id,String workLocation) {
        DevopsArrangementShift devopsArrangementShift = this.baseMapper.selectById(id);
        devopsArrangementShift.setStatus(CommonConstant.ARRANGEMENT_SHIFT_STATUS_ON_DUTY);
        devopsArrangementShift.setCreattTime(new Date());
        this.baseMapper.updateById(devopsArrangementShift);
        DevopsArrangementLocation devopsArrangementLocation = new DevopsArrangementLocation();
        devopsArrangementLocation.setArrangementId(id);
        devopsArrangementLocation.setUpWorkLocation(workLocation);
        locationService.save(devopsArrangementLocation);

    }

    @Override
    public List<TodoArrangement> getTodoArrangement(String userId,Integer status) {
        return this.baseMapper.getTodoArrangement(userId,status);
    }
}
