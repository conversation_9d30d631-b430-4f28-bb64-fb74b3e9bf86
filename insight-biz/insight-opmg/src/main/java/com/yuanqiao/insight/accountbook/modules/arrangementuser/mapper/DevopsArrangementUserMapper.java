package com.yuanqiao.insight.accountbook.modules.arrangementuser.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanqiao.insight.accountbook.modules.arrangementuser.entity.DevopsArrangementUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Description: 排班用户关系表
 * @Author: jeecg-boot
 * @Date:   2021-03-12
 * @Version: V1.0
 */
@Mapper
@Repository
public interface DevopsArrangementUserMapper extends BaseMapper<DevopsArrangementUser> {
    /**
     *
     * @param ids
     * @return
     */
    List<DevopsArrangementUser> getArrangementUserList(@Param("ids") List<String> ids);

    void delByArrangementInfoIds(@Param("arrangementInfoIds") List<String> arrangementInfoIds);

}
