package com.yuanqiao.insight.accountbook.modules.evaluate.controller;

import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateMetricsScoreRecord;
import com.yuanqiao.insight.accountbook.modules.evaluate.service.IEvaluateMetricsScoreService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName EvaluateMetricsScoreController
 * @description: 评估指标打分控制器
 * @datetime 2025年 07月 28日
 * @version: 1.0
 */
@Api(tags = "评估指标打分")
@RestController
@RequestMapping("/evaluate/metricsScore")
@Slf4j
public class EvaluateMetricsScoreController extends JeecgController<EvaluateMetricsScoreRecord, IEvaluateMetricsScoreService> {

    @Autowired
    private IEvaluateMetricsScoreService scoreService;

    @AutoLog(value = "执行评估打分")
    @ApiOperation(value = "执行评估打分", notes = "根据评估规则对字段值进行打分")
    @PostMapping(value = "/execute")
    public Result<Map<String, Object>> executeEvaluation(@RequestBody Map<String, Object> requestData) {
        try {
            String projectId = (String) requestData.get("projectId");
            String metricsId = (String) requestData.get("metricsId");
            Map<String, Object> fieldValues = (Map<String, Object>) requestData.get("fieldValues");
            String evaluator = (String) requestData.get("evaluator");

            if (projectId == null || metricsId == null || fieldValues == null) {
                return Result.error("参数不完整：projectId、metricsId、fieldValues不能为空");
            }

            Map<String, Object> result = scoreService.executeEvaluation(projectId, metricsId, fieldValues, evaluator);
            
            if ((Boolean) result.get("success")) {
                return Result.OK(result);
            } else {
                return Result.error((String) result.get("message"));
            }
        } catch (Exception e) {
            log.error("执行评估打分失败", e);
            return Result.error("评估失败：" + e.getMessage());
        }
    }

    @AutoLog(value = "获取打分记录")
    @ApiOperation(value = "获取打分记录", notes = "获取项目指标的打分记录")
    @GetMapping(value = "/records/{projectId}/{metricsId}")
    public Result<List<EvaluateMetricsScoreRecord>> getScoreRecords(@PathVariable("projectId") String projectId,
                                                                   @PathVariable("metricsId") String metricsId) {
        try {
            List<EvaluateMetricsScoreRecord> records = scoreService.getScoreRecords(projectId, metricsId);
            return Result.OK(records);
        } catch (Exception e) {
            log.error("获取打分记录失败", e);
            return Result.error("获取失败：" + e.getMessage());
        }
    }

    @AutoLog(value = "获取总分")
    @ApiOperation(value = "获取总分", notes = "获取项目指标的总分信息")
    @GetMapping(value = "/total/{projectId}/{metricsId}")
    public Result<Map<String, Object>> getTotalScore(@PathVariable("projectId") String projectId,
                                                    @PathVariable("metricsId") String metricsId) {
        try {
            Map<String, Object> totalScore = scoreService.getTotalScore(projectId, metricsId);
            return Result.OK(totalScore);
        } catch (Exception e) {
            log.error("获取总分失败", e);
            return Result.error("获取失败：" + e.getMessage());
        }
    }

    @AutoLog(value = "重新计算打分")
    @ApiOperation(value = "重新计算打分", notes = "重新计算项目指标的打分")
    @PostMapping(value = "/recalculate")
    public Result<Map<String, Object>> recalculateScore(@RequestBody Map<String, Object> requestData) {
        try {
            String projectId = (String) requestData.get("projectId");
            String metricsId = (String) requestData.get("metricsId");
            String evaluator = (String) requestData.get("evaluator");

            if (projectId == null || metricsId == null) {
                return Result.error("参数不完整：projectId、metricsId不能为空");
            }

            Map<String, Object> result = scoreService.recalculateScore(projectId, metricsId, evaluator);
            
            if ((Boolean) result.get("success")) {
                return Result.OK(result);
            } else {
                return Result.error((String) result.get("message"));
            }
        } catch (Exception e) {
            log.error("重新计算打分失败", e);
            return Result.error("重新计算失败：" + e.getMessage());
        }
    }

    @AutoLog(value = "批量执行评估")
    @ApiOperation(value = "批量执行评估", notes = "批量执行多个项目的评估打分")
    @PostMapping(value = "/batchExecute")
    public Result<Map<String, Object>> batchExecuteEvaluation(@RequestBody Map<String, Object> requestData) {
        try {
            List<String> projectIds = (List<String>) requestData.get("projectIds");
            String metricsId = (String) requestData.get("metricsId");
            String evaluator = (String) requestData.get("evaluator");

            if (projectIds == null || projectIds.isEmpty() || metricsId == null) {
                return Result.error("参数不完整：projectIds、metricsId不能为空");
            }

            Map<String, Object> batchResult = new java.util.HashMap<>();
            List<Map<String, Object>> results = new java.util.ArrayList<>();
            int successCount = 0;
            int failCount = 0;

            for (String projectId : projectIds) {
                try {
                    // 这里需要根据实际业务逻辑获取项目的字段值
                    Map<String, Object> fieldValues = new java.util.HashMap<>();
                    // TODO: 实现获取项目字段值的逻辑
                    
                    Map<String, Object> result = scoreService.executeEvaluation(projectId, metricsId, fieldValues, evaluator);
                    result.put("projectId", projectId);
                    results.add(result);
                    
                    if ((Boolean) result.get("success")) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (Exception e) {
                    log.error("项目{}评估失败", projectId, e);
                    Map<String, Object> errorResult = new java.util.HashMap<>();
                    errorResult.put("projectId", projectId);
                    errorResult.put("success", false);
                    errorResult.put("message", "评估失败：" + e.getMessage());
                    results.add(errorResult);
                    failCount++;
                }
            }

            batchResult.put("totalCount", projectIds.size());
            batchResult.put("successCount", successCount);
            batchResult.put("failCount", failCount);
            batchResult.put("results", results);

            return Result.OK(batchResult);
        } catch (Exception e) {
            log.error("批量执行评估失败", e);
            return Result.error("批量评估失败：" + e.getMessage());
        }
    }
}
