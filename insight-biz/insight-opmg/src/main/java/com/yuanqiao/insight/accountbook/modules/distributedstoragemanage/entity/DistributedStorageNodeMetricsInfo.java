package com.yuanqiao.insight.accountbook.modules.distributedstoragemanage.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.vmware.vim25.DiskChangeInfo;
import com.yuanqiao.insight.common.core.validator.constraints.LengthForUtf8;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Data
public class DistributedStorageNodeMetricsInfo {

    String nodeName;

    //online offline
    String state = "online";

    double cpuUsage;

    String memUsageString;

    int diskNum;//minio_cluster_disk_total{server="192.168.16.249:9000"} 2 cluster

    int onlineDiskNum;//minio_cluster_disk_online_total{server="192.168.16.249:9000"} 2 cluster

    //掉线的磁盘minio_cluster_disk_offline_total{server="192.168.16.249:9000"} 0

    //有多个磁盘时，是多个磁盘总容量的总和
    String totalStorageCapacity;
    //minio_node_disk_total_bytes{disk="/data/minio/data1",server="192.168.16.21:9000"} 2.1463302144e+10
    //minio_node_disk_total_bytes{disk="/data/minio/data1",server="192.168.16.249:9000"} 2.1463302144e+10

    //有多个磁盘时，是多个磁盘可用容量的总和
    String allAvailableStorageCapacity;
    //minio_node_disk_used_bytes{disk="/data/minio/data1",server="192.168.16.21:9000"} 2.822168576e+09
    //minio_node_disk_used_bytes{disk="/data/minio/data1",server="192.168.16.249:9000"} 2.793250816e+09

    //IO操作等待数
    int requestsWaitNum;
    //minio_s3_requests_waiting_total{server="192.168.16.249:9000"} 0 //这个不确实是不是这个指标，v2、v3有区别

    boolean isException = false;
    String errorMessage;


    //磁盘超时错误数 //这个去掉吧，不要了，v2版本的没找到合适的
//    long disk;
    //# HELP minio_inter_node_traffic_dial_errors Total number of internode TCP dial timeouts and errors
    //# TYPE minio_inter_node_traffic_dial_errors counter
    //minio_inter_node_traffic_dial_errors{server="192.168.16.249:9000"} 562



    //单位 秒
    double runtime;
    Double allDiskUsedPercent;
    long allDiskLatencyAverage;


    //计算
    long allDiskUsedBytes;

    List<DiskInfo> diskInfos = new ArrayList<DiskInfo>();


    @Data
    public static class DiskInfo{
        String diskName;

        //磁盘使用量
        //这三个是在cluster里的
        String diskUsedString;
        String diskTotalString;
        String diskFreeString;
        double diskUsedDouble;
        double diskTotalDouble;
        double diskFreeDouble;



        //IO操作等待数
        Integer requestsWaitNum;
        //minio_s3_requests_waiting_total{server="192.168.16.249:9000"} 0 //这个不确实是不是这个指标，v2、v3有区别

        //空闲inode数
        double freeINode;
        //    long freeINode;
        //minio_node_disk_free_inodes{disk="/data/minio/data1",server="192.168.16.21:9000"} 1.0462485e+07
        //minio_node_disk_free_inodes{disk="/data/minio/data1",server="192.168.16.249:9000"} 1.046248e+07

        //磁盘延迟,单位ms
        Long diskLatency;
    /*
    # HELP minio_node_disk_latency_us Average last minute latency in µs for drive API storage operations
    # TYPE minio_node_disk_latency_us gauge
    minio_node_disk_latency_us{api="storage.DiskInfo",disk="/data/minio/data1",server="192.168.16.249:9000"} 21
    minio_node_disk_latency_us{api="storage.ListVols",disk="/data/minio/data1",server="192.168.16.249:9000"} 99
    minio_node_disk_latency_us{api="storage.ReadXL",disk="/data/minio/data1",server="192.168.16.249:9000"} 97
    minio_node_disk_latency_us{api="storage.RenameData",disk="/data/minio/data1",server="192.168.16.249:9000"} 2518
    minio_node_disk_latency_us{api="storage.StatVol",disk="/data/minio/data1",server="192.168.16.249:9000"} 28
     */

        //磁盘错误数
        long diskErrorNum;
        //minio_inter_node_traffic_errors_total 也不怎么合适
//
//        //IO操作等待数
//        int requestsWaitNum;
//        //minio_s3_requests_waiting_total{server="192.168.16.249:9000"} 0 //这个不确实是不是这个指标，v2、v3有区别

    }

    public void setDiskRequestsWaitNumByDiskName(int requestsWaitNum){
        for (DiskInfo diskInfo : this.diskInfos) {
            diskInfo.setRequestsWaitNum(requestsWaitNum);
        }
    }

    public void setDiskLatencyByDiskName(String diskName,long diskLatency){
        boolean flag = true;
        for (DiskInfo diskInfo : this.diskInfos){
            if (StringUtils.equals(diskInfo.getDiskName(),diskName)){
                flag = !flag;
                diskInfo.setDiskLatency(diskLatency);
            }
        }
        if (flag){
            DiskInfo diskInfo = new DiskInfo();
            diskInfo.setDiskName(diskName);
            diskInfo.setDiskLatency(diskLatency);
            this.diskInfos.add(diskInfo);
        }
    }

    public void setDiskErrorNumByDiskName(long diskErrorNum) {

        for (DiskInfo diskInfo : this.diskInfos) {
            diskInfo.setDiskErrorNum(diskErrorNum);
        }
    }

//    public void setRequestsWaitNumByDiskName(int requestsWaitNum) {
//        for (DiskInfo diskInfo : this.diskInfos) {
//            diskInfo.setRequestsWaitNum(requestsWaitNum);
//        }
//    }



    public void setDiskUsedByDiskName(String diskName,String diskUsed){
        boolean flag = true;
        for (DiskInfo diskInfo : this.diskInfos){
            if (StringUtils.equals(diskInfo.getDiskName(),diskName)){
                flag = !flag;
                diskInfo.setDiskUsedString(diskUsed);
            }
        }
        if (flag){
            DiskInfo diskInfo = new DiskInfo();
            diskInfo.setDiskName(diskName);
            diskInfo.setDiskUsedString(diskUsed);
            this.diskInfos.add(diskInfo);
        }
    }

    public void setDiskUsedByDiskName(String diskName,double diskUsed){
        boolean flag = true;
        for (DiskInfo diskInfo : this.diskInfos){
            if (StringUtils.equals(diskInfo.getDiskName(),diskName)){
                flag = !flag;
                diskInfo.setDiskUsedDouble(diskUsed);
            }
        }
        if (flag){
            DiskInfo diskInfo = new DiskInfo();
            diskInfo.setDiskName(diskName);
            diskInfo.setDiskUsedDouble(diskUsed);
            this.diskInfos.add(diskInfo);
        }
    }

    public void setDiskTotalByDiskName(String diskName,String diskTotal){
        boolean flag = true;
        for (DiskInfo diskInfo : this.diskInfos){
            if (StringUtils.equals(diskInfo.getDiskName(),diskName)){
                flag = !flag;
                diskInfo.setDiskTotalString(diskTotal);
            }
        }
        if (flag){
            DiskInfo diskInfo = new DiskInfo();
            diskInfo.setDiskName(diskName);
            diskInfo.setDiskTotalString(diskTotal);
            this.diskInfos.add(diskInfo);
        }
    }

    public void setDiskTotalByDiskName(String diskName,double diskTotal){
        boolean flag = true;
        for (DiskInfo diskInfo : this.diskInfos){
            if (StringUtils.equals(diskInfo.getDiskName(),diskName)){
                flag = !flag;
                diskInfo.setDiskTotalDouble(diskTotal);
            }
        }
        if (flag){
            DiskInfo diskInfo = new DiskInfo();
            diskInfo.setDiskName(diskName);
            diskInfo.setDiskTotalDouble(diskTotal);
            this.diskInfos.add(diskInfo);
        }
    }
    public void setDiskFreeByDiskName(String diskName,String diskFree){
        boolean flag = true;
        for (DiskInfo diskInfo : this.diskInfos){
            if (StringUtils.equals(diskInfo.getDiskName(),diskName)){
                flag = !flag;
                diskInfo.setDiskFreeString(diskFree);
            }
        }
        if (flag){
            DiskInfo diskInfo = new DiskInfo();
            diskInfo.setDiskName(diskName);
            diskInfo.setDiskFreeString(diskFree);
            this.diskInfos.add(diskInfo);
        }
    }

    public void setDiskFreeByDiskName(String diskName,double diskFree){
        boolean flag = true;
        for (DiskInfo diskInfo : this.diskInfos){
            if (StringUtils.equals(diskInfo.getDiskName(),diskName)){
                flag = !flag;
                diskInfo.setDiskFreeDouble(diskFree);
            }
        }
        if (flag){
            DiskInfo diskInfo = new DiskInfo();
            diskInfo.setDiskName(diskName);
            diskInfo.setDiskFreeDouble(diskFree);
            this.diskInfos.add(diskInfo);
        }
    }

    public void setFreeINodeByDiskName(String diskName,double freeINode){
        boolean flag = true;
        for (DiskInfo diskInfo : this.diskInfos){
            if (StringUtils.equals(diskInfo.getDiskName(),diskName)){
                flag = !flag;
                diskInfo.setFreeINode(freeINode);
            }
        }
        if (flag){
            DiskInfo diskInfo = new DiskInfo();
            diskInfo.setDiskName(diskName);
            diskInfo.setFreeINode(freeINode);
            this.diskInfos.add(diskInfo);
        }
    }
}
