package com.yuanqiao.insight.accountbook.modules.schedualinfo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuanqiao.insight.accountbook.modules.schedualinfo.entity.DevopsSchedualInfo;

import java.util.List;
import java.util.Map;

/**
 * @Description: 班次管理表
 * @Author: jeecg-boot
 * @Date:   2021-03-12
 * @Version: V1.0
 */
public interface IDevopsSchedualInfoService extends IService<DevopsSchedualInfo> {

    /**
     * 查询所有班次名称
     * @return
     */
    Map<String,String> getSchedualMap();

    void delByIds(List<String> ids);

}
