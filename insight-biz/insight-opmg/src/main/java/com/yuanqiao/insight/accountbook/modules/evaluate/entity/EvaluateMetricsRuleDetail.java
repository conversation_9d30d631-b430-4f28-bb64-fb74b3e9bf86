package com.yuanqiao.insight.accountbook.modules.evaluate.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName EvaluateMetricsRuleDetail
 * @description: 评估指标规则详情表
 * @datetime 2025年 07月 28日
 * @version: 1.0
 */
@Data
@TableName("devops_evaluate_metrics_rule_detail")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="devops_evaluate_metrics_rule_detail对象", description="评估指标规则详情表")
public class EvaluateMetricsRuleDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "指标ID")
    private String metricsId;

    @ApiModelProperty(value = "规则配置ID")
    private String ruleConfigId;

    @ApiModelProperty(value = "评估字段key")
    private String fieldKey;

    @ApiModelProperty(value = "评估字段标签")
    private String fieldLabel;

    @ApiModelProperty(value = "规则类型")
    private String ruleType;

    @ApiModelProperty(value = "比较操作符")
    private String comparisonOperator;

    @ApiModelProperty(value = "规则值JSON")
    private String ruleValue;

    @ApiModelProperty(value = "打分类型")
    private String scoreType;

    @ApiModelProperty(value = "打分配置JSON")
    private String scoreConfig;

    @ApiModelProperty(value = "权重")
    private BigDecimal weight;

    @ApiModelProperty(value = "排序")
    private Integer sortOrder;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @Dict(dictTable = "sys_users", dicText = "realname", dicCode = "username")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;

    @Dict(dictTable = "sys_users", dicText = "realname", dicCode = "username")
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
}
