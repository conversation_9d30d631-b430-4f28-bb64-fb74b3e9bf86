package com.yuanqiao.insight.accountbook.modules.orderinfo.alarmconfirmevent;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.monitoring.modules.alarm.entity.AlarmHistory;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.mq.aspect.annotation.RedisMessageExtend;
import org.jeecg.common.mq.stream.Streams;
import org.jeecg.common.mq.utils.AnalyzeContent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.stream.MapRecord;
import org.springframework.data.redis.stream.StreamListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class AlarmConfirmEventListener implements StreamListener<String, MapRecord<String, String, String>> {

    @Autowired
    private AlarmConfirm alarmConfirm;

    @Override
    @Async("stream-core-pool")
    @RedisMessageExtend(stream = Streams.ALARM_CONFIRM)
    public void onMessage(MapRecord<String, String, String> message) {
        log.info("调用处理告警确认事件的方法..." );
        final Map<String, String> value = message.getValue();
        JSONObject jsonObject = AnalyzeContent.getContent(value);
        String jsonObjectString = jsonObject.getString("alarmHistoryList");
        //json串转list集合
        List<AlarmHistory> alarmHistoryList = JSONArray.parseArray(jsonObjectString, AlarmHistory.class);
        alarmConfirm.reLoadJob(alarmHistoryList);
    }
}
