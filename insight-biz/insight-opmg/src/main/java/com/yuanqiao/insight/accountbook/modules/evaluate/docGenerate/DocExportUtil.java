package com.yuanqiao.insight.accountbook.modules.evaluate.docGenerate;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSONObject;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.itextpdf.text.Paragraph;
import com.yuanqiao.insight.common.util.common.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.jeecg.common.util.DateUtils;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import org.springframework.beans.factory.annotation.Value;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
public class DocExportUtil {

    public static void main(String[] args) {
        String projectName = "XXX项目";
        String evaluateTime = DateUtil.date2Str(new Date(), new SimpleDateFormat("yyyyMMddHHmmss"));
        String versionSerial = "V1.0";
        JSONObject evaluateContent = new JSONObject();
        evaluateContent.put("totalIndicators", "10");
        evaluateContent.put("supportingMaterials", "10");
        evaluateContent.put("attachments", "10");
        List<IndicatorData> indicatorDataList = null;
        indicatorDataList = CollUtil.newArrayList(new IndicatorData("指标1", 1, 1), new IndicatorData("指标2", 2, 2), new IndicatorData("指标3", 3, 3));
        List<String> missingIndicators = CollUtil.newArrayList("指标4", "指标5");

        DocExportUtil.createReportDoc(projectName, evaluateTime, "管理员", versionSerial, evaluateContent, indicatorDataList, missingIndicators);

//        createInfoDoc(new JSONObject());
    }

    public static File createReportDoc(String projectName, String evaluateTime, String createBy, String versionSerial, JSONObject evaluateContent, List<IndicatorData> indicatorDataList, List<String> missingIndicators) {
        // 构建文件名
        String fileName = "resource/evaluate/resultReport/" + projectName + "_" + evaluateTime + "_" + versionSerial + "_" + DateUtil.date2Str(new Date(), new SimpleDateFormat("yyyyMMddHHmmss")) + ".doc";
        File outputFile = new File(fileName);

        try (XWPFDocument document = new XWPFDocument();
             FileOutputStream out = new FileOutputStream(outputFile)) {

            // 设置标题
            XWPFParagraph titleParagraph = document.createParagraph();
            titleParagraph.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun titleRun = titleParagraph.createRun();
            titleRun.setText(projectName + "项目评估报告");
            titleRun.setFontSize(18);
            titleRun.setBold(true);

            // 评估时间
            XWPFParagraph evaluationTimeParagraph = document.createParagraph();
            evaluationTimeParagraph.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun evaluationTimeRun = evaluationTimeParagraph.createRun();
            evaluationTimeRun.setText("（" + evaluateTime + "）");
            evaluationTimeRun.setFontSize(16);
            evaluationTimeRun.setBold(true);

            // 评估概况
            XWPFParagraph overviewParagraph = document.createParagraph();
            overviewParagraph.setSpacingAfterLines(6);
            XWPFRun overviewRun = overviewParagraph.createRun();
            overviewRun.setText("一、评估概况");
            overviewRun.setFontSize(14);
            overviewRun.setBold(true);

            XWPFParagraph overviewContentParagraph = document.createParagraph();
            XWPFRun overviewContentRun = overviewContentParagraph.createRun();
            overviewContentRun.setText("共评估指标" + evaluateContent.getString("totalIndicators") + "项，佐证材料" +
                    evaluateContent.getString("supportingMaterials") + "条，附件材料" +
                    evaluateContent.getString("attachments") + "份。");

            // 佐证材料完整性分析
            XWPFParagraph integrityParagraph = document.createParagraph();
            XWPFRun integrityRun = integrityParagraph.createRun();
            integrityRun.setText("二、佐证材料完整性分析");
            integrityRun.setFontSize(14);
            integrityRun.setBold(true);

            XWPFParagraph integrityContentParagraph = document.createParagraph();
            XWPFRun integrityContentRun = integrityContentParagraph.createRun();
            if (CollUtil.isNotEmpty(missingIndicators)) {
                integrityContentRun.setText("在对各项指标进行核验过程中，发现以下" + missingIndicators.size() + "项指标未提供有效佐证材料，具体如下：");
                integrityContentRun.addBreak();

                for (int i = 0; i < missingIndicators.size(); i++) {
                    integrityContentRun.setText((i + 1) + ". " + missingIndicators.get(i) + "指标");
                    integrityContentRun.addBreak();
                }
            } else {
                integrityContentRun.setText("对各项指标进行核验过程中，未发现任何问题，全部指标均已提供有效佐证材料。");
            }

            // 添加指标和佐证材料对照表标题
            XWPFParagraph tableTitleParagraph = document.createParagraph();
            tableTitleParagraph.setAlignment(ParagraphAlignment.LEFT);
            XWPFRun tableTitleRun = tableTitleParagraph.createRun();
            tableTitleRun.setText("三、具体指标和佐证材料对照表");
            tableTitleRun.setFontSize(14);
            tableTitleRun.setBold(true);

            // 创建表格
            XWPFTable table = document.createTable(indicatorDataList.size() + 1, 3);
            CTTblBorders borders = table.getCTTbl().getTblPr().addNewTblBorders();
            setTableBorder(borders, STBorder.SINGLE, 8, 0, "000000"); // 4pt粗细的黑色边框

            // 设置表格宽度为100%自适应
            CTTblPr tablePr = table.getCTTbl().addNewTblPr();
            CTTblWidth tblWidth = tablePr.addNewTblW();
            tblWidth.setType(STTblWidth.PCT);
            tblWidth.setW(BigInteger.valueOf(5000)); // 50%的页面宽度

            // 设置列宽比例为3:3:3
            int[] colWidths = {1666, 1666, 1666};
            for (int rowIdx = 0; rowIdx <= indicatorDataList.size(); rowIdx++) {
                XWPFTableRow row = table.getRow(rowIdx);
                for (int colIdx = 0; colIdx < 3; colIdx++) {
                    CTTcPr tcPr = row.getCell(colIdx).getCTTc().addNewTcPr();
                    CTTblWidth cellWidth = tcPr.addNewTcW();
                    cellWidth.setType(STTblWidth.DXA);
                    cellWidth.setW(BigInteger.valueOf(colWidths[colIdx]));
                }
            }
            // 设置表格标题行
            XWPFTableRow headerRow = table.getRow(0);
            // 设置第一列标题
            XWPFTableCell cell0 = headerRow.getCell(0);
            cell0.setText("指标名");
            cell0.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            setCellBackgroundColor(cell0, "E6F2FF"); // 淡蓝色

            // 设置第二列标题
            XWPFTableCell cell1 = headerRow.getCell(1);
            cell1.setText("佐证材料数量");
            cell1.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            setCellBackgroundColor(cell1, "E6F2FF"); // 淡蓝色

            // 设置第三列标题
            XWPFTableCell cell2 = headerRow.getCell(2);
            cell2.setText("附件数量");
            cell2.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            setCellBackgroundColor(cell2, "E6F2FF"); // 淡蓝色

            // 填充表格数据
            for (int i = 0; i < indicatorDataList.size(); i++) {
                IndicatorData data = indicatorDataList.get(i);
                XWPFTableRow row = table.getRow(i + 1);

                // 确保单元格存在
                for (int colIdx = 0; colIdx < 3; colIdx++) {
                    if (row.getCell(colIdx) == null) {
                        row.createCell();
                    }
                }

                row.getCell(0).setText(data.getIndicatorName());
                row.getCell(1).setText(String.valueOf(data.getSupportingMaterialCount()));
                row.getCell(2).setText(String.valueOf(data.getAttachmentCount()));
            }

            // 添加结论
            XWPFParagraph conclusionParagraph = document.createParagraph();
            XWPFRun conclusionRun = conclusionParagraph.createRun();
            conclusionRun.setText("四、结论：");
            conclusionRun.setFontSize(14);
            conclusionRun.setBold(true);

            XWPFParagraph conclusionContentParagraph = document.createParagraph();
            XWPFRun conclusionContentRun = conclusionContentParagraph.createRun();
            if (CollUtil.isEmpty(missingIndicators)) {
                conclusionContentRun.setText("项目指标佐证材料充足，符合要求。");
            } else {
                conclusionContentRun.setText("缺少关键性指标佐证材料，需要补充完整。");
            }

            // 添加空行
            document.createParagraph();

            // 编制人
            XWPFParagraph compilerParagraph = document.createParagraph();
            compilerParagraph.setAlignment(ParagraphAlignment.RIGHT);
            XWPFRun compilerRun = compilerParagraph.createRun();
            compilerRun.setText("编制人：" + createBy);

            // 报告日期
            XWPFParagraph dateParagraph = document.createParagraph();
            dateParagraph.setAlignment(ParagraphAlignment.RIGHT);
            XWPFRun dateRun = dateParagraph.createRun();
            dateRun.setText("报告生成日期：" + DateUtils.getDate("yyyy-MM-dd HH:mm:ss"));

            // 写入文件
            document.write(out);

            return outputFile;

        } catch (Exception e) {
            System.err.println("生成报告时出错: " + e.getMessage());
            e.printStackTrace();

            // 删除可能创建的空文件
            if (outputFile.exists()) {
                outputFile.delete();
            }

            return null;
        }
    }

    // 设置单元格背景色的辅助方法
    private static void setCellBackgroundColor(XWPFTableCell cell, String color) {
        CTTcPr tcPr = cell.getCTTc().getTcPr();
        if (tcPr == null) tcPr = cell.getCTTc().addNewTcPr();

        CTShd shd = tcPr.isSetShd() ? tcPr.getShd() : tcPr.addNewShd();
        shd.setFill(color);
        shd.setVal(STShd.CLEAR);
    }

    // 设置表格边框的辅助方法
    private static void setTableBorder(CTTblBorders borders, STBorder.Enum borderStyle, int size, int space, String color) {
        // 设置所有边框样式
        setBorder(borders.addNewBottom(), borderStyle, size, space, color);
        setBorder(borders.addNewLeft(), borderStyle, size, space, color);
        setBorder(borders.addNewRight(), borderStyle, size, space, color);
        setBorder(borders.addNewTop(), borderStyle, size, space, color);
        setBorder(borders.addNewInsideH(), borderStyle, size, space, color);
        setBorder(borders.addNewInsideV(), borderStyle, size, space, color);
    }

    // 设置单个边框的辅助方法
    private static void setBorder(CTBorder border, STBorder.Enum borderStyle, int size, int space, String color) {
        border.setVal(borderStyle);
        border.setSz(new BigInteger(String.valueOf(size)));
        border.setSpace(new BigInteger(String.valueOf(space)));
        border.setColor(color);
    }


    public static File createInfoDoc(String fileName, JSONObject jsonData) {
        File outputFile = new File(fileName);
        try (XWPFDocument document = new XWPFDocument();
             FileOutputStream out = new FileOutputStream(outputFile)) {

            // 添加标题
            XWPFParagraph titleParagraph = document.createParagraph();
            titleParagraph.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun titleRun = titleParagraph.createRun();
            titleRun.setText("佐证材料详情");
            titleRun.setFontSize(20);

            // 添加报告生成时间
            XWPFParagraph timeParagraph = document.createParagraph();
            timeParagraph.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun timeRun = timeParagraph.createRun();
            timeRun.setText("报告生成时间：" + DateUtils.getDate("yyyy-MM-dd HH:mm:ss"));

            // 计算需要的行数（每两行显示四列数据）
            int rowCount = (int) Math.ceil(jsonData.keySet().size() / 2.0);
            XWPFTable table = document.createTable(rowCount, 4);

            // 设置表格宽度为100%自适应
            CTTblPr tablePr = table.getCTTbl().addNewTblPr();
            CTTblWidth tblWidth = tablePr.addNewTblW();
            tblWidth.setType(STTblWidth.PCT);
            tblWidth.setW(BigInteger.valueOf(5000));

            // 设置列宽比例为1:3:1:3
            int[] colWidths = {500, 1500, 500, 1500};
            for (int rowIdx = 0; rowIdx < rowCount; rowIdx++) {
                XWPFTableRow row = table.getRow(rowIdx);
                for (int colIdx = 0; colIdx < 4; colIdx++) {
                    CTTcPr tcPr = row.getCell(colIdx).getCTTc().addNewTcPr();
                    CTTblWidth cellWidth = tcPr.addNewTcW();
                    cellWidth.setType(STTblWidth.DXA);
                    cellWidth.setW(BigInteger.valueOf(colWidths[colIdx]));
                }
            }

            // 动态填充表格内容
            Iterator<String> keys = jsonData.keySet().iterator();
            int currentRow = 0;
            int currentCol = 0;

            while (keys.hasNext()) {
                String key = keys.next();
                String value = jsonData.getString(key);

                // 设置键单元格（第一列和第三列）
                XWPFTableCell keyCell = table.getRow(currentRow).getCell(currentCol);
                setCellBackgroundColor(keyCell, "D9D9D9"); // 浅灰色背景
                keyCell.setText(key);

                // 设置值单元格（第二列和第四列）
                XWPFTableCell valueCell = table.getRow(currentRow).getCell(currentCol + 1);
                valueCell.setText(value);

                // 移动到下一组列（跳过两列）
                currentCol += 2;

                // 如果到达行尾，移动到下一行并重置列
                if (currentCol >= 4 && keys.hasNext()) {
                    currentRow++;
                    currentCol = 0;
                }
            }

            // 保存文档
            document.write(out);
            return outputFile;
        } catch (Exception e) {
            System.err.println("生成word文档异常！ " + e.getMessage());
            return null;
        }
    }


    public static String createReportDocTemplate(Map<String,Object> exportReportMap,  String evaluateReportTemplate, HttpServletResponse response) {
        ServletOutputStream outputStream = null;
        File tempFile = null;
        try {
            if (CollUtil.isNotEmpty((Collection<?>) exportReportMap.get("noAttachmentMetricsList"))) {
                exportReportMap.put("conclusion",  "当前评估中存在缺少关键性指标佐证材料的问题，导致部分评估内容无法得到有效验证。需要补充完整。");
            } else {
                exportReportMap.put("conclusion",  "项目指标佐证材料充足，符合要求。");
            }

            InputStream inputStream = DocExportUtil.class.getClassLoader().getResourceAsStream(evaluateReportTemplate);
            if (inputStream == null) {
                throw new RuntimeException("模板文件不存在，请检查路径是否正确：" + evaluateReportTemplate);
            }

            // 配置模板引擎并绑定策略
            Configure config = Configure.builder()
                    .useSpringEL(false)
                    .bind("detail_table", new DetailTablePolicy())
                    .build();

            // 编译模板
            XWPFTemplate template = XWPFTemplate.compile(inputStream, config);

            // 渲染模板
            template.render(exportReportMap);

            // 当前时间戳
            String timestamp = String.valueOf(System.currentTimeMillis());
            String projectName = String.valueOf(exportReportMap.get("projectName"));
            String evaluateTime = String.valueOf(exportReportMap.get("evaluateTime"));
            String versionSerial = String.valueOf(exportReportMap.get("nextVersion"));
            String fileName = projectName + "_" + evaluateTime + "_" + timestamp + "_" + versionSerial;

            // 创建临时文件
            tempFile = new File("resource/evaluate/resultReport/" + fileName + ".docx");
            FileOutputStream fos = new FileOutputStream(tempFile);
            template.write(fos);
            fos.close();

            // 设置响应头
            outputStream = response.getOutputStream();
            response.setHeader("Content-Disposition", "attachment;filename*=utf-8''" + URLUtil.encode(fileName) + ".docx");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document;charset=UTF-8");

            // 将临时文件内容写入响应流
            FileInputStream fis = new FileInputStream(tempFile);
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            fis.close();

            outputStream.flush();
            log.info("数据导出成功！");

            // 返回临时文件路径供后续处理
            return tempFile.getPath();
        } catch (Exception e) {
            log.error("生成 Word 文档失败", e);
            e.printStackTrace();
            return null;
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
            // 可以选择在这里删除临时文件，或者让调用者处理
            if (tempFile != null && tempFile.exists()) {
                // tempFile.delete(); // 取消注释此行将在响应后删除临时文件
            }
        }
    }

}