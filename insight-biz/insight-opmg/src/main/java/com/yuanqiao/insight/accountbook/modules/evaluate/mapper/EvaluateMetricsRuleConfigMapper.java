package com.yuanqiao.insight.accountbook.modules.evaluate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateMetricsRuleConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;

import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName EvaluateMetricsRuleConfigMapper
 * @description: 评估指标规则配置Mapper
 * @datetime 2025年 07月 28日
 * @version: 1.0
 */
@Mapper
public interface EvaluateMetricsRuleConfigMapper extends BaseMapper<EvaluateMetricsRuleConfig> {
    
    /**
     * 根据指标ID查询规则配置
     */
    @Select("SELECT * FROM devops_evaluate_metrics_rule_config WHERE metrics_id = #{metricsId}")
    EvaluateMetricsRuleConfig selectByMetricsId(@Param("metricsId") String metricsId);
    
    /**
     * 根据指标ID删除规则配置
     */
    @Delete("DELETE FROM devops_evaluate_metrics_rule_config WHERE metrics_id = #{metricsId}")
    int deleteByMetricsId(@Param("metricsId") String metricsId);
    
    /**
     * 统计指标的规则配置信息
     */
    @Select("SELECT rule_count FROM devops_evaluate_metrics_rule_config WHERE metrics_id = #{metricsId}")
    Map<String, Object> countByMetricsId(@Param("metricsId") String metricsId);
}
