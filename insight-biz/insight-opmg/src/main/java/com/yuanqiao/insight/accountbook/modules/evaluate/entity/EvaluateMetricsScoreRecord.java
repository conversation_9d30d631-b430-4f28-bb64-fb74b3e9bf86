package com.yuanqiao.insight.accountbook.modules.evaluate.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName EvaluateMetricsScoreRecord
 * @description: 评估指标打分记录表
 * @datetime 2025年 07月 28日
 * @version: 1.0
 */
@Data
@TableName("devops_evaluate_metrics_score_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="devops_evaluate_metrics_score_record对象", description="评估指标打分记录表")
public class EvaluateMetricsScoreRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "指标ID")
    private String metricsId;

    @ApiModelProperty(value = "规则详情ID")
    private String ruleDetailId;

    @ApiModelProperty(value = "评估字段key")
    private String fieldKey;

    @ApiModelProperty(value = "字段实际值")
    private String fieldValue;

    @ApiModelProperty(value = "规则匹配结果")
    private Integer ruleResult;

    @ApiModelProperty(value = "得分")
    private BigDecimal score;

    @ApiModelProperty(value = "满分")
    private BigDecimal maxScore;

    @ApiModelProperty(value = "得分原因")
    private String scoreReason;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "评估时间")
    private Date evaluateTime;

    @Dict(dictTable = "sys_users", dicText = "realname", dicCode = "username")
    @ApiModelProperty(value = "评估人")
    private String evaluator;

    @Dict(dictTable = "sys_users", dicText = "realname", dicCode = "username")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;

    @Dict(dictTable = "sys_users", dicText = "realname", dicCode = "username")
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
}
