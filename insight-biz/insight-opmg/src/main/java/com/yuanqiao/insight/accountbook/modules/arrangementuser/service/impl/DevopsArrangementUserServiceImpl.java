package com.yuanqiao.insight.accountbook.modules.arrangementuser.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.accountbook.modules.arrangementuser.entity.DevopsArrangementUser;
import com.yuanqiao.insight.accountbook.modules.arrangementuser.mapper.DevopsArrangementUserMapper;
import com.yuanqiao.insight.accountbook.modules.arrangementuser.service.IDevopsArrangementUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 排班用户关系表
 * @Author: jeecg-boot
 * @Date:   2021-03-12
 * @Version: V1.0
 */
@Slf4j
@Service
public class DevopsArrangementUserServiceImpl extends ServiceImpl<DevopsArrangementUserMapper, DevopsArrangementUser> implements IDevopsArrangementUserService {

    @Autowired
    private DevopsArrangementUserMapper arrangementUserMapper;

    @Override
    public Map<String, String> getArrangementUserMap(List<String> ids ) {
        log.info("ids={}",ids);
        log.info("com.yuanqiao.insight.accountbook.modules.arrangementuser.service.impl.DevopsArrangementUserServiceImpl.getArrangementUserMap(ids={})",ids);
        List<DevopsArrangementUser> devopsArrangementUsers = arrangementUserMapper.getArrangementUserList(ids);
        Map<String, String> map = new HashMap<>();
        devopsArrangementUsers.forEach(item ->{
            map.put(item.getArrangementId(),item.getUserName());
        });
        return map;
    }


    @Override
    public void addBatchArrangementUser(String arrangementIds, String userId) {
        log.info("arrangementIds={}",arrangementIds);
        log.info("com.yuanqiao.insight.accountbook.modules.arrangementuser.service.impl.DevopsArrangementUserServiceImpl.addBatchArrangementUser(arrangementIds={})",arrangementIds);
        log.info("userId={}",userId);
        log.info("com.yuanqiao.insight.accountbook.modules.arrangementuser.service.impl.DevopsArrangementUserServiceImpl.addBatchArrangementUser(userId={})",userId               );

        //判断不等于null
        if(StringUtils.isNotBlank(arrangementIds) && StringUtils.isNotBlank(userId)){
            //前台传过来的排班id的数组
            String[] arrangementIdArr = arrangementIds.split(",");
            //获取传过了排班id的用户关联
            List<DevopsArrangementUser> oldList = this.baseMapper.getArrangementUserList(Arrays.asList(arrangementIdArr));
            //将list转Map
            Map<String,DevopsArrangementUser> oldMap = oldList.stream().collect(Collectors.toMap(DevopsArrangementUser::getArrangementId,DevopsArrangementUser->DevopsArrangementUser));
            //需要添加的list
            List<DevopsArrangementUser> addArrangementUsers = new ArrayList<>(arrangementIdArr.length);
            //需要修改的list
            List<DevopsArrangementUser> updateArrangementUsers = new ArrayList<>();
            DevopsArrangementUser devopsArrangementUser = null;
            for(int i = 0 ;i<arrangementIdArr.length; i++){
                if(null == oldMap.get(arrangementIdArr[i])){
                    devopsArrangementUser = new DevopsArrangementUser();
                    devopsArrangementUser.setUserId(userId);
                    devopsArrangementUser.setArrangementId(arrangementIdArr[i]);
                    addArrangementUsers.add(devopsArrangementUser);
                }else{
                    oldMap.get(arrangementIdArr[i]).setUserId(userId);
                    updateArrangementUsers.add(oldMap.get(arrangementIdArr[i]));
                }

            }
            if(0 < addArrangementUsers.size()){
                this.saveBatch(addArrangementUsers);
            }

            if(0 < updateArrangementUsers.size()){
                this.updateBatchById(updateArrangementUsers);
            }
        }
    }


    @Override
    public void delByArrangementInfoIds(List<String> arrangementInfoIds) {
        log.info("arrangementInfoIds={}",arrangementInfoIds);
        log.info("com.yuanqiao.insight.accountbook.modules.arrangementuser.service.impl.DevopsArrangementUserServiceImpl.delByArrangementInfoIds(arrangementInfoIds={})",arrangementInfoIds);
        this.baseMapper.delByArrangementInfoIds(arrangementInfoIds);
    }
}
