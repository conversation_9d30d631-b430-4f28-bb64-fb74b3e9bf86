package com.yuanqiao.insight.accountbook.modules.app.service.impl;

import com.yuanqiao.insight.accountbook.modules.app.entity.SoftwareTaskTerminal;
import com.yuanqiao.insight.accountbook.modules.app.mapper.SoftwareTaskTerminalMapper;
import com.yuanqiao.insight.accountbook.modules.app.service.ISoftwareTaskTerminalService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 任务管理-关联终端
 * @Author: jeecg-boot
 * @Date:   2024-11-15
 * @Version: V1.0
 */
@Service
public class SoftwareTaskTerminalServiceImpl extends ServiceImpl<SoftwareTaskTerminalMapper, SoftwareTaskTerminal> implements ISoftwareTaskTerminalService {

}
