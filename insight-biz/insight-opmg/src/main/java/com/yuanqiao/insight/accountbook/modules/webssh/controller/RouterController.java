package com.yuanqiao.insight.accountbook.modules.webssh.controller;

import com.yuanqiao.insight.common.constant.CommonConstant;
import com.yuanqiao.insight.common.util.common.LocalCacheUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ssh")
public class RouterController {
    @Value(value = "${server.port}")
    private Integer port;
    @Value(value = "${server.servlet.context-path}")
    private String path;

    LocalCacheUtils cacheUtils = LocalCacheUtils.getInstance();
//    @Value(value = "${ssh.ip}")
    private String ip;

    @RequestMapping("/websshpage")
    public String websshpage(){
        return "webssh";
    }

    @GetMapping(value = "/getPath")
    public Result<?> getPath(){
        ip = (String) cacheUtils.getValueByKey(CommonConstant.DATA_DICT_KEY + "ssh_ip_ipAddress");
        return Result.OK(ip+":"+String.valueOf(port)+path);
    }

}
