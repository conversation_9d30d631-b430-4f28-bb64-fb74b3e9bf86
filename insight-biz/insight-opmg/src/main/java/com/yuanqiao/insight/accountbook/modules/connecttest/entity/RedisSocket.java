package com.yuanqiao.insight.accountbook.modules.connecttest.entity;

import lombok.Getter;

import java.io.*;
import java.net.Socket;


public class RedisSocket {

    private Socket socket;
    private OutputStream outputStream;
    private InputStream inputStream;
    @Getter
    private BufferedReader reader;
    @Getter
    private PrintWriter writer;

    public void connect(Socket socket) throws IOException {
        this.socket = socket;
        this.outputStream = socket.getOutputStream();
        this.inputStream = socket.getInputStream();
        this.reader = new BufferedReader(new InputStreamReader(inputStream));
        this.writer = new PrintWriter(outputStream, true);
    }

    public void close() {
        try {
            if (writer != null) {
                writer.close();
            }
            if (reader != null) {
                reader.close();
            }
            if (outputStream != null) {
                outputStream.close();
            }
            if (inputStream != null) {
                inputStream.close();
            }
            if (socket != null) {
                socket.close();
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

}
