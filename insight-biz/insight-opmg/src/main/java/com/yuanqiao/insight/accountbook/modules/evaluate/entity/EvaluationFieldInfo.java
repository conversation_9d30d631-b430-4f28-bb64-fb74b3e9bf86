package com.yuanqiao.insight.accountbook.modules.evaluate.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName EvaluationFieldInfo
 * @description: 评估字段信息
 * @datetime 2025年 07月 28日
 * @version: 1.0
 */
@Data
@ApiModel(value="EvaluationFieldInfo", description="评估字段信息")
public class EvaluationFieldInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "字段值")
    private String value;

    @ApiModelProperty(value = "字段标签")
    private String label;

    @ApiModelProperty(value = "字段类型")
    private String fieldType;

    @ApiModelProperty(value = "字段描述")
    private String description;

    @ApiModelProperty(value = "是否必填")
    private Boolean required;
}
