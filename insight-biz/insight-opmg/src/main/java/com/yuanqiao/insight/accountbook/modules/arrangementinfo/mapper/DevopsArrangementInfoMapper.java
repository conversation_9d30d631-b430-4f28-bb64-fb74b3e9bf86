package com.yuanqiao.insight.accountbook.modules.arrangementinfo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanqiao.insight.accountbook.modules.arrangementinfo.entity.DevopsArrangementInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 排班表
 * @Author: jeecg-boot
 * @Date:   2021-03-12
 * @Version: V1.0
 */
public interface DevopsArrangementInfoMapper extends BaseMapper<DevopsArrangementInfo> {

    /**
     * 查询排班时间
     * @return
     */
    List<DevopsArrangementInfo> getTimeMap();

    void updateBatchByIds(@Param("ids")List<String> ids);

    /**
     * 通过班次ids  获取排班
     * @param ids
     * @return
     */
    List<String> selectBySchedualId(@Param("ids")List<String> ids);
    /**
     * 通过班次ids  删除排班
     * @param ids
     * @return
     */
    void  delBySchedualId(@Param("ids")List<String> ids);

    /**
     * 通过id获取班次
     * @param ids
     * @return
     */
    List<DevopsArrangementInfo> getDevopsSchedualInfoMaps(@Param("ids") List<String> ids);

}
