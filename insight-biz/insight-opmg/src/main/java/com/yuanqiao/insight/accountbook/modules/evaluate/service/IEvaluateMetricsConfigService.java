package com.yuanqiao.insight.accountbook.modules.evaluate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateMetricsConfig;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName IEvaluateMetricsConfigService
 * @description: 评估指标配置服务接口
 * @datetime 2025年 07月 28日
 * @version: 1.0
 */
public interface IEvaluateMetricsConfigService extends IService<EvaluateMetricsConfig> {

    /**
     * 保存指标配置（统一方法）
     * @param metricsId 指标ID
     * @param configType 配置类型（1-指标字段，2-规则配置）
     * @param json 配置JSON
     * @return 是否保存成功
     */
    boolean saveConfig(String metricsId, String configType, String json);

    /**
     * 根据指标ID获取所有配置
     * @param metricsId 指标ID
     * @return 配置列表
     */
    List<EvaluateMetricsConfig> getByMetricsId(String metricsId);

    /**
     * 根据指标ID和配置类型获取配置
     * @param metricsId 指标ID
     * @param configType 配置类型（1-指标字段，2-规则配置）
     * @return 配置信息
     */
    EvaluateMetricsConfig getByMetricsIdAndType(String metricsId, String configType);

    /**
     * 获取指标的字段配置JSON
     * @param metricsId 指标ID
     * @return 字段配置JSON字符串
     */
    String getFieldConfigJson(String metricsId);

    /**
     * 获取指标的规则配置JSON
     * @param metricsId 指标ID
     * @return 规则配置JSON字符串
     */
    String getRuleConfigJson(String metricsId);

    /**
     * 获取指标的表单数据（pointName作为key，fieldData作为value）
     * @param metricsId 指标ID
     * @return 表单数据Map
     */
    Map<String, Object> getFormData(String metricsId);

    /**
     * 获取指标的完整表单信息（配置+数据）
     * @param metricsId 指标ID
     * @return 包含配置和数据的Map
     */
    Map<String, Object> getCompleteFormInfo(String metricsId);

    /**
     * 更新指标配置（统一方法）
     * @param metricsId 指标ID
     * @param configType 配置类型（1-指标字段，2-规则配置）
     * @param json 配置JSON
     * @return 是否更新成功
     */
    boolean updateConfig(String metricsId, String configType, String json);

    /**
     * 更新指标字段配置
     * @param metricsId 指标ID
     * @param json 字段配置JSON
     * @return 是否更新成功
     */
    boolean updateFieldConfig(String metricsId, String json);

    /**
     * 更新指标规则配置
     * @param metricsId 指标ID
     * @param json 规则配置JSON
     * @return 是否更新成功
     */
    boolean updateRuleConfig(String metricsId, String json);

    /**
     * 删除指标配置
     * @param metricsId 指标ID
     * @return 是否删除成功
     */
    boolean removeByMetricsId(String metricsId);

    /**
     * 根据指标ID和配置类型删除配置
     * @param metricsId 指标ID
     * @param configType 配置类型
     * @return 是否删除成功
     */
    boolean removeByMetricsIdAndType(String metricsId, String configType);

    List<Map<String,String>> parseEvaluationFieldsFromJson(String formJson)
}
