package com.yuanqiao.insight.accountbook.modules.devopsipaudit.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.accountbook.modules.devopsipaudit.entity.DevopsIpAuditTaskAlarm;
import com.yuanqiao.insight.accountbook.modules.devopsipaudit.model.DevopsIpAuditTaskAlarmModelVo;
import com.yuanqiao.insight.accountbook.modules.devopsipaudit.service.IDevopsIpAuditTaskAlarmService;
import com.yuanqiao.insight.accountbook.modules.devopsipplan.service.IDevopsIpSegmentService;
import com.yuanqiao.insight.common.util.dbType.DataSourceTypeUtils;
import com.yuanqiao.insight.service.devopsip.entity.DevopsIpSegment;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Date;

/**
 * @Description: 审计任务告警表
 * @Author: jeecg-boot
 * @Date: 2024-03-01
 * @Version: V1.0
 */
@Api(tags = "审计任务告警表")
@RestController
@RequestMapping("/devops/ip/auditTaskAlarm")
@Slf4j
public class DevopsIpAuditTaskAlarmController extends JeecgController<DevopsIpAuditTaskAlarm, IDevopsIpAuditTaskAlarmService> {
    @Autowired
    private IDevopsIpAuditTaskAlarmService devopsIpAuditTaskAlarmService;
    @Autowired
    private IDevopsIpSegmentService devopsIpSegmentService;
    @Autowired
    private DataSourceTypeUtils dataSourceTypeUtils;

    /**
     * 分页列表查询
     *
     * @param devopsIpAuditTaskAlarm
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "审计任务告警表-分页列表查询")
    @ApiOperation(value = "审计任务告警表-分页列表查询", notes = "审计任务告警表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<DevopsIpAuditTaskAlarm>> queryPageList(DevopsIpAuditTaskAlarm devopsIpAuditTaskAlarm,
                                                               @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                               @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                               HttpServletRequest req) {
        QueryWrapper<DevopsIpAuditTaskAlarm> queryWrapper = new QueryWrapper<>();
        Page<DevopsIpAuditTaskAlarm> page = new Page<DevopsIpAuditTaskAlarm>(pageNo, pageSize);
        if (StringUtils.isNotBlank(devopsIpAuditTaskAlarm.getAuditTaskName())) {
            queryWrapper.like("audit_task_name", devopsIpAuditTaskAlarm.getAuditTaskName());
        }
        if (devopsIpAuditTaskAlarm.getHandleStatus() != null) {
            queryWrapper.eq("handle_status", devopsIpAuditTaskAlarm.getHandleStatus());
        }
        if (StringUtils.isNotEmpty(devopsIpAuditTaskAlarm.getIpAddress())) {
            queryWrapper.eq("ip_address", devopsIpAuditTaskAlarm.getIpAddress());
        }
        if (devopsIpAuditTaskAlarm.getStartTime() != null && devopsIpAuditTaskAlarm.getEndTime() != null) {
            Date date = DateUtil.offsetDay(DateUtil.parse(devopsIpAuditTaskAlarm.getEndTime()), 1);
            if (dataSourceTypeUtils.getDBType().equals("highgo")){
                DateTime date1 = DateUtil.parse(devopsIpAuditTaskAlarm.getStartTime());
                DateTime date2 = DateUtil.parse(DateUtil.format(date, "yyyy-MM-dd"));
                queryWrapper.between("alarm_time", date1, date2);
            }else {
                queryWrapper.between("alarm_time", devopsIpAuditTaskAlarm.getStartTime(), DateUtil.format(date, "yyyy-MM-dd"));
            }
        }
        queryWrapper.orderByDesc("create_time");
        IPage<DevopsIpAuditTaskAlarm> pageList = devopsIpAuditTaskAlarmService.page(page, queryWrapper);
        if (CollUtil.isNotEmpty(pageList.getRecords())) {
            for (DevopsIpAuditTaskAlarm task : pageList.getRecords()) {
                StringBuilder strategyText = new StringBuilder();
                String[] strategy = task.getAuditStrategy().split(",");
                for (String str : strategy) {
                    if ("0".equals(str)) {
                        strategyText.append("使用的IP是否已配置,");
                    } else if ("1".equals(str)) {
                        strategyText.append("IP对应的MAC地址是否一致,");
                    } else if ("2".equals(str)) {
                        strategyText.append("对应的设备是否在线,");
                    }
                }
                task.setAuditStrategyText(StringUtils.chop(strategyText.toString()).trim());
            }
        }
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param devopsIpAuditTaskAlarm
     * @return
     */
    @AutoLog(value = "审计任务告警表-添加")
    @ApiOperation(value = "审计任务告警表-添加", notes = "审计任务告警表-添加")
    //@RequiresPermissions("org.jeecg.modules.demo:devops_ip_audit_task_alarm:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody DevopsIpAuditTaskAlarm devopsIpAuditTaskAlarm) {
        devopsIpAuditTaskAlarmService.save(devopsIpAuditTaskAlarm);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param devopsIpAuditTaskAlarm
     * @return
     */
    @AutoLog(value = "审计任务告警表-编辑")
    @ApiOperation(value = "审计任务告警表-编辑", notes = "审计任务告警表-编辑")
    //@RequiresPermissions("org.jeecg.modules.demo:devops_ip_audit_task_alarm:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody DevopsIpAuditTaskAlarm devopsIpAuditTaskAlarm) {
        devopsIpAuditTaskAlarmService.updateById(devopsIpAuditTaskAlarm);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "审计任务告警表-通过id删除")
    @ApiOperation(value = "审计任务告警表-通过id删除", notes = "审计任务告警表-通过id删除")
    //@RequiresPermissions("org.jeecg.modules.demo:devops_ip_audit_task_alarm:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        devopsIpAuditTaskAlarmService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * IP告警关闭
     *
     * @param id
     * @return
     */
    @AutoLog(value = "审计任务告警表-IP告警关闭")
    @ApiOperation(value = "审计任务告警表-IP告警关闭", notes = "审计任务告警表-IP告警关闭")
    @GetMapping(value = "/alarmClose")
    public Result<?> alarmClose(@RequestParam(name = "id", required = true) String id) {
        DevopsIpAuditTaskAlarm devopsIpAuditTaskAlarm = devopsIpAuditTaskAlarmService.getById(id);
        if (devopsIpAuditTaskAlarm == null) {
            return Result.error("未找到对应数据");
        }
        devopsIpAuditTaskAlarm.setHandleStatus(2);
        devopsIpAuditTaskAlarmService.updateById(devopsIpAuditTaskAlarm);
        return Result.OK("操作成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "审计任务告警表-批量删除")
    @ApiOperation(value = "审计任务告警表-批量删除", notes = "审计任务告警表-批量删除")
    //@RequiresPermissions("org.jeecg.modules.demo:devops_ip_audit_task_alarm:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.devopsIpAuditTaskAlarmService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "审计任务告警表-通过id查询")
    @ApiOperation(value = "审计任务告警表-通过id查询", notes = "审计任务告警表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        DevopsIpAuditTaskAlarm devopsIpAuditTaskAlarm = devopsIpAuditTaskAlarmService.getById(id);
        if (devopsIpAuditTaskAlarm == null) {
            return Result.error("未找到对应数据");
        }
        DevopsIpAuditTaskAlarmModelVo devopsIpAuditTaskAlarmModelVo = new DevopsIpAuditTaskAlarmModelVo();
        BeanUtils.copyProperties(devopsIpAuditTaskAlarm, devopsIpAuditTaskAlarmModelVo);
        if (devopsIpAuditTaskAlarmModelVo.getAuditStrategy().equals("0")) {
            devopsIpAuditTaskAlarmModelVo.setAuditStrategyText("使用IP需申请");
        } else if (devopsIpAuditTaskAlarmModelVo.getAuditStrategy().equals("1")) {
            devopsIpAuditTaskAlarmModelVo.setAuditStrategyText("IP和MAC地址已变更");
        } else if (devopsIpAuditTaskAlarmModelVo.getAuditStrategy().equals("2")) {
            devopsIpAuditTaskAlarmModelVo.setAuditStrategyText("设备离线");
        }
        if (devopsIpAuditTaskAlarmModelVo.getSegmentId() != null) {
            DevopsIpSegment devopsIpSegment = devopsIpSegmentService.getById(devopsIpAuditTaskAlarmModelVo.getSegmentId());
            if (devopsIpSegment != null) {
                devopsIpAuditTaskAlarmModelVo.setLocation(devopsIpSegment.getLocation());
                devopsIpAuditTaskAlarmModelVo.setStartIp(devopsIpSegment.getStartIp());
                devopsIpAuditTaskAlarmModelVo.setEndIp(devopsIpSegment.getEndIp());
                devopsIpAuditTaskAlarmModelVo.setDepartName(devopsIpSegment.getDepartName());
                devopsIpAuditTaskAlarmModelVo.setSegmentName(devopsIpSegment.getSegmentName());
                devopsIpAuditTaskAlarmModelVo.setLocation(devopsIpSegment.getLocation());
            }
        }
        return Result.OK(devopsIpAuditTaskAlarmModelVo);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param devopsIpAuditTaskAlarm
     */
    //@RequiresPermissions("org.jeecg.modules.demo:devops_ip_audit_task_alarm:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DevopsIpAuditTaskAlarm devopsIpAuditTaskAlarm) {
        return super.exportXls(request, devopsIpAuditTaskAlarm, DevopsIpAuditTaskAlarm.class, "审计任务告警表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    //@RequiresPermissions("devops_ip_audit_task_alarm:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, DevopsIpAuditTaskAlarm.class);
    }

}
