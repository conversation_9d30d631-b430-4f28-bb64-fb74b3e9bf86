package com.yuanqiao.insight.accountbook.modules.evaluate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateMetricsFieldConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;

import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName EvaluateMetricsFieldConfigMapper
 * @description: 评估指标字段配置Mapper
 * @datetime 2025年 07月 28日
 * @version: 1.0
 */
@Mapper
public interface EvaluateMetricsFieldConfigMapper extends BaseMapper<EvaluateMetricsFieldConfig> {
    
    /**
     * 根据指标ID查询字段配置
     */
    @Select("SELECT * FROM devops_evaluate_metrics_field_config WHERE metrics_id = #{metricsId}")
    EvaluateMetricsFieldConfig selectByMetricsId(@Param("metricsId") String metricsId);
    
    /**
     * 根据指标ID删除字段配置
     */
    @Delete("DELETE FROM devops_evaluate_metrics_field_config WHERE metrics_id = #{metricsId}")
    int deleteByMetricsId(@Param("metricsId") String metricsId);
    
    /**
     * 统计指标的字段配置信息
     */
    @Select("SELECT field_count, evaluation_field_count FROM devops_evaluate_metrics_field_config WHERE metrics_id = #{metricsId}")
    Map<String, Object> countByMetricsId(@Param("metricsId") String metricsId);
}
