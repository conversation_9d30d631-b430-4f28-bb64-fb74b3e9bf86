package com.yuanqiao.insight.accountbook.modules.evaluate.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateMetricsRuleDetail;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateMetricsScoreRecord;
import com.yuanqiao.insight.accountbook.modules.evaluate.mapper.EvaluateMetricsScoreRecordMapper;
import com.yuanqiao.insight.accountbook.modules.evaluate.service.IEvaluateMetricsRuleDetailService;
import com.yuanqiao.insight.accountbook.modules.evaluate.service.IEvaluateMetricsScoreService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.util.UUIDGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @ClassName EvaluateMetricsScoreServiceImpl
 * @description: 评估指标打分服务实现类
 * @datetime 2025年 07月 28日
 * @version: 1.0
 */
@Service
@Slf4j
public class EvaluateMetricsScoreServiceImpl extends ServiceImpl<EvaluateMetricsScoreRecordMapper, EvaluateMetricsScoreRecord> implements IEvaluateMetricsScoreService {

    @Autowired
    private IEvaluateMetricsRuleDetailService ruleDetailService;

    @Override
    @Transactional
    public Map<String, Object> executeEvaluation(String projectId, String metricsId, Map<String, Object> fieldValues, String evaluator) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取评估规则详情
            List<EvaluateMetricsRuleDetail> ruleDetails = ruleDetailService.listByMetricsId(metricsId);
            if (ruleDetails.isEmpty()) {
                result.put("success", false);
                result.put("message", "未找到评估规则");
                return result;
            }
            
            List<EvaluateMetricsScoreRecord> scoreRecords = new ArrayList<>();
            BigDecimal totalScore = BigDecimal.ZERO;
            BigDecimal totalMaxScore = BigDecimal.ZERO;
            int passedRules = 0;
            
            Date evaluateTime = new Date();
            
            // 遍历每个规则进行评估
            for (EvaluateMetricsRuleDetail ruleDetail : ruleDetails) {
                String fieldKey = ruleDetail.getFieldKey();
                Object fieldValue = fieldValues.get(fieldKey);
                
                // 执行规则评估
                Map<String, Object> ruleResult = evaluateRule(ruleDetail, fieldValue);
                
                // 创建打分记录
                EvaluateMetricsScoreRecord scoreRecord = new EvaluateMetricsScoreRecord();
                scoreRecord.setId(UUIDGenerator.generate());
                scoreRecord.setProjectId(projectId);
                scoreRecord.setMetricsId(metricsId);
                scoreRecord.setRuleDetailId(ruleDetail.getId());
                scoreRecord.setFieldKey(fieldKey);
                scoreRecord.setFieldValue(fieldValue != null ? fieldValue.toString() : "");
                scoreRecord.setRuleResult((Integer) ruleResult.get("ruleResult"));
                scoreRecord.setScore((BigDecimal) ruleResult.get("score"));
                scoreRecord.setMaxScore((BigDecimal) ruleResult.get("maxScore"));
                scoreRecord.setScoreReason((String) ruleResult.get("scoreReason"));
                scoreRecord.setEvaluateTime(evaluateTime);
                scoreRecord.setEvaluator(evaluator);
                scoreRecord.setCreateTime(evaluateTime);
                
                scoreRecords.add(scoreRecord);
                
                // 累计分数
                totalScore = totalScore.add(scoreRecord.getScore());
                totalMaxScore = totalMaxScore.add(scoreRecord.getMaxScore());
                
                if (scoreRecord.getRuleResult() == 1) {
                    passedRules++;
                }
            }
            
            // 批量保存打分记录
            this.saveBatch(scoreRecords);
            
            // 计算通过率
            BigDecimal passRate = BigDecimal.ZERO;
            if (totalMaxScore.compareTo(BigDecimal.ZERO) > 0) {
                passRate = totalScore.divide(totalMaxScore, 4, BigDecimal.ROUND_HALF_UP)
                          .multiply(new BigDecimal("100"));
            }
            
            result.put("success", true);
            result.put("totalScore", totalScore);
            result.put("totalMaxScore", totalMaxScore);
            result.put("passRate", passRate);
            result.put("totalRules", ruleDetails.size());
            result.put("passedRules", passedRules);
            result.put("scoreDetails", scoreRecords);
            result.put("evaluateTime", evaluateTime);
            
        } catch (Exception e) {
            log.error("执行评估打分失败", e);
            result.put("success", false);
            result.put("message", "评估失败：" + e.getMessage());
        }
        
        return result;
    }

    @Override
    public List<EvaluateMetricsScoreRecord> getScoreRecords(String projectId, String metricsId) {
        return baseMapper.selectByProjectAndMetrics(projectId, metricsId);
    }

    @Override
    public Map<String, Object> getTotalScore(String projectId, String metricsId) {
        return baseMapper.countScoreByProjectAndMetrics(projectId, metricsId);
    }

    @Override
    @Transactional
    public Map<String, Object> recalculateScore(String projectId, String metricsId, String evaluator) {
        try {
            // 删除现有的打分记录
            Map<String, Object> queryMap = new HashMap<>();
            queryMap.put("project_id", projectId);
            queryMap.put("metrics_id", metricsId);
            this.removeByMap(queryMap);
            
            // 重新获取字段值（这里需要根据实际业务逻辑获取）
            Map<String, Object> fieldValues = getFieldValuesFromProject(projectId, metricsId);
            
            // 重新执行评估
            return executeEvaluation(projectId, metricsId, fieldValues, evaluator);
            
        } catch (Exception e) {
            log.error("重新计算打分失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "重新计算失败：" + e.getMessage());
            return result;
        }
    }

    /**
     * 执行单个规则评估
     */
    private Map<String, Object> evaluateRule(EvaluateMetricsRuleDetail ruleDetail, Object fieldValue) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String ruleType = ruleDetail.getRuleType();
            String comparisonOperator = ruleDetail.getComparisonOperator();
            String ruleValue = ruleDetail.getRuleValue();
            String scoreConfig = ruleDetail.getScoreConfig();
            
            // 执行规则匹配
            boolean ruleMatched = false;
            String scoreReason = "";
            
            switch (ruleType) {
                case "regular_judgment":
                    ruleMatched = evaluateRegularJudgment(fieldValue, comparisonOperator, ruleValue);
                    break;
                case "quantitative_interval":
                case "frequency_quantity":
                case "timeliness":
                    ruleMatched = evaluateQuantitative(fieldValue, comparisonOperator, ruleValue);
                    break;
                case "compliance":
                    ruleMatched = evaluateCompliance(fieldValue, comparisonOperator, ruleValue);
                    break;
                default:
                    ruleMatched = false;
                    scoreReason = "未知的规则类型：" + ruleType;
            }
            
            // 计算得分
            Map<String, Object> scoreResult = calculateScore(ruleMatched, scoreConfig);
            
            result.put("ruleResult", ruleMatched ? 1 : 0);
            result.put("score", scoreResult.get("score"));
            result.put("maxScore", scoreResult.get("maxScore"));
            result.put("scoreReason", StringUtils.isBlank(scoreReason) ? 
                      (ruleMatched ? "规则匹配成功" : "规则匹配失败") : scoreReason);
            
        } catch (Exception e) {
            log.error("规则评估失败", e);
            result.put("ruleResult", 0);
            result.put("score", BigDecimal.ZERO);
            result.put("maxScore", BigDecimal.TEN);
            result.put("scoreReason", "规则评估异常：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 常规判断类规则评估
     */
    private boolean evaluateRegularJudgment(Object fieldValue, String operator, String ruleValue) {
        switch (operator) {
            case "exists":
                return fieldValue != null && !fieldValue.toString().trim().isEmpty();
            case "not_exists":
                return fieldValue == null || fieldValue.toString().trim().isEmpty();
            case "==":
                return Objects.equals(fieldValue != null ? fieldValue.toString() : "", ruleValue);
            case "!=":
                return !Objects.equals(fieldValue != null ? fieldValue.toString() : "", ruleValue);
            default:
                return false;
        }
    }

    /**
     * 数量类规则评估
     */
    private boolean evaluateQuantitative(Object fieldValue, String operator, String ruleValue) {
        try {
            if (fieldValue == null) return false;
            
            double fieldNum = Double.parseDouble(fieldValue.toString());
            double ruleNum = Double.parseDouble(ruleValue);
            
            switch (operator) {
                case ">":
                    return fieldNum > ruleNum;
                case ">=":
                    return fieldNum >= ruleNum;
                case "<":
                    return fieldNum < ruleNum;
                case "<=":
                    return fieldNum <= ruleNum;
                case "==":
                    return fieldNum == ruleNum;
                case "between":
                    // 区间判断，ruleValue格式：min,max
                    String[] range = ruleValue.split(",");
                    if (range.length == 2) {
                        double min = Double.parseDouble(range[0]);
                        double max = Double.parseDouble(range[1]);
                        return fieldNum >= min && fieldNum <= max;
                    }
                    return false;
                default:
                    return false;
            }
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 合规性规则评估
     */
    private boolean evaluateCompliance(Object fieldValue, String operator, String ruleValue) {
        if (fieldValue == null) return false;
        
        String fieldStr = fieldValue.toString();
        
        switch (operator) {
            case "contains":
                return fieldStr.contains(ruleValue);
            case "not_contains":
                return !fieldStr.contains(ruleValue);
            case "==":
                return fieldStr.equals(ruleValue);
            case "!=":
                return !fieldStr.equals(ruleValue);
            case "exists":
                return !fieldStr.trim().isEmpty();
            case "not_exists":
                return fieldStr.trim().isEmpty();
            default:
                return false;
        }
    }

    /**
     * 计算得分
     */
    private Map<String, Object> calculateScore(boolean ruleMatched, String scoreConfig) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (StringUtils.isBlank(scoreConfig)) {
                // 默认打分：匹配得10分，不匹配得0分
                result.put("score", ruleMatched ? BigDecimal.TEN : BigDecimal.ZERO);
                result.put("maxScore", BigDecimal.TEN);
                return result;
            }
            
            JSONObject config = JSON.parseObject(scoreConfig);
            String scoreType = config.getString("scoreType");
            
            if ("fixed".equals(scoreType)) {
                // 固定分数
                BigDecimal score = config.getBigDecimal("score");
                BigDecimal maxScore = config.getBigDecimal("maxScore");
                
                result.put("score", ruleMatched ? score : BigDecimal.ZERO);
                result.put("maxScore", maxScore != null ? maxScore : score);
                
            } else {
                // 其他打分类型的实现...
                result.put("score", ruleMatched ? BigDecimal.TEN : BigDecimal.ZERO);
                result.put("maxScore", BigDecimal.TEN);
            }
            
        } catch (Exception e) {
            log.error("计算得分失败", e);
            result.put("score", BigDecimal.ZERO);
            result.put("maxScore", BigDecimal.TEN);
        }
        
        return result;
    }

    /**
     * 从项目中获取字段值（需要根据实际业务逻辑实现）
     */
    private Map<String, Object> getFieldValuesFromProject(String projectId, String metricsId) {
        // TODO: 根据实际业务逻辑实现
        // 这里应该从项目数据中获取对应字段的值
        return new HashMap<>();
    }
}
