package com.yuanqiao.insight.accountbook.modules.devopsipaudit.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.accountbook.modules.devopsipplan.entity.DevopsIpSubnetGroup;
import com.yuanqiao.insight.accountbook.modules.devopsipplan.service.IDevopsIpSegmentService;
import com.yuanqiao.insight.accountbook.modules.devopsipplan.service.IDevopsIpSubnetGroupService;
import com.yuanqiao.insight.accountbook.modules.devopsipplan.service.IDevopsIpSubnetService;
import com.yuanqiao.insight.service.device.entity.DeviceInfo;
import com.yuanqiao.insight.service.device.entity.DeviceScanRecord;
import com.yuanqiao.insight.service.device.entity.DeviceScanTask;
import com.yuanqiao.insight.service.device.entity.MomgAbutmentTask;
import com.yuanqiao.insight.service.device.mapper.DeviceInfoMapper;
import com.yuanqiao.insight.service.device.service.IDeviceScanRecordService;
import com.yuanqiao.insight.service.device.service.IMomgAbutmentTaskService;
import com.yuanqiao.insight.service.device.service.impl.DeviceScanTaskServiceImpl;
import com.yuanqiao.insight.service.devopsip.entity.DevopsIpSegment;
import com.yuanqiao.insight.service.devopsip.entity.DevopsIpSubnet;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 设备扫描任务表
 * @Author: jeecg-boot
 * @Date: 2021-04-01
 * @Version: V1.0
 */
@Api(tags = "设备扫描任务表")
@RestController
@RequestMapping("/deviceScan/task")
@Slf4j
public class DeviceScanTaskController extends JeecgController<MomgAbutmentTask, IMomgAbutmentTaskService> {
    @Autowired
    private DeviceScanTaskServiceImpl deviceScanTaskService;
    @Autowired
    private IDeviceScanRecordService deviceScanRecordService;
    @Autowired
    private DeviceInfoMapper deviceInfoMapper;
    @Autowired
    IDevopsIpSubnetGroupService devopsIpSubnetGroupService;
    @Autowired
    IDevopsIpSubnetService devopsIpSubnetService;
    @Autowired
    IDevopsIpSegmentService devopsIpSegmentService;

    /**
     * 分页列表查询
     *
     * @param deviceScanTask
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "设备扫描任务表-分页列表查询")
    @ApiOperation(value = "设备扫描任务表-分页列表查询", notes = "设备扫描任务表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(DeviceScanTask deviceScanTask,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<DeviceScanTask> queryWrapper = new QueryWrapper<DeviceScanTask>();
        if (StringUtils.isNotEmpty(deviceScanTask.getTaskName())){
            queryWrapper.like("task_name", deviceScanTask.getTaskName());
        }
        if (deviceScanTask.getIsEnable() != null) {
            queryWrapper.eq("is_enable", deviceScanTask.getIsEnable());
        }
        if (StringUtils.isNotEmpty(deviceScanTask.getGatewayCode())){
            queryWrapper.eq("gateway_code", deviceScanTask.getGatewayCode());
        }
        if (StringUtils.isNotEmpty(deviceScanTask.getTransferProtocol())){
            queryWrapper.eq("transfer_protocol", deviceScanTask.getTransferProtocol());
        }
        if (deviceScanTask.getExecuteType() != null){
            queryWrapper.eq("execute_type", deviceScanTask.getExecuteType());
        }
        if (StringUtils.isNotEmpty(deviceScanTask.getFilterType())){
            queryWrapper.eq("filter_type", deviceScanTask.getFilterType());
        }
        if (StringUtils.isNotEmpty(deviceScanTask.getIpFilter())){
            queryWrapper.eq("ip_filter", deviceScanTask.getIpFilter());
        }
        IPage<DeviceScanTask> pageList = deviceScanTaskService.page(new Page<DeviceScanTask>(pageNo, pageSize), queryWrapper);

        if (CollUtil.isNotEmpty(pageList.getRecords())) {
            Map<String, DeviceInfo> gawayDevMap = new HashMap<>();
            List<DeviceInfo> gatewayDeviceList = deviceInfoMapper.selectAllGatewayDevices(new ArrayList<>());
            if (CollUtil.isNotEmpty(gatewayDeviceList)) {
                gawayDevMap = gatewayDeviceList.stream().collect(Collectors.toMap(DeviceInfo::getDeviceCode, deviceInfo -> deviceInfo));
            }
            Map<String, DeviceInfo> finalGawayDevMap = gawayDevMap;
            pageList.getRecords().forEach(scanTask -> {
                scanTask.setScanRecordNum(deviceScanRecordService.count(new QueryWrapper<DeviceScanRecord>().eq("task_id", scanTask.getId())));
                if (finalGawayDevMap.containsKey(scanTask.getGatewayCode())) {
                    DeviceInfo deviceInfo = finalGawayDevMap.get(scanTask.getGatewayCode());
                    scanTask.setGatewayName(deviceInfo.getName());
                }

                switch (scanTask.getFilterType()) {
                    case "0":
                        DevopsIpSubnetGroup ipSubnetGroup = devopsIpSubnetGroupService.getById(scanTask.getIpFilter());
                        if (ipSubnetGroup != null) {
                            scanTask.setIpName(ipSubnetGroup.getSubnetGroupName());
                        }
                        break;
                    case "1":
                        DevopsIpSubnet ipSubnet = devopsIpSubnetService.getById(scanTask.getIpFilter());
                        if (ipSubnet != null) {
                            scanTask.setIpName(ipSubnet.getSubnetName());
                        }
                        break;
                    case "2":
                        DevopsIpSegment ipSegment = devopsIpSegmentService.getById(scanTask.getIpFilter());
                        if (ipSegment != null) {
                            scanTask.setIpName(ipSegment.getSegmentName());
                        }
                        break;
                    default:
                        break;
                }
            });
        }
        return Result.OK(pageList);
    }

    /**
     * 查询任务执行结果列表
     *
     * @param taskId
     * @return
     */
    @AutoLog(value = "设备扫描任务表-查询任务执行结果列表")
    @ApiOperation(value = "设备扫描任务表-查询任务执行结果列表", notes = "设备扫描任务表-查询任务执行结果列表")
    @GetMapping(value = "/queryRecordsById")
    public Result<?> queryRecordsById(@RequestParam(name = "taskId", required = true) String taskId,
                               String ip,
                               @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                               @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {

        QueryWrapper<DeviceScanRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_id", taskId);
        if (StringUtils.isNotEmpty(ip)) {
            queryWrapper.eq("ip", ip);
        }
        queryWrapper.orderByAsc("ip");
        IPage<DeviceScanRecord> pageList = deviceScanRecordService.page(new Page<>(pageNo, pageSize), queryWrapper);
        if (CollUtil.isNotEmpty(pageList.getRecords())) {
            pageList.getRecords().forEach(deviceScanRecord -> {
                deviceScanRecord.setRecordColumnJson(JSONArray.parseArray(deviceScanRecord.getRecordColumn()));
            });
        }
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param deviceScanTask
     * @return
     */
    @AutoLog(value = "设备扫描任务表-添加")
    @ApiOperation(value = "设备扫描任务表-添加", notes = "设备扫描任务表-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody DeviceScanTask deviceScanTask) {
        Result<?> result = deviceScanTaskService.saveTask(deviceScanTask);
        return result;
    }


    /**
     * 编辑
     *
     * @param deviceScanTask
     * @return
     */
    @AutoLog(value = "设备扫描任务表-编辑")
    @ApiOperation(value = "设备扫描任务表-编辑", notes = "设备扫描任务表-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody DeviceScanTask deviceScanTask) {
        Result<?> result = deviceScanTaskService.updateTask(deviceScanTask);
        return result;
    }

    /**
     * 编辑
     *
     * @param deviceScanRecord
     * @return
     */
    @AutoLog(value = "设备扫描结果表-编辑")
    @ApiOperation(value = "设备扫描结果表-编辑", notes = "设备扫描结果表-编辑")
    @PutMapping(value = "/editRecord")
    public Result<?> editRecord(@RequestBody DeviceScanRecord deviceScanRecord) {
        boolean b = deviceScanRecordService.updateById(deviceScanRecord);
        if (b) {
            return Result.OK("操作成功!");
        } else {
            return Result.error("操作失败!");
        }
    }

    /**
     * 立即执行
     *
     * @param deviceScanTask
     * @return
     */
    @AutoLog(value = "设备扫描任务表-立即执行")
    @ApiOperation(value = "设备扫描任务表-立即执行", notes = "设备扫描任务表-立即执行")
    @PutMapping(value = "/execute")
    public Result<?> execute(@RequestBody DeviceScanTask deviceScanTask) {
        deviceScanTaskService.execute(deviceScanTask);
        return Result.OK("操作成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "设备扫描任务表-通过id删除")
    @ApiOperation(value = "设备扫描任务表-通过id删除", notes = "设备扫描任务表-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        deviceScanTaskService.deleteTask(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "设备扫描任务表-批量删除")
    @ApiOperation(value = "设备扫描任务表-批量删除", notes = "设备扫描任务表-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        List<String> list = Arrays.asList(ids.split(","));
        list.forEach(id -> {
            deviceScanTaskService.deleteTask(id);
        });
        return Result.OK("批量删除成功！");
    }

    /**
     * 导出excel
     *
     * @param request
     * @param abutmentTask
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, MomgAbutmentTask abutmentTask) {
        return super.exportXls(request, abutmentTask, MomgAbutmentTask.class, "设备扫描任务表");
    }


}
