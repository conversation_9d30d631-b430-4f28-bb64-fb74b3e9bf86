package com.yuanqiao.insight.accountbook.modules.evaluate.controller;

import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateMetricsFieldConfig;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluationFieldInfo;
import com.yuanqiao.insight.accountbook.modules.evaluate.service.IEvaluateMetricsFieldConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName EvaluateMetricsFieldConfigController
 * @description: 评估指标字段配置控制器
 * @datetime 2025年 07月 28日
 * @version: 1.0
 */
@Api(tags = "评估指标字段配置")
@RestController
@RequestMapping("/evaluate/metricsFieldConfig")
@Slf4j
public class EvaluateMetricsFieldConfigController extends JeecgController<EvaluateMetricsFieldConfig, IEvaluateMetricsFieldConfigService> {

    @Autowired
    private IEvaluateMetricsFieldConfigService fieldConfigService;

    @AutoLog(value = "保存指标字段配置")
    @ApiOperation(value = "保存指标字段配置", notes = "Tab2保存字段配置")
    @PostMapping(value = "/save/{metricsId}")
    public Result<?> saveFieldConfig(@PathVariable("metricsId") String metricsId, 
                                   @RequestBody Map<String, Object> requestData) {
        try {
            String formConfig = (String) requestData.get("formConfig");
            if (formConfig == null || formConfig.trim().isEmpty()) {
                return Result.error("表单配置不能为空");
            }

            boolean result = fieldConfigService.saveFieldConfig(metricsId, formConfig);
            if (result) {
                return Result.OK("保存成功！");
            } else {
                return Result.error("保存失败！");
            }
        } catch (Exception e) {
            log.error("保存指标字段配置失败", e);
            return Result.error("保存失败：" + e.getMessage());
        }
    }

    @AutoLog(value = "获取指标字段配置")
    @ApiOperation(value = "获取指标字段配置", notes = "获取指标字段配置")
    @GetMapping(value = "/get/{metricsId}")
    public Result<EvaluateMetricsFieldConfig> getFieldConfig(@PathVariable("metricsId") String metricsId) {
        try {
            EvaluateMetricsFieldConfig config = fieldConfigService.getByMetricsId(metricsId);
            return Result.OK(config);
        } catch (Exception e) {
            log.error("获取指标字段配置失败", e);
            return Result.error("获取失败：" + e.getMessage());
        }
    }

    @AutoLog(value = "获取评估字段列表")
    @ApiOperation(value = "获取评估字段列表", notes = "Tab3获取评估字段选项")
    @GetMapping(value = "/evaluation-fields/{metricsId}")
    public Result<List<EvaluationFieldInfo>> getEvaluationFields(@PathVariable("metricsId") String metricsId) {
        try {
            List<EvaluationFieldInfo> fields = fieldConfigService.getEvaluationFields(metricsId);
            return Result.OK(fields);
        } catch (Exception e) {
            log.error("获取评估字段列表失败", e);
            return Result.error("获取失败：" + e.getMessage());
        }
    }

    @AutoLog(value = "更新指标字段配置")
    @ApiOperation(value = "更新指标字段配置", notes = "更新指标字段配置")
    @PutMapping(value = "/update/{metricsId}")
    public Result<?> updateFieldConfig(@PathVariable("metricsId") String metricsId,
                                     @RequestBody Map<String, Object> requestData) {
        try {
            String formConfig = (String) requestData.get("formConfig");
            boolean result = fieldConfigService.updateFieldConfig(metricsId, formConfig);
            if (result) {
                return Result.OK("更新成功！");
            } else {
                return Result.error("更新失败！");
            }
        } catch (Exception e) {
            log.error("更新指标字段配置失败", e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }

    @AutoLog(value = "删除指标字段配置")
    @ApiOperation(value = "删除指标字段配置", notes = "删除指标字段配置")
    @DeleteMapping(value = "/delete/{metricsId}")
    public Result<?> deleteFieldConfig(@PathVariable("metricsId") String metricsId) {
        try {
            boolean result = fieldConfigService.removeByMetricsId(metricsId);
            if (result) {
                return Result.OK("删除成功！");
            } else {
                return Result.error("删除失败！");
            }
        } catch (Exception e) {
            log.error("删除指标字段配置失败", e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }
}
