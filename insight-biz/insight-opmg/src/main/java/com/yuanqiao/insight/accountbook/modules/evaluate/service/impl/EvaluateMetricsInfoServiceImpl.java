package com.yuanqiao.insight.accountbook.modules.evaluate.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateMetricsInfo;
import com.yuanqiao.insight.accountbook.modules.evaluate.mapper.EvaluateMetricsInfoMapper;
import com.yuanqiao.insight.accountbook.modules.evaluate.service.IEvaluateMetricsInfoService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName EvaluateMetricsServiceImpl
 * @description: TODO
 * @datetime 2025年 05月 29日 19:30
 * @version: 1.0
 */
@Service
public class EvaluateMetricsInfoServiceImpl extends ServiceImpl<EvaluateMetricsInfoMapper, EvaluateMetricsInfo> implements IEvaluateMetricsInfoService {
    @Override
    public List<EvaluateMetricsInfo> listByMetricsTypeId(String id) {
        return baseMapper.selectList(
                new QueryWrapper<EvaluateMetricsInfo>()
                        .eq("metrics_type_id", id)
        );
    }
}
