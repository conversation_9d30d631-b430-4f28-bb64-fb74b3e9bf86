package com.yuanqiao.insight.accountbook.modules.autoInspection.generater;

import com.yuanqiao.insight.accountbook.modules.autoInspection.entity.DevopsAutoInspection;

/**
 * <AUTHOR>
 * @title: AIRGenerater            定义自动巡检报告生成组件接口
 * @projectName jeecg-boot-parent
 * @description: TODO
 * @date 2021/5/15-11:36
 */
public interface AIRGenerater {

    /**
     * 生成报告
     * @param autoInspection 自动巡检任务类
     * @throws
     */
    public void report(DevopsAutoInspection autoInspection,AITask aITask) throws Exception;
}
