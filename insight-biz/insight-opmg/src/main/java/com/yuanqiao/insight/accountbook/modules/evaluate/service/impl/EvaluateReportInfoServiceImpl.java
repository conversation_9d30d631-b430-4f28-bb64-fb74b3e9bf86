package com.yuanqiao.insight.accountbook.modules.evaluate.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.accountbook.modules.evaluate.entity.EvaluateReportInfo;
import com.yuanqiao.insight.accountbook.modules.evaluate.mapper.EvaluateReportInfoMapper;
import com.yuanqiao.insight.accountbook.modules.evaluate.service.IEvaluateReportInfoService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName EvaluateReportInfoServiceImpl
 * @description: TODO
 * @datetime 2025年 05月 29日 19:30
 * @version: 1.0
 */
@Service
public class EvaluateReportInfoServiceImpl extends ServiceImpl<EvaluateReportInfoMapper, EvaluateReportInfo> implements IEvaluateReportInfoService {
}
