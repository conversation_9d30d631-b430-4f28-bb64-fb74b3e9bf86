package com.yuanqiao.insight.accountbook.modules.orderinfo.alarmconfirmevent;

import com.yuanqiao.insight.accountbook.modules.orderinfo.entity.DevopsOrderInfo;
import com.yuanqiao.insight.accountbook.modules.orderinfo.service.IDevopsOrderInfoService;
import com.yuanqiao.insight.common.constant.CommonConstant;
import com.yuanqiao.insight.monitoring.modules.alarm.entity.AlarmHistory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class AlarmConfirmImpl implements AlarmConfirm {
    @Autowired
    private IDevopsOrderInfoService devopsOrderInfoService;
    @Override
    public void reLoadJob(List<AlarmHistory> alarmHistoryList) {
        log.info("开始处理确认告警事件...");
        try {
            List<DevopsOrderInfo> addDevopsOrderInfo = new ArrayList<>(alarmHistoryList.size());
            DevopsOrderInfo devopsOrderInfo = null;
            for (AlarmHistory item:alarmHistoryList) {
                devopsOrderInfo = new DevopsOrderInfo();
                devopsOrderInfo.setOrderName(item.getTemplateName());
                devopsOrderInfo.setOrderSource(CommonConstant.DEVOPS_ORDER_INFO_SOURCE_2);
                devopsOrderInfo.setOrderDescription(item.getErrorDesc());
                devopsOrderInfo.setOrderCategoryId(item.getErrorType());
                devopsOrderInfo.setWarningId(item.getId());//告警id
                devopsOrderInfo.setConfirmUserId(item.getConfirmById());//确认人id
                devopsOrderInfo.setOrderState(CommonConstant.DEVOPS_ORDER_INFO_STATE_0);//工单状态
                devopsOrderInfo.setWamingCreateTime(item.getAlarmTime2());//故障触发时间
                devopsOrderInfo.setResolveDate(item.getResolveDate());
                addDevopsOrderInfo.add(devopsOrderInfo);
            }
            devopsOrderInfoService.saveBatch(addDevopsOrderInfo);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}