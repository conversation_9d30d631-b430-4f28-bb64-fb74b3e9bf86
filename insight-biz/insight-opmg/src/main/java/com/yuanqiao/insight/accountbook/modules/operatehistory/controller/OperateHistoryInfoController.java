package com.yuanqiao.insight.accountbook.modules.operatehistory.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.accountbook.modules.operatehistory.entity.OperateHistoryInfo;
import com.yuanqiao.insight.accountbook.modules.operatehistory.service.IOperateHistoryInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Map;

 /**
 * @Description: 操作历史表
 * @Author: jeecg-boot
 * @Date:   2021-03-11
 * @Version: V1.0
 */
@Api(tags="操作历史表")
@RestController
@RequestMapping("/operate_history/operateHistoryInfo")
@Slf4j
public class OperateHistoryInfoController extends JeecgController<OperateHistoryInfo, IOperateHistoryInfoService> {
	@Autowired
	private IOperateHistoryInfoService operateHistoryInfoService;
	 @Autowired
	 private ISysUserService userService;

	/**
	 * 分页列表查询
	 *
	 * @param operateHistoryInfo
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "操作历史表-分页列表查询")
	@ApiOperation(value="操作历史表-分页列表查询", notes="操作历史表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(OperateHistoryInfo operateHistoryInfo,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		log.info("operateHistoryInfo={}",operateHistoryInfo);
		log.info("com.yuanqiao.insight.accountbook.modules.operatehistory.controller.OperateHistoryInfoController.queryPageList(operateHistoryInfo={})",operateHistoryInfo);
		QueryWrapper<OperateHistoryInfo> queryWrapper = QueryGenerator.initQueryWrapper(operateHistoryInfo, req.getParameterMap());
		Page<OperateHistoryInfo> page = new Page<OperateHistoryInfo>(pageNo, pageSize);
		IPage<OperateHistoryInfo> pageList = operateHistoryInfoService.page(page, queryWrapper);

		if(null != pageList.getRecords() && pageList.getRecords().size()>0){
			//用户名称
			Map<String,String> userMap = userService.selectRealnameByUsername();
			pageList.getRecords().forEach(item ->{
				item.setOperateUserName(userMap.get(item.getOperateUserId()));
			});
		}
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param operateHistoryInfo
	 * @return
	 */
	@AutoLog(value = "操作历史表-添加")
	@ApiOperation(value="操作历史表-添加", notes="操作历史表-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody OperateHistoryInfo operateHistoryInfo) {
		log.info("operateHistoryInfo={}",operateHistoryInfo);
		log.info("com.yuanqiao.insight.accountbook.modules.operatehistory.controller.OperateHistoryInfoController.add(operateHistoryInfo={})",operateHistoryInfo);
		operateHistoryInfoService.save(operateHistoryInfo);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param operateHistoryInfo
	 * @return
	 */
	@AutoLog(value = "操作历史表-编辑")
	@ApiOperation(value="操作历史表-编辑", notes="操作历史表-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody OperateHistoryInfo operateHistoryInfo) {
		log.info("operateHistoryInfo={}",operateHistoryInfo);
		log.info("com.yuanqiao.insight.accountbook.modules.operatehistory.controller.OperateHistoryInfoController.edit(operateHistoryInfo={})",operateHistoryInfo);
		operateHistoryInfoService.updateById(operateHistoryInfo);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "操作历史表-通过id删除")
	@ApiOperation(value="操作历史表-通过id删除", notes="操作历史表-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		log.info("id={}",id);
		log.info("com.yuanqiao.insight.accountbook.modules.operatehistory.controller.OperateHistoryInfoController.delete(id={})",id);
		operateHistoryInfoService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "操作历史表-批量删除")
	@ApiOperation(value="操作历史表-批量删除", notes="操作历史表-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		log.info("ids={}",ids);
		log.info("com.yuanqiao.insight.accountbook.modules.operatehistory.controller.OperateHistoryInfoController.deleteBatch(ids={})",ids);
		this.operateHistoryInfoService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "操作历史表-通过id查询")
	@ApiOperation(value="操作历史表-通过id查询", notes="操作历史表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		log.info("id={}",id);
		log.info("com.yuanqiao.insight.accountbook.modules.operatehistory.controller.OperateHistoryInfoController.queryById(id={})",id);
		OperateHistoryInfo operateHistoryInfo = operateHistoryInfoService.getById(id);
		if(operateHistoryInfo==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(operateHistoryInfo);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param operateHistoryInfo
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, OperateHistoryInfo operateHistoryInfo) {
    	log.info("com.yuanqiao.insight.accountbook.modules.operatehistory.controller.OperateHistoryInfoController.exportXls####");
        return super.exportXls(request, operateHistoryInfo, OperateHistoryInfo.class, "操作历史表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
    	log.info("com.yuanqiao.insight.accountbook.modules.operatehistory.controller.OperateHistoryInfoController.importExcel####");
        return super.importExcel(request, response, OperateHistoryInfo.class);
    }

}
