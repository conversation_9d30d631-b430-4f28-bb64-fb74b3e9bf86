package com.yuanqiao.insight.accountbook.modules.devopsipsegsent.job;

import com.yuanqiao.insight.accountbook.modules.devopsipmanagereal.ipScan.IPScanActuator;
import com.yuanqiao.insight.accountbook.modules.devopsipmanagereal.service.IDevopsIpManageRealService;
import com.yuanqiao.insight.accountbook.modules.devopsipmanagereal.service.impl.DevopsIpManageRealServiceImpl;
import com.yuanqiao.insight.accountbook.modules.devopsipsegsent.entity.DevopsIpSegsent;
import com.yuanqiao.insight.accountbook.modules.devopsipsegsent.service.IDevopsIpSegsentService;
import com.yuanqiao.insight.accountbook.modules.devopsipsegsent.service.impl.DevopsIpSegsentServiceImpl;
import com.yuanqiao.insight.acore.collector.Collector;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.acore.scheduler.SchedulerManagerInter;
import com.yuanqiao.insight.acore.utils.SpringContextUtil;
import org.springframework.context.ApplicationContext;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @title: IPSegsentJob
 * @projectName jeecg-boot-parent
 * @description: TODO
 * @date 2021/5/29-9:25
 */
public class IPSegsentJob implements Collector {

    //任务id
    private String taskId;
    //用户id
    private String userId;
    //创建一个可缓存的线程池。如果线程池的大小超过了处理任务所需要的线程，那么就会回收部分空闲（60秒不执行任务）的线程，
    //线程池
    private ExecutorService pools = Executors.newCachedThreadPool();
    //监控配置
    private IDevopsIpSegsentService devopsIpSegsentService;
    //
    private IDevopsIpManageRealService devopsIpManageRealService;


    public IPSegsentJob(){
        this.devopsIpSegsentService = SpringContextUtil.getApplicationContext().getBean(DevopsIpSegsentServiceImpl.class);
        this.devopsIpManageRealService = SpringContextUtil.getApplicationContext().getBean(DevopsIpManageRealServiceImpl.class);

    }
    @Override
    public void init(Device device, SchedulerManagerInter schedulerManager) {

    }

    @Override
    public void init(Device device, SchedulerManagerInter schedulerManager, ApplicationContext applicationContext) {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void execute() {
       TaskIdRunnable runnable = new TaskIdRunnable();
        runnable.setBoby(this.taskId);
        runnable.setUserId(this.userId);
        pools.execute(runnable);

    }

    //线程执行
    class TaskIdRunnable implements Runnable {
        //备份ID
        private String boby;
        private String userId;
        public void setBoby(String boby) {
            this.boby = boby;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        @Override
        public void run() {
            DevopsIpSegsent devopsIpSegsent = devopsIpSegsentService.getById(boby);
            devopsIpManageRealService.delBySegsentId(boby );
            for (int i = 0; i < 10; i++) {
               new IPScanActuator(null,i,devopsIpSegsent,userId).start();
            }
        }
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
}
