package com.yuanqiao.insight.accountbook.modules.orderinfo.controller;

import com.yuanqiao.insight.accountbook.modules.orderinfo.entity.HandOrderVO;
import com.yuanqiao.insight.accountbook.modules.orderinfo.entity.HandleAverageVO;
import com.yuanqiao.insight.accountbook.modules.orderinfo.entity.TypeCountVo;
import com.yuanqiao.insight.accountbook.modules.orderinfo.mapper.DevopsOrderInfoMapper;
import com.yuanqiao.insight.accountbook.modules.orderinfo.service.IDevopsOrderInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.modules.util.ZipEncryUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: OrderApiController
 * @projectName jeecg-boot-parent
 * @description: TODO
 * @date 2021/6/29-11:01
 */
@Api(tags = "运维工单详情表")
@RestController
//@RequestMapping("/data-analysis/order")
@Slf4j
public class OrderApiController {

    @Autowired
    private IDevopsOrderInfoService devopsOrderInfoService;
    @Autowired
    private DevopsOrderInfoMapper devopsOrderInfoMapper;
    @Autowired
    private ZipEncryUtils zipEncryUtils;

    //@AutoLog(value = "运维工单详情表-工单统计")
    @ApiOperation(value = "运维工单详情表-工单统计", notes = "运维工单详情表-工单统计")
    @GetMapping(value = "/count")
    public Result<?> statistics(String time1, String time2){
        Map<String, List<Map<String,Object>>> map = devopsOrderInfoService.statistics(time1, time2);
        return Result.OK(map);
    }


    //@AutoLog(value = "运维工单详情表-工单处理数量TOP10")
    @ApiOperation(value = "运维工单详情表-工单处理数量TOP10", notes = "运维工单详情表-工单处理数量TOP10")
    @GetMapping(value = "/handle/top")
    public Result<?> handleTop(@RequestParam(name = "time1") String time1,@RequestParam(name = "time2") String time2){
        List<HandOrderVO> mapList = devopsOrderInfoService.handleTop(time1,time2);
        return Result.OK(mapList);
    }

    /**
     * - 返回用户指定时间内的的平均处理时间前10位
     * - 按照平均处理时间升序排列
     * @param time1 开始时间
     * @param time2 结束时间
     * @return
     */
    //@AutoLog(value = "运维工单详情表-平均处理时间TOP10")
    @ApiOperation(value = "运维工单详情表-平均处理时间TOP10", notes = "运维工单详情表-平均处理时间TOP10")
    @GetMapping(value = "/handle/average/top")
    public Result<?> handleAverageTop(@RequestParam(name = "time1") String time1,@RequestParam(name = "time2") String time2){
        List<HandleAverageVO> list = devopsOrderInfoService.handleAverageTop(time1,time2);
        return Result.OK(list);
    }

    /**
     * - 返回指定时间内的故障类型统计, 默认返回所有
     * - 安装工单故障类型统计
     * - 按照数量从大到小排列
     * @param time1 开始时间
     * @param time2 结束时间
     * @return
     */
    //@AutoLog(value = "运维工单详情表-工单类型统计")
    @ApiOperation(value = "运维工单详情表-工单类型统计", notes = "运维工单详情表-工单类型统计")
    @GetMapping(value = "/type/count")
    public Result<?> typeCount( String time1, String time2){
        List<TypeCountVo> typeCountVos = devopsOrderInfoService.typeCount(time1, time2);
        return Result.OK(typeCountVos);
    }

    /**
     * - 返回用户指定时间内的的结果, 默认返回最近30天
     * @param time1  开始时间
     * @param time2  结束时间
     * @return
     */
  //  @AutoLog(value = "运维工单详情表-工单数量统计")
    @ApiOperation(value = "运维工单详情表-工单数量统计", notes = "运维工单详情表-工单数量统计")
    @GetMapping(value = "/day/count")
    public Result<?> dayCount(String time1, String time2){
        List<HandOrderVO> handOrderVOS = devopsOrderInfoService.dayCount(time1, time2);
        return Result.OK(handOrderVOS);
    }

    @AutoLog(value = "工单报表-导出")
    @ApiOperation(value = "工单报表-导出", notes = "工单报表-导出")
    @GetMapping(value = "/export")
    public Result<?> export(String time1, String time2){

        //工单处理数量TOP10
        List<HandOrderVO> mapList = devopsOrderInfoService.handleTop(time1,time2);
        //工单信息统计+工单达标率统计
        Map<String, List<Map<String,Object>>> map = devopsOrderInfoService.statistics(time1, time2);
        //平均处理时间TOP10
        List<HandleAverageVO> list = devopsOrderInfoService.handleAverageTop(time1,time2);
        //工单类型统计
        List<TypeCountVo> typeCountVos = devopsOrderInfoService.typeCount(time1, time2);
        //工单数量统计
        List<HandOrderVO> handOrderVOS = devopsOrderInfoService.dayCount(time1, time2);
        //todo 此处判断是否要加密处理
        String pdfPath = devopsOrderInfoService.exportPdf(mapList,map,list,typeCountVos,handOrderVOS);;
//        if(true){
//            try {
//                pdfPath = zipEncryUtils.pdfEncry(pdfPath,"123456");
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }

        return Result.OK(pdfPath);
    }

}
