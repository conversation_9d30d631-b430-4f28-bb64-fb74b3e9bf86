package com.yuanqiao.insight.accountbook.modules.devopsipmanagereal.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: ip地址详情表
 * @Author: jeecg-boot
 * @Date: 2021-03-22
 * @Version: V1.0
 */
@Data
@TableName("devops_ip_manage_real")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "devops_ip_manage_real对象", description = "ip地址详情表")
public class DevopsIpManageReal implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
    /**
     * ip地址
     */
    @Excel(name = "IP地址", width = 20)
    @ApiModelProperty(value = "ip地址")
    private String ipAddress;
    /**
     * MAC地址
     */
    @Excel(name = "MAC地址", width = 30)
    @ApiModelProperty(value = "MAC地址")
    private String macCode;
    /**
     * 资源名称
     */
    @Excel(name = "设备名称", width = 30)
    @ApiModelProperty(value = "设备名称")
    private String terminalName;
    /**
     * 使用状态(0:已使用,1:未使用)
     */
    @Excel(name = "IP地址状态", width = 15, replace = {"已使用_0", "未使用_1"})
    @ApiModelProperty(value = "使用状态")
    private String ipState;
    @TableField(exist = false)
    private String ipStateText;
    @TableField(exist = false)
    private String occupiedText;
    /**
     * 非法占用  非法占用(0:是,1:非法篡改)
     */
    @Excel(name = "非法占用", width = 15, replace = {"非法占用_0", "非法篡改_1", "未获取到mac_2"})
    @ApiModelProperty(value = "非法占用")
    private String occupied;
    /**
     * 创建人
     */
    @Excel(name = "创建人", width = 15, dicCode = "username", dictTable = "sys_users", dicText = "realname")
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @Excel(name = "创建时间", width = 30, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;

    @ApiModelProperty(value = "监控配置")
    private String segsentId;

    @ApiModelProperty(value = "排序")
    private Integer sort;


    @TableField(exist = false)
    private Integer ipAddressUsage;
    @TableField(exist = false)
    private Integer ipAddressillegal;
    @TableField(exist = false)
    private String createByName;
}
