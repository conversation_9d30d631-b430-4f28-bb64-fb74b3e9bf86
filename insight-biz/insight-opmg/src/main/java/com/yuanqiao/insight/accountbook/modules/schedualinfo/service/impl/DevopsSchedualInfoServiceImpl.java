package com.yuanqiao.insight.accountbook.modules.schedualinfo.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.accountbook.modules.arrangementinfo.service.IDevopsArrangementInfoService;
import com.yuanqiao.insight.accountbook.modules.arrangementshift.service.IDevopsArrangementShiftService;
import com.yuanqiao.insight.accountbook.modules.arrangementuser.service.IDevopsArrangementUserService;
import com.yuanqiao.insight.accountbook.modules.schedualinfo.entity.DevopsSchedualInfo;
import com.yuanqiao.insight.accountbook.modules.schedualinfo.mapper.DevopsSchedualInfoMapper;
import com.yuanqiao.insight.accountbook.modules.schedualinfo.service.IDevopsSchedualInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 班次管理表
 * @Author: jeecg-boot
 * @Date:   2021-03-12
 * @Version: V1.0
 */
@Slf4j
@Service
public class DevopsSchedualInfoServiceImpl extends ServiceImpl<DevopsSchedualInfoMapper, DevopsSchedualInfo> implements IDevopsSchedualInfoService {

    @Autowired
    private DevopsSchedualInfoMapper schedualInfoMapper;

    @Autowired
    private IDevopsArrangementInfoService devopsArrangementInfoService;
    @Autowired
    private IDevopsArrangementUserService devopsArrangementUserService;
    @Autowired
    private IDevopsArrangementShiftService devopsArrangementShiftService;

    @Override
    public Map<String, String> getSchedualMap() {

        List<DevopsSchedualInfo> schedualList = schedualInfoMapper.getSchedualMap();
        log.info("schedualList={}",schedualList);
        log.info("com.yuanqiao.insight.accountbook.modules.schedualinfo.service.impl.DevopsSchedualInfoServiceImpl.getSchedualMap(schedualList={})",schedualList);
        Map<String,String> map = new HashMap<>();
        schedualList.forEach(itme ->{
            map.put(itme.getId(),itme.getName());
        });
        return map;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delByIds(List<String> ids) {
        log.info("ids={}",ids);
        log.info("com.yuanqiao.insight.accountbook.modules.schedualinfo.service.impl.DevopsSchedualInfoServiceImpl.delByIds(ids={})",ids);
        //获取所有的排班
        List<String> arrangementInfoIds = devopsArrangementInfoService.selectBySchedualId(ids);
        //删除值班记录
        devopsArrangementShiftService.delByArrangementInfoIds(arrangementInfoIds);
        //删除排班用户关系
        devopsArrangementUserService.delByArrangementInfoIds(arrangementInfoIds);
        //删除排班
        devopsArrangementInfoService.delBySchedualId(ids);
        this.removeByIds(ids);
    }
}
