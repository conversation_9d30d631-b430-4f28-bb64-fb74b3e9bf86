package com.yuanqiao.insight.accountbook.modules.evaluate.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName EvaluateMetricsRuleConfig
 * @description: 评估指标规则配置表
 * @datetime 2025年 07月 28日
 * @version: 1.0
 */
@Data
@TableName("devops_evaluate_metrics_rule_config")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="devops_evaluate_metrics_rule_config对象", description="评估指标规则配置表")
public class EvaluateMetricsRuleConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "指标ID")
    private String metricsId;

    @ApiModelProperty(value = "评估规则配置JSON")
    private String ruleConfig;

    @ApiModelProperty(value = "评估规则数量")
    private Integer ruleCount;

    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;

    @Dict(dictTable = "sys_users", dicText = "realname", dicCode = "username")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;

    @Dict(dictTable = "sys_users", dicText = "realname", dicCode = "username")
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;

    // 非数据库字段，用于返回解析后的规则列表
    @TableField(exist = false)
    @ApiModelProperty(value = "评估规则列表")
    private List<EvaluationRuleInfo> evaluationRules;
}
