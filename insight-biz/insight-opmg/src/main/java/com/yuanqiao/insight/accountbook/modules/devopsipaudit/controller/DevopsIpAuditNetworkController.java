package com.yuanqiao.insight.accountbook.modules.devopsipaudit.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.accountbook.modules.devopsipplan.entity.DevopsIpPlan;
import com.yuanqiao.insight.accountbook.modules.devopsipplan.service.IDevopsIpPlanService;
import com.yuanqiao.insight.cmdb.modules.category.entity.CmdbAssetsCategory;
import com.yuanqiao.insight.cmdb.modules.category.service.ICmdbAssetsCategoryService;
import com.yuanqiao.insight.service.device.entity.DeviceInfo;
import com.yuanqiao.insight.service.device.mapper.DeviceConnectInfoMapper;
import com.yuanqiao.insight.service.device.mapper.DeviceInfoMapper;
import com.yuanqiao.insight.service.product.entity.Product;
import com.yuanqiao.insight.service.product.service.IProductService;
import com.yuanqiao.insight.monitoring.modules.product.service.IProductTypeService;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @title DevopsIpAuditNetworkController
 * @description
 * @create 2024/3/14 14:13
 */
@RestController
@RequestMapping("/devops/ip/network")
@Slf4j
public class DevopsIpAuditNetworkController {

    @Autowired
    DeviceInfoMapper deviceInfoMapper;
    @Autowired
    DeviceConnectInfoMapper deviceConnectInfoMapper;
    @Autowired
    private IProductTypeService productTypeService;
    @Autowired
    private ICmdbAssetsCategoryService cmdbAssetsCategoryService;
    @Autowired
    private IProductService productService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private IDevopsIpPlanService devopsIpPlanService;

    /**
     * Ip管理中 网络设备分页查询
     *
     * @param dictCode    Network
     * @param status
     * @param alarmStatus
     * @param enable
     * @param name
     * @param ip
     * @param productId
     * @param pageNo
     * @param pageSize
     * @return
     */
    @GetMapping(value = "/getDeviceByCategoryDict")
    public Result<?> getDeviceByCategoryDict(@RequestParam(name = "dictCode", required = true) String dictCode,
                                             Integer status,
                                             Integer alarmStatus,
                                             Integer enable,
                                             String name,
                                             String ip,
                                             String productId,
                                             @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                             @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        Page<DeviceInfo> page = new Page<>(pageNo, pageSize);
        IPage<DeviceInfo> iPage = new Page<>();
        List<DevopsIpPlan> devopsIpPlanList = devopsIpPlanService.list(new QueryWrapper<DevopsIpPlan>().eq("del_flag", 0));
        if (CollectionUtils.isEmpty(devopsIpPlanList)) {
            return Result.OK(iPage);
        }
        List<String> ips = devopsIpPlanList.stream().map(DevopsIpPlan::getIpAddress).collect(Collectors.toList());
        List<Product> productList = productTypeService.selectProductListByTypeCode(dictCode);
        if (CollUtil.isNotEmpty(productList)) {
            List<String> productIdList = productList.stream().map(Product::getId).collect(Collectors.toList());
            iPage = deviceInfoMapper.getDeviceByCategoryIdsAndIps(page, productIdList, status, alarmStatus, enable, name, ip, productId, null, ips);
        }
        if (!CollectionUtils.isEmpty(iPage.getRecords())) {
            for (DeviceInfo deviceInfo : iPage.getRecords()) {
                Product product = productService.getById(deviceInfo.getProductId());
                if (product != null) {
                    CmdbAssetsCategory category = cmdbAssetsCategoryService.getById(product.getAssetsCategoryId());
                    if (category != null) {
                        deviceInfo.setCategoryName(category.getCategoryName());
                    }
                }
                String key = "stca:" + deviceInfo.getDeviceCode() + "_cpuRate";
                if (redisTemplate.hasKey(key)) {
                    JSONObject dataObject = JSONObject.parseObject(redisTemplate.boundValueOps(key).get() + "");
                    if (dataObject.getString("value") != null) {
                        String cpu = dataObject.getString("value");
                        deviceInfo.setCpuRate(cpu);
                    }
                }
                String memKey = "stca:" + deviceInfo.getDeviceCode() + "_memRate";
                if (redisTemplate.hasKey(memKey)) {
                    JSONObject dataObject = JSONObject.parseObject(redisTemplate.boundValueOps(memKey).get() + "");
                    if (dataObject.getString("value") != null) {
                        String mem = dataObject.getString("value");
                        deviceInfo.setMemRate(mem);
                    }
                }
            }
        }
        return Result.OK(iPage);
    }
}
