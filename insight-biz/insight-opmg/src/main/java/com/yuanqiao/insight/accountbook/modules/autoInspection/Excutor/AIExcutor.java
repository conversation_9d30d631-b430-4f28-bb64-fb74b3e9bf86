package com.yuanqiao.insight.accountbook.modules.autoInspection.Excutor;

import com.yuanqiao.insight.accountbook.modules.autoInspection.entity.DevopsAutoInspection;

/**
 * <AUTHOR>
 * @title: AIExcutor              任务执行器定义接口类
 * @projectName jeecg-boot-parent
 * @description: TODO
 * @date 2021/5/15-11:29
 */
public interface AIExcutor {
    /**
     * 任务执行器接口的执行方法
     * @param autoInspection 自动巡检任务类对象
     */
    void excute(DevopsAutoInspection autoInspection,String aIfilePath,String path);
}
