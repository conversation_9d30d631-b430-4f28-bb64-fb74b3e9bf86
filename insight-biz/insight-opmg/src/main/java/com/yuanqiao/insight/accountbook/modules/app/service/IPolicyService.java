package com.yuanqiao.insight.accountbook.modules.app.service;

import com.github.yulichang.base.MPJBaseService;
import com.yuanqiao.insight.accountbook.modules.app.entity.Policy;

import java.util.List;

/**
 * @Description: 软件策略
 * @Author: jeecg-boot
 * @Date:   2024-11-14
 * @Version: V1.0
 */
public interface IPolicyService extends MPJBaseService<Policy> {

    void savePolicy(Policy policy);

    void updatePolicy(Policy policy);

    void removePolicys(List<String> list);
}
