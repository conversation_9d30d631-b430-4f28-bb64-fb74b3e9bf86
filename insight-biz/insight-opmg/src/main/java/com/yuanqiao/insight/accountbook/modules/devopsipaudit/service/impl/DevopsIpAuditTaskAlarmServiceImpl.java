package com.yuanqiao.insight.accountbook.modules.devopsipaudit.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.accountbook.modules.devopsipaudit.entity.DevopsIpAuditTaskAlarm;
import com.yuanqiao.insight.accountbook.modules.devopsipaudit.mapper.DevopsIpAuditTaskAlarmMapper;
import com.yuanqiao.insight.accountbook.modules.devopsipaudit.service.IDevopsIpAuditTaskAlarmService;
import org.springframework.stereotype.Service;

/**
 * @Description: 审计任务告警表
 * @Author: jeecg-boot
 * @Date: 2024-03-01
 * @Version: V1.0
 */
@Service
public class DevopsIpAuditTaskAlarmServiceImpl extends ServiceImpl<DevopsIpAuditTaskAlarmMapper, DevopsIpAuditTaskAlarm> implements IDevopsIpAuditTaskAlarmService {

}
