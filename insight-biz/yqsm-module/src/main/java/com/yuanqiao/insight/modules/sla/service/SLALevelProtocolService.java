package com.yuanqiao.insight.modules.sla.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuanqiao.insight.modules.sla.eneity.SLALevelProtocol;

import java.util.Date;


public interface SLALevelProtocolService  extends IService<SLALevelProtocol> {
    SLALevelProtocol getOneBySlaTypeAndProcessKey(String slaType, String processKey);

    Date getDateByLevel(String level, Date taskCreateTime);
}
