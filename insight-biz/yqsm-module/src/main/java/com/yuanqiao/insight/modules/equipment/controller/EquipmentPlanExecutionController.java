package com.yuanqiao.insight.modules.equipment.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.modules.equipment.entity.EquipmentPlanExecution;
import com.yuanqiao.insight.modules.equipment.service.IEquipmentPlanExecutionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

/**
 * @Description: 设备执行表
 * @author: yqkj
 * @Date: 2022-07-25
 * @Version: V1.0
 */
@Api(tags = "设备执行表")
@RestController
@RequestMapping("/equipment/equipmentPlanExecution")
@Slf4j
public class EquipmentPlanExecutionController extends JeecgController<EquipmentPlanExecution, IEquipmentPlanExecutionService> {
	@Autowired
	private IEquipmentPlanExecutionService equipmentPlanExecutionService;

	/**
	 * 分页列表查询
	 *
	 * @param equipmentPlanExecution
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "设备执行表-分页列表查询")
	@ApiOperation(value = "设备执行表-分页列表查询", notes = "设备执行表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(EquipmentPlanExecution equipmentPlanExecution,
															   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
															   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
															   HttpServletRequest req) {
        // todo-wc 魏晨 list查询过滤掉大字段值，到达具体页面单独查询
		QueryWrapper<EquipmentPlanExecution> queryWrapper = QueryGenerator.initQueryWrapper(equipmentPlanExecution, req.getParameterMap());
		Page<EquipmentPlanExecution> page = new Page<EquipmentPlanExecution>(pageNo, pageSize);
		IPage<EquipmentPlanExecution> pageList = equipmentPlanExecutionService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 * 添加
	 *
	 * @param equipmentPlanExecution
	 * @return
	 */
	@AutoLog(value = "设备执行表-添加")
	@ApiOperation(value = "设备执行表-添加", notes = "设备执行表-添加")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody EquipmentPlanExecution equipmentPlanExecution) {
		equipmentPlanExecutionService.save(equipmentPlanExecution);
		return Result.OK("添加成功！");
	}

	/**
	 * 编辑
	 *
	 * @param equipmentPlanExecution
	 * @return
	 */
	@AutoLog(value = "设备执行表-编辑")
	@ApiOperation(value = "设备执行表-编辑", notes = "设备执行表-编辑")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
	public Result<String> edit(@RequestBody EquipmentPlanExecution equipmentPlanExecution) {
		equipmentPlanExecutionService.updateById(equipmentPlanExecution);
		return Result.OK("编辑成功!");
	}

	/**
	 * 通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "设备执行表-通过id删除")
	@ApiOperation(value = "设备执行表-通过id删除", notes = "设备执行表-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
		equipmentPlanExecutionService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 * 批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "设备执行表-批量删除")
	@ApiOperation(value = "设备执行表-批量删除", notes = "设备执行表-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
		this.equipmentPlanExecutionService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "设备执行表-通过id查询")
	@ApiOperation(value = "设备执行表-通过id查询", notes = "设备执行表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
		EquipmentPlanExecution equipmentPlanExecution = equipmentPlanExecutionService.getById(id);
		if (equipmentPlanExecution == null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(equipmentPlanExecution);
	}

	/**
	 * 导出excel
	 *
	 * @param request
	 * @param equipmentPlanExecution
	 */
	@RequestMapping(value = "/exportXls")
	public ModelAndView exportXls(HttpServletRequest request, EquipmentPlanExecution equipmentPlanExecution) {
		return super.exportXls(request, equipmentPlanExecution, EquipmentPlanExecution.class, "设备执行表");
	}

	/**
	 * 通过excel导入数据
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/importExcel", method = RequestMethod.POST)
	public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
		return super.importExcel(request, response, EquipmentPlanExecution.class);
	}

}
