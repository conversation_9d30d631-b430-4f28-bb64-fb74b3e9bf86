package com.yuanqiao.insight.modules.equipment.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecg.common.aspect.annotation.Dict;

import java.io.Serializable;
import java.util.Date;

@Data
public class EquipmentPlanExecutionUnion implements Serializable {
    protected String id;
    protected String processInstanceName;
    protected String processInstanceId;
    protected String yqBusinessTitle;
    protected String taskName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    protected Date endTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    protected Date createTime;
    protected String ownerName;
    protected String assigneeName;

    @Dict(dictTable = "yq_pro_equipment", dicText = "name", dicCode = "id")
    protected String equipmentId;
    protected String equipmentName;

    private String taskCreatedAfter;
    private String taskCreatedBefore;
    private String taskCompletedAfter;
    private String taskCompletedBefore;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    protected Date dueDate;
    private String taskDueAfter;
    private String taskDueBefore;

    @Dict(dictTable = "sys_users", dicText = "realname", dicCode = "username")
    private String taskAssignee;

    @Dict(dictTable = "sys_users", dicText = "realname", dicCode = "username")
    private String taskOwner;

    private String processDefinitionKey;

    protected String durationTime;

    protected String workTime;

    @Dict(dicCode = "equipment_plan_execution_category")
    protected String category;
}
