package com.yuanqiao.insight.modules.language.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanqiao.insight.modules.language.entity.LanguageManage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface LanguageManageMapper extends BaseMapper<LanguageManage> {



    void updateLanguageInfo(@Param(value = "languageInfo") String languageInfo,
                            @Param(value = "orderBy") Integer orderBy,
                            @Param(value = "id") String id);



    List<LanguageManage> queryStatusInfo(@Param(value = "status") Integer status);


    List<LanguageManage> queryLanguageInfo(@Param(value = "languageInfo") String languageInfo);


    List<LanguageManage> queryAllInfo(@Param(value = "languageInfo") String languageInfo,
                                      @Param(value = "status") Integer status);

}
