package com.yuanqiao.insight.modules.schedule.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yuanqiao.insight.common.core.util.ObjectUtils;
import com.yuanqiao.insight.modules.schedule.entity.ShiftUserRecord;
import com.yuanqiao.insight.modules.schedule.service.ShiftUserRecordService;
import com.yuanqiao.insight.modules.schedule.vo.ShiftVo;
import io.swagger.annotations.Api;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

/**
 * 值班人员信息记录表
 */
@RestController
@RequestMapping("/shiftUser/record")
@Api(tags = "值班人员信息记录")
public class ShiftUserRecordController extends JeecgController<ShiftUserRecord, ShiftUserRecordService> {

    @Autowired
    protected ISysBaseAPI iSysBaseAPI;

    /**
     * 查询值班排班记录信息
     *
     * @param userId
     * @param shiftId
     * @return
     */
    @GetMapping(value = "/queryUserRecords")
    public Result<List<ShiftUserRecord>> queryUserRecords(@RequestParam(name = "userId", required = false) String userId, @RequestParam(name = "shiftId", required = false) String shiftId) {

        QueryWrapper<ShiftUserRecord> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotEmpty(userId)) {
            queryWrapper.eq("user_id", userId);
        }
        if (StringUtils.isNotEmpty(shiftId)) {
            queryWrapper.eq("shift_id", shiftId);
        }
        queryWrapper.orderByAsc("duty_date");
        queryWrapper.orderByAsc("start_time");
        List<ShiftUserRecord> list = service.list(queryWrapper);
        List<ShiftUserRecord> lists=new ArrayList<>();
        for (ShiftUserRecord shiftUserRecord : list) {
            LoginUser user = iSysBaseAPI.getUserByName(shiftUserRecord.getUserId());
            if (user!=null){
                shiftUserRecord.setUserName(user.getRealname());
                lists.add(shiftUserRecord);
            }
        }
        return Result.OK(lists);
    }

    /**
     * 多条件删除
     *
     * @param shiftVo
     * @return
     */
    @GetMapping(value = "/deleteRecords")
    public Result deleteRecords(ShiftVo shiftVo)  {
        service.deleteRecords(shiftVo);
        return Result.OK();
    }

    /**
     * 根据ID删除
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/deleteById")
    public Result deleteById(String id) {
        service.removeById(id);
        return Result.OK();
    }

    /**
     * 单个添加
     *
     * @param shiftVo
     * @return
     */
    @GetMapping(value = "/addRecords")
    public Result addRecords(ShiftVo shiftVo) throws ParseException {
        LambdaQueryWrapper<ShiftUserRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ShiftUserRecord::getDutyDate,shiftVo.getStartDate());
        wrapper.eq(ShiftUserRecord::getShiftId,shiftVo.getId());
        List<ShiftUserRecord> shiftUserRecords1 = service.list(wrapper);
        if (shiftUserRecords1.size()>=1){
            return Result.error("当天已安排值班人员");
        }
        service.addRecords(shiftVo);
        return Result.OK();
    }

    /**
     * 修改排班记录
     *
     * @param shiftVo
     * @return
     * @throws ParseException
     */
    @GetMapping(value = "/updateRecords")
    public Result updateRecords(ShiftVo shiftVo) throws ParseException {
        ShiftUserRecord shiftUserRecord = service.getById(shiftVo.getRecordId());
        if (ObjectUtils.isEmpty(shiftUserRecord)) {
            return Result.error("不存在该班次信息");
        }
        service.removeById(shiftVo.getRecordId());
        service.addRecords(shiftVo);
        return Result.OK();
    }

}
