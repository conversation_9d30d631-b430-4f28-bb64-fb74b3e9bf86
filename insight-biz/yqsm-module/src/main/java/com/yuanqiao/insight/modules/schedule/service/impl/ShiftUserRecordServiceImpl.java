package com.yuanqiao.insight.modules.schedule.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.modules.schedule.entity.DutyShift;
import com.yuanqiao.insight.modules.schedule.entity.ShiftUserRecord;
import com.yuanqiao.insight.modules.schedule.mapper.DutyShiftMapper;
import com.yuanqiao.insight.modules.schedule.mapper.ShiftUserRecordMapper;
import com.yuanqiao.insight.modules.schedule.service.ShiftUserRecordService;
import com.yuanqiao.insight.modules.schedule.vo.ShiftVo;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ShiftUserRecordServiceImpl extends ServiceImpl<ShiftUserRecordMapper, ShiftUserRecord> implements ShiftUserRecordService {

    @Autowired
    DutyShiftMapper dutyShiftMapper;

    /**
     * 删除大于开始时间小于结束时间的排班记录信息
     *
     * @param shiftId
     * @param startDate
     * @param endDate
     */
    @Override
    public void deleteRecordByDateAndShiftId(String shiftId, Date startDate, Date endDate) {
        //删除当前班次大于等于当前时间的信息
        QueryWrapper<ShiftUserRecord> shiftUserRecordWrapper = new QueryWrapper<>();
        shiftUserRecordWrapper.eq("shift_id", shiftId);
        if (startDate != null) {
            shiftUserRecordWrapper.ge("duty_date", startDate);
        }
        if (endDate != null) {
            shiftUserRecordWrapper.le("duty_date", endDate);
        }

        baseMapper.delete(shiftUserRecordWrapper);
        List<ShiftUserRecord> list = baseMapper.selectList(shiftUserRecordWrapper);
    }

    /**
     * 统计当前人员的值班天数
     *
     * @param userId
     * @param shiftId
     * @param startDate
     * @return
     */
    @Override
    public Integer censusUserSumByUserIdAndShiftId(String userId, String shiftId, Date startDate) {
        QueryWrapper<ShiftUserRecord> shiftUserRecordWrapper = new QueryWrapper<>();
        shiftUserRecordWrapper.eq("user_id", userId);
        shiftUserRecordWrapper.eq("shift_id", shiftId);
        shiftUserRecordWrapper.le("duty_date", startDate);
        return baseMapper.selectCount(shiftUserRecordWrapper);
    }

    /**
     * 删除指定排班信息记录
     *
     * @param shiftVo
     */
    @Override
    public void deleteRecords(ShiftVo shiftVo) {
        QueryWrapper<ShiftUserRecord> shiftUserRecordWrapper = new QueryWrapper<>();
        if (StringUtils.isNotEmpty(shiftVo.getStartDate())) {
            try {
                shiftUserRecordWrapper.ge("duty_date", DateUtils.date_sdf.get().parse(shiftVo.getStartDate()));
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        if (StringUtils.isNotEmpty(shiftVo.getEndDate())) {
            try {
                shiftUserRecordWrapper.le("duty_date", DateUtils.date_sdf.get().parse(shiftVo.getEndDate()));
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        if (StringUtils.isNotEmpty(shiftVo.getId())) {
            String[] ids = shiftVo.getId().split(",");
            shiftUserRecordWrapper.in("shift_id", ids);
        }
        if (StringUtils.isNotEmpty(shiftVo.getUserId())) {
            String[] userIds = shiftVo.getUserId().split(",");
            shiftUserRecordWrapper.in("user_id", userIds);
        }
        baseMapper.delete(shiftUserRecordWrapper);
    }

    /**
     * 单个添加
     *
     * @param shiftVo
     */
    @Override
    public void addRecords(ShiftVo shiftVo) throws ParseException {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //获取班次信息
        DutyShift dutyShift = dutyShiftMapper.selectById(shiftVo.getId());
        String[] userIds = shiftVo.getUserId().split(",");
        Date parse = null;
        try {
            parse = DateUtils.datetimeFormat.get().parse(shiftVo.getStartDate());
        } catch (ParseException  e) {
            parse = DateUtils.datetimeFormat.get().parse(shiftVo.getStartDate()+" 00:00:00");
        }
        String s = DateUtils.formatDate(parse) + " " + dutyShift.getStartTime() + ":00";
        Date nowDate = DateUtils.datetimeFormat.get().parse(s);
        List<ShiftUserRecord> shiftUserRecords = new ArrayList<>();
        for (String userId : userIds) {
            ShiftUserRecord shiftUserRecord = new ShiftUserRecord();
            shiftUserRecord.setShiftId(shiftVo.getId());
            if (StringUtils.isEmpty(dutyShift.getRemind()) || dutyShift.getRemind().equals(CacheConstant.SCHEDULE_ALERT_NONE)) {
                shiftUserRecord.setCron("");
            } else {
                Date cronDate = DateUtils.getDateByRemind(nowDate, dutyShift.getRemind());
                shiftUserRecord.setCron(DateUtils.getCron(cronDate, new SimpleDateFormat("ss mm HH dd MM ?")));
            }
            shiftUserRecord.setUserId(userId);
            shiftUserRecord.setColor(dutyShift.getColor());
            shiftUserRecord.setShiftName(dutyShift.getShiftName());
            shiftUserRecord.setStartTime(dutyShift.getStartTime());
            shiftUserRecord.setEndTime(dutyShift.getEndTime());
            shiftUserRecord.setDutyDate(parse);
            shiftUserRecord.setId(UUID.randomUUID().toString());
            shiftUserRecord.setCreateBy(sysUser.getId());
            shiftUserRecord.setCreateTime(new Date());
            shiftUserRecords.add(shiftUserRecord);
        }
        baseMapper.insertRecords(shiftUserRecords);
    }

    /**
     * 获取当前时间以后的数据
     *
     * @return
     */
    @Override
    public List<ShiftUserRecord> selectByNow() {
        QueryWrapper<ShiftUserRecord> shiftUserRecordWrapper = new QueryWrapper<>();
        String format = DateUtils.date_sdf.get().format(new Date());
        shiftUserRecordWrapper.ge("duty_date", DateUtils.str2Date(format, DateUtils.date_sdf.get()));
        return baseMapper.selectList(shiftUserRecordWrapper);
    }

    /**
     * 通过时间和人员id查询值班人员记录表
     *
     * @param userId
     * @param dutyDate
     * @return
     */
    @Override
    public List<ShiftUserRecord> selectByUserAndDutyDate(String userId, String dutyDate) {

        return baseMapper.selectByUserAndDutyDate(userId, dutyDate);
    }

    /**
     * 查询当前值班人员
     *
     * @return
     */
    @Override
    public List<String> selectUsersByNowDutyDate() {
        QueryWrapper<ShiftUserRecord> shiftUserRecordWrapper = new QueryWrapper<>();
        LocalDateTime todayStart = LocalDateTime.now().with(LocalTime.MIN);
        LocalDateTime todayEnd = LocalDateTime.now().with(LocalTime.MAX);
        shiftUserRecordWrapper.between("duty_date", todayStart, todayEnd);
        List<ShiftUserRecord> shiftUserRecords = baseMapper.selectList(shiftUserRecordWrapper);
        if (CollectionUtils.isNotEmpty(shiftUserRecords)) {
            return shiftUserRecords.stream().map(ShiftUserRecord::getUserId).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }
}
