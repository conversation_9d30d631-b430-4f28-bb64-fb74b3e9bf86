package com.yuanqiao.insight.modules.userPageInfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.modules.userPageInfo.eneity.UserPageInfo;
import com.yuanqiao.insight.modules.userPageInfo.mapper.UserPageInfoMapper;
import com.yuanqiao.insight.modules.userPageInfo.service.UserPageInfoService;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class UserPageInfoServiceImpl extends ServiceImpl<UserPageInfoMapper, UserPageInfo> implements UserPageInfoService {

    /**
     * 用户页面配置添加
     *
     * @param userPageInfo
     */
    @Override
    @Transactional
    public void add(UserPageInfo userPageInfo) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        LambdaQueryWrapper<UserPageInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserPageInfo::getUserId, sysUser.getUsername());
        wrapper.eq(UserPageInfo::getPageType, userPageInfo.getPageType());
        UserPageInfo userPageInfo1 = getOne(wrapper);
        if (userPageInfo1!=null){
            removeById(userPageInfo1.getId());
        }
        userPageInfo.setUserId(sysUser.getUsername());
        save(userPageInfo);
    }
}
