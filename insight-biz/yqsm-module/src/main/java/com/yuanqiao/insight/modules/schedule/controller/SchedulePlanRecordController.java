package com.yuanqiao.insight.modules.schedule.controller;

import com.yuanqiao.insight.modules.schedule.entity.SchedulePlan;
import com.yuanqiao.insight.modules.schedule.entity.SchedulePlanRecord;
import com.yuanqiao.insight.modules.schedule.service.SchedulePlanRecordService;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/schedulePlan/record")
public class SchedulePlanRecordController extends JeecgController<SchedulePlanRecord, SchedulePlanRecordService> {


    /**
     * 添加
     *
     * @param schedulePlan
     * @return
     */
    @AutoLog(value = "值班日程安排-添加")
    @ApiOperation(value = "值班日程安排-添加", notes = "值班日程安排-添加")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody SchedulePlan schedulePlan) {
        service.add(schedulePlan);
        return Result.OK();
    }

    /**
     * 查询本人是否完成此纪录
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/checkCompletion")
    public Result checkCompletion(@RequestParam(name = "id", required = true) String id) {
        boolean check = service.checkCompletion(id);
        return Result.OK(check);
    }


}
