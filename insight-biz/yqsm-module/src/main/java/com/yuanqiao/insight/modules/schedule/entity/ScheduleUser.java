package com.yuanqiao.insight.modules.schedule.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Description: 日程人员关联表
 * @Author: zhang-bin
 * @Date:   2022-07-27
 * @Version: V1.0
 */
@Data
@TableName("yq_biz_schedule_user")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="schedule_user对象", description="日程人员关联表")
public class ScheduleUser implements Serializable {

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
     private String userId;

    /**
     * 日程ID
     */
    @ApiModelProperty(value = "日程ID")
     private String scheduleId;
}
