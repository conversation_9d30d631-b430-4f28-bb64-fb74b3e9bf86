package com.yuanqiao.insight.modules.knowledge.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.modules.knowledge.entity.KnowledgeLog;
import com.yuanqiao.insight.modules.knowledge.mapper.KnowledgeLogMapper;
import com.yuanqiao.insight.modules.knowledge.service.IKnowledgeLogService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Description: yq_op_knowledge_log
 * @author: yqkj
 * @Date: 2023-06-08
 * @Version: V1.0
 */
@Service
@Transactional
public class KnowledgeLogServiceImpl extends ServiceImpl<KnowledgeLogMapper, KnowledgeLog> implements IKnowledgeLogService {

    /**
     * 添加记录
     *
     * @param knowledgeId
     * @param operationType
     * @param description
     * @return
     */
    @Override
    public boolean insertKnowledgeLog(String knowledgeId, String operationType, String description) {
        KnowledgeLog knowledgeLog = new KnowledgeLog();
        knowledgeLog.setKnowledgeId(knowledgeId);
        knowledgeLog.setDescription(description);
        knowledgeLog.setOperationType(operationType);
        return save(knowledgeLog);
    }
}
