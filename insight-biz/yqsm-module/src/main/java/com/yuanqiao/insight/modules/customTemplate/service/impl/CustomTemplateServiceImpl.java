package com.yuanqiao.insight.modules.customTemplate.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.modules.customTemplate.eneity.CustomTemplate;
import com.yuanqiao.insight.modules.customTemplate.mapper.CustomTemplateMapper;
import com.yuanqiao.insight.modules.customTemplate.service.CustomTemplateService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class CustomTemplateServiceImpl extends ServiceImpl<CustomTemplateMapper, CustomTemplate> implements CustomTemplateService {

    /**
     * 修改自定义页面状态
     *
     * @param customTemplate
     * @param showType
     */
    @Override
    @Transactional
    public void updateByShowType(CustomTemplate customTemplate, String showType) {
        //将默认展示为0的改为1
        LambdaUpdateWrapper<CustomTemplate> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(CustomTemplate::getShowType,"1");
        wrapper.eq(CustomTemplate::getShowType,"0");
        update(wrapper);
        customTemplate.setShowType(showType);
        updateById(customTemplate);
    }

    @Override
    public List<CustomTemplate> selectCustomTemplates(String showType) {
        List<CustomTemplate> customTemplates = baseMapper.selectCustomTemplates(showType);
        return customTemplates;
    }
}
