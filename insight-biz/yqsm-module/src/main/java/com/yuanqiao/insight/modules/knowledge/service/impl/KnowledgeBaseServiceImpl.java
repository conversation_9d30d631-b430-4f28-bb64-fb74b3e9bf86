package com.yuanqiao.insight.modules.knowledge.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.modules.knowledge.entity.KnowledgeBase;
import com.yuanqiao.insight.modules.knowledge.mapper.KnowledgeBaseMapper;
import com.yuanqiao.insight.modules.knowledge.service.IKnowledgeBaseService;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.SysCategoryModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 知识库
 * @author: yqkj
 * @Date: 2022-08-11
 * @Version: V1.0
 */
@Service
public class KnowledgeBaseServiceImpl extends ServiceImpl<KnowledgeBaseMapper, KnowledgeBase> implements IKnowledgeBaseService {

    @Resource
    KnowledgeBaseMapper knowledgeBaseMapper;

    @Autowired
    protected ISysBaseAPI iSysBaseAPI;

    /**
     * 通过服务请求编号查询知识库
     * @param serviceRequestCode
     * @return
     */
    @Override
    public KnowledgeBase getByServiceRequestCode(String serviceRequestCode) {
        return knowledgeBaseMapper.selectByServiceRequestCode(serviceRequestCode);
    }

    /**
     * 通过知识库分类查询所有知识库
     * @param id
     * @return
     */
    @Override
    public List<KnowledgeBase> selectAllByCategory(String id) {
        return knowledgeBaseMapper.selectAllByCategory(id);
    }

    /**
     * 通过知识库分类查询知识库并分页
     * @param pageNo
     * @param pageSize
     * @param categoryPid
     * @param knowledgeBase
     * @return
     */
    @Override
    public List<KnowledgeBase> selectByCategory(Integer pageNo, Integer pageSize, String categoryPid, KnowledgeBase knowledgeBase) {
        SysCategoryModel sysCategoryModel = iSysBaseAPI.getCategoryDictById(categoryPid);

        //该分类下的所有子分类
        List<String> categoryIds = iSysBaseAPI.querySysCategoryByCode(sysCategoryModel.getCode());

        //所有的子分类查询
        Integer current = (pageNo - 1) * pageSize;
        if (categoryIds != null && categoryIds.size() > 0) {
            List<KnowledgeBase> knowledgeBaseList = knowledgeBaseMapper.queryByCondition(current, pageSize, categoryIds, knowledgeBase);
            for (KnowledgeBase base : knowledgeBaseList) {
                String categoryId = base.getCategory();
                SysCategoryModel model = iSysBaseAPI.getCategoryDictById(categoryId);
                if (model != null){
                    base.setCategoryName(model.getName());
                }
            }
            return knowledgeBaseList;
        } else {
            return new ArrayList<>();
        }
    }

    /**
     * 根据分类获取知识库数量
     * @param categoryPid
     * @param knowledgeBase
     * @return
     */
    @Override
    public long selectCountByCategory(String categoryPid, KnowledgeBase knowledgeBase) {
        SysCategoryModel sysCategoryModel = iSysBaseAPI.getCategoryDictById(categoryPid);

        List<String> categoryIds = iSysBaseAPI.querySysCategoryByCode(sysCategoryModel.getCode());
        if (categoryIds != null && categoryIds.size() > 0) {
            return knowledgeBaseMapper.queryCountByCondition(categoryIds, knowledgeBase);
        } else {
            return 0;
        }

    }

    /**
     * 知识库-导出根据分类id查询
     * @param knowledgeBase
     * @param categoryPid
     * @return
     */
    @Override
    public List<KnowledgeBase> selectAllByCategoryAndCondition(KnowledgeBase knowledgeBase, String categoryPid) {
        SysCategoryModel sysCategoryModel = iSysBaseAPI.getCategoryDictById(categoryPid);

        List<String> categoryIds = iSysBaseAPI.querySysCategoryByCode(sysCategoryModel.getCode());

        if (categoryIds != null && categoryIds.size() > 0) {
            return knowledgeBaseMapper.queryByCategoryAndCondition(categoryIds, knowledgeBase);
        }
        return null;
    }
}
