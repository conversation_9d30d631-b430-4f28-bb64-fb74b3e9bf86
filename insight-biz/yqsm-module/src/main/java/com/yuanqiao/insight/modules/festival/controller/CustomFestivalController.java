package com.yuanqiao.insight.modules.festival.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.modules.festival.entity.CustomFestival;
import com.yuanqiao.insight.modules.festival.service.CustomFestivalService;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.ImportExcelUtil;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 自定义节假日
 */
@RestController
@RequestMapping("/festival/custom")
public class CustomFestivalController extends JeecgController<CustomFestival, CustomFestivalService> {


    /**
     * 分页列表查询
     *
     * @param customFestival
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @ApiOperation(value = "自定义节假日-分页列表查询", notes = "自定义节假日-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(CustomFestival customFestival,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        service.judgeHoliday();
        QueryWrapper<CustomFestival> queryWrapper = QueryGenerator.initQueryWrapper(customFestival, req.getParameterMap());
        if (customFestival.getHoliday() != null) {
            queryWrapper.eq("holiday", customFestival.getHoliday());
        }
        if (customFestival.getHolidayType() != null) {
            queryWrapper.eq("holiday_type", customFestival.getHolidayType());
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        if (StringUtils.isNotEmpty(customFestival.getStartTime())) {
            try {
                queryWrapper.ge("holiday_time", sdf.parse(customFestival.getStartTime()));
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        if (StringUtils.isNotEmpty(customFestival.getEndTime())) {
            try {
                queryWrapper.le("holiday_time", sdf.parse(customFestival.getEndTime()));
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        Page<CustomFestival> page = new Page<>(pageNo, pageSize);
        IPage<CustomFestival> pageList = service.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param CustomFestival
     * @return
     */
    @AutoLog(value = "自定义节假日-添加")
    @ApiOperation(value = "自定义节假日-添加", notes = "自定义节假日-添加")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody CustomFestival CustomFestival) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date parse = sdf.parse(CustomFestival.getDate());
            CustomFestival.setHolidayTime(parse);
            Calendar c = Calendar.getInstance();
            c.setTime(parse);
            String year = String.valueOf(c.get(Calendar.YEAR));
            CustomFestival.setYear(year);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        service.save(CustomFestival);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param CustomFestival
     * @return
     */
    @AutoLog(value = "自定义节假日-编辑")
    @ApiOperation(value = "自定义节假日-编辑", notes = "自定义节假日-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody CustomFestival CustomFestival) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date parse = sdf.parse(CustomFestival.getDate());
            CustomFestival.setHolidayTime(parse);
            Calendar c = Calendar.getInstance();
            c.setTime(parse);
            String year = String.valueOf(c.get(Calendar.YEAR));
            CustomFestival.setYear(year);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        service.updateById(CustomFestival);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "自定义节假日-通过id删除")
    @ApiOperation(value = "自定义节假日-通过id删除", notes = "自定义节假日-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        service.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "自定义节假日-通过id查询", notes = "自定义节假日-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        CustomFestival CustomFestival = service.getById(id);
        if (CustomFestival == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(CustomFestival);
    }

    /**
     * 判断当前时间是否为休息日
     *
     * @return
     */
    @AutoLog(value = "判断当前时间是否为休息日")
    @ApiOperation(value = "判断当前时间是否为休息日", notes = "判断当前时间是否为休息日")
    @GetMapping(value = "/judgeNowFestival")
    public Result judgeNowFestival() {
        boolean b = service.judgeNowFestival();
        return Result.OK(b);
    }

    /**
     * 查询节假日
     *
     * @return
     */
    @GetMapping(value = "/queryFestival")
    public Result queryFestival() {
        List<CustomFestival> list = service.getAllFestival();
        return Result.OK(list);
    }

    /**
     * 导出excel
     *
     * @param req
     * @param response
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(CustomFestival customFestival, HttpServletRequest req, HttpServletResponse response) {
        QueryWrapper<CustomFestival> queryWrapper = QueryGenerator.initQueryWrapper(customFestival, req.getParameterMap());
        if (customFestival.getHoliday() != null) {
            queryWrapper.eq("holiday", customFestival.getHoliday());
        }
        if (customFestival.getHolidayType() != null) {
            queryWrapper.eq("holiday_type", customFestival.getHolidayType());
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        if (StringUtils.isNotEmpty(customFestival.getStartTime())) {
            try {
                queryWrapper.ge("holiday_time", sdf.parse(customFestival.getStartTime()));
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        if (StringUtils.isNotEmpty(customFestival.getEndTime())) {
            try {
                queryWrapper.le("holiday_time", sdf.parse(customFestival.getEndTime()));
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        List<CustomFestival> list = service.list(queryWrapper);
        //导出文件名称
        mv.addObject(NormalExcelConstants.FILE_NAME, "节假日列表");
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("节假日列表数据", "导出信息"));
        mv.addObject(NormalExcelConstants.CLASS, CustomFestival.class);
        mv.addObject(NormalExcelConstants.DATA_LIST, list);
        return mv;
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) throws IOException {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        // 错误信息
        List<String> errorMessage = new ArrayList<>();
        int successLines = 0, errorLines = 0;
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            // 获取上传文件对象
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setTitleRows(1);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<Object> listSysPositions = ExcelImportUtil.importExcel(file.getInputStream(), CustomFestival.class, params);
                listSysPositions.forEach(o -> {
                    CustomFestival customFestival = (CustomFestival) o;
                    try {
                        customFestival.setHolidayTime(new SimpleDateFormat("yyyy-MM-dd").parse(customFestival.getDate()));
                        customFestival.setYear(new SimpleDateFormat("yyyy").format(customFestival.getHolidayTime()));
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                });
                List<String> list = ImportExcelUtil.importDateSave(listSysPositions, CustomFestivalService.class, errorMessage, CommonConstant.SQL_INDEX_UNIQ_CODE);
                errorLines += list.size();
                successLines += (listSysPositions.size() - errorLines);
            } catch (Exception e) {
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return ImportExcelUtil.imporReturnRes(errorLines, successLines, errorMessage);
    }
    /**
     * 通过json导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @PostMapping("/importJson")
    public Result<?> importJson(HttpServletRequest request, HttpServletResponse response){
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        MultipartFile file = multipartRequest.getFile("file");

        if (file == null || file.isEmpty()) {
            return Result.error("上传的文件不能为空");
        }

        // 验证文件类型是否为 JSON
        String contentType = file.getContentType();
        if (!"application/json".equals(contentType)) {
            return Result.error("上传的文件必须是 JSON 格式");
        }

        try (InputStream inputStream = file.getInputStream()) {
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, Object> jsonData = objectMapper.readValue(inputStream, Map.class);
            Map<String, Map<String, Object>> holidayMap = (Map<String, Map<String, Object>>) jsonData.get("holiday");
            List<CustomFestival> customFestivals = new ArrayList<>();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

            for (Map.Entry<String, Map<String, Object>> entry : holidayMap.entrySet()) {
                Map<String, Object> holidayData = entry.getValue();

                CustomFestival customFestival = new CustomFestival();
                customFestival.setDate((String) holidayData.get("date"));
                customFestival.setHoliday((Boolean) holidayData.get("holiday"));
                customFestival.setName((String) holidayData.get("name"));
                if (holidayData.get("holidayType") == null) {
                    customFestival.setHolidayType(true);
                }
                customFestival.setHolidayTime(sdf.parse((String) holidayData.get("date")));
                customFestival.setYear(new SimpleDateFormat("yyyy").format(customFestival.getHolidayTime()));

                customFestivals.add(customFestival);
            }

            List<String> errorMessage = new ArrayList<>();
            int errorLines = 0;
            int successLines = 0;

            try {
                List<String> errors = ImportExcelUtil.importDateSave(customFestivals, CustomFestivalService.class, errorMessage, CommonConstant.SQL_INDEX_UNIQ_CODE);
                errorLines += errors.size();
                successLines += (customFestivals.size() - errorLines);
            } catch (Exception e) {
                return Result.error("数据导入失败: " + e.getMessage());
            }
            return ImportExcelUtil.imporReturnRes(errorLines, successLines, errorMessage);
        } catch (IOException | ParseException e) {
            return Result.error("读取或解析文件时出错: " + e.getMessage());
        }
    }

}
