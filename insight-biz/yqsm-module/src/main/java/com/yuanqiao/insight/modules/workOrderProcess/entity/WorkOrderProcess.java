package com.yuanqiao.insight.modules.workOrderProcess.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("yq_work_order_process")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "yq_work_order_process对象", description = "工单流程实例记录表")
public class WorkOrderProcess implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    public String id;

    /**
     * 流程实例ID
     */
    public String processInstanceId;
    /**
     * 流程分类
     */
    public String processCategory;
    /**
     * 状态
     */
    public String status;
    /**
     * 处理人
     */
    public String handlePerson;
    /**
     * 创建人
     */
    public String createBy;
    /**
     * 创建日期
     */
    public Date createTime;
    /**
     * 更新人
     */
    public String updateBy;
    /**
     * 更新日期
     */
    public Date updateTime;
    /**
     * 完成所消耗时间
     */
    public Long durationMillis;

    /**
     * 分配超时标示 1表示超时
     */
    public String allocationTimeOut;
    /**
     * 处理超时标示 1表示超时
     */
    public String handleTimeOut;

    /**
     * 变更类型
     */
    public String changeManageCategory;

    /**
     * 变更原因
     */
    public String changeManageReason;

    /**
     * 工单类型
     */
    public String workOrderType;

    /**
     * 事件申报方式
     */
    public String declarationMethod;
    /**
     * 问题申报来源
     */
    public String problemSource;

    /**
     * 申请人所属单位
     */
    @Dict(dictTable ="sys_depart",dicText = "depart_name",dicCode = "id")
    public String userUnitId;

    /**
     * 申请人
     */
    public String applicantUserId;

}
