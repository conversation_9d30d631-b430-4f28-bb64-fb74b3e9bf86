package com.yuanqiao.insight.modules.schedule.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.modules.schedule.entity.SchedulePlan;
import com.yuanqiao.insight.modules.schedule.entity.SchedulePlanRecord;
import com.yuanqiao.insight.modules.schedule.mapper.SchedulePlanMapper;
import com.yuanqiao.insight.modules.schedule.mapper.SchedulePlanRecordMapper;
import com.yuanqiao.insight.modules.schedule.service.SchedulePlanRecordService;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.List;
import java.util.UUID;

@Service
public class SchedulePlanRecordServiceImpl extends ServiceImpl<SchedulePlanRecordMapper, SchedulePlanRecord> implements SchedulePlanRecordService {

    @Autowired
    private SchedulePlanMapper scheduleService;

    /**
     * 添加完成记录
     *
     * @param schedulePlan
     */
    @Override
    public void add(SchedulePlan schedulePlan) {
        Boolean check = schedulePlan.getCheck();
        if (check) {
            long time1 = schedulePlan.getStartTime().getTime();
            long time = new Date().getTime();
            Assert.isTrue(time > time1, "还未到日程规定时间范围内");
        }
        SchedulePlanRecord schedulePlanRecord = new SchedulePlanRecord();
        schedulePlanRecord.setId(UUID.randomUUID().toString());
        schedulePlanRecord.setScheduleId(schedulePlan.getId());
        schedulePlanRecord.setScheduleTitle(schedulePlan.getTitle());
        schedulePlanRecord.setStartTime(schedulePlan.getStartTime());
        schedulePlanRecord.setEndTime(schedulePlan.getEndTime());
        schedulePlanRecord.setCreateTime(new Date());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        schedulePlanRecord.setUserId(sysUser.getUsername());
        schedulePlanRecord.setCreateBy(sysUser.getUsername());
        baseMapper.insert(schedulePlanRecord);
    }

    /**
     * 检验完成情况
     *
     * @param id
     * @return
     */
    @Override
    public boolean checkCompletion(String id) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        QueryWrapper<SchedulePlanRecord> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("user_id", sysUser.getUsername());
        queryWrapper1.eq("schedule_id", id);
        List<SchedulePlanRecord> schedulePlanRecords = baseMapper.selectList(queryWrapper1);
        return schedulePlanRecords.size() > 0;
    }
}
