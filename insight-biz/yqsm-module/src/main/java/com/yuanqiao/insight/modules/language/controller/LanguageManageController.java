package com.yuanqiao.insight.modules.language.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.modules.language.entity.LanguageManage;
import com.yuanqiao.insight.modules.language.service.LanguageManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

@Api(tags = "语言管理表")
@RestController
@RequestMapping("/language/languageManage")
@Slf4j
public class LanguageManageController extends JeecgController<LanguageManage,LanguageManageService> {

    @Autowired
    private LanguageManageService languageManageService;



    /**分页查询*/
    @ApiOperation(value = "语言管理表-分页列表查询",notes = "语言管理表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<LanguageManage>> queryPageList(@RequestParam(name = "pageNo",defaultValue = "1") Integer pageNo,
                                                       @RequestParam(name = "pageSize",defaultValue = "10") Integer pageSize,
                                                       @RequestParam(value = "languageInfo", required = false) String languageInfo,
                                                       @RequestParam(value = "status", required = false) Integer status,
                                                       @RequestParam(value = "module", required = false) String module){
        //查询条件
        //QueryWrapper<LanguageManage> queryWrapper = QueryGenerator.initQueryWrapper(languageManage, request.getParameterMap());
        //构建查询条件
        QueryWrapper<LanguageManage> queryWrapper = new QueryWrapper<>();
        //模糊查询
        queryWrapper.like(!StringUtils.isEmpty(languageInfo), "language_info", languageInfo);
        queryWrapper.eq(status != null, "status", status);
        queryWrapper.eq(module != null, "module", module);
        queryWrapper.orderByAsc("order_by");
        //分页条件
        Page<LanguageManage> page = new Page<>(pageNo, pageSize);
        ///分页条件和查询条件
        IPage<LanguageManage> pageList = languageManageService.page(page, queryWrapper);

        return Result.OK(pageList);

    }

    @GetMapping(value = "/queryAll")
    public Result<?> queryAll(String module){
        LambdaQueryWrapper<LanguageManage> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(module)){
            queryWrapper.eq(LanguageManage::getModule,module);
        }
        queryWrapper.orderByAsc(LanguageManage::getOrderBy);
        List<LanguageManage> list = service.list(queryWrapper);
        return Result.ok(list);
    }

    /**模糊查询*/
    /*@ApiOperation(value = "语言管理表-模糊查询", notes = "语言管理表-模糊查询")
    @GetMapping(value = "/info")
    public Result<List<LanguageManage>> queryLanguageInfo(@RequestParam(value = "languageInfo", required = false) String languageInfo,
                                                          @RequestParam(value = "status", required = false) Integer status){
        List<LanguageManage>  queryUserLanguage = languageManageService.queryUserLanguageInfo(languageInfo, status);
        return Result.ok(queryUserLanguage);

    }*/




    /**通过id查询*/
    @ApiOperation(value = "语言管理表-通过id查询",notes = "语言管理表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id",required = true) String id){
        LanguageManage languageManage = languageManageService.getById(id);
        if (languageManage == null){
            return Result.error("未找到对应数据！");
        }
        return Result.ok(languageManage);
    }



    /**添加*/
    @AutoLog(value = "语言管理表-添加")
    @ApiOperation(value = "语言管理表-添加",notes = "语言管理表-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody LanguageManage languageManage){
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (sysUser.getUsername().equals("admin") || sysUser.getRealname().equals("管理员")){
            languageManage.setStatus(1);
        }else {
            languageManage.setStatus(0);
        }
        languageManageService.save(languageManage);
        return Result.ok("添加成功！");

    }



    /**编辑*/
    @AutoLog(value = "语言管理表-编辑")
    @ApiOperation(value = "语言管理表-编辑",notes = "语言管理表-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody LanguageManage languageManage){
        languageManageService.updateById(languageManage);
        return Result.ok("编辑成功！");

    }



    /**通过id删除*/
    @AutoLog(value = "语言管理表-通过id删除")
    @ApiOperation(value = "语言管理表-通过id删除",notes = "语言管理表-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id",required = true) String id){
        languageManageService.removeById(id);
        return Result.ok("删除成功！");
    }



    /**批量删除*/
    @AutoLog(value = "语言管理表-批量删除")
    @ApiOperation(value = "语言管理表-批量删除",notes = "语言管理表-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids",required = true) String ids ){
        this.languageManageService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功！");
    }






    /**导出excel*/
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, LanguageManage languageManage){
        return super.exportXls(request, languageManage, LanguageManage.class,"语言管理表");
    }

    /**通过excel导入数据*/
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response){
        return super.importExcel(request, response, LanguageManage.class);
    }



}

