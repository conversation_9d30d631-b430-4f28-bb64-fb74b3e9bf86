package com.yuanqiao.insight.modules.workOrderProcess.mapper;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.modules.workOrderProcess.entity.WorkOrderProcess;
import com.yuanqiao.insight.modules.workOrderProcess.vo.HandOrderVO;
import com.yuanqiao.insight.modules.workOrderProcess.vo.HandleAverageVO;
import com.yuanqiao.insight.modules.workOrderProcess.vo.TypeCountVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface WorkOrderProcessServiceMapper extends BaseMapper<WorkOrderProcess> {
    IPage<HandOrderVO> handleTop(Page<?> page, String time1, String time2);

    IPage<HandOrderVO> handleTopByHg(Page<HandOrderVO> page, @Param("date1") DateTime date1, @Param("date2") DateTime date2);

    List<HandOrderVO> dayCount(String time1, String time2);

    List<HandOrderVO> dayCountByHg(@Param("date1") DateTime date1, @Param("date2") DateTime date2);

    List<HandOrderVO> dayCountAndStatus(String time1, String time2);

    HandOrderVO countApplyCompletion(String time1, String time2);

    List<TypeCountVo> getTypeCountVoByWrapper(String time1, String time2, String wrapper);

    List<TypeCountVo> getTypeCountVoByWrapperByHg(@Param("date1") DateTime date1, @Param("date2") DateTime date2, String wrapper);

    IPage<HandleAverageVO> handleAverageTop(Page page, String time1, String time2);

    IPage<HandleAverageVO> handleAverageTopByHg(Page<HandleAverageVO> page, @Param("date1") DateTime date1, @Param("date2") DateTime date2);

}
