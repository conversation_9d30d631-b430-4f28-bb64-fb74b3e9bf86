package com.yuanqiao.insight.modules.schedule.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanqiao.insight.modules.schedule.entity.SchedulePlan;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: 值班日程安排
 * @Author: zhang-bin
 * @Date: 2022-07-27
 * @Version: V1.0
 */
public interface SchedulePlanMapper extends BaseMapper<SchedulePlan> {
    /**
     * 保存值班安排与人员信息
     *
     * @param ScheduleId
     * @param userIds
     */
    @Insert("<script>" + " insert into yq_biz_schedule_user " + "(scheduleId,userId) " + " values " + "<foreach collection=\"userIds\" index=\"index\" item=\"item\" separator=\",\"> " + "(#{ScheduleId},#{item})" + "</foreach> " + "</script>")
    void insertScheduleUser(String ScheduleId, String[] userIds);

    /**
     * 删除人员与值班安排信息
     *
     * @param scheduleId
     */
    @Delete("<script>" + " DELETE FROM yq_biz_schedule_user WHERE scheduleId=#{scheduleId}" + "</script>")
    void deleteScheduleUserByScheduleId(String scheduleId);


    /**
     * 根据ID查询相关人员中文名称
     *
     * @param scheduleId
     * @return
     */
    @Select("<script>" + "SELECT realname FROM sys_users where del_flag = 0 and username in(" + "SELECT userId FROM yq_biz_schedule_user WHERE scheduleId=#{scheduleId}" + ")" + "</script>")
    List<String> selectRealNameByScheduleId(String scheduleId);

    /**
     * 根据ID查询关联人员id
     *
     * @param scheduleId
     * @return
     */
    @Select("<script>" + "SELECT userId FROM yq_biz_schedule_user WHERE scheduleId=#{scheduleId}" + "</script>")
    List<String> selectUserIdByScheduleId(String scheduleId);

    /**
     * 根据userName查询scheduleId
     *
     * @param userName
     * @return
     */
    @Select("<script>" + "SELECT scheduleId FROM yq_biz_schedule_user WHERE userId=#{userName}" + "</script>")
    List<String> selectScheduleIdsByUser(String userName);
}
