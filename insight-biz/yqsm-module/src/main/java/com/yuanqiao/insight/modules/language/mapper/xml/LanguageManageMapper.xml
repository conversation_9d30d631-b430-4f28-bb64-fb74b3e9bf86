<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.modules.language.mapper.LanguageManageMapper">
    <update id="updateLanguageInfo">
        update yq_biz_language_manage set language_info = #{languageInfo}, order_by = #{orderBy}
                                      where id = #{id};
    </update>

    <select id="queryStatusInfo" resultType="com.yuanqiao.insight.modules.language.entity.LanguageManage">
        select * from yq_biz_language_manage where status = #{status} order by order_by asc;
    </select>

    <select id="queryLanguageInfo" resultType="com.yuanqiao.insight.modules.language.entity.LanguageManage">
        select * from yq_biz_language_manage where language_info like concat('%', #{languageInfo}, '%') order by order_by asc;
    </select>

    <select id="queryAllInfo" resultType="com.yuanqiao.insight.modules.language.entity.LanguageManage">
        select * from yq_biz_language_manage
                 where language_info like concat('%', #{languageInfo}, '%') and status = #{status} order by order_by asc;
    </select>


</mapper>
