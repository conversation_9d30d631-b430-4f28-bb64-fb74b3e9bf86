package com.yuanqiao.insight.modules.language.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.modules.language.entity.LanguageManage;
import com.yuanqiao.insight.modules.language.mapper.LanguageManageMapper;
import com.yuanqiao.insight.modules.language.service.LanguageManageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class LanguageManageServiceImpl extends ServiceImpl<LanguageManageMapper, LanguageManage> implements LanguageManageService {

    @Autowired
    private LanguageManageMapper languageManageMapper;


    /**编辑*/
    /*@Override
    public void edit(String languageInfo, Integer orderBy, String id) {

        languageManageMapper.updateLanguageInfo(languageInfo, orderBy, id);
    }*/



    /**模糊查询*/
   /* @Override
    public List<LanguageManage> queryUserLanguageInfo(String languageInfo, Integer status) {
        //判断languageInfo和status是否为空
        if (languageInfo == null || languageInfo.isEmpty()){
            if (status == null){
                return null;
            }else {
                try {
                    List<LanguageManage> statusInfo = languageManageMapper.queryStatusInfo(status);
                    return statusInfo;
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        }else {
            if (status == null){
                try {
                    List<LanguageManage> languageInfoOne = languageManageMapper.queryLanguageInfo(languageInfo);
                    return languageInfoOne;
                }catch (Exception e){
                    e.printStackTrace();
                }
            }else {
                try {
                    List<LanguageManage> allInfo = languageManageMapper.queryAllInfo(languageInfo, status);
                    return allInfo;
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        }
        return null;
    }*/







}
