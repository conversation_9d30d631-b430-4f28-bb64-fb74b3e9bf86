package com.yuanqiao.insight.modules.asset.warehouse.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.modules.asset.warehouse.entity.WarehouseInputDetail;
import com.yuanqiao.insight.modules.asset.warehouse.mapper.WarehouseInputDetailMapper;
import com.yuanqiao.insight.modules.asset.warehouse.service.IWarehouseInputDetailService;
import org.springframework.stereotype.Service;

/**
 * @Description: warehouse_input_detail
 * @author: yqkj
 * @Date:   2022-10-28
 * @Version: V1.0
 */
@Service
public class WarehouseInputDetailServiceImpl extends ServiceImpl<WarehouseInputDetailMapper, WarehouseInputDetail> implements IWarehouseInputDetailService {

}
