package com.yuanqiao.insight.modules.schedule.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.concurrent.ScheduledFuture;

/**
 * @Description: 值班日程安排
 * @Author: zhang-bin
 * @Date:   2022-07-27
 * @Version: V1.0
 */
@Data
@TableName("yq_biz_schedule_plan")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class SchedulePlan implements Serializable {

    /*主键*/
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     *展示方式 1个人  2部门
     */
    private String showType;

    /*是否全天 true是 false否*/
    private boolean allDay;

    /*日程标题*/
    private String title;

    /*计划颜色*/
    private String planColor;

    /*重复提醒*/
    private String reminders;

    /*提醒时间*/
    private String remind;

    /*开始时间*/
    private Date startTime;

    /*结束时间*/
    private Date endTime;

    /*创建人*/
    private String createBy;

    /*创建日期*/
    private Date createTime;

    /*更新人*/
    private String updateBy;

    /*更新日期*/
    private  Date updateTime;

    /*描述*/
    private  String suggestion;

    /*文件地址*/
    private  String files;

    private  String cron;

    /*关联人员*/
    @TableField(exist = false)
    private String userIds;

    @TableField(exist = false)
    private ScheduledFuture<?> schedule;

    @TableField(exist = false)
    private Boolean check;



}
