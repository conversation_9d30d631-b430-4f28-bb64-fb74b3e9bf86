package com.yuanqiao.insight.modules.sla.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.yuanqiao.insight.modules.sla.eneity.SlaProcessOverTime;
import com.yuanqiao.insight.modules.sla.vo.SlaVo;
import org.flowable.task.api.Task;

import java.util.Map;


public interface SlaProcessOverTimeService extends IService<SlaProcessOverTime> {

    Map<String, SlaVo> queryStatisticsSlaList(String date);

    SlaProcessOverTime getOneByTask(Task task,String slaType);
}
