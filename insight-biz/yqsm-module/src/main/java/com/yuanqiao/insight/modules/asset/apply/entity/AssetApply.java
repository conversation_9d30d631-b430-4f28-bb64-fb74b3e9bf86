package com.yuanqiao.insight.modules.asset.apply.entity;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecg.common.handler.JSONArrayTypeHandler;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 物品申请单
 * @author: yqkj
 * @Date:   2022-10-20
 * @Version: V1.0
 */
@Data
@TableName("yq_pro_asset_apply")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "yq_pro_asset_apply对象", description = "物品申请单")
public class AssetApply implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 申领单号
     */
    @Excel(name = "申领单号", width = 30)
    @ApiModelProperty(value = "申领单号")
    private String serialNumber;
    /**
     * 申请人
     */
    @Excel(name = "申请人", dictTable = "sys_users", dicText = "realname", dicCode = "username")
    @ApiModelProperty(value = "申请人")
    @Dict(dictTable = "sys_users", dicText = "realname", dicCode = "username")
    private String createBy;
    /**
     * 申领时间
     */
    @ApiModelProperty(value = "申领时间")
    @Excel(name = "申领时间", format = "yyyy-MM-dd HH:mm:ss", width = 30)
    private Date createTime;
    /**
     * 状态
     */
    @Excel(name = "状态", width = 15, dicCode = "asset_apply_state")
    @Dict(dicCode = "asset_apply_state")
    @ApiModelProperty(value = "状态")
    private String state;
    /**
     * 申领内容
     */
    @Excel(name = "申领内容", width = 15)
    @ApiModelProperty(value = "申领内容")
    @TableField(typeHandler = JSONArrayTypeHandler.class)
    @NotEmpty
    private JSONArray content;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
    /**
     * 服务请求编码
     */
    private String bizSerialNumber;
}
