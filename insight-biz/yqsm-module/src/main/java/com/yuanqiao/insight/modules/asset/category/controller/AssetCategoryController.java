package com.yuanqiao.insight.modules.asset.category.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.modules.CodeRuleSetting.constant.RuleBindEnum;
import com.yuanqiao.insight.modules.asset.category.entity.AssetCategory;
import com.yuanqiao.insight.modules.asset.category.service.IAssetCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.FillRuleUtil;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

/**
 * @Description: asset_category
 * @author: yqkj
 * @Date: 2022-06-07
 * @Version: V1.0
 */
@Api(tags = "asset_category")
@RestController
@RequestMapping("/category/assetCategory")
@Slf4j
public class AssetCategoryController extends JeecgController<AssetCategory, IAssetCategoryService> {
    @Autowired
    private IAssetCategoryService assetCategoryService;

    /**
     * 分页列表查询
     *
     * @param assetCategory
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "asset_category-分页列表查询")
    @ApiOperation(value = "asset_category-分页列表查询", notes = "asset_category-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<AssetCategory>> queryPageList(AssetCategory assetCategory,
                                                      @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                      @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                      HttpServletRequest req) {
        QueryWrapper<AssetCategory> queryWrapper = QueryGenerator.initQueryWrapper(assetCategory, req.getParameterMap());
        Page<AssetCategory> page = new Page<AssetCategory>(pageNo, pageSize);
        IPage<AssetCategory> pageList = assetCategoryService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 获取类目树
     *
     * @return
     */
    @GetMapping(value = "/tree")
    public Result tree() {
        return assetCategoryService.tree();
    }

    @GetMapping(value = "/getCode")
    public String getCode() {
        return getSerialNumber();
    }

    @Nullable
    private String getSerialNumber() {
        //		生成自定义序号
        JSONObject formData = new JSONObject();
        formData.put("code-generation-bind", RuleBindEnum.category);

        String codeGeneration = (String) FillRuleUtil.executeRule("biz_code_generation", formData);
        if (StringUtils.isEmpty(codeGeneration)) {
            throw  new RuntimeException(("获取编码异常！"));
        }
        return codeGeneration;
    }


    /**
     * 添加
     *
     * @param assetCategory
     * @return
     */
    @AutoLog(value = "asset_category-添加")
    @ApiOperation(value = "asset_category-添加", notes = "asset_category-添加")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody AssetCategory assetCategory) {

//        用户没有使用自定义编码，默认生成一个
        if (StringUtils.isBlank(assetCategory.getCategoryCode())) {
            String serialNumber = getSerialNumber();
            assetCategory.setCategoryCode(serialNumber);
        }
        boolean save = assetCategoryService.save(assetCategory);
        return Result.OK("添加成功！", JSON.toJSONString(assetCategory));
    }

    /**
     * 编辑
     *
     * @param assetCategory
     * @return
     */
    @AutoLog(value = "asset_category-编辑")
    @ApiOperation(value = "asset_category-编辑", notes = "asset_category-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody AssetCategory assetCategory) {
        assetCategoryService.updateById(assetCategory);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "asset_category-通过id删除")
    @ApiOperation(value = "asset_category-通过id删除", notes = "asset_category-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) Integer id) {
        assetCategoryService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "asset_category-通过id删除")
    @ApiOperation(value = "asset_category-通过id删除", notes = "asset_category-通过id删除")
    @DeleteMapping(value = "/deleteCascade")
    public Result deleteCascade(@RequestParam(name = "id") String id) {
        return assetCategoryService.removeCascade(id);
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "asset_category-批量删除")
    @ApiOperation(value = "asset_category-批量删除", notes = "asset_category-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.assetCategoryService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "asset_category-通过id查询")
    @ApiOperation(value = "asset_category-通过id查询", notes = "asset_category-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        AssetCategory assetCategory = assetCategoryService.getById(id);
        if (assetCategory == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(assetCategory);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param assetCategory
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AssetCategory assetCategory) {
        return super.exportXls(request, assetCategory, AssetCategory.class, "asset_category");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AssetCategory.class);
    }

}
