package com.yuanqiao.insight.modules.quickEntrance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.modules.quickEntrance.eneity.QuickEntrance;
import com.yuanqiao.insight.modules.quickEntrance.mapper.QuickEntranceMapper;
import com.yuanqiao.insight.modules.quickEntrance.service.QuickEntranceService;
import com.yuanqiao.insight.modules.quickEntrance.vo.QuickMenu;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class QuickEntranceServiceImpl extends ServiceImpl<QuickEntranceMapper, QuickEntrance> implements QuickEntranceService {


    @Autowired
    ISysBaseAPI iSysBaseAPI;
    @Autowired
    QuickEntranceMapper quickEntranceMapper;
    /**
     * 添加快捷入口菜单
     *
     * @param ids
     */
    @Override
    @Transactional
    public void add(String ids) {
        //获取当前登录人员信息
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //删除关联的菜单信息
        LambdaQueryWrapper<QuickEntrance> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(QuickEntrance::getUserId,sysUser.getId());
        remove(wrapper);
        //添加菜单信息
        if (StringUtils.isNotEmpty(ids)) {
            String[] menuIds = ids.split(",");
            List<QuickEntrance> quickEntrances = new ArrayList<>();
            for (String menuId : menuIds) {
                QuickEntrance quickEntrance = new QuickEntrance();
                Integer platformType = iSysBaseAPI.getMenPlatformTypeById(menuId);
                quickEntrance.setPlatformType(platformType);
                quickEntrance.setUserId(sysUser.getId());
                quickEntrance.setMenuId(menuId);
                quickEntrances.add(quickEntrance);
            }
            saveBatch(quickEntrances);
        }
    }

    /**
     * 查询菜单信息
     *
     * @return
     */
    @Override
    public List<QuickMenu> queryMenu(Integer platformType) {
        //获取当前登录人员信息
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (platformType!=null){
            return quickEntranceMapper.queryMenuByUserIdAndPlatformType(sysUser.getId(),platformType);
        }else {
            return quickEntranceMapper.queryMenuByUserId(sysUser.getId());
        }
    }
}
