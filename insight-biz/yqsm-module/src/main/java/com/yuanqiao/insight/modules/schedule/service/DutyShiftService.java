package com.yuanqiao.insight.modules.schedule.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuanqiao.insight.modules.schedule.entity.DutyShift;
import com.yuanqiao.insight.modules.schedule.entity.DutyShiftPlan;
import com.yuanqiao.insight.modules.schedule.vo.UserVo;

import java.text.ParseException;
import java.util.List;

public interface DutyShiftService extends IService<DutyShift> {
    /**
     * 查询人员信息
     *
     * @param roleCode
     * @return
     */
    List<UserVo> selectUserList(String roleCode);

    /**
     * 修改班次
     *
     * @param dutyShift
     */
    void update(DutyShift dutyShift);

    /**
     * 删除班次
     *
     * @param id
     */
    void remove(String id);

    /**
     * 排班
     *
     * @param dutyShiftPlan
     */
    void schedulePlan(DutyShiftPlan dutyShiftPlan) throws ParseException;

    /**
     * 查询值班的人员
     *
     * @return
     */
    List<UserVo> queryUsers();

    List<UserVo> queryUsersByWeek();
}
