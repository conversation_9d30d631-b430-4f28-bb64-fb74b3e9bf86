package com.yuanqiao.insight.modules.equipment.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.modules.equipment.dto.EquipmentLinkDTO;
import com.yuanqiao.insight.modules.equipment.entity.EquipmentPlanLink;
import com.yuanqiao.insight.modules.equipment.service.IEquipmentPlanLinkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

 /**
 * @Description: equipment_plan_link
 * @author: 魏晨
 * @Date:   2022-11-02
 * @Version: V1.0
 */
@Api(tags="equipment_plan_link")
@RestController
@RequestMapping("/equipment/equipmentPlanLink")
@Slf4j
public class EquipmentPlanLinkController extends JeecgController<EquipmentPlanLink, IEquipmentPlanLinkService> {

	/**
	 * 分页列表查询
	 *
	 * @param equipmentPlanLink
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "equipment_plan_link-分页列表查询")
	@ApiOperation(value="equipment_plan_link-分页列表查询", notes="equipment_plan_link-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<EquipmentPlanLink>> queryPageList(EquipmentPlanLink equipmentPlanLink,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<EquipmentPlanLink> queryWrapper = QueryGenerator.initQueryWrapper(equipmentPlanLink, req.getParameterMap());
		Page<EquipmentPlanLink> page = new Page<EquipmentPlanLink>(pageNo, pageSize);
		IPage<EquipmentPlanLink> pageList = service.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param equipmentPlanLink
	 * @return
	 */
	@AutoLog(value = "equipment_plan_link-添加")
	@ApiOperation(value="equipment_plan_link-添加", notes="equipment_plan_link-添加")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody EquipmentPlanLink equipmentPlanLink) {
		service.save(equipmentPlanLink);
		return Result.OK("添加成功！");
	}

	@PostMapping(path = "saveOrUpdate")
	public Result saveOrUpdate(@RequestBody EquipmentLinkDTO equipmentLinkDTO){
		service.lambdaUpdate().eq(EquipmentPlanLink::getPlanId, equipmentLinkDTO.getPlanId()).remove();

		List<EquipmentPlanLink> equipmentPlanLinkList=new ArrayList<EquipmentPlanLink>();
		for (String equipmentId : equipmentLinkDTO.getEquipmentIds()) {
			EquipmentPlanLink equipmentPlanLink = new EquipmentPlanLink();
			equipmentPlanLink.setEquipmentId(equipmentId);
			equipmentPlanLink.setPlanId(equipmentLinkDTO.getPlanId());
			equipmentPlanLinkList.add(equipmentPlanLink);
		}
		service.saveBatch(equipmentPlanLinkList);
		return Result.ok("绑定成功！");
	}

	/**
	 *  编辑
	 *
	 * @param equipmentPlanLink
	 * @return
	 */
	@AutoLog(value = "equipment_plan_link-编辑")
	@ApiOperation(value="equipment_plan_link-编辑", notes="equipment_plan_link-编辑")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody EquipmentPlanLink equipmentPlanLink) {
		service.updateById(equipmentPlanLink);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "equipment_plan_link-通过id删除")
	@ApiOperation(value="equipment_plan_link-通过id删除", notes="equipment_plan_link-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		service.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "equipment_plan_link-批量删除")
	@ApiOperation(value = "equipment_plan_link-批量删除", notes = "equipment_plan_link-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
		service.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	 /**
	  * 解绑
	  *
	  * @param planId
	  * @param equipmentId
	  * @return
	  */
	 @DeleteMapping("deleteByPlanIdAndEquipmentId")
	 public Result<?> deleteByPlanIdAndEquipmentId(@RequestParam String planId, @RequestParam String equipmentId) {
		 boolean remove = service.lambdaUpdate().eq(EquipmentPlanLink::getPlanId, planId).eq(EquipmentPlanLink::getEquipmentId, equipmentId).remove();
		 return remove ? Result.OK("解绑成功!") : Result.error("解绑失败！");
	 }

	 /**
	  * 通过id查询
	  *
	  * @param id
	  * @return
	  */
	 //@AutoLog(value = "equipment_plan_link-通过id查询")
	 @ApiOperation(value = "equipment_plan_link-通过id查询", notes = "equipment_plan_link-通过id查询")
	 @GetMapping(value = "/queryById")
	public Result queryById(@RequestParam(name="id",required=true) String id) {
		EquipmentPlanLink equipmentPlanLink = service.getById(id);
		if(equipmentPlanLink==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(equipmentPlanLink);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param equipmentPlanLink
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, EquipmentPlanLink equipmentPlanLink) {
        return super.exportXls(request, equipmentPlanLink, EquipmentPlanLink.class, "equipment_plan_link");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, EquipmentPlanLink.class);
    }

}
