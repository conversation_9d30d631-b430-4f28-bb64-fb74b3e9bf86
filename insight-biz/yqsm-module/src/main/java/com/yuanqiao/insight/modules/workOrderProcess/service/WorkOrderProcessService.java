package com.yuanqiao.insight.modules.workOrderProcess.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yuanqiao.insight.modules.workOrderProcess.entity.DepartProcessStatistics;
import com.yuanqiao.insight.modules.workOrderProcess.entity.WorkOrderProcess;
import com.yuanqiao.insight.modules.workOrderProcess.vo.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface WorkOrderProcessService extends IService<WorkOrderProcess> {

    WorkOrderProcess getOneByProcessInstanceId(String processInstanceId);

    List<HandOrderVO> handleTop(String time1, String time2);

    List<HandOrderVO> dayCount(String time1, String time2);

    List<TypeCountVo> typeCount(String time1, String time2);

    /**
     * @param time1  开始时间
     * @param time2  截止时间
     * @param adCode 所属区域编码
     * @return
     */
    Map<String, List<TypeCountVo>> statistics(String time1, String time2, String adCode);

    List<HandleAverageVO> handleAverageTop(String time1, String time2);

    List<TypeCountVo> evaluationStatistics(String time1, String time2);

    List<TypeCountVo> workOrderTypeStatistics(String time1, String time2);

    List<TypeCountVo> workOrderStatusStatistics(String time1, String time2);

    Object queryChangeManageReason(String time1, String time2);

    Object queryChangeManageCategory(String time1, String time2);

    Object queryDeclarationMethod(String time1, String time2);

    Object queryProblemSource(String time1, String time2);

    List<HandOrderVO> countByType();

    List<HandTypeVo> trendStatistics();

    /**
     * 根据区域获取工单统计信息
     *
     * @param time1  查询时间范围-开始时间
     * @param time2  查询时间范围-截止时间
     * @param adCode 区域编码
     * @return
     */
    List<TypeCountVo> informationStatistics(String time1, String time2, String adCode);

    Integer queryMonthStatistics(String adCode);

    void addWorkOrderProcess(WorkOrderProcess workOrderProcess);

    void updateWorkOrderProcess(WorkOrderProcess workOrderProcess, String type, Date dueDate);

    List<TypeCountVo> getAllDeptStatistics();

    Page<?> getListDeptStatistics( int pageNo, int pageSize);

    List<WorkOrderProcess> getListByStatusTime(String status, String time1, String time2, List<String> departs);

    Integer getListByTimeTimeout(String type, String time1, String time2, List<String> departs);

    List<DepartProcessStatistics> getDepartStatisticsVos(MPJLambdaWrapper<WorkOrderProcess> wrapper);
}
