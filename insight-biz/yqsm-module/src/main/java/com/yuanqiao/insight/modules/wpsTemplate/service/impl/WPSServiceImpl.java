package com.yuanqiao.insight.modules.wpsTemplate.service.impl;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.yuanqiao.insight.modules.wpsTemplate.service.IWPSService;
import org.springframework.stereotype.Service;

import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;



/**
 * <AUTHOR>
 * @ClassName RegistrationServiceImpl
 * @description: TODO
 * @datetime 2023年 10月 10日 15:37
 * @version: 1.0
 */
@Service
public class WPSServiceImpl implements IWPSService {
    @Override
    public XWPFTemplate getRegistration(Map<String, Object> variables,InputStream inputStream) throws FileNotFoundException {
        //SpEl配置
        Configure config = Configure.builder().useSpringEL(false).build();

        if (variables.get("changeManageClass")!= null){
            //多选进行特殊处理
            List changeManageClass = (List) variables.get("changeManageClass");
            variables.put("changeManageClassr", "无");
            variables.put("changeManageClassy", "无");
            variables.put("changeManageClassw", "无");
            variables.put("changeManageClassz", "无");
            variables.put("changeManageClassj", "无");
            variables.put("changeManageClassq", "无");

            for (Object changeManage : changeManageClass) {
                System.out.println("==============================================" + changeManage);
                switch (changeManage.toString()) {
                    case "软件变更":
                        variables.put("changeManageClassr", "软件变更");
                        break;
                    case "硬件变更":
                        variables.put("changeManageClassy", "硬件变更");
                        break;
                    case "网络变更":
                        variables.put("changeManageClassw", "网络变更");
                        break;
                    case "资产变更":
                        variables.put("changeManageClassz", "资产变更");
                        break;
                    case "基础设施变更":
                        variables.put("changeManageClassj", "基础设施变更");
                        break;
                    case "其他":
                        variables.put("changeManageClassq", "其他");
                        break;
                    default:
                        break;
                }
            }
        }
        //加载模板文件
        XWPFTemplate template = XWPFTemplate.compile(inputStream,config);

        //填充模板数据
        template.render(variables);
        return template;
    }
}
