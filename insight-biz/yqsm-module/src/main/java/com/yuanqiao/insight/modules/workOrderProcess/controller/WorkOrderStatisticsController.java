package com.yuanqiao.insight.modules.workOrderProcess.controller;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yuanqiao.insight.modules.workOrderProcess.entity.DepartProcessStatistics;
import com.yuanqiao.insight.modules.workOrderProcess.entity.WorkOrderProcess;
import com.yuanqiao.insight.modules.workOrderProcess.service.WorkOrderProcessService;
import com.yuanqiao.insight.modules.workOrderProcess.vo.HandOrderVO;
import com.yuanqiao.insight.modules.workOrderProcess.vo.HandleAverageVO;
import com.yuanqiao.insight.modules.workOrderProcess.vo.TypeCountVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;


@Api(tags = "运维工单详情表")
@RestController
@RequestMapping("/data-analysis/order")
@Slf4j
public class WorkOrderStatisticsController {

    @Autowired
    WorkOrderProcessService workOrderProcessService;

    /**
     * 工单处理数量TOP10
     *
     * @param time1
     * @param time2
     * @return
     */
    @ApiOperation(value = "运维工单详情表-工单处理数量TOP10", notes = "运维工单详情表-工单处理数量TOP10")
    @GetMapping(value = "/handle/top")
    public Result<?> handleTop(@RequestParam(name = "time1") String time1, @RequestParam(name = "time2") String time2) {
        List<HandOrderVO> list = workOrderProcessService.handleTop(time1, time2);
        return Result.OK(list);
    }

    /**
     * 返回用户指定时间内的的结果, 默认返回最近30天
     *
     * @param time1 开始时间
     * @param time2 结束时间
     * @return
     */
    @ApiOperation(value = "运维工单详情表-工单数量统计", notes = "运维工单详情表-工单数量统计")
    @GetMapping(value = "/day/count")
    public Result<?> dayCount(String time1, String time2) {
        List<HandOrderVO> handOrderVOS = workOrderProcessService.dayCount(time1, time2);
        return Result.OK(handOrderVOS);
    }

    /**
     * - 返回指定时间内的类型统计
     *
     * @param time1 开始时间
     * @param time2 结束时间
     * @return
     */
    @ApiOperation(value = "运维工单详情表-工单类型统计", notes = "运维工单详情表-工单类型统计")
    @GetMapping(value = "/type/count")
    public Result<?> typeCount(String time1, String time2) {
        List<TypeCountVo> typeCountVos = workOrderProcessService.typeCount(time1, time2);
        return Result.OK(typeCountVos);
    }

    /**
     * 工单信息统计
     *
     * @param time1
     * @param time2
     * @return
     */
    @ApiOperation(value = "运维工单详情表-工单信息统计", notes = "运维工单详情表-工单信息统计")
    @GetMapping(value = "/count")
    public Result<?> statistics(String time1, String time2) {
        Map<String, List<TypeCountVo>> map = workOrderProcessService.statistics(time1, time2, null);
        return Result.OK(map);
    }

    /**
     * - 返回用户指定时间内的的平均处理时间前10位
     * - 按照平均处理时间升序排列
     *
     * @param time1 开始时间
     * @param time2 结束时间
     * @return
     */
    @ApiOperation(value = "运维工单详情表-平均处理时间TOP10", notes = "运维工单详情表-平均处理时间TOP10")
    @GetMapping(value = "/handle/average/top")
    public Result<?> handleAverageTop(@RequestParam(name = "time1") String time1, @RequestParam(name = "time2") String time2) {
        List<HandleAverageVO> list = workOrderProcessService.handleAverageTop(time1, time2);
        return Result.OK(list);
    }

    /**
     * 评价统计
     *
     * @param time1
     * @param time2
     * @return
     */
    @GetMapping(value = "/evaluation")
    public Result<?> evaluationStatistics(String time1, String time2) {
        List<TypeCountVo> typeCountVos = workOrderProcessService.evaluationStatistics(time1, time2);
        return Result.OK(typeCountVos);
    }

    /**
     * 工单类型统计
     *
     * @param time1
     * @param time2
     * @return
     */
    @GetMapping(value = "/workOrderTypeStatistics")
    public Result<?> workOrderTypeStatistics(String time1, String time2) {
        return Result.OK(workOrderProcessService.workOrderTypeStatistics(time1, time2));
    }

    /**
     * 工单状态统计
     *
     * @param time1
     * @param time2
     * @return
     */
    @GetMapping(value = "/workOrderStatusStatistics")
    public Result<?> workOrderStatusStatistics(String time1, String time2) {
        return Result.OK(workOrderProcessService.workOrderStatusStatistics(time1, time2));
    }

    @GetMapping("/queryChangeManageReason")
    public Result<?> queryChangeManageReason(String time1, String time2) {
        return Result.OK(workOrderProcessService.queryChangeManageReason(time1, time2));
    }

    @GetMapping("/queryChangeManageCategory")
    public Result<?> queryChangeManageCategory(String time1, String time2) {
        return Result.OK(workOrderProcessService.queryChangeManageCategory(time1, time2));
    }

    @GetMapping("/queryDeclarationMethod")
    public Result<?> queryDeclarationMethod(String time1, String time2) {
        return Result.OK(workOrderProcessService.queryDeclarationMethod(time1, time2));
    }

    @GetMapping("/queryProblemSource")
    public Result<?> queryProblemSource(String time1, String time2) {
        return Result.OK(workOrderProcessService.queryProblemSource(time1, time2));
    }

    /**
     * 工单处理结果统计
     *
     * @return
     */
    @GetMapping(value = "/countByType")
    public Result<?> countByType() {
        return Result.OK(workOrderProcessService.countByType());
    }

    /**
     * 工单趋势一个月统计
     *
     * @return
     */
    @GetMapping(value = "/trendStatistics")
    public Result<?> trendStatistics() {
        return Result.OK(workOrderProcessService.trendStatistics());
    }

    /**
     * 服务请求信息统计
     *
     * @return
     */
    @GetMapping(value = "/informationStatistics")
    public Result<?> informationStatistics(@RequestParam(name = "time1", defaultValue = "") String time1,
                                           @RequestParam(name = "time2", defaultValue = "") String time2,
                                           @RequestParam(name = "adCode", defaultValue = "") String adCode) {
        return Result.OK(workOrderProcessService.informationStatistics(time1, time2, adCode));
    }

    /**
     * 通过区域显示当月工单数量
     *
     * @param adCode
     * @return
     */
    @GetMapping(value = "/queryMonthStatistics")
    public Result<?> queryMonthStatistics(@RequestParam(name = "adCode", defaultValue = "") String adCode) {
        return Result.OK(workOrderProcessService.queryMonthStatistics(adCode));
    }


    @GetMapping(value = "/getAllDeptStatistics")
    public Result<?> getAllDeptStatistics() {
        return Result.OK(workOrderProcessService.getAllDeptStatistics());
    }


    @GetMapping(value = "/getListDeptStatistics")
    public Result<?> getListDeptStatistics(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                           @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        return Result.OK(workOrderProcessService.getListDeptStatistics(pageNo, pageSize));
    }

    @RequestMapping(value = "/exportXlsDept")
    public ModelAndView exportXls(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "工单单位统计"); //此处设置的filename无效 ,前端会重更新设置一下
        mv.addObject(NormalExcelConstants.CLASS, DepartProcessStatistics.class);
        try {
            mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("工单单位统计报表", "工单单位统计"));
        } catch (Exception e) {
            e.printStackTrace();
        }
        MPJLambdaWrapper<WorkOrderProcess> wrapper = new MPJLambdaWrapper<>();
        wrapper.select("user_unit_id as DEPARTID, count(*) as NUMBER");
        List<DepartProcessStatistics> exportList = workOrderProcessService.getDepartStatisticsVos(wrapper);
        mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
        return mv;
    }


}
