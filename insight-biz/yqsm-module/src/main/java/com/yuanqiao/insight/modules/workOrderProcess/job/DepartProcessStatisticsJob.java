package com.yuanqiao.insight.modules.workOrderProcess.job;


import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yuanqiao.insight.modules.workOrderProcess.entity.DepartProcessStatistics;
import com.yuanqiao.insight.modules.workOrderProcess.entity.WorkOrderProcess;
import com.yuanqiao.insight.modules.workOrderProcess.service.DepartProcessStatisticsService;
import com.yuanqiao.insight.modules.workOrderProcess.service.WorkOrderProcessService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class DepartProcessStatisticsJob implements Job {

    @Autowired
    DepartProcessStatisticsService departProcessStatisticsService;
    @Autowired
    private WorkOrderProcessService workOrderProcessService;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        MPJLambdaWrapper<WorkOrderProcess> wrapper = new MPJLambdaWrapper<>();
        wrapper.select("user_unit_id as DEPARTID, count(*) as NUMBER,d.depart_name as DEPARTNAME");
        wrapper.leftJoin("sys_depart d  on  d.id = user_unit_id");
        List<DepartProcessStatistics> departStatisticsVos = workOrderProcessService.getDepartStatisticsVos(wrapper);
        departProcessStatisticsService.saveBatch(departStatisticsVos);
    }
}
