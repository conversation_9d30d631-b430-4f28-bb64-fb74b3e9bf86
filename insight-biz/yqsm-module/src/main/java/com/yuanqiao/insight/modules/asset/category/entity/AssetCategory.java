package com.yuanqiao.insight.modules.asset.category.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: yq_pro_asset_category
 * @author: yqkj
 * @Date: 2022-06-07
 * @Version: V1.0
 */
@Data
@TableName("yq_pro_asset_category")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "yq_pro_asset_category对象", description = "yq_pro_asset_category")
public class AssetCategory implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 类目名称
     */
    @Excel(name = "类目名称", width = 15)
    @ApiModelProperty(value = "类目名称")
    private String name;
    /**
     * 父级节点
     */
    @Excel(name = "父级节点", width = 15)
    @ApiModelProperty(value = "父级节点")
    private String parentId;
    /**
     * createBy
     */
    @ApiModelProperty(value = "createBy")
    private String createBy;
    /**
     * createTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "createTime")
    private java.util.Date createTime;
    /**
     * updateBy
     */
    @ApiModelProperty(value = "updateBy")
    private String updateBy;
    /**
     * updateTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "updateTime")
    private java.util.Date updateTime;


    private String remarks;
    private String categoryCode;

    @ApiModelProperty("1：已删除 0：正常")
    @TableLogic
    private Boolean delFlag;

    @TableField(exist = false)
    private List<AssetCategory> assetCategories;
}
