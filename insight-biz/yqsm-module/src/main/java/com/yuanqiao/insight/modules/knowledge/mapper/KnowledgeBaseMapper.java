package com.yuanqiao.insight.modules.knowledge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanqiao.insight.modules.knowledge.entity.KnowledgeBase;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 知识库
 * @author: wangyuan<PERSON>
 * @Date: 2022-08-11
 * @Version: V1.0
 */
public interface KnowledgeBaseMapper extends BaseMapper<KnowledgeBase> {

    KnowledgeBase selectByServiceRequestCode(@Param("serviceRequestCode") String serviceRequestCode);

    List<KnowledgeBase> selectAllByCategory( @Param("categoryId") String categoryId);

    List<KnowledgeBase> queryByCondition(@Param("current") Integer current, @Param("pageSize") Integer pageSize, @Param("categoryIds") List<String> categoryIds, @Param("knowledgeBase")  KnowledgeBase knowledgeBase);

    long queryCountByCondition(@Param("categoryIds")List<String> categoryIds,@Param("knowledgeBase") KnowledgeBase knowledgeBase);

    List<KnowledgeBase> queryByCategoryAndCondition(@Param("categoryIds")List<String> categoryIds, @Param("knowledgeBase")KnowledgeBase knowledgeBase);
}
