package com.yuanqiao.insight.modules.knowledge.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: yq_op_knowledge_log
 * @author: yqkj
 * @Date:   2023-06-08
 * @Version: V1.0
 */
@Data
@TableName("yq_op_knowledge_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="yq_op_knowledge_log对象", description="yq_op_knowledge_log")
public class KnowledgeLog implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键ID*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;
	/**知识id*/
	@Excel(name = "知识id", width = 15)
    @ApiModelProperty(value = "知识id")
    private String knowledgeId;
	/**操作类型*/
	@Excel(name = "操作类型", width = 15, dicCode = "operation_type")
	@Dict(dicCode = "operation_type")
    @ApiModelProperty(value = "操作类型")
    private String operationType;
	/**操作说明*/
	@Excel(name = "操作说明", width = 15)
    @ApiModelProperty(value = "操作说明")
    private String description;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    @Dict(dictTable = "sys_users", dicText = "realname", dicCode = "username")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
}
