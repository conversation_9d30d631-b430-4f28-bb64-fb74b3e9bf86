package com.yuanqiao.insight.modules.asset.warehouse.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.modules.asset.warehouse.entity.WarehouseInputDetail;
import com.yuanqiao.insight.modules.asset.warehouse.service.IWarehouseInputDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

 /**
 * @Description: warehouse_input_detail
 * @author: 魏晨
 * @Date:   2022-10-28
 * @Version: V1.0
 */
@Api(tags="warehouse_input_detail")
@RestController
@RequestMapping("/asset.warehouse/warehouseInputDetail")
@Slf4j
public class WarehouseInputDetailController extends JeecgController<WarehouseInputDetail, IWarehouseInputDetailService> {

	/**
	 * 分页列表查询
	 *
	 * @param warehouseInputDetail
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "warehouse_input_detail-分页列表查询")
	@ApiOperation(value="warehouse_input_detail-分页列表查询", notes="warehouse_input_detail-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WarehouseInputDetail>> queryPageList(WarehouseInputDetail warehouseInputDetail,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		warehouseInputDetail.setSubmitFlag(1);
		QueryWrapper<WarehouseInputDetail> queryWrapper = QueryGenerator.initQueryWrapper(warehouseInputDetail, req.getParameterMap());
		Page<WarehouseInputDetail> page = new Page<WarehouseInputDetail>(pageNo, pageSize);
		IPage<WarehouseInputDetail> pageList = service.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	 /**
	  * 获取物品仓储不同版本的列表
	  *
	  * @param warehouseInputDetail
	  * @return
	  */
	 @GetMapping("stockList")
	 public Result stockList( WarehouseInputDetail warehouseInputDetail) {
		 List<WarehouseInputDetail> warehouseInputDetailList = service.lambdaQuery()
				 .gt(WarehouseInputDetail::getItemQuantity, 0)
				 .eq(WarehouseInputDetail::getItemId, warehouseInputDetail.getItemId())
				 .eq(WarehouseInputDetail::getSubmitFlag, warehouseInputDetail.getSubmitFlag())
				 .orderBy(true, true, WarehouseInputDetail::getVersion)
				 .list();
		 return warehouseInputDetailList.size() > 0 ? Result.ok(warehouseInputDetailList) : Result.error("库存已空！");

	 }

	/**
	 *   添加
	 *
	 * @param warehouseInputDetail
	 * @return
	 */
	@AutoLog(value = "warehouse_input_detail-添加")
	@ApiOperation(value="warehouse_input_detail-添加", notes="warehouse_input_detail-添加")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody WarehouseInputDetail warehouseInputDetail) {
		service.save(warehouseInputDetail);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param warehouseInputDetail
	 * @return
	 */
	@AutoLog(value = "warehouse_input_detail-编辑")
	@ApiOperation(value="warehouse_input_detail-编辑", notes="warehouse_input_detail-编辑")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody WarehouseInputDetail warehouseInputDetail) {
		service.updateById(warehouseInputDetail);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "warehouse_input_detail-通过id删除")
	@ApiOperation(value="warehouse_input_detail-通过id删除", notes="warehouse_input_detail-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		service.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "warehouse_input_detail-批量删除")
	@ApiOperation(value="warehouse_input_detail-批量删除", notes="warehouse_input_detail-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		service.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "warehouse_input_detail-通过id查询")
	@ApiOperation(value="warehouse_input_detail-通过id查询", notes="warehouse_input_detail-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		WarehouseInputDetail warehouseInputDetail = service.getById(id);
		if(warehouseInputDetail==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(warehouseInputDetail);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param warehouseInputDetail
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WarehouseInputDetail warehouseInputDetail) {
		return super.exportXls(request, warehouseInputDetail, WarehouseInputDetail.class, "入库详情");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WarehouseInputDetail.class);
    }

}
