package com.yuanqiao.insight.modules.equipment.controller;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.common.core.util.SecurityUtils;
import com.yuanqiao.insight.modules.CodeRuleSetting.constant.RuleBindEnum;
import com.yuanqiao.insight.modules.equipment.constant.EquipmentLogEnum;
import com.yuanqiao.insight.modules.equipment.constant.EquipmentStateEnum;
import com.yuanqiao.insight.modules.equipment.entity.Equipment;
import com.yuanqiao.insight.modules.equipment.entity.EquipmentLog;
import com.yuanqiao.insight.modules.equipment.service.IEquipmentLogService;
import com.yuanqiao.insight.modules.equipment.service.IEquipmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.common.util.FillRuleUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 设备表
 * @author: yqkj
 * @Date: 2022-07-25
 * @Version: V1.0
 */
@Api(tags = "设备表")
@RestController
@RequestMapping("/equipment/equipment")
@Slf4j
@RequiredArgsConstructor
public class EquipmentController extends JeecgController<Equipment, IEquipmentService> {
    private final IEquipmentService equipmentService;
    private final IEquipmentLogService iEquipmentLogService;
    private final ISysBaseAPI iSysBaseAPI;


    @Value("${yq.logo}")
    private Boolean logoValid;

    /**
     * 分页列表查询
     *
     * @param equipment
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "设备表-分页列表查询")
    @ApiOperation(value = "设备表-分页列表查询", notes = "设备表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<Equipment>> queryPageList(Equipment equipment,
                                                  @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                  @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                  HttpServletRequest req) {
        QueryWrapper<Equipment> queryWrapper = QueryGenerator.initQueryWrapper(equipment, req.getParameterMap());
        Page<Equipment> page = new Page<Equipment>(pageNo, pageSize);
        IPage<Equipment> pageList = equipmentService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 分页列表查询 小程序
     *
     * @param equipment
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "设备表-分页列表查询")
    @ApiOperation(value = "设备表-分页列表查询", notes = "设备表-分页列表查询")
    @GetMapping(value = "/listApp")
    public Result<IPage<Equipment>> queryPageListApp(Equipment equipment,
                                                     @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                     @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                     HttpServletRequest req) {
        QueryWrapper<Equipment> queryWrapper = QueryGenerator.initQueryWrapper(equipment, req.getParameterMap());
        Page<Equipment> page = new Page<Equipment>(pageNo, pageSize);
        IPage<Equipment> pageList = equipmentService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    @ApiOperation(value = "设备表-查询全部", notes = "设备表-查询全部，工单下拉")
    @GetMapping(value = "/allList")
    public Result<?> queryAllList() {
        List<Equipment> list = equipmentService.list();
        return Result.ok(list);
    }

    /**
     * 设备表-查询全部 小程序
     *
     * @return
     */
    @ApiOperation(value = "设备表-查询全部", notes = "设备表-查询全部，工单下拉")
    @GetMapping(value = "/allListApp")
    public Result<?> allListApp() {
        List<Equipment> list = equipmentService.list();
        return Result.ok(list);
    }

    @ApiOperation(value = "设备表-根据设备编号查询", notes = "根据设备编号查询")
    @GetMapping(value = "/queryBySerialNumber")
    public Result<?> getBySerialNumber(@RequestParam(name = "serialNumber", required = true) String serialNumber) {
        if (StringUtils.isEmpty(serialNumber)) {
            return Result.error("参数'serialNumber'为空，获取失败");
        }

        QueryWrapper<Equipment> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("serial_number", serialNumber);
        Equipment equipment = equipmentService.getOne(queryWrapper);
        return Result.ok(equipment);
    }

    /**
     * 添加
     *
     * @param equipment
     * @return
     */
    @AutoLog(value = "设备表-添加")
    @ApiOperation(value = "设备表-添加", notes = "设备表-添加")
    @PostMapping(value = "/add")
    @Transactional
    public Result<?> add(@RequestBody Equipment equipment) {
        JSONObject formData = new JSONObject();
        formData.put("code-generation-bind", RuleBindEnum.equipment.name());

        String codeGeneration = (String) FillRuleUtil.executeRule("biz_code_generation", formData);
        if (StringUtils.isEmpty(codeGeneration)) {
            throw new RuntimeException(("获取编码异常！"));
        }
        equipment.setSerialNumber(codeGeneration);

        boolean flag = equipmentService.save(equipment);
        if (flag) {
            EquipmentLog equipmentLog = new EquipmentLog();
            List<DictModel> dictModelList = iSysBaseAPI.getDictItems("equipment_state");
            if (dictModelList != null && dictModelList.size() > 0) {
                for (DictModel dictModel : dictModelList) {
                    String value = dictModel.getValue();
                    String state = equipment.getState();
                    if (dictModel.getValue().equals(equipment.getState())) {
                        equipmentLog.setDescription("新增" + dictModel.getText() + "设备");
                    }
                }
            }

            equipmentLog.setEquipmentId(equipment.getId());
            equipmentLog.setCategory(EquipmentLogEnum.create.name());
            equipmentLog.setUsername(SecurityUtils.getUser().getUsername());
            equipmentLog.setDealTime(new Date());
            iEquipmentLogService.save(equipmentLog);
        }
        return Result.OK("添加成功！");
    }

    /**
     * 设备启用
     *
     * @param equipment
     * @return
     */
    @AutoLog(value = "设备表-启用")
    @ApiOperation(value = "设备表-启用", notes = "设备表-启用")
    @RequestMapping(value = "/start", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> start(@RequestBody Equipment equipment) {
        boolean update = equipmentService.lambdaUpdate().eq(Equipment::getId, equipment.getId())
                .set(Equipment::getState, EquipmentStateEnum.normal.name())
                .update();
        if (update) {

            EquipmentLog equipmentLog = new EquipmentLog();
            equipmentLog.setEquipmentId(equipment.getId());
            equipmentLog.setCategory(EquipmentLogEnum.start.name());
            equipmentLog.setUsername(SecurityUtils.getUser().getUsername());
            equipmentLog.setDealTime(new Date());
            equipmentLog.setDescription(equipment.getOperateDescription());
            iEquipmentLogService.save(equipmentLog);
        }
        return Result.OK("设备开启成功!");
    }

    /**
     * 设备停用
     *
     * @param equipment
     * @return
     */
    @AutoLog(value = "设备表-停用")
    @ApiOperation(value = "设备表-停用", notes = "设备表-停用")
    @RequestMapping(value = "/stop", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> stop(@RequestBody Equipment equipment) {
        boolean update = equipmentService.lambdaUpdate().eq(Equipment::getId, equipment.getId())
                .set(Equipment::getState, EquipmentStateEnum.pause.name())
                .update();

//        添加执行日志
        if (update) {
            EquipmentLog equipmentLog = new EquipmentLog();
            equipmentLog.setEquipmentId(equipment.getId());
            equipmentLog.setCategory(EquipmentLogEnum.stop.name());
            equipmentLog.setUsername(SecurityUtils.getUser().getUsername());
            equipmentLog.setDealTime(new Date());
            equipmentLog.setDescription(equipment.getOperateDescription());
            iEquipmentLogService.save(equipmentLog);
        }

        return Result.OK("设备停用成功!");
    }

    /**
     * 提交报废申请
     *
     * @param equipment
     * @return
     */
    @PutMapping("applyScrap")
    public Result applyScrap(@RequestBody Equipment equipment) {
        boolean update = service.lambdaUpdate().eq(Equipment::getId, equipment.getId())
                .set(Equipment::getState, EquipmentStateEnum.applyScrap.name())
                .set(Equipment::getScrapExplain, equipment.getScrapExplain())
                .set(Equipment::getScrapUser, equipment.getScrapUser())
                .set(Equipment::getScrapTime, equipment.getScrapTime())
                .update();

        if (update) {
            EquipmentLog equipmentLog = new EquipmentLog();
            equipmentLog.setEquipmentId(equipment.getId());
            equipmentLog.setCategory(EquipmentLogEnum.applyScrap.name());
            equipmentLog.setDealTime(new Date());
            equipmentLog.setScrapUser(equipment.getScrapUser());
            equipmentLog.setScrapTime(equipment.getScrapTime());
            equipmentLog.setUsername(equipment.getScrapUser());
            equipmentLog.setDescription(equipment.getScrapExplain());
            iEquipmentLogService.save(equipmentLog);
        }
        return Result.ok("已提交报废申请！");
    }

    /**
     * 驳回报废申请
     *
     * @param equipment
     * @return
     */
    @PutMapping(value = "/rejectScrap")
    public Result rejectScrap(@RequestBody Equipment equipment) {

        boolean update = service.lambdaUpdate().eq(Equipment::getId, equipment.getId())
                .set(Equipment::getState, EquipmentStateEnum.pause.name())
                .update();

        if (update) {
            EquipmentLog equipmentLog = new EquipmentLog();
            equipmentLog.setEquipmentId(equipment.getId());
            equipmentLog.setCategory(EquipmentLogEnum.rejectScrap.name());
            equipmentLog.setDealTime(new Date());
            equipmentLog.setUsername(SecurityUtils.getUser().getUsername());
            equipmentLog.setDescription(equipment.getOperateDescription());
            iEquipmentLogService.save(equipmentLog);
        }
        return Result.ok("驳回成功！");
    }

    /**
     * 接受报废申请
     *
     * @param equipment
     * @return
     */
    @PutMapping(path = "acceptScrap")
    public Result acceptScrap(@RequestBody Equipment equipment) {
        boolean update = service.lambdaUpdate().eq(Equipment::getId, equipment.getId())
                .set(Equipment::getState, EquipmentStateEnum.rubbish.name())
                .update();
        if (update) {
            EquipmentLog equipmentLog = new EquipmentLog();
            equipmentLog.setEquipmentId(equipment.getId());
            equipmentLog.setCategory(EquipmentLogEnum.acceptScrap.name());
            equipmentLog.setDealTime(new Date());
            equipmentLog.setScrapUser(equipment.getScrapUser());
            equipmentLog.setScrapTime(equipment.getScrapTime());
            equipmentLog.setUsername(SecurityUtils.getUser().getUsername());
            equipmentLog.setDescription(equipment.getOperateDescription());
            iEquipmentLogService.save(equipmentLog);
        }
        return Result.ok("报废成功！");
    }

    /**
     * 编辑
     *
     * @param equipment
     * @return
     */
    @AutoLog(value = "设备表-编辑")
    @ApiOperation(value = "设备表-编辑", notes = "设备表-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody Equipment equipment) {
        if (Objects.isNull(equipment.getSerialNumber())) {
            JSONObject formData = new JSONObject();
            formData.put("code-generation-bind", RuleBindEnum.equipment.name());

            String codeGeneration = (String) FillRuleUtil.executeRule("biz_code_generation", formData);
            if (StringUtils.isEmpty(codeGeneration)) {
                throw new RuntimeException(("获取编码异常！"));
            }
            equipment.setSerialNumber(codeGeneration);
        }

        equipmentService.updateById(equipment);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "设备表-通过id删除")
    @ApiOperation(value = "设备表-通过id删除", notes = "设备表-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        equipmentService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "设备表-批量删除")
    @ApiOperation(value = "设备表-批量删除", notes = "设备表-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        List<String> strings = Arrays.asList(ids.split(","));
        service.removeByIds(strings);
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "设备表-通过id查询")
    @ApiOperation(value = "设备表-通过id查询", notes = "设备表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        Equipment equipment = equipmentService.getById(id);
        if (equipment == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(equipment);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param equipment
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Equipment equipment) {
        return super.exportXls(request, equipment, Equipment.class, "设备表");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Equipment.class);
    }

    @GetMapping(value = "/qr")
    public Result qr(@RequestParam String equipmentId) {
//        获取详细数据
        Equipment byId = equipmentService.getById(equipmentId);
        if (byId == null) {
//            防止生成无效的二维码
            return Result.error("数据不存在！");
        }
        Equipment equipment = new Equipment();
        equipment.setId(byId.getId());
        equipment.setScene("yqsm_wx_device");

//        生成二维码
        String QR_String = null;


        if (logoValid) {
            byte[] bytes = null;
            ClassPathResource classPathResource = new ClassPathResource("static/logo.png");
            try (InputStream inputStream = classPathResource.getInputStream();) {
                bytes = new byte[inputStream.available()];
                inputStream.read(bytes);
            } catch (IOException e) {
                log.error("获取二维码logo图片失败", e.getMessage());
                return Result.error("获取二维码logo图片失败");
            }
            QR_String = QrCodeUtil.generateAsBase64(JSONObject.toJSONString(equipment), new QrConfig(), ImgUtil.IMAGE_TYPE_PNG, bytes);
        } else {
            QR_String = QrCodeUtil.generateAsBase64(JSONObject.toJSONString(equipment), new QrConfig(), ImgUtil.IMAGE_TYPE_PNG);
        }

//        返回数据 base64 encoding
        Result<Object> ok = Result.ok();
        ok.setResult(QR_String);
        return ok;
    }

}
