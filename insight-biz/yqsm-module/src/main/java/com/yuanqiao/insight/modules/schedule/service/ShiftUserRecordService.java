package com.yuanqiao.insight.modules.schedule.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuanqiao.insight.modules.schedule.entity.ShiftUserRecord;
import com.yuanqiao.insight.modules.schedule.vo.ShiftVo;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

public interface ShiftUserRecordService extends IService<ShiftUserRecord> {

    /**
     * 删除大于开始时间小于结束时间的排班记录信息
     *
     * @param shiftId
     * @param startDate
     * @param endDate
     */
    void deleteRecordByDateAndShiftId(String shiftId, Date startDate, Date endDate);

    /**
     * 统计当前人员的值班天数
     *
     * @param userId
     * @param shiftId
     * @param startDate
     * @return
     */
    Integer censusUserSumByUserIdAndShiftId(String userId, String shiftId, Date startDate);

    /**
     * 删除指定排班信息记录
     *
     * @param shiftVo
     */
    void deleteRecords(ShiftVo shiftVo);

    /**
     * 添加排班信息
     *
     * @param shiftVo
     * @throws ParseException
     */
    void addRecords(ShiftVo shiftVo) throws ParseException;

    /**
     * 获取当前时间以后的数据
     *
     * @return
     */
    List<ShiftUserRecord> selectByNow();

    /**
     * 通过时间和人员id查询值班人员记录表
     *
     * @param userId
     * @param dutyDate
     * @return
     */
    List<ShiftUserRecord> selectByUserAndDutyDate(String userId, String dutyDate);

    /**
     * 查询当天的值班人信息
     *
     */
    List<String> selectUsersByNowDutyDate();
}
