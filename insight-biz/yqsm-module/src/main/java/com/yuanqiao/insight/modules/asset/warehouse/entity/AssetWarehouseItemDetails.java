package com.yuanqiao.insight.modules.asset.warehouse.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * @author: yqkj
 * @Date: 2022-06-07
 * @Version: V1.0
 */
@Data
public class AssetWarehouseItemDetails implements Serializable {
    private static final long serialVersionUID = 1L;


    private String innerId;

    /**
     * 单价
     */
    private double innerUnitPrice;

    /**
     * 数量
     */
    private Integer innerCount;

    /**
     * 该批次物品出库的金额
     */
    private Double innerAmount;


}
