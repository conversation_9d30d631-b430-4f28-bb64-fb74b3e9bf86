package com.yuanqiao.insight.modules.quickEntrance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuanqiao.insight.modules.quickEntrance.eneity.QuickEntrance;
import com.yuanqiao.insight.modules.quickEntrance.vo.QuickMenu;

import java.util.List;

public interface QuickEntranceService extends IService<QuickEntrance> {
    void add(String ids);

    List<QuickMenu> queryMenu(Integer platformType);
}
