package com.yuanqiao.insight.modules.customTemplate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanqiao.insight.modules.customTemplate.eneity.CustomTemplate;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface CustomTemplateMapper extends BaseMapper<CustomTemplate> {
    @Select("select id, name, image, show_type, convert_from(template_data, 'UTF8') AS template_data, create_by, create_time, update_by, update_time from yq_biz_custom_template where show_type = #{showType}::varchar")
    List<CustomTemplate> selectCustomTemplates(@Param("showType") String showType);

}
