package com.yuanqiao.insight.mutual.tags.mapper;

import com.alibaba.fastjson.JSONObject;
import com.github.yulichang.base.MPJBaseMapper;
import com.yuanqiao.insight.mutual.tags.entity.UtlTagResource;
import com.yuanqiao.insight.service.device.entity.TagVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public interface UtlTagResourceMapper extends MPJBaseMapper<UtlTagResource> {

    List<Map<String, Object>> countTagsResourceNum();

    List<TagVo> queryTagInfoWithTagResource(@Param("tagKeyList") List<String> tagKeyList);

    List<JSONObject> selectListByResourceTypeAndResourceCategory(@Param("resourceType") String resourceType, @Param("categoryIdName") String categoryIdName, @Param("resourceCategoryIds") String[] resourceCategoryIds);
}
