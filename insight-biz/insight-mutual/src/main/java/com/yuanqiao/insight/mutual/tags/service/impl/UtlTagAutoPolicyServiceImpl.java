package com.yuanqiao.insight.mutual.tags.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.yuanqiao.insight.mutual.tags.entity.UtlTagAutoPolicy;
import com.yuanqiao.insight.mutual.tags.entity.UtlTagAutoPolicyRelated;
import com.yuanqiao.insight.mutual.tags.entity.UtlTagResource;
import com.yuanqiao.insight.mutual.tags.mapper.UtlTagAutoPolicyMapper;
import com.yuanqiao.insight.mutual.tags.mapper.UtlTagResourceMapper;
import com.yuanqiao.insight.mutual.tags.service.IUtlTagAutoPolicyRelatedService;
import com.yuanqiao.insight.mutual.tags.service.IUtlTagAutoPolicyService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 标签自动化策略
 * @Author: jeecg-boot
 * @Date: 2025-04-18
 * @Version: V1.0
 */
@Service
public class UtlTagAutoPolicyServiceImpl extends MPJBaseServiceImpl<UtlTagAutoPolicyMapper, UtlTagAutoPolicy> implements IUtlTagAutoPolicyService {

    @Autowired
    private UtlTagResourceMapper tagsResourceMapper;
    @Autowired
    private IUtlTagAutoPolicyRelatedService utlTagAutoPolicyRelatedService;

    @Override
    public IPage<UtlTagAutoPolicy> pageList(Integer pageNo, Integer pageSize, UtlTagAutoPolicy utlTagAutoPolicy) {
        Page<UtlTagAutoPolicy> page = new Page<UtlTagAutoPolicy>(pageNo, pageSize);
        String policyName = utlTagAutoPolicy.getPolicyName();
        if (StringUtils.isNotEmpty(policyName)) {
            utlTagAutoPolicy.setPolicyName("%" + policyName + "%");
        }
        return baseMapper.selectPageList(page, utlTagAutoPolicy);
    }

    @Override
    public List<UtlTagAutoPolicy> dataList(String isEnable, List<String> tagKeys) {
        UtlTagAutoPolicy utlTagAutoPolicy = new UtlTagAutoPolicy();
        utlTagAutoPolicy.setIsEnable(isEnable);
        utlTagAutoPolicy.setTagKeys(tagKeys);
        IPage<UtlTagAutoPolicy> utlTagAutoPolicyIPage = this.pageList(1, -1, utlTagAutoPolicy);
        return utlTagAutoPolicyIPage.getRecords();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeTags(List<String> ids, Integer isCleanDeviceTags) {
        if (this.removeByIds(ids) && isCleanDeviceTags == 1) {
            tagsResourceMapper.delete(new LambdaQueryWrapper<UtlTagResource>()
                    .in(UtlTagResource::getTagPolicyId, ids));
        }
        utlTagAutoPolicyRelatedService.remove(new LambdaQueryWrapper<UtlTagAutoPolicyRelated>()
                .in(UtlTagAutoPolicyRelated::getPolicyId, ids));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePolicy(UtlTagAutoPolicy utlTagAutoPolicy) {
        boolean save = this.save(utlTagAutoPolicy);
        if (save) {
            String tags = utlTagAutoPolicy.getTags();
            String[] tagKeys = tags.split(",");
            List<UtlTagAutoPolicyRelated> utlTagAutoPolicyRelatedList = new ArrayList<>();
            for (String tagKey : tagKeys) {
                utlTagAutoPolicyRelatedList.add(new UtlTagAutoPolicyRelated(utlTagAutoPolicy.getId(), tagKey));
            }
            utlTagAutoPolicyRelatedService.saveBatch(utlTagAutoPolicyRelatedList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePolicyById(UtlTagAutoPolicy utlTagAutoPolicy) {
        boolean b = this.updateById(utlTagAutoPolicy);
        if (b) {
            utlTagAutoPolicyRelatedService.remove(new LambdaQueryWrapper<UtlTagAutoPolicyRelated>()
                    .eq(UtlTagAutoPolicyRelated::getPolicyId, utlTagAutoPolicy.getId()));
            String tags = utlTagAutoPolicy.getTags();
            String[] tagKeys = tags.split(",");
            List<UtlTagAutoPolicyRelated> utlTagAutoPolicyRelatedList = new ArrayList<>();
            for (String tagKey : tagKeys) {
                utlTagAutoPolicyRelatedList.add(new UtlTagAutoPolicyRelated(utlTagAutoPolicy.getId(), tagKey));
            }
            utlTagAutoPolicyRelatedService.saveBatch(utlTagAutoPolicyRelatedList);
        }
    }
}
