package com.yuanqiao.insight.mutual.tags.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

@Data
@TableName("utl_tag_info")
public class UtlTagInfo implements Serializable {

    /**ID*/
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    @Excel(name = "职务编码", width = 15)
    @ApiModelProperty(value = "职务编码")
    private String pid;

    @Excel(name = "标签Key", width = 15)
    @ApiModelProperty(value = "标签Key")
    private String tagKey;

    @Excel(name = "标签名字", width = 15)
    @ApiModelProperty(value = "标签名字")
    private String tagName;

    @Excel(name = "标签颜色", width = 15)
    @ApiModelProperty(value = "标签颜色")
    private String tagColor;

    @Excel(name = "是否为内置标签", width = 15)
    @ApiModelProperty(value = "是否为内置标签")
    private String isInlay;

    @Excel(name = "是否用作统计标签", width = 15)
    @ApiModelProperty(value = "是否用作统计标签")
    private String isStatistic;

    @Excel(name = "是否为标签组", width = 15)
    @ApiModelProperty(value = "是否为标签组")
    private String isGroupTag;

    @Excel(name = "标签排序", width = 15)
    @ApiModelProperty(value = "标签排序")
    private Integer sort;

    @Excel(name = "标签描述", width = 15)
    @ApiModelProperty(value = "标签描述")
    private String description;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;

    @ApiModelProperty(value = "修改人")
    private String updateBy;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
    private java.util.Date updateTime;

    //当前标签已绑定数量
    @TableField(exist = false)
    private Integer resourceNum;

    //是否参与统计
    @TableField(exist = false)
    private String isStatisText;








}
