package com.yuanqiao.insight.cmdb.modules.extendForm.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.cmdb.modules.assetscategory.entity.AssetsCategory;
import com.yuanqiao.insight.cmdb.modules.assetscategory.service.IAssetsCategoryService;
import com.yuanqiao.insight.cmdb.modules.extendForm.entity.ExtendForm;
import com.yuanqiao.insight.cmdb.modules.extendForm.mapper.ExtendFormMapper;
import com.yuanqiao.insight.cmdb.modules.extendForm.service.IExtendFormService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 附加表单
 * @Author: jeecg-boot
 * @Date: 2021-05-12
 * @Version: V1.0
 */
@Service
@Slf4j
public class ExtendFormServiceImpl extends ServiceImpl<ExtendFormMapper, ExtendForm> implements IExtendFormService {

    @Autowired
    private IAssetsCategoryService assetsCategoryService;
    @Autowired
    private IExtendFormService extendFormService;

    @Override
    public Result importSheets(MultipartFile file) {
        log.info("file", file);
        log.info("com.yuanqiao.insight.cmdb.modules.extendForm.service.impl.importSheets(file={})", file);
        try {
            int count = 2;
            Workbook workbook = new XSSFWorkbook(file.getInputStream());
            Sheet sheet = workbook.getSheet("附加表单");
            int rowCount = sheet.getPhysicalNumberOfRows();
            int cellCount = sheet.getRow(1).getPhysicalNumberOfCells();
            System.out.println("========= " + rowCount + " 行，========= " + cellCount + " 列 ");
            List<ExtendForm> list = new ArrayList<>();
            //根据物理行数，读取每一格内容
            for (int i = count; i < rowCount; i++) {
                ExtendForm extendForm = new ExtendForm();
                //读取每一行内容
                Row rowData = sheet.getRow(i);
                if (rowData.getCell(0) != null) {
                    rowData.getCell(0).setCellType(CellType.STRING);
                    extendForm.setName(rowData.getCell(0).getStringCellValue());
                }
                if (rowData.getCell(1) != null) {
                    rowData.getCell(1).setCellType(CellType.STRING);
                    extendForm.setCode(rowData.getCell(1).getStringCellValue());
                }
                if (rowData.getCell(2) != null) {
                    rowData.getCell(2).setCellType(CellType.STRING);
                    extendForm.setDescription(rowData.getCell(2).getStringCellValue());
                }
                if (rowData.getCell(3) != null) {
                    rowData.getCell(0).setCellType(CellType.STRING);
                    extendForm.setAssetsCategoryId(rowData.getCell(3).getStringCellValue());
                }

                list.add(extendForm);
            }

            if (list.size() == 0) {
                return Result.error("文件不能为空 ");
            }
            QueryWrapper<ExtendForm> queryWrapper = new QueryWrapper<>();
            QueryWrapper<ExtendForm> queryWrapper1 = new QueryWrapper<>();
            for (ExtendForm extendForm : list) {
                count += 1;
                if (StringUtils.isEmpty(extendForm.getName()) || StringUtils.isEmpty(extendForm.getCode())) {
                    return Result.error("第 " + count + " 行的表单名称或表单编码为空！");
                }
                queryWrapper.eq("name", extendForm.getName()).eq("delflag", "0");
                List<ExtendForm> lists = extendFormService.list(queryWrapper);
                if (lists.size() > 0) {
                    return Result.error("第" + count + "行的表单名称重复！");
                }
                queryWrapper1.eq("code", extendForm.getCode()).eq("delflag", "0");
                List<ExtendForm> liste = extendFormService.list(queryWrapper1);
                if (liste.size() > 0) {
                    return Result.error("第" + count + "行的表单编码重复！");
                }
                AssetsCategory assetsCategory = assetsCategoryService.getOne(new QueryWrapper<AssetsCategory>().eq("category_name", extendForm.getAssetsCategoryId()));
                if (assetsCategory == null) {
                    return Result.error("第" + count + "行的资产类型不存在！");
                }
                extendForm.setAssetsCategoryId(assetsCategory.getId());
            }
            super.saveBatch(list);
            return Result.ok("文件导入成功！");
        } catch (IOException e) {
            e.printStackTrace();
            return Result.error("文件导入失败:" + e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                file.getInputStream().close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return Result.error("文件导入失败!");
    }
}
