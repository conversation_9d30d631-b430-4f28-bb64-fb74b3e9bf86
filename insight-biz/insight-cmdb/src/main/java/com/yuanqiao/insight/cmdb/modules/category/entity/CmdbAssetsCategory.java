package com.yuanqiao.insight.cmdb.modules.category.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.IsLikeQueryColumn;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

/**
 * @Description: 资产类型
 * @Author: jeecg-boot
 * @Date:   2021-05-10
 * @Version: V1.0
 */
@Data
@TableName("cmdb_assets_category")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cmdb_assets_category1对象", description="资产类型")
public class CmdbAssetsCategory implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**类型名称*/
	@IsLikeQueryColumn
	@Excel(name = "类型名称", width = 15)
    @ApiModelProperty(value = "类型名称")
    private java.lang.String categoryName;
	/**类型编号*/
	@Excel(name = "类型编号", width = 15)
    @ApiModelProperty(value = "类型编号")
    private java.lang.String categoryCode;
	/**描述*/
	@Excel(name = "描述", width = 15)
    @ApiModelProperty(value = "描述")
    private java.lang.String categoryDescribe;
	/**序号*/
	//@Excel(name = "序号", width = 15)
    @ApiModelProperty(value = "序号")
    private java.lang.Integer categorySerial;
	/**状态（0启用;1未启用）*/
	@Excel(name = "状态（1未启用 0启用）", width = 15, replace = {"启用_0", "未启用_1"})
    @ApiModelProperty(value = "状态（1未启用 0启用）")
    private java.lang.String categoryState;
	/**父级节点*/
	//@Excel(name = "父级节点", width = 15, dictTable = "cmdb_assets_category",dicCode = "id",dicText = "category_name")
    @ApiModelProperty(value = "父级节点")
    private java.lang.String parentId;
	/**是否有子节点*/
	//@Excel(name = "是否有子节点", width = 15, dicCode = "yn")
    @ApiModelProperty(value = "是否有子节点")
    private java.lang.String hasChild;
	/**删除标识*/
	//@Excel(name = "删除标识", width = 15)
    @ApiModelProperty(value = "删除标识")
    private java.lang.Integer delflag;
	/**是否可监控，1：可监控，0：不可监控*/
	@Excel(name = "类型属性（1可监控 0不可监控）", width = 15, replace = {"可监控_1", "不可监控_0"})
    @ApiModelProperty(value = "类型属性（1可监控 0不可监控）")
    private java.lang.Integer isMonitorable;

	@TableField(exist = false)
	private java.lang.String oldParentId,parentName;

}
