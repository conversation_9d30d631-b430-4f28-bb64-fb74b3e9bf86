package com.yuanqiao.insight.cmdb.modules.relation.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.cmdb.modules.assetscategory.util.ExcelUtil;
import com.yuanqiao.insight.cmdb.modules.relation.entity.CmdbRelation;
import com.yuanqiao.insight.cmdb.modules.relation.mapper.CmdbRelationMapper;
import com.yuanqiao.insight.cmdb.modules.relation.service.ICmdbRelationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.system.entity.SysCategory;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * @Description: 关系类型
 * @Author: jeecg-boot
 * @Date:   2021-05-11
 * @Version: V1.0
 */
@Service
@Slf4j
public class CmdbRelationServiceImpl extends ServiceImpl<CmdbRelationMapper, CmdbRelation> implements ICmdbRelationService {

    @Autowired
    private ICmdbRelationService cmdbRelationService;

    @Override
    public Result importSheets(MultipartFile file) {
        log.info("file", file);
        log.info("com.yuanqiao.insight.cmdb.modules.relation.service.impl.importSheets(file={})", file);
        try {
            Workbook hssfWorkbook = ExcelUtil.getWorkBook(file);
            ImportParams params = new ImportParams();
            // 循环工作表Sheet
            SysCategory sysCategory = new SysCategory();
            int count = 2;
            QueryWrapper<CmdbRelation> queryWrapper = new QueryWrapper<>();
            QueryWrapper<CmdbRelation> queryWrapper1 = new QueryWrapper<>();
            for (int numSheet = 0; numSheet < hssfWorkbook.getNumberOfSheets(); numSheet++) {
                params.setTitleRows(1);
                params.setHeadRows(1);
                params.setNeedSave(true);
                List<CmdbRelation> list = ExcelImportUtil.importExcel(file.getInputStream(), CmdbRelation.class, params);
                if(list.size()==0){
                    return Result.error("文件不能为空 ");
                }
                for (CmdbRelation cmdbRelation : list) {
                    if (StringUtils.isEmpty(cmdbRelation.getRelation())||StringUtils.isEmpty(cmdbRelation.getRelationReverse())) {
                        count+=1;
                        return Result.error("文件导入失败,  错误存在第 "+count+" 行!");
                    }

                    queryWrapper.eq("relation",cmdbRelation.getRelation());
                    List<CmdbRelation> list1 = cmdbRelationService.list(queryWrapper);
                    if (list1.size()>0){
                        return Result.error("第"+count+"行关系类型重复!");
                    }

                    queryWrapper1.eq("relation_reverse",cmdbRelation.getRelationReverse());
                    List<CmdbRelation> list2 = cmdbRelationService.list(queryWrapper1);
                    if (list2.size()>0){
                        return Result.error("第"+count+"行反向关系类型重复!");
                    }
                    super.save(cmdbRelation);
                }
            }
            return Result.ok("文件导入成功！");
        } catch (IOException e) {
            return Result.error("文件导入失败:" + e.getMessage());
        } catch (Exception e) {
            return Result.error("文件导入失败:" + e.getMessage());
        } finally {
            try {
                file.getInputStream().close();
            } catch (IOException e) {
                return Result.error("文件导入失败!"+ e.getMessage());
            }
        }

    }
}
