<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.cmdb.modules.assets.mapper.AssetsMapper">

    <update id="upAssetsByIds" parameterType="java.util.List">
        update cmdb_assets set delflag=1 WHERE id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateEntity">

    update cmdb_assets set ${column} = null where id = #{id};

    </update>

    <select id="selectNameById" resultType="java.lang.String">
        select assets_name from cmdb_assets where id = #{assetsId}
    </select>

    <select id="selectAssetsByCondition" resultType="com.yuanqiao.insight.cmdb.modules.assets.entity.Assets">

        select * from cmdb_assets

        <where>
            <if test='categoryIds.size() > 0 and categoryIds != null'>
                and assets_category_id in
                <foreach collection="categoryIds" item="categoryId" open="(" separator="," close=")">
                    #{categoryId}
                </foreach>
            </if>

            <if test='assetsName != ""and assetsName != null'>
                and assets_name like CONCAT('%',#{assetsName},'%')
            </if>

            <if test='producerName != ""and producerName != null'>
                and producer_name like CONCAT('%',#{producerName},'%')
            </if>

            <if test='id != "" and id != null'>
                and id = #{id}
            </if>
            and delflag = 0

        </where>

        order by create_time desc

    </select>


    <select id="countAssetsByCondition" resultType="com.yuanqiao.insight.cmdb.modules.assets.entity.Assets">

        select * from cmdb_assets

        <where>
            <if test='categoryIds.size() > 0 and categoryIds != null'>
                and assets_category_id in
                <foreach collection="categoryIds" item="categoryId" open="(" separator="," close=")">
                    #{categoryId}
                </foreach>
            </if>

            <if test='assetsName != ""and assetsName != null'>
                and assets_name like CONCAT('%',#{assetsName},'%')
            </if>

            <if test='producerName != ""and producerName != null'>
                and producer_name like CONCAT('%',#{producerName},'%')
            </if>

            <if test='id != "" and id != null'>
                and id = #{id}
            </if>
            and delflag = 0

        </where>

    </select>

    <select id="queryByAssetsCode" resultType="com.yuanqiao.insight.cmdb.modules.assets.entity.Assets">

        select * from cmdb_assets where assets_code = #{assetsCode}

    </select>

    <select id="selectLogById" resultType="com.yuanqiao.insight.cmdb.modules.assets.entity.AssetsLog">

        select * from cmdb_log where assets_id = #{assetsId}  order by  create_time desc

    </select>

    <select id="selectAssetsByCategoryId" resultType="com.yuanqiao.insight.cmdb.modules.assets.entity.Assets">
        select * from  cmdb_assets where assets_category_id = #{categoryId}  and delflag = 0
    </select>


    <select id="findAll" resultType="com.yuanqiao.insight.cmdb.modules.assets.entity.Assets">

        select
        ca.*,cac .category_name
        from
        cmdb_assets ca
        left join cmdb_assets_category cac on
        ca.assets_category_id = cac.id
        where
        ca.delflag = 0
        <if test="assetsCategoryId != null and assetsCategoryId.trim()!=''">
            and ca.assets_category_id = #{assetsCategoryId}
        </if>
        <if test="assetsId != null and assetsId.trim()!=''">
            and ca.id != #{assetsId}
        </if>

    </select>


    <select id="cmdbCount" resultType="com.yuanqiao.insight.cmdb.modules.assets.entity.CmdbAssets">

        select
        cac.category_name ,
        count(ca.id) as value1,
        count(maa.id) as value2
        from
        cmdb_assets ca
        left join cmdb_assets_category cac on
        ca.assets_category_id = cac.id
        left join (
        select
        *
        from
        momg_alarm_assets
        where
        alarm_type = '1')as maa on
        ca.assets_code = maa.assets_code
        where
        cac.delflag = 0
        and ca.delflag = 0
        <if test="time1 != null and time2 != null and time1 != '' and time2 != '' ">
            and ca.create_time between #{time1} and #{time2}
        </if>
        group by
        cac.category_name
    </select>

    <select id="cmdbCountByHg" resultType="com.yuanqiao.insight.cmdb.modules.assets.entity.CmdbAssets">
        select
        cac.category_name ,
        count(ca.id) as value1,
        count(maa.id) as value2
        from
        cmdb_assets ca
        left join cmdb_assets_category cac on
        ca.assets_category_id = cac.id
        left join (
        select
        *
        from
        momg_alarm_assets
        where
        alarm_type = '1')as maa on
        ca.assets_code = maa.assets_code
        where
        cac.delflag = 0
        and ca.delflag = 0
        <if test="date1 != null and date2 != null">
            and ca.create_time between #{date1} and #{date2}
        </if>
        group by
        cac.category_name
    </select>

    <select id="expireTop" resultType="com.yuanqiao.insight.cmdb.modules.analysis.entity.ExpireTop">

        select
        maa.assets_name as name ,
        maa.quality_remain as value ,
        '天' as unit
        from
        momg_alarm_assets maa

        <where>
            <if test="time1 != '' and  time2 != '' and time1 != null and time2 !=null ">
                and maa.create_time between #{time1} and #{time2}
            </if>
        </where>

        order by
        maa.quality_remain

    </select>

    <select id="expireTopByHg" resultType="com.yuanqiao.insight.cmdb.modules.analysis.entity.ExpireTop">
        select
        maa.assets_name as name ,
        maa.quality_remain as value ,
        '天' as unit
        from
        momg_alarm_assets maa

        <where>
            <if test="date1 != null and date2 != null">
                and maa.create_time between #{date1} and #{date2}
            </if>
        </where>

        order by
        maa.quality_remain
    </select>

    <select id="assetsCount" resultType="com.yuanqiao.insight.cmdb.modules.assets.entity.Assets">
                select
            count(1)
        from
            cmdb_assets ca
        left join cmdb_assets_category cac on
            ca.assets_category_id = cac .id
        where
            cac.is_monitorable = 1
    </select>


    <select id="assetsExpired" resultType="int">

        select
        count(alarm.id)
        from
        momg_alarm_assets alarm
        left join cmdb_assets ca on
        ca.assets_code = alarm.assets_code
        where
        quality_remain &lt;0

        <if test="time1 != null and time2 != null and time1 != '' and time2 != ''">
            and ca.create_time between #{time1} and #{time2}
        </if>


    </select>

    <select id="assetsExpiredByHg" resultType="int">
        select
        count(alarm.id)
        from
        momg_alarm_assets alarm
        left join cmdb_assets ca on
        ca.assets_code = alarm.assets_code
        where
        quality_remain &lt;0

        <if test="date1 != null and date2 != null">
            and ca.create_time between #{date1} and #{date2}
        </if>
    </select>

    <select id="categoryCount" resultType="com.yuanqiao.insight.cmdb.modules.analysis.entity.ExpireTop">


        select
        cac.category_name as name , (count(ca.id))/( select
        count(ca.id )
        from
        cmdb_assets_category cac
        left join cmdb_assets ca on
        cac.id = ca.assets_category_id
        where
        cac.delflag = 0
        and ca.delflag = 0
        )*100 as value ,'%' as unit
        from
        cmdb_assets_category cac
        left join cmdb_assets ca on
        cac.id = ca.assets_category_id
        where
        cac.delflag = 0
        and ca.delflag = 0
        <if test="time1 != '' and time2 != '' and time1 != null and time2 !=null ">
            and cac.create_time between #{time1} and #{time2}
        </if>
        group by cac.category_name
    </select>


    <select id="findName" resultType="com.yuanqiao.insight.cmdb.modules.assets.entity.Dep">


select
	dep.realname,
	dep.depart_name
from
	cmdb_assets ca
left join (
	select
		sud.dep_id ,
		sud.user_id ,
		su.realname ,
		sd.depart_name
	from
		sys_users su
	right join sys_user_depart sud on
		su.id = sud.user_id
	left join sys_depart sd on
		sud .dep_id = sd.id ) dep on
	ca.department_id = dep.dep_id
	and ca.owner_id = dep.user_id
where ca.id =#{id}
    </select>


    <select id="search4delcheckByConditions" resultType="com.yuanqiao.insight.cmdb.modules.assets.entity.Assets">

        select * from cmdb_assets

        <where>

            <if test='status_id != "" and status_id != null'>
                and status_id = #{status_id}

            </if>

            <if test='producer_id != "" and producer_id != null'>
                and producer_id = #{producer_id}
            </if>

            and delflag = 0

        </where>

    </select>

    <select id="selectByCategoryAndStatus" resultType="com.yuanqiao.insight.cmdb.modules.assets.entity.Assets">
        select ca.*, cac.category_name as assetsCategoryText from cmdb_assets ca
        left join cmdb_assets_category cac on ca.assets_category_id = cac.id
        <where>
            <if test='categoryIdList.size() > 0 and categoryIdList != null'>
                and ca.assets_category_id in
                <foreach collection="categoryIdList" item="categoryId" open="(" separator="," close=")">
                    #{categoryId}
                </foreach>
            </if>

            <if test='cmdbStatusId != "" and cmdbStatusId != null'>
                and ca.status_id = #{cmdbStatusId}
            </if>
            and ca.delflag = 0
        </where>
        order by ca.create_time desc

    </select>
    <select id="getAccestInfo" resultType="com.yuanqiao.insight.cmdb.modules.assets.entity.AccestInfo">
        SELECT
        assets_name,
        cac.category_name AS assetsCategoryName,
        assets_code,
        producer_name,
        assets_model,
        su.realname AS userName,
        sd.depart_name AS userDepartName,
        start_quality_time,
        quality_term,
        mdi.device_code as deviceCode,
        mdi.name as deviceName,
        mdi.ip as deviceIp
        FROM
        cmdb_assets AS ca
        LEFT JOIN cmdb_assets_category AS cac ON ca.assets_category_id = cac.id
        LEFT JOIN sys_users as su ON ca.owner_id = su.id
        LEFT JOIN sys_depart as sd ON ca.department_id = sd.id
        LEFT JOIN momg_device2assets md2a ON ca.id = md2a.assets_id
        LEFT JOIN momg_device_info mdi ON mdi.id = md2a.device_id
        <where>
            <if test=' startTime != "" and startTime != null'>
                and ca.storage_time &gt;= #{startTime}
            </if>
            <if test=' endTime != "" and endTime != null'>
                and ca.storage_time &lt; #{endTime}
            </if>
            and ca.delflag = 0
        </where>

    </select>
    <select id="getprocessNum" resultType="java.lang.Integer">
        SELECT count(*) from  act_z_business
    </select>
    <select id="getPendingTasks" resultType="java.lang.Integer">
        SELECT count(*) from  act_z_business WHERE result IN (#{a},#{b})
    </select>
    <select id="selectNumbers" resultType="java.lang.String">
        select assets_code from cmdb_assets where assets_code like CONCAT('%',#{middle},'%')
    </select>
    <select id="assetsName" resultType="java.lang.String">
        select assets_name from cmdb_assets
    </select>
    <select id="assetsCode" resultType="java.lang.String">
        select assets_code from cmdb_assets
    </select>

    <select id="getAssetsNameNum" resultType="java.lang.Integer">
        select count(*) from cmdb_assets where assets_name in (select assets_name from ${tableName});
    </select>

    <update id="updateAssetsStatusByIds" >
        update cmdb_assets set status_name=#{statusName},status_id=#{statusId} WHERE id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
