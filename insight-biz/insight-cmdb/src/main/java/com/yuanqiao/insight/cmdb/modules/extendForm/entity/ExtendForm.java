package com.yuanqiao.insight.cmdb.modules.extendForm.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecg.common.aspect.annotation.IsLikeQueryColumn;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description:  附加表单
 * @Author: jeecg-boot
 * @Date:   2021-05-12
 * @Version: V1.0
 */
@Data
@TableName("cmdb_extend_form")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="extend_form对象", description=" 附加表单")
public class ExtendForm implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**表单名称*/
	@IsLikeQueryColumn
	@Excel(name = "表单名称", width = 15)
    @ApiModelProperty(value = "表单名称")
    private String name;



    /**表单编码*/
    @Excel(name = "表单编码", width = 15)
    @ApiModelProperty(value = "表单编码")
    private String code;


    /**关联的资产类型id*/
    @Excel(name = "资产类型", width = 15, dictTable = "cmdb_assets_category",dicCode = "id",dicText = "category_name" )
    @ApiModelProperty(value = "资产类型")
    private String assetsCategoryId;

    /**描述*/
    @Excel(name = "描述", width = 15)
    @ApiModelProperty(value = "描述")
    private String description;

    /**删除标记, 0:未删除, 1:已删除*/
    //@Excel(name = "删除标记, 0:未删除, 1:已删除", width = 15)
    @Dict(dicCode = "delflag")
    @ApiModelProperty(value = "删除标记, 0:未删除, 1:已删除")
    private Integer delflag;

	@TableField(exist = false)
	private String categoryName;
}
