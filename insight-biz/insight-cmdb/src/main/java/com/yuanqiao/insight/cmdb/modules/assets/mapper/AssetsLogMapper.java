package com.yuanqiao.insight.cmdb.modules.assets.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanqiao.insight.cmdb.modules.assets.entity.AssetsCount;
import com.yuanqiao.insight.cmdb.modules.assets.entity.AssetsLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Mapper
@Component
public interface AssetsLogMapper extends BaseMapper<AssetsLog> {

    int assetsConfingMont(@Param("preDay") Date preDay);
    int assetsConfing(@Param("startDate") Date startDate, @Param("endDate") Date endDate);
    int stAssetsConfingMont();

    List<AssetsCount> getAssets(@Param("times") String times);

    List<AssetsCount> getAssets2(@Param("times") String times);
}
