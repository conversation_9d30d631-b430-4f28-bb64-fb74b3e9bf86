package com.yuanqiao.insight.cmdb.modules.extendValue.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanqiao.insight.cmdb.modules.extendValue.entity.ExtendValue;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 附加字段的值
 * @Author: jeecg-boot
 * @Date: 2021-05-13
 * @Version: V1.0
 */
@Mapper
@Component
public interface ExtendValueMapper extends BaseMapper<ExtendValue> {
    void delByAssId(@Param("id") String id);

    List<ExtendValue> findByExtendFieldName(@Param("name") String name);

    void delByAssIds(@Param("asList") List<String> asList);

    List<ExtendValue> selectByCategoryAndAsset(@Param("assetsCategoryId") String assetsCategoryId, @Param("assetsId") String assetsId);

    ExtendValue findByFieldIdAndAssetId(@Param("fieldId") String fieldId, @Param("assetsId") String assetsId);

    List<ExtendValue> findByFieldIdAndValue(@Param("fieldId") String fieldId, @Param("value") String value);

    List<ExtendValue> findByFieldIdAndAssetIdAndValue(@Param("fieldId") String fieldId, @Param("assetsId") String assetsId, @Param("value") String value);

    List<ExtendValue> selectByCategoryAndAssetByPostgresql(@Param("assetsCategoryId") String assetsCategoryId, @Param("assetsId") String assetsId);

}
