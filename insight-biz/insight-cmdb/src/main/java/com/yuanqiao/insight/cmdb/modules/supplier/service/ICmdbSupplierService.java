package com.yuanqiao.insight.cmdb.modules.supplier.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.yuanqiao.insight.cmdb.modules.supplier.entity.CmdbSupplier;
import org.jeecg.common.api.vo.Result;
import org.springframework.web.multipart.MultipartFile;

/**
 * @Description: 供应商
 * @Author: jeecg-boot
 * @Date:   2021-05-10
 * @Version: V1.0
 */
public interface ICmdbSupplierService extends IService<CmdbSupplier> {

    public Result importSheets(MultipartFile file);
}
