package com.yuanqiao.insight.cmdb.modules.supplier.controller;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.cmdb.modules.assets.entity.Assets;
import com.yuanqiao.insight.cmdb.modules.assets.service.IAssetsService;
import com.yuanqiao.insight.cmdb.modules.assetscategory.util.ExcelUtil;
import com.yuanqiao.insight.cmdb.modules.supplier.entity.CmdbSupplier;
import com.yuanqiao.insight.cmdb.modules.supplier.service.ICmdbSupplierService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.umpPwdManage.entity.UmpPwdManage;
import org.jeecg.modules.umpPwdManage.service.IUmpPwdManageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.transaction.Transactional;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * @Description: 供应商
 * @Author: jeecg-boot
 * @Date:   2021-05-10
 * @Version: V1.0
 */
@Api(tags="供应商")
@RestController
@RequestMapping("/supplier/cmdbSupplier")
@Slf4j
public class CmdbSupplierController extends JeecgController<CmdbSupplier, ICmdbSupplierService> {
	@Autowired
	private ICmdbSupplierService cmdbSupplierService;
	@Autowired
	private IUmpPwdManageService umpPwdManageService;
	@Autowired
	IAssetsService assetsService;
	@Value("${excel.supplierPath}")
	private String path;
	/**
	 * 分页列表查询
	 *
	 * @param cmdbSupplier
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "供应商-分页列表查询")
	@ApiOperation(value="供应商-分页列表查询", notes="供应商-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(CmdbSupplier cmdbSupplier,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		log.info("cmdbSupplier={}" ,cmdbSupplier);
		log.info("com.yuanqiao.insight.cmdb.modules.supplier.controller.queryPageList(status={})", cmdbSupplier);
		String name = cmdbSupplier.getName();
		if (StringUtils.isNotBlank(name)){
			cmdbSupplier.setName(null);
		}
		QueryWrapper<CmdbSupplier> queryWrapper = QueryGenerator.initQueryWrapper(cmdbSupplier, req.getParameterMap());
		if (StringUtils.isNotBlank(name)){
			queryWrapper.like("name",name);
		}
		Page<CmdbSupplier> page = new Page<CmdbSupplier>(pageNo, pageSize);
		IPage<CmdbSupplier> pageList = cmdbSupplierService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 * 查询所有未删除的供应商
	 *
	 * @return
	 */
//	@AutoLog(value = "供应商-查询所有未删除的供应商")
	@ApiOperation(value="供应商-查询所有未删除的供应商", notes="供应商-查询所有未删除的供应商")
	@GetMapping(value = "/queryAllSupplier")
	public Result<?> queryAllSuppliler() {
		List<CmdbSupplier> supplierList = cmdbSupplierService.list(new QueryWrapper<CmdbSupplier>().eq("delflag", 0));
		if(CollUtil.isNotEmpty(supplierList)) {
			return Result.OK(supplierList);
		} else {
			return Result.OK(new ArrayList<CmdbSupplier>());
		}
	}

	/**
	 *   添加
	 *
	 * @param cmdbSupplier
	 * @return
	 */
	@AutoLog(value = "供应商-添加")
	@ApiOperation(value="供应商-添加", notes="供应商-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody CmdbSupplier cmdbSupplier) {
		log.info("cmdbSupplier={}" ,cmdbSupplier);
		log.info("com.yuanqiao.insight.cmdb.modules.supplier.controller.add(cmdbSupplier={})", cmdbSupplier);
		QueryWrapper<CmdbSupplier> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("name",cmdbSupplier.getName());
		List<CmdbSupplier> list = cmdbSupplierService.list(queryWrapper);
		if(list.size()>0){
			return Result.error("供应商名称重复！");
		}
		cmdbSupplierService.save(cmdbSupplier);
 		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param cmdbSupplier
	 * @return
	 */
	@AutoLog(value = "供应商-编辑")
	@ApiOperation(value="供应商-编辑", notes="供应商-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody CmdbSupplier cmdbSupplier) {
		log.info("cmdbSupplier={}" ,cmdbSupplier);
		log.info("com.yuanqiao.insight.cmdb.modules.supplier.controller.edit(cmdbSupplier={})", cmdbSupplier);
		List<CmdbSupplier> list = cmdbSupplierService.list();
		Iterator<CmdbSupplier> iterator = list.iterator();
		while (iterator.hasNext()){
			CmdbSupplier next = iterator.next();
			if(next.getId().equals(cmdbSupplier.getId())){
				iterator.remove();
			}
		}
		for (CmdbSupplier supplier : list) {
			if (supplier.getName().equals(cmdbSupplier.getName())){
				return Result.error("供应商名称重复！");
			}
		}
		cmdbSupplierService.updateById(cmdbSupplier);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "供应商-通过id删除")
	@ApiOperation(value="供应商-通过id删除", notes="供应商-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		log.info("id={}" ,id);
		log.info("com.yuanqiao.insight.cmdb.modules.supplier.controller.delete(id={})", id);

		CmdbSupplier cmdbSupplier = cmdbSupplierService.getById(id);
		if (null == cmdbSupplier){
			return Result.error("未找到该供应商");
		}

		List<Assets> assetsList = assetsService.search4delcheck(null, cmdbSupplier.getId());
		if (!CollectionUtils.isEmpty(assetsList)){
			return Result.error("供应商【"+cmdbSupplier.getName()+"】已应用资产数据，不可删除！");
		}

		cmdbSupplierService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "供应商-批量删除")
	@ApiOperation(value="供应商-批量删除", notes="供应商-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		log.info("ids={}" ,Arrays.asList(ids.split(",")));
		log.info("com.yuanqiao.insight.cmdb.modules.supplier.controller.deleteBatch(ids={})", Arrays.asList(ids.split(",")));

		List<String> idss = Arrays.asList(ids.split(","));
		for (String id : idss) {
			CmdbSupplier cmdbSupplier = cmdbSupplierService.getById(id);
			if (null == cmdbSupplier){
				return Result.error("未找到该供应商");
			}
			List<Assets> assetsList = assetsService.search4delcheck(null, cmdbSupplier.getId());
			if (!CollectionUtils.isEmpty(assetsList)){
				return Result.error("供应商【"+cmdbSupplier.getName()+"】已应用资产数据，不可删除！");
			}
		}

		this.cmdbSupplierService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "供应商-通过id查询")
	@ApiOperation(value="供应商-通过id查询", notes="供应商-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		CmdbSupplier cmdbSupplier = cmdbSupplierService.getById(id);
		log.info("id={}" ,id);
		log.info("com.yuanqiao.insight.cmdb.modules.supplier.controller.queryById(cmdbSupplier={})", cmdbSupplier);
		if(cmdbSupplier==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(cmdbSupplier);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param cmdbSupplier
    */
    @AutoLog(value = "供应商-导出")
    @RequestMapping(value = "/exportXls")
	public void exportXls(HttpServletRequest request,HttpServletResponse response, CmdbSupplier cmdbSupplier) {
		cmdbSupplier.setDelflag(0);
		log.info("com.yuanqiao.insight.cmdb.modules.supplier.controller.exportXls(cmdbSupplier={})", cmdbSupplier);
		UmpPwdManage zip = umpPwdManageService.getZip();
		super.exportXlsZip(request,response, cmdbSupplier, CmdbSupplier.class, "供应商报表",zip.getZipPwd(),zip.getIsEncry());
	}
	/**
	 * 模板导出
	 *
	 * @param
	 * @param
	 * @return
	 */
	@AutoLog(value = "供应商-模板下载")
	@GetMapping(value = "/downloadTemplate")
	@ApiOperation("模板导出")
	public void downloadTemplate(HttpServletResponse response, HttpServletRequest request) {
		try {
			InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(path);
			String str1=path.substring(0, path.indexOf("."));
			String str2=path.substring(str1.length());
			ExcelUtil.downloadPoi(inputStream,response,"供应商导入模板"+str2);
		} catch (IOException e) {
			log.info("导入失败"+e.getMessage());
			e.printStackTrace();
		}
	}
    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
//    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
//    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
//        return super.importExcel(request, response, CmdbSupplier.class);
//    }
	@AutoLog(value = "供应商-导入")
	@RequestMapping(value = "/importExcel", method = RequestMethod.POST)
	@Transactional
	public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
		MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
		Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
		for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
			MultipartFile file = entity.getValue();// 获取上传文件对象
			return cmdbSupplierService.importSheets(file);
		}
		return Result.error("文件导入失败！");
	}

}
