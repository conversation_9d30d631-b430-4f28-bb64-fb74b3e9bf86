package com.yuanqiao.insight.cmdb.modules.contract.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.cmdb.modules.assets.entity.Assets;
import com.yuanqiao.insight.cmdb.modules.contract.entity.ContractEntity;
import com.yuanqiao.insight.cmdb.modules.contract.mapper.ContractMapper;
import com.yuanqiao.insight.cmdb.modules.contract.service.IContractService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class ContractServiceImpl extends ServiceImpl<ContractMapper, ContractEntity>
        implements IContractService {
    @Autowired
    ContractMapper contractMapper;
    @Override
    public IPage<ContractEntity> queryPage(Page<ContractEntity> page, String contractName, String contractType) {
        LambdaQueryWrapper<ContractEntity> qw = new LambdaQueryWrapper<ContractEntity>();
        if (StringUtils.isNotBlank(contractName)){
            qw.like(ContractEntity::getName,contractName);
        }
        if (StringUtils.isNotBlank(contractType)){
            qw.eq(ContractEntity::getType,contractType);
        }
        qw.eq(ContractEntity::getDelflag,0);
        qw.orderByDesc(ContractEntity::getUpdateTime);
        return this.page(page,qw);
    }

    @Override
    public List<ContractEntity> listAll(String contractName) {
        LambdaQueryWrapper<ContractEntity> qw = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(contractName)){
            qw.like(ContractEntity::getName,contractName);
        }
        qw.eq(ContractEntity::getDelflag,0);
        qw.select(ContractEntity::getId,ContractEntity::getName);
        qw.orderByDesc(ContractEntity::getUpdateTime);
        List<ContractEntity> list = this.list(qw);
        return list;
    }

    @Override
    public List<String> getIdsByLikeContractName(String contractName){
        List<String> ids = contractMapper.getIdsByLikeContractName(contractName);
        return ids;
    }

    @Override
    public ContractEntity getByIdAndNotDeleted(String contractId) {
        LambdaQueryWrapper<ContractEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(ContractEntity::getId,contractId);
        qw.eq(ContractEntity::getDelflag,0);
        ContractEntity contract = this.getOne(qw);
        return contract;
    }


    public void setContractNameForAssets(Assets assets){
        if (StringUtils.isNotBlank(assets.getContractId())){
            ContractEntity contract = this.getByIdAndNotDeleted(assets.getContractId());
            if (contract == null){
                assets.setContractName("--请检查合同是否已删除--");
            }else {
                assets.setContractName(contract.getName());
            }
        }
    }
}
