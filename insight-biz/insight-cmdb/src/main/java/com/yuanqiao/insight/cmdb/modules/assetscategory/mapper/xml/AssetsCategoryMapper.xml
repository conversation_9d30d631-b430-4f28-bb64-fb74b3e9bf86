<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.cmdb.modules.assetscategory.mapper.AssetsCategoryMapper">

    <update id="updateTreeNodeStatus" parameterType="java.lang.String">
		update cmdb_assets_category set has_child = #{status} where id = #{id}
	</update>

    <update id="updateDelflagByIds" parameterType="java.util.List">
        update cmdb_assets_category set delflag=1 WHERE id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectByParentId" resultType="com.yuanqiao.insight.cmdb.modules.assetscategory.entity.AssetsCategory">

        select * from cmdb_assets_category where parent_id = #{parentId} and delflag = 0 order by category_serial asc,create_time desc

    </select>

    <select id="selectByDeviceId" resultType="com.yuanqiao.insight.cmdb.modules.assetscategory.entity.AssetsCategory">

        select * from cmdb_assets_category a

        where a.id = (select b.assets_category_id from cmdb_assets b

        where b.id = (select c.assets_id from momg_device_info c where c.id = #{deviceId}))

    </select>

    <select id="selectByCategoryode" resultType="com.yuanqiao.insight.cmdb.modules.assetscategory.entity.AssetsCategory">

        select * from cmdb_assets_category a

        where a.category_code = #{code}

    </select>


    <select id="selectProductForTree" resultType="com.yuanqiao.insight.service.product.entity.Product">

        select * from momg_product where assets_category_id = #{assetsCategoryId}

    </select>

    <select id="selectAssetsCategoryForTree"
            resultType="com.yuanqiao.insight.cmdb.modules.assetscategory.entity.AssetsCategory">

        select * from cmdb_assets_category where delflag = 0 and is_monitorable = 1 and parent_id = '0' order by category_serial, create_time desc

    </select>

    <select id="selectStatusByCategoryId" resultType="java.lang.String">

      select name from cmdb_status where assets_category_id = #{assetsCategoryId}

    </select>

    <select id="selectAll" resultType="com.yuanqiao.insight.cmdb.modules.assetscategory.entity.AssetsCategory">

        select * from cmdb_assets_category
        <where>
            parent_id in ('0','',null) and
            delflag = 0
            <if test="categoryName != null and categoryName != ''">
                and category_name like CONCAT('%',#{categoryName},'%')
            </if>
            <if test="isMonitorable != null">
                and is_monitorable = ${isMonitorable}
            </if>

            order by category_serial asc,create_time desc

        </where>

    </select>

    <select id="countAll" resultType="java.lang.Integer">

        select count(*) from cmdb_assets_category
        <where>
            parent_id in ('0','',null)

            and delflag = 0

            <if test="categoryName != null and categoryName != ''">
                and category_name like CONCAT('%',#{categoryName},'%')
            </if>
            <if test="isMonitorable != null">
                and is_monitorable = ${isMonitorable}
            </if>

        </where>

    </select>


    <select id="cmdbCategoryDevice" resultType="java.lang.Integer">
        select
        count(distinct mdi.id)
        from
        cmdb_assets_category cac
        left join momg_product mp on
        cac.id = mp.assets_category_id
        left join momg_device_info mdi on
        mp.id = mdi.product_id
        left join momg_alarm_history mah on
        mdi.id = mah.device_id
        where
        cac.is_monitorable = '1'
        and cac.delflag = 0
        and mah.delflag =0
        and mah.alarm_status = '0'
        and mah.confirm_status = '0'
        <if test="alarmStatus != null">
            and mdi.alarm_status = #{alarmStatus}
        </if>
        <if test="level != null">
            and mah .alarm_level = #{level}
        </if>
        and cac.id = #{id}

    </select>


    <select id="cmdbCategoryDeviceCount" resultType="java.lang.Integer">

        select
        count(distinct mdi.id)
        from
        cmdb_assets_category cac
        left join momg_product mp on
        cac.id = mp.assets_category_id
        left join momg_device_info mdi on
        mp.id = mdi.product_id
        where
        cac.is_monitorable = '1'
        and cac.delflag = 0
        and mdi.delflag =0
        <if test="status != null">
            and mdi.status = #{status}
        </if>
        and cac.id = #{id}
    </select>

    <select id="duplicateCheck" resultType="com.yuanqiao.insight.cmdb.modules.assetscategory.entity.AssetsCategory">

        select * from cmdb_assets_category where delflag = 0 and (category_name = #{categoryName} or category_code = #{categoryCode} or category_serial = #{categorySerial})

    </select>

    <select id="selectByParentIds" resultType="com.yuanqiao.insight.cmdb.modules.assetscategory.entity.AssetsCategory">

        select * from cmdb_assets_category where parent_id in

        <foreach collection="parentIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>

         and delflag = 0 order by category_serial asc,create_time desc

    </select>
    <select id="getChiledList"
            resultType="com.yuanqiao.insight.cmdb.modules.assetscategory.entity.AssetsTypeTree">
select * from cmdb_assets_category where delflag = 0 and parent_id =#{parentId}
    </select>
    <select id="getList" resultType="com.yuanqiao.insight.cmdb.modules.assetscategory.entity.AssetsTypeTree">
        select id,category_name,category_code,category_describe,parent_id,has_child from cmdb_assets_category
        <where>
            parent_id in ('0','',null) and
            delflag = 0
            <if test="accestName != null and accestName != ''">
                and category_name like CONCAT('%',#{accestName},'%')
            </if>

        </where>
    </select>
    <select id="cmdbCategoryEnableDevice" resultType="java.lang.Integer">
        select
        count(distinct mdi.id)
        from
        cmdb_assets_category cac
        left join momg_product mp on
        cac.id = mp.assets_category_id
        left join momg_device_info mdi on
        mp.id = mdi.product_id
        where
        cac.is_monitorable = '1'
        and cac.delflag = 0
        and mdi.delflag =0
        <if test="enable != null">
            and mdi.enable = #{enable}
        </if>
        and cac.id = #{id}
    </select>


</mapper>
