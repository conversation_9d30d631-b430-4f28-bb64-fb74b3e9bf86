package com.yuanqiao.insight.cmdb.modules.extendValue.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 附加字段的值
 * @Author: jeecg-boot
 * @Date:   2021-05-13
 * @Version: V1.0
 */
@Data
@TableName("cmdb_extend_value")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="extend_value对象", description="附加字段的值")
public class ExtendValue implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**资产id*/
	@Excel(name = "资产id", width = 15)
    @ApiModelProperty(value = "资产id")
    private String assetsId;
	/**字段名称*/
	@Excel(name = "字段名称", width = 15)
    @ApiModelProperty(value = "字段名称")
    private String name;
	/**字段类型*/
	@Excel(name = "字段类型", width = 15)
    @ApiModelProperty(value = "字段类型")
    private String type;
	/**字段描述*/
	@Excel(name = "字段描述", width = 15)
    @ApiModelProperty(value = "字段描述")
    private String description;
	/**字段的值*/
	@Excel(name = "字段的值", width = 15)
    @ApiModelProperty(value = "字段的值")
    private String value;
	/** 删除标记, 0:未删除, 1:已删除*/
	@Excel(name = " 删除标记, 0:未删除, 1:已删除", width = 15)
    @ApiModelProperty(value = " 删除标记, 0:未删除, 1:已删除")
    private Integer delflag;
    /**附加字段id*/
    @Excel(name = "附加字段id", width = 15)
    @ApiModelProperty(value = "附加字段id")
    private String fieldId;
    /**
     * 导出标记, 0:否, 1:是
     */
    @TableField(exist = false)
    private Integer isExport;
    /**
     * 前台展示标记, 0:否, 1:是
     */
    @TableField(exist = false)
    private Integer isShow;
    /**
     * 查询条件标记, 0:否, 1:是
     */
    @TableField(exist = false)
    private Integer isQuery;
    /**
     * 必填标记, 0:否, 1:是
     */
    @TableField(exist = false)
    private Integer isInput;

    @TableField(exist = false)
    private String dictType;
}
