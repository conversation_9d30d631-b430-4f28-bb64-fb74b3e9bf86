package com.yuanqiao.insight.cmdb.modules.status.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.cmdb.modules.assetscategory.entity.AssetsCategory;
import com.yuanqiao.insight.cmdb.modules.assetscategory.service.IAssetsCategoryService;
import com.yuanqiao.insight.cmdb.modules.assetscategory.util.ExcelUtil;
import com.yuanqiao.insight.cmdb.modules.status.entity.CmdbStatus;
import com.yuanqiao.insight.cmdb.modules.status.mapper.CmdbStatusMapper;
import com.yuanqiao.insight.cmdb.modules.status.service.ICmdbStatusService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.system.entity.SysCategory;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 资产状态
 * @Author: jeecg-boot
 * @Date: 2021-05-11
 * @Version: V1.0
 */
@Service
@Slf4j
public class CmdbStatusServiceImpl extends ServiceImpl<CmdbStatusMapper, CmdbStatus> implements ICmdbStatusService {

    @Autowired
    private IAssetsCategoryService assetsCategoryService;
    @Autowired
    private ICmdbStatusService cmdbStatusService;

    @Override
    public void saveAssetsId(CmdbStatus status, String ids) {
        log.info("com.yuanqiao.insight.cmdb.modules.status.service.impl.saveAssetsId(ids={})", ids);
        log.info("com.yuanqiao.insight.cmdb.modules.status.service.impl.saveAssetsId(status={})", status);
        ArrayList<String> list = new ArrayList<>();
        if (ids == null || ids == "") {
            status.setAssetsCategoryId(null);
            this.updateById(status);
        } else {
            String[] split = ids.split(",");
            for (int i = 0; i < split.length; i++) {
                list.add(split[i]);
            }
            status.setAssetsCategoryId(list.toString());
            this.updateById(status);
        }
    }

    @Override
    public Result importSheets(MultipartFile file) {
        log.info("com.yuanqiao.insight.cmdb.modules.status.service.impl.importSheets(file={})", file);
        try {
            Workbook hssfWorkbook = ExcelUtil.getWorkBook(file);
            ImportParams params = new ImportParams();
            // 循环工作表Sheet
            SysCategory sysCategory = new SysCategory();

            List<AssetsCategory> categoryList = assetsCategoryService.list();
            ArrayList<String> list1 = new ArrayList<>();
            int count = 2;
            for (int numSheet = 0; numSheet < hssfWorkbook.getNumberOfSheets(); numSheet++) {
                params.setTitleRows(1);
                params.setHeadRows(1);
                params.setNeedSave(true);
                List<CmdbStatus> list = ExcelImportUtil.importExcel(file.getInputStream(), CmdbStatus.class, params);
                if (list.size() == 0) {
                    return Result.error("文件不能为空 ");
                }
                for (CmdbStatus cmdbStatus : list) {
                    count += 1;
                    if (StringUtils.isEmpty(cmdbStatus.getName()) || StringUtils.isEmpty(cmdbStatus.getCode())) {
                        return Result.error("文件导入失败,错误出现在第 " + count + " 行");
                    }

                    QueryWrapper<CmdbStatus> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("name",cmdbStatus.getName());
                    List<CmdbStatus> lists = cmdbStatusService.list(queryWrapper);
                    if(lists.size()>0){
                        return Result.error("状态名称重复!");
                    }
                    QueryWrapper<CmdbStatus> queryWrapper1 = new QueryWrapper<>();
                    queryWrapper1.eq("code",cmdbStatus.getCode());
                    List<CmdbStatus> list2 = cmdbStatusService.list(queryWrapper1);
                    if(list2.size()>0){
                        return Result.error("状态编码重复!");
                    }
                    cmdbStatus.setAssetsCategoryId(list1.toString());
                    cmdbStatus.setDelflag("0");
                    super.save(cmdbStatus);
                }
            }
            return Result.ok("文件导入成功！");
        } catch (IOException e) {
            e.printStackTrace();
            return Result.error("文件导入失败:" + e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("文件导入失败:" + e.getMessage());
        } finally {
            try {
                file.getInputStream().close();
            } catch (IOException e) {
                e.printStackTrace();
                return Result.error("文件导入失败:" + e.getMessage());
            }
        }

    }


}
