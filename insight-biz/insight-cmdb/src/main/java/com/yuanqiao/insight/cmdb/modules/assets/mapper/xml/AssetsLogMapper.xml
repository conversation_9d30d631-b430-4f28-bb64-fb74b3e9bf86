<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.cmdb.modules.assets.mapper.AssetsLogMapper">


    <select id="assetsConfingMont" resultType="java.lang.Integer">
        SELECT
        count(id)
        FROM
        cmdb_log
        WHERE
        create_time >= #{preDay}
        AND
        assets_config = '1'
    </select>

    <select id="assetsConfing" resultType="java.lang.Integer">
        SELECT
            count(id)
        FROM
            cmdb_log
        WHERE
            assets_config = '1'
        <if test="startDate != null and endDate != null">
            AND create_time between #{startDate} and #{endDate}
        </if>
    </select>

    <!-- 神通数据库 -->
    <select id="stAssetsConfingMont" resultType="java.lang.Integer">
        SELECT
        count(id)
        FROM
        cmdb_log
        WHERE
        dateadd(DAY, -30, now()) &lt;= create_time
        AND
        assets_config = '1'
    </select>

    <select id="getAssets" resultType="com.yuanqiao.insight.cmdb.modules.assets.entity.AssetsCount">
                select
            date_format( create_time, '%Y-%m-%d' ) as `name`,
            count(*) as counts
        from
            cmdb_log
        where
            assets_config = '1'
            and create_time like #{times}
            group by date_format( create_time, '%Y-%m-%d' )
    </select>

    <select id="getAssets2" resultType="com.yuanqiao.insight.cmdb.modules.assets.entity.AssetsCount">
           select
        date_format( create_time, '%Y-%m-%d' ) as `name`,
        count(*) as counts
    from
        cmdb_log
    where
        assets_config = '2'
        and create_time like
        #{times} group by  date_format( create_time, '%Y-%m-%d' )

    </select>
</mapper>
