package com.yuanqiao.insight.cmdb.modules.extendField.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.cmdb.modules.extendField.entity.ExtendField;
import com.yuanqiao.insight.cmdb.modules.extendField.mapper.ExtendFieldMapper;
import com.yuanqiao.insight.cmdb.modules.extendField.service.IExtendFieldService;
import com.yuanqiao.insight.cmdb.modules.extendValue.entity.ExtendValue;
import com.yuanqiao.insight.cmdb.modules.extendValue.mapper.ExtendValueMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 附加表单的字段
 * @Author: jeecg-boot
 * @Date: 2021-05-13
 * @Version: V1.0
 */
@Service
public class ExtendFieldServiceImpl extends ServiceImpl<ExtendFieldMapper, ExtendField> implements IExtendFieldService {
    @Autowired
    ExtendFieldMapper extendFieldMapper;
    @Autowired
    ExtendValueMapper extendValueMapper;

    @Override
    public List<ExtendField> selectByCategoryAndAsset(String assetsCategoryId, String assetsId) {
        List<ExtendField> extendFieldList = extendFieldMapper.findCodeName(assetsCategoryId);
        for (ExtendField extendField : extendFieldList) {
            ExtendValue extendValue = extendValueMapper.findByFieldIdAndAssetId(extendField.getId(), assetsId);
            if (extendValue != null) {
                extendField.setValue(extendValue.getValue());
            }
        }
        return extendFieldList;
    }

    @Override
    public List<ExtendField> findCodeNameBatch(String assetsCategoryId) {
        return extendFieldMapper.findCodeNameBatch(assetsCategoryId);
    }
}
