package com.yuanqiao.insight.cmdb.modules.extendField.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.cmdb.modules.assets.mapper.AssetsMapper;
import com.yuanqiao.insight.cmdb.modules.extendField.entity.ExtendField;
import com.yuanqiao.insight.cmdb.modules.extendField.mapper.ExtendFieldMapper;
import com.yuanqiao.insight.cmdb.modules.extendField.service.IExtendFieldService;
import com.yuanqiao.insight.cmdb.modules.extendValue.entity.ExtendValue;
import com.yuanqiao.insight.cmdb.modules.extendValue.mapper.ExtendValueMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.DictModel;
import com.yuanqiao.insight.acore.system.service.ISysDictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 附加表单的字段
 * @Author: jeecg-boot
 * @Date: 2021-05-13
 * @Version: V1.0
 */
@Api(tags = "附加表单的字段")
@RestController
@RequestMapping("/extendField/extendField")
@Slf4j
public class ExtendFieldController extends JeecgController<ExtendField, IExtendFieldService> {
    @Autowired
    private IExtendFieldService extendFieldService;
    @Autowired
    private ExtendFieldMapper extendFieldMapper;
    @Autowired
    private ExtendValueMapper extendValueMapper;
    @Autowired
    private AssetsMapper assetsMapper;
    @Autowired
    private ISysDictService dictService;

    /**
     * 分页列表查询
     *
     * @param extendField
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "附加表单的字段-分页列表查询")
    @ApiOperation(value = "附加表单的字段-分页列表查询", notes = "附加表单的字段-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(ExtendField extendField,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        log.info("extendField", extendField);
        log.info("com.yuanqiao.insight.cmdb.modules.extendField.controller.list(extendField={})", extendField);
        QueryWrapper<ExtendField> queryWrapper = QueryGenerator.initQueryWrapper(extendField, req.getParameterMap());
        Page<ExtendField> page = new Page<ExtendField>(pageNo, pageSize);
        IPage<ExtendField> pageList = extendFieldService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param extendField
     * @return
     */
    @AutoLog(value = "附加表单的字段-添加")
    @ApiOperation(value = "附加表单的字段-添加", notes = "附加表单的字段-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody ExtendField extendField) {
        log.info("extendField", extendField);
        log.info("com.yuanqiao.insight.cmdb.modules.extendField.controller.add(extendField={})", extendField);
        List<ExtendField> extendFieldList = extendFieldMapper.selectList(new QueryWrapper<ExtendField>().eq("delflag", 0)
                .eq("form_id", extendField.getFormId()));
        Map<String, String> map = extendFieldList.stream().collect(Collectors.toMap(ExtendField::getCode, ExtendField::getName));
        if (map.containsKey(extendField.getCode())) {
            return Result.error("当前标识已存在！");
        }
        if (map.containsValue(extendField.getName())) {
            return Result.error("当前名称已存在！");
        }
        extendField.setDelflag(0);
        extendFieldService.save(extendField);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param extendField
     * @return
     */
    @AutoLog(value = "附加表单的字段-编辑")
    @ApiOperation(value = "附加表单的字段-编辑", notes = "附加表单的字段-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody ExtendField extendField) {
        log.info("extendField", extendField);
        log.info("com.yuanqiao.insight.cmdb.modules.extendField.controller.edit(extendField={})", extendField);
        List<ExtendField> extendFieldList = extendFieldMapper.selectList(new QueryWrapper<ExtendField>().eq("delflag", 0).eq("form_id", extendField.getFormId()));
        Map<String, String> map = extendFieldList.stream().collect(Collectors.toMap(ExtendField::getCode, ExtendField::getName));
        ExtendField byId = extendFieldService.getById(extendField.getId());
        String remove = map.remove(byId.getCode());
        if (map.containsKey(extendField.getCode())) {
            return Result.error("当前字段标识已存在！");
        }
        if (map.containsValue(extendField.getName())) {
            return Result.error("当前字段名称已存在！");
        }
        boolean b = extendFieldService.updateById(extendField);
        if (b) {
            return Result.OK("编辑成功！");
        } else {
            return Result.error("编辑失败！");
        }
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "附加表单的字段-通过id删除")
    @ApiOperation(value = "附加表单的字段-通过id删除", notes = "附加表单的字段-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        log.info("id", id);
        log.info("com.yuanqiao.insight.cmdb.modules.extendField.controller.delete(id={})", id);

        ExtendField field = extendFieldService.getById(id);
        if (null == field) {
            return Result.error("当前字段不存在");
        }
        List<ExtendValue> extendValueList = extendValueMapper.findByExtendFieldName(field.getName());
        List<String> assetsNameList = extendValueList.stream().map(u -> {
            return assetsMapper.selectNameById(u.getAssetsId());
        }).collect(Collectors.toList());
        String assetNameStr = StringUtils.join(assetsNameList, " , ");

        if (!CollectionUtils.isEmpty(extendValueList)) {
            return Result.error("当前附加字段已被资产【" + assetNameStr + "】引用，不能删除！");
        }

        ExtendField extendField = new ExtendField();
        extendField.setDelflag(1);
        extendField.setId(id);
        extendFieldService.updateById(extendField);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "附加表单的字段-批量删除")
    @ApiOperation(value = "附加表单的字段-批量删除", notes = "附加表单的字段-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        log.info("ids", ids);
        log.info("com.yuanqiao.insight.cmdb.modules.extendField.controller.deleteBatch(ids={})", ids);
        List<String> idList = Arrays.asList(ids.split(","));
        for (String id : idList) {
            ExtendField field = extendFieldService.getById(id);
            if (null == field) {
                continue;
            }
            List<ExtendValue> extendValueList = extendValueMapper.findByExtendFieldName(field.getName());
            List<String> assetsNameList = extendValueList.stream().map(u -> {
                return assetsMapper.selectNameById(u.getAssetsId());
            }).collect(Collectors.toList());
            String assetNameStr = StringUtils.join(assetsNameList, " , ");

            if (!CollectionUtils.isEmpty(extendValueList)) {
                return Result.error("附加字段【" + field.getName() + "】已被资产【" + assetNameStr + "】引用，不能删除！");
            }
        }

        this.extendFieldService.removeByIds(idList);
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "附加表单的字段-通过id查询")
    @ApiOperation(value = "附加表单的字段-通过id查询", notes = "附加表单的字段-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(String id) {
        log.info("id", id);
        log.info("com.yuanqiao.insight.cmdb.modules.extendField.controller.queryById(id={})", id);
        if (StringUtils.isNotEmpty(id)) {
            ExtendField extendField = extendFieldService.getById(id);
            if (extendField == null) {
                return Result.error("未找到对应数据");
            }
            return Result.OK(extendField);
        }
        return null;
    }

    /**
     * 导出excel
     *
     * @param request
     * @param extendField
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ExtendField extendField) {
        log.info("extendField", extendField);
        log.info("com.yuanqiao.insight.cmdb.modules.extendField.controller.exportXls(extendField={})", extendField);
        return super.exportXls(request, extendField, ExtendField.class, "附加表单的字段");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ExtendField.class);
    }


    @AutoLog(value = "附加表单的字段-展示")
    @ApiOperation(value = "展示", notes = "附加表单的字段-展示")
    @GetMapping(value = "/listAll")
    public Result<?> listAll(@RequestParam("fromId") String fromId,
                             @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                             @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {

        log.info("fromId", fromId);
        log.info("com.yuanqiao.insight.cmdb.modules.extendField.controller.listAll(fromId={})", fromId);
        QueryWrapper<ExtendField> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("delflag", 0);
        if (StringUtils.isNotEmpty(fromId)) {
            queryWrapper.eq("form_id", fromId);
        }
        queryWrapper.orderByDesc("create_time");
        Page<ExtendField> page = new Page<ExtendField>(pageNo, pageSize);
        IPage<ExtendField> pageList = extendFieldService.page(page, queryWrapper);
        if (!CollectionUtils.isEmpty(pageList.getRecords())) {
            List<DictModel> dictModelList = dictService.getDictItems("type_code");
            for (ExtendField extendField : pageList.getRecords()) {
                for (DictModel dictModel : dictModelList) {
                    if (extendField.getType().equals(dictModel.getValue())) {
                        extendField.setType(dictModel.getText());
                    }
                }
            }
        }
        return Result.OK(pageList);
    }

    /**
     * @param assetsCategoryId 资产类型id
     * @return
     */
    @AutoLog(value = "展示codeName")
    @ApiOperation(value = "展示codeName", notes = "附加表单的字段-展示codeName")
    @GetMapping(value = "/findCodeName")
    public Result<?> findCodeName(@RequestParam("assetsCategoryId") String assetsCategoryId) {
        log.info("assetsCategoryId", assetsCategoryId);
        log.info("com.yuanqiao.insight.cmdb.modules.extendField.controller.findCodeName(assetsCategoryId={})", assetsCategoryId);
        List<ExtendField> findCodeName = extendFieldMapper.findCodeName(assetsCategoryId);
        return Result.OK(findCodeName);
    }

    /**
     * @param assetsCategoryId 资产类型id
     * @param assetsId         资产id
     * @return
     */
    @AutoLog(value = "findCodeValue")
    @ApiOperation(value = "findCodeValue", notes = "附加表单的字段-findCodeValue")
    @GetMapping(value = "/findCodeValue")
    public Result<?> findCodeValue(@RequestParam("assetsCategoryId") String assetsCategoryId, @RequestParam("assetsId") String assetsId) {
        log.info("assetsCategoryId", assetsCategoryId);
        log.info("com.yuanqiao.insight.cmdb.modules.extendField.controller.findCodeValue(assetsCategoryId={},assetsId={})", assetsCategoryId, assetsId);
        List<ExtendField> codeValue = extendFieldService.selectByCategoryAndAsset(assetsCategoryId, assetsId);
        return Result.OK(codeValue);
    }

    /**
     * @param assetsCategoryId 资产类型id
     * @return
     */

    @GetMapping(value = "/findCodeNameBatch")
    public Result<?> findCodeNameBatch(@RequestParam("assetsCategoryId") String assetsCategoryId) {
        List<ExtendField> findCodeName = extendFieldMapper.findCodeNameBatch(assetsCategoryId);
        return Result.OK(findCodeName);
    }
}
