package com.yuanqiao.insight.cmdb.modules.extendForm.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.cmdb.modules.assetscategory.entity.AssetsCategory;
import com.yuanqiao.insight.cmdb.modules.assetscategory.service.impl.AssetsCategoryServiceImpl;
import com.yuanqiao.insight.cmdb.modules.assetscategory.util.ExcelUtil;
import com.yuanqiao.insight.cmdb.modules.category.entity.CmdbAssetsCategory;
import com.yuanqiao.insight.cmdb.modules.category.service.ICmdbAssetsCategoryService;
import com.yuanqiao.insight.cmdb.modules.extendField.entity.ExtendField;
import com.yuanqiao.insight.cmdb.modules.extendField.mapper.ExtendFieldMapper;
import com.yuanqiao.insight.cmdb.modules.extendForm.entity.ExtendForm;
import com.yuanqiao.insight.cmdb.modules.extendForm.mapper.ExtendFormMapper;
import com.yuanqiao.insight.cmdb.modules.extendForm.service.IExtendFormService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.umpPwdManage.entity.UmpPwdManage;
import org.jeecg.modules.umpPwdManage.service.IUmpPwdManageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.transaction.Transactional;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 附加表单
 * @Author: jeecg-boot
 * @Date: 2021-05-12
 * @Version: V1.0
 */
@Api(tags = " 附加表单")
@RestController
@RequestMapping("/extendForm/extendForm")
@Slf4j
public class ExtendFormController extends JeecgController<ExtendForm, IExtendFormService> {
    @Autowired
    private IExtendFormService extendFormService;
    @Autowired
    private ExtendFormMapper extendFormMapper;
    @Autowired
    private ExtendFieldMapper extendFieldMapper;
    @Autowired
    private IUmpPwdManageService umpPwdManageService;
    @Autowired
    private ICmdbAssetsCategoryService cmdbAssetsCategoryService;
    @Value("${excel.extendFormPath}")
    private String path;
    @Autowired
    private AssetsCategoryServiceImpl assetsCategoryServiceImpl;


    /**
     * 分页列表查询
     *
     * @param extendForm
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = " 附加表单-分页列表查询")
    @ApiOperation(value = " 附加表单-分页列表查询", notes = " 附加表单-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(ExtendForm extendForm,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        log.info("com.yuanqiao.insight.cmdb.modules.extendForm.controller.list(extendForm={})", extendForm);
        Page<ExtendForm> page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapper<ExtendForm> queryWrapper = new LambdaQueryWrapper<ExtendForm>()
                .eq(ExtendForm::getDelflag, 0)
                .like(StringUtils.isNotEmpty(extendForm.getName()), ExtendForm::getName, extendForm.getName())
                .eq(StringUtils.isNotEmpty(extendForm.getAssetsCategoryId()), ExtendForm::getAssetsCategoryId, extendForm.getAssetsCategoryId())
                .orderByDesc(ExtendForm::getCreateTime);
        IPage<ExtendForm> iPage = extendFormService.page(page, queryWrapper);
        List<ExtendForm> records = iPage.getRecords();
        //查看操作为什么要从列表中获取单条记录数据?答:祖宗之法不可变(dog
        records.forEach(item -> {
            try {
                item.setCategoryName(cmdbAssetsCategoryService.getNames(item.getAssetsCategoryId()));
            } catch (Exception e) {
                log.error("附加表单-分页列表查询-获取资产类别异常！", e);
            }

        });
        return Result.OK(iPage);
    }

    /**
     * 添加
     *
     * @param extendForm
     * @return
     */
    @AutoLog(value = " 附加表单-添加")
    @ApiOperation(value = " 附加表单-添加", notes = " 附加表单-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody ExtendForm extendForm) {
        log.info("com.yuanqiao.insight.cmdb.modules.extendForm.controller.add(extendForm={})", extendForm);
        QueryWrapper<ExtendForm> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", extendForm.getName()).eq("delflag", 0);
        List<ExtendForm> list = extendFormService.list(queryWrapper);
        if (!list.isEmpty()) {
            return Result.error("表单名称重复！");
        }
        QueryWrapper<ExtendForm> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("code", extendForm.getCode()).eq("delflag", 0);
        List<ExtendForm> list1 = extendFormService.list(queryWrapper1);
        if (!list1.isEmpty()) {
            return Result.error("表单编码重复！");
        }
        extendForm.setDelflag(0);
        int countByCategoryId = extendFormMapper.findCountByCategoryId(extendForm.getAssetsCategoryId());
        if (countByCategoryId > 0) {
            return Result.error("该资产类型已存在表单，无需重复创建");
        } else {
            extendFormService.save(extendForm);
            return Result.OK("添加成功！");
        }

    }

    /**
     * 编辑
     *
     * @param extendForm
     * @return
     */
    @AutoLog(value = " 附加表单-编辑")
    @ApiOperation(value = " 附加表单-编辑", notes = " 附加表单-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody ExtendForm extendForm) {
        log.info("com.yuanqiao.insight.cmdb.modules.extendForm.controller.edit(extendForm={})", extendForm);
        QueryWrapper<ExtendForm> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("delflag", 0);
        List<ExtendForm> list = extendFormService.list(queryWrapper);
        list.removeIf(next -> next.getId().equals(extendForm.getId()));
        for (ExtendForm form : list) {
            if (form.getName().equals(extendForm.getName())) {
                return Result.error("表单名称重复!");
            }
            if (form.getCode().equals(extendForm.getCode())) {
                return Result.error("表单代码重复!");
            }
            if (form.getAssetsCategoryId()!=null && form.getAssetsCategoryId().equals(extendForm.getAssetsCategoryId()))
            {
                return Result.error("资产类型重复!");
            }
        }
//		ArrayList<ExtendForm> extendForms = new ArrayList<>();
//		for (ExtendForm form : list) {
//			if (form.getId().equals(extendForm.getId())){
//				list.remove(form);
//			}else {
//				extendForms.add(form);
//			}
//		}
//		for (ExtendForm form : extendForms) {
//			if (extendForm.getName().equals(form.getName())){
//				return Result.error("表单名称重复!");
//			}
//		}
//		for (ExtendForm form : extendForms) {
//			if (extendForm.getCode().equals(form.getCode())){
//				return Result.error("表单代码重复!");
//			}
//		}
        if (StringUtils.isNotEmpty(extendForm.getId())) {
            boolean b = extendFormService.updateById(extendForm);
            if (b) {
                return Result.OK("编辑成功!");
            } else {
                return Result.error("编辑失败!");
            }
        } else {
            return Result.error("附加表单ID不存在，无法修改！");
        }
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = " 附加表单-通过id删除")
    @ApiOperation(value = " 附加表单-通过id删除", notes = " 附加表单-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        log.info("id", id);
        log.info("com.yuanqiao.insight.cmdb.modules.extendForm.controller.delete(id={})", id);

        // 1、要删cmdb_extend_form表中数据 则保证cmdb_extend_field表中没有引用。
        // 2、要删cmdb_extend_field表中数据，则保证没有cmd_extend_value表中没有引用

        List<ExtendField> extendFieldList = extendFieldMapper.findAllByFromId(id);
        if (!CollectionUtils.isEmpty(extendFieldList)) {
            return Result.error("当前表单已有字段数据，不能删除！");
        }

        ExtendForm extendForm = new ExtendForm();
        extendForm.setId(id);
        extendForm.setDelflag(1);
        extendFormService.updateById(extendForm);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = " 附加表单-批量删除")
    @ApiOperation(value = " 附加表单-批量删除", notes = " 附加表单-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        log.info("ids", Arrays.asList(ids.split(",")));
        log.info("com.yuanqiao.insight.cmdb.modules.extendForm.controller.deleteBatch(ids={})", Arrays.asList(ids.split(",")));
        List<String> idList = Arrays.asList(ids.split(","));

        for (String id : idList) {
            ExtendForm extendForm = extendFormService.getById(id);
            if (null == extendForm) {
                continue;// 如果没有根据id找到这个表单，说明被删除了，则跳过本次校验操作
            }
            List<ExtendField> extendFieldList = extendFieldMapper.findAllByFromId(id);
            if (!CollectionUtils.isEmpty(extendFieldList)) {
                return Result.error("表单【" + extendForm.getName() + " 】已有字段数据，不能删除！");
            }
        }
        this.extendFormService.removeByIds(idList);
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = " 附加表单-通过id查询")
    @ApiOperation(value = " 附加表单-通过id查询", notes = " 附加表单-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        log.info("id", id);
        log.info("com.yuanqiao.insight.cmdb.modules.extendForm.controller.queryById(id={})", id);
        ExtendForm extendForm = extendFormService.getById(id);
        if (extendForm == null) {
            return Result.error("未找到对应数据");
        }
        CmdbAssetsCategory byId = cmdbAssetsCategoryService.getById(extendForm.getAssetsCategoryId());
        extendForm.setCategoryName(byId.getCategoryName());
        return Result.OK(extendForm);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param extendForm
     */
    @AutoLog(value = " 附加表单-导出")
    @RequestMapping(value = "/exportXls")
    public void exportXls(HttpServletRequest request, HttpServletResponse response, ExtendForm extendForm) {
        extendForm.setDelflag(0);
        log.info("extendForm", extendForm);
        log.info("com.yuanqiao.insight.cmdb.modules.extendForm.controller.exportXls(extendForm={})", extendForm);
        UmpPwdManage zip = umpPwdManageService.getZip();
        super.exportXlsZip(request, response, extendForm, ExtendForm.class, " 附加字段表单", zip.getZipPwd(), zip.getIsEncry());
    }

    /**
     * 模板导出
     *
     * @param
     * @param
     * @return
     */
    @AutoLog(value = " 附加表单-模板下载")
    @GetMapping(value = "/downloadTemplate")
    @ApiOperation("模板导出")
    public void downloadTemplate(HttpServletResponse response, HttpServletRequest request) {
        try {
            List<AssetsCategory> list = assetsCategoryServiceImpl.list(new LambdaQueryWrapper<AssetsCategory>().eq(AssetsCategory::getDelflag, org.jeecg.common.constant.CommonConstant.DEL_FLAG_0));
            List<String> categoryNameList = list.stream().map(AssetsCategory::getCategoryName).collect(Collectors.toList());

            InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(path);
            String str1 = path.substring(0, path.indexOf("."));
            String str2 = path.substring(str1.length());

            // 根据输入流创建工作簿
            Workbook workbook = new XSSFWorkbook(inputStream);
            Sheet sheet = workbook.getSheet("字典值");
            setColumnValue(categoryNameList, sheet, 0);

            response.setContentType("application/binary;charset=ISO8859-1");
            String fileName = URLEncoder.encode("附加字段导入模板" + str2, "UTF-8");
            response.setHeader("Content-disposition", "attachment; filename=" + fileName);
            ServletOutputStream out = null;
            out = response.getOutputStream();
            workbook.write(out);
            out.flush();
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
            log.info("导出错误", e.getMessage());
        }
    }

    /**
     * 将字典值写入数据表
     *
     * @param terminalTypeList
     * @param sheet
     * @param column
     */
    private void setColumnValue(List<String> terminalTypeList, Sheet sheet, Integer column) {
        for (int i = 0; i < terminalTypeList.size(); i++) {
            Row row = sheet.getRow(i + 1);
            if (row == null) {
                row = sheet.createRow(i + 1);
            }
            Cell cell = row.createCell(column);
            cell.setCellValue(terminalTypeList.get(i));
        }
    }

    //    /**
//      * 通过excel导入数据
//    *
//    * @param request
//    * @param response
//    * @return
//    */
//    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
//    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
//        return super.importExcel(request, response, ExtendForm.class);
//    }
    @AutoLog(value = " 附加表单-导入 ")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    @Transactional
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            return extendFormService.importSheets(file);
        }
        return Result.error("文件导入失败！");
    }

}
