package com.yuanqiao.insight.cmdb.modules.itInnovate.service.impl;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.cmdb.modules.assetscategory.entity.AssetsCategory;
import com.yuanqiao.insight.cmdb.modules.assetscategory.service.IAssetsCategoryService;
import com.yuanqiao.insight.cmdb.modules.assetscategory.util.ExcelUtil;
import com.yuanqiao.insight.cmdb.modules.itInnovate.entity.CmdbItInnovate;
import com.yuanqiao.insight.cmdb.modules.itInnovate.mapper.CmdbItInnovateMapper;
import com.yuanqiao.insight.cmdb.modules.itInnovate.service.ICmdbItInnovateService;
import com.yuanqiao.insight.cmdb.modules.supplier.entity.CmdbSupplier;
import com.yuanqiao.insight.cmdb.modules.supplier.service.ICmdbSupplierService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.DictModel;
import com.yuanqiao.insight.acore.system.service.ISysDictService;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * @Description: 供应商
 * @Author: jeecg-boot
 * @Date: 2021-05-10
 * @Version: V1.0
 */
@Service
@Slf4j
public class CmdbItInnovateServiceImpl extends ServiceImpl<CmdbItInnovateMapper, CmdbItInnovate> implements ICmdbItInnovateService {
    @Autowired
    private IAssetsCategoryService categoryService;
    @Autowired
    private ICmdbSupplierService cmdbSupplierService;
    @Autowired
    private ISysDictService sysDictService;

    @Override
    public Result importSheets(MultipartFile file) {

        try {
            Workbook hssfWorkbook = ExcelUtil.getWorkBook(file);
            ImportParams params = new ImportParams();
            int count = 3;
//            for (int numSheet = 0; numSheet < hssfWorkbook.getNumberOfSheets(); numSheet++) {
                params.setTitleRows(2);
                params.setHeadRows(1);
                params.setNeedSave(true);
                List<CmdbItInnovate> list = ExcelImportUtil.importExcel(file.getInputStream(), CmdbItInnovate.class, params);
                if (list.isEmpty()) {
                    return Result.error("文件不能为空！");
                }
                for (CmdbItInnovate cmdbItInnovate : list) {
                    count += 1;
                    if (StringUtils.isEmpty(cmdbItInnovate.getModel()) || StringUtils.isEmpty(cmdbItInnovate.getSupplierText()) || StringUtils.isEmpty(cmdbItInnovate.getProductCategoryText())
                            || StringUtils.isEmpty(cmdbItInnovate.getCatalogueText())) {
                        return Result.error("文件导入失败，第 " + count + " 行，存在空数据！");
                    }
                    QueryWrapper<CmdbItInnovate> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("model", cmdbItInnovate.getModel());
                    queryWrapper.ne("id", cmdbItInnovate.getId());
                    List<CmdbItInnovate> historyList = this.list(queryWrapper);
                    if (CollUtil.isNotEmpty(historyList)) {
                        return Result.error("文件导入失败，第" + count + "行，型号重复！");
                    }
                    List<DictModel> dicItemList = sysDictService.queryDictItemsByCode("innovatedDeviceCatalogue");
                    Map<String,String> dictModeMap = dicItemList.stream().collect(Collectors.toMap(DictModel::getText, DictModel::getValue));
                    AssetsCategory assetsCategory = categoryService.getOne(new QueryWrapper<AssetsCategory>().eq("category_name", cmdbItInnovate.getProductCategoryText()));
                    if (assetsCategory != null) {
                        cmdbItInnovate.setProductCategoryId(assetsCategory.getId());
                    }
                    CmdbSupplier supplier = cmdbSupplierService.getOne(new QueryWrapper<CmdbSupplier>().eq("name", cmdbItInnovate.getSupplierText()));
                    if (supplier != null){
                        cmdbItInnovate.setSupplier(supplier.getId());
                    }
                    if (dictModeMap.containsKey(cmdbItInnovate.getCatalogueText())){
                        cmdbItInnovate.setCatalogue(dictModeMap.get(cmdbItInnovate.getCatalogueText()));
                    }
                    super.save(cmdbItInnovate);
                }
//            }
            return Result.ok("文件导入成功！");
        } catch (IOException e) {
            log.error("文件导入失败！", e);
            return Result.error("文件导入失败：" + e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("文件导入失败!" + e.getMessage());
        } finally {
            try {
                file.getInputStream().close();
            } catch (IOException e) {
                e.printStackTrace();
                return Result.error("文件导入失败!" + e.getMessage());
            }
        }

    }
}
