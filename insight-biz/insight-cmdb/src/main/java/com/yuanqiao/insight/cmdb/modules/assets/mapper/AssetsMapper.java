package com.yuanqiao.insight.cmdb.modules.assets.mapper;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.cmdb.modules.analysis.entity.ExpireTop;
import com.yuanqiao.insight.cmdb.modules.assets.entity.*;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @Description: 资产表
 * @Author: jeecg-boot
 * @Date: 2021-03-02
 * @Version: V1.0
 */
@Component
public interface AssetsMapper extends BaseMapper<Assets> {

    boolean upAssetsByIds(@Param("ids") List<String> ids);

    String selectNameById(String assetsId);

    List<Assets> selectAssetsByCategoryId(String categoryId);

    IPage<Assets> selectAssetsByCondition(Page<?> page, @Param("id") String id, @Param("categoryIds") List<String> categoryIds, @Param("assetsName") String assetsName, @Param("producerName") String producerName);

    List<Assets> countAssetsByCondition(@Param("id") String id, @Param("categoryIds") List<String> categoryIds, @Param("assetsName") String assetsName, @Param("producerName") String producerName);

    Assets queryByAssetsCode(String assetsCode);

    IPage<AssetsLog> selectLogById(Page<AssetsLog> page, String assetsId);

    List<Assets> findAll(@Param("assetsCategoryId") String assetsCategoryId, @Param("assetsId") String assetsId);

    List<CmdbAssets> cmdbCount(@Param("time1") String time1, @Param("time2") String time2);

    List<CmdbAssets> cmdbCountByHg(@Param("date1") Date date1, @Param("date2") Date date2);

    // 仅查询前十条
    IPage<ExpireTop> expireTop(Page<?> page, @Param("time1") String time1, @Param("time2") String time2);

    // 仅查询前十条-适配瀚高
    IPage<ExpireTop> expireTopByHg(Page<?> page, @Param("date1") Date date1, @Param("date2") Date date2);

    List<Assets> assetsCount();

    int assetsExpired(@Param("time1") String time1, @Param("time2") String time2);

    int assetsExpiredByHg(@Param("date1") DateTime date1, @Param("date2") DateTime date2);

    List<ExpireTop> categoryCount(@Param("time1") String time1, @Param("time2") String time2);

    Dep findName(@Param("id") String id);

    List<Assets> search4delcheckByConditions(@Param("status_id") String status_id, @Param("producer_id") String producer_id);

    List<Assets> selectByCategoryAndStatus(@Param("categoryIdList") List<String> categoryIdList, @Param("cmdbStatusId") String cmdbStatusId);

    List<AccestInfo> getAccestInfo(@Param("startTime") String startTime, @Param("endTime") String endTime);

    Integer getprocessNum();

    Integer getPendingTasks(@Param("a") int a, @Param("b") int b);

    List<String> selectNumbers(@Param("middle") String middle);

    List<String> assetsName();

    List<String> assetsCode();

    int getAssetsNameNum(@Param("tableName") String tableName);

    /**
     * 创建临时表
     */

    @Insert("create table if not exists ${tableName} (assets_name varchar(255))")
    void createTemporaryTable(@Param("tableName") String tableName);

    /**
     * 保存数据到临时表里面以便校验数据重复
     */

    @Insert("insert into ${tableName} values (#{assetsName})")
    void insertData(@Param("tableName") String tableName, @Param("assetsName") String assetsName);

    @Delete("delete from ${tableName}")
    void deleteTableContent(@Param("tableName") String tableName);

    /**
     * 删除临时表
     */

    @Delete({"drop table if exists ${tableName}"})
    void dropTemporaryTable(@Param("tableName") String tableName);

    boolean updateEntity(@Param("column") String column, @Param("id") String id);

    boolean updateAssetsStatusByIds(@Param("ids") List<String> ids, @Param("statusName") String statusName, @Param("statusId") String statusId);

}
