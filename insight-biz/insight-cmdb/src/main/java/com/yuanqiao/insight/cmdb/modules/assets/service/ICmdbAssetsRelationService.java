package com.yuanqiao.insight.cmdb.modules.assets.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuanqiao.insight.cmdb.modules.assets.entity.AssetsRelationVo;
import com.yuanqiao.insight.cmdb.modules.assets.entity.CmdbAssetsRelation;

public interface ICmdbAssetsRelationService extends IService<CmdbAssetsRelation> {


     AssetsRelationVo relationship (String  assetsId);

     void removeByAssetsIdOne(String assetsIdOne);
}
