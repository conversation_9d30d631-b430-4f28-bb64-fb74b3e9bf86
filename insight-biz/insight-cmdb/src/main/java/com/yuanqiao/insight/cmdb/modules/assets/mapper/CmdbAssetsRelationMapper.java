package com.yuanqiao.insight.cmdb.modules.assets.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanqiao.insight.cmdb.modules.assets.entity.CmdbAssetsRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

@Mapper
@Component
public interface CmdbAssetsRelationMapper extends BaseMapper<CmdbAssetsRelation> {

    //根据父子查询出当前父类下包含的子
    List<CmdbAssetsRelation> findAllByAssetsOneAndTwo (@Param("assetsIdOne")String assetsIdOne,@Param("list") List<String> list );

    //查询出当前父子
    List<String> findAllByAssetsOne (@Param("assetsId")String assetsId);

    //根据子查询出父类
    List<CmdbAssetsRelation> findParentByAssetsOneAndTwo (@Param("assetsIdTwo") String assetsIdTwo);

    CmdbAssetsRelation findAll (@Param("assetsId")String assetsId);

    //根据One查询出One相关的所有的关系
    List<CmdbAssetsRelation> findParentByAssetsOne (@Param("assetsIdOne") String assetsIdOne);
}
