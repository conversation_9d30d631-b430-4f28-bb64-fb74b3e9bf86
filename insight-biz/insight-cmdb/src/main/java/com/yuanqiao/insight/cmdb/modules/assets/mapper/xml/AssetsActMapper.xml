<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.cmdb.modules.assets.mapper.AssetsActMapper">

    <select id="selectAssetsUnion" resultType="com.yuanqiao.insight.cmdb.modules.assets.entity.AssetsAct">
        select assets_code, assets_name
        from
        cmdb_assets
        <where>
            <if test="id != null and id.trim() != ''">
                and id != #{id}
            </if>
            <if test="assetsName != null and assetsName.trim() != ''">
                and (assets_name = #{assetsName}
                <if test="assetsCode != null and assetsCode.trim() != ''">
                    or assets_code = #{assetsCode}
                </if>
                )
            </if>
            and delflag = 0
        </where>
        union
        select assets_code, assets_name
        from
        cmdb_assets_act
        <where>
            <if test="id != null and id.trim() != ''">
                and assets_id != #{id}
            </if>
            <if test="assetsName != null and assetsName.trim() != ''">
                and (assets_name = #{assetsName}
                <if test="assetsCode != null and assetsCode.trim() != ''">
                    or assets_code = #{assetsCode}
                </if>
                )
            </if>
            and delflag = 0
        </where>
    </select>

    <select id="selectDevUnion" resultType="com.yuanqiao.insight.service.device.entity.DeviceInfo">

        select name, device_code
        from
        momg_device_info
        <where>
            <if test="id != null and id.trim() != ''">
                and id != #{id}
            </if>
            <if test="name != null and name.trim() != ''">
                and (name = #{name}
                <if test="deviceCode != null and deviceCode.trim() != ''">
                    or device_code = #{deviceCode}
                </if>
                )
            </if>
        </where>

    </select>

    <select id="stockAssetsList" resultType="com.yuanqiao.insight.cmdb.modules.assets.entity.Assets">

        select *
        from
        cmdb_assets
        <where>
            <if test="assetsName != null and assetsName.trim() != ''">
                assets_name like concat('%', #{assetsName}, '%')
            </if>
            <if test="assetsCategoryId != null and assetsCategoryId.trim() != ''">
                assets_category_id = #{assetsCategoryId}
            </if>
            <if test="producerId != null and producerId.trim() != ''">
                producer_id = #{producerId}
            </if>
            <if test='assetsActIdList != null and assetsActIdList.size() > 0 '>
                and id not in
                <foreach collection="assetsActIdList" item="assetsId" open="(" separator="," close=")">
                    #{assetsId}
                </foreach>
            </if>
        </where>

    </select>

</mapper>
