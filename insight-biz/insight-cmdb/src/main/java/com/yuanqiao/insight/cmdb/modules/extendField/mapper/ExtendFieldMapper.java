package com.yuanqiao.insight.cmdb.modules.extendField.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanqiao.insight.cmdb.modules.extendField.entity.ExtendField;
import com.yuanqiao.insight.cmdb.modules.extendValue.entity.ExtendValue;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * @Description: 附加表单的字段
 * @Author: jeecg-boot
 * @Date: 2021-05-13
 * @Version: V1.0
 */
@Component
public interface ExtendFieldMapper extends BaseMapper<ExtendField> {

    List<ExtendField> findAllByFromId(@Param("fromId") String fromId);

    List<ExtendField> findCodeName(@Param("assetsCategoryId") String assetsCategoryId);

    List<ExtendValue> findCodeValue(@Param("assetsId") String assetsId);

    ExtendField getExtendFieldByNameAndCode(@Param("formId") String formId,@Param("name") String name, @Param("code") String code);

    /**
     * 根据分类id查询批量操作的附加字段
     *
     * @param assetsCategoryId
     * @return
     */
    List<ExtendField> findCodeNameBatch(@Param("assetsCategoryId") String assetsCategoryId);

    List<ExtendField> findCodeNameByPostgresql(@Param("assetsCategoryId") String assetsCategoryId);

}
