package com.yuanqiao.insight.cmdb.modules.contract.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("cmdb_contract")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="cmdb_contract对象", description="合同信息")
public class ContractEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;




    //合同编号
    @Excel(name = "合同编号", width = 15)
    @ApiModelProperty(value = "合同编号")
    private String code;
    //合同名称
    @Excel(name = "合同名称", width = 15)
    @ApiModelProperty(value = "合同名称")
    private String name;
    //合同类型
    //配置字典 contractType.赠与合同	GIFT_CONTRACT,借用合同	BORROW_CONTRACT,租赁合同	LEASE_CONTRACT,购买合同	PURCHASE_CONTRACT
    @Excel(name = "合同类型", width = 15)
    @ApiModelProperty(value = "合同类型")
    private String type;
    //合同描述
    @Excel(name = "合同名称", width = 50)
    @ApiModelProperty(value = "合同描述")
    private String description;
    //合同金额
    @Excel(name = "合同金额", width = 20)
    @ApiModelProperty(value = "合同金额")
    private double amount;

    @ApiModelProperty(value = "删除标识")
    private Integer delflag; // 删除标识（0：未删除，1：已删除）
}
