package com.yuanqiao.insight.cmdb.modules.assetscategory.util;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: 资产报表-PdfUtil
 * @projectName jeecg-boot-parent
 * @description: TODO
 * @date 2021/7/29-9:02
 */
@Slf4j
public class PdfUtil {
    public void createPDF(Map<String, List<Map<String, Object>>> map, String filePath, String[] photoPath) {
        FileOutputStream fos = null;
        try {

            // 设置纸张大小和背景色
            Rectangle rect = new Rectangle(PageSize.A4);
            // 创建文档实例
            Document doc = new Document(rect);
            // 添加中文字体
          //  BaseFont bfChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            BaseFont bfChinese = null;
            try {
                bfChinese = BaseFont.createFont("STSong-Light","UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
            } catch (DocumentException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
        }

            // 设置字体样式
            Font textFont = new Font(bfChinese, 11, Font.NORMAL, BaseColor.BLACK); // 正常
            Font lineFont = new Font(bfChinese, 11, Font.NORMAL, BaseColor.BLACK); // 正常
            Font firsetTitleFont = new Font(bfChinese, 22, Font.NORMAL, BaseColor.BLACK); // 一级标题
            Font secondTitleFont = new Font(bfChinese, 15, Font.BOLD, BaseColor.BLACK); // 二级标题
            Font textFont2 = new Font(bfChinese, 11, Font.BOLD, BaseColor.BLACK); // 正常

            // 将组织得到的报告内容写入pdf文件
            fos = new FileOutputStream(new File(filePath));
            // 创建输出流
            PdfWriter.getInstance(doc, fos);
            doc.open();
            doc.newPage();

            //PDF文档内容开始
            // 标题及下划线
            Paragraph p1 = new Paragraph();
            p1 = new Paragraph("资产统计", firsetTitleFont);
            p1.setLeading(10);

            p1.setAlignment(Element.ALIGN_CENTER);
            doc.add(p1);
            String line = "-------------------------------";
            line = line + line + line + line + "--";
            p1 = new Paragraph(line, lineFont);
            p1.setLeading(10);
            doc.add(p1);
            p1 = new Paragraph(" ", lineFont);
            doc.add(p1);
            p1 = new Paragraph("资产统计", textFont);
            p1.setLeading(30);
            p1.setAlignment(Element.ALIGN_CENTER);//最对齐
            doc.add(p1);
            // 创建基本信息列表
            if(null != map && 0 < map.size()){
                createBaseInformationTable(doc,textFont, secondTitleFont,map);
            }
            //添加图片
            if(null != photoPath && 0 < photoPath.length){
                setDocImag(doc, secondTitleFont, textFont, map,photoPath,p1);
            }
            doc.close();
        } catch (DocumentException e) {
            log.error("PdfUtil类的createPDF方法发生异常", e);
        } catch (Exception e) {
            log.error("PDF写入图片信息有问题");
        } finally {
            if (fos != null) {
                try {
                    fos.flush();
                    fos.close();
                } catch (IOException e) {
                }
                fos = null;
            }
        }

    }
    /**
     *
     * @param doc
     * @param textFont
     * @param secondTitleFont
     * @param map
     * @throws Exception
     */
    private void createBaseInformationTable(Document doc, Font textFont, Font secondTitleFont,Map<String, List<Map<String, Object>>> map ) throws Exception {
        PdfPTable detailsTable = new PdfPTable(8);
        detailsTable.setSpacingBefore(20f);
        detailsTable.setTotalWidth(new float[] {80,38,80,38,80,38,80,38}); // 设置列宽
        detailsTable.setLockedWidth(true); // 锁定列宽
        // 添加中文字体
        BaseFont bfChinese =BaseFont.createFont("STSong-Light","UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        // 设置字体样式
        Font font = new Font(bfChinese, 11, Font.NORMAL,BaseColor.BLACK); // 正常
        BaseColor color =  color = new BaseColor(46,73,88);
        List<Map<String, Object>> numberList = map.get("number");
        for (Map map1:numberList ) {
            createDetailsCell(color,"资产数量",font,detailsTable,1);
            createDetailsCell(BaseColor.WHITE,map1.get("assetsCount").toString(),font,detailsTable,1);

            createDetailsCell(color,"过期资产",font,detailsTable,1);
            createDetailsCell(BaseColor.WHITE,map1.get("assetsExpired").toString(),font,detailsTable,1);

            createDetailsCell(color,"未过期资产",font,detailsTable,1);
            createDetailsCell(BaseColor.WHITE,map1.get("assetsNotExpire").toString(),font,detailsTable,1);

            createDetailsCell(color,"即将过期资产",font,detailsTable,1);
            createDetailsCell(BaseColor.WHITE,map1.get("assetsSoonExpire").toString(),font,detailsTable,1);
            break;
        }
//        List<Map<String, Object>> chartList = map.get("chart");
//        for (Map map1:chartList ) {
//            createDetailsCell(color,map1.get("name").toString(),font,detailsTable,1);
//            createDetailsCell(BaseColor.WHITE,map1.get("value").toString(),font,detailsTable,1);
//        }

        doc.add(detailsTable);

    }
    private void setDocImag(Document doc, Font secondTitleFont,Font textFont,Map<String, List<Map<String, Object>>> map,String[] photoPath,Paragraph p1 ) throws Exception{
        PdfPTable tablePhoto = new PdfPTable(1);
        //第一行
        PdfPCell detailsCell = new PdfPCell(p1);
        //设置第一幅图片
        if(StringUtils.isNotEmpty(photoPath[0])){
            Image image1 = null;
            image1 = Image.getInstance(photoPath[0]);
            image1.setBorder(0);
            detailsCell.addElement(image1);
            detailsCell.setBorderWidth(0);
            detailsCell.setBorderColor(null);
            tablePhoto.addCell(detailsCell);
        }
        doc.add(tablePhoto);

        PdfPTable tablePhoto2 = new PdfPTable(1);
        //第一行
        PdfPCell detailsCell2 = new PdfPCell(p1);
        if(StringUtils.isNotEmpty(photoPath[1])){
            //设置第二幅图片
            Image image2 = null;
            image2 = Image.getInstance(photoPath[1]);
            detailsCell2.addElement(image2);
            detailsCell2.setBorderWidth(0);
            detailsCell2.setBorderColor(null);
            tablePhoto2.addCell(detailsCell2);
        }
        doc.add(tablePhoto2);

        PdfPTable tablePhoto3 = new PdfPTable(1);
        //第二行
        PdfPCell detailsCel4 = new PdfPCell(p1);
        if(StringUtils.isNotEmpty(photoPath[2])){
            //设置第一幅图片
            Image image3 = null;
            image3= Image.getInstance(photoPath[2]);
            image3.setBorder(0);
            detailsCel4.addElement(image3);
            detailsCel4.setBorderWidth(0);
            detailsCel4.setBorderColor(null);
            tablePhoto3.addCell(detailsCel4);
        }
        doc.add(tablePhoto3);
    }
    /**
     * 添加单元格PDF
     * @param color 单元格背景色
     * @param key 单元格内容
     * @param font 单元格字体
     * @param table 表格
     * @param n 合并单元格数
     */
    private void createDetailsCell(BaseColor color,String key,Font font,PdfPTable table,int n){
        PdfPCell cell = new PdfPCell(new Phrase(key, font));
        cell.setMinimumHeight(15); // 设置单元格高度
        cell.setUseAscender(true); // 设置可以居中
        cell.setHorizontalAlignment(Element.ALIGN_LEFT); // 设置水平居中
        cell.setVerticalAlignment(Element.ALIGN_MIDDLE); // 设置垂直居中
        cell.setColspan(n);
        table.addCell(cell);
    }
}
