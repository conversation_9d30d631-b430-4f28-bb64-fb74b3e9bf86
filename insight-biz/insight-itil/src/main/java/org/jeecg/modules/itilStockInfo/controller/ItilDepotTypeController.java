package org.jeecg.modules.itilStockInfo.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.itilStockInfo.entity.ItilDepotType;
import org.jeecg.modules.itilStockInfo.service.IItilDepotTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

/**
 * @Description: 备品备库类型
 * @Author: jeecg-boot
 * @Date:   2021-08-23
 * @Version: V1.0
 */
@Api(tags="备品备库类型")
@RestController
@RequestMapping("/itilDepotType/itilDepotType")
@Slf4j
public class ItilDepotTypeController extends JeecgController<ItilDepotType, IItilDepotTypeService> {
	@Autowired
	private IItilDepotTypeService itilDepotTypeService;

	/**
	 * 分页列表查询
	 *
	 * @param itilDepotType
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "备品备库类型-分页列表查询")
	@ApiOperation(value="备品备库类型-分页列表查询", notes="备品备库类型-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(ItilDepotType itilDepotType,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
	    log.info("ItilDepotTypeController ## queryPageList(itilDepotType={})",itilDepotType);
        try {
            QueryWrapper<ItilDepotType> queryWrapper = QueryGenerator.initQueryWrapper(itilDepotType, req.getParameterMap());
            Page<ItilDepotType> page = new Page<ItilDepotType>(pageNo, pageSize);
            IPage<ItilDepotType> pageList = itilDepotTypeService.page(page, queryWrapper);
            return Result.OK(pageList);
        } catch (Exception e) {
            log.error("ItilDepotTypeController ## queryPageList(itilDepotType={})",itilDepotType,e);
            return Result.error(e.getMessage());
        }
    }

	/**
	 *   添加
	 *
	 * @param itilDepotType
	 * @return
	 */
	@AutoLog(value = "备品备库类型-添加")
	@ApiOperation(value="备品备库类型-添加", notes="备品备库类型-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody ItilDepotType itilDepotType) {
        log.info("ItilDepotTypeController ## add(itilDepotType={})",itilDepotType);
        try {
            itilDepotType.setDelflag(0);
            itilDepotTypeService.save(itilDepotType);
            return Result.OK("添加成功！");
        } catch (Exception e) {
            log.error("ItilDepotTypeController ## add(itilDepotType={})",itilDepotType,e);
            return Result.error(e.getMessage());
        }
    }

	/**
	 *  编辑
	 *
	 * @param itilDepotType
	 * @return
	 */
	@AutoLog(value = "备品备库类型-编辑")
	@ApiOperation(value="备品备库类型-编辑", notes="备品备库类型-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody ItilDepotType itilDepotType) {
        log.info("ItilDepotTypeController ## edit(itilDepotType={})",itilDepotType);
        try {
            itilDepotTypeService.updateById(itilDepotType);
            return Result.OK("编辑成功!");
        } catch (Exception e) {
            log.error("ItilDepotTypeController ## edit(itilDepotType={})",itilDepotType,e);
            return Result.error(e.getMessage());
        }
    }

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "备品备库类型-通过id删除")
	@ApiOperation(value="备品备库类型-通过id删除", notes="备品备库类型-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
        try {
            log.info("ItilDepotTypeController ## edit(id={})",id);
            ItilDepotType depot = itilDepotTypeService.getById(id);
            depot.setDelflag(1);
            itilDepotTypeService.updateById(depot);
//		itilDepotTypeService.removeById(id);
            return Result.OK("删除成功!");
        } catch (Exception e) {
            log.error("ItilDepotTypeController ## edit(id={})",id,e);
            return Result.error(e.getMessage());
        }
    }

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "备品备库类型-批量删除")
	@ApiOperation(value="备品备库类型-批量删除", notes="备品备库类型-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        try {
            log.info("ItilDepotTypeController ## deleteBatch(ids={})",ids);
            this.itilDepotTypeService.removeByIds(Arrays.asList(ids.split(",")));
            return Result.OK("批量删除成功!");
        } catch (Exception e) {
            log.error("ItilDepotTypeController ## deleteBatch(ids={})",ids,e);
            return Result.error(e.getMessage());
        }
    }

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "备品备库类型-通过id查询")
	@ApiOperation(value="备品备库类型-通过id查询", notes="备品备库类型-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
        try {
            log.info("ItilDepotTypeController ## queryById(id={})",id);
            ItilDepotType itilDepotType = itilDepotTypeService.getById(id);
            if(itilDepotType==null) {
                return Result.error("未找到对应数据");
            }
            return Result.OK(itilDepotType);
        } catch (Exception e) {
            log.error("ItilDepotTypeController ## queryById(id={})",id,e);
            return Result.error(e.getMessage());
        }
    }

    /**
    * 导出excel
    *
    * @param request
    * @param itilDepotType
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ItilDepotType itilDepotType) {
        return super.exportXls(request, itilDepotType, ItilDepotType.class, "备品备库类型");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ItilDepotType.class);
    }

}
