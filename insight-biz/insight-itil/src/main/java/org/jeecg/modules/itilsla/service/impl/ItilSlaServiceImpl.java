package org.jeecg.modules.itilsla.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.itilsla.entity.ItilSla;
import org.jeecg.modules.itilsla.mapper.ItilSlaMapper;
import org.jeecg.modules.itilsla.service.IItilSlaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 服务级别协议
 * @Author: jeecg-boot
 * @Date:   2020-09-10
 * @Version: V1.0
 */
@Service
@Slf4j
public class ItilSlaServiceImpl extends ServiceImpl<ItilSlaMapper, ItilSla> implements IItilSlaService {

    @Autowired
    private ItilSlaMapper itilSlaMapper;

    @Override
    public List<ItilSla> getItilSlaList(String pathType) {
        log.info("ItilSlaServiceImpl ## getItilSlaList(pathType={})",pathType);
        return itilSlaMapper.getIiliSlaList(pathType);
    }
}
