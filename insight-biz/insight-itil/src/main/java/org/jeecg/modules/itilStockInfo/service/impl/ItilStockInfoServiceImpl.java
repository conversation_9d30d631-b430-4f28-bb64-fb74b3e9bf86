package org.jeecg.modules.itilStockInfo.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.itilStockInfo.entity.ItilDepotType;
import org.jeecg.modules.itilStockInfo.entity.ItilStockInfo;
import org.jeecg.modules.itilStockInfo.mapper.ItilDepotTypeMapper;
import org.jeecg.modules.itilStockInfo.mapper.ItilStockInfoMapper;
import org.jeecg.modules.itilStockInfo.service.IItilStockInfoService;
import org.jeecg.modules.system.entity.SysCategory;
import org.jeecg.modules.system.util.ExcelUtil;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 库存表
 * @Author: jeecg-boot
 * @Date:   2021-07-20
 * @Version: V1.0
 */
@Service
@Slf4j
public class ItilStockInfoServiceImpl extends ServiceImpl<ItilStockInfoMapper, ItilStockInfo> implements IItilStockInfoService {

    @Autowired
    private ItilStockInfoMapper itilStockInfoMapper;
    @Autowired
    private ItilDepotTypeMapper itilDepotTypeMapper;

    @Override
    public Double getMoney() {
        log.info("ItilStockInfoServiceImpl ## getMoney()");
        return itilStockInfoMapper.getMoney();
    }

    @Override
    public ItilStockInfo getOperateId(String operateId) {
        log.info("ItilStockInfoServiceImpl ## getOperateId(operateId = {})",operateId);
        return itilStockInfoMapper.getOperateId(operateId);
    }

    @Override
    public Result importSheets(MultipartFile file) {
        try {
            Workbook hssfWorkbook = ExcelUtil.getWorkBook(file);
            ImportParams params = new ImportParams();
            // 循环工作表Sheet
            SysCategory sysCategory = new SysCategory();
            int count = 2;
            for (int numSheet = 0; numSheet < hssfWorkbook.getNumberOfSheets(); numSheet++) {
                params.setTitleRows(1);
                params.setHeadRows(1);
                params.setNeedSave(true);
                List<ItilStockInfo> list = ExcelImportUtil.importExcel(file.getInputStream(), ItilStockInfo.class, params);
                log.info("list={}",list);
                log.info("org.jeecg.modules.itilStockInfo.service.impl.ItilStockInfoServiceImpl.importSheets(list={})",list);
                if (list.size() == 0) {
                    return Result.error("文件不能为空");
                }
                List<ItilStockInfo> stockInfos = new ArrayList<>();
                for (ItilStockInfo stockInfo : list) {
                    count += 1;
                    if (StringUtils.isEmpty(stockInfo.getGoodsCode()) || StringUtils.isEmpty(stockInfo.getGoodsName()) || StringUtils.isEmpty(stockInfo.getUnitCode())) {
                        return Result.error("文件导入失败，第 " + count + " 行，出现错误");
                    }
                    ItilStockInfo operateId = this.itilStockInfoMapper.getOperateId(stockInfo.getGoodsCode());
                    if (null != operateId) {
                        return Result.error("文件导入失败，第"+  count + " 行，物品编号重复!");
                    }
                    //新导入的数据，所属类目默认为（全部）
                    String goodsType = "1429648780066770946";
                    ItilDepotType itilDepotType = itilDepotTypeMapper.selectById(goodsType);
                    stockInfo.setGoodsType(itilDepotType.getId());
                    stockInfo.setGoodsTypeName(itilDepotType.getTypeName());
                    stockInfo.setStockNum(0);
                    stockInfo.setTotalMoney(0.00);
                    stockInfos.add(stockInfo);
                }
                super.saveBatch(stockInfos);
            }
            return Result.ok("文件导入成功！");
        } catch (IOException e) {
            e.printStackTrace();
            return Result.error("文件导入失败:" + e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("文件导入失败:" + e.getMessage());
        } finally {
            try {
                file.getInputStream().close();
            } catch (IOException e) {
                e.printStackTrace();
                return Result.error("文件导入失败:" + e.getMessage());
            }
        }

    }


}
