package org.jeecg.modules.itilStockInfo.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.itilStockInfo.entity.ItilDepotType;
import org.jeecg.modules.itilStockInfo.mapper.ItilDepotTypeMapper;
import org.jeecg.modules.itilStockInfo.service.IItilDepotTypeService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 备品备库类型
 * @Author: jeecg-boot
 * @Date:   2021-08-23
 * @Version: V1.0
 */
@Service
@Slf4j
public class ItilDepotTypeServiceImpl extends ServiceImpl<ItilDepotTypeMapper, ItilDepotType> implements IItilDepotTypeService {


    @Override
    public List<ItilDepotType> getPid(String id) {
        log.info("ItilDepotTypeServiceImpl ## getPid(id={})",id);
        return this.baseMapper.getpid(id);
    }
}
