package org.jeecg.modules.itilSyTargetDetail.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.itilSyTargetDetail.entity.ItilSyTargetDetail;

/**
 * @Description: 指标详情
 * @Author: jeecg-boot
 * @Date:   2021-07-05
 * @Version: V1.0
 */
public interface ItilSyTargetDetailMapper extends BaseMapper<ItilSyTargetDetail> {

    /**
     * @param id
     * @return
     */
    ItilSyTargetDetail getTarget(@Param("id") String id);
}
