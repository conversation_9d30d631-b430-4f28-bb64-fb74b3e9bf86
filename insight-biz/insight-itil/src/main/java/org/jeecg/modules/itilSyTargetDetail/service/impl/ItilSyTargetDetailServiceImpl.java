package org.jeecg.modules.itilSyTargetDetail.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.itilSyTargetDetail.entity.ItilSyTargetDetail;
import org.jeecg.modules.itilSyTargetDetail.mapper.ItilSyTargetDetailMapper;
import org.jeecg.modules.itilSyTargetDetail.service.IItilSyTargetDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description: 指标详情
 * @Author: jeecg-boot
 * @Date:   2021-07-05
 * @Version: V1.0
 */
@Service
@Slf4j
public class ItilSyTargetDetailServiceImpl extends ServiceImpl<ItilSyTargetDetailMapper, ItilSyTargetDetail> implements IItilSyTargetDetailService {

    @Autowired
    private ItilSyTargetDetailMapper itilSyTargetDetailMapper;

    @Override
    public ItilSyTargetDetail getTarget(String id) {
        log.info("ItilSyTargetDetailServiceImpl ## getTarget(id = {})",id);
        return itilSyTargetDetailMapper.getTarget(id);
    }
}
