package org.jeecg.modules.reportdetails.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.reportdetails.entity.ItilReportDetailsInfo;
import org.jeecg.modules.reportdetails.service.IItilReportDetailsInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: 服务报告详情表
 * @Author: jeecg-boot
 * @Date:   2021-07-02
 * @Version: V1.0
 */
@Api(tags="服务报告详情表")
@RestController
@RequestMapping("/reportdetails/itilReportDetailsInfo")
@Slf4j
public class ItilReportDetailsInfoController extends JeecgController<ItilReportDetailsInfo, IItilReportDetailsInfoService> {
	@Autowired
	private IItilReportDetailsInfoService itilReportDetailsInfoService;

	/**
	 * 分页列表查询
	 *
	 * @param itilReportDetailsInfo
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "服务报告详情表-分页列表查询")
	@ApiOperation(value="服务报告详情表-分页列表查询", notes="服务报告详情表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(ItilReportDetailsInfo itilReportDetailsInfo,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
	    log.info("ItilReportDetailsInfoController ## queryPageList(itilReportDetailsInfo = {})",itilReportDetailsInfo);
        try {
            QueryWrapper<ItilReportDetailsInfo> queryWrapper = QueryGenerator.initQueryWrapper(itilReportDetailsInfo, req.getParameterMap());
            Page<ItilReportDetailsInfo> page = new Page<ItilReportDetailsInfo>(pageNo, pageSize);
            IPage<ItilReportDetailsInfo> pageList = itilReportDetailsInfoService.page(page, queryWrapper);
            return Result.OK(pageList);
        } catch (Exception e) {
            log.error("ItilReportDetailsInfoController ## queryPageList(itilReportDetailsInfo = {})",itilReportDetailsInfo,e);
            return Result.error(e.getMessage());
        }
    }

	/**
	 *   添加
	 *
	 * @param itilReportDetailsInfo
	 * @return
	 */
	@AutoLog(value = "服务报告详情表-添加")
	@ApiOperation(value="服务报告详情表-添加", notes="服务报告详情表-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody ItilReportDetailsInfo itilReportDetailsInfo) {
        try {
            log.info("ItilReportDetailsInfoController ## add(itilReportDetailsInfo = {})",itilReportDetailsInfo);
            String mess = itilReportDetailsInfoService.saveReportDetails(itilReportDetailsInfo);
            return Result.OK(mess);
        } catch (Exception e) {
            log.error("ItilReportDetailsInfoController ## add(itilReportDetailsInfo = {})",itilReportDetailsInfo,e);
            return Result.error(e.getMessage());
        }
    }

	/**
	 *  编辑
	 *
	 * @param itilReportDetailsInfo
	 * @return
	 */
	@AutoLog(value = "服务报告详情表-编辑")
	@ApiOperation(value="服务报告详情表-编辑", notes="服务报告详情表-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody ItilReportDetailsInfo itilReportDetailsInfo) {
        try {
            log.info("ItilReportDetailsInfoController ## edit(itilReportDetailsInfo = {})",itilReportDetailsInfo);
            itilReportDetailsInfoService.updateById(itilReportDetailsInfo);
            return Result.OK("编辑成功!");
        } catch (Exception e) {
            log.error("ItilReportDetailsInfoController ## edit(itilReportDetailsInfo = {})",itilReportDetailsInfo,e);
            return Result.error(e.getMessage());
        }
    }

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "服务报告详情表-通过id删除")
	@ApiOperation(value="服务报告详情表-通过id删除", notes="服务报告详情表-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
        try {
            log.info("ItilReportDetailsInfoController ## delete(id = {})",id);
            itilReportDetailsInfoService.removeById(id);
            return Result.OK("删除成功!");
        } catch (Exception e) {
            log.error("ItilReportDetailsInfoController ## delete(id = {})",id,e);
            return Result.error(e.getMessage());
        }
    }

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "服务报告详情表-批量删除")
	@ApiOperation(value="服务报告详情表-批量删除", notes="服务报告详情表-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        try {
            log.info("ItilReportDetailsInfoController ## deleteBatch(ids = {})",ids);
            this.itilReportDetailsInfoService.removeByIds(Arrays.asList(ids.split(",")));
            return Result.OK("批量删除成功!");
        } catch (Exception e) {
            log.error("ItilReportDetailsInfoController ## deleteBatch(ids = {})",ids,e);
            return Result.error(e.getMessage());
        }
    }

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "服务报告详情表-通过id查询")
	@ApiOperation(value="服务报告详情表-通过id查询", notes="服务报告详情表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
	    log.info("ItilReportDetailsInfoController ## queryById(id = {})",id);
		ItilReportDetailsInfo itilReportDetailsInfo = itilReportDetailsInfoService.getById(id);
		if(itilReportDetailsInfo==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(itilReportDetailsInfo);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param itilReportDetailsInfo
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ItilReportDetailsInfo itilReportDetailsInfo) {
        return super.exportXls(request, itilReportDetailsInfo, ItilReportDetailsInfo.class, "服务报告详情表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ItilReportDetailsInfo.class);
    }


     @AutoLog(value = "服务报告详情表-指标情况")
     @ApiOperation(value="服务报告详情表-指标情况", notes="服务报告详情表-指标情况")
     @GetMapping(value = "/getList")
     public Result<?> getList(ItilReportDetailsInfo itilReportDetailsInfo) {
         try {
             log.info("ItilReportDetailsInfoController ## getList(itilReportDetailsInfo = {})",itilReportDetailsInfo);
             return Result.OK(itilReportDetailsInfoService.getList(itilReportDetailsInfo));
         } catch (Exception e) {
             log.error("ItilReportDetailsInfoController ## getList(itilReportDetailsInfo = {})",itilReportDetailsInfo,e);
             return Result.error(e.getMessage());
         }
     }

}
