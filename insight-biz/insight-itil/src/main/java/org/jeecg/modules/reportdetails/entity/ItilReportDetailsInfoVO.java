package org.jeecg.modules.reportdetails.entity;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @title: ItilReportDetailsInfoVO
 * @projectName jeecg-boot-parent
 * @description: TODO
 * @date 2021/7/6-14:54
 */
@Data
public class ItilReportDetailsInfoVO {
    //指标项
    private java.lang.String indexItemText;
    //指标描述
    private java.lang.String indexDescripText;
    //服务时间
    private java.lang.String serviceTime;
    //服务方式
    private java.lang.String serviceModeText;
    //服务次数
    private java.lang.Integer serviceCount;
    //附件名称
    private java.lang.String fileName;
    //附件地址
    private java.lang.String fileUrl;
    //备注信息
    private java.lang.String remarks;

    private List<ItilReportDetailsInfoVO> children = new ArrayList<>();




}
