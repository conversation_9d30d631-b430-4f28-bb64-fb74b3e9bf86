package org.jeecg.modules.knowledge.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.knowledge.entity.UmpKnowledge;
import org.jeecg.modules.knowledge.service.IUmpKnowledgeService;
import com.yuanqiao.insight.acore.system.service.ISysDictService;
import org.jeecg.modules.umpPwdManage.entity.UmpPwdManage;
import org.jeecg.modules.umpPwdManage.service.IUmpPwdManageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 事件、问题知识库
 * @Author: jeecg-boot
 * @Date: 2020-09-18
 * @Version: V1.0
 */
@Api(tags = "事件、问题知识库")
@RestController
@RequestMapping("/knowledge")
@Slf4j
public class UmpKnowledgeController extends JeecgController<UmpKnowledge, IUmpKnowledgeService> {
    @Autowired
    private IUmpKnowledgeService umpKnowledgeService;
    @Autowired
    private ISysDictService sysDictService;
    @Autowired
    private IUmpPwdManageService umpPwdManageService;

    /**
     * 分页列表查询
     *
     * @param umpKnowledge
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "事件、问题知识库-分页列表查询")
    @ApiOperation(value = "事件、问题知识库-分页列表查询", notes = "事件、问题知识库-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(UmpKnowledge umpKnowledge,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        log.info("UmpKnowledgeController ## queryPageList(umpKnowledge = {})",umpKnowledge);
        try {
            QueryWrapper<UmpKnowledge> queryWrapper = QueryGenerator.initQueryWrapper(umpKnowledge, req.getParameterMap());
            queryWrapper.eq("record_type",2);
            Page<UmpKnowledge> page = new Page<UmpKnowledge>(pageNo, pageSize);
            IPage<UmpKnowledge> pageList = umpKnowledgeService.page(page, queryWrapper);
            return Result.ok(pageList);
        } catch (Exception e) {
            log.info("UmpKnowledgeController ## queryPageList(umpKnowledge = {})",umpKnowledge,e);
            return Result.error(e.getMessage());
        }
    }
    /**
     * 事件分页列表查询
     *
     * @param umpKnowledge
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "事件、问题知识库-分页列表查询")
    @ApiOperation(value = "事件、问题知识库-分页列表查询", notes = "事件、问题知识库-分页列表查询")
    @GetMapping(value = "/eventList")
    public Result<?> queryPageevenList(UmpKnowledge umpKnowledge,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        try {
            log.info("UmpKnowledgeController ## queryPageevenList(umpKnowledge = {})",umpKnowledge);
            QueryWrapper<UmpKnowledge> queryWrapper = QueryGenerator.initQueryWrapper(umpKnowledge, req.getParameterMap());
            queryWrapper.eq("record_type",1);
            Page<UmpKnowledge> page = new Page<UmpKnowledge>(pageNo, pageSize);
            IPage<UmpKnowledge> pageList = umpKnowledgeService.page(page, queryWrapper);
            return Result.ok(pageList);
        } catch (Exception e) {
            log.info("UmpKnowledgeController ## queryPageevenList(umpKnowledge = {})",umpKnowledge,e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 添加
     *
     * @param umpKnowledge
     * @return
     */
    @AutoLog(value = "事件、问题知识库-添加")
    @ApiOperation(value = "事件、问题知识库-添加", notes = "事件、问题知识库-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody UmpKnowledge umpKnowledge) {
        try {
            log.info("UmpKnowledgeController ## add(umpKnowledge = {})",umpKnowledge);
            umpKnowledge.setSearchesNum(0);
            int count = umpKnowledgeService.count(new QueryWrapper<UmpKnowledge>().lambda().eq(UmpKnowledge::getTitle, umpKnowledge.getTitle()));
            if (count > 0) {
                return Result.error("已添加到知识库");
            }
            umpKnowledgeService.save(umpKnowledge);
            return Result.ok("添加成功！");
        } catch (Exception e) {
            log.info("UmpKnowledgeController ## add(umpKnowledge = {})",umpKnowledge,e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 编辑
     *
     * @param umpKnowledge
     * @return
     */
    @AutoLog(value = "事件、问题知识库-编辑")
    @ApiOperation(value = "事件、问题知识库-编辑", notes = "事件、问题知识库-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody UmpKnowledge umpKnowledge) {
        try {
            log.info("UmpKnowledgeController ## edit(umpKnowledge = {})",umpKnowledge);
            umpKnowledgeService.updateById(umpKnowledge);
            return Result.ok("编辑成功!");
        } catch (Exception e) {
            log.info("UmpKnowledgeController ## edit(umpKnowledge = {})",umpKnowledge,e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "事件、问题知识库-通过id删除")
    @ApiOperation(value = "事件、问题知识库-通过id删除", notes = "事件、问题知识库-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        log.info("UmpKnowledgeController ## delete(id = {})",id);
        umpKnowledgeService.removeById(id);
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "事件、问题知识库-批量删除")
    @ApiOperation(value = "事件、问题知识库-批量删除", notes = "事件、问题知识库-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        log.info("UmpKnowledgeController ## deleteBatch(ids = {})",ids);
        this.umpKnowledgeService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "事件、问题知识库-通过id查询")
    @ApiOperation(value = "事件、问题知识库-通过id查询", notes = "事件、问题知识库-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        log.info("UmpKnowledgeController ## queryById(id = {})",id);
        UmpKnowledge umpKnowledge = umpKnowledgeService.getById(id);
        if (umpKnowledge == null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(umpKnowledge);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param umpKnowledge
     */
    @AutoLog(value = "事件、问题知识库-导出excel")
    @RequestMapping(value = "/exportXls")
//    public ModelAndView exportXls(HttpServletRequest request, UmpKnowledge umpKnowledge) {
//        return super.exportXls(request, umpKnowledge, UmpKnowledge.class, "事件、问题知识库");
//    }
    public void exportXls(HttpServletRequest request,HttpServletResponse response, UmpKnowledge umpKnowledge) {
        UmpPwdManage zip = umpPwdManageService.getZip();
        super.exportXlsZip(request,response, umpKnowledge, UmpKnowledge.class, "事件、问题知识库",zip.getZipPwd(),zip.getIsEncry());
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, UmpKnowledge.class);
    }

    /**
     * 获取知识库热搜前5条
     *
     * @return
     */
    @RequestMapping(value = "/getHotSearch", method = RequestMethod.GET)
    public Result<?> getHotSearch(){
        log.info("UmpKnowledgeController ## getHotSearch()");
        QueryWrapper<UmpKnowledge> queryWrapper = new QueryWrapper<>();
        Integer[] recordType = new Integer[]{1,2};
        queryWrapper.in("record_type",Arrays.asList(recordType));
        queryWrapper.orderByDesc("searches_num");
        List<UmpKnowledge> list = umpKnowledgeService.list(queryWrapper).stream().sorted(Comparator.comparing(UmpKnowledge::getSearchesNum).reversed()).limit(5).collect(Collectors.toList());
        return Result.ok(list);
    }

    /**
     * 模糊查询
     *
     * @param umpKnowledge
     * @return
     */
    @RequestMapping(value = "/getFuzzyQuery", method = RequestMethod.GET)
    public Result<?> getFuzzyQuery(UmpKnowledge umpKnowledge){
        log.info("UmpKnowledgeController ## getFuzzyQuery(umpKnowledge={})",umpKnowledge);
        List<UmpKnowledge> list = umpKnowledgeService.getFuzzyQueryByTitle(umpKnowledge.getTitle());
        return Result.ok(list);
    }

    /**
     * 修改知识库的搜索次数
     * @param umpKnowledge
     * @return
     */
    @RequestMapping(value="/updateSearchesNum",method = RequestMethod.POST)
    public Result<?> updateSearchesNum(@RequestBody UmpKnowledge umpKnowledge){
        log.info("UmpKnowledgeController ## updateSearchesNum(umpKnowledge={})",umpKnowledge);
        umpKnowledge = umpKnowledgeService.getById(umpKnowledge);
        umpKnowledge.setSearchesNum(umpKnowledge.getSearchesNum()+1);
        umpKnowledgeService.updateById(umpKnowledge);
        return Result.OK();
    }


}
