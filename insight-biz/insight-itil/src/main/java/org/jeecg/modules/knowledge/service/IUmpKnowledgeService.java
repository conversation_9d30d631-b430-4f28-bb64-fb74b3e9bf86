package org.jeecg.modules.knowledge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.knowledge.entity.UmpKnowledge;

import java.util.List;

/**
 * @Description: 事件、问题知识库
 * @Author: jeecg-boot
 * @Date:   2020-09-18
 * @Version: V1.0
 */
public interface IUmpKnowledgeService extends IService<UmpKnowledge> {
    /**
     * 模糊查询
     *
     * @param title 标题
     * @return
     */
    List<UmpKnowledge> getFuzzyQueryByTitle(String title);

}
