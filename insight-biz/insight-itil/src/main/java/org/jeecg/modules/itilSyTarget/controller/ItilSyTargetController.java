package org.jeecg.modules.itilSyTarget.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.itilSyTarget.entity.ItilSyTarget;
import org.jeecg.modules.itilSyTarget.service.IItilSyTargetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 指标项
 * @Author: jeecg-boot
 * @Date:   2021-07-05
 * @Version: V1.0
 */
@Api(tags="指标项")
@RestController
@RequestMapping("/itilSyTarget/itilSyTarget")
@Slf4j
public class ItilSyTargetController extends JeecgController<ItilSyTarget, IItilSyTargetService> {
	@Autowired
	private IItilSyTargetService itilSyTargetService;

	/**
	 * 分页列表查询
	 *
	 * @param itilSyTarget
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "指标项-分页列表查询")
	@ApiOperation(value="指标项-分页列表查询", notes="指标项-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(ItilSyTarget itilSyTarget,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        try {
            log.info("ItilSyTargetController ## queryPageList(itilSyTarget = {})",itilSyTarget);
            QueryWrapper<ItilSyTarget> queryWrapper = QueryGenerator.initQueryWrapper(itilSyTarget, req.getParameterMap());
            Page<ItilSyTarget> page = new Page<ItilSyTarget>(pageNo, pageSize);
            IPage<ItilSyTarget> pageList = itilSyTargetService.page(page, queryWrapper);
            return Result.OK(pageList);
        } catch (Exception e) {
            log.error("ItilSyTargetController ## queryPageList(itilSyTarget = {})",itilSyTarget,e);
            return Result.error(e.getMessage());
        }
    }

	/**
	 *   添加
	 *
	 * @param itilSyTarget
	 * @return
	 */
	@AutoLog(value = "指标项-添加")
	@ApiOperation(value="指标项-添加", notes="指标项-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody ItilSyTarget itilSyTarget) {
        try {
            log.info("ItilSyTargetController ## add(itilSyTarget = {})",itilSyTarget);
            itilSyTargetService.save(itilSyTarget);
            return Result.OK("添加成功！");
        } catch (Exception e) {
            log.error("ItilSyTargetController ## add(itilSyTarget = {})",itilSyTarget,e);
            return Result.error(e.getMessage());
        }
    }


    @AutoLog(value = "指标项-通过项目id获取配置项ID")
    @ApiOperation(value="指标项-通过项目id获取配置项ID", notes="指标项-通过项目id获取配置项ID")
    @GetMapping(value = "/indexItemList")
    public Result<?> getIndexItemList(String projectId){

        log.info("ItilSyTargetController ## getIndexItemList(projectId = {})",projectId);
        try {
            if(StringUtils.isBlank(projectId)){
return Result.error("请输入项目ID");
}
            List<ItilSyTarget> targets = itilSyTargetService.getTargetListByProjectId(projectId);
            return Result.OK(targets);
        } catch (Exception e) {
            log.error("ItilSyTargetController ## getIndexItemList(projectId = {})",projectId,e);
            return Result.error(e.getMessage());
        }

    }
	/**
	 *  编辑
	 *
	 * @param itilSyTarget
	 * @return
	 */
	@AutoLog(value = "指标项-编辑")
	@ApiOperation(value="指标项-编辑", notes="指标项-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody ItilSyTarget itilSyTarget) {
        log.info("ItilSyTargetController ## edit(itilSyTarget = {})",itilSyTarget  );
		itilSyTargetService.updateById(itilSyTarget);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "指标项-通过id删除")
	@ApiOperation(value="指标项-通过id删除", notes="指标项-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
        log.info("ItilSyTargetController ## edit(id = {})",id);
		itilSyTargetService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "指标项-批量删除")
	@ApiOperation(value="指标项-批量删除", notes="指标项-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
	    log.info("ItilSyTargetController ## deleteBatch(ids = {})",ids);
		this.itilSyTargetService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "指标项-通过id查询")
	@ApiOperation(value="指标项-通过id查询", notes="指标项-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
        log.info("ItilSyTargetController ## queryById(id = {})",id);
		ItilSyTarget itilSyTarget = itilSyTargetService.getById(id);
		if(itilSyTarget==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(itilSyTarget);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param itilSyTarget
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ItilSyTarget itilSyTarget) {
        return super.exportXls(request, itilSyTarget, ItilSyTarget.class, "指标项");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ItilSyTarget.class);
    }

    @AutoLog(value = "指标项-列表查询")
    @ApiOperation(value="指标项-列表查询", notes="指标项-列表查询")
    @GetMapping(value = "/getList")
    public Result<?> queryList(ItilSyTarget itilSyTarget,
                                   HttpServletRequest req) {
        log.info("ItilSyTargetController ## queryList(itilSyTarget = {})",itilSyTarget);
        QueryWrapper<ItilSyTarget> queryWrapper = QueryGenerator.initQueryWrapper(itilSyTarget, req.getParameterMap());
        queryWrapper.eq("delflag",0);
        List<ItilSyTarget> list = itilSyTargetService.list(queryWrapper);
        return Result.OK(list);
    }

}
