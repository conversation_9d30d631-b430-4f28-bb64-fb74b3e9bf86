package org.jeecg.modules.itilOutDepot.util;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.itilInDepot.entity.ItilInDepotVo;
import org.jeecg.modules.itilOutDepot.entity.ItilOutDepot;
import org.jeecg.modules.itilOutDepot.entity.OutInfoVO;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @title: SaveReportPdfUtil
 * @projectName jeecg-boot-parent
 * @description: TODO
 * @date 2021/7/7-15:30
 */
@Slf4j
public class SaveReportPdfUtil {
    public void createPDF(ItilOutDepot itilOutDepot, OutInfoVO infoVO, List<ItilInDepotVo> itilInDepotVos) throws Exception{
        FileOutputStream fos = null;
        try {
            // 设置纸张大小和背景色
            Rectangle rect = new Rectangle(PageSize.A4);
            // 创建文档实例
            Document doc = new Document(rect);
            // 添加中文字体
            BaseFont bfChinese= BaseFont.createFont("STSong-Light","UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

            // 设置字体样式
            Font textFont = new Font(bfChinese, 11, Font.NORMAL, BaseColor.BLACK); // 正常
            Font lineFont = new Font(bfChinese, 11, Font.NORMAL,BaseColor.BLACK); // 正常
            Font firsetTitleFont = new Font(bfChinese, 22, Font.NORMAL,BaseColor.BLACK); // 一级标题
            Font secondTitleFont = new Font(bfChinese, 15, Font.BOLD,BaseColor.BLACK); // 二级标题
            Font textFont2 = new Font(bfChinese, 11, Font.BOLD, BaseColor.BLACK); // 正常

            //// TODO: 2021/7/29
            // 将组织得到的报告内容写入pdf文件resource       resource/inRoutePath/RK-20210805-0009.pdf
            File file = new File(itilOutDepot.getOutPath());
            System.out.println("----------"+file);
            if (!file.exists()){
                file.createNewFile();
            }

            fos=new FileOutputStream(file);
            // 创建输出流
            PdfWriter.getInstance(doc, fos);
            doc.open();
            doc.newPage();
            //表头
            //PDF文档内容开始
            // 标题及下划线
            Paragraph p1 = new Paragraph();
            p1 = new Paragraph("出库单", firsetTitleFont);
            p1.setLeading(10);
            p1.setAlignment(Element.ALIGN_CENTER);
            doc.add(p1);


            p1 = new Paragraph("出库时间:"+infoVO.getOutTime(),textFont);
            p1.setLeading(30);
            p1.setAlignment(Element.ALIGN_LEFT);
            doc.add(p1);

            p1 = new Paragraph("单号:"+infoVO.getOutCode(),textFont);
            p1.setLeading(0);
            p1.setAlignment(Element.ALIGN_RIGHT);
            doc.add(p1);

            p1 = new Paragraph("出库类型:"+infoVO.getOutType(),textFont);
            p1.setLeading(30);
            p1.setAlignment(Element.ALIGN_LEFT);
            doc.add(p1);

            if (infoVO.getOutType().equals("内部领用")){
                p1 = new Paragraph("领用人:"+infoVO.getReceiver(),textFont);
                p1.setLeading(0);
                p1.setAlignment(Element.ALIGN_JUSTIFIED);
                p1.setIndentationLeft(150);
                doc.add(p1);


                p1 = new Paragraph("联系电话:"+infoVO.getReceiverPhone(),textFont);
                p1.setLeading(0);
                p1.setAlignment(Element.ALIGN_JUSTIFIED);
                p1.setIndentationLeft(270);
                doc.add(p1);
            }else if(infoVO.getOutType().equals("退货出库")){
                p1 = new Paragraph("供应商:"+infoVO.getSupplier(),textFont);
                p1.setLeading(0);
                p1.setAlignment(Element.ALIGN_JUSTIFIED);
                p1.setIndentationLeft(150);
                doc.add(p1);

                p1 = new Paragraph("联系人:"+infoVO.getSupplierName(),textFont);
                p1.setLeading(0);
                p1.setAlignment(Element.ALIGN_JUSTIFIED);
                p1.setIndentationLeft(300);
                doc.add(p1);

                p1 = new Paragraph("联系电话:"+infoVO.getSupplierPhone(),textFont);
                p1.setLeading(0);
                p1.setAlignment(Element.ALIGN_RIGHT);
                doc.add(p1);
            }else if (infoVO.getOutType().equals("其他出库")){
                p1 = new Paragraph("出库对象:"+infoVO.getOthers(),textFont);
                p1.setLeading(0);
                p1.setAlignment(Element.ALIGN_JUSTIFIED);
                p1.setIndentationLeft(150);
                doc.add(p1);
            }

            //物品数据
            //创建指标详情列表
            if(null != itilInDepotVos && 0 < itilInDepotVos.size()){
                createItilReportDetailsTable(doc,textFont, secondTitleFont,itilInDepotVos,infoVO);
            }

            doc.close();
//        } catch (DocumentException e) {
//            log.error("AIRGenetaterUtil类的createPDF方法发生异常", e);
        } catch (IOException e) {
            throw new Exception("将入库单内容写入本地PDF文件过程中发生异常！", e);
        }finally {
            if (fos != null){
                try {
                    fos.flush();
                    fos.close();
                } catch (IOException e) {
                }
                fos = null;
            }
        }
    }


    /**
     *
     * @param doc
     * @param textFont
     * @param secondTitleFont
     * @param itilInDepotVos
     * @throws Exception
     */
    private void createItilReportDetailsTable(Document doc,Font textFont, Font secondTitleFont,List<ItilInDepotVo> itilInDepotVos,OutInfoVO infoVO) throws Exception {
        PdfPTable detailsTable = new PdfPTable(9);
        detailsTable.setSpacingBefore(20f);
        detailsTable.setTotalWidth(new float[] {30,70,60,60,60,60,60,60,60}); // 设置列宽
        detailsTable.setLockedWidth(true); // 锁定列宽
        String title[] = {"序号","物品编号","物品名称","当前库存","单位","数量","单价","金额","备注"};
        // 添加中文字体
        BaseFont bfChinese =BaseFont.createFont("STSong-Light","UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        // 设置字体样式
        Font font = new Font(bfChinese, 11, Font.NORMAL,BaseColor.BLACK); // 正常

        BaseColor color = BaseColor.WHITE;
       try {
           for(int j=0;j<9;j++){
               color = new BaseColor(46,73,88);
               createDetailsCell2(color,title[j],font,detailsTable,1);
           }
       }catch (Exception e){
           e.printStackTrace();
           System.out.println("============================");
       }
        Double money = 0.0;
        for (ItilInDepotVo itilInDepotVo:itilInDepotVos) {
            createDetailsCell2(color,itilInDepotVo.getOlderNum(),font,detailsTable,1);
            createDetailsCell2(color,itilInDepotVo.getGoodsCode(),font,detailsTable,1);
            createDetailsCell2(color,itilInDepotVo.getGoodsName(),font,detailsTable,1);
            createDetailsCell2(color,String.valueOf(itilInDepotVo.getGoodsNum()),font,detailsTable,1);
            createDetailsCell2(color,itilInDepotVo.getGoodsUnit(),font,detailsTable,1);
            createDetailsCell2(color,String.valueOf(itilInDepotVo.getInGoodsNum()),font,detailsTable,1);
            createDetailsCell2(color,"￥"+String.valueOf(itilInDepotVo.getGoodsPrice()),font,detailsTable,1);
            createDetailsCell2(color,"￥"+String.valueOf(itilInDepotVo.getMoneys()),font,detailsTable,1);
            createDetailsCell2(color,itilInDepotVo.getRemarks(),font,detailsTable,1);
            money+=itilInDepotVo.getMoneys();
        }
        createDetailsCell2(color,"总金额: ￥"+money+"元",font,detailsTable,9);
        doc.add(detailsTable);


        Paragraph p1 = new Paragraph();
        p1 = new Paragraph("出库人:"+infoVO.getOutBy(), textFont);
        p1.setLeading(30);
        p1.setAlignment(Element.ALIGN_LEFT);
        doc.add(p1);

        p1 = new Paragraph("联系电话:"+infoVO.getOutByPhone(),textFont);
        p1.setLeading(0);
        p1.setAlignment(Element.ALIGN_JUSTIFIED);
        p1.setIndentationLeft(150);
        doc.add(p1);


        p1 = new Paragraph("签字/盖章:",textFont);
        p1.setLeading(0);
        p1.setAlignment(Element.ALIGN_JUSTIFIED);
        p1.setIndentationLeft(320);
        doc.add(p1);

        p1 = new Paragraph("备注信息:"+infoVO.getRemarks(),textFont);
        p1.setLeading(30);
        p1.setAlignment(Element.ALIGN_LEFT);
        doc.add(p1);


    }


    /**
     * 添加单元格PDF
     * @param color 单元格背景色
     * @param key 单元格内容
     * @param font 单元格字体
     * @param table 表格
     * @param n 合并单元格数
     */
    private void createDetailsCell2(BaseColor color,String key,Font font,PdfPTable table,int n){
        PdfPCell cell = new PdfPCell(new Phrase(key, font));
        cell.setMinimumHeight(15); // 设置单元格高度
        cell.setUseAscender(true); // 设置可以居中
        cell.setHorizontalAlignment(Element.ALIGN_CENTER); // 设置水平居中
        cell.setVerticalAlignment(Element.ALIGN_MIDDLE); // 设置垂直居中
        cell.setColspan(n);
        table.addCell(cell);
    }


}
