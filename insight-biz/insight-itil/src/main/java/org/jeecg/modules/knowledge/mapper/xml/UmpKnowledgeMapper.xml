<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.knowledge.mapper.UmpKnowledgeMapper">

    <select id="getFuzzyQueryByTitle" resultType="org.jeecg.modules.knowledge.entity.UmpKnowledge">
      select * from ump_knowledge where title like '%${title}%' and record_type in (1,2)
    </select>
</mapper>
