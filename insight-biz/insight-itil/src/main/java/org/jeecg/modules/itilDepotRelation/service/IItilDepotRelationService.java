package org.jeecg.modules.itilDepotRelation.service;


import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.itilDepotRelation.entity.ItilDepotRelation;

/**
 * @Description: 库存关系表
 * @Author: jeecg-boot
 * @Date:   2021-07-20
 * @Version: V1.0
 */
public interface IItilDepotRelationService extends IService<ItilDepotRelation> {

    ItilDepotRelation getInfo(String inId, String stockId,String type);
}
