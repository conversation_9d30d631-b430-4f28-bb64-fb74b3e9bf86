package com.yuanqiao.insight.monitoring.modules.device.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.cmdb.modules.assetscategory.entity.AssetsCategory;
import com.yuanqiao.insight.cmdb.modules.assetscategory.service.IAssetsCategoryService;
import com.yuanqiao.insight.common.entity.SelectTree;
import com.yuanqiao.insight.monitoring.modules.device.entity.ProductCategory;
import com.yuanqiao.insight.monitoring.modules.device.service.IProductCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 设备分类
 * @Author: jeecg-boot
 * @Date: 2021-03-01
 * @Version: V1.0
 */
@Api(tags = "设备分类")
@RestController
@RequestMapping("/device/deviceCategory")
@Slf4j
public class ProductCategoryController extends JeecgController<ProductCategory, IProductCategoryService> {
    @Autowired
    private IProductCategoryService deviceCategoryService;
    @Autowired
    private IAssetsCategoryService assetsCategoryService;

    @AutoLog(value = "设备分类-设备分类树")
    @ApiOperation(value = "设备分类-分页列表查询", notes = "设备分类-分页列表查询")
    @GetMapping(value = "/selectTree")
    Result<List> getSelectTree() {
        Result<List> result = new Result<>();
        List<ProductCategory> list = deviceCategoryService.list();
        List<SelectTree> selectTreeList = new ArrayList();
        setSelectTree(list, selectTreeList);
        result.setResult(selectTreeList);
        return result;
    }

    private void setSelectTree(List<ProductCategory> list, List<SelectTree> selectTreeList) {

        for (ProductCategory deviceCategory : list) {
            if ("0".equals(deviceCategory.getPid())) {
                SelectTree selectTree = new SelectTree();
                selectTree.setKey(deviceCategory.getId());
                selectTree.setTitle(deviceCategory.getName());
                selectTree.setValue(deviceCategory.getId());
                selectTreeList.add(selectTree);
                setChildrenSelectTree(list, selectTree);
            }
        }
    }

    private void setChildrenSelectTree(List<ProductCategory> list, SelectTree selectTree) {
        for (ProductCategory deviceCategory : list) {
            if (selectTree.getValue().equals(deviceCategory.getPid())) {
                SelectTree child = new SelectTree();
                child.setKey(deviceCategory.getId());
                child.setTitle(deviceCategory.getName());
                child.setValue(deviceCategory.getId());
                selectTree.getChildren().add(child);
                setChildrenSelectTree(list, child);
            }
        }
    }


    /**
     * 分页列表查询
     *
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
	/*@AutoLog(value = "设备分类-分页列表查询")
	@ApiOperation(value="设备分类-分页列表查询", notes="设备分类-分页列表查询")
	@GetMapping(value = "/rootList")
	public Result<?> queryPageList(ProductCategory deviceCategory,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		String hasQuery = req.getParameter("hasQuery");
        if(hasQuery != null && "true".equals(hasQuery)){
            QueryWrapper<ProductCategory> queryWrapper =  QueryGenerator.initQueryWrapper(deviceCategory, req.getParameterMap());
            List<ProductCategory> list = deviceCategoryService.queryTreeListNoPage(queryWrapper);
            IPage<ProductCategory> pageList = new Page<>(1, 10, list.size());
            pageList.setRecords(list);
            return Result.OK(pageList);
        }else{
            String parentId = deviceCategory.getPid();
            if (oConvertUtils.isEmpty(parentId)) {
                parentId = "0";
            }
            deviceCategory.setPid(null);
            QueryWrapper<ProductCategory> queryWrapper = QueryGenerator.initQueryWrapper(deviceCategory, req.getParameterMap());
            // 使用 eq 防止模糊查询
            queryWrapper.eq("pid", parentId);
            Page<ProductCategory> page = new Page<ProductCategory>(pageNo, pageSize);
            IPage<ProductCategory> pageList = deviceCategoryService.page(page, queryWrapper);
            return Result.OK(pageList);
        }
	}*/

    /**
     * 分页列表查询
     *
     * @param assetsCategory
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "资产类型-分页列表查询")
    @ApiOperation(value = "资产类型-分页列表查询", notes = "资产类型-分页列表查询")
    @GetMapping(value = "/rootList")
    public Result<?> queryPageList(AssetsCategory assetsCategory,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        /*QueryWrapper<AssetsCategory> queryWrapper = QueryGenerator.initQueryWrapper(assetsCategory, req.getParameterMap());
        queryWrapper.eq("is_monitorable", 1);
        List<AssetsCategory> list = assetsCategoryService.queryIsMonitorable(queryWrapper);

        IPage<AssetsCategory> pageList = new Page<>(pageNo, pageSize, list.size());
        pageList.setRecords(list);
        return Result.OK(pageList);*/

        String hasQuery = req.getParameter("hasQuery");
        if("true".equals(hasQuery)){
            QueryWrapper<AssetsCategory> queryWrapper =  QueryGenerator.initQueryWrapper(assetsCategory, req.getParameterMap());
            queryWrapper.eq("delflag",0);
            queryWrapper.eq("is_monitorable", 1);
            List<AssetsCategory> list = assetsCategoryService.queryTreeListNoPage(queryWrapper);
            IPage<AssetsCategory> pageList = new Page<>(1, 10, list.size());
            pageList.setRecords(list);
            return Result.OK(pageList);
        }else{
            String parentId = assetsCategory.getParentId();
            if (oConvertUtils.isEmpty(parentId)) {
                parentId = "0";
            }
            assetsCategory.setParentId(null);
            QueryWrapper<AssetsCategory> queryWrapper = QueryGenerator.initQueryWrapper(assetsCategory, req.getParameterMap());
            // 使用 eq 防止模糊查询
            queryWrapper.eq("parent_id", parentId);
            Page<AssetsCategory> page = new Page<AssetsCategory>(pageNo, pageSize);
            IPage<AssetsCategory> pageList = assetsCategoryService.page(page, queryWrapper);
            return Result.OK(pageList);
        }


    }


    /**
     * 获取子数据
     *
     * @param deviceCategory
     * @param req
     * @return
     */
    @AutoLog(value = "设备分类-获取子数据")
    @ApiOperation(value = "设备分类-获取子数据", notes = "设备分类-获取子数据")
    @GetMapping(value = "/childList")
    public Result<?> queryPageList(ProductCategory deviceCategory, HttpServletRequest req) {
        QueryWrapper<ProductCategory> queryWrapper = QueryGenerator.initQueryWrapper(deviceCategory, req.getParameterMap());
        List<ProductCategory> list = deviceCategoryService.list(queryWrapper);
        IPage<ProductCategory> pageList = new Page<>(1, 10, list.size());
        pageList.setRecords(list);
        return Result.OK(pageList);
    }

    /**
     * 批量查询子节点
     *
     * @param parentIds 父ID（多个采用半角逗号分割）
     * @param parentIds
     * @return
     */
    @AutoLog(value = "设备分类-批量获取子数据")
    @ApiOperation(value = "设备分类-批量获取子数据", notes = "设备分类-批量获取子数据")
    @GetMapping("/getChildListBatch")
    public Result getChildListBatch(@RequestParam("parentIds") String parentIds) {
        try {
            QueryWrapper<ProductCategory> queryWrapper = new QueryWrapper<>();
            List<String> parentIdList = Arrays.asList(parentIds.split(","));
            queryWrapper.in("pid", parentIdList);
            List<ProductCategory> list = deviceCategoryService.list(queryWrapper);
            IPage<ProductCategory> pageList = new Page<>(1, 10, list.size());
            pageList.setRecords(list);
            return Result.OK(pageList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("批量查询子节点失败：" + e.getMessage());
        }
    }

    /**
     * 添加
     *
     * @param
     * @return
     */
    /*@AutoLog(value = "设备分类-添加")
    @ApiOperation(value = "设备分类-添加", notes = "设备分类-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody ProductCategory deviceCategory) {
        deviceCategoryService.addDeviceCategory(deviceCategory);
        return Result.OK("添加成功！");
    }*/
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody AssetsCategory assetsCategory) {
        assetsCategoryService.addAssetsCategory(assetsCategory);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param
     * @return
     */
    /*@AutoLog(value = "设备分类-编辑")
    @ApiOperation(value = "设备分类-编辑", notes = "设备分类-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody ProductCategory deviceCategory) {
        deviceCategoryService.updateDeviceCategory(deviceCategory);
        return Result.OK("编辑成功!");
    }*/
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody AssetsCategory assetsCategory) {
        assetsCategoryService.updateById(assetsCategory);
        return Result.OK("编辑成功!");
    }


    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    /*@AutoLog(value = "设备分类-通过id删除")
    @ApiOperation(value = "设备分类-通过id删除", notes = "设备分类-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        deviceCategoryService.deleteDeviceCategory(id);
        return Result.OK("删除成功!");
    }*/
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        assetsCategoryService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
   /* @AutoLog(value = "设备分类-批量删除")
    @ApiOperation(value = "设备分类-批量删除", notes = "设备分类-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.deviceCategoryService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }*/

    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.assetsCategoryService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    /*@AutoLog(value = "设备分类-通过id查询")
    @ApiOperation(value = "设备分类-通过id查询", notes = "设备分类-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        ProductCategory deviceCategory = deviceCategoryService.getById(id);
        if (deviceCategory == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(deviceCategory);
    }*/
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        AssetsCategory assetsCategory = assetsCategoryService.getById(id);
        if (assetsCategory == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(assetsCategory);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param deviceCategory
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ProductCategory deviceCategory) {
        return super.exportXls(request, deviceCategory, ProductCategory.class, "设备分类");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ProductCategory.class);
    }

}
