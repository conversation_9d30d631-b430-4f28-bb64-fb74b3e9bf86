package com.yuanqiao.insight.monitoring.modules.ledger.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.monitoring.modules.ledger.entity.LedgerMonth;
import com.yuanqiao.insight.monitoring.modules.ledger.entity.LedgerMonthNum;
import com.yuanqiao.insight.monitoring.modules.ledger.mapper.LedgerMonthMapper;
import com.yuanqiao.insight.monitoring.modules.ledger.service.ILedgerMonthService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

/**
 * @Description: 台账月报
 * @Author: jeecg-boot
 * @Date:   2020-03-19
 * @Version: V1.0
 */
@Service
public class LedgerMonthServiceImpl extends ServiceImpl<LedgerMonthMapper, LedgerMonth> implements ILedgerMonthService {

    /**
     * 获取
     * @param year
     * @return
     */
    public HashMap getTotalLedger(String year,String region){
        return  baseMapper.getTotalLedger(year,region);
    }

    /**
     * 获取各个资源类型总数
     * @param year
     * @param region
     * @return
     */
    public List<LedgerMonthNum> getTotalMonthType(String year, String region){
        return baseMapper.getTotalMonthType(year,region);
    }

    /**
     * 通过年份和地区id 获取 月报
     *
     * @param year   年份
     * @param region 地区id
     * @return
     */
    @Override
    public List<LedgerMonth> queryLedgerMonthByYearAndRegion(String year, String region, String assets) {
        return this.baseMapper.queryLedgerMonthByYearAndRegion(year, region, assets);
    }
}
