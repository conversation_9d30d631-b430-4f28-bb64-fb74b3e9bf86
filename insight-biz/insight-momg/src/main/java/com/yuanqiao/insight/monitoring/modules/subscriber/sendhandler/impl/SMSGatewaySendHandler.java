package com.yuanqiao.insight.monitoring.modules.subscriber.sendhandler.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.digest.DigestAlgorithm;
import cn.hutool.crypto.digest.Digester;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yuanqiao.insight.system.config.ISendHandler;
import com.yuanqiao.insight.system.config.emus.NoticeType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.modules.system.entity.SysUsers;
import org.jeecg.modules.system.service.ISysUserService;
import org.jsoup.Jsoup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

/**
 * 短信网关
 *
 * <AUTHOR>
 * @date 2023/3/23
 */
@Slf4j
@Component
public class SMSGatewaySendHandler implements ISendHandler {

    @Autowired
    private ISysUserService userService;
    // 密码固定的加密key
    private final static String ENCRYPT_KEY = "SMmsEncryptKey";

    @Override
    public boolean support(String support) {
        return StringUtils.equals(support, NoticeType.MESSAGE_TELECOM.getCode());
    }

    @Override
    public void send(JSONObject sendMsg, JSONObject template, Boolean isPar) throws Exception {
        //发送地址
        String url = sendMsg.getString("url");
        // 账户
        String accountId = sendMsg.getString("accountId");
        //密码原文
        String password = sendMsg.getString("password");
        // 产品编码
        String productId = sendMsg.getString("productId");
        //生成随机数
        final long random = RandomUtil.randomLong(1, 9223372036854775807L);
        //生成时间戳
        final long timestamp = DateUtil.currentSeconds();
        //密码MD5加密,且转为大写
        Digester md5 = new Digester(DigestAlgorithm.MD5);
        String digestHex = md5.digestHex(password + ENCRYPT_KEY).toUpperCase(Locale.ROOT);
        //手机号
        JSONArray sendTo = template.getJSONArray("sendTo");
        List<String> userPhoneList = userService.list(new QueryWrapper<SysUsers>().lambda().in(SysUsers::getId, sendTo))
                .stream().map(SysUsers::getPhone).collect(Collectors.toList());
        String phones = String.join(",", userPhoneList);
        //构建AccessKey内容
        String accessKeyPar = "AccountId=" + accountId +
                "&PhoneNos=" + userPhoneList.get(0) +
                "&Password=" + digestHex +
                "&Random=" + random +
                "&Timestamp=" + timestamp;
        Digester sha256 = new Digester(DigestAlgorithm.SHA256);
        String accessKey = sha256.digestHex(accessKeyPar);
        //获取短信内容
        String content = template.getString("content");
        content = Jsoup.parse(content).text();
        if (isPar) {
            JSONObject par = template.getJSONObject("par");
            content = generateWelcome(par, content);
        }
        //短信不识别html标签，清除html标签
        JSONObject sendObj = new JSONObject();
        sendObj.put("AccountId", accountId);
        sendObj.put("AccessKey", accessKey);
        sendObj.put("Timestamp", timestamp);
        sendObj.put("Random", random);
        sendObj.put("ProductId", productId);
        sendObj.put("PhoneNos", phones);
        sendObj.put("Content", content);

        try {
            log.info("发送数据,password {} , hexPassword {}, sendObj {}", password, digestHex, sendObj.toJSONString());
            String sendResult = HttpUtil.post(url, sendObj);
            log.info("发送结果，{}", sendResult);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("发送错误，{}", ex.getMessage());
        }
    }
}
