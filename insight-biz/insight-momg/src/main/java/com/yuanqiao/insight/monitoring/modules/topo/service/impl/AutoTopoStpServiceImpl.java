package com.yuanqiao.insight.monitoring.modules.topo.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.common.util.snmp.SNMPUtils;
import com.yuanqiao.insight.monitoring.modules.topo.entity.AutoTopoStp;
import com.yuanqiao.insight.monitoring.modules.topo.mapper.AutoTopoStpMapper;
import com.yuanqiao.insight.monitoring.modules.topo.service.IAutoTopoService;
import com.yuanqiao.insight.monitoring.modules.topo.service.IAutoTopoStpService;
import com.yuanqiao.insight.monitoring.modules.topo.utils.AutoTopoUtils;
import org.jeecg.common.util.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 自动拓扑-网桥表
 * @Author: jeecg-boot
 * @Date:   2025-01-02
 * @Version: V1.0
 */
@Service
public class AutoTopoStpServiceImpl extends ServiceImpl<AutoTopoStpMapper, AutoTopoStp> implements IAutoTopoStpService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refreshBatch(List<AutoTopoStp> stpList) {
        if(CollUtil.isNotEmpty(stpList)){
            this.remove(new LambdaQueryWrapper<>());
            this.saveBatch(stpList);
        }

    }
}
