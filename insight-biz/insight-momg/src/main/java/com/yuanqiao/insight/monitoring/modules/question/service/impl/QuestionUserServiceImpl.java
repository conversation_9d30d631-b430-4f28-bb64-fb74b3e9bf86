package com.yuanqiao.insight.monitoring.modules.question.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.monitoring.modules.question.entity.QuestionUser;
import com.yuanqiao.insight.monitoring.modules.question.mapper.QuestionUserMapper;
import com.yuanqiao.insight.monitoring.modules.question.service.QuestionUserService;
import org.springframework.stereotype.Service;

@Service
public class QuestionUserServiceImpl extends ServiceImpl<QuestionUserMapper, QuestionUser> implements QuestionUserService {
    /**
     * 根据问题id和用户名删除关联记录
     * @param associationId
     * @param loginUser
     */
    @Override
    public void deleteByQuestionIdAndUserName(String associationId, String loginUser) {
        baseMapper.deleteByQuestionIdAndUserName(associationId, loginUser);
    }
}
