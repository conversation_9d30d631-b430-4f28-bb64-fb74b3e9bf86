package com.yuanqiao.insight.monitoring.modules.serviceProvider.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

@Data
public class UserVo {

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "用户名")
    private String username;

    @ApiModelProperty(value = "中文名")
    private String realname;

    @ApiModelProperty(value = "优先级")
    private Double  priority;

    @ApiModelProperty(value = "服务与用户关联id")
    private String providerUserId;

    @ApiModelProperty(value = "服务商名称")
    private String providerName;

    @ApiModelProperty(value = "服务商id")
    private String providerId;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "电子邮箱")
    private String email;

    @ApiModelProperty(value = "头像")
    private String avatar;

    @ApiModelProperty(value = "工号")
    private String workNo;
    @ApiModelProperty(value = "座机号")
    private String telephone;
}
