package com.yuanqiao.insight.monitoring.modules.ops.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yuanqiao.insight.cmdb.modules.assets.entity.AssetsLog;
import com.yuanqiao.insight.cmdb.modules.assets.service.IAssetsLogService;
import com.yuanqiao.insight.common.constant.CommonConstant;
import com.yuanqiao.insight.common.util.common.LocalCacheUtils;
import com.yuanqiao.insight.monitoring.modules.alarm.entity.AlarmHistory;
import com.yuanqiao.insight.monitoring.modules.alarm.entity.AlarmLevel;
import com.yuanqiao.insight.monitoring.modules.alarm.service.IAlarmHistoryService;
import com.yuanqiao.insight.monitoring.modules.alarm.service.IAlarmLevelService;
import com.yuanqiao.insight.monitoring.modules.device.entity.MomgLogDevice;
import com.yuanqiao.insight.monitoring.modules.device.service.IMomgLogDeviceService;
import com.yuanqiao.insight.monitoring.modules.ops.entity.DevopsOrderInfo;
import com.yuanqiao.insight.monitoring.modules.ops.entity.Top;
import com.yuanqiao.insight.monitoring.modules.ops.service.IOrderInfoService;
import com.yuanqiao.insight.monitoring.modules.ops.service.OpsService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.modules.system.controller.SysUserController;
import org.jeecg.modules.system.entity.SysLog;
import org.jeecg.modules.system.entity.SysUserDepart;
import org.jeecg.modules.system.entity.SysUsers;
import org.jeecg.modules.system.mapper.SysUserDepartMapper;
import org.jeecg.modules.system.service.ISysLogService;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 运维报表
 */
@RestController
@RequestMapping("/data-analysis/ops")
public class OpsController {

    @Autowired
    private ISysLogService sysLogService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private SysUserDepartMapper sysUserDepartMapper;
    @Autowired
    private IAlarmHistoryService alarmHistoryService;
    @Autowired
    private IMomgLogDeviceService momgLogDeviceService;
    @Autowired
    private IAssetsLogService assetsLogService;
    @Autowired
    private OpsService opsService;
    @Autowired
    private IAlarmLevelService alarmLevelService;
    @Autowired
    private IOrderInfoService devopsOrderInfoService;

    private final LocalCacheUtils localCacheUtils = LocalCacheUtils.getInstance();

    /**
     * 运维情况统计
     *
     * @param pageNo
     * @param pageSize
     * @param time1
     * @param time2
     * @return
     */
    @GetMapping("/count/table")
    public Result<?> countList(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                               @RequestParam(name = "pageSize", defaultValue = "20") Integer pageSize,
                               @RequestParam(required = false, name = "time1") String time1,
                               @RequestParam(required = false, name = "time2") String time2) {
        Date[] dates = startAndEndDate(time1, time2);
        Date date1 = dates[0], date2 = dates[1];
        final List<SysLog> logUser = getLogUser(date1, date2);
        final List<SysUsers> userList = getUserList(logUser, pageNo, pageSize, false);
        Map<String, JSONArray> table = getCountList(date1, date2, logUser, userList);
        return Result.OK(table);
    }

    Date[] startAndEndDate(String time1, String time2) {
        Date[] startAndEndDate = new Date[2];
        if (StringUtils.isNotBlank(time1) && StringUtils.isNotBlank(time2)) {
            startAndEndDate[0] = DateUtil.parseDate(time1.trim());
            startAndEndDate[1] = DateUtil.offsetDay(DateUtil.parseDate(time2.trim()), 1);
        }
        return startAndEndDate;
    }

    /**
     * 使用频次统计
     * 每天00 02 04 ...的使用数量
     *
     * @return
     */
    @GetMapping("/count/use")
    public Result<?> userCount(@RequestParam(required = false, name = "time1") String time1,
                               @RequestParam(required = false, name = "time2") String time2) {
        Date[] dates = startAndEndDate(time1, time2);
        Date date1 = dates[0], date2 = dates[1];
        if (isTimeRangeValid(date1, date2)){
            int between = (int)(DateUtil.between(date1, date2, DateUnit.DAY));
            List<Top> useCount = getUseCount(date1.toString(), between, date1, date2, DateField.DAY_OF_YEAR);
            return Result.ok(useCount);
        }
        String todayStr = DateUtil.formatDate(new Date());
        Date today = DateUtil.parseDate(DateUtil.formatDate(new Date()));
        Date tomorrow = DateUtil.offsetDay(today, 1);
        List<Top> topList = getUseCount(todayStr, 24, today, tomorrow, DateField.HOUR);
        return Result.OK(topList);
    }

    /**
     * 运维业务占比 饼图统计
     * 如果无时间参数，默认查询所有的数据
     * 如果时间参数不为空，则查询时间段内数据
     * @return
     */
    @GetMapping("/count/biz")
    public Result<?> bizCount(@RequestParam(required = false, name = "time1") String time1,
                              @RequestParam(required = false, name = "time2") String time2) {
        Date[] dates = startAndEndDate(time1, time2);
        Date date1 = dates[0], date2 = dates[1];
        List<Top> bizCount = getBizCount(date1, date2);
        return Result.OK(bizCount);
    }

    /**
     * 告警top10统计
     *
     * @return
     */
    @GetMapping("/top/alarm")
    public Result<?> topAlarm(@RequestParam(required = false, name = "time1") String time1,
                              @RequestParam(required = false, name = "time2") String time2) {
        Date[] dates = startAndEndDate(time1, time2);
        Date date1 = dates[0], date2 = dates[1];
        final List<SysUsers> userList = getUserList();
        final List<AlarmLevel> levelList = alarmLevelService.list();
        JSONObject alarmTopData = new JSONObject();
        List<String> color = levelList.stream().map(AlarmLevel::getColor).collect(Collectors.toList());
        List<String> xAxis = new ArrayList<>();

        Map<Integer, JSONObject> dataList = new HashMap<>();
        for (SysUsers users : userList) {
//            String id = users.getId();
            xAxis.add(users.getRealname());
            for (AlarmLevel alarmLevel : levelList) {
                JSONObject yAxisData = dataList.computeIfAbsent(Integer.parseInt(alarmLevel.getAlarmLevel()), k -> new JSONObject());
                final JSONArray values = (JSONArray) yAxisData.computeIfAbsent("values", x -> new JSONArray());
                QueryWrapper<AlarmHistory> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("responsible_user", users.getUsername())
                        .in("handle_status", "1", "2", "3")
                        .eq("alarm_level", alarmLevel.getAlarmLevel())
                        .between(isTimeRangeValid(date1, date2),"create_time", date1, date2);
                Integer alarmCount = alarmHistoryService.count(queryWrapper);
                if (!yAxisData.containsKey("name")) {
                    yAxisData.put("name", alarmLevel.getLevelName());
                }
                values.add(alarmCount);
            }
        }
        final List<JSONObject> yAxis = dataList.entrySet()
                .stream()
                .sorted(Map.Entry.comparingByKey())
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());
        alarmTopData.put("color", color);
        alarmTopData.put("xAxis", xAxis);
        alarmTopData.put("yAxis", yAxis);
        return Result.OK(alarmTopData);
    }

    /**
     * 设备配置top10统计
     *
     * @return
     */
    @GetMapping("/top/product")
    public Result<?> topProduct(@RequestParam(required = false, name = "time1") String time1,
                                @RequestParam(required = false, name = "time2") String time2) {
        Date[] dates = startAndEndDate(time1, time2);
        Date date1 = dates[0], date2 = dates[1];
        final List<SysUsers> userList = getUserList();
        List<Top> tops = getProductTop(userList,date1,date2);
        return Result.OK(tops);
    }

    /**
     * 资产配置top10统计
     *
     * @return
     */
    @GetMapping("/top/assets")
    public Result<?> assetsTop(@RequestParam(required = false, name = "time1") String time1,
                               @RequestParam(required = false, name = "time2") String time2) {
        Date[] dates = startAndEndDate(time1, time2);
        Date date1 = dates[0], date2 = dates[1];
        final List<SysUsers> userList = getUserList();
        List<Top> tops = getAssetsTop(userList,date1,date2);
        return Result.OK(tops);
    }


    @AutoLog(value = "运维报表-导出")
    @GetMapping("/count/export")
    public Result<?> countExport(HttpServletResponse response,
                                 @RequestParam(required = false, name = "time1") String time1,
                                 @RequestParam(required = false, name = "time2") String time2) {
        //运维情况统计
        Date[] dates = startAndEndDate(time1, time2);
        Date date1 = dates[0], date2 = dates[1];
        final List<SysLog> logUser = getLogUser(date1, date2);
        final List<SysUsers> userList = getUserList(logUser, null, null, true);
        Map<String, JSONArray> map = getCountList(date1, date2, logUser, userList);

        //使用频率折线图-当前月
        //当前月份
//        final String month = DateUtil.format(new Date(), "yyyy-MM");
//        //本月第一天
//        int lastDay = DateUtil.getLastDayOfMonth(new Date());
//        Date startTime = DateUtil.parseDate(String.format("%s-01", month));
//        Date endTime = DateUtil.offsetDay(startTime, lastDay);
//        List<Top> useCount = getUseCount(month, lastDay, startTime, endTime, DateField.MONTH);

        int between = (int)(DateUtil.between(date1, date2, DateUnit.DAY));
        List<Top> useCount = getUseCount(date1.toString(), between, date1, date2, DateField.DAY_OF_YEAR);

        //运维业务占比饼图
        List<Top> bizCount = getBizCount(date1, date2);

        //告警top10柱状
        final List<AlarmLevel> levelList = alarmLevelService.list();
        Map<String, Map<String, BigDecimal>> alarmTop = new HashMap<>();
        for (SysUsers users : userList) {
            Map<String, BigDecimal> yAxisData = alarmTop.computeIfAbsent(users.getRealname(), k -> new HashMap<>());
            for (AlarmLevel alarmLevel : levelList) {
                QueryWrapper<AlarmHistory> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("responsible_user", users.getUsername())
                        .in("handle_status", "1", "2", "3")
                        .eq("alarm_level", alarmLevel.getAlarmLevel())
                        .between(isTimeRangeValid(date1, date2),"create_time", date1, date2);
                int alarm = alarmHistoryService.count(queryWrapper);
                yAxisData.put(alarmLevel.getAlarmLevel(), BigDecimal.valueOf(alarm));
            }
        }

        //设备配置柱状图
        List<Top> productTop = getProductTop(userList,date1,date2);

        //资产配置柱状图
        List<Top> assetsTop = getAssetsTop(userList,date1,date2);

        String filePath = opsService.exportPdf(map, useCount, bizCount, alarmTop, productTop, assetsTop);
        return Result.OK(filePath);

    }

    private List<SysLog> getLogUser(Date time1, Date time2) {
        QueryWrapper<SysLog> sysLogQueryWrapper = new QueryWrapper<>();
        sysLogQueryWrapper.eq("log_type", 1);
        if (time1 != null && time2 != null) {
            sysLogQueryWrapper.between("create_time", time1, time2);
        }
        return sysLogService.list(sysLogQueryWrapper);//获取登录用的log
    }

    private List<SysUsers> getUserList() {
        final List<SysLog> logUser = getLogUser(null, null);
        return getUserList(logUser, 1, 10, true);
    }

    private List<SysUsers> getUserList(List<SysLog> sysLogs, Integer page, Integer pageSize, boolean isAll) {

        List<String> list = new ArrayList<>();
        for (SysLog sysLog : sysLogs) {
            String userid = sysLog.getUserid();
            list.add(userid);
        }
        List<String> nameList;
        page = Optional.ofNullable(page).orElse(1);
        if (pageSize != null && pageSize > 0) {
            page = (page - 1) * pageSize;
            nameList = list.stream().distinct().skip(page).limit(pageSize).collect(Collectors.toList());
        } else {
            nameList = list.stream().distinct().collect(Collectors.toList());
        }
        if (CollUtil.isNotEmpty(nameList)) {
            QueryWrapper<SysUsers> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("username", nameList);
            if (!isAll) {
                queryWrapper.eq("del_flag", 0);
            }
            return sysUserService.list(queryWrapper);
        } else {
            return new ArrayList<>();
        }
    }

    private Map<String, JSONArray> getCountList(Date date1, Date date2, List<SysLog> logUser, List<SysUsers> userList) {
        boolean flag = date1 != null && date2 != null;

        Map<String, JSONArray> table = new HashMap<>();
        //生成标题行
        JSONArray title = table.computeIfAbsent("title", x -> new JSONArray());
        title.add("用户名");
        title.add("登录次数");
        title.add("设备配置修改");
        title.add("资产配置修改");
        title.add("资产保修处理");
        String threePowersKey = CommonConstant.DATA_DICT_KEY + "insight_threePowers";
        Optional<Object> optional = Optional.ofNullable(localCacheUtils.getValueByKey(threePowersKey + "_isEnable"));
        int isThreePowers = Integer.parseInt(String.valueOf(optional.orElse("0")));
        final List<String> threePowers = SysUserController.getThreePowers();

        List<AlarmLevel> levelList = alarmLevelService.list();
        levelList = levelList.stream().sorted(Comparator.comparing(i -> Integer.parseInt(i.getAlarmLevel()))).collect(Collectors.toList());
        levelList.forEach(alarmLevel -> title.add(alarmLevel.getLevelName()));
        for (SysUsers users : userList) {
            if (isThreePowers == 1) {
                if (threePowers.contains(users.getUsername())) {
                    continue;
                }
            }
            //数据行,使用二维数组
            JSONArray rows = table.computeIfAbsent("rows", x -> new JSONArray());
            JSONArray row = new JSONArray();
            rows.add(row);
            //用户名
            row.add(users.getRealname());
            //登录次数
            final int loginCount = getLoginCount(logUser, users.getUsername());

            row.add(loginCount);
            final QueryWrapper<MomgLogDevice> deviceQueryWrapper = new QueryWrapper<MomgLogDevice>()
                    .eq("create_by", users.getUsername())
                    .eq("product_config", "1");
            final QueryWrapper<AssetsLog> assetsConfigs1QueryWrapper = new QueryWrapper<AssetsLog>()
                    .eq("user_id", users.getUsername())
                    .eq("assets_config", "1");
            final QueryWrapper<AssetsLog> assetsConfigs2QueryWrapper = new QueryWrapper<AssetsLog>()
                    .eq("user_id", users.getUsername())
                    .eq("assets_config", "2");
            if (flag) {
                deviceQueryWrapper.between("create_time", date1, date2);
                assetsConfigs1QueryWrapper.between("create_time", date1, date2);
                assetsConfigs2QueryWrapper.between("create_time", date1, date2);
            }
            //设备配置修改
            final int productConfigs = momgLogDeviceService.count(deviceQueryWrapper);
            row.add(productConfigs);
            //资产配置修改
            final int assetsConfigs1 = assetsLogService.count(assetsConfigs1QueryWrapper);
            row.add(assetsConfigs1);
            //资产保修处理
            final int assetsConfigs2 = assetsLogService.count(assetsConfigs2QueryWrapper);
            row.add(assetsConfigs2);
            //告警次数
            for (AlarmLevel alarmLevel : levelList) {
//                MPJLambdaWrapper<AlarmHistory> alarmQueryWrapper = new MPJLambdaWrapper<AlarmHistory>()
//                        .select(AlarmHistory::getId)
//                        .leftJoin(DevopsOrderInfo.class, DevopsOrderInfo::getWarningId, AlarmHistory::getId)
//                        .eq(DevopsOrderInfo::getOrderSource, CommonConstant.DEVOPS_ORDER_INFO_SOURCE_2)
//                        .eq(AlarmHistory::getAlarmLevel, alarmLevel.getAlarmLevel())
//                        .eq(AlarmHistory::getResponsibleUser, users.getUsername())
//                        .in(AlarmHistory::getHandleStatus, "1", "2", "3");
//                if (flag) {
//                    alarmQueryWrapper.between(DevopsOrderInfo::getCreateTime, date1, date2);
//                }
                LambdaQueryWrapper<AlarmHistory> alarmQueryWrapper = new LambdaQueryWrapper<AlarmHistory>();
                alarmQueryWrapper.eq(AlarmHistory::getAlarmLevel, alarmLevel.getAlarmLevel())
                        .eq(AlarmHistory::getResponsibleUser, users.getUsername())
                        .in(AlarmHistory::getHandleStatus, "1", "2", "3");
                if (flag) {
                    alarmQueryWrapper.between(AlarmHistory::getCreateTime, date1, date2);
                }
                Integer alarm = alarmHistoryService.count(alarmQueryWrapper);
                row.add(alarm);
            }
        }
        return table;
    }

    /**
     * 使用频次统计折线图
     *
     * @return
     */
    public List<Top> getUseCount(String dStr, int lastTime, Date startTime, Date endTime, DateField dateField) {
        List<Top> topList = new ArrayList<>();
        //登录记录
        List<SysLog> sysLogList = Optional
                .ofNullable(sysLogService.list(new QueryWrapper<SysLog>()
                                .between("create_time", startTime, endTime)
                                .eq("log_type", 1)
                        )
                )
                .orElse(new ArrayList<>());
        //告警确认记录
        List<DevopsOrderInfo> devopsOrderInfoList = Optional
                .ofNullable(devopsOrderInfoService.list(new QueryWrapper<DevopsOrderInfo>()
                                .between("create_time", startTime, endTime)
                                .eq("order_source", CommonConstant.DEVOPS_ORDER_INFO_SOURCE_2)
                        )
                )
                .orElse(new ArrayList<>());
        //设备修改记录
        List<MomgLogDevice> momgLogDeviceList = Optional
                .ofNullable(momgLogDeviceService.list(new QueryWrapper<MomgLogDevice>()
                                .between("create_time", startTime, endTime)
                                .eq("product_config", "1")
                        )
                )
                .orElse(new ArrayList<>());
        //资产配置记录
        List<AssetsLog> assetsLogList = Optional
                .ofNullable(assetsLogService.list(new QueryWrapper<AssetsLog>()
                                .between("create_time", startTime, endTime)
                                .in("assets_config", "1", "2")
                        )
                )
                .orElse(new ArrayList<>());
        int interval = lastTime > 12 ? lastTime / 12 : 1;
        Date currentDate = startTime;
        for (int i = 0; i <= lastTime;) {
            String format = null;
            DateTime sTime = null;
            DateTime eTime = null;
            switch (dateField) {
                case HOUR:
                    format = String.format("%02d", i);
                    sTime = DateUtil.parseDateTime(String.format("%s %02d:00:00", dStr, Math.max(i - 2, 0)));
                    eTime = DateUtil.parseDateTime(String.format("%s %02d:00:00", dStr, i));
                    i += 2;
                    break;
                case MONTH:
                    if (i == 0) {
                        i++;
                    }
                    format = String.format("%s-%02d", dStr, i);
                    sTime = DateUtil.parseDate(format);
                    eTime = DateUtil.offsetDay(sTime, 1);
                    i++;
                    break;
                case DAY_OF_YEAR:
                    sTime = DateUtil.date(currentDate);
                    format = DateUtil.format(sTime, "yyyy-MM-dd");
                    eTime = DateUtil.date(DateUtil.offset(currentDate, DateField.DAY_OF_YEAR, interval));
                    if (DateUtil.between(eTime,  endTime,DateUnit.DAY) < interval){
                        eTime = DateUtil.date(endTime);
                    }
                    currentDate = DateUtil.offset(currentDate, DateField.DAY_OF_YEAR, interval);
                    i += interval;
                    break;
            }

            final int loginCount = count(sysLogList, i, sTime, eTime).intValue();
            final int orderCount = count(devopsOrderInfoList, i, sTime, eTime).intValue();
            final int deviceCount = count(momgLogDeviceList, i, sTime, eTime).intValue();
            final int assetsCount = count(assetsLogList, i, sTime, eTime).intValue();
            Top top = new Top();
            top.setName(format);
            top.setValue(loginCount + orderCount + deviceCount + assetsCount);
            top.setUnit("");
            topList.add(top);
        }
        return topList;
    }

    //运维业务占比 饼图
    private List<Top> getBizCount(Date startTime, Date endTime) {
        List<Top> topData = new ArrayList<>();
        //之前是查询30天的数据，但是其他表格都是在未指定时间时查询全部，这里也改为查全部
        final List<AlarmLevel> levelList = alarmLevelService.list();
        for (AlarmLevel alarmLevel : levelList) {
            Integer alarm = alarmHistoryService.selectAlarmHistory(alarmLevel.getAlarmLevel(), startTime, endTime);
            Top top1 = new Top();
            top1.setName(alarmLevel.getLevelName());
            top1.setValue(alarm);
            top1.setUnit("%");
            top1.setColor(alarmLevel.getColor());
            topData.add(top1);
        }
        //设备配置数量
        Integer productConfig = momgLogDeviceService.productConfig(startTime,endTime);
        //资产配置数量
        Integer assertion = assetsLogService.assetsConfig(startTime,endTime);
        Top top2 = new Top();
        top2.setName("设备配置");
        top2.setValue(productConfig);
        top2.setUnit("%");
        top2.setColor("rgb(90, 133, 176)");
        topData.add(top2);

        Top top3 = new Top();
        top3.setName("资产配置");
        top3.setValue(assertion);
        top3.setUnit("%");
        top3.setColor("rgb(126, 135, 143)");
        topData.add(top3);
        return topData;
    }

    public List<Top> getProductTop(List<SysUsers> userList,Date startTime, Date endTime) {
        List<Top> tops = new ArrayList<>();
        for (SysUsers users : userList) {
            //产品修改配置
            Integer productConfigs = momgLogDeviceService.count(new QueryWrapper<MomgLogDevice>()
                    .eq("create_by", users.getUsername())
                    .eq("product_config", "1")
                    .between(isTimeRangeValid(startTime, endTime),"create_time", startTime, endTime));
//            LambdaQueryWrapper<MomgLogDevice> momgLogDeviceLambdaQueryWrapper = new LambdaQueryWrapper<MomgLogDevice>().groupBy(MomgLogDevice::getCreateBy);
//            productConfigs = momgLogDeviceService.count(momgLogDeviceLambdaQueryWrapper);
            Top top = new Top();
            top.setName(users.getRealname());
            top.setValue(productConfigs);
            top.setUnit("");
            tops.add(top);
        }
        return tops.stream().sorted(Comparator.comparing(Top::getValue).reversed()).collect(Collectors.toList());
    }

    public List<Top> getAssetsTop(List<SysUsers> userList,Date startTime, Date endTime) {
        List<Top> tops = new ArrayList<>();
        //资产配资修改
        for (SysUsers users : userList) {
            //资产配资修改
            Integer assetsConfigs = assetsLogService.count(new QueryWrapper<AssetsLog>()
                    .eq("user_id", users.getUsername())
                    .in("assets_config", "1", "2")
                    .between(isTimeRangeValid(startTime, endTime), "create_time", startTime, endTime));
            Top top = new Top();
            top.setName(users.getRealname());
            top.setValue(assetsConfigs);
            top.setUnit("");
            tops.add(top);
        }
        return tops.stream().sorted(Comparator.comparing(Top::getValue).reversed()).collect(Collectors.toList());
    }

    boolean isTimeRangeValid(Date startTime, Date endTime){
        return startTime != null && endTime != null && startTime.before(endTime);
    }
    public int getLoginCount(List<SysLog> sysLogs, String name) {
        int a = 0;
        for (SysLog sysLog : sysLogs) {
            if (name.equals(sysLog.getUserid())) {
                a++;
            }
        }
        return a;
    }

    public String getDepart(String userid) {
        String id = sysUserService.getUserid(userid);
        List<SysUserDepart> userDepartByUid = sysUserDepartMapper.getUserDepartByUid(id);
        ArrayList<String> depIds = new ArrayList<>();
        for (SysUserDepart sysUserDepart : userDepartByUid) {
            String depId = sysUserDepart.getDepId();
            depIds.add(depId);
        }
        return depIds.toString();
    }

    private static <T> Long count(List<T> list, int i, DateTime sTime, DateTime eTime) {
        if (CollUtil.isNotEmpty(list)){
            Class<?> aClass = list.get(0).getClass();
            try {
                Method getCreateTime = aClass.getMethod("getCreateTime");
                return list.stream()
                        .filter(item -> {
                            try {
                                final Date createTime = (Date) getCreateTime.invoke(item);
                                if (i == 0) {
                                    return DateUtil.compare(createTime, eTime) == 0;
                                } else {
                                    return DateUtil.compare(createTime, sTime) >= 0 && DateUtil.compare(createTime, eTime) < 1;
                                }
                            } catch (Exception e) {
                                return false;
                            }
                        })
                        .count();
            } catch (NoSuchMethodException e) {
                throw new RuntimeException(e);
            }
        }
        return 0L;
    }

    public static void main(String[] args) {
        for (int i = 0; i <= 10; i++) {
            System.out.println(i);
            i++;
        }
    }
}
