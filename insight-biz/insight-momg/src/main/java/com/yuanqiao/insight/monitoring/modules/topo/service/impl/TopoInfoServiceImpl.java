package com.yuanqiao.insight.monitoring.modules.topo.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.common.constant.CommonConstant;
import com.yuanqiao.insight.common.util.common.LocalCacheUtils;
import com.yuanqiao.insight.common.util.common.UnitConvertUtil;
import com.yuanqiao.insight.common.util.dbType.DataSourceTypeUtils;
import com.yuanqiao.insight.monitoring.modules.alarm.entity.AlarmHistory;
import com.yuanqiao.insight.monitoring.modules.alarm.mapper.AlarmHistoryMapper;
import com.yuanqiao.insight.monitoring.modules.topo.entity.EdgeInfo;
import com.yuanqiao.insight.monitoring.modules.topo.entity.NodeInfo;
import com.yuanqiao.insight.monitoring.modules.topo.entity.ProCatVo;
import com.yuanqiao.insight.monitoring.modules.topo.entity.TopoInfo;
import com.yuanqiao.insight.monitoring.modules.topo.mapper.TopoInfoMapper;
import com.yuanqiao.insight.monitoring.modules.topo.service.IEdgeInfoService;
import com.yuanqiao.insight.monitoring.modules.topo.service.INodeInfoService;
import com.yuanqiao.insight.monitoring.modules.topo.service.ITopoInfoService;
import com.yuanqiao.insight.service.device.entity.DeviceInfo;
import com.yuanqiao.insight.service.device.entity.PortVo;
import com.yuanqiao.insight.service.device.mapper.DeviceInfoMapper;
import com.yuanqiao.insight.service.product.entity.Product;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import com.yuanqiao.insight.service.product.mapper.ProductMapper;
import com.yuanqiao.insight.service.product.mapper.ProertyMetadataMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 拓扑图
 * @Author: jeecg-boot
 * @Date: 2021-03-21
 * @Version: V1.0
 */
@Slf4j
@Service
public class TopoInfoServiceImpl extends ServiceImpl<TopoInfoMapper, TopoInfo> implements ITopoInfoService {
    @Autowired
    private DeviceInfoMapper deviceInfoMapper;
    @Autowired
    private ProductMapper productMapper;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private AlarmHistoryMapper alarmHistoryMapper;
    @Autowired
    private TopoInfoMapper topoInfoMapper;
    @Autowired
    private INodeInfoService nodeInfoService;
    @Autowired
    private IEdgeInfoService edgeInfoService;
    @Autowired
    private ProertyMetadataMapper proertyMetadataMapper;
    private final LocalCacheUtils cacheUtils = LocalCacheUtils.getInstance();
    @Autowired
    private DataSourceTypeUtils dataSourceTypeUtils;


    @Override
    public JSONObject info(String deviceCode) {
        JSONObject jsonObject = new JSONObject();
        DeviceInfo deviceInfo = deviceInfoMapper.findAllByCode(deviceCode);  //获取到设备所有信息
        if (deviceInfo != null) {
            jsonObject.put("id", deviceInfo.getId());
            jsonObject.put("code", deviceInfo.getDeviceCode());
            jsonObject.put("name", deviceInfo.getName());
            jsonObject.put("icons", getIcons(deviceInfo.getProductId()));
            jsonObject.put("portNum", 1);
            jsonObject.put("property", getStatic(deviceInfo.getDeviceCode(), deviceInfo.getIp()));
            ProCatVo name = topoInfoMapper.findName(deviceInfo.getId());
            if (name == null) {
                jsonObject.put("displayName", "---");
                jsonObject.put("categoryName", "---");
                jsonObject.put("ip", "---");
            } else {
                jsonObject.put("displayName", name.getDisplayName());
                jsonObject.put("categoryName", name.getCategoryName());
                jsonObject.put("ip", deviceInfo.getIp());

                String portInfoKey = "stca:" + deviceInfo.getDeviceCode() + "_portInfo";
                JSONObject portInfoEntries = JSONObject.parseObject(String.valueOf(redisTemplate.opsForValue().get(portInfoKey)));
                if (portInfoEntries != null && !portInfoEntries.isEmpty()) {
                    ArrayList<PortVo> resultList = new ArrayList<>();
                    JSONArray portArray = portInfoEntries.getJSONArray("value");
                    portArray.stream().forEach(item -> {
                        System.out.println(JSONObject.parseObject(item.toString()).getJSONObject("index").get("value") + " ******** " + JSONObject.parseObject(item.toString()).getJSONObject("portStatus").get("value"));
                        PortVo portVo = new PortVo();
                        portVo.setId(JSONObject.parseObject(item.toString()).getJSONObject("index").getInteger("value"));
                        if (JSONObject.parseObject(item.toString()).getJSONObject("portStatus").getString("value").equals("连接")) {
                            portVo.setStatus(1);
                        } else {
                            portVo.setStatus(0);
                        }
                        resultList.add(portVo);
                    });
                    resultList.sort(Comparator.comparing(PortVo::getId));
                    System.out.println(resultList);
                    jsonObject.put("portStatus", resultList);
                }

                if (deviceInfo.getProductName().contains("矩阵") || true) {
                    String inkey = "stca:" + deviceInfo.getDeviceCode() + "_inputPortOccupancy";
                    String outkey = "stca:" + deviceInfo.getDeviceCode() + "_outputPortOccupancy";
                    JSONObject inEntries = JSONObject.parseObject(String.valueOf(redisTemplate.opsForValue().get(inkey)));
                    JSONObject outEntries = JSONObject.parseObject(String.valueOf(redisTemplate.opsForValue().get(outkey)));

                    if (inEntries != null && !inEntries.isEmpty() && outEntries != null && !outEntries.isEmpty()) {
                        HashMap<String, JSONObject> matirxMap = new HashMap<>();
                        matirxMap.put("inEntries", inEntries);
                        matirxMap.put("outEntries", outEntries);
                        List<PortVo> resultList = getPortVoList(matirxMap);
                        jsonObject.put("portStatus", resultList);
                    }
                }
            }
        }
        return jsonObject;
    }

    // 产品图片
    private JSONArray getIcons(String productId) {
        Product product = productMapper.selectById(productId);//查询到所有产品信息
        if (product.getIcon() != null) {
            String[] split = product.getIcon().split(",");
            JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(split));
            return jsonArray;
        }
        return null;
    }

    //静态信息
    private JSONObject getStatic(String deviceCode, String ip) {
        String key = "stca:" + deviceCode;
        if (redisTemplate.hasKey(key)) {
            HashMap<String, Object> entries = (HashMap<String, Object>) redisTemplate.opsForHash().entries(key);
            if (entries.containsKey("static")) {
                JSONObject aStatic = (JSONObject) entries.get("static");
                JSONArray valueJsonArray = aStatic.getJSONArray("value");
                if (!valueJsonArray.isEmpty()) {
                    //此处写JSONArray里面获取的东西
                    return getValueName(valueJsonArray, ip);
                }
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("ip", ip);
                return jsonObject;
            }
        }
        return null;
    }

    private JSONObject getValueName(JSONArray jsonArray, String ip) {
        JSONObject parJson = new JSONObject();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            if (!jsonObject.isEmpty()) {
                Iterator<Map.Entry<String, Object>> iterator = jsonObject.entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry<String, Object> next = iterator.next();
                    JSONObject value = (JSONObject) next.getValue();
                    parJson.put("ip", ip);
                    if ("manufacturer".equals(next.getKey())) {
                        parJson.put(next.getKey(), value.getString("name"));
                    }
                    if ("productModel".equals(next.getKey())) {
                        parJson.put(next.getKey(), value.getString("name"));
                    }
                    if ("productName".equals(next.getKey())) {
                        parJson.put(next.getKey(), value.getString("name"));
                    }
                }

            }
            parJson.put("ip", ip);
            return parJson;
        }
        return parJson;
    }


    @Override
    @Transactional
    public Result<?> addTopo(TopoInfo topoInfo) {
        QueryWrapper<TopoInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("topo_name", topoInfo.getTopoName());
        if (StringUtils.isNotEmpty(topoInfo.getTopoType())) {
            queryWrapper.eq("topo_type", topoInfo.getTopoType());
        }
        List<TopoInfo> list = this.list(queryWrapper);
        if (CollUtil.isNotEmpty(list)) {
            return Result.error("拓扑图名称重复！");
        }
        if (StringUtils.isEmpty(topoInfo.getTopoImg())) {
            topoInfo.setTopoImg("default/topological.png");
        }
        if (StringUtils.isEmpty(topoInfo.getShowType())) {
            topoInfo.setShowType("0");
        }
        this.save(topoInfo);

        if (CollUtil.isNotEmpty(topoInfo.getImgUrlList())) {
            redisTemplate.opsForValue().set("topo_BG", topoInfo.getImgUrlList());
        } else {
            redisTemplate.opsForValue().set("topo_BG", new ArrayList<String>());
        }

        if (CollUtil.isNotEmpty(topoInfo.getNodeList())) {
            nodeInfoService.saveBatch(topoInfo.getNodeList());
        }
        if (CollUtil.isNotEmpty(topoInfo.getEdgeList())) {
            edgeInfoService.saveBatch(topoInfo.getEdgeList());
        }

        return Result.OK("添加成功！");
    }

    @Override
    @Transactional
    public Result<?> editTopo(TopoInfo topoInfo) {
        QueryWrapper<TopoInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("topo_name", topoInfo.getTopoName());
        queryWrapper.eq("topo_type", topoInfo.getTopoType());
        queryWrapper.ne("id", topoInfo.getId());
        List<TopoInfo> list = this.list(queryWrapper);
        if (CollUtil.isNotEmpty(list)) {
            return Result.error("拓扑图名称重复！");
        }
        //根据传入字段，修改topoInfo对象
        this.updateById(topoInfo);

        if (CollUtil.isNotEmpty(topoInfo.getImgUrlList())) {
            redisTemplate.opsForValue().set("topo_BG", topoInfo.getImgUrlList());
        } else {
            redisTemplate.opsForValue().set("topo_BG", new ArrayList<String>());
        }

        // 拓扑图收藏时，传入的节点集合与边线集合为null，仅执行传入字段的修改操作
        // 编辑拓扑图时，传入的节点集合与边线集合为空集合或者非空集合，空集合需要删除节点与边线数据，非空集合需要先删除数据后重新添加

        if (topoInfo.getNodeList() != null) {
            nodeInfoService.remove(new QueryWrapper<NodeInfo>().eq("topo_id", topoInfo.getId()));
            if (topoInfo.getNodeList().size() > 0) {
                nodeInfoService.saveBatch(topoInfo.getNodeList());
            }
        }

        if (topoInfo.getEdgeList() != null) {
            edgeInfoService.remove(new QueryWrapper<EdgeInfo>().eq("topo_id", topoInfo.getId()));
            if (topoInfo.getEdgeList().size() > 0) {
                edgeInfoService.saveBatch(topoInfo.getEdgeList());
            }
        }

        return Result.OK("编辑成功!");
    }

    private void nodeDynamicUpdate(List<NodeInfo> newList, String topoId) {

        //新增数据
        List<NodeInfo> insertNodeList = newList.stream().filter(data -> StringUtils.isEmpty(data.getId())).collect(Collectors.toList());

        //修改数据
        List<NodeInfo> updateNodeList = newList.stream().filter(data -> StringUtils.isNotEmpty(data.getId())).collect(Collectors.toList());

        //删除数据（原始数据中过滤掉待修改数据）
        if (CollUtil.isNotEmpty(updateNodeList)) {
            List<NodeInfo> dbOriginList = nodeInfoService.list(new QueryWrapper<NodeInfo>().eq("topo_id", topoId));
            List<String> deleteNodeIdList = dbOriginList.stream().map(NodeInfo::getId)
                    .filter(data -> updateNodeList.stream().noneMatch(item -> StringUtils.equals(data, item.getId())))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(deleteNodeIdList)) {
                nodeInfoService.removeByIds(deleteNodeIdList);
            }
        }

        if (CollUtil.isNotEmpty(updateNodeList)) {
            nodeInfoService.updateBatchById(updateNodeList);
        }
        if (CollUtil.isNotEmpty(insertNodeList)) {
            nodeInfoService.saveBatch(insertNodeList);
        }

    }

    private void edgeDynamicUpdate(List<EdgeInfo> newList, String topoId) {

        //新增数据
        List<EdgeInfo> insertEdgeList = newList.stream().filter(data -> StringUtils.isEmpty(data.getId())).collect(Collectors.toList());

        //修改数据
        List<EdgeInfo> updateEdgeList = newList.stream().filter(data -> StringUtils.isNotEmpty(data.getId())).collect(Collectors.toList());

        //删除数据
        if (CollUtil.isNotEmpty(updateEdgeList)) {
            List<EdgeInfo> dbOriginList = edgeInfoService.list(new QueryWrapper<EdgeInfo>().eq("topo_id", topoId));
            List<String> deleteNodeIdList = dbOriginList.stream().map(EdgeInfo::getId).
                    filter(data -> updateEdgeList.stream().noneMatch(item -> StringUtils.equals(data, item.getId())))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(deleteNodeIdList)) {
                edgeInfoService.removeByIds(deleteNodeIdList);
            }
        }

        if (CollUtil.isNotEmpty(updateEdgeList)) {
            edgeInfoService.updateBatchById(updateEdgeList);
        }
        if (CollUtil.isNotEmpty(insertEdgeList)) {
            edgeInfoService.saveBatch(insertEdgeList);
        }

    }


    public JSONObject status(DeviceInfo dev) {

        JSONObject jsonObject = new JSONObject();
        DeviceInfo deviceInfo = deviceInfoMapper.findAllByCode(dev.getDeviceCode());  //获取到设备所有信息
        if (deviceInfo == null) {
            log.error("未找到标识为 " + dev.getDeviceCode() + " 的设备，拓扑信息中将展示默认信息！");
            jsonObject.put("cpu", "");
            jsonObject.put("mem", "");
            jsonObject.put("disk", "");
            jsonObject.put("id", "");
            jsonObject.put("code", "");
            jsonObject.put("name", "");
            jsonObject.put("ip", "");
            jsonObject.put("eth", new JSONArray());
            jsonObject.put("displayName", "");
            jsonObject.put("categoryName", "");

            //设备在线离线状态
            jsonObject.put("status", 0);
            //设备告警状态
            jsonObject.put("alarmStatus", 0);
            //设备端口状态
            jsonObject.put("portStatus", new ArrayList<JSONObject>());
            //设备启用状态
            jsonObject.put("isEnable", false);

            return jsonObject;
        }

        jsonObject.put("status", deviceInfo.getStatus());
        jsonObject.put("alarmStatus", deviceInfo.getAlarmStatus());

        if (deviceInfo.getEnable() != null && deviceInfo.getEnable() == 0) {
            jsonObject.put("isEnable", false);
        }

        String cpuKey = "stca:" + deviceInfo.getDeviceCode() + "_cpuRate";
        JSONObject cpuJson = JSONObject.parseObject(String.valueOf(redisTemplate.opsForValue().get(cpuKey)));
        if (null != cpuJson && !cpuJson.isEmpty()) {
            log.error("网络拓扑鼠标悬浮--CPU使用率 ：" + cpuJson.getDouble("value"));
            Double cpuRte = Optional.ofNullable(cpuJson.getDouble("value")).orElse(0.00);
            jsonObject.put("cpu", String.format("%.2f", cpuRte) + "%");
        } else {
            log.error("网络拓扑鼠标悬浮--CPU使用率获取为空！");
        }


        String memKey = "stca:" + deviceInfo.getDeviceCode() + "_memRate";
        String memKey1 = "stca:" + deviceInfo.getDeviceCode() + "_memUtilizRate";
        JSONObject memJson = null;
        if (redisTemplate.hasKey(memKey)) {
            memJson = JSONObject.parseObject(String.valueOf(redisTemplate.opsForValue().get(memKey)));
        } else if (redisTemplate.hasKey(memKey1)) {
            memJson = JSONObject.parseObject(String.valueOf(redisTemplate.opsForValue().get(memKey1)));
        }
        if (null != memJson && !cpuJson.isEmpty()) {
            log.error("网络拓扑鼠标悬浮--内存使用率 ：" + memJson.getDouble("value"));
            Double memRate = Optional.ofNullable(memJson.getDouble("value")).orElse(0.00);
            jsonObject.put("mem", String.format("%.2f", memRate) + "%");
        } else {
            log.error("网络拓扑鼠标悬浮--内存使用率获取为空！");
        }

        String diskUsageKey = "stca:" + deviceInfo.getDeviceCode() + "_diskRate";
        JSONObject diskJson = JSONObject.parseObject(String.valueOf(redisTemplate.opsForValue().get(diskUsageKey)));
        if (null != diskJson) {
            jsonObject.put("disk", String.format("%.2f", Double.parseDouble(diskJson.getString("value"))) + "%");
        } else {
            jsonObject.put("disk", "");
        }

        String portInfoKey = "stca:" + deviceInfo.getDeviceCode() + "_portInfo";
        JSONObject portInfoJson = JSONObject.parseObject(String.valueOf(redisTemplate.opsForValue().get(portInfoKey)));
        if (null != portInfoJson) {
            JSONArray portInfoArray = JSONArray.parseArray(portInfoJson.getString("value"));
            jsonObject.put("portInfo", portInfoArray);
        }

        String currentStep = (String) cacheUtils.getValueByKey(CommonConstant.DATA_DICT_KEY + "unit_convert_unit");
        List<ProertyMetadata> metadataList = proertyMetadataMapper.findMetadataByDevIdAndProId(deviceInfo.getProductId(), null);
        if (CollUtil.isNotEmpty(metadataList)) {
            Map<String, ProertyMetadata> metadataMap = metadataList.stream().collect(Collectors.toMap(ProertyMetadata::getCode, ProertyMetadata -> ProertyMetadata));
            String inSpeedKey = "stca:" + deviceInfo.getDeviceCode() + "_inSpeed";
            JSONObject inSpeedJson = JSONObject.parseObject(String.valueOf(redisTemplate.opsForValue().get(inSpeedKey)));
            if (null != inSpeedJson) {
                ProertyMetadata inSpeedMetadata = metadataMap.get("inSpeed");
                Double originStep = StringUtils.isNotEmpty(inSpeedMetadata.getOriginStep()) ? Double.valueOf(inSpeedMetadata.getOriginStep()) : 1024;
                Map<String, Object> valueMap = UnitConvertUtil.unitDynamic(inSpeedMetadata.getUnit(), inSpeedJson.getDouble("value"), originStep);
                jsonObject.put("inSpeed", valueMap.get("value") + "" + valueMap.get("unit"));
                log.error("网络拓扑鼠标悬浮--下行速率 ：" + valueMap.get("value") + " " + valueMap.get("unit"));
            } else {
                log.error("网络拓扑鼠标悬浮--下行速率 ：获取为空！");
            }

            String outSpeedKey = "stca:" + deviceInfo.getDeviceCode() + "_outSpeed";
            JSONObject outSpeedJson = JSONObject.parseObject(String.valueOf(redisTemplate.opsForValue().get(outSpeedKey)));
            if (null != outSpeedJson) {
                ProertyMetadata outSpeedMetadata = metadataMap.get("outSpeed");
                Double originStep = StringUtils.isNotEmpty(outSpeedMetadata.getOriginStep()) ? Double.valueOf(outSpeedMetadata.getOriginStep()) : 1024;
                Map<String, Object> valueMap = UnitConvertUtil.unitDynamic(outSpeedMetadata.getUnit(), outSpeedJson.getDouble("value"), originStep);
                jsonObject.put("outSpeed", valueMap.get("value") + "" + valueMap.get("unit"));
                log.error("网络拓扑鼠标悬浮--下行速率 ：" + valueMap.get("value") + " " + valueMap.get("unit"));
            } else {
                log.error("网络拓扑鼠标悬浮--上行速率 ：获取为空！");
            }
        }

        /*if (deviceInfo.getProductName().contains("矩阵")) {
            String inkey = "stca:" + deviceInfo.getDeviceCode() + "_inputPortOccupancy";
            String outkey = "stca:" + deviceInfo.getDeviceCode() + "_outputPortOccupancy";
            JSONObject inEntries = JSONObject.parseObject(String.valueOf(redisTemplate.opsForValue().get(inkey)));
            JSONObject outEntries = JSONObject.parseObject(String.valueOf(redisTemplate.opsForValue().get(outkey)));

            if (inEntries != null && !inEntries.isEmpty() && outEntries != null && !outEntries.isEmpty()) {
                HashMap<String, JSONObject> matirxMap = new HashMap<>();
                matirxMap.put("inEntries", inEntries);
                matirxMap.put("outEntries", outEntries);
                List<PortVo> resultList = getPortVoList(matirxMap);
                jsonObject.put("portStatus", resultList);
            }
        }*/

        jsonObject.put("id", deviceInfo.getId());
        jsonObject.put("code", deviceInfo.getDeviceCode());
        jsonObject.put("name", deviceInfo.getName());
        if (StringUtils.isNotEmpty(deviceInfo.getIp())) {
            jsonObject.put("ip", deviceInfo.getIp());
        } else {
            jsonObject.put("ip", "");
        }
        jsonObject.put("eth", new JSONArray());
        ProCatVo name = topoInfoMapper.findName(deviceInfo.getId());
        jsonObject.put("displayName", name.getDisplayName());
        jsonObject.put("categoryName", name.getCategoryName());
        return jsonObject;

    }


    public List<JSONObject> initStatus(DeviceInfo devInfo) {
        List<JSONObject> list = new ArrayList<>();
        List<String> codes = Arrays.asList(devInfo.getDeviceCode().split(","));
        List<DeviceInfo> deviceList = deviceInfoMapper.selectList(new QueryWrapper<DeviceInfo>().in("device_code", codes));
        if (deviceList != null && !deviceList.isEmpty()) {
            for (DeviceInfo dev : deviceList) {
                JSONObject statusObject = new JSONObject();
                statusObject.put("id", dev.getId());
                statusObject.put("deviceCode", dev.getDeviceCode());
                statusObject.put("status", dev.getStatus());
                statusObject.put("alarmStatus", dev.getAlarmStatus());
                List<AlarmHistory> alarmHistoryList;
                if (dataSourceTypeUtils.getDBType().equals("highgo")){
                    alarmHistoryList = alarmHistoryMapper.selectListByHg(dev.getId());
                }else {
                    alarmHistoryList = alarmHistoryMapper.selectList(new LambdaQueryWrapper<AlarmHistory>()
                            .eq(AlarmHistory::getDeviceId, dev.getId())
                            .eq(AlarmHistory::getAlarmStatus, "0")
                            .eq(AlarmHistory::getDelflag,"0")
                            .orderByDesc(AlarmHistory::getAlarmTime2));
                }
                if (CollUtil.isNotEmpty(alarmHistoryList)) {
                    JSONArray alarmArray = new JSONArray();
                    for (AlarmHistory alarmHistory : alarmHistoryList) {
                        JSONObject eleObject = new JSONObject();
                        eleObject.put("alarmName", alarmHistory.getTemplateName());
                        eleObject.put("alarmLevel", alarmHistory.getAlarmLevel());
                        if (StringUtils.isNotEmpty(alarmHistory.getAlarmDisplay())) {
                            JSONObject object = JSON.parseObject(alarmHistory.getAlarmDisplay());
                            eleObject.put("alarmDisplay", object.getJSONArray("alarmStructs"));
                        }
                        alarmArray.add(eleObject);
                    }
                    statusObject.put("alarmInfo", alarmArray);
                }

                if (dev.getEnable() != null && dev.getEnable() == 0) {
                    statusObject.put("isEnable", "false");
                }

                //火炬中心特有矩阵设备
                /*if (dev.getProductName().contains("矩阵")) {
                    String inkey = "stca:" + dev.getDeviceCode() + "_inputPortOccupancy";
                    String outkey = "stca:" + dev.getDeviceCode() + "_outputPortOccupancy";
                    JSONObject inEntries = JSONObject.parseObject(String.valueOf(redisTemplate.opsForValue().get(inkey)));
                    JSONObject outEntries = JSONObject.parseObject(String.valueOf(redisTemplate.opsForValue().get(outkey)));

                    if (inEntries != null && !inEntries.isEmpty() && outEntries != null && !outEntries.isEmpty()) {
                        HashMap<String, JSONObject> matirxMap = new HashMap<>();
                        matirxMap.put("inEntries", inEntries);
                        matirxMap.put("outEntries", outEntries);
                        statusObject.put("portStatus", getPortVoList(matirxMap));
                    }
                }*/

                //其他SNMP监控的设备
                String portInfoKey = "stca:" + dev.getDeviceCode() + "_portInfo";
                if (redisTemplate.hasKey(portInfoKey)) {
                    JSONObject portInfoObject = JSONObject.parseObject(String.valueOf(redisTemplate.opsForValue().get(portInfoKey)));
                    if (portInfoObject != null && !portInfoObject.isEmpty()) {
                        JSONArray portInfoArray = portInfoObject.getJSONArray("value");
                        ArrayList<PortVo> resList = new ArrayList<>();
                        ArrayList<PortVo> portVoList = new ArrayList<>();
                        for (int i = 0; i < portInfoArray.size(); i++) {
                            PortVo portVo = new PortVo();
                            portVo.setId(portInfoArray.getJSONObject(i).getJSONObject("index").getInteger("value"));
                            switch (portInfoArray.getJSONObject(i).getJSONObject("portStatus").getString("value")) {
                                case "连接":
                                    portVo.setStatus(1);
                                    break;
                                case "关闭":
                                    portVo.setStatus(0);
                                    break;
                                case "其他":
                                    portVo.setStatus(2);
                                    break;
                            }
                            portVoList.add(portVo);
                        }
                        portVoList.sort(Comparator.comparing(PortVo::getId));
                        resList.addAll(portVoList);
                        statusObject.put("portStatus", resList);
                    }
                }

                list.add(statusObject);
            }
        }
        return list;
    }


    private List<PortVo> getPortVoList(HashMap<String, JSONObject> matirxMap) {
        ArrayList<PortVo> resultList = new ArrayList<>();
        JSONObject inPortStatusObject = matirxMap.get("inEntries");
        if (inPortStatusObject != null && !inPortStatusObject.isEmpty()) {
            log.info("inPortStatusObject：" + inPortStatusObject);
            JSONObject inPortStatusMap = JSONObject.parseObject(inPortStatusObject.getString("value"));
            if (inPortStatusMap != null && !inPortStatusMap.isEmpty()) {
                ArrayList<PortVo> list1 = new ArrayList<>();
                for (Map.Entry entry : inPortStatusMap.entrySet()) {
                    PortVo portVo = new PortVo();
                    portVo.setId(Integer.parseInt(String.valueOf(entry.getKey())));
                    portVo.setType("in");
                    if (entry.getValue().equals("已占用")) {
                        portVo.setStatus(1);
                    } else {
                        portVo.setStatus(0);
                    }
                    list1.add(portVo);
                }
                list1.sort(Comparator.comparing(PortVo::getId));
                resultList.addAll(list1);
            }
        }

        JSONObject outPortStatusObject = matirxMap.get("outEntries");
        if (outPortStatusObject != null && !outPortStatusObject.isEmpty()) {
            log.info("outPortStatusObject：" + outPortStatusObject);
            JSONObject outPortStatusMap = JSONObject.parseObject(outPortStatusObject.getString("value"));
            if (outPortStatusMap != null && !outPortStatusMap.isEmpty()) {
                ArrayList<PortVo> list2 = new ArrayList<>();
                for (Map.Entry entry : outPortStatusMap.entrySet()) {
                    PortVo portVo = new PortVo();
                    portVo.setId(Integer.parseInt(String.valueOf(entry.getKey())));
                    portVo.setType("out");
                    if (entry.getValue().equals("已占用")) {
                        portVo.setStatus(1);
                    } else {
                        portVo.setStatus(0);
                    }
                    list2.add(portVo);
                }
                list2.sort(Comparator.comparing(PortVo::getId));
                resultList.addAll(list2);
            }
        }

        return resultList;
    }


}
