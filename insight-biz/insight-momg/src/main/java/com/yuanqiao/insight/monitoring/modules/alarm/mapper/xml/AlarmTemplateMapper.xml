<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.monitoring.modules.alarm.mapper.AlarmTemplateMapper">


    <resultMap id="subjectMetadata" type="com.yuanqiao.insight.service.product.entity.ProertyMetadata">
        <id column="id" property="id"/>
        <collection property="proertyMetadataList"
                    select="com.yuanqiao.insight.monitoring.modules.alarm.mapper.AlarmTemplateMapper.findMateCodeByPid"
                    column="id">
        </collection>
    </resultMap>

    <update id="isOnline">
        update momg_alarm_template
        set is_online = #{isOnline}
        where id = #{id}
    </update>

    <select id="findAlarmProByDevId" resultType="com.yuanqiao.insight.monitoring.modules.alarm.entity.AlarmTemplate">
        select alarm.*
        from momg_alarm_template alarm
                 left join momg_device_info dev on
            alarm.device_id = dev.id
                 left join momg_product pro on
            alarm.product_id = pro.id
        where alarm.device_id = #{deviceId}
           or alarm.product_id = #{productId}

    </select>

    <select id="findAlarmProByDevIdCount" resultType="java.lang.Integer">
        select count(1)
        from momg_alarm_template alarm
                 left join momg_device_info dev on
            alarm.device_id = dev.id
                 left join momg_product pro on
            alarm.product_id = pro.id
        where alarm.device_id = #{deviceId}
           or alarm.product_id = #{productId}

    </select>

    <select id="findProIdAndDevIdByDevice" resultType="com.yuanqiao.insight.service.device.entity.DeviceInfo">
        select id,
               product_id
        from momg_device_info
        where id = #{deviceId}
    </select>

    <select id="findAlarmTemplateByOtherCount" resultType="java.lang.Integer">

        select
        count(1)
        from
        momg_alarm_template tem
        left join momg_device_info dev on
        tem.device_id = dev.id
        left join momg_product pro on
        tem.product_id = pro.id
        <where>
            <if test=" name != null and name.trim()!='' ">
                and tem.name like '%${name}%'
            </if>

            <if test=" isOnline != null  ">
                and tem.is_online = #{isOnline}
            </if>

            <if test=" productId != null  and productId.trim() !='' ">
                and tem.product_id = #{productId}
            </if>
            <if test=" displayName != null  and displayName.trim() !='' ">
                and pro.display_name = #{displayName}
            </if>


        </where>
    </select>

    <select id="findRuleByTemId" resultType="com.yuanqiao.insight.acore.alarm.model.AlarmRuleModel">
        select *
        from momg_alarm_rule
        where alarm_template_id = #{id}
    </select>

    <select id="findRuleByTemIdCount" resultType="com.yuanqiao.insight.acore.alarm.model.AlarmRuleModel">
        select mpm.product_id,
               sdi.description,
               mpm.name,
               newTable.*
        from (
                 select mar.*,
                        mat.product_id
                 from momg_alarm_rule mar
                          left join momg_alarm_template mat on
                     mat.id = mar.alarm_template_id
                 where mar.alarm_template_id = #{id}) as newTable
                 left join momg_proerty_metadata mpm on
                    newTable.product_id = mpm.product_id
                and newTable.subject_index = mpm.code
                 left join (
            select dict.dict_code,
                   item.description,
                   item.item_value
            from sys_dict dict
                     left join sys_dict_item item on
                item.dict_id = dict.id
            where dict.dict_code = 'compareCondition') sdi on
            sdi.item_value = newTable.conditions
        where newTable.alarm_template_id = #{id}
    </select>


    <resultMap id="code" type="com.yuanqiao.insight.monitoring.modules.alarm.entity.NumberVo">
        <id column="id" property="id"/>

        <collection property="momgProertyMetadata"
                    select="com.yuanqiao.insight.monitoring.modules.alarm.mapper.AlarmTemplateMapper.findRuleBySubjectIndex"
                    column="id">

        </collection>


    </resultMap>


    <select id="findNameByName" resultMap="code">
        select
        pro.id ,
        pro.display_name
        from
        momg_product pro
        <where>
            <if test="displayname!=null and displayname.trim()!=''">
                pro.display_name like '%${displayname}%'
            </if>

            <if test="id!=null and id.trim()!=''">
                pro.id =#{id}
            </if>
        </where>

    </select>


    <select id="findRuleBySubjectIndex" resultMap="subjectMetadata">
        select
        id,
        code,
        name,
        data_type,
        origin_step,
        unit
        from
        momg_proerty_metadata
        <where>
            is_warning = '1' and (pid is null or pid = '')
            <if test="id!= null and id.trim()!=''">
                and product_id = #{id}
            </if>
        </where>

    </select>

    <select id="findTemAndRuleByDeviceIdCount" resultType="java.lang.Integer">
        select
        count(1)
        from
        momg_alarm_template tem
        left join momg_device_info dev on
        tem.device_id = dev.id
        left join momg_product pro on
        tem.product_id = pro.id
        <where>
            <if test="id!= null and id.trim()!='' ">
                and tem.id = #{id}
            </if>
            <if test="deviceId!= null and deviceId.trim()!='' ">
                and tem.device_id = #{deviceId}
            </if>
        </where>
    </select>

    <select id="getAlarmTemByDeviceId" resultType="com.yuanqiao.insight.acore.alarm.model.AlarmTemplateModel">
        select
        tem.*,dev.name as devName ,pro.display_name as proName
        from
        momg_alarm_template tem
        left join momg_alarm_template_dev td on
        tem.id = td.alarm_template_id
        left join momg_device_info dev on
        td.device_id = dev.id
        left join momg_product pro on
        tem.product_id = pro.id
        <where>
            <if test="deviceId!= null and deviceId.trim()!='' ">
                and td.device_id = #{deviceId}
            </if>
        </where>
        order by tem.create_time desc
    </select>

    <select id="getAlarmTemByProId" resultType="com.yuanqiao.insight.acore.alarm.model.AlarmTemplateModel">
        select
        tem.* ,pro.display_name as proName, pro.id as peoductId
        from
        momg_alarm_template tem
        left join momg_product pro on
        tem.product_id = pro.id
        <where>
            <if test="productId!= null and productId.trim()!='' ">
                and tem.product_id = #{productId}
                and tem.device_id is null
            </if>
        </where>
        order by tem.create_time desc
    </select>

    <select id="findMateCodeByPid" resultType="com.yuanqiao.insight.service.product.entity.ProertyMetadata">
        select id,
               code,
               name,
               data_type,
               origin_step,
               unit
        from momg_proerty_metadata
        where is_warning = '1'
          and pid = #{id}

    </select>
    <select id="alarmTemplateView"
            resultType="com.yuanqiao.insight.monitoring.modules.alarm.model.AlarmTemplateViewModel">
        SELECT
        ate.id AS template_id,
        ate.name AS template_name,
        COALESCE(SUM(ah.repeat_times), 0) AS repeat_times,
        COUNT(ah.id) AS alarm_count,
        MAX(ah.alarm_time2) AS alarm_time,
        COUNT(DISTINCT COALESCE(dev.device_id, ddi.id)) AS device_num
        FROM
        momg_alarm_template ate
        LEFT JOIN
        momg_alarm_history ah ON ah.template_id = ate.id
        LEFT JOIN
        momg_alarm_template_dev dev ON dev.ALARM_TEMPLATE_ID = ate.id
        LEFT JOIN
        momg_device_info ddi ON (
        dev.device_id IS NULL AND ddi.product_id = ate.product_id
        OR dev.device_id = ddi.id
        )
        where
            ah.delflag = 0
            <if test="start != null">
                and ah.alarm_time2 &gt;= #{start}
            </if>
            <if test="end != null">
                and ah.alarm_time2 &lt;= #{end}
            </if>
        GROUP BY
        ate.id, ate.name
    </select>
    <select id="pageList" resultType="com.yuanqiao.insight.monitoring.modules.alarm.entity.AlarmTemplate">
        SELECT tem.*
        FROM momg_alarm_template tem
                 LEFT JOIN momg_device_info mdi ON mdi.product_id = tem.product_id
                 LEFT JOIN momg_product mp ON mp.id = tem.product_id
        WHERE mdi.id = #{deviceId}
          AND EXISTS (SELECT 1
                      FROM momg_alarm_template_dev atd
                      WHERE atd.device_id = mdi.id AND atd.alarm_template_id = tem.id)
        UNION ALL
        SELECT tem.*
        FROM momg_alarm_template tem
                 LEFT JOIN momg_device_info mdi ON mdi.product_id = tem.product_id
                 LEFT JOIN momg_product mp ON mp.id = tem.product_id
        WHERE mdi.id = #{deviceId}
          AND NOT EXISTS (SELECT 1
                          FROM momg_alarm_template_dev atd
                          WHERE atd.alarm_template_id = tem.id)
    </select>


</mapper>
