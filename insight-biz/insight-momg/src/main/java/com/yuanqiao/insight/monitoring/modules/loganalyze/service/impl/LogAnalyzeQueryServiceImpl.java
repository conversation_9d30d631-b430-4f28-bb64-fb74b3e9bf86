package com.yuanqiao.insight.monitoring.modules.loganalyze.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.yuanqiao.insight.monitoring.modules.loganalyze.entity.LogAnalyzeQuery;
import com.yuanqiao.insight.monitoring.modules.loganalyze.entity.LogAnalyzeTerm;
import com.yuanqiao.insight.monitoring.modules.loganalyze.mapper.LogAnalyzeQueryMapper;
import com.yuanqiao.insight.monitoring.modules.loganalyze.mapper.LogAnalyzeTermMapper;
import com.yuanqiao.insight.monitoring.modules.loganalyze.service.ILogAnalyzeQueryService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 客户明细
 * @Author: jeecg-boot
 * @Date: 2024-02-02
 * @Version: V1.0
 */
@Service
@RequiredArgsConstructor
public class LogAnalyzeQueryServiceImpl extends MPJBaseServiceImpl<LogAnalyzeQueryMapper, LogAnalyzeQuery> implements ILogAnalyzeQueryService {

    private final LogAnalyzeTermMapper logAnalyzeTermMapper;

    @Override
    @Transactional
    public void add(LogAnalyzeQuery logAnalyzeQuery) {
        super.baseMapper.updateIsCurr(logAnalyzeQuery.getLogAnalyzeId(), "0");
        List<LogAnalyzeTerm> logAnalyzeTerms = new ArrayList<>();
        if (StringUtils.isNotBlank(logAnalyzeQuery.getId())) {
            logAnalyzeTerms = logAnalyzeTermMapper.selectList(new LambdaQueryWrapper<LogAnalyzeTerm>()
                    .eq(LogAnalyzeTerm::getTermQueryId, logAnalyzeQuery.getId()));
        }
        logAnalyzeQuery.setId(null);
        super.baseMapper.insert(logAnalyzeQuery);
        if (CollUtil.isNotEmpty(logAnalyzeTerms)) {
            logAnalyzeTerms.forEach(item -> {
                item.setId(null);
                item.setCreateTime(null);
                item.setTermQueryId(logAnalyzeQuery.getId());
                logAnalyzeTermMapper.insert(item);
            });
        }
    }

    @Override
    @Transactional
    public void toggleQuery(LogAnalyzeQuery logAnalyzeQuery) {
        super.baseMapper.updateIsCurr(logAnalyzeQuery.getLogAnalyzeId(), "0");
        logAnalyzeQuery.setIsCurr("1");
        super.baseMapper.updateById(logAnalyzeQuery);
    }

    @Override
    @Transactional
    public void delete(String queryId, String isCurr) {
        final boolean b = this.removeById(queryId);
        if ("1".equals(isCurr)) {
            LogAnalyzeQuery query = new LogAnalyzeQuery();
            query.setIsCurr("1");
            super.baseMapper.update(query, new LambdaQueryWrapper<LogAnalyzeQuery>()
                    .eq(LogAnalyzeQuery::getQueryName, "DEFAULT"));
        }
        if (b) {
            logAnalyzeTermMapper.delete(new LambdaQueryWrapper<LogAnalyzeTerm>()
                    .eq(LogAnalyzeTerm::getTermQueryId, queryId));
        }
    }
}
