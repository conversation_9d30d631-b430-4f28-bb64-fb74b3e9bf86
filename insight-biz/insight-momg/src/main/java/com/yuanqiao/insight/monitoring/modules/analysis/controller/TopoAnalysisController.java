package com.yuanqiao.insight.monitoring.modules.analysis.controller;

import com.yuanqiao.insight.monitoring.modules.topoRoom.entity.TopoRoom;
import com.yuanqiao.insight.monitoring.modules.topoRoom.mapper.TopoRoomMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Api(tags="大屏展示树")
@RestController
@RequestMapping("/data-analysis/topo")
@Slf4j
public class TopoAnalysisController {

    @Autowired
    private TopoRoomMapper topoRoomMapper;


    @AutoLog(value = "告警历史-告警轮播")
    @ApiOperation(value="告警历史-告警轮播", notes="告警历史-告警轮播")
    @GetMapping(value = "/room/tree2")
    public Result<?> tree2() {
        List<TopoRoom> list = topoRoomMapper.list();
        List<TopoRoom> topoRooms = buildTree2(list);
        return Result.OK(topoRooms);

    }


    public List<TopoRoom> buildTree2(List<TopoRoom> pidList) {
        Map<String, List<TopoRoom>> pidListMap =
                pidList.stream().collect(Collectors.groupingBy(TopoRoom::getPid));
        pidList.stream().forEach(item -> item.setChildren(pidListMap.get(item.getId())));
        //返回结果也改为返回顶层节点的list
        return pidListMap.get("");
    }
}
