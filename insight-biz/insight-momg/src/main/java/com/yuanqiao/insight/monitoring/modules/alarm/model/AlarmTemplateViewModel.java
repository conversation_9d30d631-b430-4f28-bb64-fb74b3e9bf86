package com.yuanqiao.insight.monitoring.modules.alarm.model;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/3/12
 */
@Getter
@Setter
public class AlarmTemplateViewModel {

    /**
     * 告警模板id
     */
    private String templateId;
    /**
     * 告警模板名称
     */
    private String templateName;
    /**
     * 重复次数
     */
    private Integer repeatTimes;
    /**
     * 告警数量
     */
    private Integer alarmCount;
    /**
     * 最近告警时间
     */
    private String alarmTime;
    /**
     * 绑定设备数量
     */
    private Integer deviceNum;

}
