package com.yuanqiao.insight.monitoring.modules.subscriber.sendhandler.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yuanqiao.insight.acore.config.SendMail3;
import com.yuanqiao.insight.common.util.common.LocalCacheUtils;
import com.yuanqiao.insight.system.config.ISendHandler;
import com.yuanqiao.insight.system.config.emus.NoticeType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.modules.system.entity.SysUsers;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 邮箱
 *
 * <AUTHOR>
 * @date 2023/3/13
 */
@Slf4j
@Component
public class EmailSendHandler implements ISendHandler {

    @Autowired
    private ISysUserService userService;

    LocalCacheUtils cacheUtils = LocalCacheUtils.getInstance();

    @Override
    public boolean support(String support) {
        return StringUtils.equals(support, NoticeType.EMAIL.getCode());
    }

    @Override
    public void send(JSONObject sendMsg, JSONObject template, Boolean isPar) {
        try {
            //String prefix = String.valueOf(cacheUtils.getValueByKey(CommonConstant.DATA_DICT_KEY + "attachmentPath_pathPrefix"));
            //根据通知配置ID获取必要参数
            String smtp = sendMsg.getString("url");
            String sendName = sendMsg.getString("username");
            String password = sendMsg.getString("password");
            String port = sendMsg.getString("port");
            String sender = sendMsg.getString("from");
            boolean sslEnable = sendMsg.getBoolean("sslEnable");
            //主题
            String subject = template.getString("subject");
            //消息内容
            String content = template.getString("content");
            //重组消息体
            if (isPar) {
                JSONObject par = template.getJSONObject("par");
                content = generateWelcome(par, content);
            }
            //附件
            List<String> attachmentPathList = new ArrayList<>();
            //["GBase8sV8.8daorudaochugongjuzhinan_1683517171566.pdf","GBase8sV8.8daorudaochugongjuzhinan_1683517171561.pdf"]
            /*JSONArray attachments = template.getJSONArray("attachments");
            for (int i = 0; i < attachments.size(); i++) {
                attachmentPathList.add(prefix + attachments.getString(i));
            }*/
            //收件方（根据用户ID获取用户的email）
            JSONArray sendTo = template.getJSONArray("sendTo");
            if (sendTo == null || sendTo.isEmpty()) {
                return;
            }
            final List<SysUsers> users = userService.list(new QueryWrapper<SysUsers>().lambda().in(SysUsers::getId, sendTo));

            //向每个用户发送一份邮件
            if (CollUtil.isNotEmpty(users)) {
                for (SysUsers user : users) {
                    try {
                        SendMail3.sendEmail(sender, password, smtp, user.getEmail(), sendName, subject, content, port,sslEnable, attachmentPathList);
                    } catch (Exception e) {
                        log.error("向收件方 ：" + user.getEmail() + " 发送邮件出现异常！", e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("邮件发送失败！", e);
        }
    }
}
