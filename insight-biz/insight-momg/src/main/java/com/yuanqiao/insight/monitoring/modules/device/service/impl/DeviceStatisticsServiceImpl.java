package com.yuanqiao.insight.monitoring.modules.device.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.cmdb.modules.assetscategory.util.CallPhantomJS;
import com.yuanqiao.insight.cmdb.modules.assetscategory.util.ChartUtils;
import com.yuanqiao.insight.common.util.common.TimeUtils;
import com.yuanqiao.insight.monitoring.modules.device.entity.NetworkDeviceVO;
import com.yuanqiao.insight.monitoring.modules.device.service.IDeviceStatisticsService;
import com.yuanqiao.insight.monitoring.modules.device.utils.PDFUtil;
import com.yuanqiao.insight.monitoring.modules.product.service.IProductTypeService;
import com.yuanqiao.insight.monitoring.modules.terminal.utils.ESUtils;
import com.yuanqiao.insight.service.device.entity.DeviceInfo;
import com.yuanqiao.insight.service.device.mapper.DeviceInfoMapper;
import com.yuanqiao.insight.service.product.entity.Product;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import com.yuanqiao.insight.service.product.mapper.ProertyMetadataMapper;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.util.DateUtils;
import org.jfree.chart.ChartColor;
import org.jfree.chart.ChartFactory;
import org.jfree.chart.JFreeChart;
import org.jfree.chart.plot.CategoryPlot;
import org.jfree.chart.plot.PlotOrientation;
import org.jfree.data.category.CategoryDataset;
import org.jfree.data.category.DefaultCategoryDataset;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class DeviceStatisticsServiceImpl implements IDeviceStatisticsService {


    @Value(value = "${dataCenter.reportFilePath}")
    private String reportFilePath;
    static String ES_ROUTER_DATASTREAM = "metrics-router-snmp";
    static String ES_SWITCH_DATASTREAM = "metrics-switch-snmp";
    @Autowired
    ESUtils esUtils;
    @Autowired
    private IProductTypeService productTypeService;
    @Autowired
    DeviceInfoMapper deviceInfoMapper;
    @Autowired
    private ProertyMetadataMapper proertyMetadataMapper;

    public List<NetworkDeviceVO> getNetworkDeviceStatistics(String starttime, String endtime) throws IOException, ParseException {
        List<String> datastreams = Arrays.asList(ES_SWITCH_DATASTREAM, ES_ROUTER_DATASTREAM);
        //status为null，表示查询所有设备
        List<DeviceInfo> infoList = getDevices("Network", DeviceInfo.builder().build());
        List<NetworkDeviceVO> networkDeviceStats = new ArrayList<>(infoList.size());
        for (int i = 0; i < infoList.size(); i++) {
            NetworkDeviceVO networkDeviceVO = new NetworkDeviceVO();
            String deviceCode = infoList.get(i).getDeviceCode();
            networkDeviceVO.setDeviceCode(deviceCode);
            networkDeviceVO.setName(infoList.get(i).getName());
            networkDeviceVO.setIp(infoList.get(i).getIp());
            networkDeviceVO.setType(infoList.get(i).getProductName());

            String portNum = esUtils.getPortNum(datastreams, deviceCode);
            networkDeviceVO.setPortNum(portNum);

            String runtime = esUtils.getRunTime(datastreams, "staticInfo.runTime", deviceCode);
            networkDeviceVO.setSysUptime(runtime);

            ProertyMetadata metadataByProductIdAndCode = proertyMetadataMapper.findMetadataByProductIdAndCode(infoList.get(i).getProductId(), "inputFlow");

            if (StringUtils.isNotBlank(metadataByProductIdAndCode.getMaxValue())) {
                double maxvalue = Double.parseDouble(metadataByProductIdAndCode.getMaxValue());
                Map<String, String> netMap = esUtils.getInSumForHasMaxValue(datastreams, infoList.get(i).getDeviceCode(), starttime, endtime, maxvalue);
                networkDeviceVO.setIn(netMap.get("inputFlowSum"));
                networkDeviceVO.setOut(netMap.get("outputFlowSum"));
            } else {
                //如果maxValue不存在，认为该参数是累加值，不会清零
                Map<String, Double> netMap = esUtils.getInSum(datastreams, deviceCode, starttime, endtime);
                networkDeviceVO.setIn(String.valueOf(netMap.get("inputFlowSum")));
                networkDeviceVO.setOut(String.valueOf(netMap.get("outputFlowSum")));
            }

            networkDeviceStats.add(networkDeviceVO);
        }
        return networkDeviceStats;
    }

    public List<NetworkDeviceVO> getNetworkCpuTop10() throws IOException {
        // 暂时没有交换机的设备，没有这个数据流
        // 如果使用es不存在的数据流，es会报错。目前的做法是es在初始化时把所有数据流都建好。至少建好大屏需要的这三个数据流
        List<String> datastreams = Arrays.asList(ES_SWITCH_DATASTREAM, ES_ROUTER_DATASTREAM);
        // status为1，表示查询上线的设备
        List<DeviceInfo> infoList = getDevices("Network", DeviceInfo.builder().status(1).build());
        List<String> list = infoList.stream().map(DeviceInfo::getDeviceCode).collect(Collectors.toList());
        Map<String, Double> cpuRateTop = esUtils.getCpuRateTop10(datastreams, list);


        List<NetworkDeviceVO> networkDeviceStats = new ArrayList<>(infoList.size());
        for (int i = 0; i < infoList.size(); i++) {
            NetworkDeviceVO networkDeviceVO = new NetworkDeviceVO();
            String deviceCode = infoList.get(i).getDeviceCode();
            networkDeviceVO.setDeviceCode(deviceCode);
            networkDeviceVO.setName(infoList.get(i).getName());
            networkDeviceVO.setIp(infoList.get(i).getIp());
            networkDeviceVO.setType(infoList.get(i).getProductName());

            networkDeviceVO.setCpuRate(cpuRateTop.getOrDefault(infoList.get(i).getDeviceCode(), 0D));

            networkDeviceStats.add(networkDeviceVO);
        }
        networkDeviceStats.sort((a, b) -> a.getCpuRate() - b.getCpuRate() >= 0 ? -1 : 1);
        List<NetworkDeviceVO> result = networkDeviceStats.subList(0, Math.min(networkDeviceStats.size(), 10));
        return result;
    }


    public List<NetworkDeviceVO> getNetworkMemTop10() throws IOException {
        List<String> datastreams = Arrays.asList(ES_SWITCH_DATASTREAM, ES_ROUTER_DATASTREAM);
        //status为1，表示查询上线的设备
        List<DeviceInfo> infoList = getDevices("Network", DeviceInfo.builder().status(1).build());
        List<String> list = infoList.stream().map(DeviceInfo::getDeviceCode).collect(Collectors.toList());
        Map<String, Double> memusedTop = esUtils.getMemUsedTop10(datastreams, list);

        List<NetworkDeviceVO> networkDeviceStats = new ArrayList<>(infoList.size());
        for (int i = 0; i < infoList.size(); i++) {
            NetworkDeviceVO networkDeviceVO = new NetworkDeviceVO();
            String deviceCode = infoList.get(i).getDeviceCode();
            networkDeviceVO.setDeviceCode(deviceCode);
            networkDeviceVO.setName(infoList.get(i).getName());
            networkDeviceVO.setIp(infoList.get(i).getIp());
            networkDeviceVO.setType(infoList.get(i).getProductName());

            networkDeviceVO.setMemUsed(memusedTop.getOrDefault(infoList.get(i).getDeviceCode(), 0D));

            networkDeviceStats.add(networkDeviceVO);
        }
        networkDeviceStats.sort((a, b) -> a.getMemUsed() - b.getMemUsed() >= 0 ? -1 : 1);
        List<NetworkDeviceVO> result = networkDeviceStats.subList(0, Math.min(networkDeviceStats.size(), 10));
        return result;
    }

    @Override
    public List<NetworkDeviceVO> getNetInOutTop() throws IOException {
        List<String> datastreams = Arrays.asList(ES_SWITCH_DATASTREAM, ES_ROUTER_DATASTREAM);
        //status为1，表示查询上线的设备
        List<DeviceInfo> infoList = getDevices("Network", DeviceInfo.builder().status(1).build());
        List<String> list = infoList.stream().map(DeviceInfo::getDeviceCode).collect(Collectors.toList());
        Map<String, Double> netInOutTop = esUtils.getNetInOutTop(datastreams, list);
        System.out.println(netInOutTop);

        List<NetworkDeviceVO> networkDeviceStats = new ArrayList<>(infoList.size());
        for (int i = 0; i < infoList.size(); i++) {
            NetworkDeviceVO networkDeviceVO = new NetworkDeviceVO();
            String deviceCode = infoList.get(i).getDeviceCode();
            networkDeviceVO.setDeviceCode(deviceCode);
            networkDeviceVO.setName(infoList.get(i).getName());
            networkDeviceVO.setIp(infoList.get(i).getIp());
            networkDeviceVO.setType(infoList.get(i).getProductName());

            networkDeviceVO.setNetInOutTop(netInOutTop.getOrDefault(infoList.get(i).getDeviceCode(), 0D));

            networkDeviceStats.add(networkDeviceVO);
        }
        networkDeviceStats.sort((a, b) -> a.getNetInOutTop() - b.getNetInOutTop() >= 0 ? -1 : 1);
        List<NetworkDeviceVO> result = networkDeviceStats.subList(0, Math.min(networkDeviceStats.size(), 10));
        return result;
    }

    @Override
    public List<NetworkDeviceVO> getNetSpeedTop() throws IOException {
        List<String> datastreams = Arrays.asList(ES_SWITCH_DATASTREAM, ES_ROUTER_DATASTREAM);
        //status为1，表示查询上线的设备
        List<DeviceInfo> infoList = getDevices("Network", DeviceInfo.builder().status(1).build());
        List<String> list = infoList.stream().map(DeviceInfo::getDeviceCode).collect(Collectors.toList());
        Map<String, Double> netSpeedTop = esUtils.getNetSpeedTop(datastreams, list);

        List<NetworkDeviceVO> networkDeviceStats = new ArrayList<>(infoList.size());
        for (int i = 0; i < infoList.size(); i++) {
            NetworkDeviceVO networkDeviceVO = new NetworkDeviceVO();
            String deviceCode = infoList.get(i).getDeviceCode();
            networkDeviceVO.setDeviceCode(deviceCode);
            networkDeviceVO.setName(infoList.get(i).getName());
            networkDeviceVO.setIp(infoList.get(i).getIp());
            networkDeviceVO.setType(infoList.get(i).getProductName());

            networkDeviceVO.setNetSpeed(netSpeedTop.getOrDefault(infoList.get(i).getDeviceCode(), 0D));

            networkDeviceStats.add(networkDeviceVO);
        }
        networkDeviceStats.sort((a, b) -> a.getNetSpeed() - b.getNetSpeed() >= 0 ? -1 : 1);
        List<NetworkDeviceVO> result = networkDeviceStats.subList(0, Math.min(networkDeviceStats.size(), 10));
        return result;
    }

    public List<DeviceInfo> getDevices(String typeCode, DeviceInfo deviceInfo) {
        IPage<DeviceInfo> infoIPage = new Page<>();

        List<Product> productList = productTypeService.selectProductListByTypeCode(typeCode);
        if (CollUtil.isNotEmpty(productList)) {
            List<String> productIdList = productList.stream().map(Product::getId).collect(Collectors.toList());
//            Map<String, List<String>> loginUserMap = groupAndUserAuthControl.getGroupIdsAndDeptIdsByLoginUser();

            infoIPage = deviceInfoMapper.getDeviceByCategoryIds(new Page<>(1, -1), productIdList, deviceInfo, null, null);
        }
        return infoIPage.getRecords();
    }


    @Override
    public String exportPdf(String starttime,String endtime) throws IOException, InvocationTargetException, NoSuchMethodException, IllegalAccessException, ParseException {


        List<NetworkDeviceVO> networkDeviceStatistics = getNetworkDeviceStatistics(starttime, endtime);
        List<NetworkDeviceVO> networkCpuTop10 = getNetworkCpuTop10();
        List<NetworkDeviceVO> networkMemTop10 = getNetworkMemTop10();
        List<NetworkDeviceVO> netSpeedTop = getNetSpeedTop();
        List<NetworkDeviceVO> netInOutTop = getNetInOutTop();

        //时间
        String tempStr1 = DateUtils.formatDateHMS(new Date()).replaceAll(":", "");
        String fileNameTimeSuffix = tempStr1.replaceAll("-", "").replaceAll(" ", "");

        //保留三天内数据（含今天）
        List<String> dayStrList = TimeUtils.getDaysBetween(TimeUtils.getBeforeDateStr(tempStr1.split(" ")[0], -2), tempStr1.split(" ")[0]);
        File directory = new File(this.reportFilePath);
        File[] directoryFiles = directory.listFiles();
        if (directoryFiles != null && directoryFiles.length > 0) {
            for (int i = 0; i < directoryFiles.length; i++) {
                String directoryFilePath = directoryFiles[i].getPath();
                String lastPathStr = directoryFilePath.substring(directoryFilePath.lastIndexOf("\\") + 1);
                if (!dayStrList.contains(lastPathStr)) {
                    CallPhantomJS.deleteFolders(directoryFiles[i].getPath());
                }
            }
        }

        String photoPathUrl = this.reportFilePath + "/" + tempStr1.split(" ")[0] + "/";
        File file = new File(photoPathUrl);
        if (!file.isDirectory()) {
            file.mkdirs();
        }

        String[] photoPath = new String[4];
        if (null != networkCpuTop10 && !networkCpuTop10.isEmpty()) {
            photoPath[0] = getPhoto("CPU使用率TOP10",networkCpuTop10, photoPathUrl, fileNameTimeSuffix,"getCpuRate");
        }
        if (null != networkMemTop10 && !networkMemTop10.isEmpty()) {
            photoPath[1] = getPhoto("内存使用率TOP10",networkMemTop10, photoPathUrl, fileNameTimeSuffix,"getMemUsed");
        }
        if (null != netSpeedTop && !netSpeedTop.isEmpty()) {
            photoPath[2] = getPhoto("网络吞吐量Top10",netSpeedTop, photoPathUrl, fileNameTimeSuffix,"getNetSpeed");
        }
        if (null != netInOutTop && !netInOutTop.isEmpty()) {
            photoPath[3] = getPhoto("总流量TOP10",netInOutTop, photoPathUrl, fileNameTimeSuffix,"getNetInOutTop");
        }


        String pdfPath = photoPathUrl + "/" + "Network Device Statistics Report " + fileNameTimeSuffix + ".pdf";
        PDFUtil pdfUtil = new PDFUtil();
        pdfUtil.createPDF(starttime,endtime,networkDeviceStatistics,pdfPath,photoPath);
        CallPhantomJS.deleteScreenShort(photoPath);

        return pdfPath;
    }



    private String getPhoto(String title,List<NetworkDeviceVO> devices, String photoPathUrl, String fileNameSuffix,String fieldName) throws InvocationTargetException, NoSuchMethodException, IllegalAccessException {
        ChartUtils chartUtils = new ChartUtils();
        CategoryDataset dataset = getDataSet(devices, fieldName);// 2. 构造chart
        JFreeChart chart = ChartFactory.createBarChart(
                title, // 图表标题
                "", // 目录轴的显示标签--横轴
                "", // 数值轴的显示标签--纵轴
                dataset, // 数据集
                PlotOrientation.HORIZONTAL, // 图表方向：水平
                true, // 是否显示图例(对于简单的柱状图必须
                false, // 是否生成工具
                false // 是否生成URL链接
        );
        CategoryPlot cp = chart.getCategoryPlot();
        cp.setBackgroundPaint(ChartColor.WHITE); // 背景色设置
        cp.setRangeGridlinePaint(ChartColor.GRAY); // 网格线色设置
        // 3. 处理chart中文显示问题
        chartUtils.processChart(chart);
        // 4. chart输出图片
        String barStr = chartUtils.writeChartAsImage(chart, photoPathUrl, fieldName + fileNameSuffix);
        return barStr;
    }


    private static CategoryDataset getDataSet(List<NetworkDeviceVO> networkDevices,String methodName) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {

        Class<?> clazz = NetworkDeviceVO.class;
        Method method = clazz.getMethod(methodName);
        DefaultCategoryDataset dataset = new DefaultCategoryDataset();
        for (NetworkDeviceVO deviceVO : networkDevices) {
            Double value = (Double)method.invoke(deviceVO);
            dataset.addValue(value, deviceVO.getName(), deviceVO.getName());
        }
        return dataset;
    }
}
