package com.yuanqiao.insight.monitoring.modules.device.controller;

import com.yuanqiao.insight.service.statuscache.AttributesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @Description: 设备表
 * @Author: jeecg-boot
 * @Date: 2021-03-05
 * @Version: V1.0
 */
@Api(tags = "设备状态")
@RestController
@RequestMapping("/device/statuscache")
@Slf4j
public class StatusCacheController {

    @Autowired
    private AttributesService attributesService;

    @AutoLog(value = "根据Key获取状态")
    @ApiOperation(value="根据Key获取状态", notes="根据Key获取状态")
    @PostMapping(value = "/getStatusByKey")
    Result<?> getStatusByKey(String key){
        return Result.OK(attributesService.getStatusByKey(key));
    }
    @AutoLog(value = "根据Key获取状态")
    @ApiOperation(value="根据Key获取状态", notes="根据Key获取状态")
    @PostMapping(value = "/getStatusByKeys")
    Result<?> getStatusByKeys(@RequestBody Map keys){
        List<Map> list= (List<Map>) keys.get("keyList");
        for(Map map:list){
            map.put("status",attributesService.getStatusByKey((String) map.get("key")));
        }
        return Result.OK(list);
    }

}
