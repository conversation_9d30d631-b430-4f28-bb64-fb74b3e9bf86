package com.yuanqiao.insight.monitoring.modules.alarm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanqiao.insight.monitoring.modules.alarm.entity.AlarmRule;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 告警规则
 * @Author: jeecg-boot
 * @Date:   2021-03-03
 * @Version: V1.0
 */
@Component
public interface AlarmRuleMapper extends BaseMapper<AlarmRule> {


    List<AlarmRule> selectAllAlarmRule();

    void delRuleByTemId(@Param("id") String id);

    void delRuleByTemArrayId (@Param("arrayList")List arrayList );
}
