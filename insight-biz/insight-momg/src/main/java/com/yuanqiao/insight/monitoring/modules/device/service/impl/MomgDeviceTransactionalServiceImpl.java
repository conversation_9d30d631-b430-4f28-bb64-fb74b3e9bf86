package com.yuanqiao.insight.monitoring.modules.device.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.aggregations.CalendarInterval;
import co.elastic.clients.elasticsearch._types.aggregations.DateHistogramBucket;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.TrackHits;
import co.elastic.clients.elasticsearch.indices.ElasticsearchIndicesClient;
import co.elastic.clients.json.JsonData;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.yuanqiao.insight.acore.dataReport.util.DataReportUtils;
import com.yuanqiao.insight.cmdb.modules.assets.entity.Assets;
import com.yuanqiao.insight.cmdb.modules.assets.entity.AssetsAct;
import com.yuanqiao.insight.cmdb.modules.assets.entity.AssetsLog;
import com.yuanqiao.insight.cmdb.modules.assets.entity.CmdbAssetsRelation;
import com.yuanqiao.insight.cmdb.modules.assets.mapper.AssetsActMapper;
import com.yuanqiao.insight.cmdb.modules.assets.mapper.AssetsMapper;
import com.yuanqiao.insight.cmdb.modules.assets.mapper.CmdbAssetsRelationMapper;
import com.yuanqiao.insight.cmdb.modules.assets.service.IAssetsActService;
import com.yuanqiao.insight.cmdb.modules.assets.service.IAssetsLogService;
import com.yuanqiao.insight.cmdb.modules.assets.service.IAssetsService;
import com.yuanqiao.insight.cmdb.modules.assetscategory.entity.AssetsCategory;
import com.yuanqiao.insight.cmdb.modules.assetscategory.service.IAssetsCategoryService;
import com.yuanqiao.insight.cmdb.modules.extendField.entity.ExtendField;
import com.yuanqiao.insight.cmdb.modules.extendField.service.IExtendFieldService;
import com.yuanqiao.insight.cmdb.modules.extendValue.entity.ExtendValue;
import com.yuanqiao.insight.cmdb.modules.extendValue.mapper.ExtendValueMapper;
import com.yuanqiao.insight.cmdb.modules.extendValue.service.IExtendValueService;
import com.yuanqiao.insight.cmdb.modules.status.entity.CmdbStatus;
import com.yuanqiao.insight.cmdb.modules.status.service.ICmdbStatusService;
import com.yuanqiao.insight.cmdb.modules.supplier.entity.CmdbSupplier;
import com.yuanqiao.insight.cmdb.modules.supplier.service.ICmdbSupplierService;
import com.yuanqiao.insight.common.util.common.CustomConvertUtil;
import com.yuanqiao.insight.common.util.common.LocalCacheUtils;
import com.yuanqiao.insight.common.util.common.RedisUtils;
import com.yuanqiao.insight.common.util.common.UnitConvertUtil;
import com.yuanqiao.insight.monitoring.modules.alarm.entity.AlarmHistory;
import com.yuanqiao.insight.monitoring.modules.alarm.service.IAlarmHistoryService;
import com.yuanqiao.insight.monitoring.modules.device.entity.MomgLogDevice;
import com.yuanqiao.insight.monitoring.modules.device.service.IMomgDeviceTransactionalService;
import com.yuanqiao.insight.monitoring.modules.device.service.IMomgLogDeviceService;
import com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice;
import com.yuanqiao.insight.monitoring.modules.terminal.mapper.TerminalDeviceMapper;
import com.yuanqiao.insight.monitoring.modules.terminal.service.ITerminalDeviceService;
import com.yuanqiao.insight.monitoring.modules.topoCabinet.entity.TopoCabinet;
import com.yuanqiao.insight.monitoring.modules.topoCabinet.mapper.TopoCabinetMapper;
import com.yuanqiao.insight.monitoring.modules.topoCabinet.service.ITopoCabinetService;
import com.yuanqiao.insight.monitoring.modules.topoCabinet2device.entity.TopoCabinet2device;
import com.yuanqiao.insight.monitoring.modules.topoCabinet2device.mapper.TopoCabinet2deviceMapper;
import com.yuanqiao.insight.monitoring.modules.topoRoom.entity.TopoRoom;
import com.yuanqiao.insight.monitoring.modules.topoRoom.service.ITopoRoomService;
import com.yuanqiao.insight.mutual.tags.entity.UtlTagResource;
import com.yuanqiao.insight.mutual.tags.service.IUtlTagResourceService;
import com.yuanqiao.insight.service.ScheduleSetting.entity.ScheduleSetting;
import com.yuanqiao.insight.service.ScheduleSetting.service.IScheduleSettingService;
import com.yuanqiao.insight.service.device.entity.*;
import com.yuanqiao.insight.service.device.mapper.DeviceInfoMapper;
import com.yuanqiao.insight.service.device.mapper.MomgDevice2assetsMapper;
import com.yuanqiao.insight.service.device.mapper.MomgGateway2deviceMapper;
import com.yuanqiao.insight.service.device.service.*;
import com.yuanqiao.insight.service.product.entity.MomgProductTransferJob;
import com.yuanqiao.insight.service.product.entity.Product;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import com.yuanqiao.insight.service.product.mapper.MomgProductTransferJobMapper;
import com.yuanqiao.insight.service.product.mapper.ProductMapper;
import com.yuanqiao.insight.service.product.mapper.ProertyMetadataMapper;
import com.yuanqiao.insight.service.product.service.IProductService;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.mq.RedisMq;
import org.jeecg.common.mq.stream.Streams;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.constant.CommonConstant;
import org.jeecg.modules.system.entity.SysTerminalUser;
import org.jeecg.modules.system.mapper.SysTerminalUserMapper;
import org.jeecg.modules.util.CodeUtils;
import org.jeecg.modules.util.KeyUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 设备服务类--事务
 * @Author: jeecg-boot
 * @Date: 2021-05-19
 * @Version: V1.0
 */
@Service
public class MomgDeviceTransactionalServiceImpl extends MPJBaseServiceImpl<DeviceInfoMapper, DeviceInfo> implements IMomgDeviceTransactionalService {

    @Autowired
    private IDeviceInfoService deviceInfoService;
    @Autowired
    DeviceInfoMapper deviceInfoMapper;
    @Autowired
    private ProductMapper productMapper;
    @Autowired
    private MomgProductTransferJobMapper productTransferJobMapper;
    @Autowired
    private ProertyMetadataMapper proertyMetadataMapper;
    @Autowired
    private TerminalDeviceMapper terminalDeviceMapper;
    @Autowired
    private ITerminalDeviceService terminalDeviceService;
    @Autowired
    private MomgGateway2deviceMapper gateway2deviceMapper;
    @Autowired
    private IDeviceConnectInfoService deviceConnectInfoService;
    @Autowired
    private IScheduleSettingService scheduleSettingService;
    @Autowired
    private IMomgLogDeviceService momgLogDeviceService;
    @Autowired
    private IAssetsService assetsService;
    @Autowired
    private AssetsMapper assetsMapper;
    @Autowired
    private IProductService productService;
    @Autowired
    private IAssetsCategoryService categoryService;
    @Autowired
    private ICmdbStatusService cmdbStatusService;
    @Autowired
    private MomgDevice2assetsMapper device2assetsMapper;
    @Autowired
    private ExtendValueMapper extendValueMapper;
    @Autowired
    private IExtendFieldService iExtendFieldService;
    @Autowired
    private IExtendValueService iExtendValueService;
    @Autowired
    private ICmdbSupplierService cmdbSupplierService;
    @Autowired
    private CmdbAssetsRelationMapper cmdbAssetsRelationMapper;
    @Autowired
    private ITopoRoomService topoRoomService;
    @Autowired
    private TopoCabinetMapper topoCabinetMapper;
    @Autowired
    private TopoCabinet2deviceMapper topoCabinet2deviceMapper;
    @Autowired
    private ITopoCabinetService topoCabinetService;
    @Autowired
    private IUtlTagResourceService utlTagResourceService;
    @Autowired
    private IAlarmHistoryService alarmHistoryService;
    @Autowired
    private IAssetsLogService iAssetsLogService;
    @Autowired
    private IAssetsActService assetsActService;
    @Autowired
    private AssetsActMapper assetsActMapper;
    @Autowired
    private IMomgGroup2DeviceService group2DeviceService;
    @Autowired
    private IMomgZabbixAlarmService zabbixAlarmService;
    @Autowired
    private IMomgAbutment2deviceService abutment2deviceService;
    @Autowired
    private SysTerminalUserMapper sysTerminalUserMapper;
    @Autowired
    private CodeUtils codeUtils;
    @Autowired
    private RedisMq redisMq;
    @Autowired
    private RedisUtils redisUtil;
    @Autowired
    public ElasticsearchClient client;
    @Autowired
    private DataReportUtils dataReportUtils;

    @Value("${platform.uniqueCode}")
    private String platformCode;

    @Transactional
    public Result<?> addDeviceInfo(JSONObject infos) {

        if (this.isOverNumber()){
            return Result.error("当前设备存量已超出授权证书限制！");
        }

        DeviceInfo deviceInfo = null;
        Assets assetsInfo = null;
        List<DeviceConnectInfo> templateList = null;
        JSONObject devObj = infos.getJSONObject("deviceInfo");
        if (devObj != null && !devObj.isEmpty()) {
            deviceInfo = JSON.parseObject(devObj.toJSONString(), DeviceInfo.class);
        }
        JSONObject assetsObj = infos.getJSONObject("assetsInfo");
        if (assetsObj != null && !assetsObj.isEmpty()) {
            assetsInfo = JSON.parseObject(assetsObj.toJSONString(), Assets.class);
        }
        JSONArray templateArray = infos.getJSONArray("templateList");
        if (templateArray != null && !templateArray.isEmpty()) {
            templateList = JSONObject.parseArray(templateArray.toJSONString(), DeviceConnectInfo.class);
        }

        if (deviceInfo != null && StringUtils.isNotEmpty(deviceInfo.getDeviceCode()) && StringUtils.isNotEmpty(deviceInfo.getName()) && StringUtils.isNotEmpty(deviceInfo.getProductId())) {
            if (deviceInfo.getDeviceCode().contains("_")) {
                return Result.error("设备唯一标识中不得包含“_”符号！");
            }
            List<DeviceInfo> deviceInfoList = deviceInfoService.list();
            if (CollUtil.isNotEmpty(deviceInfoList)) {
                DeviceInfo finalDeviceInfo = deviceInfo;
                List<DeviceInfo> duplicateNameOrCodeList = deviceInfoList.stream().filter(d -> StringUtils.equals(d.getDeviceCode(), finalDeviceInfo.getDeviceCode()) || StringUtils.equals(d.getName(), finalDeviceInfo.getName())).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(duplicateNameOrCodeList)) {
                    return Result.error("设备名称或唯一标识已存在！");
                }
                if (StringUtils.isNotEmpty(deviceInfo.getIp())) {
                    List<DeviceInfo> duplicaateIpList = deviceInfoList.stream().filter(d -> StringUtils.equals(d.getIp(), finalDeviceInfo.getIp())).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(duplicaateIpList)) {
                        return Result.error("设备IP已存在！");
                    }
                }
            }

            DeviceInfo leastGatewayDevice = new DeviceInfo();
            TerminalDevice terminalDevice = null;
            List<MomgProductTransferJob> productTransferJobList = null;
            List<String> transferTypeList = null;
            Product product = deviceInfoService.getProductById(deviceInfo.getProductId(), null);
            if (product != null) {
                productTransferJobList = productTransferJobMapper.selectAllInfo(product.getId());
                transferTypeList = productTransferJobList.stream().map(MomgProductTransferJob::getCollectType).collect(Collectors.toList());
            } else {
                return Result.error("设备的产品不存在！");
            }
            JSONObject resObj = insertDevInfo(infos, deviceInfo, product, transferTypeList, leastGatewayDevice, "");
            if (resObj.getBoolean("result")) {
                try {
                    insertDevData(deviceInfo, product, transferTypeList, productTransferJobList, leastGatewayDevice, templateList, terminalDevice, assetsInfo, resObj);
                } catch (Exception e) {
                    log.error("添加设备同时添加关联数据时出现异常！", e);
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return Result.error("添加设备同时添加关联数据时出现异常！" + e);
                }
            } else {
                return Result.error(resObj.getString("errorMsg"));
            }
        } else {
            return Result.error("设备基本信息不存在！");
        }

        return Result.OK("设备以及关联数据添加成功！");

    }

    @Transactional
    public Result<?> addAssetsInfo(JSONObject infos) {

        DeviceInfo deviceInfo = null;
        Assets assetsInfo = null;
        List<DeviceConnectInfo> templateList = null;
        JSONObject devObj = infos.getJSONObject("deviceInfo");
        if (devObj != null && !devObj.isEmpty()) {
            deviceInfo = JSON.parseObject(devObj.toJSONString(), DeviceInfo.class);
        }
        JSONObject assetsObj = infos.getJSONObject("assetsInfo");
        if (assetsObj != null && !assetsObj.isEmpty()) {
            assetsInfo = JSON.parseObject(assetsObj.toJSONString(), Assets.class);
        }
        JSONArray templateArray = infos.getJSONArray("templateList");
        if (templateArray != null && !templateArray.isEmpty()) {
            templateList = JSONObject.parseArray(templateArray.toJSONString(), DeviceConnectInfo.class);
        }

        if (assetsInfo != null) {
            try {
                insertAssets_A(assetsInfo, "");
            } catch (Exception e) {
                log.error("添加资产时出现异常！", e);
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return Result.error("添加资产时出现异常！" + e.getMessage());
            }

            if (deviceInfo != null) {
                if (this.isOverNumber()) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return Result.error("当前设备存量已超出授权证书限制！");
                }

                if (deviceInfo.getDeviceCode().contains("_")) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return Result.error("设备唯一标识中不得包含“_”符号！");
                }
                DeviceInfo allByName = deviceInfoMapper.selectOne(new QueryWrapper<DeviceInfo>().eq("name", deviceInfo.getName()).or().eq("device_code", deviceInfo.getDeviceCode()));
                if (allByName != null && !allByName.getId().equals(deviceInfo.getId())) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return Result.error("设备名称或唯一标识已存在！");
                }
                // 预置设备相关数据
                DeviceInfo leastGatewayDevice = new DeviceInfo();
                TerminalDevice terminalDevice = null;
                List<MomgProductTransferJob> productTransferJobList = null;
                List<String> transferTypeList = null;
                Product product = deviceInfoService.getProductById(deviceInfo.getProductId(), null);
                if (product != null) {
                    productTransferJobList = productTransferJobMapper.selectAllInfo(product.getId());
                    transferTypeList = productTransferJobList.stream().map(MomgProductTransferJob::getCollectType).collect(Collectors.toList());
                } else {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return Result.error("设备的产品不存在！");
                }

                /**
                 * 添加设备基本信息
                 */
                JSONObject resObj = null;
                try {
                    resObj = insertDevInfo(infos, deviceInfo, product, transferTypeList, leastGatewayDevice, "");
                } catch (Exception e) {
                    log.error("添加资产同时添加关联的设备时出现异常！", e);
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return Result.error("添加资产同时添加关联的设备时出现异常！");
                }

                if (resObj != null && resObj.getBoolean("result")) {
                    try {
                        insertDevData(deviceInfo, product, transferTypeList, productTransferJobList, leastGatewayDevice, templateList, terminalDevice, null, resObj);
                    } catch (Exception e) {
                        log.error("添加设备同时添加关联数据时出现异常！", e);
                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                        return Result.error("添加设备同时添加关联数据时出现异常！");
                    }
                } else {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return Result.error(resObj.getString("errorMsg"));
                }

                /**
                 * 添加设备资产关系
                 */
                MomgDevice2assets device2assets = new MomgDevice2assets();
                device2assets.setDeviceId(deviceInfo.getId());
                device2assets.setAssetsId(assetsInfo.getId());
                device2assetsMapper.insert(device2assets);
            }
        } else {
            return Result.error("资产信息不存在！");
        }

        return Result.OK("资产以及关联数据添加成功！");

    }

    @Transactional
    public Result<?> addAssetsAct(JSONObject infos) {
        Assets assetsInfo = null;
        JSONObject assetsObj = infos.getJSONObject("assetsInfo");
        if (assetsObj != null && !assetsObj.isEmpty()) {
            assetsInfo = JSON.parseObject(assetsObj.toJSONString(), Assets.class);
        }

        if (assetsInfo != null) {
            try {
                List<AssetsAct> duplicateAssetsList = assetsActMapper.selectAssetsUnion(assetsInfo.getAssetsName(), assetsInfo.getAssetsCode(), null);
                if (CollUtil.isNotEmpty(duplicateAssetsList)) {
                    return Result.error("资产_流程临时数据的名称或标识已存在！");
                }
                if (StringUtils.isNotEmpty(assetsInfo.getAssetsCategoryId())) {
                    AssetsCategory assetsCategory = categoryService.getById(assetsInfo.getAssetsCategoryId());
                    if (assetsCategory != null) {
                        assetsInfo.setAssetsCategoryId(assetsCategory.getId());
                    } else {
                        throw new RuntimeException("关联的资产的资产类型不存在！");
                    }
                }
                if (StringUtils.isNotEmpty(infos.getString("deviceInfo"))) {
                    JSONObject devObj = infos.getJSONObject("deviceInfo");
                    DeviceInfo deviceInfo = JSON.parseObject(devObj.toJSONString(), DeviceInfo.class);
                    if (deviceInfo.getDeviceCode().contains("_")) {
                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                        return Result.error("设备唯一标识中不得包含“_”符号！");
                    }
                    List<DeviceInfo> duplicateDevList = assetsActMapper.selectDevUnion(deviceInfo.getName(), deviceInfo.getDeviceCode(), deviceInfo.getId());
                    if (CollUtil.isNotEmpty(duplicateDevList)) {
                        return Result.error("设备名称或唯一标识已存在！");
                    }
                    Product product = deviceInfoService.getProductById(deviceInfo.getProductId(), null);
                    if (product == null) {
                        return Result.error("设备的产品不存在！");
                    }
                }
                if (StringUtils.isNotEmpty(assetsInfo.getProducerId())) {
                    CmdbSupplier supplier = cmdbSupplierService.getById(assetsInfo.getProducerId());
                    if (supplier != null) {
                        assetsInfo.setProducerName(supplier.getName());
                    } else {
                        throw new RuntimeException("关联的资产的供应商不存在！");
                    }
                }
                assetsInfo.setDelflag(CommonConstant.DEL_FLAG_0);
                assetsInfo.setAssetsUnique(codeUtils.getCode("", "cmdb_assets_act", "assets_unique"));
                assetsInfo.setAssetsId(UUID.randomUUID().toString().replace("-", ""));
                AssetsAct assetsAct = CustomConvertUtil.ConvertClass(assetsInfo, AssetsAct.class);
                assetsAct.setOperateType("0");
                assetsAct.setAssociateData(infos.toJSONString());
                assetsActService.save(assetsAct);
            } catch (Exception e) {
                log.error("添加资产_流程临时数据时出现异常！", e);
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return Result.error("添加资产_流程临时数据时出现异常！" + e.getMessage());
            }
        } else {
            return Result.error("资产_流程临时数据不存在！");
        }

        return Result.OK("资产_流程临时数据以及关联数据添加成功！");
    }


    // 添加设备基本信息
    public JSONObject insertDevInfo(JSONObject infos, DeviceInfo deviceInfo, Product product, List<String> transferTypeList, DeviceInfo leastGatewayDevice, String operate) {
        JSONObject resObj = new JSONObject();

        try {
            //网关产品下属的设备自动设置成在线状态（考虑网关JMX自监控，此处关闭网关设备默认在线）
//            if (product.getProductType().equals("1")) {
//                deviceInfo.setStatus(1);
//            }

            //设备自动设置设备类型
            if (!transferTypeList.contains("0") || product.getProductType().equals("1")) {
                //拉模式、网关设备
                deviceInfo.setDeviceType(1234567890);
            } else {
                //推模式(同步添加终端)
                int type = 6;
                switch (product.getName()) {
                    case "ServerPush":
                        //displayName = "服务器";
                        type = 0;
                        break;
                    case "DeskTopPush":
                        //displayName = "桌面机";
                        type = 6;
                        break;
                    case "NoteBookPush":
                        //displayName = "笔记本";
                        type = 8;
                        break;
                }
                deviceInfo.setDeviceType(type);
            }
            //默认不告警
            deviceInfo.setAlarmStatus(0);
            //默认启用
            deviceInfo.setEnable(1);
            //绑定平台标识
            deviceInfo.setPlatformCode(platformCode);

            //拉模式设备绑定初始网关(未删除且已启用)，设备表gatewayCode赋值
            if (transferTypeList.contains("1")) {
                List<DeviceInfo> gatewayDeviceList = deviceInfoMapper.selectAllGatewayDevicesWithConnectInfo();
                if (gatewayDeviceList != null && !gatewayDeviceList.isEmpty()) {
                    gatewayDeviceList = gatewayDeviceList.stream().filter(dev -> StringUtils.isNotEmpty(dev.getConnectCode()) && dev.getConnectCode().equals("supportMode")).collect(Collectors.toList());
                    //第一位表示拉模式 第二位表示推模式（1为支持 0为不支持)
                    gatewayDeviceList.removeIf(next -> next.getConnectValue().charAt(0) != '1');
                    Map<Integer, DeviceInfo> map = new TreeMap<>();
                    for (DeviceInfo dev : gatewayDeviceList) {
                        Page<MomgGateway2device> page = new Page<>();
                        IPage<MomgGateway2device> iPage = gateway2deviceMapper.selectChildsPage(page, dev.getId());
                        List<MomgGateway2device> infoList = iPage.getRecords();
                        if (infoList != null && !infoList.isEmpty()) {
                            map.put(infoList.size(), dev);
                        } else {
                            map.put(0, dev);
                        }
                    }
                    //map转list后取第一个
                    List<Object> mapKeyList = Arrays.asList(map.keySet().toArray());
                    if (CollUtil.isEmpty(mapKeyList)) {
                        resObj.put("errorMsg", "未找到匹配的网关，设备添加失败！");
                        resObj.put("result", false);
                        return resObj;
                    }
                    leastGatewayDevice = map.get(mapKeyList.get(0));
                    resObj.put("result", true);
                    resObj.put("gwDevice", leastGatewayDevice);
                    deviceInfo.setGatewayCode(leastGatewayDevice.getDeviceCode());
                } else {
                    resObj.put("errorMsg", "系统中未发现网关设备，设备添加失败！");
                    resObj.put("result", false);
                    return resObj;
                }
            }

            deviceInfo.setDelflag(0);
            deviceInfo.setAlarmStatus(0);
            deviceInfo.setProductId(product.getId());
            deviceInfo.setProductName(product.getDisplayName());
            deviceInfo.setHealthyDegree("健康");
            deviceInfo.setHealthyScore(100F);
            if (StringUtils.isBlank(deviceInfo.getGatewayCode())) {
                deviceInfo.setGatewayCode(deviceInfo.getDeviceCode());
            }

            //添加设备基本信息
            boolean save = deviceInfoService.save(deviceInfo);

            deviceLog(deviceInfo.getId(), "设备新增", infos);

            resObj.put("result", save);
        } catch (Exception e) {
            log.error("设备基本信息添加异常！", e);
            resObj.put("errorMsg", "设备基本信息添加异常！");
            resObj.put("result", false);
        }
        return resObj;
    }

    // 添加设备关联信息
    public void insertDevData(DeviceInfo deviceInfo, Product product, List<String> transferTypeList, List<MomgProductTransferJob> productTransferJobList, DeviceInfo leastGatewayDevice, List<DeviceConnectInfo> templateList, TerminalDevice terminalDevice, Assets assetsInfo, JSONObject resObj) {
        /**
         * 新增网关设备时需要插入一条数据到momg_network表中
         */
        if ("1".equals(product.getProductType())) {
            String id = UUID.randomUUID().toString().replace("-", "");
            terminalDeviceMapper.insertMomgNetwork(id, deviceInfo.getDeviceCode(), "1");
        }

        /**
         * 推模式设备添加终端
         */
        if (transferTypeList.contains("0") && product.getProductType().equals("2")) {
            List<TerminalDevice> terminalDeviceList = terminalDeviceService.list(new QueryWrapper<TerminalDevice>().eq("unique_code", deviceInfo.getDeviceCode()).eq("name", deviceInfo.getName()));
            if (CollUtil.isEmpty(terminalDeviceList)) {
                terminalDevice = new TerminalDevice();
                terminalDevice.setName(deviceInfo.getName());
                terminalDevice.setUniqueCode(deviceInfo.getDeviceCode());
                terminalDevice.setTerminalType(deviceInfo.getDeviceType());
                terminalDevice.setDeptId(deviceInfo.getMomgDeptId());
                terminalDevice.setPlatformCode(platformCode);

                terminalDeviceService.save(terminalDevice);
            }
        }

        /**
         * 来自设备侧的调用，添加设备关联的资产
         */
        if (assetsInfo != null) {
            insertAssets_D(assetsInfo, deviceInfo);
        }

        /**
         * 设备资产上架下架
         */
        if (deviceInfo.getPositionInfo() != null) {
            JSONObject positionInfo = deviceInfo.getPositionInfo();
            TopoCabinet2device cabinet2device = new TopoCabinet2device();
            cabinet2device.setDeviceId(deviceInfo.getId());
            cabinet2device.setCabinetId(positionInfo.getString("cabinetId"));
            cabinet2device.setLayerPoolStr(positionInfo.getJSONArray("layerPool").toString());
            cabinet2device.setLayer(Collections.max(JSONArray.parseArray(positionInfo.getString("layerPool"), Integer.class)));
            cabinet2device.setU(positionInfo.getJSONArray("layerPool").size());
            topoCabinet2deviceMapper.insert(cabinet2device);
        }

        /**
         * 添加网关设备与子设备关系
         */
        if (resObj.get("gwDevice") != null) {
            leastGatewayDevice = (DeviceInfo) resObj.get("gwDevice");
            MomgGateway2device gateway2device = new MomgGateway2device();
            gateway2device.setGatewayId(leastGatewayDevice.getId());
            gateway2device.setDeviceId(deviceInfo.getId());
            gateway2deviceMapper.insert(gateway2device);
        }

        /**
         * 添加连接参数
         */
        boolean bool = true;
        if (CollUtil.isNotEmpty(templateList)) {
            for (DeviceConnectInfo item : templateList) {
                item.setDeviceId(deviceInfo.getId());
                //根据连接参数模板中的持久化配置，动态将对应连接参数的值存入配置的数据库表中
                dynamicPersistence(item, deviceInfo);
            }
            bool = deviceConnectInfoService.saveBatch(templateList);
        }

        /**
         * 拉模式设备添加schedule_setting
         */
        if (bool) {
            if (StringUtils.isBlank(deviceInfo.getGatewayCode())) {
                deviceInfo.setGatewayCode(deviceInfo.getDeviceCode());
            }
            insertScheduleSetting(productTransferJobList, templateList, deviceInfo, product);
        }

        /**
         * 在redis中添加设备标识与产品标识对应关系
         */
        JSONObject infoObject = new JSONObject();
        infoObject.put("productCode", product.getName());
        infoObject.put("transferProtocol", product.getProtocolList());
        redisUtil.set("rela:" + deviceInfo.getDeviceCode(), infoObject.toJSONString());

        //数据上报
        JSONObject dataObject = new JSONObject();
        dataObject.put("dataType", "device");
        JSONObject deviceObject = new JSONObject();
        deviceObject.put("operateType", "I");
        deviceObject.put("data", deviceInfo);
        dataObject.put("deviceData", deviceObject);
        JSONObject terminalObject = new JSONObject();
        terminalObject.put("operateType", "I");
        terminalObject.put("data", terminalDevice);
        dataObject.put("terminalData", terminalObject);
        dataReportUtils.pushData(dataObject.toJSONString());
    }

    // 添加设备关联的关联资产
    public void insertAssets_D(Assets assetsInfo, DeviceInfo deviceInfo) {
        List<Assets> assetsList = assetsService.list(new QueryWrapper<Assets>().eq("assets_code", assetsInfo.getAssetsUnique()).or().eq("assets_name", assetsInfo.getAssetsName()));
        if (CollUtil.isEmpty(assetsList)) {

            // 添加资产基本信息
            if (StringUtils.isNotEmpty(deviceInfo.getProductId())) {
                Product product = deviceInfoService.getProductById(deviceInfo.getProductId(), null);
                if (product != null && StringUtils.isNotEmpty(product.getAssetsCategoryId())) {
                    AssetsCategory assetsCategory = categoryService.getById(product.getAssetsCategoryId());
                    if (assetsCategory != null) {
                        assetsInfo.setAssetsCategoryId(assetsCategory.getId());
                    } else {
                        throw new RuntimeException("资产的资产类型不存在！");
                    }
                }
            }
            if (StringUtils.isNotEmpty(assetsInfo.getProducerId())) {
                CmdbSupplier supplier = cmdbSupplierService.getById(assetsInfo.getProducerId());
                if (supplier != null) {
                    assetsInfo.setProducerName(supplier.getName());
                } else {
                    throw new RuntimeException("资产的供应商不存在！");
                }
            }
            //添加资产
            assetsInfo.setDelflag(CommonConstant.DEL_FLAG_0);
            assetsInfo.setAssetsUnique(codeUtils.getCode("", "cmdb_assets", "assets_unique"));
            assetsService.save(assetsInfo);
            assetsInfo.setType(0);
            assetsLog(assetsInfo);

            // 添加资产拓展字段
            List<ExtendValue> extendList = assetsInfo.getExtendValue();
            if (CollUtil.isNotEmpty(extendList)) {
                for (ExtendValue extendValue1 : extendList) {
                    extendValue1.setAssetsId(assetsInfo.getId());
                    extendValue1.setDelflag(0);
                    extendValue1.setFieldId(extendValue1.getId());
                    extendValue1.setId(null);
                    if (extendValue1.getValue() != null) {
                        ExtendField extendField = iExtendFieldService.getById(extendValue1.getFieldId());
                        if (extendField != null) {
                            // 校验当前附加字段的值唯一性
                            if (extendField.getIsUnique() == 1) {
                                List<ExtendValue> extendValueList = extendValueMapper.findByFieldIdAndValue(extendField.getId(), extendValue1.getValue());
                                if (CollUtil.isNotEmpty(extendValueList)) {
                                    throw new RuntimeException("资产扩展字段唯一性校验失败！");
                                }
                            }
                        }
                    }
                }
                iExtendValueService.saveBatch(extendList);
            }

            // 添加设备资产关系
            if (StringUtils.isNotEmpty(deviceInfo.getId())) {
                MomgDevice2assets device2assets = new MomgDevice2assets();
                device2assets.setDeviceId(deviceInfo.getId());
                device2assets.setAssetsId(assetsInfo.getId());
                device2assetsMapper.insert(device2assets);
            }
        } else {
            throw new RuntimeException("关联的资产名称或编号已存在！");
        }
    }

    // 添加资产
    public void insertAssets_A(Assets assetsInfo, String operate) {
        List<Assets> assetsList = assetsService.list(new QueryWrapper<Assets>().eq("assets_code", assetsInfo.getAssetsUnique()).or().eq("assets_name", assetsInfo.getAssetsName()));
        if (CollUtil.isEmpty(assetsList)) {
            // 添加资产基本信息
            if (StringUtils.isNotEmpty(assetsInfo.getAssetsCategoryId())) {
                AssetsCategory assetsCategory = categoryService.getById(assetsInfo.getAssetsCategoryId());
                if (assetsCategory != null) {
                    assetsInfo.setAssetsCategoryId(assetsCategory.getId());
                } else {
                    throw new RuntimeException("关联的资产的资产类型不存在！");
                }
            }

            if (StringUtils.isNotEmpty(assetsInfo.getProducerId())) {
                CmdbSupplier supplier = cmdbSupplierService.getById(assetsInfo.getProducerId());
                if (supplier != null) {
                    assetsInfo.setProducerName(supplier.getName());
                } else {
                    throw new RuntimeException("关联的资产的供应商不存在！");
                }
            }
            assetsInfo.setDelflag(CommonConstant.DEL_FLAG_0);
            assetsInfo.setAssetsUnique(codeUtils.getCode("", "cmdb_assets", "assets_unique"));
            assetsService.save(assetsInfo);
            assetsInfo.setType(0);
            assetsLog(assetsInfo);

            // 添加资产拓展字段
            List<ExtendValue> extendList = assetsInfo.getExtendValue();
            if (CollUtil.isNotEmpty(extendList)) {
                for (ExtendValue extendValue1 : extendList) {
                    extendValue1.setAssetsId(assetsInfo.getId());
                    extendValue1.setDelflag(0);
                    extendValue1.setFieldId(extendValue1.getId());
                    extendValue1.setId(null);
                    if (extendValue1.getValue() != null) {
                        ExtendField extendField = iExtendFieldService.getById(extendValue1.getFieldId());
                        if (extendField != null) {
                            // 校验当前附加字段的值唯一性
                            if (extendField.getIsUnique() == 1) {
                                List<ExtendValue> extendValueList = extendValueMapper.findByFieldIdAndValue(extendField.getId(), extendValue1.getValue());
                                if (CollUtil.isNotEmpty(extendValueList)) {
                                    throw new RuntimeException("关联的资产的扩展字段唯一性校验失败！");
                                }
                            }
                        }
                    }
                }
                iExtendValueService.saveBatch(extendList);
            }
        } else {
            throw new RuntimeException("关联的资产名称或编号已存在！");
        }

    }

    // 拉模式设备添加scheduleSetting信息
    public void insertScheduleSetting(List<MomgProductTransferJob> productTransferJobList, List<DeviceConnectInfo> templateList, DeviceInfo deviceInfo, Product product) {
        List<ScheduleSetting> scheduleSettingList = new ArrayList<>();
        Map<String, MomgProductTransferJob> productTransferJobMap = productTransferJobList.stream().collect(Collectors.toMap(MomgProductTransferJob::getTransferProtocol, productTransferJob -> productTransferJob));
        if (CollUtil.isNotEmpty(templateList)) {
            Map<String, List<DeviceConnectInfo>> connParamMap = templateList.stream().collect(Collectors.groupingBy(DeviceConnectInfo::getTransferProtocol));
            for (Map.Entry<String, MomgProductTransferJob> entry1 : productTransferJobMap.entrySet()) {
                if (entry1.getValue().getCollectType().equals("1")) {
                    ScheduleSetting scheduleSetting = buildScheduleSetting(connParamMap, entry1, deviceInfo, product);
                    if (scheduleSetting != null) {
                        scheduleSettingList.add(scheduleSetting);
                    }
                }
            }
        }

        if (CollUtil.isNotEmpty(scheduleSettingList)) {
            boolean b = scheduleSettingService.saveBatch(scheduleSettingList);
            if (b) {
                //schedule_setting新增成功，发布连接参数新增事件
                ArrayList<String> keyList = new ArrayList<>();
                keyList.add(deviceInfo.getDeviceCode());
                publishConnectSaveEvent(keyList);
            } else {
                throw new RuntimeException("scheduleSetting 添加失败！");
            }
        }
    }

    // 根据连接参数模板中的持久化配置，动态将对应连接参数的值存入配置的数据库表中
    public void dynamicPersistence(DeviceConnectInfo item, DeviceInfo deviceInfo) {
        if (StringUtils.isNotEmpty(item.getPersistentconf())) {
            List<String> configList = Arrays.asList(item.getPersistentconf().trim().split(";"));
            configList.forEach(config -> {
                String[] split = config.split(",");
                deviceInfoMapper.dynamicSqlExec(split[0], split[1], item.getConnectValue(), split[2], deviceInfo.getDeviceCode());
                System.out.println("update " + split[0] + " set " + split[1] + " = " + item.getConnectValue() + " where " + split[2] + " = " + deviceInfo.getDeviceCode() + ";");
            });
        }
    }

    // 发布连接参数新增事件
    public void publishConnectSaveEvent(List<String> keyList) {
        JSONObject data = new JSONObject();
        data.put("deviceKeys", keyList);
        redisMq.publish(Streams.DEVICE_MONITORING_START, data);
    }


    // *-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-


    @Transactional
    public Result<?> editDeviceInfo(JSONObject infos) {

        DeviceInfo deviceInfo = null;
        Assets assetsInfo = null;
        List<DeviceConnectInfo> templateList = null;
        JSONObject devObj = infos.getJSONObject("deviceInfo");
        if (devObj != null && !devObj.isEmpty()) {
            deviceInfo = JSON.parseObject(devObj.toJSONString(), DeviceInfo.class);
        }
        JSONObject assetsObj = infos.getJSONObject("assetsInfo");
        if (assetsObj != null && !assetsObj.isEmpty()) {
            assetsInfo = JSON.parseObject(assetsObj.toJSONString(), Assets.class);
        }
        JSONArray templateArray = infos.getJSONArray("templateList");
        if (templateArray != null && !templateArray.isEmpty()) {
            templateList = JSONObject.parseArray(templateArray.toJSONString(), DeviceConnectInfo.class);
        }

        if (deviceInfo != null && StringUtils.isNotEmpty(deviceInfo.getDeviceCode()) && StringUtils.isNotEmpty(deviceInfo.getName()) && StringUtils.isNotEmpty(deviceInfo.getProductId())) {
            if (deviceInfo.getDeviceCode().contains("_")) {
                return Result.error("设备唯一标识中不得包含“_”符号！");
            }
            List<DeviceInfo> allByNameAndCode = deviceInfoMapper.selectList(new QueryWrapper<DeviceInfo>().eq("name", deviceInfo.getName()).ne("id", deviceInfo.getId()));
            if (CollUtil.isNotEmpty(allByNameAndCode)) {
                return Result.error("设备名称已存在！");
            }

            List<MomgProductTransferJob> productTransferJobList = new ArrayList<>();
            Product product = deviceInfoService.getProductById(deviceInfo.getProductId(), null);
            List<String> transferTypeList = null;
            if (product != null) {
                productTransferJobList = productTransferJobMapper.selectAllInfo(product.getId());
                transferTypeList = productTransferJobList.stream().map(MomgProductTransferJob::getCollectType).collect(Collectors.toList());
            } else {
                return Result.error("设备的产品不存在！");
            }

            /**
             * 更新设备基本信息
             */
            JSONObject resObj = editDevInfo(infos, transferTypeList, product, deviceInfo, "");
            if (resObj.getBoolean("result")) {
                try {
                    editDevData(deviceInfo, product, transferTypeList, productTransferJobList, templateList, assetsInfo, "fromDevice");
                } catch (Exception e) {
                    log.error("编辑设备关联数据时出现异常！", e);
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return Result.error("编辑设备关联数据时出现异常！" + e);
                }
            } else {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return Result.error(resObj.getString("errorMsg"));
            }
        } else {
            return Result.error("设备基本信息不存在！");
        }

        return Result.OK("设备及其关联数据编辑成功！");
    }

    @Transactional
    public Result<?> editAssetsInfo(JSONObject infos) {

        DeviceInfo deviceInfo = null;
        Assets assetsInfo = null;
        List<DeviceConnectInfo> templateList = null;
        JSONObject devObj = infos.getJSONObject("deviceInfo");
        if (devObj != null && !devObj.isEmpty()) {
            deviceInfo = JSON.parseObject(devObj.toJSONString(), DeviceInfo.class);
        }
        JSONObject assetsObj = infos.getJSONObject("assetsInfo");
        if (assetsObj != null && !assetsObj.isEmpty()) {
            assetsInfo = JSON.parseObject(assetsObj.toJSONString(), Assets.class);
        }
        JSONArray templateArray = infos.getJSONArray("templateList");
        if (templateArray != null && !templateArray.isEmpty()) {
            templateList = JSONObject.parseArray(templateArray.toJSONString(), DeviceConnectInfo.class);
        }

        if (assetsInfo != null) {
            try {
                syncAssets_A(assetsInfo, "");
            } catch (Exception e) {
                log.error("编辑资产时出现异常！", e);
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return Result.error("编辑资产时出现异常！");
            }

            if (deviceInfo != null) {
                if (deviceInfo.getDeviceCode().contains("_")) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return Result.error("设备唯一标识中不得包含“_”符号！");
                }
                DeviceInfo allByName = deviceInfoMapper.selectOne(new QueryWrapper<DeviceInfo>().eq("name", deviceInfo.getName()).or().eq("device_code", deviceInfo.getDeviceCode()));
                if (allByName != null && !allByName.getId().equals(deviceInfo.getId())) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return Result.error("设备名称或唯一标识已存在！");
                }

                // 预置设备相关数据
                DeviceInfo leastGatewayDevice = new DeviceInfo();
                TerminalDevice terminalDevice = null;
                List<MomgProductTransferJob> productTransferJobList = null;
                List<String> transferTypeList = null;
                Product product = deviceInfoService.getProductById(deviceInfo.getProductId(), null);
                if (product != null) {
                    productTransferJobList = productTransferJobMapper.selectAllInfo(product.getId());
                    transferTypeList = productTransferJobList.stream().map(MomgProductTransferJob::getCollectType).collect(Collectors.toList());
                } else {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return Result.error("添加设备的产品不存在！");
                }

                JSONObject resObj = new JSONObject();
                try {
                    // 编辑关联的设备
                    if (StringUtils.isNotEmpty(deviceInfo.getId())) {
                        resObj = editDevInfo(infos, transferTypeList, product, deviceInfo, "");
                        if (resObj.getBoolean("result")) {
                            try {
                                editDevData(deviceInfo, product, transferTypeList, productTransferJobList, templateList, assetsInfo, "fromAssets");
                            } catch (Exception e) {
                                log.error("编辑资产同时添加关关联设备的相关数据时出现异常！", e);
                                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                                return Result.error("编辑资产同时添加关联设备的相关数据时出现异常！");
                            }
                        } else {
                            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                            return Result.error(resObj.getString("errorMsg"));
                        }
                    } else {
                        // 新增关联的设备
                        try {
                            resObj = insertDevInfo(infos, deviceInfo, product, transferTypeList, leastGatewayDevice, "");
                        } catch (Exception e) {
                            log.error("编辑资产同时添加关联的设备时出现异常！", e);
                            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                            return Result.error("编辑资产同时添加关联的设备时出现异常！");
                        }
                        if (resObj != null && resObj.getBoolean("result")) {
                            try {
                                insertDevData(deviceInfo, product, transferTypeList, productTransferJobList, leastGatewayDevice, templateList, terminalDevice, null, resObj);
                            } catch (Exception e) {
                                log.error("编辑资产同时添加关联设备的相关数据时出现异常！", e);
                                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                                return Result.error("编辑资产同时添加关联设备的相关数据时出现异常！");
                            }

                            // 添加设备资产关系
                            MomgDevice2assets device2assets = new MomgDevice2assets();
                            device2assets.setDeviceId(deviceInfo.getId());
                            device2assets.setAssetsId(assetsInfo.getId());
                            device2assetsMapper.insert(device2assets);
                        } else {
                            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                            return Result.error(resObj.getString("errorMsg"));
                        }
                    }
                } catch (Exception e) {
                    log.error("编辑资产同步关联的设备时出现异常！", e);
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return Result.error("编辑资产同步关联的设备时出现异常！");
                }

            } else {
                // 删除关联的设备
                try {
                    MomgDevice2assets device2assets = device2assetsMapper.selectOne(new QueryWrapper<MomgDevice2assets>().eq("assets_id", assetsInfo.getId()));
                    if (device2assets != null) {
                        DeviceInfo dev = deviceInfoService.getById(device2assets.getDeviceId());
                        if (dev != null) {
                            deviceInfoService.removeById(device2assets.getDeviceId());
                            redisUtil.del("rela:" + dev.getDeviceCode());
                            terminalDeviceService.remove(new QueryWrapper<TerminalDevice>().eq("unique_code", dev.getDeviceCode()));
                            deviceConnectInfoService.remove(new QueryWrapper<DeviceConnectInfo>().eq("device_id", device2assets.getDeviceId()));
                            topoCabinet2deviceMapper.delete(new QueryWrapper<TopoCabinet2device>().eq("device_id", device2assets.getDeviceId()));
                            gateway2deviceMapper.delete(new QueryWrapper<MomgGateway2device>().eq("device_id", device2assets.getDeviceId()));
                        }
                        device2assetsMapper.delete(new QueryWrapper<MomgDevice2assets>().eq("device_id", dev.getId()));
                    }
                } catch (Exception e) {
                    log.error("编辑资产同步清除历史关联的设备时出现异常！", e);
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return Result.error("编辑资产同步清除历史关联的设备时出现异常！");
                }
            }
        } else {
            return Result.error("资产信息不存在！");
        }

        return Result.OK("资产及其关联数据编辑成功！");
    }

    @Transactional
    public Result<?> editAssetsAct(JSONObject infos) {
        Assets assetsInfo = null;
        JSONObject assetsObj = infos.getJSONObject("assetsInfo");
        if (assetsObj != null && !assetsObj.isEmpty()) {
            assetsInfo = JSON.parseObject(assetsObj.toJSONString(), Assets.class);
        }
        if (assetsInfo != null) {
            try {
                List<AssetsAct> duplicateAssetsList = assetsActMapper.selectAssetsUnion(assetsInfo.getAssetsName(), assetsInfo.getAssetsCode(), assetsInfo.getAssetsId());
                if (CollUtil.isNotEmpty(duplicateAssetsList)) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return Result.error("资产_流程临时数据的名称或标识已存在！");
                }
                if (StringUtils.isNotEmpty(infos.getString("deviceInfo"))) {
                    JSONObject devObj = infos.getJSONObject("deviceInfo");
                    DeviceInfo deviceInfo = JSON.parseObject(devObj.toJSONString(), DeviceInfo.class);
                    if (deviceInfo.getDeviceCode().contains("_")) {
                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                        return Result.error("设备唯一标识中不得包含“_”符号！");
                    }
                    List<DeviceInfo> duplicateDevList = assetsActMapper.selectDevUnion(deviceInfo.getName(), deviceInfo.getDeviceCode(), deviceInfo.getId());
                    if (CollUtil.isNotEmpty(duplicateDevList)) {
                        return Result.error("设备名称或唯一标识已存在！");
                    }
                    Product product = deviceInfoService.getProductById(deviceInfo.getProductId(), null);
                    if (product == null) {
                        return Result.error("设备的产品不存在！");
                    }
                }
                if (StringUtils.isNotEmpty(assetsInfo.getAssetsCategoryId())) {
                    AssetsCategory assetsCategory = categoryService.getById(assetsInfo.getAssetsCategoryId());
                    if (assetsCategory != null) {
                        assetsInfo.setAssetsCategoryId(assetsCategory.getId());
                    } else {
                        throw new RuntimeException("关联的资产的资产类型不存在！");
                    }
                }
                if (StringUtils.isNotEmpty(assetsInfo.getProducerId())) {
                    CmdbSupplier supplier = cmdbSupplierService.getById(assetsInfo.getProducerId());
                    if (supplier != null) {
                        assetsInfo.setProducerName(supplier.getName());
                    } else {
                        throw new RuntimeException("关联的资产的供应商不存在！");
                    }
                }
                assetsInfo.setAssetsId(assetsInfo.getId());
                assetsInfo.setId(null);
                AssetsAct assetsAct = CustomConvertUtil.ConvertClass(assetsInfo, AssetsAct.class);
                assetsAct.setOperateType("2");
                assetsAct.setDelflag(2);
                assetsAct.setAssociateData(infos.toJSONString());
                assetsActService.save(assetsAct);
            } catch (Exception e) {
                log.error("编辑资产_流程临时数据时出现异常！", e);
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return Result.error("编辑资产_流程临时数据时出现异常！");
            }
        } else {
            return Result.error("资产信息不存在！");
        }

        return Result.OK("资产及其关联数据编辑成功！");
    }


    // 编辑设备基本信息
    public JSONObject editDevInfo(JSONObject infos, List<String> transferTypeList, Product product, DeviceInfo deviceInfo, String operate) {
        JSONObject resObj = new JSONObject();
        try {
            //绑定平台标识
            deviceInfo.setPlatformCode(platformCode);
            deviceInfo.setProductName(product.getDisplayName());
            if (!transferTypeList.contains("0") || product.getProductType().equals("1")) {
                deviceInfo.setDeviceType(1234567890);
            } else {
                //推模式(同步添加终端)
                int type = 6;
                switch (product.getName()) {
                    case "ServerPush":
                        //displayName = "服务器";
                        type = 0;
                        break;
                    case "DeskTopPush":
                        //displayName = "桌面机";
                        type = 6;
                        break;
                    case "NoteBookPush":
                        //displayName = "笔记本";
                        type = 8;
                        break;
                }
                deviceInfo.setDeviceType(type);
            }

            boolean b = deviceInfoService.updateById(deviceInfo);

            deviceLog(deviceInfo.getId(), "属性修改", infos);

            resObj.put("result", b);
        } catch (Exception e) {
            log.error("设备基本信息编辑异常！", e);
            resObj.put("result", false);
            resObj.put("errorMsg", "设备基本信息编辑异常!");
        }
        return resObj;
    }

    // 编辑设备关联数据
    public void editDevData(DeviceInfo deviceInfo, Product product, List<String> transferTypeList, List<MomgProductTransferJob> productTransferJobList, List<DeviceConnectInfo> templateList, Assets assetsInfo, String operateType) {
        /**
         * 同步关联的终端
         */
        JSONObject terminalObject = syncTerminal(transferTypeList, product, deviceInfo);

        /**
         * 设备侧调用，更新设备关联的资产
         */
        if (operateType.equals("fromDevice")) {
            syncAssets_D(assetsInfo, deviceInfo);
        }

        /**
         * 设备资产上架下架
         */
        syncDevPosition(deviceInfo);

        if (CollUtil.isNotEmpty(templateList)) {
            /**
             * 更新连接参数表
             */
            editConnectInfo(deviceInfo, templateList);

            /**
             * 更新设备schedule_setting
             */
            if (StringUtils.isBlank(deviceInfo.getGatewayCode())) {
                deviceInfo.setGatewayCode(deviceInfo.getDeviceCode());
            }
            editScheduleSetting(productTransferJobList, templateList, deviceInfo, product);
        }

        /**
         * 修改redis中的设备标识与产品标识对应关系
         */
        JSONObject infoObject = new JSONObject();
        infoObject.put("productCode", product.getName());
        infoObject.put("transferProtocol", product.getProtocolList());
        redisUtil.set("rela:" + deviceInfo.getDeviceCode(), infoObject.toJSONString());

        //数据上报
        JSONObject dataObject = new JSONObject();
        dataObject.put("dataType", "device");
        JSONObject deviceObject = new JSONObject();
        deviceObject.put("operateType", "U");
        deviceObject.put("data", deviceInfo);
        dataObject.put("deviceData", deviceObject);
        dataObject.put("terminalData", terminalObject);
        dataReportUtils.pushData(dataObject.toJSONString());
    }

    // 同步关联的终端
    public JSONObject syncTerminal(List<String> transferTypeList, Product product, DeviceInfo deviceInfo) {
        //查询到关联的终端
        TerminalDevice terminalDevice = terminalDeviceMapper.selectOne(new QueryWrapper<TerminalDevice>().eq("unique_code", deviceInfo.getDeviceCode()));
        //判断需要修改的终端名称是否已存在
        if (terminalDevice != null) {
            List<TerminalDevice> allByName = terminalDeviceMapper.selectList(new QueryWrapper<TerminalDevice>().eq("name", deviceInfo.getName()).ne("id", terminalDevice.getId()));
            if (CollUtil.isNotEmpty(allByName)) {
                throw new RuntimeException("关联的终端名称已存在！");
            }
        } else {
            List<TerminalDevice> allByName = terminalDeviceMapper.selectList(new QueryWrapper<TerminalDevice>().eq("name", deviceInfo.getName()));
            if (CollUtil.isNotEmpty(allByName)) {
                throw new RuntimeException("关联的终端名称已存在！");
            }
        }

        String operateType = "U";
        //当前为拉模式
        if (!transferTypeList.contains("0") || product.getProductType().equals("1")) {
            if (terminalDevice != null) {
                operateType = "D";
                terminalDeviceMapper.deleteById(terminalDevice.getId());
            }
        } else {
            //当前为推模式
            if (terminalDevice != null) {
                operateType = "U";
                /**
                 * 更新关联的终端
                 */
                terminalDevice.setName(deviceInfo.getName());
                terminalDevice.setTerminalType(deviceInfo.getDeviceType());
                terminalDevice.setDeptId(deviceInfo.getMomgDeptId());
                terminalDevice.setPlatformCode(platformCode);

                terminalDeviceMapper.updateById(terminalDevice);
            } else {
                operateType = "I";
                /**
                 * 添加关联的终端
                 */
                terminalDevice = new TerminalDevice();
                terminalDevice.setName(deviceInfo.getName());
                terminalDevice.setUniqueCode(deviceInfo.getDeviceCode());
                terminalDevice.setTerminalType(deviceInfo.getDeviceType());
                terminalDevice.setDeptId(deviceInfo.getMomgDeptId());
                terminalDevice.setPlatformCode(platformCode);

                terminalDeviceMapper.insert(terminalDevice);
            }
        }

        JSONObject terminalObject = new JSONObject();
        terminalObject.put("operateType", operateType);
        terminalObject.put("data", terminalDevice);
        return terminalObject;
    }

    // 同步设备位置信息
    public void syncDevPosition(DeviceInfo deviceInfo) {
        TopoCabinet2device cabinet2device = topoCabinet2deviceMapper.selectOne(new QueryWrapper<TopoCabinet2device>().eq("device_id", deviceInfo.getId()));
        if (deviceInfo.getPositionInfo() != null) {
            try {
                JSONObject positionInfo = deviceInfo.getPositionInfo();
                List<String> currentList = JSONArray.parseArray(positionInfo.getString("layerPool"), String.class);
                List<String> freeUList = topoCabinetService.getfreeUbyCabinet(positionInfo.getString("cabinetId"), deviceInfo.getId());
                List<String> diffFromFreeList = currentList.stream().filter(e1 -> freeUList.stream().noneMatch(e2 -> StringUtils.equals(e1, e2))).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(diffFromFreeList)) {
                    throw new RuntimeException("当前设备占用的U中存在已占用的U！");
                }
                if (cabinet2device != null) {
                    cabinet2device.setDeviceId(deviceInfo.getId());
                    cabinet2device.setCabinetId(positionInfo.getString("cabinetId"));
                    cabinet2device.setLayerPoolStr(positionInfo.getJSONArray("layerPool").toString());
                    cabinet2device.setLayer(Collections.max(JSONArray.parseArray(positionInfo.getString("layerPool"), Integer.class)));
                    cabinet2device.setU(positionInfo.getJSONArray("layerPool").size());
                    topoCabinet2deviceMapper.updateById(cabinet2device);
                } else {
                    cabinet2device = new TopoCabinet2device();
                    cabinet2device.setDeviceId(deviceInfo.getId());
                    cabinet2device.setCabinetId(positionInfo.getString("cabinetId"));
                    cabinet2device.setLayerPoolStr(positionInfo.getJSONArray("layerPool").toString());
                    cabinet2device.setLayer(Collections.max(JSONArray.parseArray(positionInfo.getString("layerPool"), Integer.class)));
                    cabinet2device.setU(positionInfo.getJSONArray("layerPool").size());
                    topoCabinet2deviceMapper.insert(cabinet2device);
                }
            } catch (Exception e) {
                log.error("添加资产同时维护位置信息时出现异常！", e);
                throw new RuntimeException("添加资产同时维护位置信息时出现异常！");
            }
        } else {
            if (cabinet2device != null) {
                topoCabinet2deviceMapper.deleteById(cabinet2device.getId());
            }
        }
    }


    // 编辑设备关联的资产
    public void syncAssets_D(Assets assetsInfo, DeviceInfo deviceInfo) {
        if (assetsInfo != null) {
            List<Assets> assetsList = assetsService.list(new QueryWrapper<Assets>().eq("assets_code", assetsInfo.getAssetsUnique()).or().eq("assets_name", assetsInfo.getAssetsName()));
            // 编辑关联的资产
            if (StringUtils.isNotEmpty(assetsInfo.getId())) {
                assetsList = assetsList.stream().filter(i -> !StringUtils.equals(i.getId(), assetsInfo.getId())).collect(Collectors.toList());
                if (CollUtil.isEmpty(assetsList)) {
                    // 编辑资产基本信息
                    if (StringUtils.isNotEmpty(assetsInfo.getAssetsCategoryId())) {
                        AssetsCategory assetsCategory = categoryService.getById(assetsInfo.getAssetsCategoryId());
                        if (assetsCategory != null) {
                            assetsInfo.setAssetsCategoryId(assetsCategory.getId());
                        } else {
                            throw new RuntimeException("关联的资产的资产类型不存在！");
                        }
                    }
                    if (StringUtils.isNotEmpty(assetsInfo.getProducerId())) {
                        CmdbSupplier supplier = cmdbSupplierService.getById(assetsInfo.getProducerId());
                        if (supplier != null) {
                            assetsInfo.setProducerName(supplier.getName());
                        } else {
                            throw new RuntimeException("关联的资产的供应商不存在！");
                        }
                    }
                    assetsService.updateById(assetsInfo);
                    if (assetsInfo.getStorageTime() == null) {
                        assetsMapper.updateEntity("storage_time", assetsInfo.getId());
                    }
                    assetsInfo.setType(1);
                    assetsLog(assetsInfo);

                    // 覆盖资产拓展
                    extendValueMapper.delByAssId(assetsInfo.getId());
                    List<ExtendValue> extendList = assetsInfo.getExtendValue();
                    if (CollUtil.isNotEmpty(extendList)) {
                        for (ExtendValue extendValue1 : extendList) {
                            extendValue1.setAssetsId(assetsInfo.getId());
                            extendValue1.setDelflag(0);
                            extendValue1.setFieldId(extendValue1.getId());
                            extendValue1.setId(null);
                            if (extendValue1.getValue() != null) {
                                ExtendField extendField = iExtendFieldService.getById(extendValue1.getFieldId());
                                if (extendField != null) {
                                    if (extendField.getIsUnique() == 1) {
                                        List<ExtendValue> extendValueList = extendValueMapper.findByFieldIdAndValue(extendField.getId(), extendValue1.getValue());
                                        List<ExtendValue> extendValueList1 = extendValueMapper.findByFieldIdAndAssetIdAndValue(extendField.getId(), assetsInfo.getId(), extendValue1.getValue());
                                        if (extendValueList.size() > 1) {
                                            // 存量数据中有重复
                                            throw new RuntimeException("关联的资产的扩展字段唯一性校验失败！");
                                        } else if (extendValueList.size() == 1 && extendValueList1.size() == 1) {
                                            if (!extendValueList.get(0).getId().equals(extendValueList1.get(0).getId())) {
                                                // 编辑后的数据与存量数据重复
                                                throw new RuntimeException("关联的资产的扩展字段唯一性校验失败！");
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        iExtendValueService.saveBatch(extendList);
                    }
                } else {
                    throw new RuntimeException("关联的资产名称或编号已存在！");
                }
            } else { // 新增关联的资产
                if (CollUtil.isEmpty(assetsList)) {
                    insertAssets_D(assetsInfo, deviceInfo);
                } else {
                    throw new RuntimeException("关联的资产名称或编号已存在！");
                }
            }
        } else {
            // 清除关联的资产
            MomgDevice2assets device2assets = device2assetsMapper.selectOne(new QueryWrapper<MomgDevice2assets>().eq("device_id", deviceInfo.getId()));
            if (device2assets != null) {
                extendValueMapper.delByAssId(device2assets.getAssetsId());
                List<CmdbAssetsRelation> cmdbAssetsRelationList = cmdbAssetsRelationMapper.selectList(new QueryWrapper<CmdbAssetsRelation>().eq("assets_id_one", device2assets.getAssetsId()).or().eq("assets_id_two", device2assets.getAssetsId()));
                if (CollUtil.isNotEmpty(cmdbAssetsRelationList)) {
                    cmdbAssetsRelationMapper.deleteBatchIds(cmdbAssetsRelationList.stream().map(CmdbAssetsRelation::getId).collect(Collectors.toList()));
                }
                device2assetsMapper.delete(new QueryWrapper<MomgDevice2assets>().eq("device_id", deviceInfo.getId()));
                assetsService.removeById(device2assets.getAssetsId());
            }
        }
    }

    // 编辑资产
    public void syncAssets_A(Assets assetsInfo, String operate) {
        List<Assets> assetsList = assetsService.list(new QueryWrapper<Assets>().eq("assets_code", assetsInfo.getAssetsUnique()).or().eq("assets_name", assetsInfo.getAssetsName()));
        assetsList = assetsList.stream().filter(a -> !StringUtils.equals(a.getId(), assetsInfo.getId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(assetsList)) {
            Assets assets1 = assetsService.getById(assetsInfo.getId());
            if (assets1.getStartQualityTime() != assetsInfo.getStartQualityTime()) {
                LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
                AssetsLog assetsLog = new AssetsLog();
                assetsLog.setUserId(loginUser.getUsername());
                assetsLog.setAssetsConfig("2");
                assetsLog.setCreateBy(assetsInfo.getUpdateBy());
                assetsLog.setContent("编辑资产保修信息");
                iAssetsLogService.save(assetsLog);
            }
            // 编辑资产基本信息
            if (StringUtils.isNotEmpty(assetsInfo.getAssetsCategoryId())) {
                AssetsCategory assetsCategory = categoryService.getById(assetsInfo.getAssetsCategoryId());
                if (assetsCategory != null) {
                    assetsInfo.setAssetsCategoryId(assetsCategory.getId());
                } else {
                    throw new RuntimeException("关联的资产的资产类型不存在！");
                }
            }
            if (StringUtils.isNotEmpty(assetsInfo.getProducerId())) {
                CmdbSupplier supplier = cmdbSupplierService.getById(assetsInfo.getProducerId());
                if (supplier != null) {
                    assetsInfo.setProducerName(supplier.getName());
                } else {
                    throw new RuntimeException("关联的资产的供应商不存在！");
                }
            }
            assetsService.updateById(assetsInfo);
            if (assetsInfo.getStorageTime() == null) {
                assetsMapper.updateEntity("storage_time", assetsInfo.getId());
            }
            assetsInfo.setType(1);
            assetsLog(assetsInfo);

            // 覆盖资产拓展
            extendValueMapper.delByAssId(assetsInfo.getId());
            List<ExtendValue> extendList = assetsInfo.getExtendValue();
            if (CollUtil.isNotEmpty(extendList)) {
                for (ExtendValue extendValue1 : extendList) {
                    extendValue1.setAssetsId(assetsInfo.getId());
                    extendValue1.setDelflag(0);
                    extendValue1.setFieldId(extendValue1.getId());
                    extendValue1.setId(null);
                    if (extendValue1.getValue() != null) {
                        ExtendField extendField = iExtendFieldService.getById(extendValue1.getFieldId());
                        if (extendField != null) {
                            if (extendField.getIsUnique() == 1) {
                                List<ExtendValue> extendValueList = extendValueMapper.findByFieldIdAndValue(extendField.getId(), extendValue1.getValue());
                                List<ExtendValue> extendValueList1 = extendValueMapper.findByFieldIdAndAssetIdAndValue(extendField.getId(), assetsInfo.getId(), extendValue1.getValue());
                                if (extendValueList.size() > 1) {
                                    // 存量数据中有重复
                                    throw new RuntimeException("关联的资产的扩展字段唯一性校验失败！");
                                } else if (extendValueList.size() == 1 && extendValueList1.size() == 1) {
                                    if (!extendValueList.get(0).getId().equals(extendValueList1.get(0).getId())) {
                                        // 编辑后的数据与存量数据重复
                                        throw new RuntimeException("关联的资产的扩展字段唯一性校验失败！");
                                    }
                                }
                            }
                        }
                    }
                }
                iExtendValueService.saveBatch(extendList);
            }
        } else {
            throw new RuntimeException("资产名称或编号已存在！");
        }

    }

    // 编辑设备连接参数
    public void editConnectInfo(DeviceInfo deviceInfo, List<DeviceConnectInfo> templateList) {

        /*//清除数据库中原始的连接参数
        deviceConnectInfoService.remove(new QueryWrapper<DeviceConnectInfo>().eq("device_id", deviceInfo.getId()));
        //保存新的设备连接参数
        templateList = templateList.stream().map(c -> c.setDeviceId(deviceInfo.getId())).collect(Collectors.toList());
        deviceConnectInfoService.saveBatch(templateList);*/

        //获取数据库中原始的连接参数
        List<DeviceConnectInfo> oldInfos = deviceConnectInfoService.selectByDevId(deviceInfo.getId());
        Map<String, DeviceConnectInfo> oldMap = oldInfos.stream().collect(Collectors.toMap(DeviceConnectInfo::getTemplateId, DeviceConnectInfo -> DeviceConnectInfo));

        //获取新的设备连接参数
        Map<String, DeviceConnectInfo> newMap = templateList.stream().collect(Collectors.toMap(DeviceConnectInfo::getTemplateId, DeviceConnectInfo -> DeviceConnectInfo));
        //记录原始数据和新数据中共有数据
        List<String> commonConnList = new ArrayList<>();

        //遍历新数据
        for (Map.Entry<String, DeviceConnectInfo> newEntry : newMap.entrySet()) {

            //根据连接参数模板中的持久化配置，动态将对应连接参数的值存入配置的数据库表中
            dynamicPersistence(newEntry.getValue(), deviceInfo);

            //旧数据中存在新数据
            if (oldMap.containsKey(newEntry.getKey())) {
                //遍历旧数据,获取到新旧集合中共有数据
                for (Map.Entry<String, DeviceConnectInfo> oldEntry : oldMap.entrySet()) {
                    //修改旧数据
                    if (newEntry.getKey().equals(oldEntry.getKey())) {
                        commonConnList.add(oldEntry.getValue().getId());
                        deviceConnectInfoService.updateConnects(newEntry.getKey(), newEntry.getValue().getConnectValue(), deviceInfo.getId());
                    }
                }
            } else {
                //保存旧数据中不包含的新数据
                newEntry.getValue().setDeviceId(deviceInfo.getId());
                deviceConnectInfoService.save(newEntry.getValue());
            }
        }

        //原始数据中存在且新数据中不存在的字段，直接将其从数据库中删除
        List<DeviceConnectInfo> needDelConnList = oldInfos.stream().filter(conn -> !commonConnList.contains(conn.getId())).collect(Collectors.toList());
        for (DeviceConnectInfo oldItem : needDelConnList) {
            deviceConnectInfoService.removeById(oldItem);
        }
    }

    // 编辑设备Schedule_setting
    public void editScheduleSetting(List<MomgProductTransferJob> productTransferJobList, List<DeviceConnectInfo> templateList, DeviceInfo deviceInfo, Product product) {
        ArrayList<ScheduleSetting> addScheduleSettingList = new ArrayList<>();
        List<ScheduleSetting> editScheduleSettingList = new ArrayList<>();
        List<String> oldScheduleSettingIds = new ArrayList<>();

        Map<String, MomgProductTransferJob> productTransferJobMap = productTransferJobList.stream().collect(Collectors.toMap(MomgProductTransferJob::getTransferProtocol, productTransferJob -> productTransferJob));
        Map<String, List<DeviceConnectInfo>> connParamMap = templateList.stream().collect(Collectors.groupingBy(DeviceConnectInfo::getTransferProtocol));
        List<ScheduleSetting> oldScheduleSettings = scheduleSettingService.selectByDeviceCode(deviceInfo.getDeviceCode());
        Map<String, ScheduleSetting> oldSettingMap = oldScheduleSettings.stream().collect(Collectors.toMap(ScheduleSetting::getProtocol, ScheduleSetting -> ScheduleSetting));

        // 产品不变时，根据新增或删除的产品协议和设备连接参针对做任务进行增删，以及不变协议的任务参数编辑
        // 产品变更时，根据产品协议和设备连接参数删除所有存量任务，重新组装任务
        String editOrDelete = "delete";
        if (CollUtil.isNotEmpty(oldScheduleSettings)) {
            editOrDelete = oldScheduleSettings.get(0).getProductKey().equals(product.getName()) ? "edit" : "delete";
            oldScheduleSettingIds = oldScheduleSettings.stream().map(ScheduleSetting::getId).collect(Collectors.toList());
        }

        if (editOrDelete.equals("delete")) {
            // 删除schedule_setting表中原始数据
            scheduleSettingService.deleteByDeviceKey(deviceInfo.getDeviceCode());
            // 根据产品协议和设备连接参数重新组装任务
            for (Map.Entry<String, MomgProductTransferJob> entry1 : productTransferJobMap.entrySet()) {
                if (entry1.getValue().getCollectType().equals("1")) {
                    ScheduleSetting scheduleSetting = buildScheduleSetting(connParamMap, entry1, deviceInfo, product);
                    if (scheduleSetting != null) {
                        addScheduleSettingList.add(scheduleSetting);
                    }
                }
            }
        } else {
            for (Map.Entry<String, MomgProductTransferJob> entry1 : productTransferJobMap.entrySet()) {
                if (oldSettingMap.containsKey(entry1.getKey())) {
                    // 历史任务编辑
                    ScheduleSetting scheduleSetting = oldSettingMap.get(entry1.getKey());
                    JSONObject jsonObject = new JSONObject();
                    if (connParamMap.containsKey("COMMON")) {
                        //根据公共参数组装初始连接参数
                        connParamMap.get("COMMON").forEach(item -> {
                            jsonObject.put(item.getConnectCode(), item.getConnectValue());
                        });
                    }
                    if (connParamMap.containsKey(entry1.getKey())) {
                        connParamMap.get(entry1.getKey()).forEach(item -> {
                            jsonObject.put(item.getConnectCode(), item.getConnectValue());
                        });
                    } else if (entry1.getKey().equals("SPEC")) {
                        jsonObject.put("deviceKey", deviceInfo.getDeviceCode());
                    }
                    scheduleSetting.setConnectParam(jsonObject.toJSONString());
                    scheduleSetting.setGatewayCode(deviceInfo.getGatewayCode());
                    editScheduleSettingList.add(scheduleSetting);
                    oldSettingMap.remove(entry1.getKey());
                } else {
                    // 新增协议、组装任务
                    if (entry1.getValue().getCollectType().equals("1")) {
                        ScheduleSetting scheduleSetting = buildScheduleSetting(connParamMap, entry1, deviceInfo, product);
                        if (scheduleSetting != null) {
                            addScheduleSettingList.add(scheduleSetting);
                        }
                    }
                }
            }
        }

        if (CollUtil.isNotEmpty(addScheduleSettingList)) {
            // 新增
            scheduleSettingService.saveBatch(addScheduleSettingList);
        }
        if (CollUtil.isNotEmpty(oldSettingMap)) {
            // 删除
            scheduleSettingService.removeByIds(oldSettingMap.values().stream().map(ScheduleSetting::getId).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(editScheduleSettingList)) {
            // 编辑
            scheduleSettingService.updateBatchById(editScheduleSettingList);
        }
        // 发布连接参数变更事件
        ArrayList<String> deviceKeys = new ArrayList<>();
        deviceKeys.add(deviceInfo.getDeviceCode());
        publishConnectChangeEvent(oldScheduleSettingIds, deviceKeys);

    }

    // 发布连接参数变更事件
    public void publishConnectChangeEvent(List<String> oldIds, List<String> deviceKeys) {
        JSONObject data = new JSONObject();
        data.put("oldKey", oldIds);
        data.put("newKey", deviceKeys);
        redisMq.publish(Streams.DEVICE_MONITORING_CHANGE, data);
    }

    private ScheduleSetting buildScheduleSetting(Map<String, List<DeviceConnectInfo>> connParamMap, Map.Entry<String, MomgProductTransferJob> entry1, DeviceInfo deviceInfo, Product product) {
        ScheduleSetting scheduleSetting = new ScheduleSetting();
        JSONObject jsonObject = new JSONObject();
        if (connParamMap.containsKey("COMMON")) {
            //根据公共参数组装初始连接参数
            connParamMap.get("COMMON").forEach(item -> {
                jsonObject.put(item.getConnectCode(), item.getConnectValue());
            });
        }
        if (connParamMap.containsKey(entry1.getKey())) {
            //综合产品协议和设备对应协议组装连接参数
            connParamMap.get(entry1.getKey()).forEach(item -> {
                jsonObject.put(item.getConnectCode(), item.getConnectValue());
            });
        } else if (entry1.getKey().equals("SPEC")) {
            //默认内置SPEC协议，组装连接参数
            jsonObject.put("deviceKey", deviceInfo.getDeviceCode());
        } else {
            return null;
        }
        scheduleSetting.setConnectParam(jsonObject.toJSONString());
        scheduleSetting.setJobClass(entry1.getValue().getJobValue());
        scheduleSetting.setProtocol(entry1.getValue().getTransferProtocol());
        scheduleSetting.setDeviceCode(deviceInfo.getDeviceCode());
        scheduleSetting.setProductKey(product.getName());
        scheduleSetting.setBasicRate(entry1.getValue().getRate());
        scheduleSetting.setBasicUnit(entry1.getValue().getUnit());
        if (entry1.getValue().getUnit().equals("0")) {
            // 秒
            scheduleSetting.setCron("0/" + entry1.getValue().getRate() + " * * * * ?");
            scheduleSetting.setRate(entry1.getValue().getRate());
        } else {
            // 分钟
            scheduleSetting.setCron("0 */" + entry1.getValue().getRate() + " * * * ?");
            scheduleSetting.setRate(entry1.getValue().getRate() * 60);
        }
        if (deviceInfo.getEnable() == null || deviceInfo.getEnable() == 0) {
            scheduleSetting.setStatus(0);
        }
        scheduleSetting.setGatewayCode(deviceInfo.getGatewayCode());

        return scheduleSetting;
    }

    // *-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-


    // 修改资产状态
    public Result<?> updateStatus(Map<String, String> map) {
        try {
            String updateAssetsId = map.get("updateAssetsId");
            String status_id = map.get("status_id");
            String status_name = map.get("status_name");
            Assets assets = assetsService.getById(updateAssetsId);
            assets.setStatusId(status_id);
            assets.setStatusName(status_name);
            assetsService.updateById(assets);

            // 维护资产操作历史
            assets.setType(2);
            assetsLog(assets);
            return Result.OK("修改成功！");
        } catch (Exception e) {
            return Result.error("修改失败！");
        }
    }


    // *-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-


    @Transactional
    public Result<?> interruptAssetsAct(String tempProcessInstanceId) {
        try {
            AssetsAct assetsAct = assetsActService.getOne(new QueryWrapper<AssetsAct>().eq("temp_process_instance_id", tempProcessInstanceId).eq("delflag", 0));
            if (assetsAct != null) {
                assetsAct.setOperateType("3");
                assetsAct.setDelflag(1);
                assetsActService.updateById(assetsAct);
            }
            Assets assets = new Assets();
            assets.setId(assetsAct.getAssetsId());
            return Result.OK(assets);
        } catch (Exception e) {
            log.error("资产_流程临时数据中止异常！", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return Result.error("资产_流程临时数据中止异常！");
        }

    }

    @Transactional
    public Result<?> confirmAssetsAct(String tempProcessInstanceId) {

        try {
            Assets assetsInfo = null;
            DeviceInfo deviceInfo = null;
            JSONObject infos = null;
            List<DeviceConnectInfo> templateList = null;

            List<AssetsAct> assetsActList = assetsActService.list(new QueryWrapper<AssetsAct>().eq("temp_process_instance_id", tempProcessInstanceId).eq("delflag", 0));
            if (CollUtil.isNotEmpty(assetsActList)) {
                for (AssetsAct assetsAct : assetsActList) {
                    String operate = assetsAct.getOperateType();
                    assetsInfo = CustomConvertUtil.ConvertClass(assetsAct, Assets.class);
                    assetsInfo.setId(assetsAct.getAssetsId());
                    if (StringUtils.isNotEmpty(assetsAct.getAssociateData())) {
                        infos = JSONObject.parseObject(assetsAct.getAssociateData());
                        JSONObject assetsObject = infos.getJSONObject("assetsInfo");
                        if (assetsObject != null && !assetsObject.isEmpty()) {
                            assetsInfo.setExtendValue(JSONArray.parseArray(assetsObject.getString("extendValue"), ExtendValue.class));
                        }
                        JSONObject devObj = infos.getJSONObject("deviceInfo");
                        if (devObj != null && !devObj.isEmpty()) {
                            deviceInfo = JSON.parseObject(devObj.toJSONString(), DeviceInfo.class);
                            if (devObj.containsKey("positionInfo")) {
                                deviceInfo.setPositionInfo(devObj.getJSONObject("positionInfo"));
                            }
                        }
                        JSONArray templateArray = infos.getJSONArray("templateList");
                        if (templateArray != null && !templateArray.isEmpty()) {
                            templateList = JSONObject.parseArray(templateArray.toJSONString(), DeviceConnectInfo.class);
                        }
                    }

                    switch (operate) {
                        case "0":
                            insertAssets_A(assetsInfo, "");
                            break;
                        case "1":
                            deleteBatchAssets(assetsInfo.getId());
                            break;
                        case "2":
                            syncAssets_A(assetsInfo, "");
                            break;
                    }

                    if (deviceInfo != null) {
                        if (deviceInfo.getDeviceCode().contains("_")) {
                            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                            return Result.error("设备唯一标识中不得包含“_”符号！");
                        }
                        List<DeviceInfo> duplicateDevList = assetsActMapper.selectDevUnion(deviceInfo.getName(), deviceInfo.getDeviceCode(), deviceInfo.getId());
                        if (CollUtil.isNotEmpty(duplicateDevList)) {
                            return Result.error("设备名称或唯一标识已存在！");
                        }

                        DeviceInfo leastGatewayDevice = new DeviceInfo();
                        TerminalDevice terminalDevice = null;
                        List<MomgProductTransferJob> productTransferJobList = null;
                        List<String> transferTypeList = null;
                        Product product = deviceInfoService.getProductById(deviceInfo.getProductId(), null);
                        if (product != null) {
                            productTransferJobList = productTransferJobMapper.selectAllInfo(product.getId());
                            transferTypeList = productTransferJobList.stream().map(MomgProductTransferJob::getCollectType).collect(Collectors.toList());
                        } else {
                            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                            return Result.error("设备的产品不存在！");
                        }

                        JSONObject resObj = new JSONObject();
                        if (StringUtils.isEmpty(deviceInfo.getId())) {
                            resObj = insertDevInfo(infos, deviceInfo, product, transferTypeList, leastGatewayDevice, "");
                            if (resObj != null && resObj.getBoolean("result")) {
                                insertDevData(deviceInfo, product, transferTypeList, productTransferJobList, leastGatewayDevice, templateList, terminalDevice, null, resObj);

                                MomgDevice2assets device2assets = new MomgDevice2assets();
                                device2assets.setDeviceId(deviceInfo.getId());
                                device2assets.setAssetsId(assetsInfo.getId());
                                device2assetsMapper.insert(device2assets);
                            }
                        } else {
                            resObj = editDevInfo(infos, transferTypeList, product, deviceInfo, "");
                            if (resObj != null && resObj.getBoolean("result")) {
                                editDevData(deviceInfo, product, transferTypeList, productTransferJobList, templateList, null, "fromAssets");
                            }
                        }
                    } else {
                        MomgDevice2assets device2assets = device2assetsMapper.selectOne(new QueryWrapper<MomgDevice2assets>().eq("assets_id", assetsInfo.getId()));
                        if (device2assets != null) {
                            deleteBatchDeviceInfo(device2assets.getDeviceId());
                            device2assetsMapper.deleteById(device2assets);
                        }
                    }

                    assetsAct.setDelflag(1);
                    assetsActService.updateById(assetsAct);
                }
            } else {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return Result.error("资产_流程临时数据不存在！");
            }

            return Result.OK(assetsInfo);
        } catch (Exception e) {
            log.error("资产_流程临时数据确认执行异常！", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return Result.error("资产_流程临时数据确认执行异常！");
        }

    }


    // *-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-


    @Transactional
    public Result<?> deleteBatchDeviceInfo(String ids) {

        try {
            List<String> deviceIds = Arrays.asList(ids.split(","));

            //网关子设备
            List<MomgGateway2device> children = gateway2deviceMapper.selectList(new QueryWrapper<MomgGateway2device>().in("device_id", deviceIds));
            if (CollUtil.isNotEmpty(children)) {
                for (MomgGateway2device obj : children) {
                    DeviceInfo deviceInfo = deviceInfoMapper.selectById(obj.getGatewayId());
                    if (deviceInfo != null && deviceInfo.getDelflag() == 0) {
                        return Result.error("当前选中的设备中至少有一个网关子设备与某一网关设备绑定，禁止删除！");
                    }
                }
            }

            //网关设备
            List<MomgGateway2device> gateways = gateway2deviceMapper.selectList(new QueryWrapper<MomgGateway2device>().in("gateway_id", deviceIds));
            if (gateways != null && !gateways.isEmpty()) {
                for (MomgGateway2device obj : gateways) {
                    if (StringUtils.isNotEmpty(obj.getDeviceId())) {
                        if (deviceInfoMapper.selectById(obj.getDeviceId()) != null && deviceInfoMapper.selectById(obj.getDeviceId()).getDelflag() == 0) {
                            return Result.error("当前选中的设备中至少有一个网关设备下包含有网关子设备，禁止删除！");
                        }
                    }
                }
            }

            List<DeviceInfo> deviceInfoList = deviceInfoService.listByIds(deviceIds);
            if (CollUtil.isNotEmpty(deviceInfoList)) {
                List<String> codeList = deviceInfoList.stream().map(DeviceInfo::getDeviceCode).collect(Collectors.toList());
                // 删除设备与产品关联标识
                redisUtil.del(codeList.stream().map(x -> "rela:" + x).collect(Collectors.toList()).toArray(new String[]{}));
                // 删除设备与网关关系
                gateway2deviceMapper.delete(new QueryWrapper<MomgGateway2device>().in("device_id", deviceIds));
                // 删除关联的连接参数
                deviceConnectInfoService.remove(new QueryWrapper<DeviceConnectInfo>().in("device_id", deviceIds));
                // 删除绑定的机柜
                topoCabinet2deviceMapper.delete(new QueryWrapper<TopoCabinet2device>().in("device_id", deviceIds));
                // 删除关联的终端
                terminalDeviceService.remove(new QueryWrapper<TerminalDevice>().in("unique_code", codeList));
                // 删除关联的终端的绑定关系
                sysTerminalUserMapper.delete(new QueryWrapper<SysTerminalUser>().in("unique_code", codeList));
                // 删除关联的资产
                List<MomgDevice2assets> device2assetsList = device2assetsMapper.selectList(new QueryWrapper<MomgDevice2assets>().in("device_id", deviceIds));
                if (CollUtil.isNotEmpty(device2assetsList)) {
                    List<String> assetsIdList = device2assetsList.stream().map(MomgDevice2assets::getAssetsId).collect(Collectors.toList());
                    // 删除资产拓展字段
                    extendValueMapper.delete(new QueryWrapper<ExtendValue>().in("assets_id", assetsIdList));
                    List<CmdbAssetsRelation> cmdbAssetsRelationList = cmdbAssetsRelationMapper.selectList(new QueryWrapper<CmdbAssetsRelation>().in("assets_id_one", assetsIdList).or().in("assets_id_two", assetsIdList));
                    if (CollUtil.isNotEmpty(cmdbAssetsRelationList)) {
                        // 删除资产关系
                        cmdbAssetsRelationMapper.deleteBatchIds(cmdbAssetsRelationList.stream().map(CmdbAssetsRelation::getId).collect(Collectors.toList()));
                    }
                    // 删除设备与资产关联
                    device2assetsMapper.delete(new QueryWrapper<MomgDevice2assets>().in("device_id", deviceIds));
                    // 删除资产基本信息
                    assetsService.remove(new QueryWrapper<Assets>().in("id", assetsIdList));
                }
                // 删除分组与设备绑定关系
                group2DeviceService.remove(new QueryWrapper<MomgGroup2Device>().in("device_id", deviceIds));
                // 删除设备标签关系
                utlTagResourceService.remove(new QueryWrapper<UtlTagResource>().in("resource_id", deviceIds));
                // 删除关联的scheduleSetting
                scheduleSettingService.remove(new QueryWrapper<ScheduleSetting>().in("device_code", codeList));
                // 删除设备告警信息
                alarmHistoryService.remove(new QueryWrapper<AlarmHistory>().in("device_id", deviceIds));
                // 删除Zabbix服务设备关联数据
                for (String deviceCode : codeList) {
                    String hostKey = "zabbix_" + deviceCode + "_hosts";
                    if (redisUtil.hasKey(hostKey)) {
                        String hostArrayJsonStr = redisUtil.get(hostKey).toString();
                        JSONArray hostArray = JSONArray.parseArray(hostArrayJsonStr);
                        List<String> hostIds = hostArray.stream().map(h -> ((JSONObject) h).getString("hostid")).collect(Collectors.toList());
                        hostIds.forEach(hostId -> {
                            redisUtil.del("zabbix_" + hostId + "_items");
                        });
                        redisUtil.del("zabbix_" + deviceCode + "_hosts");
                        zabbixAlarmService.remove(new QueryWrapper<MomgZabbixAlarm>().in("host_id", hostIds));
                    }
                }
                // 删除与对接系统绑定关系
                abutment2deviceService.remove(new QueryWrapper<MomgAbutment2Device>().in("device_id", deviceIds));
                // 删除设备基本信息
                deviceInfoMapper.deleteBatchIds(deviceIds);

                //发布scheduleSetting连接参数删除事件
                publishConnectDeleteEvent(codeList);

                //数据上报
                JSONObject dataObject = new JSONObject();
                dataObject.put("dataType", "device");
                JSONObject deviceObject = new JSONObject();
                deviceObject.put("operateType", "D");
                deviceObject.put("data", JSONArray.parseArray(JSONArray.toJSONString(deviceInfoList)));
                dataObject.put("deviceData", deviceObject);
                dataReportUtils.pushData(dataObject.toJSONString());

            }

            for (DeviceInfo deviceInfo : deviceInfoList) {
                MomgLogDevice momgLogDevice = new MomgLogDevice();
                momgLogDevice.setType("设备删除");
                momgLogDevice.setDeviceId(deviceInfo.getId());
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("headers", deviceInfo);
                momgLogDevice.setContent(jsonObject.toJSONString());
                momgLogDeviceService.save(momgLogDevice);
            }

            return Result.OK("设备及其关联数据删除成功!");
        } catch (Exception e) {
            log.error("设备及其关联数据删除异常！", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return Result.error("设备及其关联数据删除异常！");
        }

    }

    @Transactional
    public Result<?> deleteBatchAssets(String ids) {

        try {
            List<String> assetsIds = Arrays.asList(ids.split(","));

            if (CollUtil.isNotEmpty(assetsIds)) {
                extendValueMapper.delete(new QueryWrapper<ExtendValue>().in("assets_id", assetsIds));
                List<CmdbAssetsRelation> cmdbAssetsRelationList = cmdbAssetsRelationMapper.selectList(new QueryWrapper<CmdbAssetsRelation>().in("assets_id_one", assetsIds).or().in("assets_id_two", assetsIds));
                if (CollUtil.isNotEmpty(cmdbAssetsRelationList)) {
                    cmdbAssetsRelationMapper.deleteBatchIds(cmdbAssetsRelationList.stream().map(CmdbAssetsRelation::getId).collect(Collectors.toList()));
                }
                device2assetsMapper.delete(new QueryWrapper<MomgDevice2assets>().in("assets_id", assetsIds));
                assetsService.removeByIds(assetsIds);

                List<MomgDevice2assets> device2assetsList = device2assetsMapper.selectList(new QueryWrapper<MomgDevice2assets>().in("assets_id", assetsIds));
                if (CollUtil.isNotEmpty(device2assetsList)) {
                    List<String> deviceIds = device2assetsList.stream().map(MomgDevice2assets::getDeviceId).collect(Collectors.toList());
                    //网关子设备
                    List<MomgGateway2device> children = gateway2deviceMapper.selectList(new QueryWrapper<MomgGateway2device>().in("device_id", deviceIds));
                    if (CollUtil.isNotEmpty(children)) {
                        for (MomgGateway2device obj : children) {
                            DeviceInfo deviceInfo = deviceInfoMapper.selectById(obj.getGatewayId());
                            if (deviceInfo != null && deviceInfo.getDelflag() == 0) {
                                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                                return Result.error("当前选中的设备中至少有一个网关子设备与某一网关设备绑定，禁止删除！");
                            }
                        }
                    }

                    //网关设备
                    List<MomgGateway2device> gateways = gateway2deviceMapper.selectList(new QueryWrapper<MomgGateway2device>().in("gateway_id", deviceIds));
                    if (gateways != null && !gateways.isEmpty()) {
                        for (MomgGateway2device obj : gateways) {
                            if (StringUtils.isNotEmpty(obj.getDeviceId())) {
                                if (deviceInfoMapper.selectById(obj.getDeviceId()) != null && deviceInfoMapper.selectById(obj.getDeviceId()).getDelflag() == 0) {
                                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                                    return Result.error("当前选中的设备中至少有一个网关设备下包含有网关子设备，禁止删除！");
                                }
                            }
                        }
                    }

                    List<DeviceInfo> deviceInfoList = deviceInfoService.listByIds(deviceIds);
                    if (CollUtil.isNotEmpty(deviceInfoList)) {
                        List<String> codeList = deviceInfoList.stream().map(DeviceInfo::getDeviceCode).collect(Collectors.toList());
                        // 删除设备与产品关联标识
                        redisUtil.del(codeList.stream().map(x -> "rela:" + x).collect(Collectors.toList()).toArray(new String[]{}));
                        // 删除设备与网关关系
                        gateway2deviceMapper.delete(new QueryWrapper<MomgGateway2device>().in("device_id", deviceIds));
                        // 删除关联的连接参数
                        deviceConnectInfoService.remove(new QueryWrapper<DeviceConnectInfo>().in("device_id", deviceIds));
                        // 删除绑定的机柜
                        topoCabinet2deviceMapper.delete(new QueryWrapper<TopoCabinet2device>().in("device_id", deviceIds));
                        // 删除关联的终端
                        terminalDeviceService.remove(new QueryWrapper<TerminalDevice>().in("unique_code", codeList));
                        // 删除设备标签关系
                        utlTagResourceService.remove(new QueryWrapper<UtlTagResource>().in("resource_id", deviceIds));
                        // 删除关联的scheduleSetting
                        scheduleSettingService.remove(new QueryWrapper<ScheduleSetting>().in("device_code", codeList));
                        // 删除设备告警信息
                        alarmHistoryService.remove(new QueryWrapper<AlarmHistory>().in("device_id", deviceIds));
                        // 删除Zabbix服务设备关联数据
                        for (String deviceCode : codeList) {
                            String hostKey = "zabbix_" + deviceCode + "_hosts";
                            if (redisUtil.hasKey(hostKey)) {
                                String hostArrayJsonStr = redisUtil.get(hostKey).toString();
                                JSONArray hostArray = JSONArray.parseArray(hostArrayJsonStr);
                                List<String> hostIds = hostArray.stream().map(h -> ((JSONObject) h).getString("hostid")).collect(Collectors.toList());
                                hostIds.forEach(hostId -> {
                                    redisUtil.del("zabbix_" + hostId + "_items");
                                });
                                zabbixAlarmService.remove(new QueryWrapper<MomgZabbixAlarm>().in("host_id", hostIds));
                            }
                        }
                        // 删除设备基本信息
                        deviceInfoMapper.deleteBatchIds(deviceIds);

                        //发布scheduleSetting连接参数删除事件
                        publishConnectDeleteEvent(codeList);
                    }
                }
            }

            return Result.OK("资产及其关联数据删除成功!");
        } catch (Exception e) {
            log.error("资产及其关联数据删除异常！", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return Result.error("资产及其关联数据删除异常！");
        }

    }

    @Transactional
    public Result<?> deleteBatchAssetsAct(String ids, String tempProcessInstanceId) {
        try {
            List<String> assetsIds = Arrays.asList(ids.split(","));
            if (CollUtil.isNotEmpty(assetsIds)) {
                for (String ele : assetsIds) {
                    AssetsAct assetsAct = new AssetsAct();
                    assetsAct.setAssetsId(ele);
                    assetsAct.setOperateType("1");
                    assetsAct.setTempProcessInstanceId(tempProcessInstanceId);
                    assetsActService.save(assetsAct);
                }
            }
            return Result.OK("资产_流程临时数据删除成功!");
        } catch (Exception e) {
            log.error("资产_流程临时数据删除异常！", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return Result.error("资产_流程临时数据删除异常！");
        }

    }


    // 发布连接参数删除事件
    public void publishConnectDeleteEvent(List<String> keys) {
        JSONObject data = new JSONObject();
        data.put("deviceKeys", keys);
        redisMq.publish(Streams.DEVICE_MONITORING_STOP, data);
    }


    // *-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-


    public Result<?> selectAssetsAndPositionInfo(String deviceId) {
        JSONObject resObj = new JSONObject();
        try {
            MomgDevice2assets device2assets = device2assetsMapper.selectOne(new QueryWrapper<MomgDevice2assets>().eq("device_id", deviceId));
            if (device2assets != null) {
                Assets assetsById = assetsService.getById(device2assets.getAssetsId());
                AssetsCategory categoryById = categoryService.getById(assetsById.getAssetsCategoryId());
                assetsById.setAssetsCategoryText(categoryById.getCategoryName());
                resObj.put("assetsInfo", assetsById);
            }
            TopoCabinet2device cabinet2device = topoCabinet2deviceMapper.selectOne(new QueryWrapper<TopoCabinet2device>().eq("device_id", deviceId));
            if (cabinet2device != null) {
                TopoCabinet topoCabinet = topoCabinetMapper.selectCabinetById(cabinet2device.getCabinetId());
                if (topoCabinet != null) {
                    cabinet2device.setCabinetName(topoCabinet.getName());
                    cabinet2device.setLayerPool(JSONArray.parseArray(cabinet2device.getLayerPoolStr()));
                    cabinet2device.setRoomId(topoCabinet.getRoomId());
                    if (StringUtils.isNotEmpty(topoCabinet.getRoomId())) {
                        TopoRoom topoRoom = topoRoomService.getById(topoCabinet.getRoomId());
                        cabinet2device.setRoomName(topoRoom.getName());
                        List<TopoRoom> parent = getParent(topoRoom, new ArrayList<TopoRoom>());
                        Collections.reverse(parent);
                        parent.add(topoRoom);
                        cabinet2device.setLocation(String.join(" ", parent.stream().map(TopoRoom::getName).collect(Collectors.toList())));
                    }
                    resObj.put("cabinet2device", cabinet2device);
                }
            }
            return Result.OK(resObj);
        } catch (Exception e) {
            log.error("查询设备关联的资产和位置信息异常！", e);
            return Result.error("查询设备关联的资产和位置信息异常！" + e.getMessage());
        }
    }

    public List<TopoRoom> getParent(TopoRoom topoRoom, List<TopoRoom> parentList) {
        if (StringUtils.isNotEmpty(topoRoom.getPid())) {
            List<TopoRoom> parents = topoRoomService.list(new QueryWrapper<TopoRoom>().eq("id", topoRoom.getPid()));
            if (CollUtil.isNotEmpty(parents)) {
                parentList.addAll(parents);
                for (TopoRoom parent : parents) {
                    parentList = getParent(parent, parentList);
                }
            }
        }
        return parentList;
    }


    public Result<?> selectDeviceAndPositionInfo(String assetsId) {
        JSONObject resObj = new JSONObject();
        try {
            MomgDevice2assets device2assets = device2assetsMapper.selectOne(new QueryWrapper<MomgDevice2assets>().eq("assets_id", assetsId));
            if (device2assets != null) {
                DeviceInfo devById = deviceInfoService.getById(device2assets.getDeviceId());
                if (devById != null) {
                    resObj.put("deviceInfo", devById);
                    TopoCabinet2device cabinet2device = topoCabinet2deviceMapper.selectOne(new QueryWrapper<TopoCabinet2device>().eq("device_id", devById.getId()));
                    if (cabinet2device != null) {
                        TopoCabinet topoCabinet = topoCabinetMapper.selectCabinetById(cabinet2device.getCabinetId());
                        if (topoCabinet != null) {
                            cabinet2device.setCabinetName(topoCabinet.getName());
                            cabinet2device.setLayerPool(JSONArray.parseArray(cabinet2device.getLayerPoolStr()));
                            cabinet2device.setRoomId(topoCabinet.getRoomId());
                            if (StringUtils.isNotEmpty(topoCabinet.getRoomId())) {
                                TopoRoom topoRoom = topoRoomService.getById(topoCabinet.getRoomId());
                                cabinet2device.setRoomName(topoRoom.getName());
                                List<TopoRoom> parent = getParent(topoRoom, new ArrayList<TopoRoom>());
                                Collections.reverse(parent);
                                parent.add(topoRoom);
                                cabinet2device.setLocation(String.join(" ", parent.stream().map(TopoRoom::getName).collect(Collectors.toList())));
                            }
                            resObj.put("cabinet2device", cabinet2device);
                        }
                    }
                }
            }
            return Result.OK(resObj);
        } catch (Exception e) {
            log.error("查询资产关联的设备和位置信息异常！", e);
            return Result.error("查询资产关联的设备和位置信息异常！" + e.getMessage());
        }
    }


    // *-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-


    public void assetsLog(Assets assets) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        AssetsLog assetsLog = new AssetsLog();
        assetsLog.setAssetsId(assets.getId());
        String statusStr = "";
        if (StringUtils.isNotEmpty(assets.getStatusId())) {
            CmdbStatus cmdbStatus = cmdbStatusService.getById(assets.getStatusId());
            statusStr = "，当前状态：" + cmdbStatus.getName();
        }
        switch (assets.getType()) {
            case 0:
                assetsLog.setCreateBy(assets.getCreateBy());
                assetsLog.setContent(String.format("%s%s", "新增资产信息", statusStr));
                break;
            case 1:
                assetsLog.setCreateBy(assets.getUpdateBy());
                assetsLog.setContent(String.format("%s%s", "编辑资产信息", statusStr));
                break;
            case 2:
                assetsLog.setCreateBy(assets.getUpdateBy());
                assetsLog.setContent(String.format("%s%s", "修改状资产态", statusStr));
        }
        assetsLog.setAssetsConfig("1");
        assetsLog.setUserId(loginUser.getUsername());
        iAssetsLogService.save(assetsLog);
    }

    public void deviceLog(String deviceId, String operateType, JSONObject infos) {
        MomgLogDevice momgLogDevice = new MomgLogDevice();
        momgLogDevice.setDeviceId(deviceId);
        momgLogDevice.setType(operateType);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("headers", infos);
        momgLogDevice.setContent(jsonObject.toJSONString());
        momgLogDeviceService.save(momgLogDevice);
    }


    // *-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-


    @Transactional
    public Result<?> edit(JSONObject infos) {
        Assets assetsInfo = null;
        JSONObject assetsObj = infos.getJSONObject("assetsInfo");
        if (assetsObj != null && !assetsObj.isEmpty()) {
            assetsInfo = JSON.parseObject(assetsObj.toJSONString(), Assets.class);
        }
        if (assetsInfo != null) {
            try {
                List<AssetsAct> duplicateAssetsList = assetsActMapper.selectAssetsUnion(assetsInfo.getAssetsName(), assetsInfo.getAssetsCode(), assetsInfo.getAssetsId());
                if (CollUtil.isNotEmpty(duplicateAssetsList)) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return Result.error("资产_流程临时数据的名称或标识已存在！");
                }
                if (StringUtils.isNotEmpty(infos.getString("deviceInfo"))) {
                    JSONObject devObj = infos.getJSONObject("deviceInfo");
                    DeviceInfo deviceInfo = JSON.parseObject(devObj.toJSONString(), DeviceInfo.class);
                    if (deviceInfo.getDeviceCode().contains("_")) {
                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                        return Result.error("设备唯一标识中不得包含“_”符号！");
                    }
                    List<DeviceInfo> duplicateDevList = assetsActMapper.selectDevUnion(deviceInfo.getName(), deviceInfo.getDeviceCode(), deviceInfo.getId());
                    if (CollUtil.isNotEmpty(duplicateDevList)) {
                        return Result.error("设备名称或唯一标识已存在！");
                    }
                    Product product = deviceInfoService.getProductById(deviceInfo.getProductId(), null);
                    if (product == null) {
                        return Result.error("设备的产品不存在！");
                    }
                }
                if (StringUtils.isNotEmpty(assetsInfo.getAssetsCategoryId())) {
                    AssetsCategory assetsCategory = categoryService.getById(assetsInfo.getAssetsCategoryId());
                    if (assetsCategory != null) {
                        assetsInfo.setAssetsCategoryId(assetsCategory.getId());
                    } else {
                        throw new RuntimeException("关联的资产的资产类型不存在！");
                    }
                }
                if (StringUtils.isNotEmpty(assetsInfo.getProducerId())) {
                    CmdbSupplier supplier = cmdbSupplierService.getById(assetsInfo.getProducerId());
                    if (supplier != null) {
                        assetsInfo.setProducerName(supplier.getName());
                    } else {
                        throw new RuntimeException("关联的资产的供应商不存在！");
                    }
                }
                AssetsAct assetsAct = CustomConvertUtil.ConvertClass(assetsInfo, AssetsAct.class);
                assetsAct.setAssociateData(infos.toJSONString());
                assetsActService.updateById(assetsAct);
            } catch (Exception e) {
                log.error("编辑临时数据出现异常！", e);
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return Result.error("编辑临时数据出现异常！");
            }
        } else {
            return Result.error("临时数据不存在！");
        }

        return Result.OK("临时数据编辑成功！");
    }


    private static final LocalCacheUtils cacheUtils = LocalCacheUtils.getInstance();
    private static List<String> displayCodeList = new ArrayList<>();

    static {
        displayCodeList.add("cpuRate");
        displayCodeList.add("memRate");
        displayCodeList.add("memUtilizRate");
        displayCodeList.add("mem.memUsed");
        displayCodeList.add("memUsed");
        displayCodeList.add("diskUsed");
        displayCodeList.add("diskFree");
        displayCodeList.add("allSpeed"); //总速率
        displayCodeList.add("inSpeed"); //下行速率
        displayCodeList.add("outSpeed"); //上行速率
    }

    private static ThreadLocal<SimpleDateFormat> sdfThreadLocal = new ThreadLocal<SimpleDateFormat>() {
        @Override
        protected SimpleDateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM-dd");
        }
    };


    @Transactional
    public Result<?> getMonitorDataByTime(String deviceCode, String productId, String startTime, String endTime) {
        long l = System.currentTimeMillis();
        String categoryCode = productMapper.getCategoryCodeByDeviceCode(deviceCode);
        Map<String, List<ProertyMetadata>> dislplayMetadataMap = new HashMap<>();
        List<String> topoLevelCodeList = displayCodeList.stream().map(c -> {
            if (c.contains(".")) {
                c = c.substring(0, c.indexOf("."));
            }
            return c;
        }).collect(Collectors.toList());
        List<ProertyMetadata> displayMetadataList = proertyMetadataMapper.findMetadataByDevCode(deviceCode, topoLevelCodeList);
        if (CollUtil.isNotEmpty(displayMetadataList)) {
            dislplayMetadataMap = displayMetadataList.stream().collect(Collectors.groupingBy(ProertyMetadata::getTransferProtocol));
            List<String> complexCodeList = displayCodeList.stream().filter(c -> c.contains(".")).collect(Collectors.toList());
            for (String str : complexCodeList) {
                for (Map.Entry<String, List<ProertyMetadata>> entry : dislplayMetadataMap.entrySet()) {
                    List<ProertyMetadata> entryValue = entry.getValue();
                    for (ProertyMetadata metadata : entry.getValue()) {
                        if (metadata.getCode().equals(str.substring(0, str.indexOf(".")))) {
                            entryValue = entry.getValue().stream().filter(m -> !m.getCode().equals(str.substring(0, str.indexOf(".")))).collect(Collectors.toList());
                            Map<String, ProertyMetadata> childMetadataMap = metadata.getProertyMetadataList().stream().collect(Collectors.toMap(ProertyMetadata::getCode, ProertyMetadata -> ProertyMetadata));
                            String childCode = str.substring(str.indexOf(".") + 1, str.length());

                            ProertyMetadata childProertyMetadata = childMetadataMap.get(childCode);
                            if (childProertyMetadata != null) {
                                childProertyMetadata.setCode(str);
                                entryValue.add(childProertyMetadata);
                            }
                        }
                    }
                    dislplayMetadataMap.put(entry.getKey(), entryValue);
                }
            }
        }
        String currentStep = (String) cacheUtils.getValueByKey(com.yuanqiao.insight.common.constant.CommonConstant.DATA_DICT_KEY + "unit_convert_unit");
        JSONObject resultMap = new JSONObject();
        List<String> transferProtocolList = deviceInfoMapper.findAllTransfers(productId);

        long l1 = System.currentTimeMillis();
        System.out.println(" 数据预处理耗时 ：" + (l1 - l));

        if (CollUtil.isNotEmpty(transferProtocolList)) {
            for (String protocol : transferProtocolList) {
                List<ProertyMetadata> metadataList = dislplayMetadataMap.get(protocol);
                JSONObject eleObj = new JSONObject();
                if (CollUtil.isNotEmpty(metadataList)) {
                    for (ProertyMetadata metadata : metadataList) {
                        long l2 = System.currentTimeMillis();
                        eleObj.put(metadata.getCode(), searchFromEsAggs("metrics-" + categoryCode.toLowerCase() + "-" + protocol.toLowerCase(), deviceCode, metadata, currentStep, startTime, endTime));
                        long l3 = System.currentTimeMillis();
                        System.out.println(" 查询Es - " + metadata.getCode() + " 监控数据耗时： " + (l3 - l2));
                    }
                }
                resultMap.put(protocol, eleObj);
            }
        }
        return Result.OK(resultMap);
    }

    public List<JSONObject> searchFromEsAggs(String esIndex, String deviceCode, ProertyMetadata metadata, String currentStep, String startTime, String endTime) {
        Map<String, JSONObject> resultMap = new HashMap<>();
        try {
            ElasticsearchIndicesClient indices = client.indices();
            boolean value = indices.exists(e -> e.index(esIndex)).value();
            if (!value) {
                return new ArrayList<>();
            }
            BoolQuery.Builder boolBuilder = new BoolQuery.Builder();
            boolBuilder.must(q -> q.term(t -> t.field("tag.host").value(FieldValue.of(deviceCode))));
            boolBuilder.must(q -> q.exists(e -> e.field(metadata.getCode())));
            if (metadata.getCode().equals("cpuRate")) {
                boolBuilder.mustNot(q2 -> q2.exists(e -> e.field("cpuCores")));
            }
            boolBuilder.must(q -> q.range(r -> r.field("@timestamp").gte(JsonData.of(startTime)).lte(JsonData.of(endTime))));

            SearchResponse<Object> searchResponse = client.search(s -> s
                            .index(esIndex)
                            .query(q -> q.bool(boolBuilder.build()))
                            .aggregations("max_value_by_day", agg -> agg.dateHistogram(dh -> dh.field("@timestamp").calendarInterval(CalendarInterval.Day).minDocCount(1)).aggregations("max_value", agg1 -> agg1.max(m -> m.field(metadata.getCode()))))
                            .trackTotalHits(TrackHits.of(t -> t.enabled(true)))
                            .sort(q -> q.field(f -> f.field("@timestamp").order(SortOrder.Asc)))
                            .size(0)
                    , Object.class);

            List<DateHistogramBucket> aggsBuckets = searchResponse.aggregations().get("max_value_by_day").dateHistogram().buckets().array();
            getResultByAggs(aggsBuckets, resultMap);

        } catch (Exception e) {
            log.error("ES查询设备监控数据趋势异常！", e);
        }

        resultMap.forEach((k, v) -> {
            Double originStep = StringUtils.isNotEmpty(metadata.getOriginStep()) ? Double.valueOf(metadata.getOriginStep()) : 1024;
            Map<String, Object> unitConvertMap = UnitConvertUtil.unitDynamic(metadata.getUnit(), v.getDouble("value"), originStep);
            v.put("value", unitConvertMap.get("value"));
            v.put("unit", unitConvertMap.get("unit"));
        });

        // 获取当前日期零时零分 2023-12-29
        DateTime startTime1 = DateUtil.beginOfDay(new Date());
        // 向前推移五天 2023-12-24
        startTime1 = DateUtil.offsetDay(startTime1, -30);
        // 在<"2023-12-29", 150>格式的Map中 从五天前的日期开始至今逐天检查是否存在对应日期的键，不存在则补零添加到Map中
        DateUtil.rangeConsume(startTime1, new Date(), DateField.DAY_OF_YEAR, d1 -> resultMap.computeIfAbsent(DateUtil.format(d1, "yyyy-MM-dd"), v -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("unit", "");
            jsonObject.put("value", "");
            return jsonObject;
        }));

        List<JSONObject> vList = new ArrayList<>();
        resultMap.entrySet().stream().sorted(Map.Entry.comparingByKey()).forEach(entry -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("time", entry.getKey());
            jsonObject.put("value", entry.getValue());
            vList.add(jsonObject);
        });

        return vList;
    }

    public void getResultByAggs(List<DateHistogramBucket> aggsBuckets, Map<String, JSONObject> resultMap) throws IOException {
        for (DateHistogramBucket bucket : aggsBuckets) {
            JSONObject dataEleObj = new JSONObject();
            dataEleObj.put("value", bucket.aggregations().get("max_value").max().value());
            try {
                if (StringUtils.isNotEmpty((String) bucket.keyAsString())) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    resultMap.put(sdfThreadLocal.get().format(simpleDateFormat.parse((String) bucket.keyAsString())), dataEleObj);
                }
            } catch (ParseException e) {
                log.error("Es Hit数据格式化异常！", e);
                continue;
            }
        }
    }


    public void changeTaskGateway(String deviceCode, String gatewayCode) {
        List<ScheduleSetting> scheduleSettingList = scheduleSettingService.list(new QueryWrapper<ScheduleSetting>().eq("device_code", deviceCode));
        if (CollUtil.isNotEmpty(scheduleSettingList)) {
            scheduleSettingList = scheduleSettingList.stream().peek(s -> s.setGatewayCode(gatewayCode)).collect(Collectors.toList());
            scheduleSettingService.updateBatchById(scheduleSettingList);

            List<String> settingIdList = scheduleSettingList.stream().map(ScheduleSetting::getId).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(gatewayCode)) {
                publishConnectChangeEvent(settingIdList, Arrays.asList(new String[]{deviceCode}));
            } else {
                publishConnectDeleteEvent(Arrays.asList(new String[]{deviceCode}));
            }
        }
    }

    @Transactional
    public DeviceInfo addDevWithTerIfNotExist(String hostName) {
        DeviceInfo device = null;
        try {
            device = deviceInfoMapper.selectOne(new QueryWrapper<DeviceInfo>().eq("device_code", hostName));
            // 不存在的设备，在此自动入库
            if (device == null) {
                if (this.isOverNumber()) {
                    log.error("当前设备存量已超出授权证书限制！无法添加设备 " + hostName);
                    return null;
                }
                device = new DeviceInfo();
                device.setStatus(0);
                device.setAlarmStatus(0);
                device.setPlatformCode(platformCode);
                device.setDescription("请求平台分配网关添加");
                device.setDeviceCode(hostName);
                device.setName("tg_" + hostName); // 默认命名前缀
                if (hostName.contains(":") || hostName.contains("-")) {
                    device.setMac(hostName);
                } else {
                    device.setIp(hostName);
                }
                Product product = deviceInfoService.getProductById(null, "DeskTopPush"); // 默认桌面机产品
                device.setProductId(product.getId());
                device.setProductName(product.getDisplayName());
                device.setCategoryId(product.getAssetsCategoryId());
                deviceInfoMapper.insert(device);
            }

            TerminalDevice terminal = terminalDeviceService.getOne(new QueryWrapper<TerminalDevice>().eq("unique_code", hostName));
            if (terminal == null) {
                if (this.isOverNumber()) {
                    log.error("当前设备存量已超出授权证书限制！无法添加终端 " + hostName);
                    return null;
                }
                terminal = new TerminalDevice();
                terminal.setName(device.getName());
                terminal.setUniqueCode(hostName);
                terminal.setMac(device.getMac());
                terminal.setIp(device.getIp());
                terminal.setPlatformCode(platformCode);
                terminal.setDescription("请求平台分配网关添加");
                terminalDeviceService.save(terminal);
            }
        } catch (Exception e) {
            log.error("终端请求分配网关时添加设备和终端异常！", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }

        return device;
    }

    /**
     * 控制添加设备数量
     * @return
     */
    public boolean isOverNumber(){
        String numberKey = "insight_empowerNumber";

        if (!redisUtil.hasKey(numberKey)){
            return false;
        }

        String number = (String) redisUtil.get(numberKey);
        String decrypt = KeyUtil.decrypt(number);

        QueryWrapper<DeviceInfo> deviceInfoQueryWrapper = new QueryWrapper<>();
        deviceInfoQueryWrapper.eq("delflag",0);
        int count = deviceInfoService.count(deviceInfoQueryWrapper);
        return count >= Integer.parseInt(decrypt);
    }

}
