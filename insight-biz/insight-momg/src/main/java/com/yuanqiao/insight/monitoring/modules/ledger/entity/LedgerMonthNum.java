package com.yuanqiao.insight.monitoring.modules.ledger.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * @Description: 资产月报统计
 * @Author: jeecg-boot
 * @Date:   2020-03-18
 * @Version: V1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class LedgerMonthNum {

    @ApiModelProperty(value = "资源")
	private String assets;

    @ApiModelProperty(value = "上线数")
	private String onlineNum;
}
