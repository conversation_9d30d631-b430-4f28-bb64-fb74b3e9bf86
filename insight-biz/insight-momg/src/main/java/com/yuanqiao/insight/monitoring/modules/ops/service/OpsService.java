package com.yuanqiao.insight.monitoring.modules.ops.service;

import com.alibaba.fastjson.JSONArray;
import com.yuanqiao.insight.monitoring.modules.ops.entity.Top;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface OpsService {

    String exportPdf(Map<String, JSONArray> map, List<Top> useCount, List<Top> bizCount, Map<String, Map<String, BigDecimal>> alarmTop, List<Top> productTop, List<Top> assetsTop);


}
