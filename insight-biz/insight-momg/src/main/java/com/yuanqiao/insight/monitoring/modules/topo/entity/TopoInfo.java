package com.yuanqiao.insight.monitoring.modules.topo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 拓扑图
 * @Author: jeecg-boot
 * @Date: 2021-03-21
 * @Version: V1.0
 */
@Data
@TableName("momg_topo_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "topo_info对象", description = "拓扑图")
public class TopoInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;

    /**
     * 名称
     */
    @Excel(name = "名称", width = 15)
    @ApiModelProperty(value = "名称")
    private String topoName;
    /**
     * 拓扑图数据
     */
    @Excel(name = "拓扑图数据", width = 15)
    private String topoDataJson;
    /**
     * 拓扑保存IMG
     */
    @Excel(name = "拓扑保存IMG", width = 15)
    @ApiModelProperty(value = "拓扑保存IMG")
    private String topoImg;
    /**
     * 拓扑保存SVG
     */
    @Excel(name = "拓扑保存SVG", width = 15)
    @ApiModelProperty(value = "拓扑保存SVG")
    private String topoSvg;
    /**
     * 拓扑类型 0为网络拓扑,1为应用拓扑
     */
    @Excel(name = "拓扑类型", width = 15)
    @ApiModelProperty(value = "拓扑类型")
    private String topoType;
    /**
     * 展示配置 是否展示到大屏上面，0：不展示，1：展示
     */
    @Excel(name = "属性配置", width = 15)
    @ApiModelProperty(value = "属性配置")
    private String showType;
    /**
     * 拓扑配置
     */
    @Excel(name = "拓扑配置", width = 15)
    @ApiModelProperty(value = "拓扑配置")
    private String topoConfig;

    @TableField(exist = false)
    private List<String> imgUrlList;

    @TableField(exist = false)
    private List<NodeInfo> nodeList;

    @TableField(exist = false)
    private List<EdgeInfo> edgeList;
}
