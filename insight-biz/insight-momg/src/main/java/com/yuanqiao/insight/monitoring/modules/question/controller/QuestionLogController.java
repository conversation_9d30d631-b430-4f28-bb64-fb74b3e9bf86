package com.yuanqiao.insight.monitoring.modules.question.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.monitoring.modules.question.entity.QuestionLog;
import com.yuanqiao.insight.monitoring.modules.question.service.IQuestionLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: 问题处理记录
 * @Author: jeecg-boot
 * @Date:   2020-03-27
 * @Version: V1.0
 */
@Slf4j
@Api(tags="问题处理记录")
@RestController
@RequestMapping("/question/questionLog")
public class QuestionLogController extends JeecgController<QuestionLog, IQuestionLogService> {
	@Autowired
	private IQuestionLogService questionLogService;

	/**
	 * 分页列表查询
	 *
	 * @param questionLog
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "问题处理记录-分页列表查询")
	@ApiOperation(value="问题处理记录-分页列表查询", notes="问题处理记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(QuestionLog questionLog,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<QuestionLog> queryWrapper = QueryGenerator.initQueryWrapper(questionLog, req.getParameterMap());
		Page<QuestionLog> page = new Page<QuestionLog>(pageNo, pageSize);
		IPage<QuestionLog> pageList = questionLogService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	/**
	 * 添加
	 *
	 * @param questionLog
	 * @return
	 */
	@AutoLog(value = "问题处理记录-添加")
	@ApiOperation(value="问题处理记录-添加", notes="问题处理记录-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody QuestionLog questionLog) {
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		questionLog.setHandleUser(sysUser.getRealname());
		questionLogService.save(questionLog);
		return Result.ok("添加成功！");
	}

	/**
	 * 编辑
	 *
	 * @param questionLog
	 * @return
	 */
	@AutoLog(value = "问题处理记录-编辑")
	@ApiOperation(value="问题处理记录-编辑", notes="问题处理记录-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody QuestionLog questionLog) {
		questionLogService.updateById(questionLog);
		return Result.ok("编辑成功!");
	}

	/**
	 * 通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "问题处理记录-通过id删除")
	@ApiOperation(value="问题处理记录-通过id删除", notes="问题处理记录-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		questionLogService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 * 批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "问题处理记录-批量删除")
	@ApiOperation(value="问题处理记录-批量删除", notes="问题处理记录-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.questionLogService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功！");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "问题处理记录-通过id查询")
	@ApiOperation(value="问题处理记录-通过id查询", notes="问题处理记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		QuestionLog questionLog = questionLogService.getById(id);
		return Result.ok(questionLog);
	}

  /**
   * 导出excel
   *
   * @param request
   * @param questionLog
   */
  @RequestMapping(value = "/exportXls")
  public ModelAndView exportXls(HttpServletRequest request, QuestionLog questionLog) {
      return super.exportXls(request, questionLog, QuestionLog.class, "问题处理记录");
  }

  /**
   * 通过excel导入数据
   *
   * @param request
   * @param response
   * @return
   */
  @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
  public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      return super.importExcel(request, response, QuestionLog.class);
  }

}
