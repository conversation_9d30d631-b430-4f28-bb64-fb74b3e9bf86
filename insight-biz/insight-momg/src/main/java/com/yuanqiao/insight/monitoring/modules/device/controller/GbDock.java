package com.yuanqiao.insight.monitoring.modules.device.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yuanqiao.insight.monitoring.modules.device.entity.MomgLogDevice;
import com.yuanqiao.insight.monitoring.modules.device.mapper.MomgLogDeviceMapper;
import com.yuanqiao.insight.service.device.entity.DeviceConnectInfo;
import com.yuanqiao.insight.service.device.entity.DeviceInfo;
import com.yuanqiao.insight.service.device.mapper.DeviceConnectInfoMapper;
import com.yuanqiao.insight.service.device.mapper.DeviceInfoMapper;
import com.yuanqiao.insight.service.device.service.IDeviceInfoService;
import com.yuanqiao.insight.service.product.entity.DeviceConnectTemplateInfo;
import com.yuanqiao.insight.service.product.mapper.DeviceConnectTemplateInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/gb/test")
public class GbDock extends JeecgController<DeviceInfo, IDeviceInfoService> {
    @Autowired
    private DeviceInfoMapper deviceInfoMapper;
    @Autowired
    private DeviceConnectTemplateInfoMapper deviceConnectTemplateInfoMapper;
    @Autowired
    private DeviceConnectInfoMapper deviceConnectInfoMapper;
    @Autowired
    private MomgLogDeviceMapper momgLogDeviceMapper;


    private static String AddTime = "";
    private static String UpdateTime = "";
    private static String DeleteTime = "";

    private static final List<String> connectCodeList = new ArrayList<String>();

    static {
        //IP地址
        connectCodeList.add("ip");
        //型号
        connectCodeList.add("model");
        //品牌
        connectCodeList.add("brand");
        //网络层级
        connectCodeList.add("netlevel");
    }

    String code = "";
    String name = "";
    String ip = "";
    String model = "";
    String brand = "";
    String netLevel = "";


    /**
     * 设备新增 或 首次推送上报
     */
    @GetMapping(value = "/getAdd")
    public void getAddNetDevice() {

        List<String> productIdList = getNetProductIdList();

        //所有网络设备
        if (StringUtils.isEmpty(AddTime)) {
            if (!CollectionUtils.isEmpty(productIdList)) {
                List<DeviceInfo> deviceList = deviceInfoMapper.selectListForGB(productIdList);
                if (!CollectionUtils.isEmpty(deviceList)) {
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    System.out.println(dateFormat.format(deviceList.get(0).getCreateTime()));
                    this.AddTime = dateFormat.format(deviceList.get(0).getCreateTime());
                    Map<String, List<DeviceInfo>> deviceConnectInfoMap = deviceList.stream().collect(Collectors.groupingBy(DeviceInfo::getDeviceCode));
                    deviceConnectInfoMap.forEach((key, value) -> {
                        if (!CollectionUtils.isEmpty(value)) {
                            Map<String, String> connectMap = value.stream().collect(Collectors.toMap(DeviceInfo::getConnectCode, DeviceInfo::getConnectValue));
                            code = key;
                            name = value.get(0).getName();
                            ip = StringUtils.isEmpty(connectMap.get(connectCodeList.get(0))) ? "--" : connectMap.get(connectCodeList.get(0));
                            model = StringUtils.isEmpty(connectMap.get(connectCodeList.get(1))) ? "--" : connectMap.get(connectCodeList.get(1));
                            brand = StringUtils.isEmpty(connectMap.get(connectCodeList.get(2))) ? "--" : connectMap.get(connectCodeList.get(2));
                            netLevel = StringUtils.isEmpty(connectMap.get(connectCodeList.get(3))) ? "--" : connectMap.get(connectCodeList.get(3));

                            System.out.println("code：" + code);
                            System.out.println("name：" + name);
                            System.out.println("ip：" + ip);
                            System.out.println("model：" + model);
                            System.out.println("brand：" + brand);
                            System.out.println("netLevel：" + netLevel);
                            System.out.println("/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*");
                        }
                    });
                }
            }
        } else {
            //增量添加的网络设备
            if (!CollectionUtils.isEmpty(productIdList)) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                List<MomgLogDevice> addLogList = momgLogDeviceMapper.selectList(new QueryWrapper<MomgLogDevice>().gt("create_time", AddTime).eq("type", "设备增加").isNotNull("content").ne("content", "").orderByDesc("create_time"));
                if (!CollectionUtils.isEmpty(addLogList)) {
                    System.out.println(dateFormat.format(addLogList.get(0).getCreateTime()));
                    this.AddTime = dateFormat.format(addLogList.get(0).getCreateTime());
                    rebuildEntity(null, addLogList, productIdList);
                }
            }
        }

    }


    /**
     * 设备修改推送上报
     */
    @GetMapping(value = "/getUpdate")
    public void getUpdateNetDevice() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<String> productIdList = getNetProductIdList();

        if (!CollectionUtils.isEmpty(productIdList)) {
            List<MomgLogDevice> updateLogList = momgLogDeviceMapper.selectList(new QueryWrapper<MomgLogDevice>().gt("create_time", UpdateTime).eq("type", "属性修改").isNotNull("content").ne("content", "").orderByDesc("create_time"));
            if (!CollectionUtils.isEmpty(updateLogList)) {
                System.out.println(dateFormat.format(updateLogList.get(0).getCreateTime()));
                this.UpdateTime = dateFormat.format(updateLogList.get(0).getCreateTime());
                rebuildEntity(null, updateLogList, productIdList);
            }
        }

    }


    /**
     * 设备删除推送上报
     */
    @GetMapping(value = "/getDelete")
    public void getDeleteNetDevice() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<String> productIdList = getNetProductIdList();

        if (!CollectionUtils.isEmpty(productIdList)) {
            List<MomgLogDevice> deleteLogList = momgLogDeviceMapper.selectList(new QueryWrapper<MomgLogDevice>().gt("create_time", DeleteTime).eq("type", "删除").isNotNull("content").ne("content", "").orderByDesc("create_time"));
            if (!CollectionUtils.isEmpty(deleteLogList)) {
                System.out.println(dateFormat.format(deleteLogList.get(0).getCreateTime()));
                this.DeleteTime = dateFormat.format(deleteLogList.get(0).getCreateTime());
                rebuildEntity("D", deleteLogList, productIdList);
            }
        }

    }


    /**
     * 根据集合中的对象某个属性进行过滤
     *
     * @return
     */
    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }

    /**
     * 获取所有网络产品的ID
     *
     * @return
     */
    public List<String> getNetProductIdList() {
        //初始化国办规范网络产品ID结果集
        List<String> productIdList = new ArrayList<>();
        //查询所有连接参数模板
        List<DeviceConnectTemplateInfo> templateInfoList = deviceConnectTemplateInfoMapper.selectList(new QueryWrapper<>());
        //重组每个产品ID及其对应的连接参数模板
        Map<String, List<DeviceConnectTemplateInfo>> productIdConnectTemplateMap = templateInfoList.stream().collect(Collectors.groupingBy(DeviceConnectTemplateInfo::getProductId));
        //遍历重组后的数据，过滤出连接参数模板中国办相关参数存在且非空的组合
        for (Map.Entry<String, List<DeviceConnectTemplateInfo>> entry : productIdConnectTemplateMap.entrySet()) {
            List<String> codeList = entry.getValue().stream().map(DeviceConnectTemplateInfo::getCode).collect(Collectors.toList());
            if (codeList.size() >= connectCodeList.size() && codeList.containsAll(connectCodeList)) {
                productIdList.add(entry.getKey());
            }
        }
        return productIdList;
    }


    /**
     * 封装国办设备资源实体
     *
     * @param dataType
     * @param logList
     * @param productIdList
     */
    private void rebuildEntity(String dataType, List<MomgLogDevice> logList, List<String> productIdList) {
        List<JSONObject> devInfoList = new ArrayList<>();
        if (StringUtils.isEmpty(dataType)) {
            devInfoList = logList.stream().filter(log -> !StringUtils.isEmpty(log.getContent())).map(content -> JSONObject.parseObject(content.getContent()).getJSONObject("headers").getJSONObject("deviceInfo")).collect(Collectors.toList());
        } else {
            devInfoList = logList.stream().filter(log -> !StringUtils.isEmpty(log.getContent())).map(content -> JSONObject.parseObject(content.getContent()).getJSONObject("headers")).collect(Collectors.toList());
        }
        devInfoList = devInfoList.stream().filter(jsonObject -> !StringUtils.isEmpty(jsonObject.getString("productId")) && productIdList.contains(jsonObject.getString("productId"))).collect(Collectors.toList());
        //根据device_code字段去重
        //devInfoList = devInfoList.stream().filter(distinctByKey(jsonObject -> jsonObject.getString("deviceCode"))).collect(Collectors.toList());
        List<String> devIdList = devInfoList.stream().map(jsonObject -> jsonObject.getString("id")).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(devIdList)) {
            List<DeviceConnectInfo> connectInfoList = deviceConnectInfoMapper.selectList(new QueryWrapper<DeviceConnectInfo>().in("device_id", devIdList));
            Map<String, List<DeviceConnectInfo>> deviceConnectInfoMap = connectInfoList.stream().collect(Collectors.groupingBy(DeviceConnectInfo::getDeviceId));

            for (JSONObject dev : devInfoList) {
                System.out.println(dev.toJSONString());
                code = dev.getString("deviceCode");
                name = dev.getString("name");
                if (!CollectionUtils.isEmpty(deviceConnectInfoMap)) {
                    List<DeviceConnectInfo> value = deviceConnectInfoMap.get(dev.getString("id"));
                    if (!CollectionUtils.isEmpty(value)) {
                        Map<String, String> connectMap = value.stream().collect(Collectors.toMap(DeviceConnectInfo::getConnectCode, DeviceConnectInfo::getConnectValue));
                        ip = StringUtils.isEmpty(connectMap.get(connectCodeList.get(0))) ? "--" : connectMap.get(connectCodeList.get(0));
                        model = StringUtils.isEmpty(connectMap.get(connectCodeList.get(1))) ? "--" : connectMap.get(connectCodeList.get(1));
                        brand = StringUtils.isEmpty(connectMap.get(connectCodeList.get(2))) ? "--" : connectMap.get(connectCodeList.get(2));
                        netLevel = StringUtils.isEmpty(connectMap.get(connectCodeList.get(3))) ? "--" : connectMap.get(connectCodeList.get(3));
                    }
                } else {
                    //读取到删除的设备，根据其device_code，在国办平台中读取其特殊字段并封装实体类
                    log.error("读取到删除的设备，根据其device_code，开始在国办平台中读取其特殊字段并封装实体类！");
                }

                System.out.println("code：" + code);
                System.out.println("name：" + name);
                System.out.println("ip：" + ip);
                System.out.println("model：" + model);
                System.out.println("brand：" + brand);
                System.out.println("netLevel：" + netLevel);
                System.out.println("/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*");
            }
        }

    }


    public static void main(String[] args) {
        class Car {
            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getModel() {
                return model;
            }

            public void setModel(String model) {
                this.model = model;
            }

            public String getCreateTime() {
                return createTime;
            }

            public void setCreateTime(String createTime) {
                this.createTime = createTime;
            }

            public Car(String name, String model, String createTime) {
                this.name = name;
                this.model = model;
                this.createTime = createTime;
            }

            private String name;
            private String model;
            private String createTime;
        }

        List<Car> list = new ArrayList<>();
        list.add(new Car("aaa", "111", "2022-08-08 15:01:33"));
        list.add(new Car("bbb", "222", "2022-08-08 14:01:33"));
        list.add(new Car("ccc", "333", "2022-08-08 13:01:33"));

        Car car = list.stream().max(Comparator.comparing(Car::getCreateTime)).get();
        System.out.println(car.getCreateTime());
    }

}
