package com.yuanqiao.insight.monitoring.modules.alarm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName("momg_alarm_history")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="MomgAlarmHistory对象", description="告警历史")
public class MomgAlarmHistory {
    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;

    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;

    /**部门*/
    @ApiModelProperty(value = "部门")
    private String sysOrgCode;


    /**设备ID*/
    @ApiModelProperty(value = "设备ID")
    private String deviceId;

    /**告警规则*/
    @ApiModelProperty(value = "告警规则")
    private String alarmRuleId;

    /**告警级别*/
    @ApiModelProperty(value = "告警级别")
    private Integer alarmLevel;

    /**告警状态*/
    @ApiModelProperty(value = "告警状态")
    private Integer alarmStatus;

    /**告警ip*/
    @ApiModelProperty(value = "告警ip")
    private String alarmIp;

    /**重复次数*/
    @ApiModelProperty(value = "重复次数")
    private Integer repeatTimes;

    /**确认状态*/
    @ApiModelProperty(value = "确认状态")
    private Integer confirmStatus;

   /**确认ren*/
    @ApiModelProperty(value = "确认ren")
    private String confirmBy;

    /**确认日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date confirmTime;

}
