package com.yuanqiao.insight.monitoring.modules.flow.vo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.yuanqiao.insight.common.util.snmp.SNMPUtils;
import com.yuanqiao.insight.service.flow.core.util.DBUtils;
import com.yuanqiao.insight.service.flow.core.util.JmxUtils;
import com.yuanqiao.insight.service.flow.core.util.RESTUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2022/11/22
 */
@Getter
@Setter
public class ChainParamVo {

    @ApiModelProperty(value = "流程唯一标识")
    private String chainId;
    @ApiModelProperty(value = "流程参数")
    private String chainParam;
    @ApiModelProperty(value = "流程链条")
    private String chain;
    @ApiModelProperty(value = "协议")
    private String protocol;
    @ApiModelProperty(value = "产品id")
    private String productId;
    @ApiModelProperty(value = "连接参数")
    private String connectParam;

    @JSONField(serialize = false)
    public Object getConnect() {
        JSONObject connectParam = JSON.parseObject(this.connectParam);
        Object connect = null;
        switch (protocol.toLowerCase()) {
            case "tcp":
            case "udp_pull":
                break;
            case "snmp":
                connect = SNMPUtils.initSNMPUtils(connectParam);
                break;
            case "zgcloudapi":
                break;
            case "zghostapi":
                break;
            case "jdbc:dm:":
            case "jdbc:mysql:":
            case "jdbc:kingbase8:":
            case "jdbc:oracle:":
            case "jdbc:oscar:":
                connectParam.put("protocol", protocol);
                connect = DBUtils.iniDBUtils(connectParam);
                break;
            case "jmx":
                connect = JmxUtils.initJmxUtils(connectParam);
                break;
            case "nginx":
                break;
            case "ipmi":
                break;
            case "vmware":
                break;
            case "web:rest":
                connect = new RESTUtil(connectParam);
                break;
            case "spec":
                break;
            default:
        }
        return connect;
    }
}
