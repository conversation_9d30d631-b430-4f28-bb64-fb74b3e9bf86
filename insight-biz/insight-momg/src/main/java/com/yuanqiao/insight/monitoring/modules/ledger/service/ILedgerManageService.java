package com.yuanqiao.insight.monitoring.modules.ledger.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuanqiao.insight.monitoring.modules.ledger.entity.LedgerManage;
import com.yuanqiao.insight.monitoring.modules.ledger.entity.LedgerManageRegionVo;
import com.yuanqiao.insight.monitoring.modules.ledger.entity.LedgerManageVO;
import org.jeecg.common.api.vo.Result;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @Description: 台账资产管理
 * @Author: jeecg-boot
 * @Date:   2020-03-16
 * @Version: V1.0
 */
public interface ILedgerManageService extends IService<LedgerManage> {
    public Result importSheets(MultipartFile file);

    public void exportSheets(HttpServletResponse response, LedgerManage ledgerManageWh, String queryWarrantyStartTimeFrom,
                             String queryWarrantyStartTimeTo,
                             String queryKeywords);

    /**
     * 模板下载
     * @param response
     * @param path     模板地址
     */
    public void downloadTemplate(HttpServletResponse response,String path);

    /**
     * 通过年和地区ID获取各资产状态的数量
     * @param time   年份
     * @param region 地区id
     * @return
     */
    List<LedgerManageVO> getLedgerManageByTimeAndRegion(String time, String region, String addType, String assetType);


    /**
     * 获取指定日期和地区的资产类型总数
     * @param time   年份
     * @param region 地区id
     * @return
     */
    List<LedgerManageVO> getAssetTypeLedgerManageVOByTimeAndRegion(String time, String region, List<String> assetStatus);


    /**
     * 通过资产类型和地区id获取资产信息
     * @param time          时间
     * @param cityProperId  地区id
     * @param assetsType    资产类型
     * @return
     */
    List<LedgerManage> getAssetTypeLedgerManage(String time, String cityProperId, String assetsType, String addType, List<String> assetStatus);


    /**
     * 资产状态和所属地区分组查询资产数量
     * @param time    创建时间
     * @param region  所属地区
     * @param assetType 资产类型
     * @return
     */
    List<LedgerManageRegionVo> getGroupByStatusRegion(String time, String region, String assetType);


    /**
     * 原有 设施替换 资产类型分组查询资产数量
     * @param time   创建时间
     * @param region 所属地区
     * @return
     */
    List<LedgerManageVO> assetsTypeReplaceRate(String time, String region);

    /**
     * 原有 设施替换 资产列表
     * @param time        创建时间
     * @param region      所属地区
     * @param assetsType  资产类型
     * @param addType     添加类型
     * @return
     */
    List<LedgerManage> queryAssetTypeOriginalReplace(String time, String region, String assetsType, String addType);



}
