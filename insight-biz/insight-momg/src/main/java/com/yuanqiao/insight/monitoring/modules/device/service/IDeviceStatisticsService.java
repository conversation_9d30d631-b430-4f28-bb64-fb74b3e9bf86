package com.yuanqiao.insight.monitoring.modules.device.service;

import com.yuanqiao.insight.monitoring.modules.device.entity.NetworkDeviceVO;
import com.yuanqiao.insight.service.device.entity.DeviceInfo;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.text.ParseException;
import java.util.List;

public interface IDeviceStatisticsService {
    List<DeviceInfo> getDevices(String typeCode, DeviceInfo deviceInfo);

    List<NetworkDeviceVO> getNetworkCpuTop10() throws IOException;
    List<NetworkDeviceVO> getNetworkMemTop10() throws IOException;
    List<NetworkDeviceVO> getNetInOutTop() throws IOException;
    List<NetworkDeviceVO> getNetSpeedTop()  throws IOException;

    String exportPdf(String starttime,String endtime) throws IOException, InvocationTargetException, NoSuchMethodException, IllegalAccessException, ParseException;
    List<NetworkDeviceVO> getNetworkDeviceStatistics(String starttime, String endtime) throws IOException, ParseException;
}
