<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.monitoring.modules.serviceProvider.mapper.ServiceProviderDepartMapper">
    <!-- 查询服务商关联部门名称信息 -->
    <select id="getDepNamesByProviderIds" resultType="org.jeecg.modules.system.vo.SysUserDepVo">
        select d.depart_name,d.id as depart_id ,spd.provider_id as user_id
        from momg_service_provider_depart spd,sys_depart d
        where d.id = spd.depart_id and spd.provider_id in
        <foreach collection="providerIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <delete id="deleteProviderDepartByProviderIds">
        delete from momg_service_provider_depart where provider_id in
        <foreach collection="providerIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
