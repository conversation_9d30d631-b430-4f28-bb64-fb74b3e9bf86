<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.monitoring.modules.terminal.mapper.TerminalDeviceMapper">

    <select id="countDevice" resultType="java.lang.Integer">

        select
            count(dev.id)
        from
            momg_device_info dev
                right join momg_terminal ter on
                dev.device_code = ter.unique_code where dev.delflag = 0;

    </select>

    <select id="countOuts" resultType="java.lang.Integer">

        select
            count(dev.id)
        from
            momg_device_info dev
                right join momg_terminal ter on
                dev.device_code = ter.unique_code where dev.delflag = 0 and dev.status = 0 ;




    </select>

    <select id="countOns" resultType="java.lang.Integer">

        select
            count(dev.id)
        from
            momg_device_info dev
                right join momg_terminal ter on
                dev.device_code = ter.unique_code where dev.status != 0 and dev.delflag = 0 and dev.enable = 1

    </select>

    <select id="countDisable" resultType="java.lang.Integer">

        select
            count(dev.id)
        from
            momg_device_info dev
                right join momg_terminal ter on
                dev.device_code = ter.unique_code where dev.delflag = 0;

    </select>

    <!--    <select id="selectWithParent" resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice">-->

    <!--        select * from momg_terminal-->
    <!--        <where>-->

    <!--            <if test=" momgDeptIds != null and momgDeptIds.size() > 0 ">-->
    <!--                and dept_id in-->
    <!--                <foreach collection="momgDeptIds" item="momgDeptId" open="(" separator="," close=")">-->
    <!--                    #{momgDeptId}-->
    <!--                </foreach>-->
    <!--            </if>-->

    <!--            <if test="terminalTypes != null and terminalTypes.size() > 0  ">-->
    <!--                and terminal_type in-->
    <!--                <foreach collection="terminalTypes" item="terminalType" open="(" separator="," close=")">-->
    <!--                    #{terminalType}-->
    <!--                </foreach>-->
    <!--            </if>-->

    <!--            <if test="cpuArch != '' and cpuArch != null">-->
    <!--                and cpu_arch = #{cpuArch}-->
    <!--            </if>-->

    <!--            <if test="name !='' and name != null">-->
    <!--                and name like CONCAT('%',#{name},'%')-->
    <!--            </if>-->

    <!--            <if test="deptId!= null and deptId !=''">-->
    <!--                and dept_id = #{deptId}-->
    <!--            </if>-->
    <!--            <if test="enable != null">-->
    <!--                and enable = #{enable}-->
    <!--            </if>-->

    <!--            and delflag = 0-->

    <!--        </where>-->

    <!--        order by create_time desc-->

    <!--    </select>-->
    <!--没用到？@wxp-->
    <select id="selectWithParent" resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice">

        select * from momg_terminal
        <where>

            <if test=" momgDeptIds != null and momgDeptIds.size() > 0 ">
                and dept_id in
                <foreach collection="momgDeptIds" item="momgDeptId" open="(" separator="," close=")">
                    #{momgDeptId}
                </foreach>
            </if>

            <if test="terminalTypes != null and terminalTypes.size() > 0  ">
                and terminal_type in
                <foreach collection="terminalTypes" item="terminalType" open="(" separator="," close=")">
                    #{terminalType}
                </foreach>
            </if>

            <if test="cpuArch != '' and cpuArch != null">
                and cpu_arch = #{cpuArch}
            </if>

            <if test="name !='' and name != null">
                and name like CONCAT('%',#{name},'%')
            </if>

            <!--            <if test="deptId!= null and deptId !=''">-->
            <!--                and dept_id = #{deptId}-->
            <!--            </if>-->

            <if test="deptIds != null and deptIds.size() > 0  ">
                and dept_id in
                <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="enable != null">
                and enable = #{enable}
            </if>

            and delflag = 0

        </where>

        order by create_time desc

    </select>
    <!--没用到？@wxp-->
    <select id="selectWithParent1" resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice">

        select * from momg_terminal
        <where>

            <if test=" momgDeptIds != null and momgDeptIds.size() > 0 ">
                and dept_id in
                <foreach collection="momgDeptIds" item="momgDeptId" open="(" separator="," close=")">
                    #{momgDeptId}
                </foreach>
            </if>

            <if test="terminalTypes != null and terminalTypes.size() > 0  ">
                and terminal_type in
                <foreach collection="terminalTypes" item="terminalType" open="(" separator="," close=")">
                    #{terminalType}
                </foreach>
            </if>

            <if test="cpuArch != '' and cpuArch != null">
                and cpu_arch = #{cpuArch}
            </if>

            <if test="name !='' and name != null">
                and name like CONCAT('%',#{name},'%')
            </if>

            <!--            <if test="deptId!= null and deptId !=''">-->
            <!--                and dept_id = #{deptId}-->
            <!--            </if>-->

            <if test="deptIds != null and deptIds.size() > 0  ">
                and dept_id in
                <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="enable != null">
                and enable = #{enable}
            </if>

            and delflag = 0

        </where>

        order by create_time desc


    </select>
    <!--终端信息列表查询-->
    <select id="selectTerminal" resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice">

        select t.id,t.create_by,t.create_time,t.update_by,t.update_time,t.sys_org_code,t.assets_id,t.name,
        t.ip,t.description,t.delflag,t.status,t.dept_id,t.position_id,t.position_tag,t.username,t.mac_addr,t.cpu_arch,
        t.cpu_type,t.os_type,t.unique_code,t.phone,t.remarks,t.user_department,t.administrator,t.admin_department,t.address,
        t.SN,t.addr_id,t.addr_detail,t.terminal_type,t.mac_ip,t.button_status,t.user_id,t.cpuname,t.osname,t.platform_code,t.client_ip,
        d.status deviceStatus, n.gateway_type,d.id as deviceId,d.alarm_status as alarmStatus
        from momg_terminal t
        left join momg_device_info d on t.unique_code = d.device_code
        left join momg_group2device g on d.id = g.device_id
        left join momg_network n on d.gateway_code=n.gateway_code
        <where>

            <if test="terminalDevice.terType != null and terminalDevice.terType != '' ">
                and t.terminal_type in
                <foreach collection="terminalDevice.terType.split(',')" item="tp" open="(" separator="," close=")">
                    cast(#{tp} as integer)
                </foreach>
            </if>

            <if test="terminalDevice.cpuArch != '' and terminalDevice.cpuArch != null">
                and t.cpu_arch = #{terminalDevice.cpuArch}
            </if>
            <if test="terminalDevice.id != '' and terminalDevice.id != null">
                and t.id = #{terminalDevice.id}
            </if>

            <if test="terminalDevice.name !='' and terminalDevice.name != null">
                and t.name like CONCAT('%',#{terminalDevice.name},'%')
            </if>

            <if test="terminalDevice.sn !='' and terminalDevice.sn != null">
                and t.SN like CONCAT('%',#{terminalDevice.sn},'%')
            </if>

            <!--            <if test="deptId!= null and deptId !=''">-->
            <!--                and dept_id = #{deptId}-->
            <!--            </if>-->

            <if test="terminalDevice.deptId != null and terminalDevice.deptId != ''">
                and t.dept_id in
                <foreach collection="terminalDevice.deptId.split(',')" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>

            <if test="terminalDevice.macOrIp != null and terminalDevice.macOrIp != ''  ">
                and (
                <foreach collection="terminalDevice.macOrIp.split(',')" item="moi" open="" separator="or" close="">
                    t.mac_addr like CONCAT('%',#{moi},'%') or t.ip like CONCAT('%',#{moi},'%') or t.mac_ip like
                    CONCAT('%',#{moi},'%')
                </foreach>
                )
            </if>
            <!--改为直接查询mac_id字段-->
            <!-- <if test="macOrIps != null and macOrIps !='' ">
                 and t.mac_ip like CONCAT('%',#{macOrIps},'%')
             </if>-->
            <if test="terminalDevice.enable != null">
                and d.enable = #{terminalDevice.enable}
            </if>
            <if test="terminalDevice.username !='' and terminalDevice.username != null">
                and t.username like CONCAT('%',#{terminalDevice.username},'%')
            </if>
            <!--            <if test="terminalDevice.userDepartment !='' and terminalDevice.userDepartment != null">
                            and t.user_department like CONCAT('%',#{terminalDevice.userDepartment},'%')
                        </if>-->
            <if test="terminalDevice.administrator !='' and terminalDevice.administrator != null">
                and t.administrator like CONCAT('%',#{terminalDevice.administrator},'%')
            </if>
            <if test="terminalDevice.adminDepartment !='' and terminalDevice.adminDepartment != null">
                and t.admin_department like CONCAT('%',#{terminalDevice.adminDepartment},'%')
            </if>
            <!--  <if test="status !='' and status != null">
                  and t.status = #{status}
              </if>-->

            <if test="terminalDevice.deviceStatus != null">
                and d.status = #{terminalDevice.deviceStatus}
            </if>

            <if test="terminalDevice.gatewayType !='' and terminalDevice.gatewayType != null">
                and n.gateway_Type = #{terminalDevice.gatewayType}
            </if>

            <if test="terminalDevice.addrId !='' and terminalDevice.addrId != null">
                and t.addr_id = #{terminalDevice.addrId}
            </if>
            <if test="terminalDevice.cpuType != null and terminalDevice.cpuType != '' ">
                and t.cpu_type in
                <foreach collection="terminalDevice.cpuType.split(',')" item="cpu" open="(" separator="," close=")">
                    #{cpu}
                </foreach>
            </if>
            <if test="terminalDevice.osType != null and terminalDevice.osType != '' ">
                and t.os_type in
                <foreach collection="terminalDevice.osType.split(',')" item="os" open="(" separator="," close=")">
                    #{os}
                </foreach>
            </if>

            and t.delflag = 0

            <if test="groupIds != null and groupIds.size() > 0">
                AND (g.group_id IN
                <foreach collection="groupIds" item="groupId" open="(" separator="," close=")">
                    #{groupId}
                </foreach>
                <if test="deptIds == null or deptIds.size() == 0">
                )
                </if>
            </if>

            <if test="deptIds != null and deptIds.size() > 0">
                <choose>
                    <when test="groupIds == null or groupIds.size() == 0">
                        and t.dept_id IN
                        <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                    </when>
                    <otherwise>
                        OR t.dept_id IN
                        <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                        <if test="groupIds != null and groupIds.size() > 0">
                            )
                        </if>
                    </otherwise>
                </choose>
            </if>

        </where>

        order by t.create_time desc


    </select>
    <!--没用到？@wxp-->
    <select id="countWithParent" resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice">

        select * from momg_terminal
        <where>

            <if test='momgDeptIds.size() > 0 and momgDeptIds != null'>
                and dept_id in
                <foreach collection="momgDeptIds" item="momgDeptId" open="(" separator="," close=")">
                    #{momgDeptId}
                </foreach>
            </if>

            <if test="cpuArch != '' and cpuArch != null">
                and cpu_arch = #{cpuArch}
            </if>

            <if test="name !='' and name != null">
                and name like CONCAT('%',#{name},'%')
            </if>

            <if test="deptId!= null and deptId !=''">
                and dept_id = #{deptId}
            </if>

            <if test="enable != null">
                and enable = #{enable}
            </if>

            and delflag = 0

        </where>

    </select>
    <!--更新设备信息-->
    <update id="singleUpdateEnable">
        update
        momg_terminal
        <set>


            <if test="enable!=null">
                enable = #{enable},
            </if>

            <if test="deptId!=null and deptId.trim()!=''">
                dept_id = #{deptId},
            </if>

            <if test="phone!=null and phone.trim()!=''">
                phone = #{phone},
            </if>
            <if test="username!=null and username.trim()!=''">
                username = #{username},
            </if>
            <if test="remarks!=null and remarks.trim()!=''">
                remarks = #{remarks},
            </if>

            <if test="userDepartment!=null and userDepartment.trim()!=''">
                user_department = #{userDepartment},
            </if>

            <if test="administrator!=null and administrator.trim()!=''">
                administrator = #{administrator},
            </if>

            <if test="adminDepartment!=null and adminDepartment.trim()!=''">
                admin_department = #{adminDepartment},
            </if>

            <if test="address!=null and address.trim()!=''">
                address = #{address},
            </if>

            <if test="SN!=null and SN.trim()!=''">
                SN = #{SN},
            </if>

            <if test="addrId!=null">
                addr_id = #{addrId},
            </if>

            <if test="addrDetail!=null and addrDetail.trim()!=''">
                addr_detail = #{addrDetail},
            </if>

        </set>
        <where>
            <if test="terminalId != null and terminalId.trim()!=''">
                and id = #{terminalId}
            </if>

            <if test="uniqueCode != null and uniqueCode.trim()!=''">
                and unique_code = #{uniqueCode}
            </if>

        </where>
    </update>

    <!--更新设备状态-->
    <update id="updateEnable">
        update momg_terminal set enable = #{enable} where id in

        <if test='terminalIds.size() > 0 and terminalIds != null'>

            <foreach collection="terminalIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>

        </if>

    </update>

    <update id="withdraw">

        update
            momg_terminal
        set
            dept_id = null,
            phone = null,
            username = null,
            user_department = null,
            administrator = null,
            admin_department = null,
            address = null,
            addr_id = null,
            addr_detail = null,
            user_id = null,
            os_type = null,
            cpu_type = null
        <where>
            <if test='codeList != null and codeList.size() > 0 '>
                and unique_code in
                <foreach collection="codeList" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
        </where>

    </update>

    <select id="selectTerWithUser"  resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice">

        select  t.id,t.create_by,t.create_time,t.update_by,t.update_time,t.sys_org_code,t.assets_id,t.name,
        t.ip,t.description,t.delflag,t.status,t.dept_id,t.position_id,t.position_tag,t.username,t.mac_addr,t.cpu_arch,
        t.cpu_type,t.os_type,t.unique_code,t.phone,t.remarks,t.user_department,t.administrator,t.admin_department,t.address,
        t.SN,t.addr_id,t.addr_detail,t.terminal_type,t.mac_ip,t.button_status,t.user_id,t.cpuname,t.osname,t.platform_code,t.client_ip,
        u.realname as bindUser, u.phone, tu.create_time as bindTime, case when u.realname is null then '未绑定' else '已绑定' end as bindStatus
        from
            momg_terminal t
                left join
            sys_terminal_user tu
            on
            t.unique_code = tu.unique_code
            left join
            sys_users u
            on
             u.username = tu.user_name
            <where>
                <if test="terminalName != null">
                    and t.name like CONCAT('%',#{terminalName},'%')
                </if>
                <if test="username != null">
                    and u.realname like CONCAT('%',#{username},'%')
                </if>
                <if test="userAccount != null">
                    and u.username = #{userAccount}
                </if>
                <if test="bindFlag != null and bindFlag == 0">
                    and tu.id is null
                </if>
                <if test="bindFlag != null and bindFlag == 1">
                    and tu.id is not null
                </if>
            </where>

    </select>

    <!--没用到？@wxp-->
    <select id="pageSelectTerminalByDeptId"
            resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice">

         select *
         from momg_terminal
         where dept_id = #{deptId}
           and delflag = 0

     </select>
    <!--没用到？@wxp-->
    <select id="pageCountTerminalByDeptId"
            resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice">

         select *
         from momg_terminal
         where dept_id = #{deptId}
           and delflag = 0

     </select>

    <insert id="insertMomgNetwork">
        INSERT INTO momg_network (id, gateway_code, gateway_type)  VALUES (#{id}, #{gatewayCode}, #{gatewayType})
    </insert>

    <!--某一单位关联的终端的分页列表？@wxp-->
    <select id="pageSelectTerminalByDeptIds"
            resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice">

        select mt.* from momg_terminal mt
        left join momg_device_info mdi
        on mdi.device_code = mt.unique_code
        -- on mdi.device_code = mt.name
        <where>
            <if test='deptIds.size() > 0 and deptIds != null'>
                and mt.dept_id in
                <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>

            <if test="gatewayCode != null">
                and gateway_code = #{gatewayCode}
            </if>

            and mt.delflag = 0


        </where>

    </select>

    <select id="pageCountTerminalByDeptIds"
            resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice">

        select mt.* from momg_terminal mt
        left join momg_device_info mdi
        on mdi.device_code = mt.unique_code
        <where>
            <if test='deptIds.size() > 0 and deptIds != null'>
                and mt.dept_id in
                <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>

            <if test="gatewayCode != null">
                and gateway_code = #{gatewayCode}
            </if>

            and mt.delflag = 0


        </where>

    </select>


    <select id="findCity" resultType="com.yuanqiao.insight.acore.system.vo.SysArea">
         select *
         from sys_province_city
         where id = #{id}
     </select>
    <select id="getByName" resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice">
         select *
         from momg_terminal
         where name = #{name}
     </select>
    <select id="getByIp" resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice">
         select *
         from momg_terminal
         where ip = #{ip}
     </select>
    <select id="getByMacAddr" resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice">
         select *
         from momg_terminal
         where mac_addr = #{macAddr}
     </select>
    <select id="getBydept" resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice">
         select *
         from momg_terminal
         where dept_id = #{deptId}
     </select>
    <select id="getTerByMacOrIp"
            resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice">

         SELECT *
         FROM `momg_terminal`
         WHERE `mac_ip` LIKE '%${macOrIp}%'
     </select>
    <select id="getTerName" resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice">
         select *
         from momg_terminal
         where unique_code = #{mac}
     </select>
    <select id="getGatewayName" resultType="java.lang.String">
         SELECT name
         from momg_device_info
         WHERE device_code = (SELECT gateway_code FROM momg_device_info where device_code = #{name})
     </select>
    <select id="getIsStatus" resultType="java.lang.Integer">
         SELECT status
         FROM momg_device_info
         where device_code = #{name}
     </select>

    <select id="pageSelectTerminalByNull"
            resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice">

        select mt.*,mdi.gateway_code
        from momg_terminal mt
        left join momg_device_info mdi
        on mdi.device_code = mt.unique_code
        -- on mdi.device_code = mt.name
        where mt.dept_id is null

        or mt.dept_id = ''

        <if test="gatewayCode != null">
            and mdi.gateway_code = #{gatewayCode}
        </if>

    </select>


    <select id="pageCounttTerminalByNull"
            resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice">

        select mt.*,mdi.gateway_code
        from momg_terminal mt
        left join momg_device_info mdi
        on mdi.device_code = mt.unique_code
        where mt.dept_id is null

        or mt.dept_id = ''

        <if test="gatewayCode != null">
            and mdi.gateway_code = #{gatewayCode}
        </if>

    </select>
    <select id="getTerByCode"
            resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice">
         select *
         from momg_terminal
         where unique_code = #{hostName}
     </select>


    <select id="findDeviceStatistics"
            resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.DeviceStatisVo">
        select
        count(mdi.id) as count,
        dict.item_text as gateway_type
        from
        momg_network mn
        left join momg_device_info mdi on
        mn.gateway_code = mdi.gateway_code
        right join momg_terminal mt on
        mt.unique_code = mdi.device_code
        left join (
        select
        sdi.*
        from
        sys_dict sd
        left join sys_dict_item sdi on
        sd.id = sdi.dict_id
        where
        sd.dict_code = 'terminal_network') dict on
        dict.item_value = mn.gateway_type
        where
        mdi.delflag = 0
        and mdi.enable = 1

        <if test="status != null">
            and mdi.status in (1,2)
        </if>
        group by
        dict.item_text

    </select>


    <select id="getMomgNetwork" resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.NetWork">
         select mn.gateway_type, dict.item_text as gatewayTypeName
         from
         momg_network mn
         left join
         (
             select sdi.*
             from
             sys_dict sd
             left join
             sys_dict_item sdi
             on
             sd.id = sdi.dict_id
             where sd.dict_code = 'terminal_network'
         ) dict
         on
         mn.gateway_type = dict.item_value
         group by
         mn.gateway_type, dict.item_text

     </select>

    <select id="getGateway" resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.NetWork">
         select
             id ,
             gateway_code,
             gateway_type
         from
             momg_network
        where gateway_type = #{gatewayType}
     </select>

    <select id="findNumber" resultType="string">
         select
             gateway_type
         from
             momg_network
         group by
             gateway_type
     </select>


    <select id="getTerminal" resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice">
        select
        mt.id, mt.name, mt.unique_code, mt.enable, mt.dept_id, mt.username, mdi.gateway_code, mdi.status as status
        from
        momg_device_info mdi
        right join momg_terminal mt on
        mdi.device_code = mt.unique_code

        <where>
            mdi.delflag = 0
            <choose>
                <when test="deptId !=  null and deptId != ''">
                    and mt.dept_id = #{deptId}
                </when>
                <otherwise>
                    and ( mt.dept_id = '' or mt.dept_id is null )
                </otherwise>
            </choose>

            <if test='gatewayCodes.size() > 0 and gatewayCodes != null'>
                and mdi.gateway_code in
                <foreach collection="gatewayCodes" item="gatewayCode" open="(" separator="," close=")">
                    #{gatewayCode}
                </foreach>
            </if>
        </where>

    </select>


    <select id="gitDeviceRateFive" resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.DeviceRate">
        select
        md.depart_name as name ,md.id as deptId ,
        count(mt.id) onCount

        from
        momg_terminal mt
        left join sys_depart md on
        mt.dept_id = md.id
        left join momg_device_info mdi on
        mt.unique_code = mdi.device_code
        where
        mdi.delflag = 0
        and mdi.enable = 1
        and mdi.status != 0
        group by
        md.depart_name ,deptId
        order by
        onCount desc

    </select>
    <!--上面的统计效率太低，重新统计@wxp-->
    <select id="getDeviceRateByDepart"  resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.DeviceRate">
        with a as(
            select
                md.depart_name as name ,md.id as deptId ,
                count(case when mdi.status!=0 then 1  end) onCount,
                count(case when mdi.status = 0 then 1  end) offCount,
                count(1)  sumCount
            from
                momg_terminal mt
                    left join sys_depart md on
                    mt.dept_id = md.id
                    left join momg_device_info mdi on
                    mt.unique_code = mdi.device_code
            where
               mdi.delflag = 0
            and md.id is not null
            group by
                md.depart_name, md.id
        )
        select a.* ,a.onCount*100/a.sumCount rate from a
        <where>
         <if test="minNum!=null and minNum !='' and minNum !=0">
         a.sumCount> #{minNum}
         </if>
        </where>
      --  ORDER BY a.onCount/a.sumCount desc
    </select>


    <select id="findCityFive" resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.DeviceRate">

        select spc.TEXT as name, count(mt.id) onCount, spc.id
        from momg_terminal mt
        left join sys_province_city spc on mt.addr_id = spc.ID
        left join momg_device_info  mdi on mt.unique_code = mdi.device_code
        where
        mdi.delflag = 0
        and mdi.enable = 1
        and mdi.status != 0
        and spc.PID = #{pid}
        group by spc.TEXT , spc.id
        order by onCount desc

    </select>


    <select id="findSumCount" resultType="int">

         select count(mt.id) as sumCount
         from momg_terminal mt
                  left join sys_depart md on
             mt.dept_id = md.id
                  left join momg_device_info mdi on
             mt.unique_code = mdi.device_code
         where

           mdi.delflag = 0
           and mdi.enable = 1
           and md.id = #{deptId}



     </select>


    <select id="findSpcCount" resultType="int">

         select
             count(mt.id)
         from
             momg_terminal mt
                 left join sys_province_city spc on
                 mt.addr_id = spc.ID
                 left join momg_device_info mdi on
                 mt.unique_code = mdi.device_code
         where


            mdi.delflag = 0
           and mdi.enable = 1
           and
             spc.ID = #{spcId}

     </select>


    <select id="tenTrend" resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.DeviceRate">
         select create_time, device_totle sumCount,
         on_pass_count + on_not_pass_count  onCount,
         not_on_count off_count
         from momg_dept_statis
         -- where DATE_SUB(CURDATE(), interval 10 day) &lt;= date(create_time)
         where create_time >= #{preDate}
         order by create_time
     </select>

    <select id="getOneCount" resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.DeptVo">
         select
             (
                 select count(mdi.id)
                 from momg_device_info as mdi
                 join momg_terminal as mt on mdi.device_code = mt.unique_code
                 where mt.addr_id = #{id} and mdi.delflag = 0 and mdi.enable = 1 and mdi.status != 0
              ) as onLine ,
             (
                 select count(mdi.id) from momg_device_info as mdi
                 join momg_terminal as mt on mdi.device_code = mt.unique_code
                 where mt.addr_id = #{id} and mdi.delflag = 0 and mdi.enable = 1 and mdi.status = 0
              ) as offLine
         from
             dual
     </select>


    <!-- 查询大屏区县地图展示数据sql计算版本 -->
    <select id="selectDeviceInfo4BigScreenCityMap"
            resultType="java.util.Map">

        select
            count(case when mdi.delflag = 0 and mdi.status != 0 then 1 end) as onLine,
            count(case when mdi.delflag = 0 and mdi.status = 0 then 1 end) as offLine,
            ROUND(count(case when mdi.delflag = 0 and mdi.status != 0 then 1 end) * 100 / count(case when mdi.delflag = 0 then 1 end) , 2) as onRate,
            count(case when mdi.delflag = 0 then 1 end) as AllCount,
            spc.id as cityId, spc.text as cityName
        from momg_device_info mdi
        left join momg_terminal mt on mdi.device_code = mt.unique_code
        left join sys_province_city spc on mt.addr_id = spc.id

        <where>
            <if test='cityIds != null and cityIds.size() > 0'>
                and mt.addr_id in
                <foreach collection="cityIds" item="cityId" open="(" separator="," close=")">
                    #{cityId}
                </foreach>
            </if>
        </where>

        group by spc.text, spc.id

    </select>


    <select id="selectUniqueCode" resultType="java.lang.String">

         select distinct unique_code from momg_terminal

     </select>

    <select id="selectTerminalByDeptId"
            resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice">

        select t.id, t.name, t.unique_code, t.enable, t.dept_id, t.username, t.cpu_arch, t.cpu_type, t.os_type, d.status as status, d.alarm_status as alarmStatus, mn.gateway_type as internetFlag
        from momg_terminal t
        left join
        momg_device_info d
        on
        t.unique_code = d.device_code
        LEFT JOIN momg_network mn on d.gateway_code = mn.gateway_code
        LEFT JOIN (
        select sdi.*
        from
        sys_dict sd
        left join
        sys_dict_item sdi
        on
        sd.id = sdi.dict_id
        where sd.dict_code = 'terminal_network'
        ) dict on mn.gateway_type = dict.item_value
        <where>
            <if test="id != null and id != ''">
                and t.dept_id = #{id}
            </if>
            <if test="id == null">
                and t.dept_id = '' or t.dept_id is null
            </if>
        </where>

    </select>

    <select id="selectTerminalByDeptIds"
            resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice">

        select t.id, t.name, t.unique_code, t.enable, t.dept_id, t.cpu_type, t.os_type, d.status as status, d.alarm_status as alarmStatus
        from
            momg_terminal t
        left join
        momg_device_info d
            on t.unique_code = d.device_code
        <where>
            <if test='deptIds != null and deptIds.size() > 0'>
                and t.dept_id in
                <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectGrafanaUrl" resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalVo">

        select t.id, ti.code as connectCode, i.connect_value as grafanaUrl, ti.default_value as grafanaUrl1
        from
         momg_terminal t
        left join
        momg_device_info d
        on t.unique_code = d.device_code
        left join
        momg_device_connect_info i
        on d.id = i.device_id
        left join
        momg_device_connect_template_info ti
        on d.product_id = ti.product_id
        <where>
            <if test="id != null">
                t.dept_id = #{id}
            </if>
            <if test="id == null">
                t.dept_id is null or t.dept_id = '';
            </if>
        </where>

    </select>

    <select id="selectStatisInfo" resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice">

        select
            ds.on_count, ds.on_total
        from
            momg_terminal t
                left join
            momg_device_info d
            on
                t.unique_code = d.device_code
                left join
            momg_device_statis ds
            on
                d.id = ds.device_id
        where
            t.unique_code = #{uniqueCode}
        and
            ds.create_time = #{date};

    </select>

    <select id="selectStateInfo" resultType="com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice">

        select t.name, t.ip, t.mac_addr, t.os_type, t.cpu_type, t.cpu_arch, t.user_department, t.username, t.phone,
               d.status,
               d.alarm_status,
               s.depart_name as deptName,
               u.realname as usernameText
        from momg_terminal t
                 left join
             momg_device_info d
             on
                 t.unique_code = d.device_code
                 left join
             sys_depart s
             on
                 t.dept_id = s.id
        left join sys_users u
                on t.username = u.username
        where t.unique_code = #{uniqueCode}

    </select>

    <select id="countAlarms" resultType="java.lang.Integer">

        select
            count(dev.id)
        from
            momg_device_info dev
        right join momg_terminal ter on
            dev.device_code = ter.unique_code
        where dev.delflag = 0 and dev.alarm_status = 1;

    </select>

    <select id="getAssetsByTerminal" resultType="com.yuanqiao.insight.cmdb.modules.assets.entity.Assets">

        select a.*
        from
        momg_device_info d
        left join
        momg_device2assets da
        on
        d.id = da.device_id
        left join
        cmdb_assets a
        on
        a.id = da.assets_id
        <where>
            <if test='uniqueCodeList != null and uniqueCodeList.size() > 0'>
                and d.device_code in
                <foreach collection="uniqueCodeList" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
        </where>

    </select>

    <select id="queryInfoName" resultType="com.alibaba.fastjson.JSONObject">
        select t.unique_code as uniqueCode, t.name
        from
        momg_terminal t
        left join
        momg_device_info d
        on t.unique_code = d.device_code
        <where>
            <if test="terminalDevice.deptId != null and terminalDevice.deptId != ''">
                and t.dept_id in
                <foreach collection="terminalDevice.deptId.split(',')" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="terminalDevice.osType != null and terminalDevice.osType != '' ">
                and t.os_type in
                <foreach collection="terminalDevice.osType.split(',')" item="os" open="(" separator="," close=")">
                    #{os}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
