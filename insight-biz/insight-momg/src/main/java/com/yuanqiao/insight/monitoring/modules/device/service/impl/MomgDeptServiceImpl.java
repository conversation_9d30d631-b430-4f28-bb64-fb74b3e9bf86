package com.yuanqiao.insight.monitoring.modules.device.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.cmdb.modules.assetscategory.util.ExcelUtil;
import com.yuanqiao.insight.monitoring.modules.device.entity.MomgDept;
import com.yuanqiao.insight.monitoring.modules.device.mapper.MomgDeptMapper;
import com.yuanqiao.insight.monitoring.modules.device.service.IMomgDeptService;
import com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDeptVo;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.system.entity.SysCategory;
import com.yuanqiao.insight.acore.system.service.ISysDictService;
import com.yuanqiao.insight.acore.system.vo.SysArea;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description: 终端组织机构
 * @Author: jeecg-boot
 * @Date:   2021-03-31
 * @Version: V1.0
 */
@Service
public class MomgDeptServiceImpl extends ServiceImpl<MomgDeptMapper, MomgDept> implements IMomgDeptService {
    @Autowired
    private IMomgDeptService iMomgDeptService;
    @Autowired
    private ISysDictService sysDictService;

//	@Override
//	public void addMomgDept(MomgDept momgDept) {
//		if(oConvertUtils.isEmpty(momgDept.getParentId())){
//			momgDept.setParentId(IMomgDeptService.ROOT_PID_VALUE);
//		}else{
//			//如果当前节点父ID不为空 则设置父节点的hasChildren 为1
//			MomgDept parent = baseMapper.selectById(momgDept.getParentId());
//			if(parent!=null && !"1".equals(parent.getHasChild())){
//				parent.setHasChild("1");
//				baseMapper.updateById(parent);
//			}
//		}
//		baseMapper.insert(momgDept);
//	}

//	@Override
//	public void updateMomgDept(MomgDept momgDept) {
//		MomgDept entity = this.getById(momgDept.getId());
//		if(entity==null) {
//			throw new JeecgBootException("未找到对应实体");
//		}
//		String old_pid = entity.getParentId();
//		String new_pid = momgDept.getParentId();
//		if(!old_pid.equals(new_pid)) {
//			updateOldParentNode(old_pid);
//			if(oConvertUtils.isEmpty(new_pid)){
//				momgDept.setParentId(IMomgDeptService.ROOT_PID_VALUE);
//			}
//			if(!IMomgDeptService.ROOT_PID_VALUE.equals(momgDept.getParentId())) {
//				baseMapper.updateTreeNodeStatus(momgDept.getParentId(), IMomgDeptService.HASCHILD);
//			}
//		}
//		baseMapper.updateById(momgDept);
//	}

//	@Override
//	@Transactional(rollbackFor = Exception.class)
//	public void deleteMomgDept(String id) throws JeecgBootException {
//		//查询选中节点下所有子节点一并删除
//        id = this.queryTreeChildIds(id);
//        if(id.indexOf(",")>0) {
//            StringBuffer sb = new StringBuffer();
//            String[] idArr = id.split(",");
//            for (String idVal : idArr) {
//                if(idVal != null){
//                    MomgDept momgDept = this.getById(idVal);
//                    String pidVal = momgDept.getParentId();
//                    //查询此节点上一级是否还有其他子节点
//                    List<MomgDept> dataList = baseMapper.selectList(new QueryWrapper<MomgDept>().eq("pid", pidVal).notIn("id",Arrays.asList(idArr)));
//                    if((dataList == null || dataList.size()==0) && !Arrays.asList(idArr).contains(pidVal)
//                            && !sb.toString().contains(pidVal)){
//                        //如果当前节点原本有子节点 现在木有了，更新状态
//                        sb.append(pidVal).append(",");
//                    }
//                }
//            }
//            //批量删除节点
//            baseMapper.deleteBatchIds(Arrays.asList(idArr));
//            //修改已无子节点的标识
//            String[] pidArr = sb.toString().split(",");
//            for(String pid : pidArr){
//                this.updateOldParentNode(pid);
//            }
//        }else{
//            MomgDept momgDept = this.getById(id);
//            if(momgDept==null) {
//                throw new JeecgBootException("未找到对应实体");
//            }
//            updateOldParentNode(momgDept.getParentId());
//            baseMapper.deleteById(id);
//        }
//	}

//	@Override
//    public List<MomgDept> queryTreeListNoPage(QueryWrapper<MomgDept> queryWrapper) {
//        List<MomgDept> dataList = baseMapper.selectList(queryWrapper);
//        List<MomgDept> mapList = new ArrayList<>();
//        for(MomgDept data : dataList){
//            String pidVal = data.getParentId();
//            //递归查询子节点的根节点
//            if(pidVal != null && !"0".equals(pidVal)){
//                MomgDept rootVal = this.getTreeRoot(pidVal);
//                if(rootVal != null && !mapList.contains(rootVal)){
//                    mapList.add(rootVal);
//                }
//            }else{
//                if(!mapList.contains(data)){
//                    mapList.add(data);
//                }
//            }
//        }
//        return mapList;
//    }
//
//    @Override
//    public List<String> getDeptListByRootId(String momgDeptId) {
//        List<String> resultList=new ArrayList<>();
//        List<MomgDept> momgDeptList=this.list();
//        resultList.add(momgDeptId);
//	    getTreeChildIds(momgDeptId,resultList,momgDeptList);
//	    return resultList;
//    }
//
//    @Override
//    public List<MomgDept> selectAllDepts() {
//        return baseMapper.selectList(new QueryWrapper<>());
//    }

    @Override
    public Result importSheets(MultipartFile file) {
        try {
            Workbook hssfWorkbook = ExcelUtil.getWorkBook(file);
            ImportParams params = new ImportParams();
            // 循环工作表Sheet
            SysCategory sysCategory = new SysCategory();
            int count = 2;
            for (int numSheet = 0; numSheet < hssfWorkbook.getNumberOfSheets(); numSheet++) {
                params.setTitleRows(2);
                params.setHeadRows(1);
                params.setNeedSave(true);
                List<MomgDept> list = ExcelImportUtil.importExcel(file.getInputStream(), MomgDept.class, params);
                if (list.size()==0){
                    return Result.error("文件不能为空" );
                }
                for (MomgDept momgDept : list) {
                    count+=1;
                    if (StringUtils.isEmpty(momgDept.getParentId())){
                        if (StringUtils.isEmpty(momgDept.getDepartName())||StringUtils.isEmpty(momgDept.getAddress())||StringUtils.isEmpty(momgDept.getMobile())||StringUtils.isEmpty(momgDept.getCityName())) {
                            return Result.error("文件导入失败,错误存在第 "+count+" 行!");
                        }

                        MomgDept name = this.getOne(new QueryWrapper<MomgDept>().eq("name", momgDept.getDepartName().trim()));
                        if (null != name){
                            return Result.error("文件导入失败,错误存在第 "+count+" 行,单位名称重复!");
                        }

                        if (!isMoPh(momgDept.getMobile())){
                            return Result.error("文件导入失败,错误存在第 "+count+" 行,电话格式错误!");
                        }
                        SysArea sysArea = sysDictService.queryAreaByText(momgDept.getCityName());

                        if (sysArea == null){
                            return Result.error("文件导入失败,错误存在第 "+count+" 行,地区错误!");
                        }else {
                            momgDept.setCityId(sysArea.getId());
                        }
                        momgDept.setParentId("0000000001");
//                        momgDept.setHasChild("1");
                        super.save(momgDept);
                    }else {
                        if (StringUtils.isEmpty(momgDept.getDepartName())||StringUtils.isEmpty(momgDept.getAddress())||StringUtils.isEmpty(momgDept.getMobile())||StringUtils.isEmpty(momgDept.getCityName())) {
                            return Result.error("文件导入失败,错误存在第 "+count+" 行!");
                        }
                        if (!isMoPh(momgDept.getMobile())){
                            return Result.error("文件导入失败,错误存在第 "+count+" 行,电话格式错误!");
                        }

                        QueryWrapper<MomgDept> queryWrapper = new QueryWrapper<>();
                        queryWrapper.eq("name",momgDept.getParentId());
                        MomgDept momgDept1 = this.baseMapper.selectOne(queryWrapper);
                        if(momgDept1 == null){
                            return Result.error("文件导入失败,错误存在第 "+count+" 行,不存在的父级节点!");
                        }
                        momgDept.setParentId(momgDept1.getId());
                        super.save(momgDept);
                    }

                }
            }
            return Result.ok("文件导入成功！");
        } catch (IOException e) {
            e.printStackTrace();
            return Result.error("文件导入失败:" + e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                file.getInputStream().close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return Result.error("文件导入失败!");
    }

//    @Override
//    public List<MomgDept> getCityId(String id) {
//        return baseMapper.getCityId(id);
//    }

    @Override
    public Integer getAll(List<String> types) {
        return baseMapper.getAll(types);
    }

//    @Override
//    public Integer getCityOnCount(String macAddr,List<String> types) {
//        return baseMapper.getCityOnCount(macAddr,types);
//    }

    @Override
    public Integer getOn(List<String> types) {
        return baseMapper.getOn(types);
    }

    @Override
    public TerminalDeptVo getDeptId(String id, List<String> types) {
        return baseMapper.getDeptId(id, types);
    }

//    @Override
//    public Integer getDeptIdOff(String id, List<String> types) {
//        return baseMapper.getDeptIdOff(id, types);
//    }

    /**
	 * 根据所传pid查询旧的父级节点的子节点并修改相应状态值
	 * @param pid
	 */
	private void updateOldParentNode(String pid) {
		if(!IMomgDeptService.ROOT_PID_VALUE.equals(pid)) {
			Integer count = baseMapper.selectCount(new QueryWrapper<MomgDept>().eq("pid", pid));
			if(count==null || count<=1) {
				baseMapper.updateTreeNodeStatus(pid, IMomgDeptService.NOCHILD);
			}
		}
	}

	/**
     * 递归查询节点的根节点
     * @param pidVal
     * @return
     */
    private MomgDept getTreeRoot(String pidVal){
        MomgDept data =  baseMapper.selectById(pidVal);
        if(data != null && !"0".equals(data.getParentId())){
            return this.getTreeRoot(data.getParentId());
        }else{
            return data;
        }
    }

    /**
     * 根据id查询所有子节点id
     * @param ids
     * @return
     */
    private String queryTreeChildIds(String ids) {
        //获取id数组
        String[] idArr = ids.split(",");
        StringBuffer sb = new StringBuffer();
        for (String pidVal : idArr) {
            if(pidVal != null){
                if(!sb.toString().contains(pidVal)){
                    if(sb.toString().length() > 0){
                        sb.append(",");
                    }
                    sb.append(pidVal);
                    this.getTreeChildIds(pidVal,sb);
                }
            }
        }
        return sb.toString();
    }

    /**
     * 递归查询所有子节点
     * @param pidVal
     * @param sb
     * @return
     */
    private StringBuffer getTreeChildIds(String pidVal,StringBuffer sb){
        List<MomgDept> dataList = baseMapper.selectList(new QueryWrapper<MomgDept>().eq("pid", pidVal));
        if(dataList != null && dataList.size()>0){
            for(MomgDept tree : dataList) {
                if(!sb.toString().contains(tree.getId())){
                    sb.append(",").append(tree.getId());
                }
                this.getTreeChildIds(tree.getId(),sb);
            }
        }
        return sb;
    }


    /**
     * 递归查询所有子节点
     * @param pidVal
     * @param result
     * @param dataList
     * @return
     */
    private void getTreeChildIds(String pidVal,List<String> result,List<MomgDept> dataList){
            for(MomgDept momgDept:dataList){
                if(pidVal.equals(momgDept.getParentId())){
                    result.add(momgDept.getId());
                    getTreeChildIds(momgDept.getId(),result,dataList);
                }
            }
    }


    /**
     * 电话手机验证
     * @param str
     * @return
     */
    public static boolean isMoPh(String str){
        Boolean b = false;
        if (isMobile(str)){
            b= true;
        }
        if (isPhone(str)){
            b= true;
        }

        return b;

    }



    /**
     * 手机号验证
     * @param str
     * @return 验证通过返回true
     */
    public static boolean isMobile( String str) {
        Pattern p = null;
        Matcher m = null;
        boolean b = false;
        String regex = "^((13[0-9])|(14[5|7])|(15([0-3]|[5-9]))|(17[013678])|(18[0,5-9]))\\d{8}$";
        p = Pattern.compile(regex); // 验证手机号
        m = p.matcher(str);
        b = m.matches();
        return b;
    }
    /**
     * 电话号码验证
     * @param str
     * @return 验证通过返回true
     */
    public static boolean isPhone( String str) {
        Pattern p1 = null, p2 = null;
        Matcher m = null;
        boolean b = false;
        p1 = Pattern.compile("^[0][1-9]{2,3}-[0-9]{5,10}$"); // 验证带区号的
        p2 = Pattern.compile("^[1-9]{1}[0-9]{5,8}$");     // 验证没有区号的
        if (str.length() > 9) {
            m = p1.matcher(str);
            b = m.matches();
        } else {
            m = p2.matcher(str);
            b = m.matches();
        }
        return b;
    }



}
