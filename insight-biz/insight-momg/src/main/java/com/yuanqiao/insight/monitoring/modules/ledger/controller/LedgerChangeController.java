package com.yuanqiao.insight.monitoring.modules.ledger.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.monitoring.modules.ledger.entity.LedgerChange;
import com.yuanqiao.insight.monitoring.modules.ledger.service.ILedgerChangeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: 台账资产变更记录
 * @Author: jeecg-boot
 * @Date:   2020-03-18
 * @Version: V1.0
 */
@Slf4j
@Api(tags="台账资产变更记录")
@RestController
@RequestMapping("/ledger/ledgerChange")
public class LedgerChangeController extends JeecgController<LedgerChange, ILedgerChangeService> {
	@Autowired
	private ILedgerChangeService ledgerChangeService;

	/**
	 * 分页列表查询
	 *
	 * @param ledgerChange
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "台账资产变更记录-分页列表查询")
	@ApiOperation(value="台账资产变更记录-分页列表查询", notes="台账资产变更记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(LedgerChange ledgerChange,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<LedgerChange> queryWrapper = QueryGenerator.initQueryWrapper(ledgerChange, req.getParameterMap());
		Page<LedgerChange> page = new Page<LedgerChange>(pageNo, pageSize);
		IPage<LedgerChange> pageList = ledgerChangeService.page(page, queryWrapper);
		return Result.ok(pageList);
	}

	 /**
	  * 根据资产编号获取变更信息
	  *
	  * @param assetNo
	  * @return
	  */
	 @AutoLog(value = "台账资产变更记录-根据资产编号获取变更信息")
	 @ApiOperation(value="台账资产变更记录-根据资产编号获取变更信息", notes="台账资产变更记录-根据资产编号获取变更信息")
	 @GetMapping(value = "/ledgerInfo")
	 public Result<?> getLedgerInfo(@RequestParam(name="assetNo") String assetNo){
	 	LedgerChange ledgerChange = new LedgerChange();
	 	ledgerChange.setAssetNo(assetNo);
	 	LedgerChange ledgerInfo = ledgerChangeService.getOne(new QueryWrapper<>(ledgerChange));
	 	return Result.ok(ledgerInfo);
	 }

	/**
	 * 添加
	 *
	 * @param ledgerChange
	 * @return
	 */
	@AutoLog(value = "台账资产变更记录-添加")
	@ApiOperation(value="台账资产变更记录-添加", notes="台账资产变更记录-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody LedgerChange ledgerChange) {
		ledgerChangeService.save(ledgerChange);
		return Result.ok("添加成功！");
	}

	/**
	 * 编辑
	 *
	 * @param ledgerChange
	 * @return
	 */
	@AutoLog(value = "台账资产变更记录-编辑")
	@ApiOperation(value="台账资产变更记录-编辑", notes="台账资产变更记录-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody LedgerChange ledgerChange) {
		ledgerChangeService.updateById(ledgerChange);
		return Result.ok("编辑成功!");
	}

	/**
	 * 通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "台账资产变更记录-通过id删除")
	@ApiOperation(value="台账资产变更记录-通过id删除", notes="台账资产变更记录-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		ledgerChangeService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 * 批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "台账资产变更记录-批量删除")
	@ApiOperation(value="台账资产变更记录-批量删除", notes="台账资产变更记录-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.ledgerChangeService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功！");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "台账资产变更记录-通过id查询")
	@ApiOperation(value="台账资产变更记录-通过id查询", notes="台账资产变更记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		LedgerChange ledgerChange = ledgerChangeService.getById(id);
		return Result.ok(ledgerChange);
	}

  /**
   * 导出excel
   *
   * @param request
   * @param ledgerChange
   */
  @RequestMapping(value = "/exportXls")
  public ModelAndView exportXls(HttpServletRequest request, LedgerChange ledgerChange) {
      return super.exportXls(request, ledgerChange, LedgerChange.class, "台账资产变更记录");
  }

  /**
   * 通过excel导入数据
   *
   * @param request
   * @param response
   * @return
   */
  @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
  public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      return super.importExcel(request, response, LedgerChange.class);
  }

}
