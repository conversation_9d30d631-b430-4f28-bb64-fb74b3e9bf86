package com.yuanqiao.insight.monitoring.modules.ledger.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanqiao.insight.monitoring.modules.ledger.entity.LedgerChange;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * @Description: 台账资产变更记录
 * @Author: jeecg-boot
 * @Date:   2020-03-18
 * @Version: V1.0
 */
@Component
public interface LedgerChangeMapper extends BaseMapper<LedgerChange> {

    String findName (@Param("id")String id );
}
