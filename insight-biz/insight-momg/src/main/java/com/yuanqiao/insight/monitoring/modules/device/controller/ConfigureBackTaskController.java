package com.yuanqiao.insight.monitoring.modules.device.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.cmdb.modules.assetscategory.service.IAssetsCategoryService;
import com.yuanqiao.insight.common.util.common.LocalCacheUtils;
import com.yuanqiao.insight.monitoring.modules.topo.entity.TopoInfo;
import com.yuanqiao.insight.monitoring.modules.topo.service.ITopoInfoService;
import com.yuanqiao.insight.service.device.entity.DeviceInfo;
import com.yuanqiao.insight.service.device.entity.MomgConfigureBackDevice;
import com.yuanqiao.insight.service.device.entity.MomgConfigureBackTask;
import com.yuanqiao.insight.service.device.entity.MomgDeviceConfigureRecord;
import com.yuanqiao.insight.service.device.mapper.DeviceInfoMapper;
import com.yuanqiao.insight.service.device.service.IMomgConfigureBackDeviceService;
import com.yuanqiao.insight.service.device.service.IMomgConfigureBackTaskService;
import com.yuanqiao.insight.service.device.service.IMomgDeviceConfigureRecordService;
import com.yuanqiao.insight.service.product.entity.Product;
import com.yuanqiao.insight.service.product.service.IProductService;
import com.yuanqiao.insight.monitoring.modules.product.service.IProductTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 网络设备-配置备份任务
 * @Author: jeecg-boot
 * @Date: 2021-03-21
 * @Version: V1.0
 */
@Api(tags = "网络设备-配置备份任务")
@RestController
@RequestMapping("/configureBack/task")
@Slf4j
public class ConfigureBackTaskController extends JeecgController<TopoInfo, ITopoInfoService> {

    @Autowired
    DeviceInfoMapper deviceInfoMapper;
    @Autowired
    private IMomgConfigureBackTaskService momgConfigureBackTaskService;
    @Autowired
    private IMomgConfigureBackDeviceService momgConfigureBackDeviceService;
    @Autowired
    private IMomgDeviceConfigureRecordService momgDeviceConfigureRecordService;
    @Autowired
    private IProductService productService;
    @Autowired
    private IProductTypeService productTypeService;
    @Autowired
    private IAssetsCategoryService categoryService;


    LocalCacheUtils cacheUtils = LocalCacheUtils.getInstance();


    @AutoLog(value = "配置备份任务列表 -- 网络设备")
    @ApiOperation(value = "配置备份任务列表 -- 网络设备", notes = "配置备份任务列表 -- 网络设备")
    @GetMapping(value = "/list")
    public Result<?> list(MomgConfigureBackTask momgConfigureBackTask,
                          @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                          @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {

        Page<MomgConfigureBackTask> page = new Page<>(pageNo, pageSize);
        QueryWrapper<MomgConfigureBackTask> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotEmpty(momgConfigureBackTask.getTaskName())) {
            queryWrapper.like("task_name", momgConfigureBackTask.getTaskName());
        }
        IPage<MomgConfigureBackTask> pageList = momgConfigureBackTaskService.page(page, queryWrapper);

        if (pageNo == 1 && pageSize == -1) { // 查询所有任务（下拉框数据源）
            MomgConfigureBackTask configureBackTask = new MomgConfigureBackTask();
            configureBackTask.setTaskName("手动执行");
            pageList.getRecords().add(0, configureBackTask);
            pageList.setTotal(pageList.getRecords().size());
        }

        return Result.OK(pageList);

    }


    @AutoLog(value = "配置备份任务-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody MomgConfigureBackTask momgConfigureBackTask) {
        try {
            List<MomgConfigureBackTask> list = momgConfigureBackTaskService.list(new QueryWrapper<MomgConfigureBackTask>().eq("task_name", momgConfigureBackTask.getTaskName()));
            if (CollUtil.isNotEmpty(list)) {
                return Result.error("任务名称重复！");
            }
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (loginUser != null) {
                momgConfigureBackTask.setCreateBy(loginUser.getRealname());
            }

            momgConfigureBackTaskService.save(momgConfigureBackTask);

            // 绑定设备
            if (StringUtils.isNotEmpty(momgConfigureBackTask.getDeviceIds())) {
                String[] split = momgConfigureBackTask.getDeviceIds().split(",");
                List<MomgConfigureBackDevice> configureBackDeviceList = new ArrayList<>();
                for (String deviceId : split) {
                    MomgConfigureBackDevice configureBackDevice = new MomgConfigureBackDevice();
                    configureBackDevice.setDeviceId(deviceId);
                    configureBackDevice.setTaskId(momgConfigureBackTask.getId());
                    configureBackDeviceList.add(configureBackDevice);
                }
                momgConfigureBackDeviceService.saveBatch(configureBackDeviceList);
            }

            if (momgConfigureBackTask.getTaskStatus().equals("1")) {
                // 添加并开启任务
                momgConfigureBackTask.setDeviceArray(momgConfigureBackTaskService.buildDeviceCommandArray(momgConfigureBackTask.getId()));
                momgConfigureBackTaskService.addTask(momgConfigureBackTask);
            }
            return Result.OK("添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("添加失败！");
        }
    }


    @AutoLog(value = "配置备份任务-查询所有网关设备")
    @ApiOperation(value = "配置备份任务-查询所有网关设备", notes = "配置备份任务-查询所有网关设备")
    @GetMapping(value = "/getGatewayList")
    public Result<?> getGatewayList() {
        List<DeviceInfo> gatewayDeviceList = deviceInfoMapper.selectAllGatewayDevices(new ArrayList<>());
        return Result.OK(gatewayDeviceList);
    }


    @AutoLog(value = "配置备份任务 -- 待绑定的网络设备")
    @ApiOperation(value = "配置备份任务 -- 待绑定的网络设备", notes = "配置备份任务 -- 待绑定的网络设备")
    @GetMapping(value = "/getResourceList")
    public Result<?> getResourceList(@RequestParam(name = "dictCode", required = true) String dictCode,
                                     String productId,
                                     String ip,
                                     String gatewayCode,
                                     @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                     @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {

        Page<DeviceInfo> page = new Page<>(pageNo, pageSize);

        List<String> productIdList = new ArrayList<>();
        if (StringUtils.isNotEmpty(productId)) {
            productIdList.add(productId);
        } else {
            List<Product> productList = productTypeService.selectProductListByTypeCode(dictCode);
            productIdList = productList.stream().map(Product::getId).collect(Collectors.toList());
        }

        List<String> alreadyDeviceIdList = new ArrayList<>();
        List<MomgConfigureBackDevice> alreadyDeviceList = momgConfigureBackDeviceService.list();
        if (CollUtil.isNotEmpty(alreadyDeviceList)) {
            alreadyDeviceIdList = alreadyDeviceList.stream().map(MomgConfigureBackDevice::getDeviceId).collect(Collectors.toList());
        }

        IPage<DeviceInfo> iPage = deviceInfoMapper.selectDevInfoWithProductPage(page, ip, gatewayCode, productIdList, null, alreadyDeviceIdList);
        return Result.OK(iPage);
    }


    @AutoLog(value = "配置备份任务-绑定设备")
    @PostMapping(value = "/bindDevice")
    public Result<?> bindDevice(@RequestBody MomgConfigureBackTask momgConfigureBackTask) {
        try {
            if (StringUtils.isNotEmpty(momgConfigureBackTask.getId()) && StringUtils.isNotEmpty(momgConfigureBackTask.getDeviceIds())) {
                String[] list = momgConfigureBackTask.getDeviceIds().split(",");
                List<MomgConfigureBackDevice> configureBackDeviceList = new ArrayList<>();
                for (String deviceId : list) {
                    MomgConfigureBackDevice configureBackDevice = new MomgConfigureBackDevice();
                    configureBackDevice.setDeviceId(deviceId);
                    configureBackDevice.setTaskId(momgConfigureBackTask.getId());
                    configureBackDeviceList.add(configureBackDevice);
                }
                momgConfigureBackDeviceService.saveBatch(configureBackDeviceList);
            }
            return Result.OK("绑定成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("绑定失败！");
        }
    }

    @AutoLog(value = "配置备份任务-解绑设备")
    @DeleteMapping(value = "/unbindDevice")
    public Result<?> unbindDevice(String taskId, String deviceId) {
        try {
            momgConfigureBackDeviceService.remove(new QueryWrapper<MomgConfigureBackDevice>().eq("task_id", taskId).eq("device_id", deviceId));
            return Result.OK("解绑成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("解绑失败！");
        }
    }

    @AutoLog(value = "配置备份任务-获取绑定的设备")
    @GetMapping(value = "/getDevice")
    public Result<?> getDevice(String taskId,
                               @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                               @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        IPage<DeviceInfo> deviceInfoPage = new Page<>();
        try {
            List<MomgConfigureBackDevice> configureBackDeviceList = momgConfigureBackDeviceService.list(new QueryWrapper<MomgConfigureBackDevice>().eq("task_id", taskId));
            if (CollUtil.isNotEmpty(configureBackDeviceList)) {
                deviceInfoPage = deviceInfoMapper.selectDevInfoWithProductPage(new Page<>(pageNo, pageSize), null, null, null, configureBackDeviceList.stream().map(MomgConfigureBackDevice::getDeviceId).collect(Collectors.toList()), null);
            } else {
                log.error("未获取到当前配置备份任务所绑定的设备！");
            }
            return Result.OK(deviceInfoPage);
        } catch (Exception e) {
            log.error("获取当前配置备份任务所绑定的设备异常！", e);
            return Result.error("获取当前配置备份任务所绑定的设备异常！");
        }
    }


    @AutoLog(value = "配置备份任务-编辑")
    @PutMapping(value = "/edit")
    public Result<?> productConfigureManageEdit(@RequestBody MomgConfigureBackTask momgConfigureBackTask) {
        try {
            List<MomgConfigureBackTask> list = momgConfigureBackTaskService.list(new QueryWrapper<MomgConfigureBackTask>().eq("task_name", momgConfigureBackTask.getTaskName()).ne("id", momgConfigureBackTask.getId()));
            if (CollUtil.isNotEmpty(list)) {
                return Result.error("任务名称重复！");
            }
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (loginUser != null) {
                momgConfigureBackTask.setUpdateBy(loginUser.getRealname());
            }

            momgConfigureBackTaskService.updateById(momgConfigureBackTask);

            if (momgConfigureBackTask.getTaskStatus().equals("1")) {
                // 删除历史任务，开启新任务
                momgConfigureBackTask.setDeviceArray(momgConfigureBackTaskService.buildDeviceCommandArray(momgConfigureBackTask.getId()));
                momgConfigureBackTaskService.editTask(momgConfigureBackTask);
            } else {
                // 删除任务
                JSONArray ids = new JSONArray();
                ids.add(momgConfigureBackTask.getId());
                momgConfigureBackTaskService.removeTask(ids);
            }
            return Result.OK("编辑成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("编辑失败！");
        }
    }

    @AutoLog(value = "配置备份任务-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> productConfigureManageDeleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        try {
            List<String> list = Arrays.asList(ids.split(","));
            momgConfigureBackDeviceService.remove(new QueryWrapper<MomgConfigureBackDevice>().in("task_id", list));

            momgConfigureBackTaskService.removeByIds(list);

            List<MomgDeviceConfigureRecord> configureRecordList = momgDeviceConfigureRecordService.list(new QueryWrapper<MomgDeviceConfigureRecord>().in("back_task_id", Arrays.asList(ids.split(","))));
            if (CollUtil.isNotEmpty(configureRecordList)) {
                // 删除关联文件（根据文件路径前缀是否为”http“ 区分 minio和local）
                CommonUtils.deleteFile(configureRecordList.stream().map(MomgDeviceConfigureRecord::getConfigureFile).collect(Collectors.toList()));
                // 删除备份记录
                momgDeviceConfigureRecordService.removeByIds(configureRecordList.stream().map(MomgDeviceConfigureRecord::getId).collect(Collectors.toList()));
            }

            momgConfigureBackTaskService.removeTask(JSON.parseArray(JSONObject.toJSONString(list)));
            return Result.OK("任务删除成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("任务删除失败！");
        }
    }


    @AutoLog(value = "配置备份任务-开启单次任务")
    @PutMapping(value = "/enableOneShot")
    public Result<?> oneShot(@RequestBody MomgConfigureBackTask momgConfigureBackTask) {
        try {
            momgConfigureBackTask.setDeviceArray(momgConfigureBackTaskService.buildDeviceCommandArray(momgConfigureBackTask.getId()));
            momgConfigureBackTaskService.addOneShot(momgConfigureBackTask);
            return Result.OK("开启单次任务成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("开启单次任务失败！");
        }
    }


    @AutoLog(value = "配置备份任务-开启定时任务")
    @PutMapping(value = "/enableTimed")
    public Result<?> enable(@RequestBody MomgConfigureBackTask momgConfigureBackTask) {
        try {
            momgConfigureBackTaskService.updateById(momgConfigureBackTask);
            momgConfigureBackTask.setDeviceArray(momgConfigureBackTaskService.buildDeviceCommandArray(momgConfigureBackTask.getId()));
            momgConfigureBackTaskService.addTask(momgConfigureBackTask);
            return Result.OK("开启定时任务成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("开启定时任务失败！");
        }
    }


    @AutoLog(value = "配置备份任务-停止任务")
    @PutMapping(value = "/pause")
    public Result<?> pause(@RequestBody MomgConfigureBackTask momgConfigureBackTask) {
        try {
            momgConfigureBackTaskService.updateById(momgConfigureBackTask);

            JSONArray ids = new JSONArray();
            ids.add(momgConfigureBackTask.getId());
            momgConfigureBackTaskService.removeTask(ids);
            return Result.OK("停止任务成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("停止任务失败！");
        }
    }


}
