package com.yuanqiao.insight.monitoring.modules.terminal.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("momg_network")
@EqualsAndHashCode(callSuper = false)
public class NetWork implements Serializable {
    private String id ;

    private String gatewayCode;


    private String gatewayType;

    @TableField(exist = false)
    private String gatewayTypeName;

    @TableField(exist = false)
    private List<TerminalDevice> getTerminal;

    @TableField(exist = false)
    private int total; //终端机总数
    @TableField(exist = false)
    private int active; //终端机活跃数量
    @TableField(exist = false)
    private int idle; //终端机空闲数量
    @TableField(exist = false)
    private int offLine; //终端机离线数量

    @TableField(exist = false)
    private List<String> gatewayCodes;
}
