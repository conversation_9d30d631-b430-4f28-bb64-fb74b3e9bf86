package com.yuanqiao.insight.monitoring.modules.topoRoom.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanqiao.insight.monitoring.modules.topoRoom.entity.TopoRoom;
import com.yuanqiao.insight.service.device.entity.DeviceInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 机房表
 * @Author: jeecg-boot
 * @Date:   2021-05-13
 * @Version: V1.0
 */
@Component
public interface TopoRoomMapper extends BaseMapper<TopoRoom> {



    List<TopoRoom> findChildren1(@Param("id")String id);

    List<TopoRoom>findChildren2 (@Param("id")String id);

    List<TopoRoom>list();

    List<DeviceInfo> findCabinetId (@Param("cabinetId")String cabinetId);


}
