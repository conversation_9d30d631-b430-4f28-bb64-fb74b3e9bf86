package com.yuanqiao.insight.monitoring.modules.alarm.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yuanqiao.insight.common.core.util.ObjectUtils;
import com.yuanqiao.insight.modules.flowable.service.AssociationProcessStrategy;
import com.yuanqiao.insight.modules.flowable.vo.ProcessInstanceRequest;
import com.yuanqiao.insight.monitoring.modules.alarm.entity.AlarmHistory;
import com.yuanqiao.insight.monitoring.modules.alarm.service.IAlarmHistoryService;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

@Component
public class AlarmDeviceProcessConcreteStrategy implements AssociationProcessStrategy {


    /**
     * 设置表单内容
     *
     * @param request
     */
    @Override
    public void SetProcessContent(ProcessInstanceRequest request) {
        Map<String, Object> values = request.getValues();
        values.put("alarmId", request.getAssociationId());
        Object processInstanceFormData = request.getValues().get("processInstanceFormData");
        JSONObject jsonObject = JSONObject.parseObject(String.valueOf(processInstanceFormData));
        Map javaObject = JSONObject.toJavaObject(jsonObject, Map.class);
        javaObject.put("alarmId", request.getAssociationId());
        request.getValues().put("processInstanceFormData", JSONObject.toJSONString(javaObject));
    }

    /**
     * 修改告警状态
     *
     * @param processId
     * @param id
     */
    @Override
    public void updateProcess(String processId, String id) {
        IAlarmHistoryService service = SpringContextUtils.getBean(IAlarmHistoryService.class);
        AlarmHistory alarmHistory = service.getById(id);
        if (ObjectUtils.isNotEmpty(alarmHistory)) {
            alarmHistory.setAssociationId(processId);
            alarmHistory.setFlowHandleStatus("1");
            service.updateById(alarmHistory);
        }
    }

    @Override
    public void updateEndProcess(String processInstanceId) {
        IAlarmHistoryService service = SpringContextUtils.getBean(IAlarmHistoryService.class);
        LambdaQueryWrapper<AlarmHistory> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(AlarmHistory::getAssociationId, processInstanceId);
        AlarmHistory alarmHistory = service.getOne(wrapper1);
        if (ObjectUtils.isNotEmpty(alarmHistory)) {
            alarmHistory.setFlowHandleStatus("2");
            alarmHistory.setHandleTime(new Date());
            alarmHistory.setHandleStatus("2");
            alarmHistory.setAlarmStatus("1");
            service.updateById(alarmHistory);
        }
    }

    @Override
    public void updateProcessByDraft(String processId, String id) {
        IAlarmHistoryService service = SpringContextUtils.getBean(IAlarmHistoryService.class);
        AlarmHistory alarmHistory = service.getById(id);
        if (ObjectUtils.isNotEmpty(alarmHistory)) {
            alarmHistory.setAssociationId(processId);
            service.updateById(alarmHistory);
        }
    }
}
