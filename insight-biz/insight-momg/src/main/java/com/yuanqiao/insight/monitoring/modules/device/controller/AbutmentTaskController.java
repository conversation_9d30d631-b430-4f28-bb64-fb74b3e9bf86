package com.yuanqiao.insight.monitoring.modules.device.controller;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.service.device.entity.MomgAbutmentTask;
import com.yuanqiao.insight.service.device.service.IMomgAbutmentTaskService;
import com.yuanqiao.insight.service.device.service.impl.MomgAbutmentTaskServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
* @Description: 监控对接拉取任务表
* @Author: jeecg-boot
* @Date:   2021-04-01
* @Version: V1.0
*/
@Api(tags="监控对接拉取任务表")
@RestController
@RequestMapping("/abutment/task")
@Slf4j
public class AbutmentTaskController extends JeecgController<MomgAbutmentTask, IMomgAbutmentTaskService>{
   @Autowired
   private MomgAbutmentTaskServiceImpl abutmentTaskService;

    /**
     *  立即执行
     *
     * @param abutmentTask
     * @return
     */
    @AutoLog(value = "监控对接拉取任务表-立即执行")
    @ApiOperation(value="监控对接拉取任务表-立即执行", notes="监控对接拉取任务表-立即执行")
    @PutMapping(value = "/execute")
    public Result<?> execute(@RequestBody MomgAbutmentTask abutmentTask) {
        abutmentTaskService.execute(abutmentTask);
        return Result.OK("操作成功!");
    }

    /**
     *   通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "监控对接拉取任务表-通过id删除")
    @ApiOperation(value="监控对接拉取任务表-通过id删除", notes="监控对接拉取任务表-通过id删除")
    @PutMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id",required=true) String id) {
        abutmentTaskService.deleteTask(id);
        return Result.OK("删除成功!");
    }

   /**
    *  批量删除
    *
    * @param ids
    * @return
    */
   @AutoLog(value = "监控对接拉取任务表-批量删除")
   @ApiOperation(value="监控对接拉取任务表-批量删除", notes="监控对接拉取任务表-批量删除")
   @DeleteMapping(value = "/deleteBatch")
   public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
       List<String> list = Arrays.asList(ids.split(","));
       list.forEach(id->{
           abutmentTaskService.deleteTask(id);
       });
       return Result.OK("批量删除成功！");
   }

   /**
   * 导出excel
   *
   * @param request
   * @param abutmentTask
   */
   @RequestMapping(value = "/exportXls")
   public ModelAndView exportXls(HttpServletRequest request, MomgAbutmentTask abutmentTask) {
       return super.exportXls(request, abutmentTask, MomgAbutmentTask.class, "监控对接拉取任务表");
   }

    @GetMapping(value = "/testAbutmentPull")
    public Result<?> testAbutmentPull(@RequestParam(name="dataType",required=true) String dataType) {

        String json = "";if (dataType.equals("device")){
            json = "{\n" +
                    "  \"dataType\":\"device\",\n" +
                    "\"operateType\":\"I\",\n" +
                    "  \"devices\": [\n" +
                    "    {\n" +
                    "      \"baseInfo\": {\n" +
                    "        \"name\": \"设备1\",\n" +
                    "        \"code\": \"aaa\",\n" +
                    "        \"ip\": \"192.168.*.*\",\n" +
                    "        \"statusCode\": 1,\n" +
                    "        \"statusText\":\"在线\",\n" +
                    "        \"productCode\": \"Air_Conditioner\"\n" +
                    "      },\n" +
                    "      \"dataInfo\": {\n" +
                    "        \"CSZT\": \"开启\",\n" +
                    "        \"ZLZT\": \"开启\"\n" +
                    "      }\n" +
                    "    },\n" +
                    "    {\n" +
                    "      \"baseInfo\": {\n" +
                    "        \"name\": \"设备2\",\n" +
                    "        \"code\": \"bbb\",\n" +
                    "        \"ip\": \"192.168.*.*\",\n" +
                    "        \"statusCode\": 1,\n" +
                    "        \"statusText\":\"在线\",\n" +
                    "        \"productCode\": \"Temperature_humidity\"\n" +
                    "      },\n" +
                    "      \"dataInfo\": {\n" +
                    "        \"EH\": 14.9,\n" +
                    "        \"ET\": 39\n" +
                    "      }\n" +
                    "    }\n" +
                    "  ]\n" +
                    "}";
        } else if (dataType.equals("alarm")){
            json = "{\n" +
                    "  \"dataType\": \"alarm\",\n" +
                    "  \"alarms\": [\n" +
                    "    {\n" +
                    "      \"alarmInfo\": {\n" +
                    "        \"deviceCode\": \"aaa\",\n" +
                    "        \"name\": \"测试告警1\",\n" +
                    "        \"levelCode\": 10,\n" +
                    "        \"levelText\": \"一般告警\",\n" +
                    "        \"statusCode\": \"0\",\n" +
                    "        \"statusText\": \"未解决\",\n" +
                    "        \"confirmCode\": 0,\n" +
                    "        \"confirmText\": \"未确认\",\n" +
                    "        \"subject\": \"cpuRate\",\n" +
                    "        \"message\": \"CPU使用率超过90%\",\n" +
                    "        \"firstTime\": \"2024-03-28 12:00:00\",\n" +
                    "        \"lastTime\": \"2024-03-28 14:25:00\"\n" +
                    "      }\n" +
                    "    },\n" +
                    "    {\n" +
                    "      \"alarmInfo\": {\n" +
                    "        \"deviceCode\": \"bbb\",\n" +
                    "        \"name\": \"测试告警2\",\n" +
                    "        \"levelCode\": 10,\n" +
                    "        \"levelText\": \"一般告警\",\n" +
                    "        \"statusCode\": \"0\",\n" +
                    "        \"statusText\": \"未解决\",\n" +
                    "        \"confirmCode\": 0,\n" +
                    "        \"confirmText\": \"未确认\",\n" +
                    "        \"subject\": \"memRate\",\n" +
                    "        \"message\": \"内存使用率超过90%\",\n" +
                    "        \"firstTime\": \"2024-03-28 12:05:00\",\n" +
                    "        \"lastTime\": \"2024-03-28 14:30:00\"\n" +
                    "      }\n" +
                    "    }\n" +
                    "  ]\n" +
                    "}";
        }
        return Result.OK(JSONObject.parseObject(json));
    }


}
