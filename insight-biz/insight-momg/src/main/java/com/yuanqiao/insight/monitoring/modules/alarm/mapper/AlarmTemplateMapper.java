package com.yuanqiao.insight.monitoring.modules.alarm.mapper;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.acore.alarm.model.AlarmRuleModel;
import com.yuanqiao.insight.acore.alarm.model.AlarmTemplateModel;
import com.yuanqiao.insight.monitoring.modules.alarm.entity.AlarmTemplate;
import com.yuanqiao.insight.monitoring.modules.alarm.entity.NumberVo;
import com.yuanqiao.insight.monitoring.modules.alarm.model.AlarmTemplateViewModel;
import com.yuanqiao.insight.service.device.entity.DeviceInfo;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 告警模板
 * @Author: jeecg-boot
 * @Date: 2021-03-03
 * @Version: V1.0
 */
@Component
@Mapper
public interface AlarmTemplateMapper extends BaseMapper<AlarmTemplate> {

    IPage<AlarmTemplate> findAlarmProByDevId(Page<?> page, @Param("deviceId") String deviceId, @Param("productId") String productId);

    int findAlarmProByDevIdCount(@Param("deviceId") String deviceId, @Param("productId") String productId);

    DeviceInfo findProIdAndDevIdByDevice(@Param("deviceId") String deviceId);

    int findAlarmTemplateByOtherCount(@Param("name") String name, @Param("isOnline") String isOnline, @Param("productId") String productId, @Param("displayName") String displayName);

    int isOnline(@Param("id") String id, @Param("isOnline") String isOnline);

    List<AlarmRuleModel> findRuleByTemId(@Param("id") String id);

    List<AlarmRuleModel> findRuleByTemIdCount(@Param("id") String id);

    List<NumberVo> findNameByName(@Param("displayname") String displayname, @Param("id") String id);

    List<ProertyMetadata> findRuleBySubjectIndex(@Param("id") String id);

    List<ProertyMetadata> findMateCodeByPid(@Param("id") String id);

    int findTemAndRuleByDeviceIdCount(@Param("id") String id, @Param("deviceId") String deviceId);

    List<AlarmTemplateModel> getAlarmTemByDeviceId(@Param("deviceId") String id);

    List<AlarmTemplateModel> getAlarmTemByProId(@Param("productId") String productId);

    IPage<AlarmTemplateViewModel> alarmTemplateView(Page<AlarmTemplateViewModel> page, @Param("start") DateTime start, @Param("end") DateTime end);

    IPage<AlarmTemplate> pageList(Page<AlarmTemplate> page, String deviceId);
}
