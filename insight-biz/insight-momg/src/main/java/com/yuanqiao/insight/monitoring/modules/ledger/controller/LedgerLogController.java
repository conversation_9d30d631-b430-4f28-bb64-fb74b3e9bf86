package com.yuanqiao.insight.monitoring.modules.ledger.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.monitoring.modules.ledger.entity.LedgerLog;
import com.yuanqiao.insight.monitoring.modules.ledger.service.ILedgerLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Map;

 /**
 * @Description: 资产管理日志
 * @Author: jeecg-boot
 * @Date:   2020-03-18
 * @Version: V1.0
 */
@Slf4j
@Api(tags="资产管理日志")
@RestController
@RequestMapping("/ledger/ledgerLog")
public class LedgerLogController extends JeecgController<LedgerLog, ILedgerLogService> {
	@Autowired
	private ILedgerLogService ledgerLogService;
	@Autowired
    private ISysUserService sysUserService;

	/**
	 * 分页列表查询
	 *
	 * @param ledgerLog
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "资产管理日志-分页列表查询")
	@ApiOperation(value="资产管理日志-分页列表查询", notes="资产管理日志-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(LedgerLog ledgerLog,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<LedgerLog> queryWrapper = QueryGenerator.initQueryWrapper(ledgerLog, req.getParameterMap());
		Page<LedgerLog> page = new Page<LedgerLog>(pageNo, pageSize);
		IPage<LedgerLog> pageList = ledgerLogService.page(page, queryWrapper);
		if(null != pageList.getRecords() && 0 <pageList.getRecords().size()){
            Map<String,String> map = sysUserService.selectRealnameByUsername();
            pageList.getRecords().forEach(item->{
                item.setCreateByText(map.get(item.getCreateBy()));
            });
        }
		return Result.OK(pageList);
	}

	/**
	 * 添加
	 *
	 * @param ledgerLog
	 * @return
	 */
	@AutoLog(value = "资产管理日志-添加")
	@ApiOperation(value="资产管理日志-添加", notes="资产管理日志-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody LedgerLog ledgerLog) {
		ledgerLogService.save(ledgerLog);
		return Result.ok("添加成功！");
	}

	/**
	 * 编辑
	 *
	 * @param ledgerLog
	 * @return
	 */
	@AutoLog(value = "资产管理日志-编辑")
	@ApiOperation(value="资产管理日志-编辑", notes="资产管理日志-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody LedgerLog ledgerLog) {
		ledgerLogService.updateById(ledgerLog);
		return Result.ok("编辑成功!");
	}

	/**
	 * 通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "资产管理日志-通过id删除")
	@ApiOperation(value="资产管理日志-通过id删除", notes="资产管理日志-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		ledgerLogService.removeById(id);
		return Result.ok("删除成功!");
	}

	/**
	 * 批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "资产管理日志-批量删除")
	@ApiOperation(value="资产管理日志-批量删除", notes="资产管理日志-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.ledgerLogService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.ok("批量删除成功！");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "资产管理日志-通过id查询")
	@ApiOperation(value="资产管理日志-通过id查询", notes="资产管理日志-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		LedgerLog ledgerLog = ledgerLogService.getById(id);
		return Result.ok(ledgerLog);
	}

  /**
   * 导出excel
   *
   * @param request
   * @param ledgerLog
   */
  @RequestMapping(value = "/exportXls")
  public ModelAndView exportXls(HttpServletRequest request, LedgerLog ledgerLog) {
      return super.exportXls(request, ledgerLog, LedgerLog.class, "资产管理日志");
  }

  /**
   * 通过excel导入数据
   *
   * @param request
   * @param response
   * @return
   */
  @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
  public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      return super.importExcel(request, response, LedgerLog.class);
  }

}
