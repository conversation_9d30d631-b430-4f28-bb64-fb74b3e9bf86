package com.yuanqiao.insight.monitoring.modules.product.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yuanqiao.insight.cmdb.modules.assetscategory.entity.AssetsCategory;
import com.yuanqiao.insight.cmdb.modules.assetscategory.mapper.AssetsCategoryMapper;
import com.yuanqiao.insight.monitoring.modules.device.entity.MomgLogDevice;
import com.yuanqiao.insight.monitoring.modules.device.service.IMomgLogDeviceService;
import com.yuanqiao.insight.service.ScheduleSetting.entity.ScheduleSetting;
import com.yuanqiao.insight.service.device.entity.DeviceInfo;
import com.yuanqiao.insight.service.device.entity.MomgProductConfigureManage;
import com.yuanqiao.insight.service.device.mapper.DeviceInfoMapper;
import com.yuanqiao.insight.service.device.service.IDeviceInfoService;
import com.yuanqiao.insight.service.device.service.IMomgProductConfigureManageService;
import com.yuanqiao.insight.service.flow.entity.FlowChain;
import com.yuanqiao.insight.service.flow.service.IFlowChainService;
import com.yuanqiao.insight.service.product.entity.*;
import com.yuanqiao.insight.service.product.mapper.DeviceConnectTemplateInfoMapper;
import com.yuanqiao.insight.service.product.mapper.MomgProductTransferJobMapper;
import com.yuanqiao.insight.service.product.model.ProductCopyModel;
import com.yuanqiao.insight.service.product.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Description: product
 * @Author: jeecg-boot
 * @Date: 2021-03-01
 * @Version: V1.0
 */
@Api(tags = "产品")
@RestController
@RequestMapping("/product/product")
@Slf4j
public class ProductController extends JeecgController<Product, IProductService> {

    private static Pattern DEFAULT_ICON_PATH_PATTERN = Pattern.compile("default/.*");
    @Autowired
    private IProductService productService;
    @Autowired
    private AssetsCategoryMapper assetsCategoryMapper;
    @Autowired
    private DeviceInfoMapper deviceInfoMapper;
    @Autowired
    private IMomgLogDeviceService momgLogDeviceService;
    @Autowired
    private IDeviceInfoService deviceInfoService;
    @Autowired
    private IMomgTransferProtocolService transferProtocolService;
    @Autowired
    private IMomgProductJobService productJobService;
    @Autowired
    private IMomgProductTransferJobService productTransferJobService;
    @Autowired
    private IProertyMetadataService proertyMetadataService;
    @Autowired
    private MetadataExtendService metadataExtendService;
    @Autowired
    private IDeviceControlCommandService deviceControlCommandService;
    @Autowired
    private IDeviceControlCommandExtendService deviceControlCommandExtendService;
    @Autowired
    private IFlowChainService flowChainService;
    @Autowired
    private MomgProductTransferJobMapper productTransferJobMapper;
    @Autowired
    private IDeviceConnectTemplateInfoService deviceConnectTemplateInfoService;
    @Autowired
    private IProductPanelInfoService productPanelInfoService;
    @Autowired
    private IMomgProductConfigureManageService productConfigureManageService;

    /**
     * 分页列表查询
     *
     * @param product
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "产品-分页列表查询")
    @ApiOperation(value = "产品-分页列表查询", notes = "产品-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(Product product,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "8") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<Product> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotEmpty(product.getDisplayName())) {
            queryWrapper.like("display_name", product.getDisplayName());
        }
        if (StringUtils.isNotEmpty(product.getAssetsCategoryId())) {
            AssetsCategory category = assetsCategoryMapper.selectOne(new QueryWrapper<AssetsCategory>().eq("id", product.getAssetsCategoryId()));
            List<AssetsCategory> childList = getChild(category, new ArrayList<>());
            childList.add(0, category);
            queryWrapper.in("assets_category_id", childList.stream().map(AssetsCategory::getId).collect(Collectors.toList()));
        }
        queryWrapper.orderByDesc("create_time");
        Page<Product> page = new Page<Product>(pageNo, pageSize);
        IPage<Product> pageList = productService.page(page, queryWrapper);

        List<Product> productList = pageList.getRecords();

        for (Product pro : productList) {
            // 每一个产品可能对应多个选择的传输协议
            List<Map<String, Object>> selectedProtocolList = new ArrayList<>();
            List<MomgProductTransferJob> product_transfer_job_list = productTransferJobMapper.selectAllInfo(pro.getId());

            for (MomgProductTransferJob transferJob : product_transfer_job_list) {
                Map<String, Object> map = new HashMap<>();
                map.put("unit", transferJob.getUnit());
                map.put("rate", transferJob.getRate());
                map.put("jobClassId", transferJob.getProductJobId());
                map.put("collectType", transferJob.getCollectType());
                map.put("transferProtocol", transferJob.getTransferProtocol());
                map.put("transferProtocolId", transferJob.getTransferProtocolId());
                selectedProtocolList.add(map);
            }
            pro.setSelectedProtocolList(selectedProtocolList);
        }

        pageList.setRecords(productList);
        return Result.OK(pageList);
    }

    //获取子级分类
    private List<AssetsCategory> getChild(AssetsCategory category, List<AssetsCategory> childList) {
        if (null == category) {
            return null;
        }
        List<AssetsCategory> list = assetsCategoryMapper.selectByParentId(category.getId());
        if (CollUtil.isNotEmpty(list)) {
            childList.addAll(list);
            for (AssetsCategory assetsCategory : list) {
                childList = getChild(assetsCategory, childList);
            }
        }
        return childList;
    }

    @ApiOperation(value = "产品-列表查询", notes = "产品-列表查询")
    @GetMapping(value = "/productList")
    public Result<?> productList(HttpServletRequest req) {

        List<Product> pageList = productService.list();
        return Result.OK(pageList);
    }

    /**
     * 产品保存
     *
     * @param product
     * @return
     */
    @AutoLog(value = "产品-复制")
    @ApiOperation(value = "产品-复制", notes = "产品-复制")
    @PostMapping(value = "/copyForProductModel")
    public Result<?> copyForProductModel(@RequestBody Product product) {
        QueryWrapper<Product> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("display_name", product.getDisplayName());
        queryWrapper.or();
        queryWrapper.eq("name", product.getName());
        List<Product> list = productService.list(queryWrapper);
        if (!list.isEmpty()) {
            return Result.error("产品标识或名称重复！");
        }
        ProductCopyModel productCopyModel = getProductCopyModel(product.getProductModel(), true);
        Product pro = productCopyModel.getProduct();
        pro.setName(product.getName());
        pro.setDisplayName(product.getDisplayName());
        boolean saveBool = productService.copyProduct(productCopyModel).isSuccess();
        //祖宗之法不可变啊
        saveSysLog();
        if (saveBool) {
            return Result.OK("添加成功！");
        } else {
            return Result.error("添加失败！");
        }
    }

    /**
     * 产品保存
     *
     * @param product
     * @return
     */
    @AutoLog(value = "产品-添加")
    @ApiOperation(value = "产品-添加", notes = "产品-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody Product product) {
        QueryWrapper<Product> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("display_name", product.getDisplayName());
        queryWrapper.or();
        queryWrapper.eq("name", product.getName());
        List<Product> list = productService.list(queryWrapper);
        if (list.size() > 0) {
            return Result.error("产品标识或名称重复！");
        }

        if (StringUtils.isNotEmpty(product.getAssetsCategoryId())) {
            String categoryId = product.getAssetsCategoryId();
            AssetsCategory assetsCategory = assetsCategoryMapper.selectOne(new QueryWrapper<AssetsCategory>().eq("id", categoryId));
            product.setAssetsCategoryName(assetsCategory.getCategoryName());
            product.setAssetsCategoryId(assetsCategory.getId());
        }

        //设置默认图片
        setDefaultImg(product);

        // 选中的传输协议及相关数据的集合
        List<MomgProductTransferJob> productTransferJobList = creatProductTransferJobList(product);
        boolean saveBool = productService.addOrEdit(product, productTransferJobList, new ArrayList<>(), new ArrayList<>());
        saveSysLog();
        if (saveBool) {
            return Result.OK("添加成功！");
        } else {
            return Result.error("添加失败！");
        }
    }

    /**
     * 产品编辑
     *
     * @param product
     * @return
     */
    @AutoLog(value = "产品-编辑")
    @ApiOperation(value = "产品-编辑", notes = "产品-编辑")
    @PutMapping(value = "/edit")
    @CacheEvict(value = "product", allEntries = true)
    public Result<?> edit(@RequestBody Product product) {
        QueryWrapper<Product> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("display_name", product.getDisplayName());
        queryWrapper1.or();
        queryWrapper1.eq("name", product.getName());
        List<Product> originList = productService.list(queryWrapper1);
        List<Product> notMeList = originList.stream().filter(p -> !p.getId().equals(product.getId())).collect(Collectors.toList());
        if (notMeList.size() > 0) {
            return Result.error("产品标识或名称重复！");
        }

        Product oldProduct = productService.getById(product.getId());
        if (StringUtils.isNotBlank(oldProduct.getIcon()) && !StringUtils.equals(oldProduct.getIcon(), product.getIcon())) {
            List<String> newFile = new ArrayList<>(Arrays.asList(product.getIcon().split(",")));
            List<String> oldFile = new ArrayList<>(Arrays.asList(oldProduct.getIcon().split(",")));
            List<String> defaultIcon = oldFile.stream().filter(file -> DEFAULT_ICON_PATH_PATTERN.matcher(file).matches()).collect(Collectors.toList());
            HashSet<String> storeIcon = new HashSet<>(newFile);
            storeIcon.addAll(defaultIcon);
            List<String> collect = storeIcon.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            product.setIcon(StringUtils.join(collect, ","));
            List<String> deleteFiles = oldFile.stream().filter(file -> !DEFAULT_ICON_PATH_PATTERN.matcher(file).matches() && !newFile.contains(file)).collect(Collectors.toList());
            CommonUtils.deleteFile(deleteFiles);
        }

        if (StringUtils.isNotEmpty(product.getAssetsCategoryId())) {
            String categoryId = product.getAssetsCategoryId();
            AssetsCategory assetsCategory = assetsCategoryMapper.selectOne(new QueryWrapper<AssetsCategory>().eq("id", categoryId));
            if (assetsCategory != null) {
                product.setAssetsCategoryName(assetsCategory.getCategoryName());
                product.setAssetsCategoryId(assetsCategory.getId());
            }
        }

        setDefaultImg(product);

        // 选中的传输协议及相关数据的集合
        List<MomgProductTransferJob> productTransferJobList = creatProductTransferJobList(product);

        //同步scheduleSetting Job表
        List<ScheduleSetting> updateSettingList = new ArrayList<>();
        List<ScheduleSetting> deleteSettingList = new ArrayList<>();
        List<ScheduleSetting> scheduleSettingList = deviceInfoMapper.selectAllScheduleByProId(product.getId(), null);
        if (!CollectionUtils.isEmpty(scheduleSettingList) && !CollectionUtils.isEmpty(productTransferJobList)) {
            Map<String, MomgProductTransferJob> productTransferJobMap = productTransferJobList.stream().collect(Collectors.toMap(MomgProductTransferJob::getTransferProtocol, ptj -> ptj));
            scheduleSettingList.forEach(scheduleSetting -> {
                if (productTransferJobMap.containsKey(scheduleSetting.getProtocol())) {
                    for (Map.Entry<String, MomgProductTransferJob> entry : productTransferJobMap.entrySet()) {
                        if (scheduleSetting.getProtocol().equals(entry.getKey())) {
                            MomgProductTransferJob productTransferJob = entry.getValue();
                            MomgProductJob productJob = productJobService.getById(productTransferJob.getProductJobId());
                            scheduleSetting.setJobClass(productJob.getValue());
                            scheduleSetting.setBasicRate(productTransferJob.getRate());
                            scheduleSetting.setBasicUnit(productTransferJob.getUnit());
                            if (productTransferJob.getUnit().equals("0")) {
                                // 秒
                                scheduleSetting.setRate(productTransferJob.getRate());
                            } else {
                                // 分钟
                                scheduleSetting.setRate(productTransferJob.getRate() * 60);
                            }
                            scheduleSetting.setCron(productTransferJob.getCron());
                            updateSettingList.add(scheduleSetting);
                        }
                    }
                } else {
                    deleteSettingList.add(scheduleSetting);
                }
            });
        }

        // 原product表上的这四个字段不再保存值
        boolean saveBool = productService.addOrEdit(product, productTransferJobList, updateSettingList, deleteSettingList);
        saveSysLog();
        if (saveBool) {
            return Result.OK("编辑成功！");
        } else {
            return Result.error("编辑失败！");
        }
    }

    /**
     * 产品导出
     *
     * @return
     */
    @AutoLog(value = "产品-导出")
    @ApiOperation(value = "产品-导出", notes = "产品-导出")
    @GetMapping(value = "/exportProduct")
    public void exportProduct(@RequestParam("productId") String productId, HttpServletResponse response) {

        ProductCopyModel productCopyModel = getProductCopyModel(productId, false);
        // 设置响应的类型及文件名
        response.addHeader("Access-Control-Expose-Headers", "file-name");
        response.setHeader("file-name", String.format("%s.json", productCopyModel.getProduct().getName()));
        response.setHeader("Content-Disposition", String.format("attachment; filename=\"%s.json\"; charset=UTF-8", productCopyModel.getProduct().getName()));
        response.setContentType("application/force-download");// 设置强制下载不打开
        String jsonString = JSON.toJSONString(productCopyModel);
        try (InputStream inputStream = new ByteArrayInputStream(jsonString.getBytes(StandardCharsets.UTF_8));
             OutputStream outputStream = response.getOutputStream()) {
            byte[] buf = new byte[1024];
            int len;
            while ((len = inputStream.read(buf)) > 0) {
                outputStream.write(buf, 0, len);
            }
            response.flushBuffer();
        } catch (Exception e) {
            log.error("导出失败", e);
        }
    }


    /**
     * 产品保存
     *
     * @param file
     * @return
     */
    @AutoLog(value = "产品-导人")
    @ApiOperation(value = "产品-导人", notes = "产品-导人")
    @PostMapping(value = "/importProduct")
    public Result<?> importProduct(@RequestParam(value = "displayName", defaultValue = "") String displayName
            , @RequestParam(value = "name", defaultValue = "") String name
            , @RequestParam("file") MultipartFile file) throws IOException {
        InputStream inputStream = file.getInputStream();
        String read = IoUtil.read(inputStream, StandardCharsets.UTF_8);
        ProductCopyModel productCopyModel = JSON.parseObject(read, ProductCopyModel.class);
        Product product = productCopyModel.getProduct();
        if (StringUtils.isNotEmpty(displayName)) {
            QueryWrapper<Product> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("display_name", displayName);
            int count = productService.count(queryWrapper);
            if (count > 0) {
                return Result.error("产品名称重复！");
            }
            product.setDisplayName(displayName);
        }
        if (StringUtils.isNotEmpty(name)) {
            QueryWrapper<Product> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("name", name);
            int count = productService.count(queryWrapper);
            if (count > 0) {
                return Result.error("产品标识重复！");
            }
            product.setName(name);
        }

        return productService.copyProduct(productCopyModel);
    }

    /**
     * 保存面板JSON串
     *
     * @param product
     * @return
     */
    @AutoLog(value = "产品-保存面板JSON串")
    @ApiOperation(value = "产品-保存面板JSON串", notes = "产品-保存面板JSON串")
    @PutMapping(value = "/savePanel")
    public Result<?> savePanel(@RequestBody Product product) {
        System.out.println(" ********** panelJson ：" + product.getPanelJson());
        productService.updateById(product);
        saveSysLog();
        return Result.OK("保存成功!");
    }

    @Autowired
    private DeviceConnectTemplateInfoMapper deviceConnectTemplateInfoMapper;


    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "产品-通过id删除")
    @ApiOperation(value = "产品-通过id删除", notes = "产品-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        List<DeviceInfo> deviceInfoList = deviceInfoMapper.selectList(new QueryWrapper<DeviceInfo>().eq("product_id", id).eq("delflag", 0));
        if (deviceInfoList != null && !deviceInfoList.isEmpty()) {
            return Result.error("当前产品下至少存在一个设备与之关联，不可删除！");
        }
        boolean b = productService.removeBatch(Collections.singletonList(id));
        saveSysLog();
        if (b) {
            return Result.OK("删除成功!");
        } else {
            return Result.error("删除失败！");
        }
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "产品-批量删除")
    @ApiOperation(value = "产品-批量删除", notes = "产品-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        List<String> idss = Arrays.asList(ids.split(","));
        for (String item : idss) {
            List<DeviceInfo> deviceInfoList = deviceInfoMapper.selectList(new QueryWrapper<DeviceInfo>().eq("product_id", item).eq("delflag", 0));
            if (deviceInfoList != null && !deviceInfoList.isEmpty()) {
                return Result.error("选中的产品中至少有一个产品存在与之关联的设备，不可删除！");
            }
        }
        boolean b = productService.removeBatch(idss);
        saveSysLog();
        if (b) {
            return Result.OK("批量删除成功!");
        } else {
            return Result.error("批量删除失败!");
        }
    }


    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "产品-通过id查询")
    @ApiOperation(value = "产品-通过id查询", notes = "产品-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        Product product = productService.getById(id);
        if (product == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(product);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param product
     */
    @AutoLog(value = "产品-导出excel")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Product product) {
        saveSysLog();
        return super.exportXls(request, product, Product.class, "com/yuanqiao/insight/modules/product");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @AutoLog(value = "产品-导入数据")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        saveSysLog();
        return super.importExcel(request, response, Product.class);
    }

    @AutoLog(value = "产品-删除图标")
    @PostMapping(value = "/updateIcons")
    public Result<?> updateIcons(@RequestBody Product product) {
        DeviceInfo byId = deviceInfoService.getById(product.getDeviceId());
        Product product1 = product.setId(byId.getProductId());
        productService.updateById(product1);
        return Result.OK("删除成功!");

    }


    /**
     * 查询所有的传输协议给下拉框用
     *
     * @return
     */
    @AutoLog(value = "产品-查询传输协议")
    @ApiOperation(value = "产品-查询传输协议", notes = "产品-查询传输协议")
    @GetMapping(value = "/queryTransferProtocols")
    public Result<?> queryTransferProtocol() {
        List<MomgTransferProtocol> list = transferProtocolService.list();
        return Result.OK(list);
    }


    /**
     * 查询当前产品绑定的传输协议
     *
     * @return
     */
    @AutoLog(value = "产品-查询当前产品绑定的传输协议")
    @ApiOperation(value = "产品-查询传输协议", notes = "产品-查询当前产品绑定的传输协议")
    @GetMapping(value = "/queryBindTransferProtocol")
    public Result<?> queryBindTransferProtocol(String productId) {
        if (StringUtils.isEmpty(productId))
            return Result.error("参数不能为空");

        QueryWrapper<MomgProductTransferJob> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("product_id", productId);
        List<MomgProductTransferJob> middle_table_list = productTransferJobService.list(queryWrapper);
        List<String> transfer_protocol_id_list = middle_table_list.stream().map(u -> u.getTransferProtocolId()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(transfer_protocol_id_list))
            return Result.OK(new ArrayList<>());

        QueryWrapper<MomgTransferProtocol> queryWrapper2 = new QueryWrapper<>();
        queryWrapper2.in("id", transfer_protocol_id_list);
        List<MomgTransferProtocol> transferProtocolList = transferProtocolService.list(queryWrapper2);
        return Result.OK(transferProtocolList);
    }


    /**
     * 查询所有的job
     *
     * @return
     */
    @AutoLog(value = "产品-查询JOB")
    @ApiOperation(value = "产品-查询JOB", notes = "产品-查询JOB")
    @GetMapping(value = "/queryProductJobs")
    public Result<?> queryProductJobs() {
        List<MomgProductJob> list = productJobService.list();
        return Result.OK(list);
    }

    private void saveSysLog() {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        MomgLogDevice momgLogDevice = new MomgLogDevice();
        momgLogDevice.setCreateBy(loginUser.getUsername());
        momgLogDevice.setProductConfig("1");
        momgLogDeviceService.save(momgLogDevice);
    }

    private void setDefaultImg(Product product) {
        if (StringUtils.isEmpty(product.getIcon())) {
            product.setIcon("default/server.png");
        }
        if (product.getDisplayName().contains("防火墙")) {
            product.setMini("/img/mini_firewall.png");
        } else if (product.getDisplayName().contains("服务器")) {
            product.setMini("/img/mini_server.png");
        } else if (product.getDisplayName().contains("路由器")) {
            product.setMini("/img/mini_router.png");
        } else if (product.getDisplayName().contains("网关")) {
            product.setMini("/img/mini_gateway.png");
        } else if (product.getDisplayName().contains("终端")) {
            product.setMini("/img/mini_pc.png");
        } else if (product.getDisplayName().contains("交换机")) {
            product.setMini("/img/mini_switch.png");
        } else if (product.getDisplayName().contains("存储")) {
            product.setMini("/img/mini_storage.png");
        } else if (product.getDisplayName().contains("数据库")) {
            product.setMini("/img/mini_database.png");
        } else {
            product.setMini("/img/mini_default.png");
        }
    }

    private List<MomgProductTransferJob> creatProductTransferJobList(Product product) {
        List<MomgProductTransferJob> productTransferJobList = new ArrayList<>();
        // 根据连接参数维护产品协议JOB信息
        List<Map<String, Object>> selectedProtocolList = product.getSelectedProtocolList();
        if (CollUtil.isNotEmpty(selectedProtocolList)) {
            for (Map<String, Object> map : selectedProtocolList) {
                MomgProductTransferJob productTransferJob = new MomgProductTransferJob();
                // 根据采集模式来判断
                String collectType = (String) map.get("collectType");
                if ("1".equals(collectType)) {
                    Integer rate = (Integer) map.get("rate");
                    String unit = (String) map.get("unit");

                    if (StringUtils.isNotEmpty(unit) && unit.equals("0")) {
                        productTransferJob.setCron("0/" + rate + " * * * * ?");
                    } else {
                        productTransferJob.setCron("0 */" + rate + " * * * ?");
                    }

                    productTransferJob.setRate(rate);
                    productTransferJob.setUnit(unit);
                }
                productTransferJob.setProductId(product.getId());
                productTransferJob.setCollectType(collectType);
                productTransferJob.setProductJobId((String) map.get("jobClassId"));
                productTransferJob.setTransferProtocol((String) map.get("transferProtocol"));
                productTransferJob.setTransferProtocolId((String) map.get("transferProtocolId"));
                productTransferJobList.add(productTransferJob);
            }
        }
        // 默认添加公共协议
        List<Map<String, Object>> containsCommonList = selectedProtocolList.stream().filter(i -> StringUtils.equals("COMMON", i.get("transferProtocol") + "")).collect(Collectors.toList());
        if (CollUtil.isEmpty(containsCommonList)) {
            MomgProductTransferJob productTransferJob = new MomgProductTransferJob();
            productTransferJob.setProductId(product.getId());
            productTransferJob.setCollectType("2");
            productTransferJob.setTransferProtocol("COMMON");
            productTransferJob.setTransferProtocolId("82760e4589c211ee8ff10242ac120002");
            productTransferJobList.add(productTransferJob);
        }

        // 默认添加SPEC协议
        List<Map<String, Object>> containsSpecList = selectedProtocolList.stream().filter(i -> StringUtils.equals("SPEC", i.get("transferProtocol") + "")).collect(Collectors.toList());
        if (CollUtil.isEmpty(containsSpecList)) {
            MomgProductTransferJob productTransferJob = new MomgProductTransferJob();
            productTransferJob.setProductId(product.getId());
            productTransferJob.setCollectType("1");
            productTransferJob.setTransferProtocol("SPEC");
            productTransferJob.setTransferProtocolId("1574946033065041922");
            productTransferJob.setRate(2);
            productTransferJob.setUnit("1");
            productTransferJob.setCron("0 */2 * * * ?");
            productTransferJob.setProductJobId("1574947943503405058");
            productTransferJobList.add(productTransferJob);
        }

        return productTransferJobList;
    }

    private ProductCopyModel getProductCopyModel(String productId, Boolean isCopy) {
        ProductCopyModel productCopyModel = new ProductCopyModel();

        //1.待复制产品
        final Product productModel = productService.getById(productId);
        productModel.setCreateTime(new Date());
        productModel.setUpdateTime(null);
        productCopyModel.setProduct(productModel);
        //2.传输协议及相关数据的集合
        List<MomgProductTransferJob> productTransferJobList = productTransferJobService
                .list(new LambdaQueryWrapper<MomgProductTransferJob>()
                        .eq(MomgProductTransferJob::getProductId, productId));
        productCopyModel.setProductTransferJobList(productTransferJobList);
        //3.物模型复制
        List<ProertyMetadata> allList = new ArrayList<>();
        productCopyModel.setMetadataList(allList);
        //4.流程
        List<FlowChain> chainList = flowChainService.list(new LambdaQueryWrapper<FlowChain>()
                .eq(FlowChain::getProductId, productId));
        productCopyModel.setChainList(chainList);
        //5.设备连接
        List<DeviceConnectTemplateInfo> deviceConnectTemplateInfos = deviceConnectTemplateInfoService
                .list(new QueryWrapper<DeviceConnectTemplateInfo>().eq("product_id", productId));
        productCopyModel.setDeviceConnectTemplateInfos(deviceConnectTemplateInfos);
        //6.面板
        List<MomgProductPanelInfo> productPanelInfos = productPanelInfoService.selectJoinList(MomgProductPanelInfo.class
                , new MPJLambdaWrapper<MomgProductPanelInfo>()
                        .selectAll(MomgProductPanelInfo.class)
                        .selectCollection(MomgProductPanelNode.class, MomgProductPanelInfo::getNodeList)
                        .leftJoin(MomgProductPanelNode.class, MomgProductPanelNode::getTopoId, MomgProductPanelInfo::getId)
                        .eq(MomgProductPanelInfo::getProductId, productId));
        List<MomgProductPanelNode> productPanelNodes = new ArrayList<>();
        productCopyModel.setProductPanelInfos(productPanelInfos);
        productCopyModel.setProductPanelNodes(productPanelNodes);
        //7.产品配置
        List<MomgProductConfigureManage> productConfigureManages = productConfigureManageService
                .list(new LambdaQueryWrapper<MomgProductConfigureManage>().eq(MomgProductConfigureManage::getProductId, productId));
        productCopyModel.setProductConfigureManages(productConfigureManages);
        //8.功能定义
        List<DeviceControlCommand> deviceControlCommands = deviceControlCommandService.selectJoinList(DeviceControlCommand.class, new MPJLambdaWrapper<DeviceControlCommand>()
                .selectAll(DeviceControlCommand.class)
                .selectCollection(DeviceControlCommandExtend.class, DeviceControlCommand::getDeviceControlCommandExtendList)
                .leftJoin(DeviceControlCommandExtend.class, DeviceControlCommandExtend::getCommandId, DeviceControlCommand::getId)
                .eq(DeviceControlCommand::getProductId, productId));
        productCopyModel.setDeviceControlCommands(deviceControlCommands);
        //9.功能定义参数
        List<DeviceControlCommandExtend> deviceControlCommandExtends = new ArrayList<>();
        productCopyModel.setDeviceControlCommandExtendList(deviceControlCommandExtends);
        if (isCopy) {
            //产品复制，产生新的产品，重新生成id
            String currentDbId = UUID.fastUUID().toString(true);
            //生成一个新的产品id
            productModel.setId(currentDbId);
            if (CollUtil.isNotEmpty(productTransferJobList)) {
                productTransferJobList.forEach(item -> {
                    item.setId(null);
                    item.setProductId(currentDbId);
                });
            }
            //物模型生成新的id
            List<ProertyMetadata> metadataList = proertyMetadataService.findMetadataByProIdAndTransfer(productId, "");
            if (CollUtil.isNotEmpty(metadataList)) {
                metadataList.forEach(item -> {
                    item.setProductId(productModel.getId());
                    item.setId(UUID.fastUUID().toString(true));
                    if (StringUtils.isNotEmpty(item.getChainId())) {
                        item.setChainId(productModel.getName() + "_" + item.getChainId());
                    }
                    allList.add(item);
                    final List<ProertyMetadata> childList = item.getProertyMetadataList();
                    if (CollUtil.isNotEmpty(item.getProertyMetadataList())) {
                        childList.forEach(child -> {
                            child.setId(null);
                            child.setPid(item.getId());
                            child.setProductId(productModel.getId());
                            if (StringUtils.isNotEmpty(child.getChainId())) {
                                child.setChainId(productModel.getName() + "_" + child.getChainId());
                            }
                        });
                        allList.addAll(childList);
                    }
                });
            }
            //流程生成新的id
            final Map<String, String> collect = allList.stream()
                    .filter(item -> StringUtils.isNotEmpty(item.getChainId()))
                    .collect(Collectors.toMap(ProertyMetadata::getChainId, ProertyMetadata::getId));
            if (CollUtil.isNotEmpty(chainList)) {
                chainList.forEach(chain -> {
                    chain.setId(null);
                    chain.setChainId(productModel.getName() + "_" + chain.getChainId());
                    chain.setProductId(productModel.getId());
                    chain.setMetadataId(collect.get(chain.getChainId()));
                });
            }
            //连接参数生成新的id
            if (CollUtil.isNotEmpty(deviceConnectTemplateInfos)) {
                deviceConnectTemplateInfos.forEach(item -> {
                    item.setId(null);
                    item.setProductId(productModel.getId());
                });
            }
            //面板生成新的id
            if (CollUtil.isNotEmpty(productPanelInfos)) {
                productPanelInfos.forEach(item -> {
                    String topoId = UUID.fastUUID().toString(true);
                    item.setId(topoId);
                    item.setProductId(productModel.getId());
                    List<MomgProductPanelNode> nodeList = item.getNodeList();
                    if (CollUtil.isNotEmpty(nodeList)) {
                        panelNodesRebuild(nodeList, topoId, null);
                        productPanelNodes.addAll(nodeList);
                    }
                    item.setNodeList(null);
                });
            }
            //配置管理生成新的id
            if (CollUtil.isNotEmpty(productConfigureManages)) {
                productConfigureManages.forEach(item -> {
                    item.setId(null);
                    item.setProductId(productModel.getId());
                });
            }
            //方法定义产生新的id
            if (CollUtil.isNotEmpty(deviceControlCommands)) {
                deviceControlCommands.forEach(item -> {
                    String fid = UUID.fastUUID().toString(true);
                    item.setId(fid);
                    item.setProductId(productModel.getId());
                    List<DeviceControlCommandExtend> deviceControlCommandExtendList = item.getDeviceControlCommandExtendList();
                    if(CollUtil.isNotEmpty(deviceControlCommandExtendList)){
                        deviceControlCommandExtendList.forEach(extend -> {
                            extend.setId(null);
                            extend.setCommandId(fid);
                            deviceControlCommandExtends.add(extend);
                        });
                    }
                });
            }
        } else {
            List<ProertyMetadata> metadataList = proertyMetadataService.list(new LambdaQueryWrapper<ProertyMetadata>()
                    .eq(ProertyMetadata::getProductId, productId));
            if (CollUtil.isNotEmpty(metadataList)) {
                allList.addAll(metadataList);
            }

            if (CollUtil.isNotEmpty(productPanelInfos)) {
                productPanelInfos.forEach(item -> {
                    List<MomgProductPanelNode> nodeList = item.getNodeList();
                    if (CollUtil.isNotEmpty(nodeList)) {
                        productPanelNodes.addAll(nodeList);
                    }
                    item.setNodeList(null);
                });
            }
            if (CollUtil.isNotEmpty(deviceControlCommands)) {
                deviceControlCommands.forEach(item -> {
                    List<DeviceControlCommandExtend> deviceControlCommandExtendList = item.getDeviceControlCommandExtendList();
                    if(CollUtil.isNotEmpty(deviceControlCommandExtendList)){
                        deviceControlCommandExtends.addAll(deviceControlCommandExtendList);
                    }
                });
            }
        }
        return productCopyModel;
    }

    private static String panelNodesRebuild(List<MomgProductPanelNode> nodeList, String topoId, String parentId) {
        StringBuilder nodeChildren = new StringBuilder();
        for (MomgProductPanelNode node : nodeList) {
            String currParentId = node.getId();
            node.setTopoId(topoId);
            String id = UUID.fastUUID().toString(true);
            node.setId(id);
            if (StringUtils.isEmpty(node.getNodeParent()) && StringUtils.isEmpty(parentId)) {
                String nc = panelNodesRebuild(nodeList, topoId, id);
                node.setNodeChildren(JSON.toJSONString(nc.split(",")));
            } else if (StringUtils.equals(node.getNodeParent(), currParentId)) {
                nodeChildren.append(id);
                nodeChildren.append(",");
            }
        }
        return nodeChildren.toString();
    }
}
