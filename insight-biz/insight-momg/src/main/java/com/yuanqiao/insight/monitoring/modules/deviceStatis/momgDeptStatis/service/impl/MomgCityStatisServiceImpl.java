package com.yuanqiao.insight.monitoring.modules.deviceStatis.momgDeptStatis.service.impl;

//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
//import com.yuanqiao.insight.service.momgDeptStatis.entity.CityStatisVo;
//import com.yuanqiao.insight.service.momgDeptStatis.mapper.MomgCityStatisMapper;
//import com.yuanqiao.insight.service.momgDeptStatis.service.ICityStatisService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.monitoring.modules.deviceStatis.momgDeptStatis.entity.CityStatisVo;
import com.yuanqiao.insight.monitoring.modules.deviceStatis.momgDeptStatis.mapper.MomgCityStatisMapper;
import com.yuanqiao.insight.monitoring.modules.deviceStatis.momgDeptStatis.service.ICityStatisService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class MomgCityStatisServiceImpl extends ServiceImpl<MomgCityStatisMapper, CityStatisVo> implements ICityStatisService {



}
