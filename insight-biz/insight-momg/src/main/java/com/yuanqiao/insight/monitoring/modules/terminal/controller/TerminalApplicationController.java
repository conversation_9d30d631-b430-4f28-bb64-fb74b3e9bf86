package com.yuanqiao.insight.monitoring.modules.terminal.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalApplication;
import com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice;
import com.yuanqiao.insight.monitoring.modules.terminal.mapper.TerminalDeviceMapper;
import com.yuanqiao.insight.monitoring.modules.terminal.service.ITerminalApplicationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 终端应用
 * @Author: jeecg-boot
 * @Date: 2024-09-29
 * @Version: V1.0
 */
@Api(tags = "终端应用")
@RestController
@RequestMapping("/terminal/application")
@Slf4j
public class TerminalApplicationController extends JeecgController<TerminalApplication, ITerminalApplicationService> {
    @Autowired
    private ITerminalApplicationService terminalApplicationService;
    @Autowired
    private TerminalDeviceMapper terminalDeviceMapper;
    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 终端下拉列表
     *
     * @return
     */
    @ApiOperation(value = "终端下拉列表", notes = "终端下拉列表")
    @GetMapping(value = "/queryInfoName")
    public Result<?> queryInfoName(TerminalDevice terminalDevice) {
        List<JSONObject> resultData = terminalDeviceMapper.queryInfoName(terminalDevice);
        return Result.OK(resultData);
    }

    /**
     * 分页列表查询
     *
     * @param terminalApplication
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "终端应用-分页列表查询")
    @ApiOperation(value = "终端应用-分页列表查询", notes = "终端应用-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<TerminalApplication>> queryPageList(TerminalApplication terminalApplication,
                                                            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                            HttpServletRequest req) {
        QueryWrapper<TerminalApplication> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotEmpty(terminalApplication.getOsType())) {
            queryWrapper.eq("os_type", terminalApplication.getOsType());
        }
        if (StringUtils.isNotEmpty(terminalApplication.getDeptId())) {
            queryWrapper.eq("dept_id", terminalApplication.getOsType());
        }
        Page<TerminalApplication> page = new Page<TerminalApplication>(pageNo, pageSize);
        IPage<TerminalApplication> pageList = terminalApplicationService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param terminalApplication
     * @return
     */
    @AutoLog(value = "终端应用-添加")
    @ApiOperation(value = "终端应用-添加", notes = "终端应用-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody TerminalApplication terminalApplication) {
        if (StringUtils.isNotEmpty(terminalApplication.getApplicationName())) {
            terminalApplication.getApplicationName().replace("，", ",");
        }
        if (terminalApplication.getScopeType() == 0) {
            TerminalDevice terminalDevice = new TerminalDevice();
            if (StringUtils.isNotEmpty(terminalApplication.getOsType())) {
                terminalDevice.setOsType(terminalApplication.getOsType());
            }
            if (StringUtils.isNotEmpty(terminalApplication.getDeptId())) {
                terminalDevice.setDeptId(terminalApplication.getDeptId());
            }
            List<JSONObject> resultData = terminalDeviceMapper.queryInfoName(terminalDevice);
            if (!CollectionUtils.isEmpty(resultData)) {
                List<String> uniqueCodes = resultData.stream()
                        .filter(jsonObject -> jsonObject.containsKey("uniqueCode"))
                        .map(jsonObject -> jsonObject.getString("uniqueCode"))
                        .collect(Collectors.toList());
                String uniqueCodesString = uniqueCodes.stream()
                        .collect(Collectors.joining(","));
                terminalApplication.setUniqueCode(uniqueCodesString);
            }
        }
        terminalApplicationService.save(terminalApplication);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param terminalApplication
     * @return
     */
    @AutoLog(value = "终端应用-编辑")
    @ApiOperation(value = "终端应用-编辑", notes = "终端应用-编辑")
    @PostMapping(value = "/edit")
    public Result<String> edit(@RequestBody TerminalApplication terminalApplication) {
        if (StringUtils.isNotEmpty(terminalApplication.getApplicationName())) {
            terminalApplication.getApplicationName().replace("，", ",");
        }
        if (terminalApplication.getScopeType() == 0) {
            TerminalDevice terminalDevice = new TerminalDevice();
            if (StringUtils.isNotEmpty(terminalApplication.getOsType())) {
                terminalDevice.setOsType(terminalApplication.getOsType());
            }
            if (StringUtils.isNotEmpty(terminalApplication.getDeptId())) {
                terminalDevice.setDeptId(terminalApplication.getDeptId());
            }
            List<JSONObject> resultData = terminalDeviceMapper.queryInfoName(terminalDevice);
            if (!CollectionUtils.isEmpty(resultData)) {
                List<String> uniqueCodes = resultData.stream()
                        .filter(jsonObject -> jsonObject.containsKey("uniqueCode"))
                        .map(jsonObject -> jsonObject.getString("uniqueCode"))
                        .collect(Collectors.toList());
                String uniqueCodesString = uniqueCodes.stream()
                        .collect(Collectors.joining(","));
                terminalApplication.setUniqueCode(uniqueCodesString);
            }
        }
        terminalApplicationService.updateById(terminalApplication);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "终端应用-通过id删除")
    @ApiOperation(value = "终端应用-通过id删除", notes = "终端应用-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        terminalApplicationService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "终端应用-批量删除")
    @ApiOperation(value = "终端应用-批量删除", notes = "终端应用-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.terminalApplicationService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "终端应用-通过id查询")
    @ApiOperation(value = "终端应用-通过id查询", notes = "终端应用-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        TerminalApplication TerminalApplication = terminalApplicationService.getById(id);
        if (TerminalApplication == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(TerminalApplication);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param terminalApplication
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, TerminalApplication terminalApplication) {
        return super.exportXls(request, terminalApplication, TerminalApplication.class, "终端应用");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, TerminalApplication.class);
    }

    /**
     * 获取终端内应用的名字列表
     *
     * @param uniqueCode
     * @return
     */
    @GetMapping(value = "/queryApplication")
    public Result<?> queryApplication(String uniqueCode) {
        String redisKey = "stca:" + uniqueCode + "_" + "application";
        List<String> nameList = new ArrayList<>();
        if (redisTemplate.hasKey(redisKey)) {
            JSONObject jsonObject = JSONObject.parseObject((String) redisTemplate.opsForValue().get(redisKey));
            if (jsonObject.getString("code").equals("application")) {
                JSONArray array = jsonObject.getJSONArray("value");
                for (int j = 0; j < array.size(); j++) {
                    JSONObject softwareInfo = array.getJSONObject(j).getJSONObject("name");
                    if (softwareInfo.getString("name").equals("软件名称")) {
                        String softwareName = softwareInfo.getString("value");
                        nameList.add(softwareName);
                    }
                }
            }
        }
        return Result.OK(nameList);
    }
}
