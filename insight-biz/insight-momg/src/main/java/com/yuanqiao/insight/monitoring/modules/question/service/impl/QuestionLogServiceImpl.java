package com.yuanqiao.insight.monitoring.modules.question.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.monitoring.modules.question.entity.QuestionLog;
import com.yuanqiao.insight.monitoring.modules.question.mapper.QuestionLogMapper;
import com.yuanqiao.insight.monitoring.modules.question.service.IQuestionLogService;
import org.springframework.stereotype.Service;

/**
 * @Description: 问题处理记录
 * @Author: jeecg-boot
 * @Date:   2020-03-27
 * @Version: V1.0
 */
@Service
public class QuestionLogServiceImpl extends ServiceImpl<QuestionLogMapper, QuestionLog> implements IQuestionLogService {

}
