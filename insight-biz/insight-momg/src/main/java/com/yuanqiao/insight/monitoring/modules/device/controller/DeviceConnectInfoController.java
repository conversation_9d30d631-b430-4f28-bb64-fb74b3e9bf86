package com.yuanqiao.insight.monitoring.modules.device.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.service.device.entity.DeviceConnectInfo;
import com.yuanqiao.insight.service.device.service.IDeviceConnectInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: 设备连接参数
 * @Author: jeecg-boot
 * @Date:   2021-03-04
 * @Version: V1.0
 */
@Api(tags="设备连接参数")
@RestController
@RequestMapping("/device/deviceConnectInfo")
@Slf4j
public class DeviceConnectInfoController extends JeecgController<DeviceConnectInfo, IDeviceConnectInfoService> {
	@Autowired
	private IDeviceConnectInfoService deviceConnectInfoService;

	/**
	 * 分页列表查询
	 *
	 * @param deviceConnectInfo
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "设备连接参数-分页列表查询")
	@ApiOperation(value="设备连接参数-分页列表查询", notes="设备连接参数-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(DeviceConnectInfo deviceConnectInfo,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		log.info("deviceConnectInfo={}", deviceConnectInfo);
		log.info("com.yuanqiao.insight.monitoring.modules.device.controller.DeviceConnectInfoController.controller.list(deviceConnectInfo={})", deviceConnectInfo);

		QueryWrapper<DeviceConnectInfo> queryWrapper = QueryGenerator.initQueryWrapper(deviceConnectInfo, req.getParameterMap());
		Page<DeviceConnectInfo> page = new Page<DeviceConnectInfo>(pageNo, pageSize);
		IPage<DeviceConnectInfo> pageList = deviceConnectInfoService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param deviceConnectInfo
	 * @return
	 */
	@AutoLog(value = "设备连接参数-添加")
	@ApiOperation(value="设备连接参数-添加", notes="设备连接参数-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody DeviceConnectInfo deviceConnectInfo) {
		log.info("deviceConnectInfo={}", deviceConnectInfo);
		log.info("com.yuanqiao.insight.monitoring.modules.device.controller.DeviceConnectInfoController.controller.add(deviceConnectInfo={})", deviceConnectInfo);

		deviceConnectInfoService.save(deviceConnectInfo);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param deviceConnectInfo
	 * @return
	 */
	@AutoLog(value = "设备连接参数-编辑")
	@ApiOperation(value="设备连接参数-编辑", notes="设备连接参数-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody DeviceConnectInfo deviceConnectInfo) {
		log.info("deviceConnectInfo={}", deviceConnectInfo);
		log.info("com.yuanqiao.insight.monitoring.modules.device.controller.DeviceConnectInfoController.controller.edit(deviceConnectInfo={})", deviceConnectInfo);

		deviceConnectInfoService.updateById(deviceConnectInfo);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "设备连接参数-通过id删除")
	@ApiOperation(value="设备连接参数-通过id删除", notes="设备连接参数-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		log.info("id=''", id);
		log.info("com.yuanqiao.insight.monitoring.modules.device.controller.DeviceConnectInfoController.controller.delete(id='')", id);

		deviceConnectInfoService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "设备连接参数-批量删除")
	@ApiOperation(value="设备连接参数-批量删除", notes="设备连接参数-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		log.info("ids=''", ids);
		log.info("com.yuanqiao.insight.monitoring.modules.device.controller.DeviceConnectInfoController.controller.deleteBatch(ids='')", ids);

		this.deviceConnectInfoService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "设备连接参数-通过id查询")
	@ApiOperation(value="设备连接参数-通过id查询", notes="设备连接参数-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		log.info("id=''", id);
		log.info("com.yuanqiao.insight.monitoring.modules.device.controller.DeviceConnectInfoController.controller.queryById(id='')", id);

		DeviceConnectInfo deviceConnectInfo = deviceConnectInfoService.getById(id);
		if(deviceConnectInfo==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(deviceConnectInfo);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param deviceConnectInfo
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, DeviceConnectInfo deviceConnectInfo) {
		log.info("deviceConnectInfo={}", deviceConnectInfo);
		log.info("com.yuanqiao.insight.monitoring.modules.device.controller.DeviceConnectInfoController.controller.exportXls(deviceConnectInfo={})", deviceConnectInfo);

        return super.exportXls(request, deviceConnectInfo, DeviceConnectInfo.class, "设备连接参数");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, DeviceConnectInfo.class);
    }

}
