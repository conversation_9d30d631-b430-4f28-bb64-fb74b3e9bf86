package com.yuanqiao.insight.monitoring.modules.topoRoom.service.impl;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.monitoring.modules.topoCabinet.entity.TopoCabinet;
import com.yuanqiao.insight.monitoring.modules.topoCabinet.service.ITopoCabinetService;
import com.yuanqiao.insight.monitoring.modules.topoCabinet2device.entity.TopoCabinet2device;
import com.yuanqiao.insight.monitoring.modules.topoCabinet2device.service.ITopoCabinet2deviceService;
import com.yuanqiao.insight.monitoring.modules.topoRoom.entity.TopoRoom;
import com.yuanqiao.insight.monitoring.modules.topoRoom.mapper.TopoRoomMapper;
import com.yuanqiao.insight.monitoring.modules.topoRoom.service.ITopoRoomService;
import com.yuanqiao.insight.service.device.entity.DeviceInfo;
import com.yuanqiao.insight.service.device.service.IDeviceInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 机房表
 * @Author: jeecg-boot
 * @Date: 2021-05-13
 * @Version: V1.0
 */
@Service
public class TopoRoomServiceImpl extends ServiceImpl<TopoRoomMapper, TopoRoom> implements ITopoRoomService {
    @Autowired
    private ITopoCabinetService topoCabinetService;
    @Autowired
    private ITopoRoomService topoRoomService;
    @Autowired
    private IDeviceInfoService deviceInfoService;
    @Autowired
    private ITopoCabinet2deviceService topoCabinet2deviceService;

    @Override
    public JSONArray topoRoomTree() {

        JSONArray roomArray = new JSONArray();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("id", "1392682158924140545");
        jsonObject.put("name", "数据中心");
        jsonObject.put("type", "city");
        JSONArray room = getRoom();
        jsonObject.put("children", room);

        roomArray.add(jsonObject);
        //System.out.println(roomArray);
        return roomArray;
    }

    @Override
    public void deleteRoom(String id) {
        if (id == null){
            return;
        }
        topoRoomService.removeById(id);
    }

    @Autowired
    private TopoRoomMapper topoRoomMapper;
    @Transactional
    public void testTx(String id){
        topoRoomMapper.deleteById(id);
        int a = 1 / 0;
    }

    @Override
    public List<TopoRoom> lists() {
        return   topoRoomService.list();
    }

    //机房
    public JSONArray getRoom() {
        List<TopoRoom> rooms = topoRoomService.list();
        String id = null;
        for (TopoRoom room : rooms) {
            if (room.getName().equals("数据中心")) {
                id = room.getId();
            }


        }
        JSONArray jsonArray = new JSONArray();
        for (TopoRoom room : rooms) {
            if (room.getPid().equals(id)) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("id", room.getId());
                jsonObject.put("name", room.getName());
                jsonObject.put("type", room.getType());
                JSONArray cabinet = getCabinet(room.getId());
                jsonObject.put("children", cabinet);
                jsonArray.add(jsonObject);
            }

        }
        return jsonArray;
    }

    //机柜
    public JSONArray getCabinet(String roomId) {
        List<TopoCabinet> cabinets = topoCabinetService.list(new QueryWrapper<TopoCabinet>().eq("room_id", roomId));
        JSONArray jsonArray = new JSONArray();
        for (TopoCabinet cabinet : cabinets) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", cabinet.getId());
            jsonObject.put("name", cabinet.getName());
            jsonObject.put("type", "cabinet");
            JSONArray device = getDevice(cabinet.getId());
            jsonObject.put("children", device);
            jsonArray.add(jsonObject);
        }
        return jsonArray;
    }

    //设备
    public JSONArray getDevice(String cabinetId) {
        List<TopoCabinet2device> cabinetIds = topoCabinet2deviceService.list(new QueryWrapper<TopoCabinet2device>().eq("cabinet_id", cabinetId));

        if(cabinetIds != null && !cabinetIds.isEmpty()) {
            List<String> deviceIds = new ArrayList<>();//相同机柜下的设备id
            for (TopoCabinet2device info : cabinetIds) {
                String deviceId = info.getDeviceId();
                deviceIds.add(deviceId);
            }
            JSONArray jsonArray = new JSONArray();

            for (String deviceId : deviceIds) {
                JSONObject jsonObject = new JSONObject();
                DeviceInfo deviceInfo = deviceInfoService.selectById(deviceId);
                if(deviceInfo != null) {
                    jsonObject.put("id", deviceInfo.getId());
                    jsonObject.put("name", deviceInfo.getName());
                }

                jsonArray.add(jsonObject);
            }
            return jsonArray;
        }else{
            return null;
        }
    }

}
