package com.yuanqiao.insight.monitoring.modules.alarm.entity;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 告警规则
 * @Author: jeecg-boot
 * @Date: 2021-03-03
 * @Version: V1.0
 */
@Data
@TableName("momg_alarm_rule")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "alarm_rule对象", description = "告警规则")
public class AlarmRule implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    /**
     * 告警模板ID
     */
    @Excel(name = "告警模板ID", width = 15)
    @ApiModelProperty(value = "告警模板ID")
    private String alarmTemplateId;
    /**
     * 指标
     */
    @Excel(name = "指标", width = 15)
    @ApiModelProperty(value = "指标")
    private String subjectIndex;

    /**
     * 指标id
     */
    @ApiModelProperty(value = "指标id")
    private String metadataId;
    /**
     * 条件
     */
    @Excel(name = "条件", width = 15)
    @ApiModelProperty(value = "条件")
    private String conditions;
    /**
     * 内容
     */
    @Excel(name = "内容", width = 15)
    @ApiModelProperty(value = "内容")
    private String contents;
    /**
     * 指标匹配键
     */
    @Excel(name = "指标匹配键", width = 15)
    @ApiModelProperty(value = "指标匹配键")
    private String specifyKey;
    /**
     * 指标匹配值
     */
    @Excel(name = "指标匹配值", width = 15)
    @ApiModelProperty(value = "指标匹配值")
    private String specifyValue;

    @TableField(exist = false)
    private String parentMetadataCode;

    @TableField(exist = false)
    private String subjectIndexText;

    @TableField(exist = false)
    private String conditionsText;

    public JSONArray getContentJson() {
        return JSONArray.parseArray(this.contents);
    }

}
