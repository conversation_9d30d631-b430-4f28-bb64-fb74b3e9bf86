package com.yuanqiao.insight.monitoring.modules.topoRoom.service;


import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yuanqiao.insight.monitoring.modules.topoRoom.entity.TopoRoom;

import java.util.List;

/**
 * @Description: 机房表
 * @Author: jeecg-boot
 * @Date:   2021-05-13
 * @Version: V1.0
 */
public interface ITopoRoomService extends IService<TopoRoom> {

    /**
     * 机房树
     * @return
     */
    JSONArray topoRoomTree();

    /**
     * 删除房间
     * @param id
     */
    void deleteRoom(String id);



    List<TopoRoom> lists();


}
