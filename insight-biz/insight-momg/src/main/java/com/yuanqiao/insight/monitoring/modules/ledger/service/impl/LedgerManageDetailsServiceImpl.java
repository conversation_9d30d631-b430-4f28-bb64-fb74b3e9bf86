package com.yuanqiao.insight.monitoring.modules.ledger.service.impl;

import com.yuanqiao.insight.monitoring.modules.ledger.entity.LedgerManageRegionVo;
import com.yuanqiao.insight.monitoring.modules.ledger.entity.LedgerManageVO;
import com.yuanqiao.insight.monitoring.modules.ledger.entity.LedgerMonth;
import com.yuanqiao.insight.monitoring.modules.ledger.service.ILedgerManageDetailsService;
import com.yuanqiao.insight.monitoring.modules.ledger.service.ILedgerManageService;
import com.yuanqiao.insight.monitoring.modules.ledger.service.ILedgerMonthService;
import com.yuanqiao.insight.monitoring.modules.ledger.service.ILedgerPlanService;
import io.micrometer.core.instrument.util.StringUtils;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.util.DateUtils;
import com.yuanqiao.insight.acore.system.service.ISysDictService;
import com.yuanqiao.insight.acore.system.vo.SysArea;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.NumberFormat;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: LedgerManageDetailsServiceImpl
 * @projectName ledger-boot-parent
 * @description: TODO
 * @date 2021/4/9-11:15
 */
@Service
public class LedgerManageDetailsServiceImpl implements ILedgerManageDetailsService {

    @Autowired
    private ILedgerPlanService ledgerPlanService;
    @Autowired
    private ILedgerManageService ledgerManageService;
    @Autowired
    private ILedgerMonthService ledgerMonthService;
    @Autowired
    private ISysDictService sysDictService;

    /**
     * 全市设备新增
     *
     * @param cityProperId 地区id
     * @param assetsType   资产类型
     * @return
     */
    @Override
    public Map<String, Object> newAssetCity(String cityProperId, String assetsType) throws Exception {

        Integer planTotal = 0;//计划新增总数
        Map<String, Double> pie = new HashMap<>(2);// 上线率
        Double purchasedRate = 0.0;//已采购率
        Double issuedCountRate = 0.0;//已下发率
        Integer allNotOnline = 0;//没有上线
        Integer purchasedCount = 0;//已采购
        Integer issuedCount = 0;//已下发
        Integer deployedCount = 0;//已部署
        Integer onlineCount = 0;//已上线
        //获取到模糊查询的地区id
        //获取区县
        String cityProperIdL = null;

        if (!"100000".equals(cityProperId)) {
            cityProperIdL = sysDictService.getcityProperIdL(cityProperId);
        }
        //获取今年台账计划的map
        Map planMap = getLedgerPlanByYearAndRegion(String.valueOf(DateUtils.getYear()), cityProperIdL);
        if (null != planMap) {
            //计划新增总数
            switch (assetsType) {
                case CommonConstant.ASSET_TYPE_SERVER: //服务器
                    planTotal = null != planMap.get("SERVERDOWNNUM").toString() ? Integer.parseInt(planMap.get("SERVERDOWNNUM").toString()) : 0;
                    break;
                case CommonConstant.ASSET_TYPE_TERMINAL: //终端
                    planTotal = null != planMap.get("TERMINALDOWNNUM").toString() ? Integer.parseInt(planMap.get("TERMINALDOWNNUM").toString()) : 0;
                    break;
                case CommonConstant.ASSET_TYPE_PRINTER:  //打印机
                    planTotal = null != planMap.get("PRINTERDOWNNUM").toString() ? Integer.parseInt(planMap.get("PRINTERDOWNNUM").toString()) : 0;
                    break;
                case CommonConstant.ASSET_TYPE_DATA: //数据库
                    planTotal = null != planMap.get("DATADOWNNUM").toString() ? Integer.parseInt(planMap.get("DATADOWNNUM").toString()) : 0;
                    break;
                case CommonConstant.ASSET_TYPE_APP:  //应用服务器
                    planTotal = null != planMap.get("APPDOWNNUM").toString() ? Integer.parseInt(planMap.get("APPDOWNNUM").toString()) : 0;
                    break;
                default:
                    planTotal = 0;
            }
            //获取今年的资产状态map
            Map<String, LedgerManageVO> ledgerManageVOMap = getLedgerManageByTimeAndRegion(String.valueOf(DateUtils.getYear()), cityProperIdL, CommonConstant.ADD_TYPE_ONE, assetsType);
            purchasedCount = null == ledgerManageVOMap.get(CommonConstant.ASSET_QUERY_STATUS_YCG) ? 0 : ledgerManageVOMap.get(CommonConstant.ASSET_QUERY_STATUS_YCG).getNum();
            issuedCount = null == ledgerManageVOMap.get(CommonConstant.ASSET_QUERY_STATUS_YXF) ? 0 : ledgerManageVOMap.get(CommonConstant.ASSET_QUERY_STATUS_YXF).getNum();
            deployedCount = null == ledgerManageVOMap.get(CommonConstant.ASSET_QUERY_STATUS_YBS) ? 0 : ledgerManageVOMap.get(CommonConstant.ASSET_QUERY_STATUS_YBS).getNum();
            onlineCount = null == ledgerManageVOMap.get(CommonConstant.ASSET_QUERY_STATUS_YSX) ? 0 : ledgerManageVOMap.get(CommonConstant.ASSET_QUERY_STATUS_YSX).getNum();
            //没有上线
            allNotOnline = purchasedCount + issuedCount + deployedCount;
            //总数
            if (0 != planTotal) {
                NumberFormat numberFormat = NumberFormat.getInstance();
                numberFormat.setMaximumFractionDigits(2);
                purchasedRate = Double.valueOf(numberFormat.format((purchasedCount * 1.0 / planTotal) * 100));
                issuedCountRate = Double.valueOf(numberFormat.format((issuedCount * 1.0 / planTotal) * 100));
                pie.put("已上线", Double.valueOf(numberFormat.format((onlineCount * 1.0 / planTotal) * 100)));
                pie.put("未上线", 100 - Double.valueOf(numberFormat.format((onlineCount * 1.0 / planTotal) * 100)));
            } else {
                pie.put("已上线", 0.0);
                pie.put("未上线", 0.0);
            }
        }
        //返回值
        Map<String, Object> map = new HashMap<>();
        map.put("planTotal", planTotal);
        map.put("pie", pie);
        map.put("purchasedRate", purchasedRate);
        map.put("issuedCountRate", issuedCountRate);
        map.put("allNotOnline", allNotOnline);
        map.put("purchasedCount", purchasedCount);
        map.put("issuedCount", issuedCount);
        map.put("deployedCount", deployedCount);
        map.put("onlineCount", onlineCount);
        return map;
    }

    /**
     * 原有设备替换
     *
     * @param cityProperId 地区id
     * @param assetsType   资产类型
     * @return
     */
    @Override
    public Map<String, Object> assetOriginalEquipment(String cityProperId, String assetsType) throws Exception {
        Integer planRepacleTotal = 0;//计划替换
        Integer replaceTotal = 0;//已替换
        Double replaceRate = 0.0;//替换率
        Integer temporarilyCount = 0;//已暂存数
        Integer transferredCount = 0;//已转移数
        Integer destroyedCount = 0;//已销毁数
        Map<String, Double> pie = new HashMap<>(3);// 上线率
        //获取到模糊查询的地区id
        //获取区县
        String cityProperIdL = null;

        if (!"100000".equals(cityProperId)) {
            cityProperIdL = sysDictService.getcityProperIdL(cityProperId);
        }
        //获取今年台账计划的map
        Map planMap = getLedgerPlanByYearAndRegion(String.valueOf(DateUtils.getYear()), cityProperIdL);
        if (null != planMap) {
            //计划新增总数
            switch (assetsType) {
                case CommonConstant.ASSET_TYPE_SERVER: //服务器
                    planRepacleTotal = null != planMap.get("SERVERREPLACENUM").toString() ? Integer.parseInt(planMap.get("SERVERREPLACENUM").toString()) : 0;
                    break;
                case CommonConstant.ASSET_TYPE_TERMINAL: //终端
                    planRepacleTotal = null != planMap.get("TERMINALREPLACENUM").toString() ? Integer.parseInt(planMap.get("TERMINALREPLACENUM").toString()) : 0;
                    break;
                case CommonConstant.ASSET_TYPE_PRINTER:  //打印机
                    planRepacleTotal = null != planMap.get("PRINTERREPLACENUM").toString() ? Integer.parseInt(planMap.get("PRINTERREPLACENUM").toString()) : 0;
                    break;
                case CommonConstant.ASSET_TYPE_DATA: //数据库
                    planRepacleTotal = null != planMap.get("DATAREPLACENUM").toString() ? Integer.parseInt(planMap.get("DATAREPLACENUM").toString()) : 0;
                    break;
                case CommonConstant.ASSET_TYPE_APP:  //应用服务器
                    planRepacleTotal = null != planMap.get("APPREPLACENUM").toString() ? Integer.parseInt(planMap.get("APPREPLACENUM").toString()) : 0;
                    break;
                default:
                    planRepacleTotal = 0;
            }
        }
        //获取今年的资产状态map
        Map<String, LedgerManageVO> ledgerManageVOMap = getLedgerManageByTimeAndRegion(String.valueOf(DateUtils.getYear()), cityProperIdL, CommonConstant.ADD_TYPE_TWO, assetsType);
        if (null != ledgerManageVOMap) {
            temporarilyCount = null == ledgerManageVOMap.get(CommonConstant.ASSET_QUERY_STATUS_YHC) ? 0 : ledgerManageVOMap.get(CommonConstant.ASSET_QUERY_STATUS_YHC).getNum();
            transferredCount = null == ledgerManageVOMap.get(CommonConstant.ASSET_QUERY_STATUS_YZY) ? 0 : ledgerManageVOMap.get(CommonConstant.ASSET_QUERY_STATUS_YZY).getNum();
            destroyedCount = null == ledgerManageVOMap.get(CommonConstant.ASSET_QUERY_STATUS_YXH) ? 0 : ledgerManageVOMap.get(CommonConstant.ASSET_QUERY_STATUS_YXH).getNum();
            //已替换 = 已暂存状态 + 已转移状态 + 已销毁状态
            replaceTotal = temporarilyCount + transferredCount + destroyedCount;
            if (0 != planRepacleTotal) {
                NumberFormat numberFormat = NumberFormat.getInstance();
                numberFormat.setMaximumFractionDigits(2);
                //替换率
                replaceRate = Double.valueOf(numberFormat.format((replaceTotal * 1.0 / planRepacleTotal) * 100));
            }
            if (null != replaceTotal && 0 != replaceTotal) {
                NumberFormat numberFormat = NumberFormat.getInstance();
                numberFormat.setMaximumFractionDigits(2);
                //pie
                pie.put("已暂存", Double.valueOf(numberFormat.format((temporarilyCount * 1.0 / replaceTotal) * 100)));
                pie.put("已替换", Double.valueOf(numberFormat.format((transferredCount * 1.0 / replaceTotal) * 100)));
                pie.put("已转移", Double.valueOf(numberFormat.format((destroyedCount * 1.0 / replaceTotal) * 100)));
            } else {
                pie.put("已暂存", 0.0);
                pie.put("已替换", 0.0);
                pie.put("已转移", 0.0);
            }
        }
        //返回值
        Map<String, Object> map = new HashMap<>();
        map.put("planRepacleTotal", planRepacleTotal);
        map.put("pie", pie);
        map.put("replaceTotal", replaceTotal);
        map.put("replaceRate", replaceRate);
        map.put("temporarilyCount", temporarilyCount);
        map.put("transferredCount", transferredCount);
        map.put("destroyedCount", destroyedCount);
        return map;
    }

    /**
     * 新增设备月度上线情况
     *
     * @param cityProperId 地区id
     * @param assetsType   资产类型
     * @return
     */
    @Override
    public Map<String, Object> newAssetMonthlyLaunch(String cityProperId, String assetsType) throws Exception {

        LinkedList<String> xAxis = new LinkedList();//月份
        LinkedList<Integer> issued = new LinkedList();//已下发
        LinkedList<Integer> shouldIssued = new LinkedList();//应下发
        LinkedList<Integer> deployed = new LinkedList();//已部署
        LinkedList<Integer> online = new LinkedList();//已上线
        //通过级别获取模糊查询的区域id
        //获取区县
        String cityProperIdL = null;

        if (!"100000".equals(cityProperId)) {
            cityProperIdL = sysDictService.getcityProperIdL(cityProperId);
        }
        //获取二个时间段的所有月份
        LinkedList<String> months = DateUtils.monthToDate(String.valueOf(DateUtils.getYear()) + "-01", DateUtils.getYearAndMonth());
        //获取今年的月报
        Map<String, LedgerMonth> ledgerMonthMap = getLedgerMonthMap(String.valueOf(DateUtils.getYear()), cityProperIdL, assetsType);
        for (String month : months) {
            LedgerMonth ledgerMonth = ledgerMonthMap.get(month);
            if (null != ledgerMonth) {
                issued.add(ledgerMonth.getDownNum());
                shouldIssued.add(0);
                deployed.add(ledgerMonth.getDeployNum());
                online.add(ledgerMonth.getOnlineNum());
            } else {
                issued.add(0);
                shouldIssued.add(0);
                deployed.add(0);
                online.add(0);
            }
            xAxis.add(month.replace("-", "."));
        }
        //返回值
        Map<String, Object> map = new HashMap<>();
        map.put("xAxis", xAxis);
        map.put("issued", issued);
        map.put("shouldIssued", shouldIssued);
        map.put("deployed", deployed);
        map.put("online", online);
        return map;
    }

    /**
     * 原有设备月度替换情况
     *
     * @param cityProperId 地区id
     * @param assetsType   资产类型
     * @return
     * @throws Exception
     */
    @Override
    public Map<String, Object> originalAssetMonthlyLaunch(String cityProperId, String assetsType) throws Exception {
        LinkedList<String> xAxis = new LinkedList<>();//月份
        LinkedList<Integer> temporarily = new LinkedList<>();//已暂存数
        LinkedList<Integer> transferred = new LinkedList<>();//已转移数
        LinkedList<Integer> destroyed = new LinkedList<>();//已销毁数
        LinkedList<Integer> replace = new LinkedList<>();//已替换
        //通过级别获取模糊查询的区域id
        //获取区县
        String cityProperIdL = null;

        if (!"100000".equals(cityProperId)) {
            cityProperIdL = sysDictService.getcityProperIdL(cityProperId);
        }
        //获取二个时间段的所有月份
        LinkedList<String> months = DateUtils.monthToDate(String.valueOf(DateUtils.getYear()) + "-01", DateUtils.getYearAndMonth());
        //获取今年的月报
        Map<String, LedgerMonth> ledgerMonthMap = getLedgerMonthMap(String.valueOf(DateUtils.getYear()), cityProperIdL, assetsType);
        for (String month : months) {
            LedgerMonth ledgerMonth = ledgerMonthMap.get(month);
            if (null != ledgerMonth) {
                temporarily.add(ledgerMonth.getDepositNum());
                transferred.add(ledgerMonth.getTransferNum());
                destroyed.add(ledgerMonth.getDeployNum());
                replace.add(ledgerMonth.getReplaceNum());
            } else {
                temporarily.add(0);
                transferred.add(0);
                destroyed.add(0);
                replace.add(0);
            }
            xAxis.add(month.replace("-", "."));
        }
        //返回值
        Map<String, Object> map = new HashMap<>();
        map.put("xAxis", xAxis);
        map.put("temporarily", temporarily);
        map.put("transferred", transferred);
        map.put("destroyed", destroyed);
        map.put("replace", replace);
        return map;
    }


    /**
     * 各地新增设备上线情况
     *
     * @param cityProperId 地区id
     * @param assetsType   资产类型
     * @return
     * @throws Exception
     */
    @Override
    public Map<String, Object> newAssetEverywhere(String cityProperId, String assetsType) throws Exception {
        LinkedList<String> names = new LinkedList<>();//地区名称
        LinkedList<Integer> issued = new LinkedList<>();//|已下发
        LinkedList<Integer> deployed = new LinkedList<>();//已部署
        LinkedList<Integer> online = new LinkedList<>();// 已上线
        //通过级别获取模糊查询的区域id
        //获取区县
        String cityProperIdL = null;

        if (!"100000".equals(cityProperId)) {
            cityProperIdL = sysDictService.getcityProperIdL(cityProperId);
        }
        //获取所有子区县
        List<SysArea> childrenList = sysDictService.queryAreaItemsByPid(cityProperId);
        List<LedgerManageRegionVo> ledgerManageRegionVos = getGroupByStatusRegion(String.valueOf(DateUtils.getYear()), cityProperIdL, assetsType);
        //全国的
        if ("100000".equals(cityProperId)) {
            for (SysArea sysArea : childrenList) {
                Integer issuedNum = 0;//已下发数
                Integer deployedNum = 0;//已部署数
                Integer onlineNum = 0;//已上线数
                if (null != ledgerManageRegionVos && 0 < ledgerManageRegionVos.size()) {
                    for (LedgerManageRegionVo ledgerManageRegionVo : ledgerManageRegionVos) {
                        //通过级别获取模糊查询的区域id
                        String cityProperL = sysDictService.getcityProperIdL(sysArea.getId());
                        //判断地区是否为 null
                        if (StringUtils.isNotBlank(ledgerManageRegionVo.getRegion())) {
                            if (cityProperL.equals(ledgerManageRegionVo.getRegion().substring(0, 2))) {
                                if (CommonConstant.ASSET_QUERY_STATUS_YSX.equals(ledgerManageRegionVo.getAssetKey())) {
                                    onlineNum += ledgerManageRegionVo.getNum();
                                } else if (CommonConstant.ASSET_QUERY_STATUS_YXF.equals(ledgerManageRegionVo.getAssetKey())) {
                                    issuedNum += ledgerManageRegionVo.getNum();
                                } else if (CommonConstant.ASSET_QUERY_STATUS_YBS.equals(ledgerManageRegionVo.getAssetKey())) {
                                    deployedNum += ledgerManageRegionVo.getNum();
                                }
                            }
                        }
                    }
                }
                names.add(sysArea.getText());
                issued.add(issuedNum);
                deployed.add(deployedNum);
                online.add(onlineNum);
            }
        }else{
            for (SysArea sysArea : childrenList) {
                Integer issuedNum = 0;//已下发数
                Integer deployedNum = 0;//已部署数
                Integer onlineNum = 0;//已上线数
                if (null != ledgerManageRegionVos && 0 < ledgerManageRegionVos.size()) {
                    for (LedgerManageRegionVo ledgerManageRegionVo : ledgerManageRegionVos) {
                        if (sysArea.getId().equals(ledgerManageRegionVo.getRegion())) {
                            if (CommonConstant.ASSET_QUERY_STATUS_YSX.equals(ledgerManageRegionVo.getAssetKey())) {
                                onlineNum += ledgerManageRegionVo.getNum();
                            } else if (CommonConstant.ASSET_QUERY_STATUS_YXF.equals(ledgerManageRegionVo.getAssetKey())) {
                                issuedNum += ledgerManageRegionVo.getNum();
                            } else if (CommonConstant.ASSET_QUERY_STATUS_YBS.equals(ledgerManageRegionVo.getAssetKey())) {
                                deployedNum += ledgerManageRegionVo.getNum();
                            }
                        }
                    }
                }
                names.add(sysArea.getText());
                issued.add(issuedNum);
                deployed.add(deployedNum);
                online.add(onlineNum);
            }
        }

        //返回值
        Map<String, Object> map = new HashMap<>();
        map.put("names", names);
        map.put("issued", issued);
        map.put("deployed", deployed);
        map.put("online", online);
        return map;
    }

    /**
     * 各地原有服务器替换情况
     *
     * @param cityProperId 地区id
     * @param assetsType   资产类型
     * @return
     * @throws Exception
     */
    @Override
    public Map<String, Object> originalAssetEverywhere(String cityProperId, String assetsType) throws Exception {
        LinkedList<String> names = new LinkedList<>();//地区名称
        LinkedList<Integer> temporarily = new LinkedList<>();//已暂存数
        LinkedList<Integer> transferred = new LinkedList<>();//已转移数
        LinkedList<Integer> destroyed = new LinkedList<>();//已销毁数
        //通过级别获取模糊查询的区域id
        //获取区县
        String cityProperIdL = null;

        if (!"100000".equals(cityProperId)) {
            cityProperIdL = sysDictService.getcityProperIdL(cityProperId);
        }
        //获取所有子区县
        List<SysArea> childrenList = sysDictService.queryAreaItemsByPid(cityProperId);
        List<LedgerManageRegionVo> ledgerManageRegionVos = getGroupByStatusRegion(String.valueOf(DateUtils.getYear()), cityProperIdL, assetsType);
        if ("100000".equals(cityProperId)) {
            for (SysArea sysArea : childrenList) {
                Integer temporarilyNum = 0;//已暂存数
                Integer transferredNum = 0;//已转移数
                Integer destroyedNum = 0;//已销毁数
                if (null != ledgerManageRegionVos && 0 < ledgerManageRegionVos.size()) {
                    for (LedgerManageRegionVo ledgerManageRegionVo : ledgerManageRegionVos) {
                        //通过级别获取模糊查询的区域id
                        String cityProperL = sysDictService.getcityProperIdL(sysArea.getId());
                        //判断地区是否为 null
                        if (StringUtils.isNotBlank(ledgerManageRegionVo.getRegion())) {
                            if (cityProperL.equals(ledgerManageRegionVo.getRegion().substring(0, 2))) {
                                if (CommonConstant.ASSET_QUERY_STATUS_YHC.equals(ledgerManageRegionVo.getAssetKey())) {
                                    temporarilyNum += ledgerManageRegionVo.getNum();
                                } else if (CommonConstant.ASSET_QUERY_STATUS_YZY.equals(ledgerManageRegionVo.getAssetKey())) {
                                    transferredNum += ledgerManageRegionVo.getNum();
                                } else if (CommonConstant.ASSET_QUERY_STATUS_YXH.equals(ledgerManageRegionVo.getAssetKey())) {
                                    destroyedNum += ledgerManageRegionVo.getNum();
                                }
                            }
                        }
                    }
                }
                names.add(sysArea.getText());
                temporarily.add(temporarilyNum);
                transferred.add(transferredNum);
                destroyed.add(destroyedNum);
            }
        }else{
            for (SysArea sysArea : childrenList) {
                Integer temporarilyNum = 0;//已暂存数
                Integer transferredNum = 0;//已转移数
                Integer destroyedNum = 0;//已销毁数
                if (null != ledgerManageRegionVos && 0 < ledgerManageRegionVos.size()) {
                    for (LedgerManageRegionVo ledgerManageRegionVo : ledgerManageRegionVos) {
                        if (sysArea.getId().equals(ledgerManageRegionVo.getRegion())) {
                            if (CommonConstant.ASSET_QUERY_STATUS_YHC.equals(ledgerManageRegionVo.getAssetKey())) {
                                temporarilyNum += ledgerManageRegionVo.getNum();
                            } else if (CommonConstant.ASSET_QUERY_STATUS_YZY.equals(ledgerManageRegionVo.getAssetKey())) {
                                transferredNum += ledgerManageRegionVo.getNum();
                            } else if (CommonConstant.ASSET_QUERY_STATUS_YXH.equals(ledgerManageRegionVo.getAssetKey())) {
                                destroyedNum += ledgerManageRegionVo.getNum();
                            }
                        }
                    }
                }
                names.add(sysArea.getText());
                temporarily.add(temporarilyNum);
                transferred.add(transferredNum);
                destroyed.add(destroyedNum);
            }
        }

        //返回值
        Map<String, Object> map = new HashMap<>();
        map.put("names", names);
        map.put("temporarily", temporarily);
        map.put("transferred", transferred);
        map.put("destroyed", destroyed);
        return map;
    }

    /**
     * 资产状态和所属地区分组查询资产数量
     *
     * @param time   创建时间
     * @param region 所属地区
     * @return
     */
    private List<LedgerManageRegionVo> getGroupByStatusRegion(String time, String region, String assetType) {
        return ledgerManageService.getGroupByStatusRegion(time, region, assetType);
    }

    /**
     * 获取指定日期和地区计划的数
     *
     * @param year   年
     * @param region 地区编号
     * @return
     */
    private Map getLedgerPlanByYearAndRegion(String year, String region) {
        return ledgerPlanService.getTotalType(year, region);
    }

    /**
     * 通过指定时间和地区id获取资产状态对应的数量
     *
     * @param time   时间
     * @param region 地区编号
     * @return
     */
    private Map<String, LedgerManageVO> getLedgerManageByTimeAndRegion(String time, String region, String addType, String assetType) {
        List<LedgerManageVO> ledgerManages = ledgerManageService.getLedgerManageByTimeAndRegion(time, region, addType, assetType);
        return ledgerManages.stream().collect(Collectors.toMap(LedgerManageVO::getAssetKey, LedgerManageVO -> LedgerManageVO));
    }

    /**
     * 获取按照日期去重的月报map
     *
     * @param year   年份
     * @param region 地区id
     * @return
     */
    private Map<String, LedgerMonth> getLedgerMonthMap(String year, String region, String assets) {
        List<LedgerMonth> ledgerMonthList = ledgerMonthService.queryLedgerMonthByYearAndRegion(year, region, assets);
        Map<String, LedgerMonth> sourceZero = new HashMap<>();
        Map<String, LedgerMonth> sourceOne = new HashMap<>();
        for (LedgerMonth ledgerMonth : ledgerMonthList) {
            if (0 == ledgerMonth.getSource()) {
                sourceZero.put(ledgerMonth.getTime(), ledgerMonth);
            } else {
                sourceOne.put(ledgerMonth.getTime(), ledgerMonth);
            }

        }
        sourceZero.putAll(sourceOne);
        return sourceZero;
    }

//    public static void main(String[] args) {
//        System.out.println(100 - 50.2);
//    }

}
