<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.monitoring.modules.topoRoom.mapper.TopoRoomMapper">



    <resultMap id="tree" type="com.yuanqiao.insight.monitoring.modules.topoRoom.entity.TopoRoom">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <collection property="children" select="com.yuanqiao.insight.monitoring.modules.topoRoom.mapper.TopoRoomMapper.findChildren2" column="id">

    </collection>
    </resultMap>



    <select id="findChildren2" resultMap="tree">

		 select * from  momg_topo_room where pid = #{id} and (type='city' or type='room')
    </select>


    <select id="findChildren1" resultType="com.yuanqiao.insight.monitoring.modules.topoRoom.entity.TopoRoom">
        select * from  momg_topo_room where pid = #{id} and (type='city' or type='room')
    </select>


    <select id="list" resultType="com.yuanqiao.insight.monitoring.modules.topoRoom.entity.TopoRoom">
        select * from  momg_topo_room where  (type='city' or type='room')
    </select>


    <select id="findCabinetId" resultType="com.yuanqiao.insight.service.device.entity.DeviceInfo">

                select
            mdi.*
        from
            momg_device_info mdi
        left join momg_topo_cabinet2device m on
            mdi.id = m.device_id
            where m.cabinet_id =#{cabinetId}

    </select>


</mapper>
