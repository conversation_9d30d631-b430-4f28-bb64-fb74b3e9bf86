package com.yuanqiao.insight.monitoring.modules.ledger.controller;

import com.yuanqiao.insight.monitoring.modules.ledger.entity.LedgerAreaCountVO;
import com.yuanqiao.insight.monitoring.modules.ledger.service.ILedgerOverview;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @title: LedgerOverviewController 台账总览api接口
 * @projectName ledger-boot-parent
 * @description: TODO
 * @date 2021/4/7-9:14
 */
@Slf4j
@Api(tags="台账总览")
@RestController
@RequestMapping("/ledgerOverview")


public class LedgerOverviewController {

    @Autowired
    private ILedgerOverview ledgerOverview;



    @AutoLog(value = "台账总览-计划新增")
    @ApiOperation(value="台账总览-计划新增", notes="台账总览-计划新增")
    @GetMapping(value = "/standingBookNewly")
    public Result<?> standingBookNewly(String cityProperId){
        Result result = new Result();
        try {

            result.setResult(ledgerOverview.standingBookNewly(cityProperId));
            result.setMessage("查询成功");
            return result;
        } catch (Exception e) {
            log.error("LedgerOverviewController ## standingBookNewly(cityProperId = #{})",cityProperId,e);
            result.error("查询失败");
            return result;
        }
    }


    @AutoLog(value = "台账总览-计划替换")
    @ApiOperation(value="台账总览-计划替换", notes="台账总览-计划新增")
    @GetMapping(value = "/standingBookReplace")
    public Result<?> standingBookReplace(String cityProperId){
        Result result = new Result();
        try {

            result.setResult(ledgerOverview.standingBookReplace(cityProperId));
            result.setMessage("查询成功");
            return result;
        } catch (Exception e) {
            log.error("LedgerOverviewController ## standingBookReplace(cityProperId = #{})",cityProperId,e);
            result.error("查询失败");
            return result;
        }
    }
    @AutoLog(value = "台账总览-新增设施上线率")
    @ApiOperation(value="台账总览-新增设施上线率", notes="台账总览-新增设施上线率")
    @GetMapping(value = "/assetsTypeOnlineRate")
    public Result<?> assetsTypeOnlineRate(String cityProperId){
        Result result = new Result();
        try {
            result.setResult(ledgerOverview.assetsTypeOnlineRate(cityProperId));
            result.setMessage("查询成功");
            return result;
        } catch (Exception e) {
            log.error("LedgerOverviewController ## assetsTypeOnlineRate(cityProperId = #{})",cityProperId,e);
            result.error("查询失败");
            return result;
        }
    }

    @AutoLog(value = "台账总览-新增设施上线趋势")
    @ApiOperation(value="台账总览-新增设施上线趋势", notes="台账总览-新增设施上线趋势")
    @GetMapping(value = "/assetsTypeTrend")
    public Result<?> assetsTypeTrend(String cityProperId,String assetsType){
        Result result = new Result();
        try {
            result.setResult(ledgerOverview.assetsTypeTrend(cityProperId,assetsType));
            result.setMessage("查询成功");
            return result;
        } catch (Exception e) {
            log.error("LedgerOverviewController ## assetsTypeTrend(cityProperId = #{})",cityProperId,e);
            result.error("查询失败");
            return result;
        }
    }

    @AutoLog(value = "台账总览-各地设施情况")
    @ApiOperation(value="台账总览-各地设施情况", notes="台账总览-各地设施情况")
    @GetMapping(value = "/localFacilities")
    public Result<?> localFacilities(String cityProperId){
        Result result = new Result();
        try {
            result.setResult(ledgerOverview.localFacilities(cityProperId));
            result.setMessage("查询成功");
            return result;
        } catch (Exception e) {
            log.error("LedgerOverviewController ## localFacilities(cityProperId = #{})",cityProperId,e);
            result.error("查询失败");
            return result;
        }
    }

    @AutoLog(value = "台账总览-原有设施替换率")
    @ApiOperation(value="台账总览-原有设施替换率", notes="台账总览-原有设施替换率")
    @GetMapping(value = "/assetsTypeReplaceRate")
    public Result<?> assetsTypeReplaceRate(String cityProperId){
        Result result = new Result();
        try {
            result.setResult(ledgerOverview.assetsTypeReplaceRate(cityProperId));
            result.setMessage("查询成功");
            return result;
        } catch (Exception e) {
            log.error("LedgerOverviewController ## assetsTypeReplaceRate(cityProperId = #{})",cityProperId,e);
            result.error("查询失败");
            return result;
        }
    }

    @AutoLog(value = "台账总览-原有设施替换进度")
    @ApiOperation(value="台账总览-原有设施替换进度", notes="台账总览-原有设施替换进度")
    @GetMapping(value = "/originalReplaceSchedule")
    public Result<?> originalReplaceSchedule(String cityProperId,String assetsType){
        Result result = new Result();
        try {
            result.setResult(ledgerOverview.originalReplaceSchedule(cityProperId,assetsType));
            result.setMessage("查询成功");
            return result;
        } catch (Exception e) {
            log.error("LedgerOverviewController ## assetsTypeReplaceRate(cityProperId = #{})",cityProperId,e);
            result.error("查询失败");
            return result;
        }
    }

    /**
     * 台账大屏各子区域数据获取-地图
     * @param cityProperId 父级区域编码
     * @param type 地图显示数据类型（onlineList上线进度/replaceList替换进度）
     * @param assetType 设施类型
     * @return
     */
    @AutoLog(value = "台账总览-各区域数据获取-地图")
    @ApiOperation(value="台账总览-各区域数据获取-地图", notes="台账总览-各区域数据获取-地图")
    @GetMapping(value = "/childStandingBookByArea")
    public Result<?> childStandingBookByArea(String cityProperId, String type, String assetType){
        Result result = new Result();
        try {
        	List<LedgerAreaCountVO> data = ledgerOverview.childStandingBookByArea(cityProperId,type,assetType);
            result.setResult(data);
            result.setMessage("查询成功");
            return result;
        } catch (Exception e) {
            log.error("LedgerOverviewController ## childStandingBookByArea(cityProperId = #{})",cityProperId,e);
            result.error("查询失败");
            return result;
        }
    }









}
