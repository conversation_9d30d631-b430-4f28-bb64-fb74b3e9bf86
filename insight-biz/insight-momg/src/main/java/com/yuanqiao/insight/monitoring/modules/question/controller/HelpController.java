package com.yuanqiao.insight.monitoring.modules.question.controller;

import com.yuanqiao.insight.monitoring.modules.question.service.IQuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

/**
* @Description: 问题答复
* @Author: jeecg-boot
* @Date:   2020-03-13
* @Version: V1.0
*/
@Controller
@RequestMapping("/questions")
public class HelpController  {
   @Autowired
   private IQuestionService questionService;



   @RequestMapping(value = "/hello")
   public String queryPageList() {

       return "hello";
   }



}
