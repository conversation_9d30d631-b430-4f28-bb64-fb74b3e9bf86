package com.yuanqiao.insight.monitoring.modules.topoCabinet.service.impl;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.monitoring.modules.topoCabinet.entity.TopoCabinet;
import com.yuanqiao.insight.monitoring.modules.topoCabinet.mapper.TopoCabinetMapper;
import com.yuanqiao.insight.monitoring.modules.topoCabinet.service.ITopoCabinetService;
import com.yuanqiao.insight.monitoring.modules.topoCabinet2device.entity.TopoCabinet2device;
import com.yuanqiao.insight.monitoring.modules.topoCabinet2device.mapper.TopoCabinet2deviceMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description: 机柜表
 * @Author: jeecg-boot
 * @Date:   2021-05-13
 * @Version: V1.0
 */
@Service
public class TopoCabinetServiceImpl extends ServiceImpl<TopoCabinetMapper, TopoCabinet> implements ITopoCabinetService {
    @Autowired
    private TopoCabinet2deviceMapper topoCabinet2deviceMapper;

    @Override
    public List<String> getfreeUbyCabinet(String cabinetId, String deviceId){
        List<String> freeLayerList = new ArrayList<>();
        TopoCabinet topoCabinet = this.getById(cabinetId);
        Integer layers = topoCabinet.getLayers();
        for (int i = 1; i <= layers; i++) {
            freeLayerList.add(i + "");
        }

        QueryWrapper<TopoCabinet2device> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("cabinet_id", cabinetId);
        if (StringUtils.isNotEmpty(deviceId)){
            queryWrapper.ne("device_id", deviceId);
        }
        List<TopoCabinet2device> childDeviceList = topoCabinet2deviceMapper.selectList(queryWrapper);
        if (CollUtil.isNotEmpty(childDeviceList)) {
            JSONArray occupiedLayerArray = new JSONArray();
            childDeviceList = childDeviceList.stream().map(item -> {
                item.setLayerPool(JSONArray.parseArray(item.getLayerPoolStr()));
                return item;
            }).collect(Collectors.toList());
            childDeviceList.stream().forEach(ele -> {
                occupiedLayerArray.addAll(ele.getLayerPool());
            });
            freeLayerList = freeLayerList.stream().filter(l -> occupiedLayerArray.stream().noneMatch(l1 -> Objects.equals(l1, l))).collect(Collectors.toList());
        }
        return freeLayerList;
    }
}
