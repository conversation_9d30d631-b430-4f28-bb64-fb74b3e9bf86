package com.yuanqiao.insight.monitoring.modules.terminal.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.acore.depart.entity.SysDepart;
import com.yuanqiao.insight.acore.depart.mapper.SysDepartMapper;
import com.yuanqiao.insight.cmdb.modules.assets.service.IAssetsService;
import com.yuanqiao.insight.common.constant.CommonConstant;
import com.yuanqiao.insight.common.util.common.LocalCacheUtils;
import com.yuanqiao.insight.common.util.common.UnitConvertUtil;
import com.yuanqiao.insight.monitoring.modules.device.GroupAndUserAuthControl;
import com.yuanqiao.insight.monitoring.modules.device.entity.MomgDept;
import com.yuanqiao.insight.monitoring.modules.device.mapper.MomgDeptMapper;
import com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice;
import com.yuanqiao.insight.monitoring.modules.terminal.mapper.TerminalDeviceMapper;
import com.yuanqiao.insight.monitoring.modules.terminal.service.ITerminalDeviceService;
import com.yuanqiao.insight.monitoring.modules.terminal.utils.ESUtils;
import com.yuanqiao.insight.service.device.mapper.DeviceConnectInfoMapper;
import com.yuanqiao.insight.service.device.mapper.DeviceInfoMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoDict;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.message.websocket.WebSocket;
import org.jeecg.common.message.webssh.pojo.WebsocketConst;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.system.entity.SysTerminalUser;
import org.jeecg.modules.system.entity.SysUsers;
import org.jeecg.modules.system.mapper.SysTerminalUserMapper;
import org.jeecg.modules.system.mapper.SysUserMapper;
import org.jeecg.modules.system.model.SysDepartTreeModel;
import org.jeecg.modules.system.service.ISysDepartService;
import com.yuanqiao.insight.acore.system.service.ISysDictService;
import org.jeecg.modules.system.service.ISysUserDepartService;
import org.jeecg.modules.system.service.ISysUserService;
import com.yuanqiao.insight.acore.system.vo.SysArea;
import org.jeecg.modules.umpPwdManage.entity.UmpPwdManage;
import org.jeecg.modules.umpPwdManage.service.IUmpPwdManageService;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.transaction.Transactional;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * @Description: 终端设备表
 * @Author: QYB
 * @Date: 2021-05-10
 * @Version: V1.0
 */
@Api(tags = "终端设备表")
@RestController
@RequestMapping("/terminal/terminalDevice")
@Slf4j
public class TerminalDeviceController extends JeecgController<TerminalDevice, ITerminalDeviceService> {
    @Autowired
    private ITerminalDeviceService terminalDeviceService;
    @Autowired
    private TerminalDeviceMapper terminalDeviceMapper;
    @Autowired
    private IUmpPwdManageService umpPwdManageService;
    @Autowired
    private ISysDictService sysDictService;
    @Autowired
    private GroupAndUserAuthControl groupAndUserAuthControl;
    @Autowired
    private IAssetsService assetsService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private ESUtils esUtils;

    LocalCacheUtils cacheUtils = LocalCacheUtils.getInstance();
    @Value("${excel.terminalPath}")
    private String path;

    ExecutorService executorService = Executors.newFixedThreadPool(10,
            new BasicThreadFactory.Builder().namingPattern("TerminalInfo-").build());
    String Terminal_ES_INDEX = "metrics-terminal-udp";

    /**
     * 分页列表查询
     *
     * @param pageNo
     * @param pageSize
     * @return
     */
    @AutoLog(value = "终端信息-分页列表查询")
    @ApiOperation(value = "终端设备表-分页列表查询", notes = "终端设备表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(TerminalDevice terminalDevice, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   @RequestParam(name = "metrics", required = false) List<String> metrics,
                                   @RequestParam(name = "isSimple", defaultValue = "false") boolean isSimple) throws InterruptedException {
        terminalDevice.setEnable(1);
        Page<TerminalDevice> page = new Page<>(pageNo, pageSize);

        IPage<TerminalDevice> terminalDeviceIPage = selectTerminal(page, terminalDevice);
        if (!isSimple) {
            List<TerminalDevice> records = terminalDeviceIPage.getRecords();
            CountDownLatch countDownLatch = new CountDownLatch(records.size());
            for (TerminalDevice device : records) {
                executorService.execute(() -> {
                    getInfoFromRedis(device, countDownLatch, metrics);
                });
            }
            countDownLatch.await();
        }
        return Result.OK(terminalDeviceIPage);
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "终端信息-通过id查询")
    @ApiOperation(value = "终端设备表-通过id查询", notes = "终端设备表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        TerminalDevice terminalDevice = terminalDeviceService.getById(id);
        if (terminalDevice == null) {
            return Result.error("未找到对应数据");
        } else {
            Page<TerminalDevice> page = new Page<>(1, 1);
            IPage<TerminalDevice> terminalDeviceIPage = selectTerminal(page, new TerminalDevice().setId(id));
            CountDownLatch countDownLatch = new CountDownLatch(1);
            List<String> metrics = Arrays.asList("static", "sysUptime");
            getInfoFromRedis(terminalDeviceIPage.getRecords().get(0), countDownLatch, metrics);
            return Result.OK(terminalDeviceIPage);
        }
    }

    public void getInfoFromRedis(TerminalDevice device, CountDownLatch countDownLatch, List<String> metrics) {
        List<String> metricsToSearch = Arrays.asList("cpuRate", "memUtilizRate", "static", "diskRate", "sysUptime", "mem", "cpu");
        if (metrics != null && !metrics.isEmpty()) {
            metricsToSearch = metrics;
        }
        List<String> haveToSearchFromES = new ArrayList<>();
        for (String metric : metricsToSearch) {
            switch (metric) {
                case "cpuRate":
                    String redisKey = "stca:" + device.getUniqueCode() + "_" + "cpuRate";
                    if (redisTemplate.hasKey(redisKey)) {
                        JSONObject dataObject = JSONObject.parseObject(redisTemplate.boundValueOps(redisKey).get() + "");
                        String cpuRateStr = dataObject.getString("value") + "%";//无百分号
                        device.setCpuRateStr(cpuRateStr);
                    } else {
                        haveToSearchFromES.add("cpuRate");
                    }
                    break;
                case "memUtilizRate":
                    String redisKeyMemRate = "stca:" + device.getUniqueCode() + "_" + "memUtilizRate";
                    if (redisTemplate.hasKey(redisKeyMemRate)) {
                        JSONObject dataObject = JSONObject.parseObject(redisTemplate.boundValueOps(redisKeyMemRate).get() + "");
                        String memUtilizRateStr = dataObject.getString("value") + "%";
                        device.setMemUtilizRateStr(memUtilizRateStr);
                    } else {
                        haveToSearchFromES.add("memUtilizRate");
                    }
                    break;
                case "static":
                    String redisKeyStatic = "stca:" + device.getUniqueCode() + "_" + "static";
                    if (redisTemplate.hasKey(redisKeyStatic)) {
                        JSONObject dataObject = JSONObject.parseObject(redisTemplate.boundValueOps(redisKeyStatic).get() + "");
                        JSONObject value = dataObject.getJSONArray("value").getJSONObject(0);
                        String sysVersion = value.getJSONObject("sysName").getString("value") + value.getJSONObject("sysVersion").getString("value");
                        device.setSysVersion(sysVersion);
                    } else {
                        haveToSearchFromES.add("static");
                    }
                    break;
                case "diskRate":
                    String redisKeyDiskFree = "stca:" + device.getUniqueCode() + "_" + "diskFree";
                    String redisKeyDiskTotal = "stca:" + device.getUniqueCode() + "_" + "diskTotal";
                    String redisKeyDiskRate = "stca:" + device.getUniqueCode() + "_" + "diskRate";
                    Double diskFree = 0D;
                    double diskTotal;
                    if (redisTemplate.hasKey(redisKeyDiskFree) && redisTemplate.hasKey(redisKeyDiskTotal) && redisTemplate.hasKey(redisKeyDiskRate)) {
                        JSONObject dataObjectDiskFree = JSONObject.parseObject(redisTemplate.boundValueOps(redisKeyDiskFree).get() + "");
                        diskFree = dataObjectDiskFree.getDouble("value");
                        String diskFreeUnit = dataObjectDiskFree.getString("unit");
                        device.setDiskFreeStr(diskFree + diskFreeUnit);

                        JSONObject dataObjectDiskTotal = JSONObject.parseObject(redisTemplate.boundValueOps(redisKeyDiskTotal).get() + "");
                        diskTotal = dataObjectDiskTotal.getDouble("value");
                        String diskTotalUnit = dataObjectDiskTotal.getString("unit");
                        device.setDiskTotalStr(diskTotal + diskTotalUnit);

                        String unit = UnitConvertUtil.getBiggerDataStorageUnit(diskTotalUnit, diskFreeUnit);
                        Map<String, Object> diskTotalMap = UnitConvertUtil.unitOriginToTarget(diskTotal, 1024D, diskTotalUnit, unit);
                        Map<String, Object> diskFreeMap = UnitConvertUtil.unitOriginToTarget(diskFree, 1024D, diskFreeUnit, unit);
                        String diskUsed = ((((BigDecimal) diskTotalMap.get("value"))).subtract((BigDecimal) diskFreeMap.get("value"))).setScale(2, RoundingMode.DOWN).toString() + unit;
                        device.setDiskUsedStr(diskUsed);

                        JSONObject dataObjectDiskRate = JSONObject.parseObject(redisTemplate.boundValueOps(redisKeyDiskRate).get() + "");
                        String diskRateStr = dataObjectDiskRate.getString("value") + "%";
                        device.setDiskRateStr(diskRateStr);
                    }else  {
                        haveToSearchFromES.add("diskRate");
                    }
                    break;
                case "sysUptime":
                    String redisKeySysUptime = "stca:" + device.getUniqueCode() + "_" + "dynamic";
                    if (redisTemplate.hasKey(redisKeySysUptime)) {
                        JSONObject dataObject = JSONObject.parseObject(redisTemplate.boundValueOps(redisKeySysUptime).get() + "");
                        JSONObject value = dataObject.getJSONArray("value").getJSONObject(0);
                        String sysUptimeStr = value.getJSONObject("sysUptime").getString("value");
                        device.setSysUptimeStr(sysUptimeStr);
                    } else {
                        haveToSearchFromES.add("sysUptime");
                    }
                    break;
                case "mem":
                    String redisKeyMem = "stca:" + device.getUniqueCode() + "_" + metric;
                    if (redisTemplate.hasKey(redisKeyMem)) {
                        JSONObject dataObject = JSONObject.parseObject(redisTemplate.boundValueOps(redisKeyMem).get() + "");
                        JSONObject value = dataObject.getJSONArray("value").getJSONObject(0);
                        Double memTotalDouble = value.getJSONObject("memTotal").getDouble("value");
                        String memTotalUnit = value.getJSONObject("memTotal").getString("unit");
                        Map<String, Object> unitConvertMap1 = UnitConvertUtil.unitDynamic(memTotalUnit, memTotalDouble, 1024D);
                        String memTotalResult = ((BigDecimal) unitConvertMap1.get("value")).setScale(2, RoundingMode.DOWN).toString() + (String) unitConvertMap1.get("unit");
                        device.setMemTotalStr(memTotalResult);
                        //不能用memAvailable指标，因为和memUtilizRate对不上
                        Double memFreeDouble = value.getJSONObject("memFree").getDouble("value");
                        String memFreeUnit = value.getJSONObject("memFree").getString("unit");
                        Map<String, Object> unitConvertMap2 = UnitConvertUtil.unitDynamic(memFreeUnit, memFreeDouble, 1024D);
                        String memFreeResult = ((BigDecimal) unitConvertMap2.get("value")).setScale(2, RoundingMode.DOWN).toString() + (String) unitConvertMap2.get("unit");

                        String unit = UnitConvertUtil.getBiggerDataStorageUnit((String) unitConvertMap1.get("unit"), (String) unitConvertMap2.get("unit"));
                        Map<String, Object> memTotalMap = UnitConvertUtil.unitOriginToTarget(memTotalDouble, 1024D, memTotalUnit, unit);
                        Map<String, Object> memFreeMap = UnitConvertUtil.unitOriginToTarget(memFreeDouble, 1024D, memFreeUnit, unit);

                        String memUsed = ((((BigDecimal) memTotalMap.get("value"))).subtract((BigDecimal) memFreeMap.get("value"))).setScale(2, RoundingMode.DOWN).toString() + unit;
                        device.setMemUsedStr(memUsed);
                        device.setMemFreeStr(memFreeResult);
                    } else {
                        haveToSearchFromES.add("mem");
                    }
                    break;
                case "cpu":
                    String redisKeyCpu = "stca:" + device.getUniqueCode() + "_" + "cpu";
                    if (redisTemplate.hasKey(redisKeyCpu)) {
                        JSONObject dataObject = JSONObject.parseObject(redisTemplate.boundValueOps(redisKeyCpu).get() + "");
                        JSONObject value = dataObject.getJSONArray("value").getJSONObject(0);
                        String cpuNameStr = value.getJSONObject("cpuName").getString("value");
                        device.setCpuNameStr(cpuNameStr);
                    } else {
                        haveToSearchFromES.add("cpu");
                    }
                    break;
                default:
                    break;
            }
        }
        if (!haveToSearchFromES.isEmpty()) {
            getInfoFromES(device, haveToSearchFromES);
        }
        countDownLatch.countDown();
    }

    public void getInfoFromES(TerminalDevice device, List<String> haveToSearchFromES) {
        //todo 从es中取有一个问题，是物模型的指标有没有开启存储。如果只存储了一段时间又不存储了，那这时拿最新的数据和现在的时间差距较大，每个指标的时间也不一致。
        List<String> searchFromES = new ArrayList<>();
        for (String metrics : haveToSearchFromES) {
            switch (metrics) {
                case "cpuRate":
                case "memUtilizRate":
                    searchFromES.add(metrics);
                    break;
                case "static":
                    searchFromES.add("static.sysName");
                    searchFromES.add("static.sysVersion");
                    break;
                case "diskRate":
                    searchFromES.add("diskRate");
                    searchFromES.add("diskTotal");
                    searchFromES.add("diskFree");
                    break;
                case "sysUptime":
                    searchFromES.add("dynamic.sysUptime");
                    break;
                case "mem":
                    searchFromES.add("mem.memTotal");
                    searchFromES.add("mem.memFree");
                    break;
                case "cpu":
                    searchFromES.add("cpu.cpuName");
                    break;
                default:
                    log.error("the metric name not match {}", metrics);
                    break;
            }
        }

        Map<String, Object> mget = esUtils.mget(Terminal_ES_INDEX, searchFromES, null, device.getUniqueCode());
        for (String metrics : haveToSearchFromES) {
            switch (metrics) {
                case "cpuRate":
                    if (mget.get("cpuRate") != null) {
                        device.setCpuRateStr(mget.get("cpuRate") + "%");
                    }
                    break;
                case "memUtilizRate":
                    if (mget.get("memUtilizRate") != null) {
                        device.setMemUtilizRateStr(mget.get("memUtilizRate") + "%");
                    }
                    break;
                case "static":
                    if (mget.containsKey("static.sysName") && mget.containsKey("static.sysVersion")) {
                        String sysVersion = String.valueOf(mget.get("static.sysName")) + mget.get("static.sysVersion");
                        device.setSysVersion(sysVersion);
                    }
                    break;
                case "diskRate":
                    if (mget.get("diskRate") != null) {
                        device.setDiskRateStr(String.format("%.2f", mget.get("diskRate")) + "%");
                    }
                    //这个在es中没有单位，可能能从物模型中获取单位，就麻烦了
                    //Map<String, Object> unitConvertMapDiskTotal = UnitConvertUtil.unitDynamic("KB", diskTotalNum, 1024D);
                    //String diskTotalResult = ((BigDecimal)unitConvertMapDiskTotal.get("value")).toString() + (String)unitConvertMapDiskTotal.get("unit");
                    if (mget.containsKey("diskTotal")) {
                        device.setDiskTotalStr(String.format("%.2f", mget.get("diskTotal")) + "GB");
                    }
                    if (mget.containsKey("diskFree")) {
                        device.setDiskFreeStr(String.format("%.2f", mget.get("diskFree")) + "GB");
                    }
                    if (mget.containsKey("diskTotal") && mget.containsKey("diskFree")) {
                        device.setDiskUsedStr(String.format("%.2f", (Double) mget.get("diskTotal") - (Double) mget.get("diskFree")) + "GB");
                    }
                    break;
                case "sysUptime":
                    if (mget.containsKey("dynamic.sysUptime")) {
                        String sysUptime = String.valueOf(mget.get("dynamic.sysUptime"));
                        device.setSysUptimeStr(sysUptime);
                    }
                    break;
                case "mem":
                    if (mget.containsKey("mem.memTotal") && mget.containsKey("mem.memFree")) {
                        Map<String, Object> unitConvertMap1 = UnitConvertUtil.unitDynamic("KB", (Double) mget.get("mem.memTotal"), 1024D);
                        String memTotalResult = ((BigDecimal) unitConvertMap1.get("value")).setScale(2, RoundingMode.DOWN).toString() + (String) unitConvertMap1.get("unit");
                        device.setMemTotalStr(memTotalResult);
                        Map<String, Object> unitConvertMap2 = UnitConvertUtil.unitDynamic("KB", (Double) mget.get("mem.memFree"), 1024D);
                        String memFreeResult = ((BigDecimal) unitConvertMap2.get("value")).setScale(2, RoundingMode.DOWN).toString() + (String) unitConvertMap2.get("unit");
                        device.setMemFreeStr(memFreeResult);

                        String unit = UnitConvertUtil.getBiggerDataStorageUnit((String)unitConvertMap1.get("unit"), (String)unitConvertMap2.get("unit"));
                        Map<String, Object> memTotalMap = UnitConvertUtil.unitOriginToTarget((Double) mget.get("mem.memTotal"), 1024D, "KB", unit);
                        Map<String, Object> memFreeMap = UnitConvertUtil.unitOriginToTarget((Double) mget.get("mem.memFree"), 1024D, "KB", unit);

                        device.setMemUsedStr(((BigDecimal) memTotalMap.get("value")).subtract((BigDecimal) memFreeMap.get("value")).setScale(2, RoundingMode.DOWN).toString() + unit);
                    }
                    break;
                case "cpu":
                    if (mget.containsKey("cpu.cpuName")) {
                        String cpuName = String.valueOf(mget.get("cpu.cpuName"));
                        device.setCpuNameStr(cpuName);
                    }
                    break;
                default:
                    log.error("the metric name not match {}", metrics);
                    break;
            }
        }
    }

    /**
     * 获取设备数量
     *
     * @param terminalDevice
     * @param req
     * @return
     */
    //@AutoLog(value = "获取在线离线数量")
    @ApiOperation(value = "获取在线离线数量", notes = "获取在线离线数量")
    @GetMapping(value = "/getTotal")
    public Result<?> getTotal(TerminalDevice terminalDevice,
                              HttpServletRequest req) {
        Map<String, Integer> map = new HashMap<>();

        //终端设备数量
        Integer disables = terminalDeviceMapper.countDisable();
        map.put("allTotal", terminalDeviceMapper.countDevice());
        map.put("online", terminalDeviceMapper.countOns());
        map.put("offline", terminalDeviceMapper.countOuts());
        return Result.OK(map);
    }


    /**
     * 添加
     *
     * @param terminalDevice
     * @return
     */
    @AutoLog(value = "终端信息-添加")
    @ApiOperation(value = "终端设备表-添加", notes = "终端设备表-添加")
    @PostMapping(value = "/add")
    @RequiresPermissions("terminal:add")
    public Result<?> add(@RequestBody TerminalDevice terminalDevice) {
        return terminalDeviceService.saveTerminal(terminalDevice);
    }

    /**
     * 编辑
     *
     * @param terminalDevice
     * @return
     */
    @AutoLog(value = "终端信息-编辑")
    @ApiOperation(value = "终端设备表-编辑", notes = "终端设备表-编辑")
    @PutMapping(value = "/edit")
    @RequiresPermissions("terminal:edit")
    public Result<?> edit(@RequestBody TerminalDevice terminalDevice) {
        return terminalDeviceService.updateTerminal(terminalDevice);
    }

    /**
     * 统计指定日期的终端国产化率
     *
     * @param timeStr
     * @return
     */
    @AutoLog(value = "终端信息-统计指定日期的终端国产化率")
    @ApiOperation(value = "终端设备表-统计指定日期的终端国产化率", notes = "终端设备表-统计指定日期的终端国产化率")
    @GetMapping(value = "/terminalNational")
    public Result<?> terminalNational(String timeStr) {
        terminalDeviceService.executeTerminalNational(timeStr);
        return Result.OK("执行成功！");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "终端信息-通过id删除")
    @ApiOperation(value = "终端设备表-通过id删除", notes = "终端设备表-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        terminalDeviceService.removeById(id);
        return Result.OK("删除成功!");
    }

    @Autowired
    DeviceConnectInfoMapper deviceConnectInfoMapper;

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "终端信息-批量删除")
    @ApiOperation(value = "终端设备表-批量删除", notes = "终端设备表-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    @RequiresPermissions("terminal:delete")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        List<String> terIdList = Arrays.asList(ids.split(","));
        return terminalDeviceService.deleteBatch(terIdList);
    }

    /***
     * 批量设备禁用/启用
     * @param ids
     * @param enable
     * @return
     */
    @AutoLog(value = "设备禁用")
    @ApiOperation(value = "设备禁用", notes = "设备禁用")
    @GetMapping(value = "/batchUpdateEnable")
    public Result<?> updateEnable(Integer enable, String ids) {
        if (!ids.isEmpty() && ids != null) {
            List<String> terminalIds = Arrays.asList(ids.split(","));
            //修改设备的enable
            terminalDeviceMapper.updateEnable(enable, terminalIds);
            return Result.OK();
        } else {
            return Result.error("未选中记录");
        }

    }


    /***
     * 设备禁用/启用
     * @param terminalId
     * @param enable
     * @return
     */
    @AutoLog(value = "设备禁用")
    @ApiOperation(value = "设备禁用", notes = "设备禁用")
    @GetMapping(value = "/updateEnable")
    public Result<?> singleUpdateEnable(Integer enable, String terminalId) {
        if (terminalId != "" && terminalId != null) {
            //修改设备的enable
            terminalDeviceMapper.singleUpdateEnable(enable, null, null, null, null, terminalId, null,
                    null, null, terminalId, null, null, null, null, null);
            return Result.OK();
        } else {
            return Result.error("未选中记录");
        }

    }


    /**
     * 导出excel
     *
     * @param request
     * @param
     */
    @AutoLog(value = "终端信息-导出")
    @RequestMapping(value = "/exportXls")
//    @RequiresPermissions("terminal:exportXls")
//    public ModelAndView exportXls(HttpServletRequest request, TerminalDevice terminalDevice) {
//        return super.exportXls(request, terminalDevice, TerminalDevice.class, "终端设备表");
//    }
    public void exportXls(HttpServletRequest request, HttpServletResponse response, TerminalDevice terminalDevice) {
        /*UmpPwdManage zip = umpPwdManageService.getZip();
        //Map<String, String[]> maps =  request.getParameterMap(); //测试 @wxp
        super.exportXlsZip(request, response, terminalDevice, TerminalDevice.class, "终端设备表", zip.getZipPwd(), zip.getIsEncry());*/
        IPage<TerminalDevice> pageList = selectTerminal(new Page<>(1, -1), terminalDevice);

        List<TerminalDevice> terminalDeviceList = pageList.getRecords();

        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        //导出文件名称
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
//        if (user == null) {
//            ExportParams exportParams = new ExportParams("终端信息表", "终端信息");
//            mv.addObject(NormalExcelConstants.PARAMS, exportParams);
//        } else {
//            ExportParams exportParams = new ExportParams("终端信息表", "导出人:" + user.getRealname(), "终端信息");
//            mv.addObject(NormalExcelConstants.PARAMS, exportParams);
//        }
//
//        mv.addObject(NormalExcelConstants.FILE_NAME, "终端设备表");
//        mv.addObject(NormalExcelConstants.CLASS, TerminalDevice.class);
//        mv.addObject(NormalExcelConstants.DATA_LIST, terminalDeviceList);
//        return mv;
        String exportFieldStr = request.getParameterMap().get("exportFields")[0];
        // 过滤选中数据
        List<TerminalDevice> exportList = new ArrayList<>();
        String selections = request.getParameter("selections");
        if (oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            exportList = terminalDeviceList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
        } else {
            exportList = terminalDeviceList;
        }
        UmpPwdManage zip = umpPwdManageService.getZip();
        String fileName = assetsService.exportXlsRename("终端信息");
        super.zipPwdWithExportFields(response, TerminalDevice.class, fileName, zip.getZipPwd(), zip.getIsEncry(), exportList, exportFieldStr);
    }

    @Autowired
    private SysDepartMapper departMapper;

    /**
     * 模板导出
     *
     * @param
     * @param
     * @return
     */
    @AutoLog(value = "终端信息-模板导出")
    @GetMapping(value = "/downloadTemplate")
    @ApiOperation("模板导出")
//    @RequiresPermissions("terminal:downloadTemplate")
    public void downloadTemplate(HttpServletResponse response, HttpServletRequest request) {
        // 获取单位名称数据
        LambdaQueryWrapper<SysDepart> departWrapper = new LambdaQueryWrapper<>();
        departWrapper.select(SysDepart::getDepartName).eq(SysDepart::getDelFlag, CommonConstant.DEL_FLAG_0);
        List<SysDepart> departList = departMapper.selectList(departWrapper);
        List<String> departNameList = departList.stream().map(SysDepart::getDepartName).collect(Collectors.toList());

        //获取架构数据
        List<DictModel> dictModelList = sysDictService.queryDictItemsByCode("frawork");
        List<String> dictTestList = dictModelList.stream().map(DictModel::getText).collect(Collectors.toList());

        //获取终端类型
        dictModelList = sysDictService.queryDictItemsByCode("resources_type");
        List<String> terminalTypeList = dictModelList.stream().map(DictModel::getText).collect(Collectors.toList());

        try {
            InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(path);
            String str1 = path.substring(0, path.indexOf("."));
            String str2 = path.substring(str1.length());
            // 根据输入流创建工作簿
            Workbook workbook = new XSSFWorkbook(inputStream);
            Sheet sheet = workbook.getSheet("字典值");
            setColumnValue(departNameList, sheet, 0);
            setColumnValue(dictTestList, sheet, 1);
            setColumnValue(terminalTypeList, sheet, 2);
            response.setContentType("application/binary;charset=ISO8859-1");
            String fileName = URLEncoder.encode("终端批量导入模板" + str2, "UTF-8");
            response.setHeader("Content-disposition", "attachment; filename=" + fileName);
            ServletOutputStream out = null;
            out = response.getOutputStream();
            workbook.write(out);
            out.flush();
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 将字典值写入数据表
     *
     * @param terminalTypeList
     * @param sheet
     * @param column
     */
    private void setColumnValue(List<String> terminalTypeList, Sheet sheet, Integer column) {
        for (int i = 0; i < terminalTypeList.size(); i++) {
            Row row = sheet.getRow(i + 1);
            if (row == null) {
                row = sheet.createRow(i + 1);
            }
            Cell cell = row.createCell(column);
            cell.setCellValue(terminalTypeList.get(i));
        }
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
//    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
//    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
//        return super.importExcel(request, response, TerminalDevice.class);
//    }
    @AutoLog(value = "终端信息-导入")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    @RequiresPermissions("terminal:importExcel")
    @Transactional
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = null;
        try {
            fileMap = multipartRequest.getFileMap();
        } catch (Exception e) {
            e.printStackTrace();
        }
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            return terminalDeviceService.importSheets(file);
        }
        return Result.error("文件导入失败！");
    }


    private String getName(int id) {
//        if (id > 0) {
//            SysArea city = terminalDeviceMapper.findCity(id);
//            name += " "+city.getText();
//            this.getName( Integer.parseInt(city.getPid()));
//        }
//        String[] s1 = name.split(" ");
//        List<String> strings = Arrays.asList(s1);
//        Collections.reverse(strings);
//        String replace = strings.toString().replace("[", "").replace("]", "").replace(",","");
//        return replace;

        SysArea city = terminalDeviceMapper.findCity(id);

        SysArea city1 = terminalDeviceMapper.findCity(Integer.parseInt(city.getPid()));

        SysArea city2 = terminalDeviceMapper.findCity(Integer.parseInt(city1.getPid()));


        return city2.getText() + " " + city1.getText() + " " + city.getText();


    }

    @Autowired
    private DeviceInfoMapper deviceInfoMapper;
    @Autowired
    private SysTerminalUserMapper sysTerminalUserMapper;
    @Autowired
    private ISysUserService sysUserService;

    @AutoLog(value = "运维助手 - 终端注册、绑定用户")
    @PutMapping(value = "/updateStatus")
    public Result<?> updateStatus(@RequestBody TerminalDevice terminalDevice) {
        return terminalDeviceService.terminalRegister(terminalDevice);
    }

    @AutoLog(value = "终端管理 - 转译终端CPU类型、操作系统类型")
    @PutMapping(value = "/translateCpuTypeAndOsType")
    public Result<?> translateCpuTypeAndOsType(@RequestBody TerminalDevice terminalDevice) {
        JSONObject resObj = new JSONObject();
        TerminalDevice one = terminalDeviceService.getOne(new QueryWrapper<TerminalDevice>().eq("unique_code", terminalDevice.getUniqueCode()));

        List<DictModel> cpuTypeDictList = sysDictService.queryDictItemsByCode("cpuTypeTranslate");
        Map<String, String> cpuTypeDictMap = new HashMap<>();
        if (CollUtil.isNotEmpty(cpuTypeDictList)) {
            cpuTypeDictMap = cpuTypeDictList.stream().collect(Collectors.toMap(DictModel::getText, DictModel::getValue));
        }
        JSONObject cpuTypeObj = new JSONObject();
        cpuTypeObj.put("oldValue", "--");
        if (one != null) {
            cpuTypeObj.put("oldValue", one.getCpuType());
        }
        cpuTypeObj.put("newValue", "其他");
        if (cpuTypeDictMap.containsKey(terminalDevice.getCpuname())) {
            cpuTypeObj.put("newValue", cpuTypeDictMap.get(terminalDevice.getCpuname()));
        }
        resObj.put("cpuType", cpuTypeObj);

        List<DictModel> osTypeDictList = sysDictService.queryDictItemsByCode("osTypeTranslate");
        Map<String, String> osTypeDictMap = new HashMap<>();
        if (CollUtil.isNotEmpty(osTypeDictList)) {
            osTypeDictMap = osTypeDictList.stream().collect(Collectors.toMap(DictModel::getText, DictModel::getValue));
        }
        JSONObject osTypeObj = new JSONObject();
        osTypeObj.put("oldValue", "--");
        if (one != null) {
            osTypeObj.put("oldValue", one.getOsType());
        }
        osTypeObj.put("newValue", "其他");
        if (osTypeDictMap.containsKey(terminalDevice.getOsname())) {
            osTypeObj.put("newValue", osTypeDictMap.get(terminalDevice.getOsname()));
        }
        resObj.put("osType", osTypeObj);

        return Result.OK(resObj);
    }

    @Autowired
    private ISysDepartService sysDepartService;

    @AutoLog(value = "终端管理 - 未绑定终端的用户列表")
    @GetMapping(value = "/getUnbindUser")
    public Result<?> getUnbindUser(SysUsers sysUser,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        if (sysUser != null && StringUtils.isNotEmpty(sysUser.getOrgCode())) {
            SysDepart sysDepart = sysDepartService.getById(sysUser.getOrgCode());
            sysUser.setOrgCode(sysDepart.getOrgCode());
        }
        List<SysTerminalUser> terminalUsers = sysTerminalUserMapper.selectList(new QueryWrapper<SysTerminalUser>());
        if (CollUtil.isNotEmpty(terminalUsers)) {
            if (sysUser == null) {
                sysUser = new SysUsers();
            }
            sysUser.setExceptUsernameList(terminalUsers.stream().map(SysTerminalUser::getUserName).collect(Collectors.toList()));
        }
        List<SysUsers> allUserList = sysUserService.list();
        Map<String, SysUsers> usersMap = allUserList.stream().collect(Collectors.toMap(SysUsers::getUsername, SysUsers -> SysUsers));
        IPage<SysUsers> userPage = sysUserService.getUsersWithDeptIds(new Page<SysUsers>(pageNo, pageSize), sysUser);
        if (userPage.getRecords() != null && CollUtil.isNotEmpty(userPage.getRecords())) {
            userPage.getRecords().forEach(u -> {
                if (StringUtils.isNotEmpty(u.getOrgCodeTxt())) {
                    List<String> deptIds = Arrays.asList(u.getOrgCodeTxt().split(","));
                    u.setAllocateOrgList(departMapper.selectList(new QueryWrapper<SysDepart>().in("id", deptIds)));
                }
                if (usersMap.containsKey(u.getUsername())) {
                    u.setDepartIds(usersMap.get(u.getUsername()).getDepartIds());
                }
                u.setPassword(null);
                u.setSalt(null);
            });
        }
        return Result.OK(userPage);
    }

    @AutoLog(value = "终端管理 - 查询终端与用户绑定关系")
    @GetMapping(value = "/selectTerWithUser")
    public Result<?> selectTerWithUser(String terminalName, String userAccount, String username, String bindFlag,
                                       @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        Page<TerminalDevice> page = new Page<>(pageNo, pageSize);
        // bindFlag  1: 已绑定，0: 未绑定
        IPage<TerminalDevice> pageList = terminalDeviceMapper.selectTerWithUser(page, terminalName, userAccount, username, bindFlag);
        return Result.OK(pageList);
    }

    @AutoLog(value = "终端管理 - 绑定用户 ; 运维助手 - 编辑终端")
    @PutMapping(value = "/updateTerminalInfo")
    public Result<?> updateTerminalInfo(@RequestBody TerminalDevice terminalDevice) {
        return terminalDeviceService.updateTerminalInfo(terminalDevice);
    }

    @ApiOperation(value = "运维助手-解绑终端与用户关系", notes = "运维助手-解绑终端与用户关系")
    @GetMapping(value = "/unBindTerminal")
    public Result<?> unBindTerminal(String uniqueCode, String isClear) {
        if (StringUtils.isEmpty(uniqueCode)) {
            return Result.error("参数异常，解绑失败！");
        }
        List<String> codeList = Arrays.asList(uniqueCode.split(","));
        if (StringUtils.isEmpty(isClear)) {
            // 修改终端
            terminalDeviceMapper.withdraw(codeList);
            // 修改设备
            deviceInfoMapper.withdraw(codeList);
        }
        // 解绑终端和用户
        sysTerminalUserMapper.delete(new QueryWrapper<SysTerminalUser>().in("unique_code", codeList));
        return Result.OK("解绑成功！");
    }

    @ApiOperation(value = "终端注册-通过uniqueCode查询终端", notes = "终端注册-通过uniqueCode查询终端")
    @GetMapping(value = "/getTerminalInfo")
    public Result<?> getTerminalInfo(@RequestParam(name = "uniqueCode", required = true) String uniqueCode) {
        TerminalDevice terminalDevice = terminalDeviceService.getOne(new QueryWrapper<TerminalDevice>().eq("unique_code", uniqueCode).eq("delflag", 0));
        if (terminalDevice == null) {
            sysTerminalUserMapper.delete(new QueryWrapper<SysTerminalUser>().eq("unique_code", uniqueCode));
            return Result.error("终端不存在！");
        } else {
            Page<TerminalDevice> page = new Page<>(1, 1);
            IPage<TerminalDevice> terminalDeviceIPage = selectTerminal(page, new TerminalDevice().setId(terminalDevice.getId()));
            for (TerminalDevice terminal : terminalDeviceIPage.getRecords()){
                SysUsers userByName = sysUserService.getUserByName(terminal.getUsername());
                if (userByName != null) {
                    terminal.setUsernameText(userByName.getRealname());
                    terminal.setPhone(userByName.getPhone());
                }
            }
            return Result.OK(terminalDeviceIPage);
        }
    }

    @ApiOperation(value = "终端设备表-通过id查询macip", notes = "终端设备表-通过id查询macip")
    @GetMapping(value = "/getMacIpById")
    public Result<?> getMacIpById(@RequestParam(name = "id", required = true) String id) {
        List<Map<String, String>> list = terminalDeviceService.getMacIpById(id);

        return Result.OK(list);
    }

    @GetMapping(value = "/getTerminalInfoByHostName")
    public Result<?> getTerminalInfoByMac(@RequestParam(name = "hostName", required = true) String hostName) {
        Map<String, String> mapInfo = terminalDeviceService.getTerminalInfo(hostName);
        return Result.OK(mapInfo);
    }

    //    @Value("${deviceStatis.cityId}")
    private String cityId;

    @GetMapping(value = "/getCity")
    public Result<?> getCity() {
        cityId = cacheUtils.getValueByKey(CommonConstant.DATA_DICT_KEY + "project_cityId_id") + "";
        ArrayList<SysArea> sysAreas = sysDictService.getInfoList(cityId);
        return Result.OK(sysAreas);
    }

    @GetMapping(value = "/sendCommand")
    public Result<?> sendCommand(String uniqueCode, String command) {
        String key = String.format("%s_%s", uniqueCode, WebsocketConst.TERMINAL);
        JSONObject sendMsg = new JSONObject();
        sendMsg.put("command", command);
        sendMsg.put("data", "");
        WebSocket.sendOneMessage(key, sendMsg.toJSONString());
        return Result.OK();
    }


    private IPage<TerminalDevice> selectTerminal(Page<TerminalDevice> page, TerminalDevice terminalDevice) {
        Map<String, List<String>> loginUserMap = groupAndUserAuthControl.getGroupIdsAndDeptIdsByLoginUser(null);

        if (StringUtils.isNotEmpty(terminalDevice.getDeptId())) {
            List<SysDepartTreeModel> childDeptList = new ArrayList<>();
            List<SysDepartTreeModel> deptTreeList = sysDepartService.queryMyDeptTreeList(terminalDevice.getDeptId());
            if (CollUtil.isNotEmpty(deptTreeList)) {
                terminalDeviceService.getChild(deptTreeList, childDeptList);
            }
            terminalDevice.setDeptId(String.join(",", childDeptList.stream().map(SysDepartTreeModel::getId).collect(Collectors.toList())));
        }

        IPage<TerminalDevice> terminalIPage = terminalDeviceMapper.selectTerminal(page, terminalDevice, loginUserMap.get("groupIds"), loginUserMap.get("deptIds"));
        if (terminalIPage != null && CollUtil.isNotEmpty(terminalIPage.getRecords())) {
            List<String> usernameList = terminalIPage.getRecords().stream().map(TerminalDevice::getUsername).filter(Objects::nonNull).collect(Collectors.toList());
            List<String> uniqueCodeList = terminalIPage.getRecords().stream().map(TerminalDevice::getUniqueCode).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(usernameList)) {
                List<SysUsers> userList = sysUserService.list(new QueryWrapper<SysUsers>().in("username", usernameList));
                Map<String, String> userMap = userList.stream().collect(Collectors.toMap(SysUsers::getUsername, SysUsers::getRealname));
                List<SysTerminalUser> terminalUserList = sysTerminalUserMapper.selectList(new QueryWrapper<SysTerminalUser>().in("unique_code", uniqueCodeList));
                Map<String, String> terminalUserMap = new HashMap<>();
                if (CollUtil.isNotEmpty(terminalUserList)) {
                    terminalUserMap = terminalUserList.stream().collect(Collectors.toMap(SysTerminalUser::getUniqueCode, SysTerminalUser::getUserName));
                }
                for (TerminalDevice terminal : terminalIPage.getRecords()) {
                    if (StringUtils.isNotEmpty(terminal.getUsername()) && userMap.containsKey(terminal.getUsername())) {
                        terminal.setUsernameText(userMap.get(terminal.getUsername()));
                    } else {
                        terminal.setUsernameText(terminal.getUsername());
                    }
                    if (terminalUserMap.containsKey(terminal.getUniqueCode())) {
                        terminal.setBindStatus("已绑定");
                    }
                }
            }
        }
        return terminalIPage;
    }


}
