package com.yuanqiao.insight.monitoring.modules.topo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.util.Date;
import java.util.Map;

/**
 * @Description: 自动拓扑-网桥表
 * @Author: jeecg-boot
 * @Date:   2025-01-02
 * @Version: V1.0
 */
@Data
@NoArgsConstructor
@TableName("momg_auto_topo_stp")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="momg_auto_topo_stp对象", description="自动拓扑-网桥表")
public class AutoTopoStp {
    
	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
	private String id;
	/**设备标识*/
	@Excel(name = "设备标识", width = 15)
    @ApiModelProperty(value = "设备标识")
	private String deviceCode;
	/**端口号*/
	@Excel(name = "端口号", width = 15)
    @ApiModelProperty(value = "端口号")
	private Integer port;
	/**端口优先级*/
	@Excel(name = "端口优先级", width = 15)
    @ApiModelProperty(value = "端口优先级")
	private Integer portPriority;
	/**端口的当前状态disabled(1),blocking(2),listening(3),learning(4),forwarding(5),broken(6)*/
	@Excel(name = "端口的当前状态disabled(1),blocking(2),listening(3),learning(4),forwarding(5),broken(6)", width = 15)
    @ApiModelProperty(value = "端口的当前状态disabled(1),blocking(2),listening(3),learning(4),forwarding(5),broken(6)")
	private Integer portState;
	/**端口的生成树协议使能或去使能状enabled(1),disabled(2)*/
	@Excel(name = "端口的生成树协议使能或去使能状enabled(1),disabled(2)", width = 15)
    @ApiModelProperty(value = "端口的生成树协议使能或去使能状enabled(1),disabled(2)")
	private Integer portEnable;
	/**端口的路径开销*/
	@Excel(name = "端口的路径开销", width = 15)
    @ApiModelProperty(value = "端口的路径开销")
	private Integer portPathCost;
	/**根网桥*/
	@Excel(name = "根网桥", width = 15)
    @ApiModelProperty(value = "根网桥")
	private String portDesignatedRoot;
	/**指定端口到根网桥的路径开销*/
	@Excel(name = "指定端口到根网桥的路径开销", width = 15)
    @ApiModelProperty(value = "指定端口到根网桥的路径开销")
	private Integer portDesignatedCost;
	/**端口的指定网桥*/
	@Excel(name = "端口的指定网桥", width = 15)
    @ApiModelProperty(value = "端口的指定网桥")
	private String portDesignatedBridge;
	/**设备的指定端口*/
	@Excel(name = "设备的指定端口", width = 15)
    @ApiModelProperty(value = "设备的指定端口")
	private String portDesignatedPort;
	/**端口由学习状态向转发状态转变的次数*/
	@Excel(name = "端口由学习状态向转发状态转变的次数", width = 15)
    @ApiModelProperty(value = "端口由学习状态向转发状态转变的次数")
	private Integer portForwardTransitions;
	/**端口的路径开销*/
	@Excel(name = "端口的路径开销", width = 15)
    @ApiModelProperty(value = "端口的路径开销")
	private Integer portPathCost32;
	private Date createTime;
	public AutoTopoStp(Map<String, Object> map, String deviceCode) {
		this.deviceCode = deviceCode;
		this.port = Integer.parseInt(String.valueOf(map.get("1.1")));
		this.portPriority = Integer.parseInt(String.valueOf(map.get("1.2")));
		this.portState = Integer.parseInt(String.valueOf(map.get("1.3")));
		this.portEnable = Integer.parseInt(String.valueOf(map.get("1.4")));
		this.portPathCost = Integer.parseInt(String.valueOf(map.get("1.5")));
		String rootMac = map.get("1.6").toString();
        this.portDesignatedRoot = rootMac.substring(rootMac.indexOf(":", rootMac.indexOf(":") + 1) + 1);
		this.portDesignatedCost = Integer.parseInt(String.valueOf(map.get("1.7")));
		String mac = map.get("1.8").toString();
        this.portDesignatedBridge = mac.substring(mac.indexOf(":", mac.indexOf(":") + 1) + 1);
		this.portDesignatedPort = String.valueOf(map.get("1.9"));
		this.portForwardTransitions = Integer.parseInt(String.valueOf(map.get("1.10")));
		this.portPathCost32 = Integer.parseInt(String.valueOf(map.get("1.11")));
	}
}
