package com.yuanqiao.insight.monitoring.modules.deviceStatis.momgDeptStatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanqiao.insight.monitoring.modules.deviceStatis.momgDeptStatis.entity.CountyStatisVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 区县统计Mapper
 *
 * <AUTHOR>
 */
@Mapper
@Component
public interface MomgCountyStatisMapper extends BaseMapper<CountyStatisVo> {

    List<CountyStatisVo> findAll(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("cityId") String cityId, @Param("terminalCategory") String terminalCategory);

    List<CountyStatisVo> findAllByHg(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("cityId") String cityId, @Param("terminalCategory") String terminalCategory);

}
