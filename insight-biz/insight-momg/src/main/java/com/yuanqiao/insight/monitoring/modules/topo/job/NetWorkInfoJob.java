package com.yuanqiao.insight.monitoring.modules.topo.job;

import com.yuanqiao.insight.monitoring.modules.topo.collector.SnmpTableCollector;
import com.yuanqiao.insight.monitoring.modules.topo.collector.SnmpTableConfig;
import com.yuanqiao.insight.monitoring.modules.topo.entity.AutoTopoArp;
import com.yuanqiao.insight.monitoring.modules.topo.entity.AutoTopoIf;
import com.yuanqiao.insight.monitoring.modules.topo.entity.AutoTopoMac;
import com.yuanqiao.insight.monitoring.modules.topo.entity.AutoTopoStp;
import com.yuanqiao.insight.monitoring.modules.topo.service.*;
import com.yuanqiao.insight.monitoring.modules.topo.utils.AutoTopoUtils;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.CommonUtils;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class NetWorkInfoJob implements Job {

    @Autowired
    private IAutoTopoService autoTopoService;
    @Autowired
    private IAutoTopoIfService autoTopoIfService;
    @Autowired
    private IAutoTopoStpService autoTopoStpService;
    @Autowired
    private IAutoTopoMacService autoTopoMacService;
    @Autowired
    private IAutoTopoArpService autoTopoArpService;

    @Override
    @Async("quartz-core-pool")
    public void execute(JobExecutionContext context) throws JobExecutionException {
        SnmpTableConfig<AutoTopoIf> ifConfig = SnmpTableConfig.<AutoTopoIf>builder()
                .oidKey(AutoTopoUtils.oIDs.get("IFTable"))
                .entityClass(AutoTopoIf.class)
                .filter(m -> !"00:00:00:00:00:00".equals(m.get("1.6").toString()))
                .cacheSaver(list -> autoTopoIfService.refreshBatch(list))
                .build();

        SnmpTableConfig<AutoTopoStp> stpConfig = SnmpTableConfig.<AutoTopoStp>builder()
                .oidKey(AutoTopoUtils.oIDs.get("STPTable"))
                .entityClass(AutoTopoStp.class)
                .filter(CommonUtils.distinctByKey(map -> map.get("1.8")))
                .cacheSaver(list -> autoTopoStpService.refreshBatch(list))
                .build();

        SnmpTableConfig<AutoTopoMac> macConfig = SnmpTableConfig.<AutoTopoMac>builder()
                .oidKey(AutoTopoUtils.oIDs.get("TPTable"))
                .entityClass(AutoTopoMac.class)
                .cacheSaver(list -> autoTopoMacService.refreshBatch(list))
                .build();

        SnmpTableConfig<AutoTopoArp> arpConfig = SnmpTableConfig.<AutoTopoArp>builder()
                .oidKey(AutoTopoUtils.oIDs.get("ARPTable"))
                .entityClass(AutoTopoArp.class)
                .cacheSaver(list -> autoTopoArpService.refreshBatch(list))
                .build();

        Map<String, SnmpTableConfig<?>> configMap = new HashMap<>();
        configMap.put("IFTable", ifConfig);
        configMap.put("STPTable", stpConfig);
        configMap.put("TPTable", macConfig);
        configMap.put("ARPTable", arpConfig);
        SnmpTableCollector collector = new SnmpTableCollector(configMap, autoTopoService);
        collector.collectAllDevicesTables();
    }
}
