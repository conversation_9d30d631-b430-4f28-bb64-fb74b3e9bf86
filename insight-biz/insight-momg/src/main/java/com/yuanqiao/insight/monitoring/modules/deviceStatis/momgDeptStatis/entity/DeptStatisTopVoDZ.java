package com.yuanqiao.insight.monitoring.modules.deviceStatis.momgDeptStatis.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 单位统计 顶部卡片总计信息
 */
@Data
public class DeptStatisTopVoDZ implements Serializable {
    private static final long serialVersionUID = 1L;

    // 分发数
    private int planNumber;
    // 注册数
    private int registerNumber;
    // 单机
    private int singleMachine;
    //专网机
    private int expertMachine;
    // 开机
    private int onCount;
    // 未开机
    private int offCount;
    // 开机率
    private double onCountRatio;


}
