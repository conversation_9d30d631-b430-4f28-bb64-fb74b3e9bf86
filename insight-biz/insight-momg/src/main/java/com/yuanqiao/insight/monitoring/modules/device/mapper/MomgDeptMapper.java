package com.yuanqiao.insight.monitoring.modules.device.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.monitoring.modules.device.entity.MomgDept;
import com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDeptVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 终端组织机构
 * @Author: jeecg-boot
 * @Date:   2021-03-31
 * @Version: V1.0
 */
@Component
public interface MomgDeptMapper extends BaseMapper<MomgDept> {

	/**
	 * 编辑节点状态
	 * @param id
	 * @param status
	 */
	void updateTreeNodeStatus(@Param("id") String id, @Param("status") String status);

    List<MomgDept> selectByParentId(String momgDeptId);


	IPage<MomgDept> pageSelectDeptByPid(Page<?> page, @Param("pid") String pid);

	List<MomgDept> pageCountDeptByPid(String pid);

	List<MomgDept> getCityId(@Param("id") String id);

    Integer getAll( @Param("types") List<String> types );

	Integer getCityOnCount(@Param("macAddr") String macAddr ,@Param("types") List<String> types );

	Integer getOn(@Param("types") List<String> types);

	TerminalDeptVo getDeptId(@Param("id") String id, @Param("types") List<String> types);

	Integer getDeptIdOff(@Param("id") String id, @Param("types") List<String> types);
}
