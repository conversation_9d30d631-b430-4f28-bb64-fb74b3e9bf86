package com.yuanqiao.insight.monitoring.modules.ledger.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @Description: 台账资产变更记录
 * @Author: jeecg-boot
 * @Date:   2020-03-18
 * @Version: V1.0
 */
@Data
@TableName("ledger_change")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="ledger_change对象", description="台账资产变更记录")
public class LedgerChange {

	/**id*/
	@TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "id")
	private String id;
	/**资产id*/
	@NotNull
    @ApiModelProperty(value = "资产id")
	private String manageId;
	/**资产编号*/
	@Excel(name = "资产编号", width = 15)
	@NotNull
    @ApiModelProperty(value = "资产编号")
	private String assetNo;
	/**资产名称*/
	@NotNull
	@Excel(name = "资产名称", width = 15)
    @ApiModelProperty(value = "资产名称")
	private String assetName;
	/**资产类型*/
	@NotNull
	@Excel(name = "资产类型", width = 15)
    @ApiModelProperty(value = "资产类型")
	private String assetType;

    /**资产状态*/
    @Excel(name = "资产状态", width = 15,replace = { "已采购_ycg", "已下发_yxf", "已部署_ybs", "已上线_ysx", "已暂存_yhc", "已转移_yzy", "已销毁_yxh", "已维修_ywx", "已使用_ysy" })
    @ApiModelProperty(value = "资产状态")
    private String assetStatus;
	/**添加方式*/
	@Excel(name = "添加方式", width = 15)
    @ApiModelProperty(value = "添加方式")
	private String addType;
	/**采购日期*/
	@Excel(name = "采购日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "采购日期")
	private Date purchaseDate;
	/**采购人*/
	@Excel(name = "采购人", width = 15)
    @ApiModelProperty(value = "采购人")
	private String purchaseUser;
	/**下发日期*/
	@Excel(name = "下发日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "下发日期")
	private Date downDate;
	/**下发地*/
	@Excel(name = "下发地", width = 15)
    @ApiModelProperty(value = "下发地")
	private String downPlace;
	/**部署日期*/
	@Excel(name = "部署日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "部署日期")
	private Date deployDate;
	/**单位*/
	@Excel(name = "单位", width = 15)
    @ApiModelProperty(value = "单位")
	private String deployCompany;
	/**上线日期*/
	@Excel(name = "上线日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "上线日期")
	private Date onlineDate;
	/**使用人*/
	@Excel(name = "使用人", width = 15)
    @ApiModelProperty(value = "使用人")
	private String userid;
	/**暂存日期*/
	@Excel(name = "暂存日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "暂存日期")
	private Date leaveDate;
	/**暂存地*/
	@Excel(name = "暂存地", width = 15)
    @ApiModelProperty(value = "暂存地")
	private String leavePlace;
	/**转移扶贫日期*/
	@Excel(name = "转移扶贫日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "转移扶贫日期")
	private Date transferDate;
	/**转移地*/
	@Excel(name = "转移地", width = 15)
    @ApiModelProperty(value = "转移地")
	private String transferPlace;
	/**销毁日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "销毁日期")
	private Date destructionDate;
	/**销毁备注*/
	@ApiModelProperty(value = "销毁备注")
	private String destructionRemarks;
	/**updateBy*/
    @ApiModelProperty(value = "updateBy")
	private String updateBy;
	/**updateTime*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "updateTime")
	private Date updateTime;
	/**createBy*/
    @ApiModelProperty(value = "createBy")
	private String createBy;
	/**createTime*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "createTime")
	private Date createTime;
}
