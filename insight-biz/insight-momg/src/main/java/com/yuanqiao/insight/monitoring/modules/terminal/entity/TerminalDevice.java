package com.yuanqiao.insight.monitoring.modules.terminal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yuanqiao.insight.common.util.common.TimeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecg.modules.system.entity.SysUsers;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 终端设备表
 * @Author: jeecg-boot
 * @Date: 2021-05-10
 * @Version: V1.0
 */
@Data
@TableName("momg_terminal")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "momg_terminal对象", description = "终端设备表")
public class TerminalDevice implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
    /**
     * 资产id
     */
//	@Excel(name = "资产id", width = 15)
    @ApiModelProperty(value = "资产id")
    @Dict(dictTable = "cmdb_assets", dicText = "assets_name", dicCode = "id")
    private String assetsId;
    /**
     * 名称
     */
    @Excel(name = "终端名称", width = 30)
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 单位ID
     */
    @Excel(name = "单位", width = 15, dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
    @ApiModelProperty(value = "单位ID")
    @Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
    private String deptId;
    //    @Excel(name = "单位", width = 15)
    @TableField(exist = false)
    private String deptName;
    @TableField(exist = false)
    private String deptCode;
    // 所在平台标识（用于区分数据属于本系统自有或来自下级上报，仅本系统自有数据允许编辑）
    private String platformCode;

    /**
     * 使用人
     */
    @ApiModelProperty(value = "使用人")
    private String username;
//    @Excel(name = "使用部门", width = 15)
//    @ApiModelProperty(value = "使用单位")
//    private String userDepartment;

    @Excel(name = "管理人", width = 25)
    private String administrator;

    @Excel(name = "管理部门", width = 25)
    private String adminDepartment;

    @Excel(name = "联系方式", width = 25)
    private String phone;

    /**
     * 状态
     */
//    @Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态")
    private Integer status;

    @Excel(name = "设备状态", width = 15, dicCode = "device_status")
    @Dict(dicCode = "device_status")
    @TableField(exist = false)
    private Integer deviceStatus;

    @Excel(name = "设备id", width = 30, dicCode = "deviceId")
    @TableField(exist = false)
    private String deviceId;
    // 终端类型
    //0-服务器,6-桌面机,8-笔记本
    @Excel(name = "终端类型", width = 15, dicCode = "resources_type")
    @Dict(dicCode = "resources_type")
    private Integer terminalType;
    /**
     * 是否启用，0：未激活，1：已激活
     */
//    @Excel(name = "激活状态，0：未激活，1：已激活", width = 15)
    @Excel(name = "激活状态", width = 15, dicCode = "terminal_enable")
    @Dict(dicCode = "terminal_enable")
    @ApiModelProperty(value = "是否启用，0：未激活，1：已激活")
    private Integer enable;
    @TableField(exist = false)

    @Excel(name = "网络类型", width = 15, dicCode = "terminal_network")
    @Dict(dicCode = "terminal_network")
    private String gatewayType;
    @TableField(exist = false)
    private String gatewayName;
    @TableField("SN")
    /*sn号*/
    @Excel(name = "SN", width = 15)
    private String sn;
    /**
     * 设备ip
     */
    @Excel(name = "终端IP", width = 15)
    @ApiModelProperty(value = "设备ip")
    private String ip;
    /**
     * mac地址
     */
    @Excel(name = "MAC地址（XX:XX:XX:XX:XX:XX）", width = 20)
    @ApiModelProperty(value = "mac地址")
    private String macAddr;
    /**
     * cpu平台架构
     */
    @Excel(name = "平台架构", width = 15)
    @ApiModelProperty(value = "cpu平台架构")
    private String cpuArch;

    @TableField(exist = false)
    private Integer internetFlag;

    @TableField(exist = false)
    @Dict(dictTable = "sys_depart", dicText = "address", dicCode = "id")
    private java.lang.String deptAddress;
    /**
     * 描述
     */
//	@Excel(name = "描述", width = 15)
    @ApiModelProperty(value = "描述")
    private String description;
    /**
     * 删除标识1:删除
     */
//	@Excel(name = "删除标识1:删除", width = 15)
    @ApiModelProperty(value = "删除标识1:删除")
    private Integer delflag;


    /**
     * 物理位置ID
     */
//	@Excel(name = "物理位置ID", width = 15)
    @ApiModelProperty(value = "物理位置ID")
    private String positionId;
    /**
     * 位置标签
     */
//	@Excel(name = "位置标签", width = 15)
    @ApiModelProperty(value = "位置标签")
    private String positionTag;


    //上面用字典获取，没必要单独的文本字段
   /* @TableField(exist = false)
    @Dict(dicCode = "resources_type")
    private String terminalTypeName;*/

    /**
     * 终端所有的mac和ip的关系
     */
    private String macIp; //添加或者注册的时候把macIp添加上,
    /**
     * cpu信息原始值
     */
//	@Excel(name = "cpu信息原始值", width = 15)
    @ApiModelProperty(value = "cpu信息原始值")
    private String cpuname;
    /**
     * cpu类型
     */
	@Excel(name = "cpu类型", width = 15, dicCode = "cpuType")
    @ApiModelProperty(value = "cpu类型")
    @Dict(dicCode = "cpuType")
    private String cpuType;
    /**
     * 操作系统信息原始值
     */
//	@Excel(name = "操作系统信息原始值", width = 15)
    @ApiModelProperty(value = "操作系统信息原始值")
    private String osname;
    /**
     * 操作系统类型
     */
	@Excel(name = "操作系统类型", width = 15, dicCode = "os_type")
    @ApiModelProperty(value = "操作系统类型")
    @Dict(dicCode = "os_type")
    private String osType;
    /**
     * 终端唯一标识
     */
//    @Excel(name = "终端唯一标识", width = 15)
    @ApiModelProperty(value = "唯一标识")
    private String uniqueCode;

    @TableField(exist = false)
    private java.lang.String assetsName;


    @TableField(exist = false)
    private String isStatus;


    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;


    private String remarks, address, addrDetail;


    private Integer addrId;

    private String clientIp;

    @TableField(exist = false)
    private String mac;


    @TableField(exist = false)
    private String grafanaUrl, gatewayCode;

    @TableField(exist = false)
    private String macOrIp;
    @TableField(exist = false)
    private String departIds;
    @TableField(exist = false)
    private String terType;

    @Excel(name = "告警状态", width = 15, dicCode = "device_alarm_status")
    @TableField(exist = false)
    private String alarmStatus;
    @TableField(exist = false)
    private String onCount; //开机次数
    @TableField(exist = false)
    private Long onTotal; //开机总时长/分钟
    @TableField(exist = false)
    private String lastOn; //最近开机时间

    @TableField(exist = false)
    private String runTime; //运行时长

    @TableField(exist = false)
    private String bindUser; //绑定人账号
    @TableField(exist = false)
    private String bindTime; //绑定事件
    @TableField(exist = false)
    private String bindStatus; //绑定状态

    @Excel(name = "使用人", width = 15)
    @TableField(exist = false)
    private String usernameText;

    @TableField(exist = false)
    private SysUsers user;

    @Excel(name = "开机总时长", width = 15)
    @TableField(exist = false)
    private String onTotal_text;
    public String getOnTotal_text(){
        return TimeUtils.getDistanceTime(this.getOnTotal());
    }



    //动态信息
    @TableField(exist = false)
    String cpuRateStr = "";
    @TableField(exist = false)
    String memUtilizRateStr = "";
    @TableField(exist = false)
    String sysVersion = "";
    @TableField(exist = false)
    String diskRateStr = "";
    @TableField(exist = false)
    String diskFreeStr = "";
    @TableField(exist = false)
    String diskTotalStr = "";
    @TableField(exist = false)
    String diskUsedStr = "";
    @TableField(exist = false)
    String sysUptimeStr = "";
    @TableField(exist = false)
    String memTotalStr = "";
    @TableField(exist = false)
    String memFreeStr = "";
    @TableField(exist = false)
    String memUsedStr = "";
    @TableField(exist = false)
    String cpuNameStr = "";

}
