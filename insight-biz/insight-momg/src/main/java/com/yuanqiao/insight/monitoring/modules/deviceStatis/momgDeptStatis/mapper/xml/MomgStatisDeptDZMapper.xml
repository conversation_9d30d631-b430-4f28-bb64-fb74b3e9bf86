<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.monitoring.modules.deviceStatis.momgDeptStatis.mapper.MomgStatisDeptDZMapper">

    <!--列表中的信息-->
    <select id="findStatisList"
            resultType="com.yuanqiao.insight.monitoring.modules.deviceStatis.momgDeptStatis.entity.DeptStatisVo2DZ">
        with a as
        (
            select
            d.id, d.momg_dept_id dept_id, d.momg_dept_name dept_name,
            sum(case when s.on_total > 0 then s.on_total else 0 end ) on_time,
            count(case when s.on_total > 0 then 1 end) hf_ontime
            from momg_device_statis s
            right join momg_device_info d on d.id = s.device_id
            right join momg_terminal ter ON d.device_code = ter.unique_code
        where s.create_time &gt; #{startTime} and s.create_time &lt; #{endTime}
            and ter.id is not null and ter.delflag = 0 and ter.terminal_type != 8 and ter.terminal_type != 180
            group by d.id, d.momg_dept_id, d.momg_dept_name
        ),
        format_sys_depart as (
            select
            id, depart_name,
            (case when plan_number is null then 0 when plan_number = '' then 0  else plan_number end) as plan_number,
            (case when latitude    is null then 0 when latitude    = '' then 0  else latitude end)    as latitude,
            (case when longitude   is null then 0 when longitude   = '' then 0  else longitude end)   as longitude
            from sys_depart
        ),
        b as (
            select a.dept_id, count(a.id) as registerNumber
            , count(case when a.on_time > 0 then 1 end) as onCount
            , COALESCE( count(case when a.on_time > 0 then 1 end), 0 ) as onlineTotal
            , (d.plan_number - d.latitude - d.longitude - ( count(case when a.on_time > 0 then 1 end)) ) as offCount
            , ( case when ( d.plan_number - d.latitude - d.longitude) != 0 then  ROUND(count(case when a.on_time > 0 then 1 end) * 100 / ( d.plan_number - d.latitude - d.longitude),  1)  else 0 end )  as onCountRatio

            from  a
            left join format_sys_depart d on a.dept_id = d.id
            group by a.dept_id, d.plan_number, d.latitude, d.longitude
        )

        select d.id, d.depart_name as deptName, d.plan_number as planNumber, d.latitude as expertMachine, d.longitude as singleMachine, b.*
        from b
        left join format_sys_depart d on b.dept_id = d.id
        <where>
            <if test="deptIdList != null ">
                d.id in
                <foreach collection="deptIdList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test=" departIds != null and departIds != '' ">
                and d.id in
                <foreach collection="departIds.split(',')" item="departId" open="(" separator="," close=")">
                    #{departId}
                </foreach>
            </if>
        </where>

        union

        select
        d.id, d.depart_name as deptName, d.plan_number as planNumber,
        d.latitude as expertMachine, d.longitude as singleMachine, b.*
        from format_sys_depart d
        left join b on b.dept_id = d.id
        where b.dept_id is null
        <if test="deptIdList != null ">
            and d.id in
            <foreach collection="deptIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test=" departIds != null and departIds != '' ">
            and d.id in
            <foreach collection="departIds.split(',')" item="departId" open="(" separator="," close=")">
                #{departId}
            </foreach>
        </if>
    </select>


</mapper>
