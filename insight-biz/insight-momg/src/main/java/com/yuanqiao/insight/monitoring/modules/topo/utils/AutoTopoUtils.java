package com.yuanqiao.insight.monitoring.modules.topo.utils;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.common.util.ping.PingTestUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class AutoTopoUtils {

    public static final Map<String, String> oIDs = new HashMap<String, String>() {{
        put("STPTable", ".*******.********.15"); //网桥表
        put("IFTable", ".*******.*******"); //端口表
        put("TPTable", ".*******.********.3"); //Mac表
        put("ARPTable", ".*******.********"); //Arp表
    }};

    public static void pingPublicize(List<JSONObject> devList) {
        try {
            for (JSONObject connInfo : devList) {
                PingTestUtil.ping(connInfo.getString("ip"));
            }
        } catch (Exception e) {
            log.error("pingPublicize error", e);
        }
    }
}
