package com.yuanqiao.insight.monitoring.modules.terminal.utils;

import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import org.influxdb.InfluxDB;
import org.influxdb.InfluxDBFactory;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.Point;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.springframework.beans.factory.annotation.Value;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @title: InfluxDbUtils
 * @projectName insight-control-parent
 * @description: TODO
 * @date 2020/11/11-14:02
 * 接口协议 HTTP API ，已经提供
 */
@Slf4j
@Deprecated
public class InfluxDbUtils {
    private InfluxDB influxDB;
    @Value("${influxdb.username}")
    String username;
    @Value("${influxdb.password}")
    String password;
    @Value("${influxdb.openurl}")
    String openurl;
    @Value("${influxdb.database}")
    String database;
    static OkHttpClient.Builder client = new OkHttpClient.Builder()
            .connectTimeout(60, TimeUnit.MINUTES)
            .readTimeout(60, TimeUnit.MINUTES)
            .writeTimeout(60, TimeUnit.MINUTES)
            .retryOnConnectionFailure(false)
            .connectionPool(new ConnectionPool(0,50,TimeUnit.SECONDS));
    @SuppressWarnings("deprecation")
    public InfluxDbUtils(Map<String,String> map) {
        try{
            this.openurl = map.get("openurl");
            this.username = map.get("username");
            this.password =  map.get("password");
            this.database = map.get("database");
            influxDB = InfluxDBFactory.connect(openurl, username, password,client);

        }catch (Exception e){
            log.error(e.getMessage());
        }

    }
    /**
     * 设置数据保存策略
     * defalut 策略名 /database 数据库名/ 30d 数据保存时限30天/ 1  副本个数为1/ 结尾DEFAULT 表示 设为默认的策略
     */
    public void createRetentionPolicy() {
        String command = String.format("CREATE RETENTION POLICY \"%s\" ON \"%s\" DURATION %s REPLICATION %s DEFAULT",
                "defalut", database, "3d", 1);
        this.query(command);
    }

    /**
     * 插入
     *
     * @param
     */
    @SuppressWarnings("deprecation")
    public  void insert(Point point) {
        if (!influxDB.databaseExists(database)) {
            influxDB.createDatabase(database);
        }
        try {
            if(influxDB==null){
                influxDB = InfluxDBFactory.connect(openurl, username, password, client);
                if (!influxDB.databaseExists(database)) {
                    influxDB.createDatabase(database);
                }
            }
            influxDB.write(point);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 批量插入
     *
     * @param
     */
    @SuppressWarnings("deprecation")
    public  void insertList(BatchPoints batchPoints) {

        try {
            if(influxDB==null){
                influxDB = InfluxDBFactory.connect(openurl, username, password, client);
                influxDB.createDatabase(database);
            }
            influxDB.write(batchPoints);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 查询
     *
     * @param command 查询语句
     * @return
     */
    public QueryResult query(String command) {
        //log.info("com.yuanqiao.insight.utils.InfluxDbUtils.query(command={})", command);
        if(influxDB==null){
            influxDB = InfluxDBFactory.connect(openurl, username, password, client);
        }
        return influxDB.query(new Query(command, database));
    }

    /**
     * 删除
     *
     * @param command 删除语句
     * @return 返回错误信息
     */
    public String deleteMeasurementData(String command) {
        if(influxDB==null){
            influxDB = InfluxDBFactory.connect(openurl, username, password, client);
        }
        QueryResult result = influxDB.query(new Query(command, database));
        return result.getError();
    }

    /**
     * 删除表
     *
     * @param measurement
     * @return 返回错误信息
     */
    public void deleteMeasurement(String measurement) {
        //log.info("dropMeasurement measurement:{}",measurement);
        if(influxDB==null){
            influxDB = InfluxDBFactory.connect(openurl, username, password, client);
        }
        influxDB.query(new Query("drop measurement \"" + measurement + "\"", database));
    }

    /**
     * 关闭数据库
     */
    public void close(){
        influxDB.close();
    }

    @Override
    public String toString() {
        return "InfluxDbUtils{" +
                "influxDB=" + influxDB +
                ", username='" + username + '\'' +
                ", password='" + password + '\'' +
                ", openurl='" + openurl + '\'' +
                ", database='" + database + '\'' +
                '}';
    }
}
