package com.yuanqiao.insight.monitoring.modules.topo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuanqiao.insight.monitoring.modules.topo.entity.AutoTopoArp;

import java.util.List;

/**
 * @Description: 自动拓扑-arp表
 * @Author: jeecg-boot
 * @Date:   2025-01-08
 * @Version: V1.0
 */
public interface IAutoTopoArpService extends IService<AutoTopoArp> {

    void refreshBatch(List<AutoTopoArp> list);
}
