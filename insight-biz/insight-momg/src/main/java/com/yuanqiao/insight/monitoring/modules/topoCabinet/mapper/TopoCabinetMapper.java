package com.yuanqiao.insight.monitoring.modules.topoCabinet.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanqiao.insight.monitoring.modules.topoCabinet.entity.CabinetToDeviceVo;
import com.yuanqiao.insight.monitoring.modules.topoCabinet.entity.TopoCabinet;
import com.yuanqiao.insight.service.device.entity.DeviceInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 机柜表
 * @Author: jeecg-boot
 * @Date:   2021-05-13
 * @Version: V1.0
 */
@Component
public interface TopoCabinetMapper extends BaseMapper<TopoCabinet> {


    List<DeviceInfo> selectAddableDevice(@Param("q") String q, @Param("viewFlag") Integer viewFlag);

    void updateCarbinetToDevice(@Param("cabinetId") String cabinetId, @Param("layer") Integer layer, @Param("devId") String devId,@Param("u") String u);

    List<CabinetToDeviceVo> selectDevIdsByCarbinetId(String id);

    DeviceInfo selectDeviceByDevId(String id);

    Integer selectDeviceByDevIdAndCarbinetId(@Param("obj") String obj, @Param("id") String id);

    TopoCabinet selectCabinetById(String id);
}
