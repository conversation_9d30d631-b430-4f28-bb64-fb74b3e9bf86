package com.yuanqiao.insight.monitoring.modules.loganalyze.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yuanqiao.insight.monitoring.modules.loganalyze.entity.LogAnalyzeTerm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @Description: 日志分析
 * @Author: jeecg-boot
 * @Date:   2024-02-02
 * @Version: V1.0
 */
@Data
@ApiModel(value="momg_log_analyzePage对象", description="日志分析")
public class LogAnalyzePage {

	/**id*/
	private String id;
	/**数据视图名称*/
  	@Excel(name = "数据视图名称", width = 15)
	private String dataViewName;
	/**索引模式*/
  	@Excel(name = "索引模式", width = 15)
	private String indexPatterns;
	/**时间戳字段*/
  	@Excel(name = "时间戳字段", width = 15)
	private String timestampFiled;
	/**创建人*/
  	@Excel(name = "创建人", width = 15)
	private String createBy;
	/**创建时间*/
  	@Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
  	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date createTime;
	/**修改人*/
  	@Excel(name = "修改人", width = 15)
	private String updateBy;
	/**修改时间*/
  	@Excel(name = "修改时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
  	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

	@ExcelCollection(name="客户明细")
	@ApiModelProperty(value = "客户明细")
	private List<LogAnalyzeTerm> logAnalyzeTermList;

}
