package com.yuanqiao.insight.monitoring.modules.device;

import cn.hutool.core.collection.CollUtil;
import com.yuanqiao.insight.acore.depart.entity.SysDepart;
import com.yuanqiao.insight.service.device.entity.MomgDeviceGroup;
import com.yuanqiao.insight.service.device.service.IMomgDeviceGroupService;
import com.yuanqiao.insight.service.device.service.IMomgGroup2UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.system.service.ISysDepartService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GroupAndUserAuthControl {
    @Autowired
    private ISysDepartService sysDepartService;
    @Autowired
    private IMomgGroup2UserService momgGroup2UserService;
    @Autowired
    private IMomgDeviceGroupService momgDeviceGroupService;


    public Map<String, List<String>> getGroupIdsAndDeptIdsByLoginUser(String groupId) {
        HashMap<String, List<String>> resultMap = new HashMap<>();

        boolean isMaxPrivilege = false;

        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (sysUser != null && sysUser.getUserIdentity() == 2) {
            isMaxPrivilege = true; //用户为上级
        }
        if (StringUtils.isNotEmpty(groupId)) {
            MomgDeviceGroup momgDeviceGroup = momgDeviceGroupService.getById(groupId);
            if (momgDeviceGroup.getIsMaxPrivilege() == 1) {
                isMaxPrivilege = true; // 指定的分组拥有最大权限
            } else {
                isMaxPrivilege = false; // 将指定的分组权限覆盖掉用户权限
            }
        }

        List<String> groupIdList = new ArrayList<>();
        if (!isMaxPrivilege) {
            if (StringUtils.isNotEmpty(groupId)) {
                groupIdList.add(groupId);
            } else {
                List<MomgDeviceGroup> deviceGroupList = momgGroup2UserService.getGroupsByUsername(sysUser.getUsername());
                if (CollUtil.isNotEmpty(deviceGroupList)) {
                    List<MomgDeviceGroup> maxPrivilegeGroup = deviceGroupList.stream().filter(g -> g.getIsMaxPrivilege() != null && g.getIsMaxPrivilege() == 1).collect(Collectors.toList());
                    if (CollUtil.isEmpty(maxPrivilegeGroup)) {
                        groupIdList = deviceGroupList.stream().map(MomgDeviceGroup::getId).collect(Collectors.toList());
                    } else {
                        isMaxPrivilege = true; // 用户所在分组用于最大权限
                    }
                } else { // 未查询到分组，不可返回空集合，否则会查询全部
                    groupIdList = Collections.singletonList("noSuchGroup");
                }
            }
        }
        resultMap.put("groupIds", groupIdList);

        List<String> deptIds = new ArrayList<>();
        if (!isMaxPrivilege) {
            List<SysDepart> departTreeList = sysDepartService.queryTreeList(sysUser);
            // 用户负责部门
            if (CollUtil.isNotEmpty(departTreeList)) {
                departTreeList.stream().map(SysDepart::getId).forEach(deptIds::add);
            } else { // 未查询到分组，不可返回空集合，否则会查询全部
                deptIds = new ArrayList<>(Collections.singletonList("noSuchDept"));
            }
        }
        resultMap.put("deptIds", deptIds);

        return resultMap;
    }


}