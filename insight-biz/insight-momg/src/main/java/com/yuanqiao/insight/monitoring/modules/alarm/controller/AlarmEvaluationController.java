package com.yuanqiao.insight.monitoring.modules.alarm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.monitoring.modules.alarm.entity.AlarmEvaluation;
import com.yuanqiao.insight.monitoring.modules.alarm.service.AlarmEvaluationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.system.entity.SysUsers;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Api(tags = "告警评价")
@RestController
@RequestMapping("/alarm/alarmEvaluation")
@Slf4j
public class AlarmEvaluationController extends JeecgController<AlarmEvaluation, AlarmEvaluationService> {

    @Autowired
    private ISysUserService sysUserService;

    @AutoLog(value = "告警评价-分页列表查询")
    @ApiOperation(value = "告警评价-分页列表查询", notes = "告警评价-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(AlarmEvaluation alarmEvaluation,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<AlarmEvaluation> queryWrapper = QueryGenerator.initQueryWrapper(alarmEvaluation, req.getParameterMap());
        queryWrapper.orderByDesc("create_time");
        Page<AlarmEvaluation> page = new Page<AlarmEvaluation>(pageNo, pageSize);
        IPage<AlarmEvaluation> pageList = service.page(page, queryWrapper);
        pageList.getRecords().forEach(evaluation -> {
            if (StringUtils.isNotEmpty(evaluation.getCreateBy())) {
                SysUsers user = sysUserService.getUserByName(evaluation.getCreateBy());
                evaluation.setAvatar(user.getAvatar());
            }
        });
        return Result.OK(pageList);
    }


    /**
     *   添加
     *
     * @param alarmEvaluation
     * @return
     */
    @AutoLog(value = "告警评价-添加")
    @ApiOperation(value="告警评价-添加", notes="告警评价-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody AlarmEvaluation alarmEvaluation) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        alarmEvaluation.setEvaluationUser(loginUser.getRealname());
        service.save(alarmEvaluation);
        return Result.OK("添加成功！");
    }

    /**
     * 通过id删除
     *
     *
     * @param id
     * @return
     */
    @AutoLog(value = "告警评价-通过id删除")
    @ApiOperation(value = "告警评价-通过id删除", notes = "告警评价-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        service.removeById(id);
        return Result.OK("删除成功!");
    }



}
