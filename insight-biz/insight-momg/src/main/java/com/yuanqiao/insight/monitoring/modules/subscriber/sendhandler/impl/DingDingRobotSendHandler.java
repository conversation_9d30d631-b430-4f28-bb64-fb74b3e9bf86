package com.yuanqiao.insight.monitoring.modules.subscriber.sendhandler.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yuanqiao.insight.acore.dingtips.service.RobotService;
import com.yuanqiao.insight.system.config.ISendHandler;
import com.yuanqiao.insight.system.config.emus.NoticeType;
import org.apache.commons.lang.StringUtils;
import org.jeecg.modules.system.entity.SysUsers;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 钉钉机器人
 *
 * <AUTHOR>
 * @date 2023/3/13
 */
@Component
public class DingDingRobotSendHandler implements ISendHandler {
    @Autowired
    private ISysUserService userService;

    @Override
    public boolean support(String support) {
        return StringUtils.equals(support, NoticeType.DINGDING_ROBOT.getCode());
    }

    @Override
    public void send(JSONObject sendMsg, JSONObject template, Boolean isPar) {
        String webHook = sendMsg.getString("webhook");
        String secret = sendMsg.getString("secret");

        //消息内容
        String content = template.getString("content");
        String subject = template.getString("subject");
        //重组消息体
        if (isPar) {
            JSONObject par = template.getJSONObject("par");
            content = generateWelcome(par, content);
        }
        // 消息体中存在html标签会导致钉钉内无法正常显示消息内容，清除html标签
        //content = Jsoup.parse(content).text();
        //收件方
        List<String> userPhoneList = new ArrayList<>();
        JSONArray sentTo = template.getJSONArray("sendTo");
        if (CollUtil.isNotEmpty(sentTo)) {
            userPhoneList = userService.list(new QueryWrapper<SysUsers>().lambda().in(SysUsers::getId, sentTo)).stream().filter(f -> f.getPhone() != null && f.getPhone() != "").map(SysUsers::getPhone).collect(Collectors.toList());
        }
        //发送消息
        new RobotService().markdown(webHook, secret, subject, content, userPhoneList);
    }
}
