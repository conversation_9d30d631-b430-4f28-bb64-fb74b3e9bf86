package com.yuanqiao.insight.monitoring.modules.alarm.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.monitoring.modules.alarm.entity.AlarmHandleProcess;
import com.yuanqiao.insight.monitoring.modules.alarm.entity.AlarmHistory;
import com.yuanqiao.insight.monitoring.modules.alarm.enums.AlarmHandleStatus;
import com.yuanqiao.insight.monitoring.modules.alarm.mapper.AlarmHandleProcessMapper;
import com.yuanqiao.insight.monitoring.modules.alarm.service.AlarmAssignHistoryService;
import com.yuanqiao.insight.monitoring.modules.alarm.service.AlarmHandleProcessService;
import com.yuanqiao.insight.monitoring.modules.alarm.service.IAlarmHistoryService;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;

@Service
public class AlarmHandleProcessServiceImpl extends ServiceImpl<AlarmHandleProcessMapper, AlarmHandleProcess> implements AlarmHandleProcessService {
    @Autowired
    private IAlarmHistoryService alarmHistoryService;
    @Autowired
    private AlarmAssignHistoryService alarmAssignHistoryService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> startHandle(AlarmHandleProcess record) {
        if (StringUtils.isBlank(record.getErrorType())) {
            return Result.error("故障类型不能为空");
        }
        if (StringUtils.isBlank(record.getErrorDesc())) {
            return Result.error("故障描述不能为空");
        }
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String handler = loginUser.getUsername();

        AlarmHistory alarm = alarmHistoryService.getById(record.getAlarmId());
        if (alarm == null) {
            return Result.error("告警不存在");
        }
        // 只有未处理状态的告警可以开始处理
        if (!AlarmHandleStatus.UNHANDLED.getCode().equals(alarm.getFlowHandleStatus())) {
            return Result.error("当前告警状态不允许处理");
        }
        // 更新告警状态为处理中
        alarm.setFlowHandleStatus(AlarmHandleStatus.PROCESSING.getCode());
        alarmHistoryService.updateById(alarm);
        // 写入处理记录表 - 开始处理记录
        record.setHandler(handler);
        record.setHandleType("0");
        this.save(record);
        // 记录处理历史
        alarmAssignHistoryService.saveByIdsName(record.getAlarmId(), handler, "开始处理告警");
        return Result.OK("开始处理成功!");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> completeHandle(AlarmHandleProcess record) {
        if (StringUtils.isBlank(record.getAlarmId())) {
            return Result.error("告警ID不能为空");
        }
        if (StringUtils.isBlank(record.getHandleResult())) {
            return Result.error("处理结果不能为空");
        }
        if (StringUtils.isBlank(record.getErrorReason())) {
            return Result.error("故障原因不能为空");
        }

        AlarmHistory alarm = alarmHistoryService.getById(record.getAlarmId());
        if (alarm == null) {
            return Result.error("告警不存在");
        }
        // 只有处理中状态的告警可以完成处理
        if (!AlarmHandleStatus.PROCESSING.getCode().equals(alarm.getFlowHandleStatus())) {
            return Result.error("当前告警状态不允许完成处理");
        }
        // 检查是否存在开始处理记录
        AlarmHandleProcess completeRecord = this.getOne(new LambdaQueryWrapper<AlarmHandleProcess>()
                .eq(AlarmHandleProcess::getAlarmId, record.getAlarmId()));
        if (completeRecord==null) {
            return Result.error("告警处理为空！");
        }
        // 新增"完成处理"记录
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String handler = loginUser.getUsername();
        completeRecord.setHandleType("1");
        completeRecord.setHandleResult(record.getHandleResult());
        completeRecord.setErrorReason(record.getErrorReason());
        completeRecord.setRemark(record.getRemark());
        this.updateById(completeRecord);
        // 更新告警状态为处理完成
        alarm.setFlowHandleStatus(AlarmHandleStatus.COMPLETED.getCode());
        alarmHistoryService.updateById(alarm);
        // 记录处理历史
        alarmAssignHistoryService.saveByIdsName(record.getAlarmId(), handler, "完成告警处理：" + record.getHandleResult());
        alarmHistoryService.modifyHistory(Collections.singletonList(alarm), alarm.getTemplateId());
        return Result.OK("处理完成!");
    }
} 
