package com.yuanqiao.insight.monitoring.modules.product.controller;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.service.ScheduleSetting.entity.ScheduleSetting;
import com.yuanqiao.insight.service.ScheduleSetting.service.IScheduleSettingService;
import com.yuanqiao.insight.service.device.mapper.DeviceInfoMapper;
import com.yuanqiao.insight.service.product.entity.MomgProductJob;
import com.yuanqiao.insight.service.product.entity.MomgProductTransferJob;
import com.yuanqiao.insight.service.product.entity.MomgTransferProtocol;
import com.yuanqiao.insight.service.product.service.IMomgProductJobService;
import com.yuanqiao.insight.service.product.service.IMomgProductTransferJobService;
import com.yuanqiao.insight.service.product.service.IMomgTransferProtocolService;
import liquibase.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.mq.RedisMq;
import org.jeecg.common.mq.stream.Streams;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/device/productJob")
@Slf4j
public class ProductJobController extends JeecgController<MomgProductJob, IMomgProductJobService> {

    @Autowired
    private IMomgProductJobService momgProductJobService;
    @Autowired
    private IMomgProductTransferJobService momgProductTransferJobService;
    @Autowired
    private IScheduleSettingService scheduleSettingService;
    @Autowired
    private IMomgTransferProtocolService transferProtocolService;
    @Autowired
    private DeviceInfoMapper deviceInfoMapper;
    @Autowired
    private RedisMq redisMq;

    /**
     * 分页列表查询
     *
     * @param productJob
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @GetMapping(value = "/list")
    public Result<IPage<MomgProductJob>> queryPageList(MomgProductJob productJob,
                                                       @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                       HttpServletRequest req) {
        QueryWrapper<MomgProductJob> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotEmpty(productJob.getCode())) {
            queryWrapper.like("code", productJob.getCode());
        }
        if (StringUtils.isNotEmpty(productJob.getValue())) {
            queryWrapper.like("value", productJob.getValue());
        }
        queryWrapper.orderByDesc("CASE WHEN update_time is null OR create_time > update_time THEN create_time ELSE update_time END");
        Page<MomgProductJob> page = new Page<>(pageNo, pageSize);
        IPage<MomgProductJob> pageList = momgProductJobService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param productJob
     * @return
     */
    @PostMapping(value = "/add")
    public Result<Object> add(@RequestBody MomgProductJob productJob) {
        List<MomgProductJob> list = momgProductJobService.list(new QueryWrapper<MomgProductJob>().eq("code", productJob.getCode()));
        if (CollUtil.isNotEmpty(list)){
            return Result.error("标识重复！");
        }
        momgProductJobService.save(productJob);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param productJob
     * @return
     */
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<Object> edit(@RequestBody MomgProductJob productJob) {
        List<MomgProductJob> list = momgProductJobService.list(new QueryWrapper<MomgProductJob>().eq("code", productJob.getCode()).ne("id", productJob.getId()));
        if (CollUtil.isNotEmpty(list)){
            return Result.error("标识重复！");
        }
        momgProductJobService.updateById(productJob);
        List<ScheduleSetting> scheduleSettingList = new ArrayList<>();
        List<MomgProductTransferJob> productTransferJobList = momgProductTransferJobService.list(new QueryWrapper<MomgProductTransferJob>().eq("product_job_id", productJob.getId()));
        if (CollUtil.isNotEmpty(productTransferJobList)) {
            for (MomgProductTransferJob productTransferJob : productTransferJobList) {
                //同步scheduleSetting Job表
                MomgTransferProtocol transferProtocol = transferProtocolService.getById(productTransferJob.getTransferProtocolId());
                List<ScheduleSetting> eleList = deviceInfoMapper.selectAllScheduleByProId(productTransferJob.getProductId(), null);
                if (CollUtil.isNotEmpty(eleList)) {
                    eleList.forEach(scheduleSetting -> {
                        if (scheduleSetting.getProtocol().equals(transferProtocol.getCode())) {
                            scheduleSetting.setJobClass(productJob.getValue());
                        }
                    });
                    scheduleSettingList.addAll(eleList);
                }
            }
            if (CollUtil.isNotEmpty(scheduleSettingList)) {
                scheduleSettingService.updateBatchById(scheduleSettingList);
                List<String> oldIds = scheduleSettingList.stream().map(ScheduleSetting::getId).collect(Collectors.toList());
                List<String> deviceKeys = scheduleSettingList.stream().map(ScheduleSetting::getDeviceCode).distinct().collect(Collectors.toList());
                //发布连接参数变更事件
                JSONObject data = new JSONObject();
                data.put("oldKey", oldIds);
                data.put("newKey", deviceKeys);
                redisMq.publish(Streams.DEVICE_MONITORING_CHANGE, data);
            }
        }
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @DeleteMapping(value = "/delete")
    public Result<Object> delete(@RequestParam(name = "id", required = true) String id) {
        List<MomgProductTransferJob> productTransferJobList = momgProductTransferJobService.list(new QueryWrapper<MomgProductTransferJob>().eq("product_job_id", id));
        if (CollUtil.isNotEmpty(productTransferJobList)) {
            return Result.error("当前JOB已被使用，删除失败！");
        }
        momgProductJobService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @DeleteMapping(value = "/deleteBatch")
    public Result<Object> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        List<MomgProductTransferJob> productTransferJobList = momgProductTransferJobService.list(new QueryWrapper<MomgProductTransferJob>().in("product_job_id", Arrays.asList(ids.split(","))));
        if (CollUtil.isNotEmpty(productTransferJobList)) {
            return Result.error("包含已被使用的JOB，删除失败！");
        }
        momgProductJobService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        MomgProductJob MomgProductJob = momgProductJobService.getById(id);
        if (MomgProductJob == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(MomgProductJob);
    }


}
