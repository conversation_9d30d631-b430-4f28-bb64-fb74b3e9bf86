<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.monitoring.modules.topo.mapper.TopoInfoMapper">

    <resultMap id="topoVo" type="com.yuanqiao.insight.monitoring.modules.topo.entity.TopoInfo">
        <!-- 定义主键 -->
        <!-- property：主键在实体类中的属性名 -->
        <!-- column：主键在数据库中的列名 -->
        <id column="id" property="id"/>
        <collection property="nodeList"
                    select="com.yuanqiao.insight.monitoring.modules.topo.mapper.TopoInfoMapper.findNodeList"
                    column="id">
        </collection>
        <collection property="edgeList"
                    select="com.yuanqiao.insight.monitoring.modules.topo.mapper.TopoInfoMapper.findEdgeList"
                    column="id">
        </collection>
    </resultMap>

    <resultMap id="nodeVo" type="com.yuanqiao.insight.monitoring.modules.topo.entity.NodeInfo">
        <association property="nodeDeviceInfo"
                    select="com.yuanqiao.insight.monitoring.modules.topo.mapper.TopoInfoMapper.findNodeDev"
                    column="device_code">
        </association>
    </resultMap>

    <select id="findName" resultType="com.yuanqiao.insight.monitoring.modules.topo.entity.ProCatVo">
                 select
        mp.display_name ,cac.category_name
    from
        momg_device_info mdi
    left join momg_product mp on
        mdi.product_id = mp.id
    left join cmdb_assets_category cac on cac.id = mp.assets_category_id
    where mdi.id = #{deviceId}
  </select>

    <select id="getTopoList" resultType="com.yuanqiao.insight.monitoring.modules.topo.entity.TopoInfo">

        select
          id, topo_name, topo_img, topo_svg, topo_type, show_type, topo_config
        from
          momg_topo_info
        <where>
            <if test="name != null and name != ''">
                and topo_name LIKE concat(concat('%',#{name}),'%')
            </if>
            <if test="topoType != null and topoType != ''">
                and topo_type = #{topoType}
            </if>
            <if test="showType != null and showType != ''">
                and show_type = #{showType}
            </if>
            <if test="ids != null and ids.size() > 0">
                and id in
                <foreach item="id" collection="ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        order by create_time DESC

    </select>

    <select id="findNodeList" resultMap="nodeVo">

        select *
        from
        momg_topo_node_info
        where
        topo_id = #{id}

    </select>

    <select id="findNodeDev" resultType="com.yuanqiao.insight.monitoring.modules.topo.entity.NodeDeviceVo">

        select d.id as deviceId, d.name as deviceName, d.device_code as deviceCode, d.ip as deviceIp, p.id as productId, p.display_name as productName
        from
            momg_device_info d
        left join
            momg_product p
        on
            d.product_id = p.id
        where
            d.device_code = #{deviceCode}

    </select>

    <select id="findEdgeList"  resultType="com.yuanqiao.insight.monitoring.modules.topo.entity.EdgeInfo">

        select *
        from
        momg_topo_edge_info
        where
        topo_id = #{id}

    </select>

    <select id="getTopoByIdJson" resultType="com.yuanqiao.insight.monitoring.modules.topo.entity.TopoInfo">

        select id, topo_name, topo_data_json, topo_img, topo_svg, topo_type, show_type, topo_config
        from
        momg_topo_info
        where
        id = #{id}

    </select>

    <select id="getTopoByIdNode" resultMap="topoVo">

        select id, topo_name, topo_img, topo_svg, topo_type, show_type, topo_config
        from
        momg_topo_info
        where
        id = #{id}

    </select>

    <select id="getTopoByIdAll" resultMap="topoVo">

        select *
        from
        momg_topo_info
        where
        id = #{id}

    </select>

    <select id="getTopoByIdNone" resultType="com.yuanqiao.insight.monitoring.modules.topo.entity.TopoInfo">

        select id, topo_name, topo_img, topo_svg, topo_type, show_type, topo_config
        from
        momg_topo_info
        where
        id = #{id}

    </select>

</mapper>
