package com.yuanqiao.insight.monitoring.modules.subscriber.sendhandler.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yuanqiao.insight.acore.config.MessageMail;
import com.yuanqiao.insight.system.config.ISendHandler;
import com.yuanqiao.insight.system.config.emus.NoticeType;
import org.apache.commons.lang.StringUtils;
import org.jeecg.modules.system.entity.SysUsers;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 阿里云短信
 *
 * <AUTHOR>
 * @date 2023/3/13
 */
@Component
public class AliMessageSendHandler implements ISendHandler {

    @Autowired
    private ISysUserService userService;

    @Override
    public boolean support(String support) {
        return StringUtils.equals(support, NoticeType.MESSAGE.getCode());
    }

    @Override
    public void send(JSONObject sendMsg, JSONObject template, Boolean isPar) throws Exception {
        String accessKeyId = sendMsg.getString("ID");
        String accessKeySecret = sendMsg.getString("secret");
        String https = sendMsg.getString("https");
        // 短信模板
        String subject = template.getString("subject"); // 短信模板
        String signName = template.getString("signName");// 短信签名
        String content = template.getString("content");
        if (isPar) {
            JSONObject par = template.getJSONObject("par");
            content = par.toJSONString();
        }
        //获取选择的用户id
        JSONArray sentTo = template.getJSONArray("sendTo");

        // 根据所选用户的id获取到用户手机号码
        List<String> userPhoneList = userService.list(new QueryWrapper<SysUsers>().lambda().in(SysUsers::getId, sentTo))
                .stream().map(SysUsers::getPhone).collect(Collectors.toList());
        String phones = String.join(",", userPhoneList);

        MessageMail.sendMessage(accessKeyId, accessKeySecret, https, phones, signName, subject, content);
    }
}
