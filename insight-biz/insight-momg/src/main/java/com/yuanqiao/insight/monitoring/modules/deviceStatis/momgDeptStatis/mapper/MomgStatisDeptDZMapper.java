package com.yuanqiao.insight.monitoring.modules.deviceStatis.momgDeptStatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanqiao.insight.monitoring.modules.deviceStatis.momgDeptStatis.entity.DeptStatisVo;
import com.yuanqiao.insight.monitoring.modules.deviceStatis.momgDeptStatis.entity.DeptStatisVo2DZ;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 单位统计定制化页面Mapper
 */
@Mapper
@Component
public interface MomgStatisDeptDZMapper extends BaseMapper<DeptStatisVo> {

    List<DeptStatisVo> findAll(@Param("createTime") String createTime ,List<String>deptIdList);

    // 单位统计 列表中 的信息
    List<DeptStatisVo2DZ>findStatisList(@Param("startTime")String startTime, @Param("endTime") String endTime, @Param("deptIdList")List<String>deptIdList, @Param("departIds")String departIds);

    // 单位统计 表头卡片信息
    // DeptStatisTopVoDZ findTopCardTotalInfo(@Param("limitTime") String limitTime);
}
