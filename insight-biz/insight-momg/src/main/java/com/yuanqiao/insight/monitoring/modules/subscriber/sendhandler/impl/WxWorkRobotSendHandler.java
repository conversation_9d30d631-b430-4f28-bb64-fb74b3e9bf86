package com.yuanqiao.insight.monitoring.modules.subscriber.sendhandler.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.system.config.ISendHandler;
import com.yuanqiao.insight.system.config.emus.NoticeType;
import org.apache.commons.lang.StringUtils;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 企业微信机器人
 *
 * <AUTHOR>
 * @date 2023/3/13
 */
@Component
public class WxWorkRobotSendHandler implements ISendHandler {
    @Autowired
    private ISysUserService userService;

    @Override
    public boolean support(String support) {
        return StringUtils.equals(support, NoticeType.WX_WORK.getCode());
    }

    @Override
    public void send(JSONObject sendMsg, J<PERSON>NObject template, Boolean isPar) throws Exception {

        String webhook = sendMsg.getString("webhook");
        // 模板内容
        String content = template.getString("content");
        if (isPar) {
            JSONObject par = template.getJSONObject("par");
            content = generateWelcome(par, content);
        }
        //content = Jsoup.parse(content).text();
        //发送post请求
        HttpRequest post = HttpUtil.createPost(webhook);
        post.header("Content-Type", "application/json; charset=utf-8");
        JSONObject data = new JSONObject();
        data.put("content", content);
        JSONObject paramData = new JSONObject();
        paramData.put("msgtype", "markdown");
        paramData.put("markdown", data);
        post.body(paramData.toString());
        post.execute();
    }
}
