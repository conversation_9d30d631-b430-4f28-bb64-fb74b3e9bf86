<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.monitoring.modules.serviceProvider.mapper.ServiceProviderMapper">
    <select id="getServiceProviderByDepIds"
            resultType="com.yuanqiao.insight.monitoring.modules.serviceProvider.entity.ServiceProvider">
        SELECT DISTINCT
        provider.id AS id,
        provider.priority AS priority
        FROM
        momg_service_provider provider
        JOIN
        momg_service_provider_depart providerDep ON provider.id = providerDep.provider_id
        WHERE
        providerDep.depart_id IN (
        <foreach item="key" collection="departIds" separator=",">
            #{key}
        </foreach>
        )
        ORDER BY
        provider.priority ASC
    </select>

    <select id="getCountByServiceProvider"
            resultType="java.lang.Integer">
        SELECT DISTINCT
        count(task.PROC_INST_ID_) count
        FROM
        (
        SELECT PROC_INST_ID_, MAX(END_TIME_) AS max_end_time
        FROM act_hi_taskinst
        WHERE TASK_DEF_KEY_ = #{activityId}
        AND END_TIME_ IS NOT NULL
        AND DELETE_REASON_ IS NULL
        GROUP BY PROC_INST_ID_
        ) max_task
        INNER JOIN act_hi_taskinst task ON task.PROC_INST_ID_ = max_task.PROC_INST_ID_ AND task.END_TIME_ =
        max_task.max_end_time
        INNER JOIN act_hi_procinst ins ON ins.ID_ = task.PROC_INST_ID_
        WHERE task.ASSIGNEE_ in
        <foreach collection="userList" item="user" open="(" separator="," close=")">
            #{user}
        </foreach>
        <if test="isCompleted!=null and isCompleted!='' and isCompleted==true">
            AND ins.END_TIME_ IS NOT NULL
        </if>
        <if test="isOverdue!=null and isOverdue!='' and isOverdue==true">
            and task.END_TIME_ is not null and task.DUE_DATE_ is null
        </if>
    </select>

</mapper>
