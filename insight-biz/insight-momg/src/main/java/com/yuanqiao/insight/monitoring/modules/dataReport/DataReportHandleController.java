package com.yuanqiao.insight.monitoring.modules.dataReport;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yuanqiao.insight.acore.dataReport.entity.MomgDataReportManage;
import com.yuanqiao.insight.acore.dataReport.service.IMomgDataReportManageService;
import com.yuanqiao.insight.acore.dataReport.util.DataReportUtils;
import com.yuanqiao.insight.acore.depart.entity.SysDepart;
import com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice;
import com.yuanqiao.insight.monitoring.modules.terminal.service.ITerminalDeviceService;
import com.yuanqiao.insight.service.device.entity.DeviceInfo;
import com.yuanqiao.insight.service.device.service.IDeviceInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.system.service.ISysDepartService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("/data/reportAndConverge")
@Slf4j
public class DataReportHandleController extends JeecgController<MomgDataReportManage, IMomgDataReportManageService> {

    @Autowired
    DataReportHandler dataReportHandler;

    @Autowired
    ISysDepartService sysDepartService;

    @Autowired
    IDeviceInfoService deviceInfoService;

    @Autowired
    ITerminalDeviceService terminalDeviceService;

    @Autowired
    DataReportUtils dataReportUtils;

    @Value("${platform.uniqueCode}")
    private String platformCode;

    /**
     * 上报数据处理
     *
     * @return
     */
    @PostMapping("/dataHandle")
    public Result<?> dataHandle(@RequestBody JSONObject dataObject) {
        dataObject = JSONObject.parseObject(dataObject.toJSONString());
        return dataReportHandler.execute(dataObject);
    }

    /**
     * 手动触发全量上报
     *
     * @return
     */
    @GetMapping("/fullReport")
    public Result<?> fullReport(String dataType) {
        Result result = null;
        if (StringUtils.isEmpty(dataType)) {
            return Result.error("请选择需要全量上报的数据类型！");
        }

        switch (dataType) {
            case "depart":
                List<SysDepart> departList = sysDepartService.list(new QueryWrapper<SysDepart>().ne("parent_id", ""));
                if (CollUtil.isNotEmpty(departList)) {
                    departList.forEach(item -> {
                        item.setParentCode(sysDepartService.getById(item.getParentId()).getDepartNameEn());
                    });

                    JSONObject dataObject = new JSONObject();
                    dataObject.put("dataType", "depart");
                    dataObject.put("operateType", "I");
                    dataObject.put("data", JSONArray.parseArray(JSONArray.toJSONString(departList)));
                    dataReportUtils.pushData(dataObject.toJSONString());

                    result = Result.OK("单位全量上报执行完毕！");
                } else {
                    result = Result.error("单位数据为空！");
                }
                break;
            case "device":
                List<DeviceInfo> deviceInfoList = deviceInfoService.list();
                if (CollUtil.isNotEmpty(deviceInfoList)) {
                    deviceInfoList.forEach(item -> {
                        if (StringUtils.isNotEmpty(item.getMomgDeptId())) {
                            item.setMomgDeptCode(sysDepartService.getById(item.getMomgDeptId()).getDepartNameEn());
                        }
                        item.setMomgDeptId("");
                    });
                    JSONObject dataObject = new JSONObject();
                    dataObject.put("dataType", "device");
                    JSONObject deviceObject = new JSONObject();
                    deviceObject.put("operateType", "I");
                    deviceObject.put("data", JSONArray.parseArray(JSONArray.toJSONString(deviceInfoList)));
                    dataObject.put("deviceData", deviceObject);
                    dataReportUtils.pushData(dataObject.toJSONString());

                    result = Result.OK("设备全量上报执行完毕！");
                } else {
                    result = Result.error("设备数据为空！");
                }
                break;
            case "terminal":
                List<TerminalDevice> terminalDeviceList = terminalDeviceService.list();
                if (CollUtil.isNotEmpty(terminalDeviceList)) {
                    terminalDeviceList.forEach(item -> {
                        if (StringUtils.isNotEmpty(item.getDeptId())) {
                            item.setDeptCode(sysDepartService.getById(item.getDeptId()).getDepartNameEn());
                        }
                        item.setDeptId("");
                    });
                    JSONObject dataObject = new JSONObject();
                    dataObject.put("dataType", "terminal");
                    JSONObject terminalObject = new JSONObject();
                    terminalObject.put("operateType", "I");
                    terminalObject.put("data", JSONArray.parseArray(JSONArray.toJSONString(terminalDeviceList)));
                    dataObject.put("terminalData", terminalObject);
                    dataReportUtils.pushData(dataObject.toJSONString());

                    result = Result.OK("终端全量上报执行完毕！");
                } else {
                    result = Result.error("终端数据为空！");
                }
                break;
            default:
                result = Result.error("未知的数据类型：" + dataType + " ！");
        }

        return result;
    }


    /**
     * 上报数据处理
     *
     * @return
     */
    @GetMapping("/getPlatformCode")
    public Result<?> getPlatformCode() {
        return Result.OK(platformCode);
    }

}
