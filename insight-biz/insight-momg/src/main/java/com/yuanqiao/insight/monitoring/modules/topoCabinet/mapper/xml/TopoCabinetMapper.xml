<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.monitoring.modules.topoCabinet.mapper.TopoCabinetMapper">

    <update id="updateCarbinetToDevice">

        update momg_topo_cabinet2device set device_id = #{devId},layer = #{layer},u = #{u} where id = #{carbinetId}

    </update>

    <select id="selectAddableDevice" resultType="com.yuanqiao.insight.service.device.entity.DeviceInfo">

        select * from momg_device_info
        <where>
            <if test="q != null and q != ''">
                and name like CONCAT('%',#{q},'%')
            </if>
            and id not in (select device_id from momg_topo_cabinet2device
                where cabinet_id in(
                  select id from momg_topo_cabinet where view_flag = #{viewFlag}
                )
            )
            and delflag = 0
        </where>


    </select>

    <select id="selectDevIdsByCarbinetId"
            resultType="com.yuanqiao.insight.monitoring.modules.topoCabinet.entity.CabinetToDeviceVo">

        select device_id as id, layer, layer_pool_str, u from momg_topo_cabinet2device where cabinet_id = #{id}

    </select>

    <select id="selectDeviceByDevId" resultType="com.yuanqiao.insight.service.device.entity.DeviceInfo">

        select * from momg_device_info where device_id = #{id}

    </select>

    <select id="selectDeviceByDevIdAndCarbinetId" resultType="java.lang.Integer">

        select layer from momg_topo_cabinet2device where cabinet_id = #{id} and device_id = #{obj}

    </select>

    <select id="selectCabinetById" resultType="com.yuanqiao.insight.monitoring.modules.topoCabinet.entity.TopoCabinet">

        select c.*, r.name as roomName
        from momg_topo_cabinet c
                 left join
             momg_topo_room r
             on
                 c.room_id = r.id
        where c.id = #{id}

    </select>
</mapper>
