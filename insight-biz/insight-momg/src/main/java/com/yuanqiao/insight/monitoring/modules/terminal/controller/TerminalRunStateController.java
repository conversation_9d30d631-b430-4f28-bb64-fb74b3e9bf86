package com.yuanqiao.insight.monitoring.modules.terminal.controller;

import cn.hutool.core.collection.CollUtil;
import co.elastic.clients.elasticsearch.core.search.Hit;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yuanqiao.insight.acore.system.service.impl.SysDictServiceImpl;
import com.yuanqiao.insight.common.util.common.DisplayDataUtil;
import com.yuanqiao.insight.common.util.common.UnitConvertUtil;
import com.yuanqiao.insight.monitoring.modules.alarm.service.IAlarmTemplateService;
import com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalApplication;
import com.yuanqiao.insight.monitoring.modules.terminal.entity.TerminalDevice;
import com.yuanqiao.insight.monitoring.modules.terminal.mapper.TerminalDeviceMapper;
import com.yuanqiao.insight.monitoring.modules.terminal.service.ITerminalApplicationService;
import com.yuanqiao.insight.monitoring.modules.terminal.utils.ESUtils;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import com.yuanqiao.insight.service.product.mapper.ProductMapper;
import com.yuanqiao.insight.service.product.mapper.ProertyMetadataMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.DictModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/terminal/runningState")
@Slf4j
public class TerminalRunStateController {

    @Autowired
    private ESUtils esUtils;
    @Autowired
    private IAlarmTemplateService alarmTemplateService;
    @Autowired
    private TerminalDeviceMapper terminalDeviceMapper;
    @Autowired
    private ProductMapper productMapper;
    @Autowired
    private ProertyMetadataMapper proertyMetadataMapper;
    @Autowired
    private SysDictServiceImpl sysDictService;

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    @Autowired
    private ITerminalApplicationService terminalApplicationService;


    /**
     * 获取终端概况
     *
     * @return
     */
    @GetMapping("/getStateInfo")
    public Result<?> getStateInfo(String uniqueCode) throws IOException {
        JSONObject resultObj = new JSONObject();
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -1);
        String yesterday = new SimpleDateFormat("yyyy-MM-dd ").format(cal.getTime());
        // 终端基本信息
        TerminalDevice terminalDevice = terminalDeviceMapper.selectStateInfo(uniqueCode);
        if (terminalDevice == null) {
            return Result.error("未查询到对应终端！");
        }
        List<DictModel> osTypeDictList = sysDictService.queryDictItemsByCode("os_type");
        Map<String, String> osTypeDictMap = new HashMap<>();
        if (CollUtil.isNotEmpty(osTypeDictList)) {
            osTypeDictMap = osTypeDictList.stream().collect(Collectors.toMap(DictModel::getValue, DictModel::getText));
        }
        List<DictModel> cpuTypeDictList = sysDictService.queryDictItemsByCode("cpuType");
        Map<String, String> cpuTypeDictMap = new HashMap<>();
        if (CollUtil.isNotEmpty(cpuTypeDictList)) {
            cpuTypeDictMap = cpuTypeDictList.stream().collect(Collectors.toMap(DictModel::getValue, DictModel::getText));
        }
        terminalDevice.setOsType(osTypeDictMap.get(terminalDevice.getOsType()));
        terminalDevice.setCpuType(cpuTypeDictMap.get(terminalDevice.getCpuType()));

        // 统计信息
        TerminalDevice terminalStatis = terminalDeviceMapper.selectStatisInfo(uniqueCode, yesterday);
        if (terminalStatis != null) {
            terminalDevice.setOnCount(terminalStatis.getOnCount());
            terminalDevice.setOnTotal(terminalStatis.getOnTotal());
            terminalDevice.setOnTotal_text(terminalStatis.getOnTotal_text());
        }

        ArrayList<String> metadataCodeList = new ArrayList<>();
        metadataCodeList.add("dynamic");
        metadataCodeList.add("cpu");
        metadataCodeList.add("cpuRate");
        metadataCodeList.add("mem");
        metadataCodeList.add("memUtilizRate");
        metadataCodeList.add("filepartirion");
        metadataCodeList.add("diskRate");
        metadataCodeList.add("diskTotal");
        metadataCodeList.add("diskFree");
        metadataCodeList.add("application");
        List<ProertyMetadata> metadataList = proertyMetadataMapper.findMetadataByDevCode(uniqueCode, metadataCodeList);
        Map<String, ProertyMetadata> metadataMap = metadataList.stream().collect(Collectors.toMap(ProertyMetadata::getCode, ProertyMetadata -> ProertyMetadata));
        List<JSONObject> dataList = new ArrayList<>();

        /**
         * 过滤数据数据查询出的物模型，展示方式不符合设定则从InfluxDB中获取源数据，符合设定则从Redis中获取源数据；
         * 如果所有或部分原本应该从Redis中读取源数据的指标读取为空，则将上述指标调整为从InfluxDB中读取源数据；
         */

        // 需要从ES中读取的，如果从redis中取不到，添加到这里来。
        Map<String, ProertyMetadata> searchInEsMetadataMap = new HashMap<>(metadataMap);

        // 展示方式正确的指标 -- 读取Redis
        List<ProertyMetadata> redisMetadataList = metadataList.stream().filter(m -> {
                    switch (m.getCode()) {
                        case "cpuRate":
                            return m.getChart().equals("line");
                        case "diskRate":
                            return m.getChart().equals("line");
                        case "memUtilizRate":
                            return false; // 兼容小程序和运维助手的趋势图和单值
                        case "cpu":
                            return m.getChart().equals("table");
                        case "filepartirion":
                            return m.getChart().equals("table");
                        case "mem":
                            return m.getChart().equals("table");
                        case "application":
                            return m.getChart().equals("table");
                        case "dynamic":
                            return true;
                        case "diskTotal":
                            return true;
                        case "diskFree":
                            return true;
                        default:
                            return false;
                    }
                }
        ).collect(Collectors.toList());


        if (CollUtil.isNotEmpty(redisMetadataList)) {
            List<JSONObject> dataListByRedis = alarmTemplateService.getDataInfoByDevCode(uniqueCode, redisMetadataList.stream().map(ProertyMetadata::getCode).collect(Collectors.toList())).getDataList();
            dataList.addAll(dataListByRedis);
            List<String> redisNoNullMetadataList = dataListByRedis.stream().map(r -> r.getString("code")).collect(Collectors.toList());
            searchInEsMetadataMap = metadataMap.entrySet().stream().filter(e -> !redisNoNullMetadataList.contains(e.getKey())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        }
        dataList.addAll(buildESData(searchInEsMetadataMap, uniqueCode));


        List<JSONObject> tempDataList = new ArrayList<>();
        for (JSONObject obj : dataList) {
            if (obj.getString("code").equals("dynamic")) {
                buildTerminalLastOnTime(obj, terminalDevice);
            }
            if (obj.getString("code").equals("cpu")) {
                tempDataList = rebuildCpu(obj, metadataMap.get("cpu"));
            }
        }
        dataList = dataList.stream().filter(t -> !StringUtils.equals(t.getString("code"), "dynamic") && !StringUtils.equals(t.getString("code"), "cpu")).collect(Collectors.toList());
        dataList.addAll(tempDataList);
        dataList.sort(Comparator.comparingInt((JSONObject s) -> s.getInteger("serial")).thenComparingLong(s -> s.getLong("createTime")));
        dataList = queryApplication(dataList, uniqueCode);
        resultObj.put("dataList", dataList);
        resultObj.put("terminalInfo", terminalDevice);
        return Result.OK(resultObj);
    }


    private List<JSONObject> queryApplication(List<JSONObject> dataList, String uniqueCode) {
        List<TerminalApplication> terminalApplicationList = terminalApplicationService.list(new QueryWrapper<TerminalApplication>().eq("strategy_status", 0));
        if (CollectionUtils.isEmpty(terminalApplicationList)) {
            return dataList;
        } else {
            for (TerminalApplication application : terminalApplicationList) {
                if (StringUtils.isNotEmpty(application.getUniqueCode())) {
                    List<String> nameList = Arrays.asList(application.getUniqueCode().split(","));
                    if (nameList.contains(uniqueCode)) {
                        if (StringUtils.isNotEmpty(application.getApplicationName())) {
                            List<String> applicationList = Arrays.asList(application.getApplicationName().split(","));
                            //过滤应用
                            if (application.getStatus() == 1) {
                                for (JSONObject obj : dataList) {
                                    if (obj.getString("code").equals("application")) {
                                        JSONArray array = obj.getJSONArray("value");
                                        JSONArray innerArray = array.getJSONObject(0).getJSONArray("value");
                                        for (int j = 0; j < innerArray.size(); j++) {
                                            JSONArray jsonArray = innerArray.getJSONArray(j);
                                            for (int i = 0; i < jsonArray.size(); i++) {
                                                JSONObject softwareInfo = jsonArray.getJSONObject(i);
                                                if (softwareInfo.getString("name").equals("软件名称")) {
                                                    String softwareName = softwareInfo.getString("value");
                                                    if (applicationList.contains(softwareName.trim())) {
                                                        innerArray.remove(j);
                                                        j--;
                                                    }
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                }
                                //展示应用
                            } else if (application.getStatus() == 0) {
                                for (JSONObject obj : dataList) {
                                    if (obj.getString("code").equals("application")) {
                                        JSONArray array = obj.getJSONArray("value");
                                        JSONArray innerArray = array.getJSONObject(0).getJSONArray("value");
                                        for (int j = 0; j < innerArray.size(); j++) {
                                            JSONArray jsonArray = innerArray.getJSONArray(j);
                                            for (int i = 0; i < jsonArray.size(); i++) {
                                                JSONObject softwareInfo = jsonArray.getJSONObject(i);
                                                if (softwareInfo.getString("name").equals("软件名称")) {
                                                    String softwareName = softwareInfo.getString("value");
                                                    if (!applicationList.contains(softwareName.trim())) {
                                                        innerArray.remove(j);
                                                        j--;
                                                    }
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return dataList;

    }

    // ES查询数据组装
    private List<JSONObject> buildESData(Map<String, ProertyMetadata> metadataMap, String uniqueCode) {
        List<JSONObject> dataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(metadataMap)) {

            String categoryCode = productMapper.getCategoryCodeByDeviceCode(uniqueCode).toLowerCase();
            String index = ("metrics-" + categoryCode + "-" + "udp").toLowerCase();

            for (Map.Entry<String, ProertyMetadata> entry : metadataMap.entrySet()) {
                ProertyMetadata metadata = entry.getValue();
                JSONObject eleObject = new JSONObject();
                eleObject.put("name", metadata.getName());
                eleObject.put("code", metadata.getCode());
                eleObject.put("type", metadata.getDataType());
                eleObject.put("unit", metadata.getUnit());
                eleObject.put("originStep", metadata.getOriginStep());
                eleObject.put("serial", metadata.getSerial());
                eleObject.put("createTime", metadata.getCreateTime().getTime());

                switch (metadata.getCode()) {
                    case "memUtilizRate":// 基本类型指标
                        Double v = 0.0;
                        JSONArray valueArray = new JSONArray();
                        JSONArray trendValueArray = new JSONArray();
                        List<Hit<Object>> memRateHits = esUtils.getHitDataForTerminal(index, "memUtilizRate", null, uniqueCode, 15);
                        if (CollUtil.isNotEmpty(memRateHits)) {
                            LinkedHashMap lastSource = (LinkedHashMap) memRateHits.get(0).source();
                            v = (double) lastSource.get("memUtilizRate");
                            JSONObject valueObj = new JSONObject();
                            valueObj.put("value", v);
                            valueArray.add(valueObj);

                            for (Hit<Object> hit : memRateHits) {
                                LinkedHashMap source = (LinkedHashMap) hit.source();
                                double memRate = (Double) source.get("memUtilizRate");
                                JSONObject json = new JSONObject();
                                json.put("memUtilizRate", memRate);
                                json.put("createTime", (String) source.get("@timestamp"));
                                trendValueArray.add(json);
                            }
                        }
                        eleObject.put("value", valueArray);
                        eleObject.put("trendValue", trendValueArray);

                        dataList.add(eleObject);
                        break;

                    case "diskFree":// 基本类型指标
                        Double v0 = 0.0;
                        List<Hit<Object>> diskFreeHits = esUtils.getHitDataForTerminal(index, "diskFree", null, uniqueCode, 1);
                        if (CollUtil.isNotEmpty(diskFreeHits)) {
                            LinkedHashMap source = (LinkedHashMap) diskFreeHits.get(0).source();
                            v0 = (double) source.get("diskFree");
                        }
                        JSONArray valueArray0 = new JSONArray();
                        JSONObject valueObj0 = new JSONObject();
                        Map<String, Object> diskFreeUnitMap = UnitConvertUtil.unitDynamic(eleObject.getString("unit"), v0, eleObject.getDouble("originStep"));
                        valueObj0.put("value", diskFreeUnitMap.get("value"));
                        valueArray0.add(valueObj0);
                        eleObject.put("unit", diskFreeUnitMap.get("unit"));
                        eleObject.put("value", valueArray0);

                        dataList.add(eleObject);
                        break;

                    case "diskTotal":// 基本类型指标
                        Double v1 = 0.0;
                        List<Hit<Object>> diskTotalHits = esUtils.getHitDataForTerminal(index, "diskTotal", null, uniqueCode, 1);
                        if (CollUtil.isNotEmpty(diskTotalHits)) {
                            LinkedHashMap source = (LinkedHashMap) diskTotalHits.get(0).source();
                            v1 = (double) source.get("diskTotal");
                        }
                        JSONArray valueArray1 = new JSONArray();
                        JSONObject valueObj1 = new JSONObject();
                        Map<String, Object> diskTotalUnitMap = UnitConvertUtil.unitDynamic(eleObject.getString("unit"), v1, eleObject.getDouble("originStep"));
                        valueObj1.put("value", diskTotalUnitMap.get("value"));
                        valueArray1.add(valueObj1);
                        eleObject.put("unit", diskTotalUnitMap.get("unit"));
                        eleObject.put("value", valueArray1);

                        dataList.add(eleObject);
                        break;

                    case "cpuRate":// 基本类型指标
                        List<Hit<Object>> cpuRateHits = esUtils.getHitDataForTerminal(index, "cpuRate", "cpuCores", uniqueCode, 15);
                        JSONObject cpuRateResultObj = new JSONObject();
                        if (CollUtil.isNotEmpty(cpuRateHits)) {
                            JSONArray valueArray2 = new JSONArray();
                            for (Hit<Object> hit : cpuRateHits) {
                                LinkedHashMap source = (LinkedHashMap) hit.source();
                                double cpuRate = (Double) source.get("cpuRate");
                                JSONObject json = new JSONObject();
                                json.put("cpuRate", cpuRate);
                                json.put("createTime", (String) source.get("@timestamp"));
                                valueArray2.add(json);
                            }
                            cpuRateResultObj.put("value", valueArray2);
                        }

                        if (!cpuRateResultObj.isEmpty()) {
                            getForCpuRateOrDiskRate(metadata, cpuRateResultObj, eleObject);
                            dataList.add(eleObject);
                        }
                        break;

                    case "diskRate":// 基本类型指标
                        List<Hit<Object>> diskRateHits = esUtils.getHitDataForTerminal(index, "diskRate", null, uniqueCode, 15);
                        JSONObject diskRateResultObj = new JSONObject();
                        if (CollUtil.isNotEmpty(diskRateHits)) {
                            JSONArray valueArray3 = new JSONArray();
                            for (Hit<Object> hit : diskRateHits) {
                                LinkedHashMap source = (LinkedHashMap) hit.source();
                                double diskRate = (Double) source.get("diskRate");
                                JSONObject json = new JSONObject();
                                json.put("diskRate", diskRate);
                                json.put("createTime", (String) source.get("@timestamp"));
                                valueArray3.add(json);
                            }
                            diskRateResultObj.put("value", valueArray3);
                        }

                        if (!diskRateResultObj.isEmpty()) {
                            getForCpuRateOrDiskRate(metadata, diskRateResultObj, eleObject);
                            dataList.add(eleObject);
                        }
                        break;

                    case "mem"://object类型
                        JSONObject memResultObj = new JSONObject();
                        List<Hit<Object>> memHits = esUtils.getHitDataForTerminal(index, "mem", null, uniqueCode, 1);
                        if (CollUtil.isNotEmpty(memHits)) {
                            JSONArray valueArray4 = new JSONArray();
                            for (Hit<Object> hit : memHits) {
                                LinkedHashMap source1 = (LinkedHashMap) hit.source();
                                //展平
                                Map<String, Object> result = new HashMap<>();
                                esUtils.flap(source1, result);
                                JSONObject jsonObject = new JSONObject(result);
                                valueArray4.add(jsonObject);
                            }
                            memResultObj.put("value", valueArray4);
                        }

                        if (!memResultObj.isEmpty()) {
                            List<JSONObject> mem = getFromArrayOrObject(metadata, memResultObj, eleObject, dataList);
                            dataList = dataList.stream().filter(t -> !t.containsKey(metadata.getCode())).collect(Collectors.toList());
                            dataList.addAll(mem);
                        }
                        break;

                    case "dynamic"://object类型
                        String runTime = esUtils.getRunTime(Arrays.asList(index), "dynamic.sysUptime", uniqueCode);
                        JSONObject json = new JSONObject();
                        json.put("code", "sysUptime");
                        json.put("value", runTime);
                        JSONArray eleArray = new JSONArray();
                        eleArray.add(json);
                        JSONArray eleArray2 = new JSONArray();
                        eleArray2.add(eleArray);
                        JSONObject json1 = new JSONObject();
                        json1.put("value", eleArray2);
                        JSONArray eleArray1 = new JSONArray();
                        eleArray1.add(json1);

                        eleObject.put("value", eleArray1);
                        dataList.add(eleObject);
                        break;

                    case "cpu"://array类型
                        JSONObject cpuResultObj = new JSONObject();
                        List<Hit<Object>> cpuHits = esUtils.getArrayLastHitsForTerminal(index, "cpu", uniqueCode, 100);
                        if (CollUtil.isNotEmpty(cpuHits)) {
                            JSONArray valueArray5 = new JSONArray();
                            for (Hit<Object> hit : cpuHits) {
                                LinkedHashMap source1 = (LinkedHashMap) hit.source();
                                //展平
                                Map<String, Object> result = new HashMap<>();
                                esUtils.flap(source1, result);
                                JSONObject jsonObject = new JSONObject(result);
                                valueArray5.add(jsonObject);
                            }
                            cpuResultObj.put("value", valueArray5);
                        }

                        if (!cpuResultObj.isEmpty()) {
                            List<JSONObject> cpu = getFromArrayOrObject(metadata, cpuResultObj, eleObject, dataList);
                            dataList = dataList.stream().filter(t -> !t.containsKey(metadata.getCode())).collect(Collectors.toList());
                            dataList.addAll(cpu);
                        }
                        break;
                    case "application"://array类型
                        JSONObject applicationResultObj = new JSONObject();
                        List<Hit<Object>> applicationHits = esUtils.getArrayLastHitsForTerminal(index, "application", uniqueCode, 100);
                        JSONArray valueArray16 = new JSONArray();
                        for (Hit<Object> hit : applicationHits) {
                            LinkedHashMap source1 = (LinkedHashMap) hit.source();
                            //展平
                            Map<String, Object> result = new HashMap<>();
                            esUtils.flap(source1, result);
                            JSONObject jsonObject = new JSONObject(result);
                            valueArray16.add(jsonObject);
                        }
                        applicationResultObj.put("value", valueArray16);

                        List<JSONObject> applicationPartirion = getFromArrayOrObject(metadata, applicationResultObj, eleObject, dataList);
                        dataList = dataList.stream().filter(t -> !t.containsKey(metadata.getCode())).collect(Collectors.toList());
                        dataList.addAll(applicationPartirion);
                        break;
                    case "filepartirion"://array类型
                        JSONObject fileResultObj = new JSONObject();
                        List<Hit<Object>> fileHits = esUtils.getArrayLastHitsForTerminal(index, "filepartirion", uniqueCode, 100);
                        JSONArray valueArray6 = new JSONArray();
                        for (Hit<Object> hit : fileHits) {
                            LinkedHashMap source1 = (LinkedHashMap) hit.source();
                            //展平
                            Map<String, Object> result = new HashMap<>();
                            esUtils.flap(source1, result);
                            JSONObject jsonObject = new JSONObject(result);
                            valueArray6.add(jsonObject);
                        }
                        fileResultObj.put("value", valueArray6);

                        List<JSONObject> filepartirion = getFromArrayOrObject(metadata, fileResultObj, eleObject, dataList);
                        dataList = dataList.stream().filter(t -> !t.containsKey(metadata.getCode())).collect(Collectors.toList());
                        dataList.addAll(filepartirion);
                        break;
                }
            }
        }
        return dataList;
    }

    // 终端动态信息中的运行时间，上次开机时间
    public void buildTerminalLastOnTime(JSONObject obj, TerminalDevice terminalDevice) {
        try {
            JSONArray array = obj.getJSONArray("value");
            JSONArray dataArray = array.getJSONObject(0).getJSONArray("value");
            JSONArray eleArray = dataArray.getJSONArray(0);
            for (int j = 0; j < eleArray.size(); j++) {
                JSONObject eleObj = eleArray.getJSONObject(j);
                if (eleObj.getString("code").equals("sysUptime")) {
                    String sysUpTime = eleObj.getString("value");
                    terminalDevice.setRunTime(sysUpTime);
                    String[] split = sysUpTime.split(" ");
                    Long[] duration = new Long[]{0L, 0L, 0L, 0L};
                    for (String s : split) {
                        Pattern p = Pattern.compile("([-+])?\\d+(\\.\\d+)?");
                        Matcher matcher = p.matcher(s);
                        if (s.contains("天")) {
                            duration[0] = matcher.find() ? Long.parseLong(matcher.group(0)) : 0;
                        } else if (s.contains("小时")) {
                            duration[1] = matcher.find() ? Long.parseLong(matcher.group(0)) : 0;
                        } else if (s.contains("分")) {
                            duration[2] = matcher.find() ? Long.parseLong(matcher.group(0)) : 0;
                        } else if (s.contains("秒")) {
                            duration[3] = matcher.find() ? Long.parseLong(matcher.group(0)) : 0;
                        }
                    }
                    long startMillis = TimeUnit.DAYS.toMillis(duration[0]) + TimeUnit.HOURS.toMillis(duration[1]) + TimeUnit.MINUTES.toMillis(duration[2]) + TimeUnit.SECONDS.toMillis(duration[3]);
                    Date lastOnDate = new Date(new Date().getTime() - startMillis);
                    terminalDevice.setLastOn(sdf.format(lastOnDate));
                }
            }
        } catch (Exception e) {
            log.error("解析终端运行时长和上次开机时间异常！", e);
        }
    }

    // 根据CPU信息重组数据
    public List<JSONObject> rebuildCpu(JSONObject obj, ProertyMetadata metadata) {
        String cpuName = "";
        String cpuFreq = "";
        int cpuCores = 0;
        List<JSONObject> tempDataList = new ArrayList<>();
        try {
            JSONArray array = obj.getJSONArray("value");
            JSONArray dataArray = array.getJSONObject(0).getJSONArray("value");
            cpuCores = dataArray.size();
            JSONArray eleArray = dataArray.getJSONArray(0);
            for (int i = 0; i < eleArray.size(); i++) {
                if (eleArray.getJSONObject(i).getString("code").equals("cpuName")) {
                    cpuName = eleArray.getJSONObject(i).getString("value");
                }
                if (eleArray.getJSONObject(i).getString("code").equals("cpuFreq")) {
                    cpuFreq = eleArray.getJSONObject(i).getString("value");
                }
            }
            JSONObject valueMap = new JSONObject();
            valueMap.put("cpuName", cpuName);
            valueMap.put("cpuFreq", cpuFreq);
            valueMap.put("cpuCores", cpuCores);

            JSONObject outObject = new JSONObject();
            JSONArray valueArray = new JSONArray();
            JSONObject eleObject = new JSONObject();
            outObject.put("code", "cpu");
            outObject.put("name", metadata.getName());
            outObject.put("type", metadata.getDataType());
            outObject.put("serial", metadata.getSerial() == null ? 1 : metadata.getSerial());
            outObject.put("createTime", metadata.getCreateTime().getTime());
            for (ProertyMetadata childProperty : metadata.getProertyMetadataList()) {  //物模型子模型
                valueMap.forEach((k, v) -> {
                    if (childProperty.getCode().equals(k)) {
                        JSONObject jsonObject = new JSONObject();
                        String code = childProperty.getCode();
                        String name = childProperty.getName();
                        String type = StringUtils.isNotEmpty(childProperty.getDataType()) ? childProperty.getDataType() : metadata.getDataType();
                        jsonObject.put("name", name);
                        jsonObject.put("code", code);
                        jsonObject.put("type", type);
                        jsonObject.put("unit", childProperty.getUnit());
                        jsonObject.put("value", v);
                        jsonObject.put("originStep", childProperty.getOriginStep());
                        jsonObject.put("serial", childProperty.getSerial());
                        jsonObject.put("createTime", childProperty.getCreateTime());
                        eleObject.put(code, jsonObject);
                    }
                });
            }
            valueArray.add(eleObject);

            outObject.put("value", valueArray);
            outObject.put("display", getArrayList(valueArray));

            DisplayDataUtil.getDisplay(outObject, "cpu", tempDataList);
        } catch (Exception e) {
            log.error("CPU详情监控数据格式重组异常！", e);
        }

        return tempDataList;
    }

    // 推模式Array、Object类型 - display数据组装
    public JSONArray getArrayList(JSONArray list) {
        JSONArray jsonArrayOut = new JSONArray();
        JSONObject disObject = new JSONObject();
        disObject.put("timestamp", sdf.format(System.currentTimeMillis()));
        disObject.put("type", "table");
        JSONArray jsonArray = new JSONArray();
        for (int i = 0; i < list.size(); i++) {
            Map<String, Object> o = (Map<String, Object>) list.get(i);
            Collection<Object> values = o.values();
            String s = JSONArray.toJSONString(values);  //复制一份出来
            JSONArray array = JSONArray.parseArray(s);
            jsonArray.add(array);
        }
        disObject.put("value", jsonArray);
        jsonArrayOut.add(disObject);
        return jsonArrayOut;
    }

    void getForCpuRateOrDiskRate(ProertyMetadata metadata, JSONObject dataObject, JSONObject eleObject) {
        try {
            JSONArray originValueArray = dataObject.getJSONArray("value");
            JSONArray targetValueArray = new JSONArray();
            for (int i = 0; i < originValueArray.size(); i++) {
                JSONObject eleOriginValueObj = originValueArray.getJSONObject(i);
                eleOriginValueObj.put("type", "line");
                eleOriginValueObj.put("value", Double.parseDouble(String.format("%.2f", eleOriginValueObj.getDouble(metadata.getCode()))));
                String createTime = eleOriginValueObj.getString("createTime");
                if (StringUtils.isNotEmpty(createTime)) {
                    eleOriginValueObj.put("timestamp", sdf.parse(createTime));
                }
                eleOriginValueObj.remove(metadata.getCode());
                targetValueArray.add(eleOriginValueObj);
            }
            targetValueArray.sort((v1, v2) -> {
                JSONObject a = (JSONObject) v1;
                JSONObject b = (JSONObject) v2;
                int n = a.getString("timestamp").compareTo(b.getString("timestamp"));
                return n;
            });
            eleObject.put("value", targetValueArray);
//          dataList = dataList.stream().filter(t -> !t.containsKey(metadata.getCode())).collect(Collectors.toList());
//            dataList.add(eleObject);
        } catch (ParseException e) {
            log.error("时间戳转换异常！", e);
        }
    }

    List<JSONObject> getFromArrayOrObject(ProertyMetadata metadata, JSONObject dataObject, JSONObject eleObject, List<JSONObject> dataList) {
        // Array、Object类型指标  //mem就是一个object类型的，下面有子指标，cpu是array，dynamic是object，filepartirion是array //这要是每个都弄，也够我受的。。。。。。
        List<ProertyMetadata> childMetadataList = metadata.getProertyMetadataList();
        Map<String, ProertyMetadata> childMetadaMap = childMetadataList.stream().collect(Collectors.toMap(ProertyMetadata::getCode, ProertyMetadata -> ProertyMetadata));

        JSONArray originValueArray = dataObject.getJSONArray("value");
        JSONArray valueArray = new JSONArray();
        for (int i = 0; i < originValueArray.size(); i++) {
            JSONObject targetValueObject = new JSONObject();
            originValueArray.getJSONObject(i).forEach((k, v) -> {
                JSONObject eleInValueObject = new JSONObject();
                if (childMetadaMap.containsKey(k)) {
                    ProertyMetadata childMetadata = childMetadaMap.get(k);
                    eleInValueObject.put("name", childMetadata.getName());
                    eleInValueObject.put("code", childMetadata.getCode());
                    eleInValueObject.put("type", childMetadata.getDataType());
                    eleInValueObject.put("unit", childMetadata.getUnit());
                    eleInValueObject.put("originStep", childMetadata.getOriginStep());
                    eleInValueObject.put("serial", childMetadata.getSerial());
                    eleInValueObject.put("createTime", childMetadata.getCreateTime().getTime());
                    eleInValueObject.put("value", v);
                    targetValueObject.put(k, eleInValueObject);
                }
            });
            valueArray.add(targetValueObject);
        }
        eleObject.put("value", valueArray);
        eleObject.put("display", getArrayList(valueArray));
        List<JSONObject> tempDataList = new ArrayList<>();
        DisplayDataUtil.getDisplay(eleObject, metadata.getCode(), tempDataList);//这里涉及到单位转化
        return tempDataList;
    }

}
