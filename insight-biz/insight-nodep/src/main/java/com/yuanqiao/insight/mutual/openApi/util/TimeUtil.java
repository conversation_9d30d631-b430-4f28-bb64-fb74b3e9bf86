package com.yuanqiao.insight.mutual.openApi.util;

import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;

public class TimeUtil {

    /**
     * 日期增加一个月或减少一天
     * @param date 时间
     * @param mount 要增加的月份
     * @return
     * @throws ParseException
     */
    public Date subMonth(Date date, Integer mount) throws ParseException {
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//        Date dt = sdf.parse(date);
        Calendar rightNow = Calendar.getInstance();
        rightNow.setTime(date);
        rightNow.add(Calendar.MONTH, mount);
        Date dt1 = rightNow.getTime();
//        String reStr = sdf.format(dt1);
        return dt1;
    }
}
