<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>insight-biz</artifactId>
        <groupId>com.yuanqiao.insight</groupId>
        <version>3.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>insight-nodep</artifactId>
    <version>3.0.0</version>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.jeecgframework.boot</groupId>
            <artifactId>jeecg-boot-module-system</artifactId>
        </dependency>



        <dependency>
            <groupId>com.yuanqiao.insight</groupId>
            <artifactId>insight-service</artifactId>
            <version>3.0.0</version>
        </dependency>
        <!--<dependency>
            <groupId>com.yuanqiao.insight</groupId>
            <artifactId>insight-acore</artifactId>
            <version>3.0.0</version>
        </dependency>-->
        <dependency>
            <groupId>com.yuanqiao.insight</groupId>
            <artifactId>insight-momg</artifactId>
            <version>3.0.0</version>
        </dependency>
        <!-- 引入jedis jar包 -->
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>
        <!--<dependency>
            <groupId>com.yuanqiao.insight</groupId>
            <artifactId>insight-activiti</artifactId>
            <version>3.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.yuanqiao.insight</groupId>
            <artifactId>insight-itil</artifactId>
            <version>3.0.0</version>
        </dependency>-->
    </dependencies>

</project>
