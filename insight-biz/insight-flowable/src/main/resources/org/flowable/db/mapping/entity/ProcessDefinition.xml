<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.flowable.engine.impl.persistence.entity.ProcessDefinitionEntityImpl">

    <!-- PROCESSDEFINITION INSERT -->

    <insert id="insertProcessDefinition"
            parameterType="org.flowable.engine.impl.persistence.entity.ProcessDefinitionEntityImpl">
        insert into ${prefix}ACT_RE_PROCDEF(ID_, REV_, CATEGORY_, NAME_, KEY_, VERSION_, DEPLOYMENT_ID_, RESOURCE_NAME_,
                                            DGRM_RESOURCE_NAME_, DESCRIPTION_, HAS_START_FORM_KEY_,
                                            HAS_GRAPHICAL_NOTATION_, SUSPENSION_STATE_, DERIVED_FROM_,
                                            DERIVED_FROM_ROOT_, DERIVED_VERSION_, TENANT_ID_, ENGINE_VERSION_)
        values (#{id, jdbcType=VARCHAR},
                1,
                #{category, jdbcType=VARCHAR},
                #{name, jdbcType=VARCHAR},
                #{key, jdbcType=VARCHAR},
                #{version, jdbcType=INTEGER},
                #{deploymentId, jdbcType=VARCHAR},
                #{resourceName, jdbcType=VARCHAR},
                #{diagramResourceName, jdbcType=VARCHAR},
                #{description, jdbcType=VARCHAR},
                #{hasStartFormKey, jdbcType=BOOLEAN},
                #{isGraphicalNotationDefined, jdbcType=BOOLEAN},
                #{suspensionState, jdbcType=INTEGER},
                #{derivedFrom, jdbcType=VARCHAR},
                #{derivedFromRoot, jdbcType=VARCHAR},
                #{derivedVersion, jdbcType=INTEGER},
                #{tenantId, jdbcType=VARCHAR},
                #{engineVersion, jdbcType=VARCHAR})
    </insert>

    <insert id="bulkInsertProcessDefinition" parameterType="java.util.List">
        INSERT INTO ${prefix}ACT_RE_PROCDEF(ID_, REV_, CATEGORY_, NAME_, KEY_, VERSION_, DEPLOYMENT_ID_, RESOURCE_NAME_,
        DGRM_RESOURCE_NAME_, DESCRIPTION_, HAS_START_FORM_KEY_, HAS_GRAPHICAL_NOTATION_ , SUSPENSION_STATE_,
        DERIVED_FROM_, DERIVED_FROM_ROOT_, DERIVED_VERSION_, TENANT_ID_, ENGINE_VERSION_)
        VALUES
        <foreach collection="list" item="processDefinition" index="index" separator=",">
            (#{processDefinition.id, jdbcType=VARCHAR},
            1,
            #{processDefinition.category, jdbcType=VARCHAR},
            #{processDefinition.name, jdbcType=VARCHAR},
            #{processDefinition.key, jdbcType=VARCHAR},
            #{processDefinition.version, jdbcType=INTEGER},
            #{processDefinition.deploymentId, jdbcType=VARCHAR},
            #{processDefinition.resourceName, jdbcType=VARCHAR},
            #{processDefinition.diagramResourceName, jdbcType=VARCHAR},
            #{processDefinition.description, jdbcType=VARCHAR},
            #{processDefinition.hasStartFormKey, jdbcType=BOOLEAN},
            #{processDefinition.isGraphicalNotationDefined, jdbcType=BOOLEAN},
            #{processDefinition.suspensionState, jdbcType=INTEGER},
            #{processDefinition.derivedFrom, jdbcType=VARCHAR},
            #{processDefinition.derivedFromRoot, jdbcType=VARCHAR},
            #{processDefinition.derivedVersion, jdbcType=INTEGER},
            #{processDefinition.tenantId, jdbcType=VARCHAR},
            #{processDefinition.engineVersion, jdbcType=VARCHAR})
        </foreach>
    </insert>

    <insert id="bulkInsertProcessDefinition" databaseId="oracle" parameterType="java.util.List">
        INSERT ALL
        <foreach collection="list" item="processDefinition" index="index">
            INTO ${prefix}ACT_RE_PROCDEF(ID_, REV_, CATEGORY_, NAME_, KEY_, VERSION_, DEPLOYMENT_ID_, RESOURCE_NAME_,
            DGRM_RESOURCE_NAME_, DESCRIPTION_, HAS_START_FORM_KEY_, HAS_GRAPHICAL_NOTATION_ , SUSPENSION_STATE_,
            DERIVED_FROM_, DERIVED_FROM_ROOT_, DERIVED_VERSION_, TENANT_ID_, ENGINE_VERSION_) VALUES
            (#{processDefinition.id, jdbcType=VARCHAR},
            1,
            #{processDefinition.category, jdbcType=VARCHAR},
            #{processDefinition.name, jdbcType=VARCHAR},
            #{processDefinition.key, jdbcType=VARCHAR},
            #{processDefinition.version, jdbcType=INTEGER},
            #{processDefinition.deploymentId, jdbcType=VARCHAR},
            #{processDefinition.resourceName, jdbcType=VARCHAR},
            #{processDefinition.diagramResourceName, jdbcType=VARCHAR},
            #{processDefinition.description, jdbcType=VARCHAR},
            #{processDefinition.hasStartFormKey, jdbcType=BOOLEAN},
            #{processDefinition.isGraphicalNotationDefined, jdbcType=BOOLEAN},
            #{processDefinition.suspensionState, jdbcType=INTEGER},
            #{processDefinition.derivedFrom, jdbcType=VARCHAR},
            #{processDefinition.derivedFromRoot, jdbcType=VARCHAR},
            #{processDefinition.derivedVersion, jdbcType=INTEGER},
            #{processDefinition.tenantId, jdbcType=VARCHAR},
            #{processDefinition.engineVersion, jdbcType=VARCHAR})
        </foreach>
        SELECT * FROM dual
    </insert>

    <!-- PROCESSDEFINITION UPDATE -->

    <update id="updateProcessDefinition"
            parameterType="org.flowable.engine.impl.persistence.entity.ProcessDefinitionEntityImpl">
        update ${prefix}ACT_RE_PROCDEF
        <set>
            REV_ = #{revisionNext, jdbcType=INTEGER},
            <if test="originalPersistentState.suspensionState != suspensionState">
                SUSPENSION_STATE_ = #{suspensionState, jdbcType=INTEGER},
            </if>
            <if test="originalPersistentState.category != category">
                CATEGORY_ = #{category, jdbcType=VARCHAR}
            </if>
        </set>
        where ID_ = #{id, jdbcType=VARCHAR}
        and REV_ = #{revision, jdbcType=INTEGER}
    </update>

    <update id="updateProcessDefinitionTenantIdForDeploymentId" parameterType="java.util.Map">
        update ${prefix}ACT_RE_PROCDEF
        set TENANT_ID_ = #{tenantId, jdbcType=VARCHAR}
        where DEPLOYMENT_ID_ = #{deploymentId, jdbcType=VARCHAR}
    </update>

    <update id="updateProcessDefinitionVersionForProcessDefinitionId" parameterType="java.util.Map">
        update ${prefix}ACT_RE_PROCDEF
        set VERSION_ = #{version, jdbcType=INTEGER}
        where ID_ = #{processDefinitionId, jdbcType=VARCHAR}
    </update>

    <!-- PROCESSDEFINITION DELETE -->

    <delete id="deleteProcessDefinitionsByDeploymentId" parameterType="string">
        delete
        from ${prefix}ACT_RE_PROCDEF
        where DEPLOYMENT_ID_ = #{deploymenId}
    </delete>

    <!-- PROCESSDEFINITION RESULTMAP -->

    <resultMap id="processDefinitionResultMap"
               type="org.flowable.engine.impl.persistence.entity.ProcessDefinitionEntityImpl">
        <id property="id" column="ID_" jdbcType="VARCHAR"/>
        <result property="revision" column="REV_"/>
        <result property="category" column="CATEGORY_"/>
        <result property="name" column="NAME_"/>
        <result property="key" column="KEY_" jdbcType="VARCHAR"/>
        <result property="version" column="VERSION_" jdbcType="INTEGER"/>
        <result property="deploymentId" column="DEPLOYMENT_ID_" jdbcType="VARCHAR"/>
        <result property="resourceName" column="RESOURCE_NAME_" jdbcType="VARCHAR"/>
        <result property="tenantId" column="TENANT_ID_" jdbcType="VARCHAR"/>
        <result property="diagramResourceName" column="DGRM_RESOURCE_NAME_" jdbcType="VARCHAR"/>
        <result property="description" column="DESCRIPTION_" jdbcType="VARCHAR"/>
        <result property="hasStartFormKey" column="HAS_START_FORM_KEY_" jdbcType="BOOLEAN"/>
        <result property="isGraphicalNotationDefined" column="HAS_GRAPHICAL_NOTATION_" jdbcType="BOOLEAN"/>
        <result property="suspensionState" column="SUSPENSION_STATE_" jdbcType="INTEGER"/>
        <result property="derivedFrom" column="DERIVED_FROM_" jdbcType="VARCHAR"/>
        <result property="derivedFromRoot" column="DERIVED_FROM_ROOT_" jdbcType="VARCHAR"/>
        <result property="derivedVersion" column="DERIVED_VERSION_" jdbcType="INTEGER"/>
        <result property="engineVersion" column="ENGINE_VERSION_" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- PROCESSDEFINITION SELECT -->

    <select id="selectProcessDefinition" parameterType="string" resultMap="processDefinitionResultMap">
        select *
        from ${prefix}ACT_RE_PROCDEF
        where ID_ = #{processDefinitionId, jdbcType=VARCHAR}
    </select>

    <select id="selectProcessDefinitionById" parameterType="string" resultMap="processDefinitionResultMap">
        select *
        from ${prefix}ACT_RE_PROCDEF
        where ID_ = #{processDefinitionId}
    </select>

    <select id="selectProcessDefinitionsByQueryCriteria"
            parameterType="org.flowable.engine.impl.ProcessDefinitionQueryImpl" resultMap="processDefinitionResultMap">
        <if test="firstResult != null and firstResult &gt;= 0">${limitBefore}</if>
        select RES.*
        <if test="firstResult != null and firstResult &gt;= 0">${limitBetween}</if>
        <include refid="selectProcessDefinitionsByQueryCriteriaSql"/>
        ${orderBy}
        <if test="firstResult != null and firstResult &gt;= 0">${limitAfter}</if>
    </select>

    <select id="selectProcessDefinitionCountByQueryCriteria"
            parameterType="org.flowable.engine.impl.ProcessDefinitionQueryImpl" resultType="long">
        select count(RES.ID_)
        <include refid="selectProcessDefinitionsByQueryCriteriaSql"/>
    </select>

    <sql id="selectProcessDefinitionsByQueryCriteriaSql">
        from ${prefix}ACT_RE_PROCDEF RES
        <where>
            <if test="id != null">
                RES.ID_ = #{id}
            </if>
            <if test="ids != null and ids">
                and RES.ID_ in
                <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="category != null">
                and RES.CATEGORY_ = #{category}
            </if>
            <if test="categoryLike != null">
                and RES.CATEGORY_ like #{categoryLike}${wildcardEscapeClause}
            </if>
            <if test="categoryNotEquals != null">
                and ( RES.CATEGORY_ &lt;&gt; #{categoryNotEquals} OR RES.CATEGORY_ is null )
            </if>
            <if test="name != null">
                and RES.NAME_ = #{name}
            </if>
            <if test="nameLike != null">
                and RES.NAME_ like #{nameLike}${wildcardEscapeClause}
            </if>
            <if test="key != null">
                and RES.KEY_ = #{key}
            </if>
            <if test="keyLike != null">
                and RES.KEY_ like #{keyLike}${wildcardEscapeClause}
            </if>
            <if test="resourceName != null">
                and RES.RESOURCE_NAME_ = #{resourceName}
            </if>
            <if test="resourceNameLike != null">
                and RES.RESOURCE_NAME_ like #{resourceNameLike}${wildcardEscapeClause}
            </if>
            <if test="version != null">
                and RES.VERSION_ = #{version}
            </if>
            <if test="versionGt != null">
                and RES.VERSION_ &gt; #{versionGt}
            </if>
            <if test="versionGte != null">
                and RES.VERSION_ &gt;= #{versionGte}
            </if>
            <if test="versionLt != null">
                and RES.VERSION_ &lt; #{versionLt}
            </if>
            <if test="versionLte != null">
                and RES.VERSION_ &lt;= #{versionLte}
            </if>
            <if test="deploymentId != null">
                and RES.DEPLOYMENT_ID_ = #{deploymentId}
            </if>
            <if test="deploymentIds != null and !deploymentIds.isEmpty()">
                and RES.DEPLOYMENT_ID_ in
                <foreach item="item" index="index" collection="deploymentIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="latest">
                and RES.VERSION_ = (select max(VERSION_) from ${prefix}ACT_RE_PROCDEF where KEY_ = RES.KEY_
                <if test="tenantId != null">
                    and TENANT_ID_ = #{tenantId}
                </if>
                <if test="tenantIdLike != null">
                    and TENANT_ID_ like #{tenantIdLike}${wildcardEscapeClause}
                </if>
                <if test="withoutTenantId">
                    and (TENANT_ID_ = '' or TENANT_ID_ is null)
                </if>
                <if test="tenantId == null and tenantIdLike == null and !withoutTenantId">
                    and ( (TENANT_ID_ IS NOT NULL and TENANT_ID_ = RES.TENANT_ID_) or (TENANT_ID_ IS NULL and
                    RES.TENANT_ID_ IS NULL) )
                </if>
                )
            </if>
            <if test="suspensionState != null">
                and (RES.SUSPENSION_STATE_ = #{suspensionState.stateCode})
            </if>
            <if test="tenantId != null">
                and RES.TENANT_ID_ = #{tenantId}
            </if>
            <if test="tenantIdLike != null">
                and RES.TENANT_ID_ like #{tenantIdLike}${wildcardEscapeClause}
            </if>
            <if test="withoutTenantId">
                and (RES.TENANT_ID_ = '' or RES.TENANT_ID_ is null)
            </if>
            <if test="engineVersion != null">
                and RES.ENGINE_VERSION_ = #{engineVersion}
            </if>
            <if test="eventSubscriptionType != null">
                and exists(select 1 from ${prefix}ACT_RU_EVENT_SUBSCR EVT where RES.ID_ = EVT.CONFIGURATION_ and
                EVT.EVENT_TYPE_ = #{eventSubscriptionType} and EVT.EVENT_NAME_ = #{eventSubscriptionName})
            </if>
            <if test="includeAuthorization">
                AND
                <trim prefix="(" prefixOverrides="OR" suffix=")">
                    <if test="authorizationUserId != null">
                        exists (select ID_ from ${prefix}ACT_RU_IDENTITYLINK IDN where IDN.PROC_DEF_ID_ = RES.ID_ and
                        IDN.USER_ID_ = #{authorizationUserId})
                    </if>
                    <if test="authorizationGroups != null &amp;&amp; authorizationGroups.size() &gt; 0">
                        OR exists (select ID_ from ${prefix}ACT_RU_IDENTITYLINK IDN where IDN.PROC_DEF_ID_ = RES.ID_ and
                        IDN.GROUP_ID_ IN
                        <foreach item="group" index="index" collection="authorizationGroups"
                                 open="(" separator="," close=")">
                            #{group}
                        </foreach>
                        )
                    </if>
                    OR not exists (select ID_ from ${prefix}ACT_RU_IDENTITYLINK IDN where IDN.PROC_DEF_ID_ = RES.ID_)
                </trim>
            </if>
        </where>
    </sql>

    <select id="selectProcessDefinitionByDeploymentAndKey" parameterType="map" resultMap="processDefinitionResultMap">
        select *
        from ${prefix}ACT_RE_PROCDEF
        where DEPLOYMENT_ID_ = #{deploymentId}
          and KEY_ = #{processDefinitionKey}
          and (TENANT_ID_ = '' or TENANT_ID_ is null)
    </select>

    <select id="selectProcessDefinitionByDeploymentAndKeyAndTenantId" parameterType="map"
            resultMap="processDefinitionResultMap">
        select *
        from ${prefix}ACT_RE_PROCDEF
        where DEPLOYMENT_ID_ = #{deploymentId}
          and KEY_ = #{processDefinitionKey}
          and TENANT_ID_ = #{tenantId}
    </select>

    <!-- TODO when deploying cmmn from the Process engine then the BPMN resources do not have a parent deployment, they are the parent deployment   -->
    <select id="selectProcessDefinitionByParentDeploymentAndKey" parameterType="map"
            resultMap="processDefinitionResultMap">
        select *
        from ${prefix}ACT_RE_PROCDEF DEF
        where EXISTS(select 1
                     from ${prefix}ACT_RE_DEPLOYMENT D
                     where D.PARENT_DEPLOYMENT_ID_ = #{parentDeploymentId}
                       and D.ID_ = DEF.DEPLOYMENT_ID_)
          and KEY_ = #{processDefinitionKey}
          and (TENANT_ID_ = '' or TENANT_ID_ is null)
    </select>

    <select id="selectProcessDefinitionByParentDeploymentAndKeyAndTenantId" parameterType="map"
            resultMap="processDefinitionResultMap">
        select *
        from ${prefix}ACT_RE_PROCDEF DEF
        where EXISTS(select 1
                     from ${prefix}ACT_RE_DEPLOYMENT D
                     where D.PARENT_DEPLOYMENT_ID_ = #{parentDeploymentId}
                       and D.ID_ = DEF.DEPLOYMENT_ID_)
          and KEY_ = #{processDefinitionKey}
          and TENANT_ID_ = #{tenantId}
    </select>

    <select id="selectProcessDefinitionsByKeyAndVersion"
            parameterType="org.flowable.common.engine.impl.db.ListQueryParameterObject"
            resultMap="processDefinitionResultMap">
        select *
        from ${prefix}ACT_RE_PROCDEF
        where KEY_ = #{parameter.processDefinitionKey}
          and VERSION_ = #{parameter.processDefinitionVersion}
          and (TENANT_ID_ = '' or TENANT_ID_ is null)
    </select>


    <select id="selectProcessDefinitionsByKeyAndVersionAndTenantId"
            parameterType="org.flowable.common.engine.impl.db.ListQueryParameterObject"
            resultMap="processDefinitionResultMap">
        select *
        from ${prefix}ACT_RE_PROCDEF
        where KEY_ = #{parameter.processDefinitionKey}
          and VERSION_ = #{parameter.processDefinitionVersion}
          and TENANT_ID_ = #{parameter.tenantId}
    </select>

    <select id="selectLatestProcessDefinitionByKey" parameterType="string" resultMap="processDefinitionResultMap">
        select *
        from ${prefix}ACT_RE_PROCDEF
        where KEY_ = #{key}
          and (TENANT_ID_ = '' or TENANT_ID_ is null)
          and DERIVED_FROM_ is null
          and VERSION_ = (select max(VERSION_)
                          from ${prefix}ACT_RE_PROCDEF
                          where KEY_ = #{processDefinitionKey}
                            and (TENANT_ID_ = '' or TENANT_ID_ is null))
    </select>

    <select id="selectLatestProcessDefinitionByKeyAndTenantId" parameterType="map"
            resultMap="processDefinitionResultMap">
        select *
        from ${prefix}ACT_RE_PROCDEF
        where KEY_ = #{processDefinitionKey}
          and TENANT_ID_ = #{tenantId}
          and DERIVED_FROM_ is null
          and VERSION_ = (select max(VERSION_)
                          from ${prefix}ACT_RE_PROCDEF
                          where KEY_ = #{processDefinitionKey}
                            and TENANT_ID_ = #{tenantId})
    </select>

    <select id="selectLatestDerivedProcessDefinitionByKey" parameterType="string"
            resultMap="processDefinitionResultMap">
        select *
        from ${prefix}ACT_RE_PROCDEF
        where KEY_ = #{key}
          and (TENANT_ID_ = '' or TENANT_ID_ is null)
          and DERIVED_VERSION_ > 0
          and DERIVED_VERSION_ = (select max(DERIVED_VERSION_)
                                  from ${prefix}ACT_RE_PROCDEF
                                  where KEY_ = #{processDefinitionKey}
                                    and (TENANT_ID_ = '' or TENANT_ID_ is null))
    </select>

    <select id="selectLatestDerivedProcessDefinitionByKeyAndTenantId" parameterType="map"
            resultMap="processDefinitionResultMap">
        select *
        from ${prefix}ACT_RE_PROCDEF
        where KEY_ = #{processDefinitionKey}
          and TENANT_ID_ = #{tenantId}
          and DERIVED_VERSION_ > 0
          and DERIVED_VERSION_ = (select max(DERIVED_VERSION_)
                                  from ${prefix}ACT_RE_PROCDEF
                                  where KEY_ = #{processDefinitionKey}
                                    and TENANT_ID_ = #{tenantId})
    </select>

    <select id="selectProcessDefinitionByNativeQuery" parameterType="java.util.Map"
            resultMap="processDefinitionResultMap">
        <include refid="org.flowable.engine.db.common.selectByNativeQuery"/>
    </select>

    <select id="selectProcessDefinitionCountByNativeQuery" parameterType="java.util.Map" resultType="long">
        ${sql}
    </select>
</mapper>
