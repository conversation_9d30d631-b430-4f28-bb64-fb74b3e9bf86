package com.yuanqiao.insight.modules.flowable.controller;

import com.yuanqiao.insight.modules.flowable.common.BaseFlowableController;
import org.flowable.job.api.Job;
import org.jeecg.common.api.vo.Result;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020年3月24日
 */
@RestController
@RequestMapping("/flowable/processDefinitionJob")
public class ProcessDefinitionJobController extends BaseFlowableController {

    //@PreAuthorize("@elp.single('flowable:processDefinitionJob:list')")
    @GetMapping(value = "/list")
    public List<Job> list(@RequestParam String processDefinitionId) {
        return managementService.createTimerJobQuery().processDefinitionId(processDefinitionId).list();
    }

    //@Log(value = "新增流程定义定时任务")
    //@PreAuthorize("@elp.single('flowable:processDefinitionJob:delete')")
    @DeleteMapping(value = "/delete")
    @Transactional(rollbackFor = Exception.class)
    public Result deleteJob(@RequestParam String jobId) {
        managementService.deleteTimerJob(jobId);
        return Result.ok();
    }
}
