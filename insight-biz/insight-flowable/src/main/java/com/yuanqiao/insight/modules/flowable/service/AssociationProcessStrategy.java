package com.yuanqiao.insight.modules.flowable.service;

import com.yuanqiao.insight.modules.flowable.vo.ProcessInstanceRequest;

public interface AssociationProcessStrategy {

    void SetProcessContent(ProcessInstanceRequest request);


    void updateProcess(String processId,String id);

    void updateEndProcess(String processInstanceId);
    //通过草稿发起流程时修改关联id
    void updateProcessByDraft(String processId,String id);
}
