package com.yuanqiao.insight.modules.flowable.service.impl;

import com.yuanqiao.insight.modules.flowable.mapper.HistoricTaskMapper;
import com.yuanqiao.insight.modules.flowable.service.HistoricTaskService;
import org.flowable.task.service.impl.persistence.entity.HistoricTaskInstanceEntityImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class HistoricTaskServiceImpl implements HistoricTaskService {
    @Autowired
    HistoricTaskMapper historicTaskMapper;

    /**
     * 添加节点历史任务
     *
     * @param historicTaskInstanceEntity
     */
    @Override
    public void insertHistoricTask(HistoricTaskInstanceEntityImpl historicTaskInstanceEntity) {
        historicTaskMapper.insertHistoricTask(historicTaskInstanceEntity);
    }
}
