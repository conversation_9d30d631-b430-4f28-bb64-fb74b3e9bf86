package com.yuanqiao.insight.modules.flowable.controller;

import com.yuanqiao.insight.common.core.util.ObjectUtils;
import com.yuanqiao.insight.common.core.util.SecurityUtils;
import com.yuanqiao.insight.modules.flowable.common.BaseFlowableController;
import com.yuanqiao.insight.modules.flowable.common.FlowablePage;
import com.yuanqiao.insight.modules.flowable.constant.FlowableConstant;
import com.yuanqiao.insight.modules.flowable.entity.ActNode;
import com.yuanqiao.insight.modules.flowable.entity.ProcessNodeVo;
import com.yuanqiao.insight.modules.flowable.entity.ProcessParam;
import com.yuanqiao.insight.modules.flowable.mapper.ProcessDefinitionMapper;
import com.yuanqiao.insight.modules.flowable.service.ProcessDefinitionService;
import com.yuanqiao.insight.modules.flowable.service.impl.ActNodeServiceImpl;
import com.yuanqiao.insight.modules.flowable.util.FlowableUtils;
import com.yuanqiao.insight.modules.flowable.vo.ProcessDefinitionRequest;
import com.yuanqiao.insight.modules.flowable.vo.ProcessDefinitionResponse;
import com.yuanqiao.insight.modules.flowable.vo.query.BaseQueryVo;
import com.yuanqiao.insight.modules.flowable.vo.query.ProcessDefinitionQueryVo;
import com.yuanqiao.insight.modules.flowable.wapper.IListWrapper;
import com.yuanqiao.insight.modules.flowable.wapper.ProcDefListWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.*;
import org.flowable.common.engine.api.FlowableException;
import org.flowable.common.engine.api.FlowableObjectNotFoundException;
import org.flowable.common.engine.api.query.QueryProperty;
import org.flowable.engine.impl.ProcessDefinitionQueryProperty;
import org.flowable.engine.impl.persistence.entity.ProcessDefinitionEntityImpl;
import org.flowable.engine.repository.Deployment;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.repository.ProcessDefinitionQuery;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 流程定义查询接口
 * @date 2020年3月24日
 */
@Api(tags = "流程定义")
@RestController
@RequestMapping("/flowable/processDefinition")
@Slf4j
public class ProcessDefinitionController extends BaseFlowableController {
    private static final Map<String, QueryProperty> ALLOWED_SORT_PROPERTIES = new HashMap<>();

    static {
        ALLOWED_SORT_PROPERTIES.put(FlowableConstant.ID, ProcessDefinitionQueryProperty.PROCESS_DEFINITION_ID);
        ALLOWED_SORT_PROPERTIES.put(FlowableConstant.KEY, ProcessDefinitionQueryProperty.PROCESS_DEFINITION_KEY);
        ALLOWED_SORT_PROPERTIES.put(FlowableConstant.CATEGORY,
                ProcessDefinitionQueryProperty.PROCESS_DEFINITION_CATEGORY);
        ALLOWED_SORT_PROPERTIES.put(FlowableConstant.NAME, ProcessDefinitionQueryProperty.PROCESS_DEFINITION_NAME);
        ALLOWED_SORT_PROPERTIES.put(FlowableConstant.VERSION,
                ProcessDefinitionQueryProperty.PROCESS_DEFINITION_VERSION);
        ALLOWED_SORT_PROPERTIES.put(FlowableConstant.TENANT_ID,
                ProcessDefinitionQueryProperty.PROCESS_DEFINITION_TENANT_ID);
    }

    @Autowired
    private ProcessDefinitionService processDefinitionService;
    @Autowired
    private ActNodeServiceImpl actNodeService;
    @Autowired
    private ProcessDefinitionMapper processDefinitionMapper;

    //@PreAuthorize("@elp.single('flowable:processDefinition:list')")
    @GetMapping(value = "/list")
    public Result list(ProcessDefinitionQueryVo processDefinitionQueryVo) {
        ProcessDefinitionQuery processDefinitionQuery = repositoryService.createProcessDefinitionQuery();
        if (ObjectUtils.isNotEmpty(processDefinitionQueryVo.getProcessDefinitionId())) {
            processDefinitionQuery.processDefinitionId(processDefinitionQueryVo.getProcessDefinitionId());
        }
        if (ObjectUtils.isNotEmpty(processDefinitionQueryVo.getProcessDefinitionCategory())) {
            processDefinitionQuery.processDefinitionCategoryLike(ObjectUtils.convertToLike(processDefinitionQueryVo.getProcessDefinitionCategory()));
        }
        if (ObjectUtils.isNotEmpty(processDefinitionQueryVo.getProcessDefinitionKey())) {
            processDefinitionQuery.processDefinitionKeyLike(ObjectUtils.convertToLike(processDefinitionQueryVo.getProcessDefinitionKey()));
        }
        if (ObjectUtils.isNotEmpty(processDefinitionQueryVo.getProcessDefinitionName())) {
            processDefinitionQuery.processDefinitionNameLike(ObjectUtils.convertToLike(processDefinitionQueryVo.getProcessDefinitionName()));
        }
        if (ObjectUtils.isNotEmpty(processDefinitionQueryVo.getProcessDefinitionVersion())) {
            processDefinitionQuery.processDefinitionVersion(processDefinitionQueryVo.getProcessDefinitionVersion());
        }

        if (processDefinitionQueryVo.getState() == 2) {
            processDefinitionQuery.active();
        } else if (processDefinitionQueryVo.getState() == 3) {
            processDefinitionQuery.suspended();
        }

        if (processDefinitionQueryVo.getLatestVersion()) {
            processDefinitionQuery.latestVersion();
        }
        if (ObjectUtils.isNotEmpty(processDefinitionQueryVo.getStartableByUser())) {
            processDefinitionQuery.startableByUser(processDefinitionQueryVo.getStartableByUser());
        }
        if (ObjectUtils.isNotEmpty(processDefinitionQueryVo.getTenantId())) {
            processDefinitionQuery.processDefinitionTenantId(processDefinitionQueryVo.getTenantId());
        }
        FlowablePage page = this.pageList(processDefinitionQueryVo, processDefinitionQuery, ProcDefListWrapper.class,
                ALLOWED_SORT_PROPERTIES);
        return Result.ok(page);
    }

    /**
     * 发起流程
     *
     * @param processDefinitionQueryVo
     * @return
     */
    @GetMapping(value = "/listMyself")
    public Result listMyself(ProcessDefinitionQueryVo processDefinitionQueryVo) {
        int pageNo = processDefinitionQueryVo.getPageNo() > 0 ? processDefinitionQueryVo.getPageNo() : 1;
        int pageSize = processDefinitionQueryVo.getPageSize() > 0 ? processDefinitionQueryVo.getPageSize() : 10;
        int offset = (pageNo - 1) * pageSize;
        String userId = SecurityUtils.getUser().getUsername();
        List<ProcessDefinitionEntityImpl> list = processDefinitionMapper.listMyself(processDefinitionQueryVo, userId, pageSize, offset);
        IListWrapper listWrapper = SpringContextUtils.getBean(ProcDefListWrapper.class);
        list = listWrapper.execute(list);
        FlowablePage flowablePage=new FlowablePage(offset,pageSize);
        flowablePage.setTotal(processDefinitionMapper.listMyselfCount(processDefinitionQueryVo, userId));
        flowablePage.setRecords(list);
        return Result.ok(flowablePage);
    }

    @GetMapping(value = "/queryById")
    public Result queryById(@RequestParam String processDefinitionId) {

        permissionService.validateReadPermissionOnProcessDefinition(SecurityUtils.getUser().getUsername(), processDefinitionId);
        ProcessDefinition processDefinition = processDefinitionService.getProcessDefinitionById(processDefinitionId);
        String formKey = null;
        if (processDefinition.hasStartFormKey()) {
            formKey = formService.getStartFormKey(processDefinitionId);
        }
        ProcessDefinitionResponse processDefinitionResponse =
                responseFactory.createProcessDefinitionResponse(processDefinition, formKey);
        return Result.ok(processDefinitionResponse);
    }

    @GetMapping(value = "/queryByKey")
    public Result queryByKey(@RequestParam String processDefinitionKey) {
        ProcessDefinition processDefinition = processDefinitionService.getProcessDefinitionByKey(processDefinitionKey);
        String formKey = null;
        if (processDefinition.hasStartFormKey()) {
            formKey = formService.getStartFormKey(processDefinition.getId());
        }
        ProcessDefinitionResponse processDefinitionResponse =
                responseFactory.createProcessDefinitionResponse(processDefinition, formKey);

        return Result.ok(processDefinitionResponse);
    }

    @GetMapping(value = "/renderedStartForm")
    public Result renderedStartForm(@RequestParam String processDefinitionId) {
        HashMap<String, Object> result = processDefinitionService.renderedStartForm(processDefinitionId);

        //获取流程定义发起节点的按钮配置
        Process process = repositoryService.getBpmnModel(processDefinitionId).getMainProcess();
        UserTask userTask = (UserTask) process.getFlowElement(FlowableConstant.INITIATOR, true);
        if (userTask == null) {
            throw new FlowableObjectNotFoundException("未找到用户发起节点信息，请联系管理员修改流程配置");
        }

        String buttons = FlowableUtils.getFlowableAttributeValue(userTask, FlowableConstant.BUTTONS);
        if (buttons != null) {
            result.put("buttons", buttons.split(","));
        }else{
            result.put("buttons", new ArrayList<>());
        }

        //是否显示业务key
        boolean showBusinessKey = this.isShowBusinessKey(processDefinitionId);
        result.put("showBusinessKey", showBusinessKey);
        return Result.ok(result);
    }

    @GetMapping(value = "/image")
    public ResponseEntity<byte[]> image(@RequestParam String processDefinitionId) {
//        permissionService.validateReadPermissionOnProcessDefinition(SecurityUtils.getUser().getUsername(), processDefinitionId);
        ProcessDefinition processDefinition = processDefinitionService.getProcessDefinitionById(processDefinitionId);
        InputStream imageStream = repositoryService.getProcessDiagram(processDefinition.getId());
        if (imageStream == null) {
            throw new FlowableException(messageFormat("Process definition image is not found with id {0}",
                    processDefinitionId));
        }
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.setContentType(MediaType.IMAGE_PNG);
        try {
            return new ResponseEntity<>(IOUtils.toByteArray(imageStream), responseHeaders, HttpStatus.OK);
        } catch (Exception e) {
            throw new FlowableException(messageFormat("Process definition image read error with id {0}",
                    processDefinitionId), e);
        }
    }

    //@PreAuthorize("@elp.single('flowable:processDefinition:xml')")
    @GetMapping(value = "/xml")
    public ResponseEntity<byte[]> xml(@RequestParam String processDefinitionId) {
        permissionService.validateReadPermissionOnProcessDefinition(SecurityUtils.getUser().getUsername(), processDefinitionId);
        ProcessDefinition processDefinition = processDefinitionService.getProcessDefinitionById(processDefinitionId);
        String deploymentId = processDefinition.getDeploymentId();
        String resourceId = processDefinition.getResourceName();
        if (deploymentId == null || deploymentId.length() == 0) {
            throw new FlowableException(messageFormat("Process definition deployment id is not found with id {0}",
                    processDefinitionId));
        }
        if (resourceId == null || resourceId.length() == 0) {
            throw new FlowableException(messageFormat("Process definition resource id is not found with id {0}",
                    processDefinitionId));
        }
        Deployment deployment = repositoryService.createDeploymentQuery().deploymentId(deploymentId).singleResult();
        if (deployment == null) {
            throw new FlowableException(messageFormat("Process definition deployment is not found with deploymentId " + "{0}", deploymentId));
        }

        List<String> resourceList = repositoryService.getDeploymentResourceNames(deploymentId);
        if (ObjectUtils.isEmpty(resourceList) || !resourceList.contains(resourceId)) {
            throw new FlowableException(messageFormat("Process definition resourceId {0} is not found with " +
                    "deploymentId {1}", resourceId, deploymentId));
        }
        InputStream resourceStream = repositoryService.getResourceAsStream(deploymentId, resourceId);
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.setContentType(MediaType.TEXT_XML);
        try {
            return new ResponseEntity<>(IOUtils.toByteArray(resourceStream), responseHeaders, HttpStatus.OK);
        } catch (Exception e) {
            log.error("获取流程定义XML信息异常", e);
            throw new FlowableException(messageFormat("ProcessDefinition xml read error with id {0}", deploymentId), e);
        }
    }

    //@Log(value = "删除流程定义")
    //@PreAuthorize("@elp.single('flowable:processDefinition:delete')")
    @DeleteMapping(value = "/delete")
    public Result delete(@RequestParam String processDefinitionId, @RequestParam(required = false, defaultValue =
            "false") Boolean cascade) {
        processDefinitionService.delete(processDefinitionId, cascade);
        return Result.ok("删除流程定义成功");
    }

    //@Log(value = "激活流程定义")
    //@PreAuthorize("@elp.single('flowable:processDefinition:suspendOrActivate')")
    @PutMapping(value = "/activate")
    public Result activate(@RequestBody ProcessDefinitionRequest actionRequest) {
        processDefinitionService.activate(actionRequest);
        return Result.ok("流程激活成功");
    }

    @DeleteMapping(value = "/deleteInstance")
    public Result deleteInstance(@RequestParam String processDefinitionId) {
        processDefinitionService.deleteInstancesByDefinitionId(processDefinitionId);
        return Result.ok("删除流程定义下的流程实例成功");
    }

    /**
     * 删除对应流程定义的流程实例
     *
     * @param processDefinitionIds 流程实例定义id组；英文，分割
     * @return
     */
    @DeleteMapping(value = "/deleteInstances")
    public Result deleteInstances(@RequestParam String processDefinitionIds) {
        for (String processDefinitionId : processDefinitionIds.split(",")) {
            processDefinitionService.deleteInstancesByDefinitionId(processDefinitionId);
        }
        return Result.ok("删除流程定义下的流程实例成功");
    }

    //@Log(value = "挂起流程定义")
    //@PreAuthorize("@elp.single('flowable:processDefinition:suspendOrActivate')")
    @PutMapping(value = "/suspend")
    public Result suspend(@RequestBody ProcessDefinitionRequest actionRequest) {
        processDefinitionService.suspend(actionRequest);
        return Result.ok("流程挂起成功");
    }

    /**
     * 导入流程定义
     *
     * @param request
     * @return
     */
    //@Log(value = "导入流程定义")
    //@PreAuthorize("@elp.single('flowable:processDefinition:import')")
    @PostMapping(value = "/import")
    public Result doImport(@RequestParam(required = false) String tenantId, HttpServletRequest request) {
        processDefinitionService.doImport(tenantId, request);
        return Result.ok();
    }

    @PostMapping(value = "/getProcessNode")
    @ApiOperation(value = "获取节点各个信息", notes = "获取节点各个信息")
    public Result getProcessNode(@RequestBody ProcessParam processParam) {
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processParam.getId());

        List<ProcessNodeVo> list = new LinkedList<>();

        List<Process> processList = bpmnModel.getProcesses();
        if (processList == null || processList.size() == 0) {
            throw new FlowableException(messageFormat("Process definition process is not found with id {0}",
                    processParam.getId()));
        }
        for (Process process : processList) {
            Collection<FlowElement> elements = process.getFlowElements().stream().filter(flowElement -> flowElement instanceof UserTask||flowElement instanceof ServiceTask).collect(Collectors.toList());
            for (FlowElement flowElement : elements) {
                ProcessNodeVo node = new ProcessNodeVo();
                node.setId(flowElement.getId());//节点id
                node.setTitle(flowElement.getName());//节点名称
                if (flowElement instanceof StartEvent) {
                    node.setType(0);
                } else if (flowElement instanceof UserTask ||flowElement instanceof ServiceTask) {
                    // 用户任务
                    node.setType(1);
                    //  2022/6/28  流程定义  列表查询 此处应该考虑节点配置的版本
//                    寻找指定版本的节点配置，如果存在，查找所有条件
                    int proDefVersion = processParam.getProDefVersion();
                    List<ActNode> nodes = actNodeService.listNodeByModelKeyAndNodeIdAndVersion(processParam.getModelKey(), flowElement.getId(), proDefVersion);
                    if (nodes!=null && nodes.size() > 0) {
                        Map<Integer, List<ActNode>> collect = nodes.stream().collect(Collectors.groupingBy(ActNode::getType));
                        for (Map.Entry<Integer, List<ActNode>> integerListEntry : collect.entrySet()) {
                            List<String> ids = integerListEntry.getValue().stream().map(ActNode::getRelateId).collect(Collectors.toList());
                            if (integerListEntry.getKey() == 0) {
                                //设置关联角色
                                node.setRoles(actNodeService.findRoleByIds(ids));
                            } else if (integerListEntry.getKey() == 1) {
                                //设置关联用户
                                node.setUsers(actNodeService.findUserByIds(ids));
                            } else if (integerListEntry.getKey() == 2) {
                                //设置关联部门
                                node.setDepartments(actNodeService.findDepartmentByIds(ids));
                            } else if (integerListEntry.getKey() == 3) {
                                // 是否设置发起人
                                node.setChooseSponsor(true);
                            } else if (integerListEntry.getKey() == 5) {
                                // 是否设置发起人
                                node.setChooseDep(true);
                            }else if (integerListEntry.getKey() == 6) {
                               //节点办理人
                                if (ids.size()>0){
                                    node.setNodeIds(ids.get(0));
                                }
                            }else if (integerListEntry.getKey() == 7) {
                                 node.setChooseDepNode(true);
                            }else if (integerListEntry.getKey() == 8) {
                                 node.setChooseProvider(true);
                            }
                        }
                    }
                } else if (flowElement instanceof EndEvent) {
                    // 结束
                    node.setType(2);
                } else {
                    continue;
                }

                list.add(node);
            }
        }
        list.sort(Comparator.comparing(ProcessNodeVo::getTitle));
        return Result.ok(list);
    }


    /**
     * 编辑节点分配用户
     *
     * @return
     */
    @ApiOperation(value = "编辑节点分配用户", notes = "编辑节点分配用户")
    @RequestMapping(value = "/editNodeUser", method = RequestMethod.POST)
    public Result editNodeUser(@RequestBody ProcessParam processParam) {
        //删除其关联权限
        actNodeService.deleteByNodeIdAndProVersion(processParam.getNodeId(), processParam.getProDefVersion());
        // 分配新用户
        if (StringUtils.isNotEmpty(processParam.getUserIds()) && processParam.getUserIds().split(",").length > 0) {
            for (String userId : processParam.getUserIds().split(",")) {
                ActNode actNode = new ActNode();
                actNode.setNodeId(processParam.getNodeId());
                actNode.setRelateId(userId);
                actNode.setProDefVersion(processParam.getProDefVersion());
                actNode.setType(1);

                actNode.setModelKey(processParam.getModelKey());
                actNodeService.save(actNode);
            }
        }
        if (StringUtils.isNotEmpty(processParam.getRoleIds()) && processParam.getRoleIds().split(",").length > 0) {
            // 分配新角色
            for (String roleId : processParam.getRoleIds().split(",")) {
                ActNode actNode = new ActNode();
                actNode.setNodeId(processParam.getNodeId());
                actNode.setRelateId(roleId);
                actNode.setProDefVersion(processParam.getProDefVersion());
                actNode.setType(0);
                actNode.setModelKey(processParam.getModelKey());

                actNodeService.save(actNode);
            }
        }
        if (StringUtils.isNotEmpty(processParam.getDepartmentIds()) && processParam.getDepartmentIds().split(",").length > 0) {
            // 分配新部门
            for (String departmentId : processParam.getDepartmentIds().split(",")) {
                ActNode actNode = new ActNode();
                actNode.setNodeId(processParam.getNodeId());
                actNode.setRelateId(departmentId);
                actNode.setProDefVersion(processParam.getProDefVersion());
                actNode.setType(2);
                actNode.setModelKey(processParam.getModelKey());

                actNodeService.save(actNode);
            }
        }

        if (processParam.getChooseDepHeader() != null && processParam.getChooseDepHeader()) {
            ActNode actNode = new ActNode();
            actNode.setNodeId(processParam.getNodeId());
            actNode.setRelateId(processParam.getRoleId());
            actNode.setType(4);
            actNode.setProDefVersion(processParam.getProDefVersion());
            actNode.setModelKey(processParam.getModelKey());

            actNodeService.save(actNode);
        }
        if (processParam.getChooseSponsor() != null && processParam.getChooseSponsor()) {
            ActNode actNode = new ActNode();
            actNode.setNodeId(processParam.getNodeId());
            actNode.setType(3);
            actNode.setProDefVersion(processParam.getProDefVersion());
            actNode.setModelKey(processParam.getModelKey());

            actNodeService.save(actNode);
        }

        if (processParam.getChooseDep()) {
            ActNode actNode = new ActNode();
            actNode.setNodeId(processParam.getNodeId());
            actNode.setType(5);
            actNode.setProDefVersion(processParam.getProDefVersion());
            actNode.setModelKey(processParam.getModelKey());

            actNodeService.save(actNode);
        }
        if (processParam.getChooseProvider()) {
            ActNode actNode = new ActNode();
            actNode.setNodeId(processParam.getNodeId());
            actNode.setType(8);
            actNode.setProDefVersion(processParam.getProDefVersion());
            actNode.setModelKey(processParam.getModelKey());
            actNodeService.save(actNode);
        }
        //节点办理人
        if (StringUtils.isNotEmpty(processParam.getNodeIds())) {
            ActNode actNode = new ActNode();
            actNode.setNodeId(processParam.getNodeId());
            actNode.setType(6);
            actNode.setRelateId(processParam.getNodeIds());
            actNode.setProDefVersion(processParam.getProDefVersion());
            actNode.setModelKey(processParam.getModelKey());
            actNodeService.save(actNode);
        }

        if (processParam.getChooseDepNode()&&StringUtils.isNotEmpty(processParam.getNodeIds())) {
            ActNode actNode = new ActNode();
            actNode.setNodeId(processParam.getNodeId());
            actNode.setType(7);
            actNode.setProDefVersion(processParam.getProDefVersion());
            actNode.setModelKey(processParam.getModelKey());
            actNodeService.save(actNode);
        }

        return Result.ok("操作成功");
    }
    /**
     * 根据业务信息ID查询流程定义
     *
     * @param tenantId
     * @return
     */
    @ApiOperation(value = "根据业务信息ID查询流程定义", notes = "根据业务信息ID查询流程定义")
    @GetMapping(value = "/queryByTenantId")
    public Result queryByTenantId(@ApiParam(name = "tenantId", value = "业务id", required = true)String tenantId) {
        Assert.notNull(tenantId,"业务信息ID不能为空");
        ProcessDefinitionQuery processDefinitionQuery = repositoryService.createProcessDefinitionQuery();
        processDefinitionQuery.processDefinitionTenantId(tenantId);
        processDefinitionQuery.latestVersion().active().startableByUser(SecurityUtils.getUser().getUsername());
        FlowablePage page = this.pageList(null, processDefinitionQuery, ProcDefListWrapper.class,
                ALLOWED_SORT_PROPERTIES, ProcessDefinitionQueryProperty.PROCESS_DEFINITION_NAME,new BaseQueryVo());
        return Result.ok(page.getRecords());
    }

    /****
     * 模糊查询全部的流程定义key
     *
     * @param processDefinitionKey
     * @return
     */
    @ApiOperation(value = "模糊查询全部的流程定义key", notes = "模糊查询全部的流程定义key")
    @GetMapping(value = "/queryKeyList")
    public Result<List<String>> queryKeyList(@ApiParam(name = "processDefinitionKey", value = "流程定义key")
                                             @RequestParam(required = false) String processDefinitionKey) {
        return Result.OK(processDefinitionService.queryKeyList(processDefinitionKey));
    }
}
