package com.yuanqiao.insight.modules.flowable.notification.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.modules.flowable.notification.entity.ActZNotification;
import com.yuanqiao.insight.modules.flowable.notification.service.IActZNotificationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

/**
 * @Description: act_z_notification
 * @author: yqkj
 * @Date: 2022-07-05
 * @Version: V1.0
 */
@Api(tags = "act_z_notification")
@RestController
@RequestMapping("/notification/actZNotification")
@Slf4j
public class ActZNotificationController extends JeecgController<ActZNotification, IActZNotificationService> {
	@Autowired
	private IActZNotificationService actZNotificationService;

	/**
	 * 分页列表查询
	 *
	 * @param actZNotification
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "act_z_notification-分页列表查询")
	@ApiOperation(value = "act_z_notification-分页列表查询", notes = "act_z_notification-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<ActZNotification>> queryPageList(ActZNotification actZNotification,
														 @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
														 @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
														 HttpServletRequest req) {
		QueryWrapper<ActZNotification> queryWrapper = QueryGenerator.initQueryWrapper(actZNotification, req.getParameterMap());
		Page<ActZNotification> page = new Page<ActZNotification>(pageNo, pageSize);
		IPage<ActZNotification> pageList = actZNotificationService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 * 添加
	 *
	 * @param actZNotification
	 * @return
	 */
	@AutoLog(value = "act_z_notification-添加")
	@ApiOperation(value = "act_z_notification-添加", notes = "act_z_notification-添加")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody ActZNotification actZNotification) {
		actZNotificationService.saveOrUpdate(actZNotification);
		return Result.OK("添加成功！", actZNotification.getId().toString());
	}

	/**
	 * 编辑
	 *
	 * @param actZNotification
	 * @return
	 */
	@AutoLog(value = "act_z_notification-编辑")
	@ApiOperation(value = "act_z_notification-编辑", notes = "act_z_notification-编辑")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
	public Result<String> edit(@RequestBody ActZNotification actZNotification) {
		actZNotificationService.updateById(actZNotification);
		return Result.OK("编辑成功!");
	}

	/**
	 * 通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "act_z_notification-通过id删除")
	@ApiOperation(value = "act_z_notification-通过id删除", notes = "act_z_notification-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
		actZNotificationService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 * 批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "act_z_notification-批量删除")
	@ApiOperation(value = "act_z_notification-批量删除", notes = "act_z_notification-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
		this.actZNotificationService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "act_z_notification-通过id查询")
	@ApiOperation(value = "act_z_notification-通过id查询", notes = "act_z_notification-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
		ActZNotification actZNotification = actZNotificationService.getById(id);
		if (actZNotification == null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(actZNotification);
	}

	@GetMapping(value = "/queryByNodeIdAndVersion")
	public Result<ActZNotification> queryByNodeIdAndVersion(@RequestParam String nodeId, @RequestParam int proDefVersion, @RequestParam String modelKey) {
		ActZNotification result = actZNotificationService.queryByNodeIdAndVersion(nodeId, proDefVersion, modelKey);
		return Result.OK(result);
	}

	/**
	 * 导出excel
	 *
	 * @param request
	 * @param actZNotification
	 */
	@RequestMapping(value = "/exportXls")
	public ModelAndView exportXls(HttpServletRequest request, ActZNotification actZNotification) {
		return super.exportXls(request, actZNotification, ActZNotification.class, "act_z_notification");
	}

	/**
	 * 通过excel导入数据
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/importExcel", method = RequestMethod.POST)
	public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
		return super.importExcel(request, response, ActZNotification.class);
	}

}
