package com.yuanqiao.insight.modules.flowable.service;

import com.yuanqiao.insight.modules.flowable.vo.ProcessInstanceRequest;
import com.yuanqiao.insight.modules.flowable.vo.ServiceProcessVo;
import com.yuanqiao.insight.modules.flowable.vo.query.ProcessInstanceQueryVo;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.runtime.ProcessInstance;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020年3月23日
 */
public interface ProcessInstanceService {
    /**
     * 查询单一流程实例
     *
     * @param processInstanceId
     * @return
     */
    ProcessInstance getProcessInstanceById(String processInstanceId);

    /**
     * 查询单一历史流程实例
     *
     * @param processInstanceId
     * @return
     */
    HistoricProcessInstance getHistoricProcessInstanceById(String processInstanceId);


    HistoricProcessInstance getHistoricProcessInstanceVariablesById(String processInstanceId);

    /**
     * 启动流程实例
     *
     * @param processInstanceRequest
     */
    String start(ProcessInstanceRequest processInstanceRequest);

    /**
     * 删除流程实例
     *
     * @param processInstanceId
     * @param cascade
     * @param deleteReason
     * @param isDelAll
     */
    void delete(String processInstanceId, boolean cascade, String deleteReason, boolean isDelAll);

    /**
     * 激活流程实例
     *
     * @param processInstanceId
     */
    void activate(String processInstanceId);

    /**
     * 挂起流程实例
     *
     * @param processInstanceId
     */
    void suspend(String processInstanceId);

    /**
     * 查询我的流程汇总信息
     *
     * @param processInstanceQueryVo
     */
    List listMyInvolvedSummary(ProcessInstanceQueryVo processInstanceQueryVo);

    Map<String,Object> getVariables(Date endTime, String processInstanceId);


    Map<String, Object> getVariables(String processInstanceId);

    void exportFrom(HttpServletResponse response, ProcessInstanceQueryVo serviceProcessVo);

    List getAllProcessInstanceByUsers(ProcessInstanceQueryVo processInstanceQueryVo,List<String> userList);

    long getAllProcessInstanceCountByUsers(ProcessInstanceQueryVo processInstanceQueryVo,List<String> userList);


    List<ServiceProcessVo> getServiceProviderWorkOrder(ServiceProcessVo serviceProcessVo, ProcessInstanceQueryVo processInstanceQueryVo, List<String> serviceProviderUserNames);

    Long getServiceProviderWorkOrderCount(ServiceProcessVo serviceProcessVo, ProcessInstanceQueryVo processInstanceQueryVo, List<String> serviceProviderUserNames);

    void exportServiceProviderWorkOrder(ServiceProcessVo serviceProcessVo, ProcessInstanceQueryVo processInstanceQueryVo, List<String> serviceProviderUserNames);
}
