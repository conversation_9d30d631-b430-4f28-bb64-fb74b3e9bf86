package com.yuanqiao.insight.modules.flowable.util;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

public class PageUtil {
    public static <T> IPage<T> convertListToIPage(List<T> list, long current, long size) {
        // 创建分页对象
        Page<T> page = new Page<>(current, size);

        // 设置总记录数
        page.setTotal(list.size());

        // 计算当前页的数据
        int start = (int) ((current - 1) * size);
        int end = Math.min(start + (int) size, list.size());
        List<T> currentPageData = list.subList(start, end);

        // 设置当前页的数据
        page.setRecords(currentPageData);

        return page;
    }

}
