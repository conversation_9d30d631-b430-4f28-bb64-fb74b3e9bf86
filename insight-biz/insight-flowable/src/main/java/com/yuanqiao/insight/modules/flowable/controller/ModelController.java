package com.yuanqiao.insight.modules.flowable.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yuanqiao.insight.common.core.util.ObjectUtils;
import com.yuanqiao.insight.modules.flowable.common.BaseFlowableController;
import com.yuanqiao.insight.modules.flowable.common.FlowablePage;
import com.yuanqiao.insight.modules.flowable.common.ResponseFactory;
import com.yuanqiao.insight.modules.flowable.common.cmd.DeployModelCmd;
import com.yuanqiao.insight.modules.flowable.common.cmd.SaveModelEditorCmd;
import com.yuanqiao.insight.modules.flowable.constant.FlowableConstant;
import com.yuanqiao.insight.modules.flowable.entity.NodeVo;
import com.yuanqiao.insight.modules.flowable.mapper.ActNodeMapper;
import com.yuanqiao.insight.modules.flowable.notification.entity.ActZNotification;
import com.yuanqiao.insight.modules.flowable.notification.service.IActZNotificationService;
import com.yuanqiao.insight.modules.flowable.service.ActNodeService;
import com.yuanqiao.insight.modules.flowable.util.PageUtil;
import com.yuanqiao.insight.modules.flowable.vo.ModelRequest;
import com.yuanqiao.insight.modules.flowable.vo.ModelResponse;
import com.yuanqiao.insight.modules.flowable.vo.query.ModelQueryVo;
import com.yuanqiao.insight.modules.flowable.wapper.ModelListWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.flowable.bpmn.model.UserTask;
import org.flowable.common.engine.api.FlowableException;
import org.flowable.common.engine.api.FlowableObjectNotFoundException;
import org.flowable.common.engine.api.query.QueryProperty;
import org.flowable.common.engine.impl.javax.el.PropertyNotFoundException;
import org.flowable.common.engine.impl.util.IoUtil;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.impl.ModelQueryProperty;
import org.flowable.engine.repository.Model;
import org.flowable.engine.repository.ModelQuery;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.api.CommonListenerService;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.system.vo.SysDepartModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * 流程模型管理接口
 */
@RestController
@Api(tags = "流程模型管理")
@RequestMapping("/flowable/model")
public class ModelController extends BaseFlowableController {
    private static final Map<String, QueryProperty> ALLOWED_SORT_PROPERTIES = new HashMap<>();

    static {
        ALLOWED_SORT_PROPERTIES.put("id", ModelQueryProperty.MODEL_ID);
        ALLOWED_SORT_PROPERTIES.put("category", ModelQueryProperty.MODEL_CATEGORY);
        ALLOWED_SORT_PROPERTIES.put("createTime", ModelQueryProperty.MODEL_CREATE_TIME);
        ALLOWED_SORT_PROPERTIES.put("key", ModelQueryProperty.MODEL_KEY);
        ALLOWED_SORT_PROPERTIES.put("lastUpdateTime", ModelQueryProperty.MODEL_LAST_UPDATE_TIME);
        ALLOWED_SORT_PROPERTIES.put("name", ModelQueryProperty.MODEL_NAME);
        ALLOWED_SORT_PROPERTIES.put("version", ModelQueryProperty.MODEL_VERSION);
        ALLOWED_SORT_PROPERTIES.put("tenantId", ModelQueryProperty.MODEL_TENANT_ID);
    }

    @Autowired
    protected ResponseFactory responseFactory;
    @Autowired
    protected ObjectMapper objectMapper;
    @Autowired
    private ActNodeService actNodeService;
    @Autowired
    CommonListenerService commonListenerService;
    @Autowired
    private IActZNotificationService iActZNotificationService;

    /**
     * 流程模型分页列表查询
     *
     * @param modelQueryVo 流程模型查询视图对象
     * @return
     */
    @ApiOperation(value = "流程模型分页列表查询", notes = "流程模型分页列表查询")
    @GetMapping(value = "/list")
    public Result list(ModelQueryVo modelQueryVo) {
        ModelQuery modelQuery = repositoryService.createModelQuery();

        if (ObjectUtils.isNotEmpty(modelQueryVo.getModelId())) {
            modelQuery.modelId(modelQueryVo.getModelId());
        }
        if (ObjectUtils.isNotEmpty(modelQueryVo.getModelCategory())) {
            modelQuery.modelCategory(modelQueryVo.getModelCategory());
        }
        if (ObjectUtils.isNotEmpty(modelQueryVo.getModelName())) {
            modelQuery.modelNameLike(ObjectUtils.convertToLike(modelQueryVo.getModelName()));
        }
        if (ObjectUtils.isNotEmpty(modelQueryVo.getModelKey())) {
            modelQuery.modelKey(modelQueryVo.getModelKey());
        }
        if (ObjectUtils.isNotEmpty(modelQueryVo.getModelVersion())) {
            modelQuery.modelVersion(modelQueryVo.getModelVersion());
        }
        if (modelQueryVo.getLatestVersion() != null) {
            if (modelQueryVo.getLatestVersion()) {
                modelQuery.latestVersion();
            }
        }
        if (ObjectUtils.isNotEmpty(modelQueryVo.getDeploymentId())) {
            modelQuery.deploymentId(modelQueryVo.getDeploymentId());
        }
        if (modelQueryVo.getDeployed() != null) {
            if (modelQueryVo.getDeployed()) {
                modelQuery.deployed();
            } else {
                modelQuery.notDeployed();
            }
        }
        if (ObjectUtils.isNotEmpty(modelQueryVo.getTenantId())) {
            modelQuery.modelTenantId(modelQueryVo.getTenantId());
        }

        modelQuery.orderByCreateTime().desc();

        FlowablePage page = this.pageList(modelQueryVo, modelQuery, ModelListWrapper.class, ALLOWED_SORT_PROPERTIES);
        return Result.ok(page);
    }

    protected Model getModelById(String modelId) {
        Model model = repositoryService.getModel(modelId);
        if (model == null) {
            throw new FlowableObjectNotFoundException("No model found with id " + modelId);
        }
        return model;
    }

    protected void checkModelKeyExists(String modelKey) {
        long countNum = repositoryService.createModelQuery().modelKey(modelKey).count();
        if (countNum > 0) {
            throw new FlowableObjectNotFoundException("ModelKey already exists with id " + modelKey);
        }
    }

    /**
     * 根据id获取流程模型信息
     *
     * @param id 流程模型ID
     * @return
     * @throws UnsupportedEncodingException
     */
    @ApiOperation(value = "根据id主键获取流程模型信息", notes = "根据id主键获取流程模型信息")
    @GetMapping(value = "/queryById")
    public Result queryById(@RequestParam String id) throws UnsupportedEncodingException {
        Model model = getModelById(id);
        ModelResponse modelResponse = responseFactory.createModelResponse(model);
        if (model.hasEditorSource()) {
            byte[] editorBytes = repositoryService.getModelEditorSource(model.getId());
            String editor = new String(editorBytes, StandardCharsets.UTF_8);
            modelResponse.setEditor(editor);
        }
        return Result.ok(modelResponse);
    }

    /**
     * 新增流程模型
     *
     * @param modelRequest
     * @return
     */
    @AutoLog(value = "新增流程模型")
    @ApiOperation(value = "新增流程模型", notes = "新增流程模型")
    @PostMapping(value = "/save")
    @Transactional(rollbackFor = Exception.class)
    public Result save(@RequestBody ModelRequest modelRequest) {
        Assert.notNull(modelRequest.getKey(), "key is null");
        Assert.notNull(modelRequest.getName(), "name is null");
        Assert.notNull(modelRequest.getCategory(), "category is null");
//        if(modelRequest.getCategory().equals("business")&&StringUtils.isEmpty(modelRequest.getTenantId())){
//         return  Result.error("业务id不能为空");
//        }
        checkModelKeyExists(modelRequest.getKey());

        Model model = repositoryService.newModel();
        model.setKey(modelRequest.getKey());
        model.setName(modelRequest.getName());
        model.setVersion(1);
        model.setMetaInfo(modelRequest.getDescription());
        model.setTenantId(modelRequest.getTenantId());
        model.setCategory(modelRequest.getCategory());
        repositoryService.saveModel(model);

        return Result.ok();
    }

    /**
     * 复制流程模型
     *
     * @param modelRequest
     * @return
     * @throws IOException
     */
    @AutoLog(value = "复制流程模型")
    @ApiOperation(value = "复制流程模型", notes = "复制流程模型")
    @PutMapping(value = "/copy")
    @Transactional(rollbackFor = Exception.class)
    public Result copy(@RequestBody ModelRequest modelRequest) throws IOException {
        Assert.notNull(modelRequest.getId(), "id is null");
        Model model = repositoryService.getModel(modelRequest.getId());
        if (model == null) {
            throw new FlowableException("Cannot find model by id:" + modelRequest.getId());
        }
        byte[] editor = repositoryService.getModelEditorSource(modelRequest.getId());
        if (editor == null || editor.length == 0) {
            throw new FlowableException("Cannot find modelEditor by id:" + modelRequest.getId());
        }
        managementService.executeCommand(new SaveModelEditorCmd(SaveModelEditorCmd.TYPE_3, null, null, null, null,
                null, editor, model.getTenantId()));
        return Result.ok();
    }

    /**
     * 删除流程模型
     *
     * @param ids
     * @param cascade
     * @return
     */
    @AutoLog(value = "删除流程模型")
    @ApiOperation(value = "删除流程模型", notes = "删除流程模型")
    @DeleteMapping(value = "/delete")
    @Transactional(rollbackFor = Exception.class)
    public Result delete(@RequestParam String ids, @RequestParam(required = false) boolean cascade) {
        if (ids == null || ids.trim().length() == 0) {
            return Result.error("ids can't be empty");
        }
        String[] idsArr = ids.split(",");
        for (String id : idsArr) {
            Model model = getModelById(id);
//            if (StringUtils.isNotBlank(model.getDeploymentId())) {
//                return Result.error("选中的模型中已经部署，不可删除");
//            }
            if (cascade && model.getDeploymentId() != null) {
                ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionKey(model.getKey()).latestVersion().singleResult();
                List<HistoricProcessInstance> list = historyService.createHistoricProcessInstanceQuery().processDefinitionId(processDefinition.getId()).list();
                for (HistoricProcessInstance historicProcessInstance : list) {
                    commonListenerService.deleteAll(historicProcessInstance.getId());
                }
                repositoryService.deleteDeployment(model.getDeploymentId(), cascade);
            }
            repositoryService.deleteModel(model.getId());
        }

        return Result.ok();
    }

    /**
     * 保存流程设计
     *
     * @param modelRequest
     * @return
     * @throws UnsupportedEncodingException
     */
    @AutoLog(value = "保存流程设计")
    @ApiOperation(value = "保存流程设计", notes = "保存流程设计")
    @PutMapping(value = "/saveModelEditor")
    @Transactional(rollbackFor = Exception.class)
    public Result saveModelEditor(@RequestBody ModelRequest modelRequest) throws UnsupportedEncodingException {
        Assert.notNull(modelRequest.getId(), "id is null");
        Assert.notNull(modelRequest.getEditor(), "editor is null");
        managementService.executeCommand(new SaveModelEditorCmd("1", modelRequest.getId(), null, null, null, null,
                modelRequest.getEditor().getBytes(StandardCharsets.UTF_8), modelRequest.getTenantId()));
        return Result.ok();
    }

    /**
     * 保存修改流程模型
     *
     * @param modelRequest
     * @return
     * @throws IOException
     */
    @AutoLog(value = "保存修改流程模型")
    @ApiOperation(value = "保存修改流程模型", notes = "保存修改流程模型")
    @PutMapping(value = "/saveOrUpdateModelEditor")
    @Transactional(rollbackFor = Exception.class)
    public Result saveOrUpdateModelEditor(@RequestBody ModelRequest modelRequest) throws UnsupportedEncodingException {
        Assert.notNull(modelRequest.getId(), "id is null");
        Assert.notNull(modelRequest.getEditor(), "editor is null");
        byte[] editor = repositoryService.getModelEditorSource(modelRequest.getId());
        if (editor == null) { //保存
            managementService.executeCommand(new SaveModelEditorCmd(SaveModelEditorCmd.TYPE_1, modelRequest.getId(), null, null, null, null,
                    modelRequest.getEditor().getBytes(StandardCharsets.UTF_8), modelRequest.getTenantId()));
        } else { //修改
            managementService.executeCommand(new SaveModelEditorCmd(SaveModelEditorCmd.TYPE_3, null, null, null, null,
                    null, modelRequest.getEditor().getBytes(StandardCharsets.UTF_8), modelRequest.getTenantId()));
        }
        return Result.ok("保存成功");
    }


    /**
     * 部署流程模型
     *
     * @param modelRequest
     * @return
     */
    @AutoLog(value = "部署流程模型")
    @ApiOperation(value = "部署流程模型", notes = "部署流程模型")
    @PostMapping(value = "/deploy")
    @Transactional(rollbackFor = Exception.class)
    public Result deployModel(@Valid @RequestBody ModelRequest modelRequest) {


        Result result = actNodeService.maintainNodeSetting(modelRequest);
        if (result.getCode() != CommonConstant.SC_OK_200) {
            return result;
        }


//        复制上个版本的抄送配置
        Integer oralversion = modelRequest.getVersion() - 1;
        while (oralversion > 0) {
            List<ActZNotification> nodes = iActZNotificationService.findNodesByModelKeyAndVersion(modelRequest.getKey(), oralversion);
            if (nodes.size() > 0) {
                nodes.forEach(actNode -> {
                    actNode.setId(null);
                    actNode.setProDefVersion(modelRequest.getVersion());
                });
                iActZNotificationService.saveBatch(nodes);
                break;
            } else {
                oralversion--;
            }

        }

        managementService.executeCommand(new DeployModelCmd(modelRequest.getId()));
        return Result.ok();
    }

    /**
     * 导入流程模型
     *
     * @param tenantId
     * @param request
     * @return
     * @throws IOException
     */
    @AutoLog(value = "导入流程模型")
    @ApiOperation(value = "导入流程模型", notes = "导入流程模型")
    @PostMapping(value = "/import")
    @Transactional(rollbackFor = Exception.class)
    public Result doImport(@RequestParam(required = false) String tenantId, HttpServletRequest request) throws IOException {
        if (!(request instanceof MultipartHttpServletRequest)) {
            throw new IllegalArgumentException("request must instance of MultipartHttpServletRequest");
        }
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        if (multipartRequest.getFileMap().size() == 0) {
            throw new IllegalArgumentException("request file is empty");
        }
        MultipartFile file = multipartRequest.getFileMap().values().iterator().next();
        String fileName = file.getOriginalFilename();
        boolean isFileNameInValid =
                ObjectUtils.isEmpty(fileName) || !(fileName.endsWith(".bpmn20.xml") || fileName.endsWith(".bpmn") || fileName.toLowerCase().endsWith(".bar") || fileName.toLowerCase().endsWith(".zip"));
        if (isFileNameInValid) {
            throw new IllegalArgumentException("Request file must end with .bpmn20.xml,.bpmn|,.bar,.zip");
        }

        boolean isBpmnFile = fileName.endsWith(".bpmn20.xml") || fileName.endsWith(".bpmn");
        if (isBpmnFile) {
            managementService.executeCommand(new SaveModelEditorCmd(SaveModelEditorCmd.TYPE_2, null, null, null, null
                    , null, file.getBytes(), tenantId));
        } else if (fileName.toLowerCase().endsWith(FlowableConstant.FILE_EXTENSION_BAR) || fileName.toLowerCase().endsWith(FlowableConstant.FILE_EXTENSION_ZIP)) {
            try {
                ZipInputStream zipInputStream = new ZipInputStream(file.getInputStream());
                ZipEntry entry = zipInputStream.getNextEntry();
                while (entry != null) {
                    if (!entry.isDirectory()) {
                        String entryName = entry.getName();
                        byte[] bytes = IoUtil.readInputStream(zipInputStream, entryName);
                        managementService.executeCommand(new SaveModelEditorCmd(SaveModelEditorCmd.TYPE_2, null, null
                                , null, null, null, bytes, tenantId));
                    }
                    entry = zipInputStream.getNextEntry();
                }
            } catch (Exception e) {
                throw new FlowableException("problem reading zip input stream", e);
            }
        }


        return Result.ok();
    }
    @Autowired
    private ISysBaseAPI sysBaseAPI;

    /**
     * 节点初始化处理人
     *
     * @param nodeVo
     * @param pageNo
     * @param pageSize
     * @return
     */
    @GetMapping(value = "/queryUsersByInitiator")
    public Result queryUsersByInitiator(NodeVo nodeVo,
                                   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(nodeVo.getProcessDefinitionId()).singleResult();
        List<String> users = actNodeService.getNodeUsersByTaskAndVersion(FlowableConstant.INITIATOR, null, processDefinition.getVersion(), processDefinition.getKey());
        return queryUsersByNodeVo(users, nodeVo,pageNo,pageSize,"");
    }

    @Autowired
    ActNodeMapper actNodeMapper;
    /**
     * 根据当前任务id获取下一节点处理人
     *
     * @param nodeVo
     * @param pageNo
     * @param pageSize
     * @return
     */
    @GetMapping(value = "/queryUsersByTaskId")
    public Result<?> queryUsersByTaskId(NodeVo nodeVo,
                                     @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                     @RequestParam(name="pageSize", defaultValue="10") Integer pageSize){
        HistoricTaskInstance task = historyService.createHistoricTaskInstanceQuery().taskId(nodeVo.getTaskId()).singleResult();
        String variables = nodeVo.getVariables();
        Map<String,Object> variablesMap =new HashMap<>();
        if(ObjectUtils.isNotEmpty(variables)){
            variablesMap = JSON.parseObject(variables, Map.class);
        }
        UserTask nextTask =null;
        try {
             nextTask = flowableTaskService.getNextTask(task,variablesMap);
            //当表达式中指定字段值为""报空指针异常 值为null报表达式错误异常
        } catch (PropertyNotFoundException b) {
            return Result.error("因存在条件判断表达式，请完善表单内容。");
        }

        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(task.getProcessDefinitionId()).singleResult();
        //判断流程定义中是否选择了服务商   8代表服务商
        Integer byChooseDepNode = actNodeMapper.findByChooseDepNode(nextTask.getId(), processDefinition.getVersion(), 8);
        Result<?> result=null;
        if (byChooseDepNode >0) {
            List<String> users = commonListenerService.getServiceProviderUsers();
            if (ObjectUtils.isEmpty(users)) {
                return Result.error("未配置服务商，请联系管理员！");
            }
             result = queryUsersByNodeVo(users, nodeVo, pageNo, pageSize,"1");
        }else{
            List<String> users =  actNodeService.getNodeUsersByTaskAndVersion(nextTask.getId(), task.getProcessInstanceId(), processDefinition.getVersion(), processDefinition.getKey());
             result = queryUsersByNodeVo(users, nodeVo, pageNo, pageSize,"");
        }
        return result;

    }


    public Result queryUsersByNodeVo(List<String> users, NodeVo nodeVo,Integer pageNo,Integer pageSize,String type) {
        List<LoginUser> loginUsers = sysBaseAPI.queryUserByNames(users.toArray(new String[0]));
        if (!loginUsers.isEmpty() &&!StringUtils.isEmpty(nodeVo.getRealname())){
            //将loginUsers中Realname进行模糊查询
            loginUsers = loginUsers.stream().filter(user -> user.getRealname().contains(nodeVo.getRealname())).collect(Collectors.toList());
        }

        if (!loginUsers.isEmpty()) {
            //通过loginUsers获取ids
            List<String> ids = loginUsers.stream().map(LoginUser::getId).collect(Collectors.toList());
            //添加部门展示
            Map<String, SysDepartModel> stringSysDepartModelMap = sysBaseAPI.queryDepartsByUserIds(ids);
            loginUsers.forEach(user -> {
                if (stringSysDepartModelMap.containsKey(user.getId())) {
                    user.setOrgCodeTxt(stringSysDepartModelMap.get(user.getId()).getDepartName());
                    user.setOrgCode(stringSysDepartModelMap.get(user.getId()).getId());
                }
            });
            //添加角色展示
            Map<String, LoginUser> roleByUserIdList = sysBaseAPI.getRoleByUserIdList(ids);
            loginUsers.forEach(user -> {
                if (roleByUserIdList.containsKey(user.getId())) {
                    user.setRoleCodes(roleByUserIdList.get(user.getId()).getRoleCodes());
                    user.setRoleNames(roleByUserIdList.get(user.getId()).getRoleNames());
                }
            });
            if ("1".equals(type)) {
                //设置服务商名称展示
                commonListenerService.userSetServiceProviderByUserNames(loginUsers);
            }

        }
        //DepartId查询
        if (!loginUsers.isEmpty() && !StringUtils.isEmpty(nodeVo.getDepartId())) {
            loginUsers = loginUsers.stream()
                    .filter(user ->  !StringUtils.isEmpty(user.getOrgCode()) && user.getOrgCode().contains(nodeVo.getDepartId()))
                    .collect(Collectors.toList());
        }
        //角色id查询
        if (!loginUsers.isEmpty() && !StringUtils.isEmpty(nodeVo.getRoleId())) {
            //包含角色id
            loginUsers = loginUsers.stream().filter(user ->!StringUtils.isEmpty(user.getRoleCodes()) && user.getRoleCodes().contains(nodeVo.getRoleId())).collect(Collectors.toList());
        }

        IPage<LoginUser> loginUserIPage = PageUtil.convertListToIPage(loginUsers, pageNo, pageSize);
        return Result.ok(loginUserIPage);
    }


}
