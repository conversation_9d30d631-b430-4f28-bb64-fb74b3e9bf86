package com.yuanqiao.insight.modules.flowable.mapper;

import com.yuanqiao.insight.modules.flowable.vo.HistoricTaskInstanceEntityImplVo;
import com.yuanqiao.insight.modules.flowable.vo.ServiceProcessVo;
import com.yuanqiao.insight.modules.flowable.vo.query.ProcessInstanceQueryVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.flowable.engine.impl.persistence.entity.HistoricProcessInstanceEntityImpl;

import java.util.List;
@Mapper
public interface HistoricProcessInstanceMapper{

    //这个sql太复杂，应该是看flowable自动生成的请求的debug日志，自己拿来用的。
    List<HistoricProcessInstanceEntityImpl> list(@Param("userName") String userName,
                                                 @Param("title") String title,
                                                 @Param("definitionKey") String definitionKey,
                                                 @Param("classificationKey") String classificationKey,
                                                 @Param("searchClassification") String searchClassification,
                                                 @Param("mustNotIdList") List<String> mustNotIdList,
                                                 @Param("offset") int offset,
                                                 @Param("pageSize") int pageSize);

    Integer getCount(@Param("userName") String userName,
                  @Param("title") String title,
                  @Param("definitionKey") String definitionKey,
                  @Param("classificationKey") String classificationKey,
                  @Param("searchClassification") String searchClassification,
                  @Param("mustNotIdList") List<String> mustNotIdList);


    /**
     * 显示已办结工单
     *
     * @param vo
     * @return
     */
    List<HistoricProcessInstanceEntityImpl> completedInstanceList(String assigneeUser,ProcessInstanceQueryVo vo);
    long getCompletedInstanceCount(String assigneeUser,ProcessInstanceQueryVo vo);

    /**
     * 显示未办结工单
     *
     * @param vo
     * @return
     */
    List<HistoricTaskInstanceEntityImplVo> unCompletedInstanceList(String assigneeUser,ProcessInstanceQueryVo vo);
    long getUnCompletedInstanceCount(String assigneeUser,ProcessInstanceQueryVo vo);

    /**
     * 查询服务实例 根据users
     * @param vo
     * @param userList
     * @return
     */
    List<HistoricProcessInstanceEntityImpl>  getAllProcessInstanceByUsers(ProcessInstanceQueryVo vo, List<String> userList);
    Long getAllProcessInstanceCountByUsers(ProcessInstanceQueryVo vo, List<String> userList);

    List<ServiceProcessVo> getAllServiceProcessVoByUsers(ServiceProcessVo serviceProcessVo, ProcessInstanceQueryVo vo, List<String> userList,@Param("valueByKey1") String valueByKey1);

    Long getAllServiceProcessVoByUsersCount(ServiceProcessVo serviceProcessVo, ProcessInstanceQueryVo vo, List<String> userList,@Param("valueByKey1") String valueByKey1);
}
