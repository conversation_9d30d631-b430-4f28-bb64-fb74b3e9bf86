package com.yuanqiao.insight.modules.flowable.notification.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

/**
 * @Description: act_z_notification
 * @author: yqkj
 * @Date: 2022-07-05
 * @Version: V1.0
 */
@Data
@TableName("act_z_notification")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "act_z_notification对象", description = "act_z_notification")
public class ActZNotification implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "id")
    private Integer id;
    /**
     * 流程设计中节点的id
     */
    @Excel(name = "流程设计中节点的id", width = 15)
    @ApiModelProperty(value = "流程设计中节点的id")
    private java.lang.String nodeId;
    /**
     * 版本
     */
    @Excel(name = "版本", width = 15)
    @ApiModelProperty(value = "版本")
    private java.lang.Integer proDefVersion;
    /**
     * 用户账号 英文模式逗号分割
     */
    @Excel(name = "用户账号 英文模式逗号分割", width = 15)
    @ApiModelProperty(value = "用户账号 英文模式逗号分割")
    private java.lang.String users;

    /**
     * createBy
     */
    @ApiModelProperty(value = "createBy")
    private java.lang.String createBy;
    /**
     * createTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "createTime")
    private java.util.Date createTime;
    /**
     * updateBy
     */
    @ApiModelProperty(value = "updateBy")
    private java.lang.String updateBy;
    /**
     * updateTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "updateTime")
    private java.util.Date updateTime;

    private String modelKey;
}
