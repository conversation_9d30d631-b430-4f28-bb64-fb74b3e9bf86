package com.yuanqiao.insight.modules.flowable.constant;

/**
 * <AUTHOR>
 * @date 2020年3月23日
 */
public class FlowableConstant {
    /**
     * 约定的发起者节点id-taskDefinitionKey
     */
    public final static String INITIATOR = "__initiator__";
    public final static String SPECIAL_GATEWAY_BEGIN_SUFFIX = "_begin";
    public final static String SPECIAL_GATEWAY_END_SUFFIX = "_end";
    public final static String PROCESS_INSTANCE_FORM_DATA = "processInstanceFormData";
    public final static String IDENTITY_USER = "1";
    public final static String IDENTITY_GROUP = "2";
    public final static String ID = "id";
    public final static String CATEGORY = "category";
    public final static String KEY = "key";
    public final static String NAME = "name";
    public final static String VERSION = "version";
    public final static String SUSPENDED = "suspended";
    public final static String LATEST_VERSION = "latestVersion";
    public final static String STARTABLE_BY_USER = "startableByUser";
    public final static String TENANT_ID = "tenantId";
    public final static String PROCESS_INSTANCE_ID = "processInstanceId";
    public final static String PROCESS_INSTANCE_NAME = "processInstanceName";
    public final static String PROCESS_DEFINITION_NAME = "processDefinitionName";
    public final static String PROCESS_DEFINITION_VERSION = "processDefinitionVersion";

    public final static String PROCESS_DEFINITION_KEY = "processDefinitionKey";
    public final static String PROCESS_DEFINITION_ID = "processDefinitionId";
    public final static String BUSINESS_KEY = "businessKey";
    public final static String INVOLVED_USER = "involvedUser";
    public final static String FINISHED = "finished";
    public final static String SUPER_PROCESS_INSTANCE_ID = "superProcessInstanceId";
    public final static String EXCLUDE_SUBPROCESSES = "excludeSubprocesses";
    public final static String FINISHED_AFTER = "finishedAfter";
    public final static String FINISHED_BEFORE = "finishedBefore";
    public final static String STARTED_AFTER = "startedAfter";
    public final static String STARTED_BEFORE = "startedBefore";
    public final static String STARTED_BY = "startedBy";
    public final static String START_BY_ME = "startByMe";
    public final static String CC_TO_ME = "ccToMe";
    public final static String CC = "CC";

    public final static String YY = "YY";
    public final static String TASK_ID = "taskId";
    public final static String TASK_NAME = "taskName";
    public final static String TASK_DESCRIPTION = "taskDescription";
    public final static String TASK_DEFINITION_KEY = "taskDefinitionKey";
    public final static String TASK_ASSIGNEE = "taskAssignee";
    public final static String TASK_OWNER = "taskOwner";
    public final static String TASK_INVOLVED_USER = "taskInvolvedUser";
    public final static String TASK_PRIORITY = "taskPriority";
    public final static String PARENT_TASK_ID = "parentTaskId";
    public final static String DUE_DATE_AFTER = "dueDateAfter";
    public final static String DUE_DATE_BEFORE = "dueDateBefore";
    public final static String TASK_CREATED_BEFORE = "taskCreatedBefore";
    public final static String TASK_CREATED_AFTER = "taskCreatedAfter";
    public final static String TASK_COMPLETED_BEFORE = "taskCompletedBefore";
    public final static String TASK_COMPLETED_AFTER = "taskCompletedAfter";
    public final static String TASK_CANDIDATE_USER = "taskCandidateUser";
    public final static String TASK_CANDIDATE_GROUP = "taskCandidateGroup";
    public final static String TASK_CANDIDATE_GROUPS = "taskCandidateGroups";
    public final static String PROCESS_INSTANCE_BUSINESS_KEY = "processInstanceBusinessKey";
    public final static String PROCESS_FINISHED = "processFinished";
    public final static String DUTY_TODO = "todo";
    public final static String DUTY_TO_DISPATCH = "toDispatch";
    public final static String DUTY_TO_EXAMINE = "toExamine";
    public final static String EXECUTION_ID = "executionId";
    public final static String FILE_EXTENSION_BAR = ".bar";
    public final static String FILE_EXTENSION_ZIP = ".zip";
    public final static String CATEGORY_TODO = "todo";
    public final static String CATEGORY_TO_READ = "toRead";
    public final static String BUTTONS = "buttons";
    public final static String FLOWABLE_NAMESPACE = "http://flowable.org/bpmn";

    //问题类型的pid
    public final static String SERVICE_TYPE_PID = "1573136755129765890";
    //工单业务相关
    public final static String ANSWER_OR_DISPATCH_ID = "Activity_0edlx43";
    public final static String DUTY_NODE_ID_PERSON = "Activity_07uzw6n";
    public final static String DUTY_NODE_ID_ROLE = "Activity_1n9qcds";
    public final static String ORDER_NODE_ID = "Activity_0v32ogk";
    public final static String AUDIT_NODE_ID = "Activity_00nxpbw";

    public final static String PROCESS_START_TIME = "yq_processStartTime";
    public final static String PROCESS_START_USER = "yq_processStartUserId";

    public final static String ORDER_MODEL_KEY = "InternalReportedFault";

    public final static String ROLE_ZBR = "zbr";
    public final static String ROLE_GCS = "gcs";
    public final static String ROLE_SHR = "shr";
    public final static String ROLE_ADMIN = "admin";

    public final static String BUSINESS_TITLE = "yq_business_title";
    /**
     * 状态 待提交申请
     */
    public final static Integer STATUS_TO_APPLY = 0;

    /**
     * 状态 处理中
     */
    public final static Integer STATUS_DEALING = 1;

    /**
     * 状态 处理结束
     */
    public final static Integer STATUS_FINISH = 2;

    /**
     * 状态 已撤回
     */
    public final static Integer STATUS_CANCELED = 3;

    /**
     * 状态 待评价
     */
    public final static Integer STATUS_TOBE_EVALUATED = 4;
    /**
     * 状态 已评价
     */
    public final static Integer STATUS_FINISH_EVALUATED = 5;

    /**
     * 状态 抄送
     */
    public final static Integer STATUS_CC = 6;
    /**
     * 状态 已阅
     */
    public final static Integer STATUS_YY = 7;

    /**
     * 结果 待提交
     */
    public final static Integer RESULT_TO_SUBMIT = 0;

    /**
     * 结果 处理中
     */
    public final static Integer RESULT_DEALING = 1;

    /**
     * 结果 通过
     */
    public final static Integer RESULT_PASS = 2;

    /**
     * 结果 驳回
     */
    public final static Integer RESULT_FAIL = 3;

    /**
     * 结果 撤回
     */
    public final static Integer RESULT_CANCEL = 4;

    public final static String TASK_TODO_TYPE = "1";
    public final static String TASK_DONE_TYPE = "2";
    public final static String TASK_TODO_READ_TYPE = "3";
    /**
     * 工单类型 业务
     */
    public final static String WORK_TYPE_BUSINESS = "business";
    /**
     * 工单类型 运维
     */
    public final static String WORK_TYPE_OPERATION = "operation";
    /**
     * 逾期时间 注意是负数
     */
    public final static Integer OVERDUE_TIME = -24;

    public final static String PROCESS_EXECUTION_STATUS_KEY = "processExecutionStatus";
    public final static String PROCESS_EXECUTION_STATUS_RUN = "1";
    public final static String PROCESS_EXECUTION_STATUS_WAIT_EVALUATE = "2";
    public final static String PROCESS_EXECUTION_STATUS_EVALUATED = "3";

    //流程状态待分配
    public final static String PROCESS_STATUS_TODO = "0";
    //流程状态处理中
    public final static String PROCESS_STATUS_DEALING = "1";
    //流程状态完成
    public final static String PROCESS_STATUS_FINISH = "2";
}
