package com.yuanqiao.insight.modules.flowable.entity;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 流程关联表
 */
@Data
@TableName("act_association")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class ActAssociation implements Serializable {
    /**
     * 主id
     */
    private String primaryId;

    /**
     * 关联Id
     */
    @TableId()
    private String associationId;

    public ActAssociation(String primaryId, String associationId) {
        this.primaryId = primaryId;
        this.associationId = associationId;
    }
}
