package com.yuanqiao.insight.modules.flowable.config;


import org.flowable.engine.TaskService;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName EvaluateConfig
 * @description: TODO
 * @datetime 2023年 09月 22日 16:12
 * @version: 1.0
 */
@Component
public class EvaluateConfig {

    private static ISysBaseAPI iSysBaseAPI;
    private static TaskService taskService;


    @Autowired
    public void setISysBaseAPI(ISysBaseAPI iSysBaseAPI){
        EvaluateConfig.iSysBaseAPI = iSysBaseAPI;
    }
    @Autowired
    public void setTaskService(TaskService taskService){
        EvaluateConfig.taskService = taskService;
    }

    public static TaskService getTaskService(){
        return EvaluateConfig.taskService;
    }
    public static ISysBaseAPI getISysBaseAPI(){
        return EvaluateConfig.iSysBaseAPI;
    }



}
