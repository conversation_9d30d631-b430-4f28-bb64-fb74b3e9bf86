package com.yuanqiao.insight.modules.flowable.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yuanqiao.insight.common.constant.CommonConstant;
import com.yuanqiao.insight.common.util.common.DateUtil;
import com.yuanqiao.insight.common.util.common.LocalCacheUtils;
import com.yuanqiao.insight.modules.flowable.constant.FlowableConstant;
import com.yuanqiao.insight.modules.flowable.entity.YqOpServiceProcessevaluate;
import com.yuanqiao.insight.modules.flowable.service.YqOpServiceProcessevaluateService;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.DateUtils;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 自动评价功能
 */
@Component
@Slf4j
public class AutomaticEvaluationJob implements Job {

    @Autowired
    private YqOpServiceProcessevaluateService evaluateService;


    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        Object parameter = jobExecutionContext.getMergedJobDataMap().get("parameter");
        if (Objects.isNull(parameter)) {
            log.error("获取自动评价参数失败");
            parameter="3";
        }
        log.info("自动评价时间间隔：{}", parameter);
        //获取状态为未评价的评价信息
        LambdaQueryWrapper<YqOpServiceProcessevaluate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(YqOpServiceProcessevaluate::getStatus, "2");
        List<YqOpServiceProcessevaluate> notEvaluate = evaluateService.list(wrapper);
        log.info("未评价的数量：{}", notEvaluate.size());
        for (YqOpServiceProcessevaluate yqOpServiceProcessevaluate : notEvaluate) {
            Date date = DateUtils.addDay(yqOpServiceProcessevaluate.getCreateTime(), Integer.parseInt(parameter.toString()));
            //判断当前时间大于date
             if (date.before(new Date())) {
                 yqOpServiceProcessevaluate.setEvaluator("系统自动评价");
                 yqOpServiceProcessevaluate.setServiceAttitude("非常满意");
                 yqOpServiceProcessevaluate.setCommentContent(parameter+"天内没有评价，系统自动评价");
                 String s = evaluateService.processEvaluate(yqOpServiceProcessevaluate, "系统自动评价");
                 log.info("自动评价结果：{}", Objects.equals(s, "0") ?"成功":"失败");
             }
        }

    }
}
