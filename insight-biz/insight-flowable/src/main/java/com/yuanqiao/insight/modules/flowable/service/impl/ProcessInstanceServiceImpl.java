package com.yuanqiao.insight.modules.flowable.service.impl;

import com.alibaba.fastjson.JSON;
import com.yuanqiao.insight.common.constant.CommonConstant;
import com.yuanqiao.insight.common.core.util.CommonUtil;
import com.yuanqiao.insight.common.core.util.SecurityUtils;
import com.yuanqiao.insight.common.util.common.LocalCacheUtils;
import com.yuanqiao.insight.modules.flowable.common.FlowablePage;
import com.yuanqiao.insight.modules.flowable.common.ResponseFactory;
import com.yuanqiao.insight.modules.flowable.common.cmd.AddCcIdentityLinkCmd;
import com.yuanqiao.insight.modules.flowable.constant.FlowableConstant;
import com.yuanqiao.insight.modules.flowable.controller.TaskController;
import com.yuanqiao.insight.modules.flowable.mapper.FlowableCommonMapper;
import com.yuanqiao.insight.modules.flowable.mapper.HistoricProcessInstanceMapper;
import com.yuanqiao.insight.modules.flowable.service.IActZBusinessService;
import com.yuanqiao.insight.modules.flowable.service.ProcessInstanceService;
import com.yuanqiao.insight.modules.flowable.vo.CategoryVo;
import com.yuanqiao.insight.modules.flowable.vo.ProcessDefinitionVo;
import com.yuanqiao.insight.modules.flowable.vo.ProcessInstanceRequest;
import com.yuanqiao.insight.modules.flowable.vo.ServiceProcessVo;
import com.yuanqiao.insight.modules.flowable.vo.query.ProcessInstanceQueryVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.common.engine.api.FlowableException;
import org.flowable.common.engine.api.FlowableObjectNotFoundException;
import org.flowable.common.engine.impl.identity.Authentication;
import org.flowable.engine.HistoryService;
import org.flowable.engine.ManagementService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.impl.persistence.entity.ExecutionEntity;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.runtime.ProcessInstanceBuilder;
import org.flowable.variable.api.history.HistoricVariableInstance;
import org.flowable.variable.api.history.HistoricVariableInstanceQuery;
import org.jeecg.common.mq.RedisMq;
import org.jeecg.common.mq.stream.Streams;
import org.jeecg.common.system.api.CommonListenerService;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static dm.jdbc.util.DriverUtil.log;

/**
 * <AUTHOR>
 * @date 2020年3月23日
 */
@Slf4j
@Service
public class ProcessInstanceServiceImpl implements ProcessInstanceService {
    @Autowired
    protected ResponseFactory responseFactory;
    @Autowired
    protected ManagementService managementService;
    @Autowired
    protected RuntimeService runtimeService;
    @Autowired
    protected HistoryService historyService;
    @Autowired
    protected PermissionServiceImpl permissionService;
    @Autowired
    protected FlowableTaskServiceImpl flowableTaskService;
    @Autowired
    protected TaskService taskService;
    @Resource
    private FlowableCommonMapper flowableCommonMapper;
    private final static ThreadLocal<SimpleDateFormat> simpleDateFormatThreadLocal = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
    @Resource
    private ISysBaseAPI iSysBaseAPI;

    @Autowired
    CommonListenerService commonListenerService;
    @Autowired
    private HistoricProcessInstanceMapper historicProcessInstanceMapper;

    private final static ExecutorService pools = Executors.newCachedThreadPool();

    @Override
    public ProcessInstance getProcessInstanceById(String processInstanceId) {
        ProcessInstance processInstance =
                runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        if (processInstance == null) {
            throw new FlowableObjectNotFoundException("未找到id为" + processInstanceId + "的流程实例");
        }
        return processInstance;
    }

    @Override
    public HistoricProcessInstance getHistoricProcessInstanceById(String processInstanceId) {
        HistoricProcessInstance historicProcessInstance =
                historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        if (historicProcessInstance == null) {
            throw new FlowableObjectNotFoundException("未找到id为" + processInstanceId + "的流程实例");
        }
        return historicProcessInstance;
    }

    @Override
    public HistoricProcessInstance getHistoricProcessInstanceVariablesById(String processInstanceId) {
        HistoricProcessInstance historicProcessInstance =
                historyService.createHistoricProcessInstanceQuery().includeProcessVariables().processInstanceId(processInstanceId).singleResult();
        if (historicProcessInstance == null) {
            throw new FlowableObjectNotFoundException("未找到id为" + processInstanceId + "的流程实例");
        }
        return historicProcessInstance;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public String start(ProcessInstanceRequest processInstanceRequest) {
        String processDefinitionKey = CommonUtil.trimToEmptyStr(processInstanceRequest.getProcessDefinitionKey());
        String processDefinitionId = CommonUtil.trimToEmptyStr(processInstanceRequest.getProcessDefinitionId());
        if (processDefinitionId.length() == 0 && processDefinitionKey.length() == 0) {
            throw new FlowableException("request param both processDefinitionId and processDefinitionKey is not found");
        } else if (processDefinitionId.length() != 0 && processDefinitionKey.length() != 0) {
            throw new FlowableException("request param both processDefinitionId and processDefinitionKey is found");
        }
//        SecurityUser user = (SecurityUser) SecurityUtils.getUserDetails();
//        String userId = user.getUsername();
        String userId = SecurityUtils.getUser().getUsername();
        String name = SecurityUtils.getUser().getRealname();
        if (processInstanceRequest.getPromoterStatus() != null && processInstanceRequest.getPromoterStatus()) {
            userId = processInstanceRequest.getPromoter();
            name = iSysBaseAPI.getUserByName(userId).getRealname();
        }

        ProcessDefinition definition = permissionService.validateReadPermissionOnProcessDefinition(userId,
                processDefinitionId, processDefinitionKey, processInstanceRequest.getTenantId());
        Map<String, Object> startVariables = null;
        if (processInstanceRequest.getValues() != null && !processInstanceRequest.getValues().isEmpty()) {
            startVariables = processInstanceRequest.getValues();
//            startVariables.put("users", Arrays.asList(((String) startVariables.get("users")).split(",")));
            // 默认设置流程启动人变量 __initiator__
            startVariables.put(FlowableConstant.INITIATOR, userId);
        } else {
            startVariables = new HashMap<>(1);
            // 默认设置流程启动人变量 __initiator__
            startVariables.put(FlowableConstant.INITIATOR, userId);
        }

        Authentication.setAuthenticatedUserId(userId);

        ProcessInstanceBuilder processInstanceBuilder = runtimeService.createProcessInstanceBuilder();
        processInstanceBuilder.processDefinitionId(definition.getId());

        if (StringUtils.isNotBlank(processInstanceRequest.getAssignNextNode())) {
            startVariables.put("assignNextNode", processInstanceRequest.getAssignNextNode());
        }
        startVariables.put(FlowableConstant.BUSINESS_TITLE, "【" + name + "】" + definition.getName());

        // 流程实例标题
        processInstanceBuilder.name("【" + name + "】" + definition.getName());
        // 业务key
        processInstanceBuilder.businessKey(processInstanceRequest.getBusinessKey());
        processInstanceBuilder.variables(startVariables);

        //启动流程
        ProcessInstance instance = processInstanceBuilder.start();
        String processInstanceId = instance.getProcessInstanceId();

        // 处理抄送
        if (CommonUtil.isNotEmptyObject(processInstanceRequest.getCcUserIds())) {
            managementService.executeCommand(new AddCcIdentityLinkCmd(processInstanceId, "", userId, processInstanceRequest.getCcUserIds()));
            // 发送消息通知
            Map<String, String> templateParam = new HashMap<>();
            templateParam.put("process_name", instance.getName());
            templateParam.put("datetime", simpleDateFormatThreadLocal.get().format(new Date()));
            templateParam.put("CC_people", name);

            Object valueByKey = cacheUtils.getValueByKey(CommonConstant.DATA_DICT_KEY + "processNotification_carbonCopy");
            if (valueByKey==null) {
                throw new FlowableException("未配置[processNotification_carbonCopy]转办通知模板参数，请联系管理员");
            }
            iSysBaseAPI.sendNoticeByProcess(templateParam, valueByKey.toString(), Arrays.asList(processInstanceRequest.getCcUserIds()));
        }

//        List<Task> tasks = taskService.createTaskQuery().processInstanceId(processInstanceId).list();
//        for (Task task : tasks) {
//
//            // 约定：发起者节点为 __initiator__ ,则自动完成任务
//            if (FlowableConstant.INITIATOR.equals(task.getTaskDefinitionKey())) {
//                flowableTaskService.addComment(task.getId(), processInstanceId, userId, CommentTypeEnum.TJ, null);
//                if (ObjectUtils.isEmpty(task.getAssignee())) {
//                    taskService.setAssignee(task.getId(), userId);
//                }
//                taskService.complete(task.getId());
//                if (CommonUtil.isNotEmptyObject(processInstanceRequest.getCcUserIds())) {
//                    managementService.executeCommand(new AddCcIdentityLinkCmd(processInstanceId, task.getId(), userId
//                            , processInstanceRequest.getCcUserIds()));
//                }
//            }
//        }
        return processInstanceId;
    }

    LocalCacheUtils cacheUtils = LocalCacheUtils.getInstance();

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String processInstanceId, boolean cascade, String deleteReason, boolean isDelAll) {
        HistoricProcessInstance historicProcessInstance = getHistoricProcessInstanceById(processInstanceId);

        if (historicProcessInstance.getEndTime() != null) {
            historyService.deleteHistoricProcessInstance(historicProcessInstance.getId());
            if (isDelAll) {
                //删除流程实例相关的问题 工单统计表
                commonListenerService.deleteAll(processInstanceId);
            }
            return;
        }
        ProcessInstance processInstance =
                runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();

        if (processInstance != null) {
            ExecutionEntity executionEntity = (ExecutionEntity) processInstance;
            if (CommonUtil.isNotEmptyAfterTrim(executionEntity.getSuperExecutionId())) {
                throw new FlowableException("This is a subprocess");
            }
        }

        runtimeService.deleteProcessInstance(processInstanceId, deleteReason);
        if (cascade) {
            historyService.deleteHistoricProcessInstance(processInstanceId);
        }
        if (isDelAll) {
            //删除流程实例相关的问题 工单统计表
            commonListenerService.deleteAll(processInstanceId);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void activate(String processInstanceId) {
        ProcessInstance processInstance = getProcessInstanceById(processInstanceId);
        if (!processInstance.isSuspended()) {
            throw new FlowableException("Process instance is not suspended with id " + processInstanceId);
        }
        runtimeService.activateProcessInstanceById(processInstance.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void suspend(String processInstanceId) {
        ProcessInstance processInstance = getProcessInstanceById(processInstanceId);
        if (processInstance.isSuspended()) {
            throw new FlowableException("Process instance is already suspended with id {0}" + processInstanceId);
        }
        runtimeService.suspendProcessInstanceById(processInstance.getId());
    }

    @Override
    public List listMyInvolvedSummary(ProcessInstanceQueryVo processInstanceQueryVo) {
        List<ProcessDefinitionVo> vos = flowableCommonMapper.listMyInvolvedSummary(processInstanceQueryVo);
        List<CategoryVo> result = new ArrayList<>();
        Map<String, List<ProcessDefinitionVo>> categorysByParent = new HashMap<>();
        for (ProcessDefinitionVo vo : vos) {
            List<ProcessDefinitionVo> childs = categorysByParent.computeIfAbsent(vo.getCategory(),
                    k -> new ArrayList<>());
            childs.add(vo);
        }
        for (Map.Entry<String, List<ProcessDefinitionVo>> entry : categorysByParent.entrySet()) {
            CategoryVo aCategoryVo = new CategoryVo();
            aCategoryVo.setCategory(entry.getKey());
            aCategoryVo.setProcessDefinitionVoList(entry.getValue());
            String categoryName = entry.getValue().iterator().next().getCategoryName();
            aCategoryVo.setCategoryName(categoryName);
            result.add(aCategoryVo);
        }
        return result;
    }


    /**
     * 获取表单信息
     *
     * @param endTime
     * @param processInstanceId
     * @return
     */
    @Override
    public Map<String, Object> getVariables(Date endTime, String processInstanceId) {
        Map<String, Object> variables = null;
        if (endTime == null) {
            variables = runtimeService.getVariables(processInstanceId);
        } else {
            List<HistoricVariableInstance> hisVals =
                    historyService.createHistoricVariableInstanceQuery().processInstanceId(processInstanceId).list();
            variables = new HashMap<>(16);
            for (HistoricVariableInstance variableInstance : hisVals) {
                variables.put(variableInstance.getVariableName(), variableInstance.getValue());
            }
        }
        return variables;
    }

    @Override
    public Map<String, Object> getVariables(String processInstanceId) {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        Map<String, Object> variables = new HashMap<>();
        HistoricVariableInstanceQuery variableInstanceQuery = historyService.createHistoricVariableInstanceQuery();
        List<HistoricVariableInstance> hisVals = variableInstanceQuery.processInstanceId(processInstanceId).list();
        if (hisVals.size() == 0) {
            variables = processInstance.getProcessVariables();
        } else {
            for (HistoricVariableInstance variableInstance : hisVals) {
                variables.put(variableInstance.getVariableName(), variableInstance.getValue());
            }
        }
        return variables;
    }


    @Autowired
    private RedisMq redisMq;

    @Async("taskExecutor")
    @Override
    public void exportFrom(HttpServletResponse response, ProcessInstanceQueryVo processInstanceQueryVo) {
        List<String> userList = new ArrayList<>();
        if (StringUtils.isNotEmpty(processInstanceQueryVo.getHandlePerson())) {
            userList.add(processInstanceQueryVo.getHandlePerson());
        } else {
            userList = commonListenerService.checkUserIsProvider(SecurityUtils.getUser().getUsername());
        }
        Object valueByKey1 = cacheUtils.getValueByKey(CommonConstant.DATA_DICT_KEY + "ProcessNoHandleTimeOut_processNodeId");
        if (valueByKey1 == null) {
            log.error("未配置[ProcessNoHandleTimeOut_processNodeId]编码参数");
            return;
        }
        List<ServiceProcessVo> allProcessInstanceByUsers = historicProcessInstanceMapper.getAllServiceProcessVoByUsers(null, processInstanceQueryVo, userList,valueByKey1.toString());
        for (ServiceProcessVo allProcessInstanceByUser : allProcessInstanceByUsers) {
            //获取表单信息
            Map<String, Object> variables = getVariables(allProcessInstanceByUser.getServiceEndTime(), allProcessInstanceByUser.getProcessInstanceId());
            //map转换
            ServiceProcessVo serviceProcessVo = JSON.parseObject(JSON.toJSONString(variables), ServiceProcessVo.class);
            serviceProcessVo.setProcessInstanceId(allProcessInstanceByUser.getProcessInstanceId());
            serviceProcessVo.setServiceEndTime(allProcessInstanceByUser.getServiceEndTime());
            serviceProcessVo.setServiceStartTime(allProcessInstanceByUser.getServiceStartTime());
            serviceProcessVo.setEngineerId(allProcessInstanceByUser.getEngineerId());
            //处理预约时间
            if (StringUtils.isNotEmpty(serviceProcessVo.getServiceWorkOrderType())) {
                String reservationTime = "";
                switch (serviceProcessVo.getServiceWorkOrderType()) {
                    case "测试单":
                        if (variables.get("handleTime_detection") != null) {
                            reservationTime = variables.get("handleTime_detection").toString();
                        }
                        break;
                    case "软件单":
                        if (variables.get("handleTime_software") != null) {
                            reservationTime = variables.get("handleTime_software").toString();
                        }
                        break;
                    case "实施单":
                        reservationTime = "";
                        break;
                    case "硬件单":
                        if (variables.get("appointment_hardware") != null) {
                            reservationTime = variables.get("appointment_hardware").toString();
                        }
                        break;
                }
                serviceProcessVo.setReservationTime(reservationTime);
            }
            HistoricVariableInstance historicVariableInstance = historyService.createHistoricVariableInstanceQuery().processInstanceId(allProcessInstanceByUser.getProcessInstanceId()).variableName("isResolved").singleResult();
            if (historicVariableInstance != null) {
                if (!historicVariableInstance.getVariableTypeName().equals("null")) {
                    serviceProcessVo.setFollowUpTime(historicVariableInstance.getLastUpdatedTime());
                }
            }
            if (StringUtils.isNotEmpty(allProcessInstanceByUser.getEngineerId())) {
                serviceProcessVo.setEngineerProvider(
                        commonListenerService.getServiceProviderByUser(allProcessInstanceByUser.getEngineerId())
                );
            }
            if (!StringUtils.isNotEmpty(allProcessInstanceByUser.getContactUserName()) && StringUtils.isNotEmpty(serviceProcessVo.getContactUserId())) {
                LoginUser userByName = iSysBaseAPI.getUserById(serviceProcessVo.getContactUserId());
                serviceProcessVo.setContactUserName(userByName.getRealname());
            }
            if (!StringUtils.isNotEmpty(allProcessInstanceByUser.getUserUnitName()) && StringUtils.isNotEmpty(serviceProcessVo.getUserUnit())) {
                String name = iSysBaseAPI.getDepartNameById(serviceProcessVo.getUserUnit());
                serviceProcessVo.setUserUnitName(name);
            }
            BeanUtils.copyProperties(serviceProcessVo, allProcessInstanceByUser);
        }

        Map<String, Object> map = iSysBaseAPI.saveZipPwd(ServiceProcessVo.class, "服务商工单信息导出", allProcessInstanceByUsers,"服务商工单信息导出");
        map.put("permission", "/flowable/processProvider");
        redisMq.publish(Streams.GENERATE_DOWNLOAD_INFO, JSON.parseObject(JSON.toJSONString(map)));

    }

    @Override
    public List getAllProcessInstanceByUsers(ProcessInstanceQueryVo processInstanceQueryVo, List<String> userList) {
        return historicProcessInstanceMapper.getAllProcessInstanceByUsers(processInstanceQueryVo, userList);
    }

    @Override
    public long getAllProcessInstanceCountByUsers(ProcessInstanceQueryVo processInstanceQueryVo, List<String> userList) {
        return historicProcessInstanceMapper.getAllProcessInstanceCountByUsers(processInstanceQueryVo, userList);
    }

    @Override
    public List<ServiceProcessVo> getServiceProviderWorkOrder(ServiceProcessVo Vo, ProcessInstanceQueryVo processInstanceQueryVo, List<String> serviceProviderUserNames) {
        processInstanceQueryVo.setIsPage(true);
        processInstanceQueryVo.setOffset(((long) (processInstanceQueryVo.getPageNo() - 1) * processInstanceQueryVo.getPageSize()));
        Object valueByKey1 =  cacheUtils.getValueByKey(CommonConstant.DATA_DICT_KEY + "ProcessNoHandleTimeOut_processNodeId");
        if (valueByKey1 == null) {
            log.error("未配置[ProcessNoHandleTimeOut_processNodeId]编码参数");
            return new ArrayList<>();
        }
        List<ServiceProcessVo> allProcessInstanceByUsers = historicProcessInstanceMapper.getAllServiceProcessVoByUsers(Vo, processInstanceQueryVo, serviceProviderUserNames,valueByKey1.toString());
        for (ServiceProcessVo allProcessInstanceByUser : allProcessInstanceByUsers) {
            //获取表单信息
            Map<String, Object> variables = getVariables(allProcessInstanceByUser.getServiceEndTime(), allProcessInstanceByUser.getProcessInstanceId());
            //map转换
            ServiceProcessVo serviceProcessVo = JSON.parseObject(JSON.toJSONString(variables), ServiceProcessVo.class);
            serviceProcessVo.setProcessInstanceId(allProcessInstanceByUser.getProcessInstanceId());
            serviceProcessVo.setServiceEndTime(allProcessInstanceByUser.getServiceEndTime());
            serviceProcessVo.setServiceStartTime(allProcessInstanceByUser.getServiceStartTime());
            serviceProcessVo.setEngineerId(allProcessInstanceByUser.getEngineerId());
            serviceProcessVo.setQuestionStatus(allProcessInstanceByUser.getQuestionStatus());
            serviceProcessVo.setServiceAttitude(allProcessInstanceByUser.getServiceAttitude());
            serviceProcessVo.setEvaluateContent(allProcessInstanceByUser.getEvaluateContent());
            //处理预约时间
            if (StringUtils.isNotEmpty(serviceProcessVo.getServiceWorkOrderType())) {
                String reservationTime = "";
                switch (serviceProcessVo.getServiceWorkOrderType()) {
                    case "测试单":
                        if (variables.get("handleTime_detection") != null) {
                            reservationTime = variables.get("handleTime_detection").toString();
                        }
                        break;
                    case "软件单":
                        if (variables.get("handleTime_software") != null) {
                            reservationTime = variables.get("handleTime_software").toString();
                        }
                        break;
                    case "实施单":
                        reservationTime = "";
                        break;
                    case "硬件单":
                        if (variables.get("appointment_hardware") != null) {
                            reservationTime = variables.get("appointment_hardware").toString();
                        }
                        break;
                }
                serviceProcessVo.setReservationTime(reservationTime);
            }
            HistoricVariableInstance historicVariableInstance = historyService.createHistoricVariableInstanceQuery().processInstanceId(allProcessInstanceByUser.getProcessInstanceId()).variableName("isResolved").singleResult();
            if (historicVariableInstance != null) {
                if (!historicVariableInstance.getVariableTypeName().equals("null")) {
                    serviceProcessVo.setFollowUpTime(historicVariableInstance.getLastUpdatedTime());
                }
            }
            if (StringUtils.isNotEmpty(allProcessInstanceByUser.getEngineerId())) {
                serviceProcessVo.setEngineerProvider(
                        commonListenerService.getServiceProviderByUser(allProcessInstanceByUser.getEngineerId())
                );
            }
            BeanUtils.copyProperties(serviceProcessVo, allProcessInstanceByUser);
        }
        return allProcessInstanceByUsers;
    }

    @Override
    public Long getServiceProviderWorkOrderCount(ServiceProcessVo Vo, ProcessInstanceQueryVo processInstanceQueryVo, List<String> serviceProviderUserNames) {
        processInstanceQueryVo.setIsPage(false);
        Object valueByKey1 = cacheUtils.getValueByKey(CommonConstant.DATA_DICT_KEY + "ProcessNoHandleTimeOut_processNodeId");
        if (valueByKey1 == null) {
            log.error("未配置[ProcessNoHandleTimeOut_processNodeId]编码参数");
            return 0L;
        }
        return historicProcessInstanceMapper.getAllServiceProcessVoByUsersCount(Vo, processInstanceQueryVo, serviceProviderUserNames,valueByKey1.toString());
    }

    @Override
    @Async("taskExecutor")
    public void exportServiceProviderWorkOrder(ServiceProcessVo vo, ProcessInstanceQueryVo processInstanceQueryVo, List<String> serviceProviderUserNames) {
        processInstanceQueryVo.setIsPage(false);
        Object valueByKey1 = cacheUtils.getValueByKey(CommonConstant.DATA_DICT_KEY + "ProcessNoHandleTimeOut_processNodeId");
        if (valueByKey1 == null) {
            log.error("未配置[ProcessNoHandleTimeOut_processNodeId]编码参数");
            return;
        }
        List<ServiceProcessVo> allProcessInstanceByUsers = historicProcessInstanceMapper.getAllServiceProcessVoByUsers(vo, processInstanceQueryVo, serviceProviderUserNames,valueByKey1.toString());
        for (ServiceProcessVo allProcessInstanceByUser : allProcessInstanceByUsers) {
            // 获取表单信息
            Map<String, Object> variables = getVariables(allProcessInstanceByUser.getServiceEndTime(), allProcessInstanceByUser.getProcessInstanceId());
            ServiceProcessVo serviceProcessVo = JSON.parseObject(JSON.toJSONString(variables), ServiceProcessVo.class);
            serviceProcessVo.setProcessInstanceId(allProcessInstanceByUser.getProcessInstanceId());
            serviceProcessVo.setServiceEndTime(allProcessInstanceByUser.getServiceEndTime());
            serviceProcessVo.setServiceStartTime(allProcessInstanceByUser.getServiceStartTime());
            serviceProcessVo.setEngineerId(allProcessInstanceByUser.getEngineerId());
            serviceProcessVo.setQuestionStatus(allProcessInstanceByUser.getQuestionStatus());
            serviceProcessVo.setServiceAttitude(allProcessInstanceByUser.getServiceAttitude());
            serviceProcessVo.setEvaluateContent(allProcessInstanceByUser.getEvaluateContent());
            if (StringUtils.isNotEmpty(serviceProcessVo.getServiceWorkOrderType())) {
                String reservationTime = "";
                switch (serviceProcessVo.getServiceWorkOrderType()) {
                    case "测试单":
                        if (variables.get("handleTime_detection") != null) {
                            reservationTime = variables.get("handleTime_detection").toString();
                        }
                        break;
                    case "软件单":
                        if (variables.get("handleTime_software") != null) {
                            reservationTime = variables.get("handleTime_software").toString();
                        }
                        break;
                    case "实施单":
                        reservationTime = "";
                        break;
                    case "硬件单":
                        if (variables.get("appointment_hardware") != null) {
                            reservationTime = variables.get("appointment_hardware").toString();
                        }
                        break;
                }
                serviceProcessVo.setReservationTime(reservationTime);
            }
            HistoricVariableInstance historicVariableInstance = historyService.createHistoricVariableInstanceQuery()
                    .processInstanceId(allProcessInstanceByUser.getProcessInstanceId())
                    .variableName("isResolved")
                    .singleResult();
            if (historicVariableInstance != null) {
                if (!"null".equals(historicVariableInstance.getVariableTypeName())) {
                    serviceProcessVo.setFollowUpTime(historicVariableInstance.getLastUpdatedTime());
                }
            }
            if (StringUtils.isNotEmpty(allProcessInstanceByUser.getEngineerId())) {
                serviceProcessVo.setEngineerProvider(commonListenerService.getServiceProviderByUser(allProcessInstanceByUser.getEngineerId()));
            }
            BeanUtils.copyProperties(serviceProcessVo, allProcessInstanceByUser);
        }

        // 异步任务
        pools.execute(() -> {
            try {
                Map<String, Object> map = iSysBaseAPI.saveZipPwd(ServiceProcessVo.class, "服务商工单信息导出", allProcessInstanceByUsers,"服务商工单信息导出");
                map.put("permission", "/flowable/order-statistics/orderStatisticsManage");
                redisMq.publish(Streams.GENERATE_DOWNLOAD_INFO, JSON.parseObject(JSON.toJSONString(map)));
            } catch (Exception e) {
                log.error("导出服务商工单信息失败", e);
            }
        });
    }


}
