package com.yuanqiao.insight.modules.flowable.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.ImmutableMap;
import com.yuanqiao.insight.common.core.util.CommonUtil;
import com.yuanqiao.insight.common.core.util.DateUtil;
import com.yuanqiao.insight.common.core.util.ObjectUtils;
import com.yuanqiao.insight.common.core.util.SecurityUtils;
import com.yuanqiao.insight.modules.flowable.common.BaseFlowableController;
import com.yuanqiao.insight.modules.flowable.common.CommentTypeEnum;
import com.yuanqiao.insight.modules.flowable.common.FlowablePage;
import com.yuanqiao.insight.modules.flowable.constant.FlowableConstant;
import com.yuanqiao.insight.modules.flowable.entity.ActAssociation;
import com.yuanqiao.insight.modules.flowable.mapper.HistoricProcessInstanceMapper;
import com.yuanqiao.insight.modules.flowable.mapper.HistoricTaskMapper;
import com.yuanqiao.insight.modules.flowable.service.ActAssociationService;
import com.yuanqiao.insight.modules.flowable.service.ProcessInstanceService;
import com.yuanqiao.insight.modules.flowable.service.YqOpServiceProcessevaluateService;
import com.yuanqiao.insight.modules.flowable.util.OverdueTimeUtils;
import com.yuanqiao.insight.modules.flowable.vo.HistoricTaskInstanceEntityImplVo;
import com.yuanqiao.insight.modules.flowable.vo.ProcessInstanceDetailResponse;
import com.yuanqiao.insight.modules.flowable.vo.ProcessInstanceRequest;
import com.yuanqiao.insight.modules.flowable.vo.ServiceProcessVo;
import com.yuanqiao.insight.modules.flowable.vo.query.ProcessInstanceQueryVo;
import com.yuanqiao.insight.modules.flowable.wapper.CommentListWrapper;
import com.yuanqiao.insight.modules.flowable.wapper.IListWrapper;
import com.yuanqiao.insight.modules.flowable.wapper.ProcInsListWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.flowable.common.engine.api.FlowableObjectNotFoundException;
import org.flowable.common.engine.api.query.QueryProperty;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.engine.impl.HistoricProcessInstanceQueryProperty;
import org.flowable.engine.impl.ProcessDefinitionQueryProperty;
import org.flowable.engine.impl.persistence.entity.HistoricProcessInstanceEntityImpl;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.repository.ProcessDefinitionQuery;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.task.Comment;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.api.CommonListenerService;
import org.jeecg.common.system.api.IEquipmentPlanAPI;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.DateUtils;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 流程实例查询接口
 * @date 2020年3月23日
 */
@Api(tags = "流程实例")
@RestController
@RequestMapping("/flowable/processInstance")
public class ProcessInstanceController extends BaseFlowableController {

    private static final Map<String, QueryProperty> allowedSortProperties = new HashMap<>();

    static {
        allowedSortProperties.put(FlowableConstant.ID, HistoricProcessInstanceQueryProperty.PROCESS_INSTANCE_ID_);
        allowedSortProperties.put(FlowableConstant.PROCESS_DEFINITION_ID,
                HistoricProcessInstanceQueryProperty.PROCESS_DEFINITION_ID);
        allowedSortProperties.put(FlowableConstant.PROCESS_DEFINITION_KEY,
                HistoricProcessInstanceQueryProperty.PROCESS_DEFINITION_KEY);
        allowedSortProperties.put(FlowableConstant.BUSINESS_KEY, HistoricProcessInstanceQueryProperty.BUSINESS_KEY);
        allowedSortProperties.put("startTime", HistoricProcessInstanceQueryProperty.START_TIME);
        allowedSortProperties.put("endTime", HistoricProcessInstanceQueryProperty.END_TIME);
        allowedSortProperties.put("duration", HistoricProcessInstanceQueryProperty.DURATION);
        allowedSortProperties.put(FlowableConstant.TENANT_ID, HistoricProcessInstanceQueryProperty.TENANT_ID);
        allowedSortProperties.put(FlowableConstant.NAME, new HistoricProcessInstanceQueryProperty("RES.NAME_"));
        allowedSortProperties.put(FlowableConstant.PROCESS_DEFINITION_VERSION, new ProcessDefinitionQueryProperty("DEF.VERSION_"));
        allowedSortProperties.put(FlowableConstant.PROCESS_DEFINITION_NAME, new ProcessDefinitionQueryProperty("DEF.NAME_"));

    }

    @Autowired
    private HistoricProcessInstanceMapper  historicProcessInstanceMapper;
    @Autowired
    private ProcessInstanceService processInstanceService;
    @Autowired
    private ISysBaseAPI iSysBaseAPI;


    private HistoricProcessInstanceQuery createQuery(ProcessInstanceQueryVo processInstanceQueryVo) {
        HistoricProcessInstanceQuery query = historyService.createHistoricProcessInstanceQuery();
        if (ObjectUtils.isNotEmpty(processInstanceQueryVo.getTitle())){
            query.variableValueLike("title","%"+processInstanceQueryVo.getTitle()+"%");
        }

        if (CommonUtil.isNotEmptyStr(processInstanceQueryVo.getCurrTaskName())) {
            Set<String> ids = taskService.createTaskQuery().taskNameLike(ObjectUtils.convertToLike(processInstanceQueryVo.getCurrTaskName())).list().stream().map(task -> task.getProcessInstanceId()).collect(Collectors.toSet());
            if (!ids.isEmpty()) {
                query.processInstanceIds(ids);
            }
        }
        if (CommonUtil.isNotEmptyAfterTrim(processInstanceQueryVo.getUserUnit())) {
            query.variableValueEquals("userUnit",processInstanceQueryVo.getUserUnit());
        }
        if (CommonUtil.isNotEmptyAfterTrim(processInstanceQueryVo.getContactUserId())) {
            query.variableValueEquals("contactUserId",processInstanceQueryVo.getContactUserId());
        }
        if (CommonUtil.isNotEmptyAfterTrim(processInstanceQueryVo.getProcessDefinitionCategory())) {
            query.processDefinitionCategory(processInstanceQueryVo.getProcessDefinitionCategory());
        }
        if (CommonUtil.isNotEmptyAfterTrim(processInstanceQueryVo.getProcessInstanceId())) {
            query.processInstanceId(processInstanceQueryVo.getProcessInstanceId());
        }
        if (CommonUtil.isNotEmptyAfterTrim(processInstanceQueryVo.getProcessInstanceName())) {
            query.processInstanceNameLike(ObjectUtils.convertToLike(processInstanceQueryVo.getProcessInstanceName()));
        }
        if (CommonUtil.isNotEmptyAfterTrim(processInstanceQueryVo.getProcessDefinitionName())) {
            query.processDefinitionName(processInstanceQueryVo.getProcessDefinitionName());
        }
        if (CommonUtil.isNotEmptyAfterTrim(processInstanceQueryVo.getProcessDefinitionKey())) {
            query.processDefinitionKeyIn(Arrays.asList(processInstanceQueryVo.getProcessDefinitionKey().split(",")));
        }
        if (CommonUtil.isNotEmptyAfterTrim(processInstanceQueryVo.getProcessDefinitionId())) {
            query.processDefinitionId(processInstanceQueryVo.getProcessDefinitionId());
        }
        if (CommonUtil.isNotEmptyAfterTrim(processInstanceQueryVo.getBusinessKey())) {
            query.processInstanceBusinessKey(processInstanceQueryVo.getBusinessKey());
        }
        if (CommonUtil.isNotEmptyAfterTrim(processInstanceQueryVo.getInvolvedUser())) {
            query.involvedUser(processInstanceQueryVo.getInvolvedUser());
        }
        if (CommonUtil.isNotEmptyAfterTrim(processInstanceQueryVo.getStatus())) {
            String status = processInstanceQueryVo.getStatus();
            if (status.equals("1")) {
                query.unfinished();
            } else if (status.equals("2")) {
                query.finished();
                query.notDeleted();
            } else if (status.equals("3")) {
                query.deleted();
            }

        }
        if (!processInstanceQueryVo.getFinished().equals(processInstanceQueryVo.getUnfinished())) {
            if (processInstanceQueryVo.getFinished()) {
                query.finished();
            }
            if (processInstanceQueryVo.getUnfinished()) {
                query.unfinished();
            }
        }

        if (CommonUtil.isNotEmptyAfterTrim(processInstanceQueryVo.getSuperProcessInstanceId())) {
            query.superProcessInstanceId(processInstanceQueryVo.getSuperProcessInstanceId());
        }
        if (processInstanceQueryVo.getExcludeSubprocesses()) {
            query.excludeSubprocesses(processInstanceQueryVo.getExcludeSubprocesses());
        }
        if (CommonUtil.isNotEmptyAfterTrim(processInstanceQueryVo.getFinishedAfter())) {
            query.finishedAfter(ObjectUtils.convertToDatetime(processInstanceQueryVo.getFinishedAfter()));
        }
        if (CommonUtil.isNotEmptyAfterTrim(processInstanceQueryVo.getFinishedBefore())) {
            query.finishedBefore(ObjectUtils.convertToDatetime(processInstanceQueryVo.getFinishedBefore()));
        }
        if (CommonUtil.isNotEmptyAfterTrim(processInstanceQueryVo.getStartedAfter())) {
            query.startedAfter(ObjectUtils.convertToDatetime(processInstanceQueryVo.getStartedAfter()));
        }
        if (CommonUtil.isNotEmptyAfterTrim(processInstanceQueryVo.getStartedBefore())) {
            query.startedBefore(ObjectUtils.convertToDatetime(processInstanceQueryVo.getStartedBefore()));
        }
        if (CommonUtil.isNotEmptyAfterTrim(processInstanceQueryVo.getStartUserId())) {
            query.startedBy(processInstanceQueryVo.getStartUserId());
        }
        // startByMe 覆盖 startedBy
        if (processInstanceQueryVo.getStartedByMe()) {
            query.startedBy(SecurityUtils.getUser().getUsername());
        }
        // ccToMe 抄送我
        if (processInstanceQueryVo.getCcToMe()) {
            query.involvedUser(SecurityUtils.getUser().getUsername(), FlowableConstant.CC);
        }
        if (CommonUtil.isNotEmptyAfterTrim(processInstanceQueryVo.getTenantId())) {
            query.processInstanceTenantIdLike(processInstanceQueryVo.getTenantId());
        }
        return query;
    }

    //@PreAuthorize("@elp.single('flowable:processInstance:list')")
    @ApiOperation(value = "流程实例-分页列表查询", notes = "流程实例-分页列表查询")
    @GetMapping(value = "/list")
    public Result list(ProcessInstanceQueryVo processInstanceQueryVo) {
        HistoricProcessInstanceQuery query = createQuery(processInstanceQueryVo);

        FlowablePage page = this.pageList(processInstanceQueryVo, query, ProcInsListWrapper.class, allowedSortProperties,
                HistoricProcessInstanceQueryProperty.START_TIME);
        return Result.ok(page);
    }


    //逾期
    @GetMapping(value = "/countOverdue")
    public Result countOverdue(ProcessInstanceQueryVo processInstanceQueryVo) throws ClassNotFoundException, NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        String overdueTimeStr = OverdueTimeUtils.getOverdueTimeStr();
        processInstanceQueryVo.setStartedBefore(overdueTimeStr);
        processInstanceQueryVo.setUnfinished(true);
        HistoricProcessInstanceQuery query = createQuery(processInstanceQueryVo);
        long count = query.count();
        Map<String, Long> map = ImmutableMap.<String, Long>builder()
                .put("count", count).build();
        return Result.ok(map);
    }

    //todo 改为count直接返回
    @GetMapping(value = "/countComplaint")
    public Result countComplaint(ProcessInstanceQueryVo processInstanceQueryVo) {
        int count = taskService.getCommentsByType(CommentTypeEnum.TS.toString()).size();
        Map<String, Integer> map = ImmutableMap.<String, Integer>builder()
                .put("count", count).build();
        return Result.ok(map);
    }

    @GetMapping(value = "/countUrge")
    public Result countUrge(ProcessInstanceQueryVo processInstanceQueryVo) {
        int count = taskService.getCommentsByType(CommentTypeEnum.CB.toString()).size();
        Map<String, Integer> map = ImmutableMap.<String, Integer>builder()
                .put("count", count).build();
        return Result.ok(map);
    }

    //按时间统计（今日、本周、本月）
    @GetMapping(value = "/countByTime")
    public Result countByTime(@RequestParam String startTime, @RequestParam String endTime) {
        ProcessInstanceQueryVo vo = new ProcessInstanceQueryVo();
        vo.setStartedAfter(startTime);
        vo.setStartedBefore(endTime);
        HistoricProcessInstanceQuery query = createQuery(vo);
        long count = query.count();
        Map<String, Long> map = ImmutableMap.<String, Long>builder()
                .put("count", count).build();
        return Result.ok(map);
    }


    //按是否完成统计
    @GetMapping(value = "/countFinished")
    public Result countFinish(@RequestParam Boolean isFinished) {
        ProcessInstanceQueryVo vo = new ProcessInstanceQueryVo();
        if (isFinished) {
            vo.setFinished(true);
        } else {
            vo.setUnfinished(true);
        }
        HistoricProcessInstanceQuery query = createQuery(vo);
        long count = query.count();
        Map<String, Long> map = ImmutableMap.<String, Long>builder()
                .put("count", count).build();
        return Result.ok(map);
    }

    //按任务类型统计(终端业务、证书业务、网络业务)
    @GetMapping(value = "/countByTaskType")
    public Result countByTaskType(@RequestParam String businessInfoId) {
        HistoricProcessInstanceQuery query = historyService.createHistoricProcessInstanceQuery();
        query.processInstanceTenantId(businessInfoId);
        long count = query.count();
        Map<String, Long> map = ImmutableMap.<String, Long>builder()
                .put("count", count).build();
        return Result.ok(map);
    }

    @GetMapping(value = "/countByMonth")
    public Result countByMonth() {
        ProcessDefinitionQuery query = repositoryService.createProcessDefinitionQuery();
        query.processDefinitionCategory("business");
//        List<String> businessIds = query.list().stream().map(ProcessDefinition::getId).collect(Collectors.toList());
        List<String> businessProcdefKeys = query.list().stream().map(ProcessDefinition::getKey).distinct().collect(Collectors.toList());

        query.processDefinitionCategory("operation");
//        List<String> operationIds = query.list().stream().map(ProcessDefinition::getId).collect(Collectors.toList());
        List<String> operationProcdefKeys = query.list().stream().map(ProcessDefinition::getKey).distinct().collect(Collectors.toList());

        HistoricProcessInstanceQuery businessQuery = historyService.createHistoricProcessInstanceQuery();

        HistoricProcessInstanceQuery operationQuery = historyService.createHistoricProcessInstanceQuery();

        int yearNumber = Calendar.getInstance().get(Calendar.YEAR);
        int currentMonth = Calendar.getInstance().get(Calendar.MONTH);
        String[] startTimeAndEndTime;
        Map<String, List<Long>> resultMap = new HashMap<>();
        List<Long> businessCount = new ArrayList<>();
        List<Long> operationCount = new ArrayList<>();
        for (int i = 0; i < 12 && i <= currentMonth; i++) {
            startTimeAndEndTime = DateUtil.getMonthStartTimeAndEndTime(yearNumber, i);
            businessQuery.processDefinitionKeyIn(businessProcdefKeys);
            businessQuery.startedAfter(ObjectUtils.convertToDatetime(startTimeAndEndTime[0]));
            businessQuery.startedBefore(ObjectUtils.convertToDatetime(startTimeAndEndTime[1]));
            businessCount.add(businessQuery.count());

            operationQuery.processDefinitionKeyIn(operationProcdefKeys);
//            operationQuery.processInstanceIds(new HashSet<>(operationIds));
            operationQuery.startedAfter(ObjectUtils.convertToDatetime(startTimeAndEndTime[0]));
            operationQuery.startedBefore(ObjectUtils.convertToDatetime(startTimeAndEndTime[1]));
            operationCount.add(operationQuery.count());
        }
        resultMap.put("business", businessCount);
        resultMap.put("operation", operationCount);
        return Result.ok(resultMap);
    }

    @GetMapping(value = "/listMyInvolvedSummary")
    public Result listMyInvolvedSummary(ProcessInstanceQueryVo processInstanceQueryVo) {
        processInstanceQueryVo.setUserId(SecurityUtils.getUser().getUsername());
        return Result.ok(this.processInstanceService.listMyInvolvedSummary(processInstanceQueryVo));
    }

    @GetMapping(value = "/listMyInvolved")
    public Result listMyInvolved(ProcessInstanceQueryVo processInstanceQueryVo) {
        processInstanceQueryVo.setInvolvedUser(SecurityUtils.getUser().getUsername());
        return list(processInstanceQueryVo);
    }

    /**
     * 我的申请
     *
     * @param processInstanceQueryVo
     * @return
     */
    @GetMapping(value = "/listStartedByMe")
    public Result listStartedByMe(ProcessInstanceQueryVo processInstanceQueryVo) {
        processInstanceQueryVo.setStartedByMe(true);
        return list(processInstanceQueryVo);
    }

    @Autowired
    HistoricTaskMapper historicTaskMapper;

    /**
     * 我的待阅
     *
     * @param processInstanceQueryVo
     * @return
     */
    @GetMapping(value = "/listCcToMe")
    public Result listCcYyToMe(ProcessInstanceQueryVo processInstanceQueryVo) {
        FlowablePage flowablePage = new FlowablePage();
        processInstanceQueryVo.setPageNo(processInstanceQueryVo.getPageNo()-1);
        flowablePage.setCurrent(processInstanceQueryVo.getPageNo());
        flowablePage.setSize(processInstanceQueryVo.getPageSize());
        List list =null;
        if (StringUtils.isNotEmpty(processInstanceQueryVo.getKeyword())){
             list =historicTaskMapper.listCcYyToMe(SecurityUtils.getUser().getUsername(),processInstanceQueryVo);
             list =searchProcessInstanceEsQuery(list, flowablePage, processInstanceQueryVo);
        }else{

            processInstanceQueryVo.setOffset(flowablePage.getOffset());
            list =historicTaskMapper.listCcYyToMePage(SecurityUtils.getUser().getUsername(),processInstanceQueryVo);
            flowablePage.setTotal(historicTaskMapper.countCcYyToMe(SecurityUtils.getUser().getUsername(),processInstanceQueryVo));
        }

        IListWrapper listWrapper = SpringContextUtils.getBean(ProcInsListWrapper.class);
        list = listWrapper.execute(list);//填充、包装数据
        flowablePage.setRecords(list);
        return Result.ok(flowablePage);
    }

    /**
     * 待阅查看功能   将待办修改为待阅
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/updateStatue")
    public Result updateStatue(@RequestParam(name = "id", required = true)String id) {
        String userName = SecurityUtils.getUser().getUsername();
        historicTaskMapper.updateIdentityType(id,FlowableConstant.YY,userName);
        return Result.ok();
    }

    @ApiOperation(value = "流程实例-查看详情", notes = "流程实例-查看详情")
    @AutoLog(value = "查询流程实例详情")
    @GetMapping(value = "/queryById")
    public Result queryById(@RequestParam String processInstanceId) {
        permissionService.validateReadPermissionOnProcessInstance(SecurityUtils.getUser().getUsername(), processInstanceId);
        ProcessInstance processInstance = null;
        HistoricProcessInstance historicProcessInstance =
                processInstanceService.getHistoricProcessInstanceById(processInstanceId);
        if (historicProcessInstance.getEndTime() == null) {
            processInstance = processInstanceService.getProcessInstanceById(processInstanceId);
        }
        ProcessInstanceDetailResponse pidr =
                responseFactory.createProcessInstanceDetailResponse(historicProcessInstance, processInstance);
        return Result.ok(pidr);
    }

    //@Log(value = "启动流程实例")
    @ApiOperation(value = "流程实例-启动流程实例", notes = "流程实例-启动流程实例")
    @AutoLog(value = "启动流程实例")
    @PostMapping(value = "/start")
    @Transactional(rollbackFor = Exception.class)
    public Result start(@RequestBody ProcessInstanceRequest processInstanceRequest) {
        processInstanceService.start(processInstanceRequest);
        return Result.ok("启动成功");
    }

    //@Log(value = "删除流程实例")
    @ApiOperation(value = "流程实例-删除流程实例", notes = "流程实例-删除流程实例")
    @AutoLog(value = "删除流程实例")
    //@PreAuthorize("@elp.single('flowable:processInstance:delete')")
    @DeleteMapping(value = "/delete")
    public Result delete(@RequestParam String processInstanceId, @RequestParam(required = false) boolean cascade,
                         @RequestParam(required = false) String deleteReason) {
        processInstanceService.delete(processInstanceId, cascade, deleteReason, true);
        return Result.ok("删除成功");
    }

    //@Log(value = "挂起流程实例")
    //@PreAuthorize("@elp.single('flowable:processInstance:suspendOrActivate')")
    @ApiOperation(value = "流程实例-挂起流程实例", notes = "流程实例-挂起流程实例")
    @AutoLog(value = "挂起流程实例")
    @PutMapping(value = "/suspend")
    public Result suspend(@RequestBody ProcessInstanceRequest processInstanceRequest) {
        processInstanceService.suspend(processInstanceRequest.getProcessInstanceId());
        return Result.ok("冻结成功");
    }

    //@Log(value = "激活流程实例")
    //@PreAuthorize("@elp.single('flowable:processInstance:suspendOrActivate')")
    @ApiOperation(value = "流程实例-激活流程实例", notes = "流程实例-激活流程实例")
    @AutoLog(value = "激活流程实例")
    @PutMapping(value = "/activate")
    public Result activate(@RequestBody ProcessInstanceRequest processInstanceRequest) {
        processInstanceService.activate(processInstanceRequest.getProcessInstanceId());
        return Result.ok("解冻成功");
    }

    @GetMapping(value = "/comments")
    public Result comments(@RequestParam String processInstanceId) {
        permissionService.validateReadPermissionOnProcessInstance(SecurityUtils.getUser().getUsername(), processInstanceId);
        List<Comment> datas = taskService.getProcessInstanceComments(processInstanceId);
        Collections.reverse(datas);
        return Result.ok(this.listWrapper(CommentListWrapper.class, datas));
    }

    @Autowired
    private IEquipmentPlanAPI iEquipmentPlanApi;

    @GetMapping(value = "/formData")
    public Result formData(@RequestParam String processInstanceId,@RequestParam(required = false) String type) {
        if (type!=null&&"read".equals(type)){
            String userName = SecurityUtils.getUser().getUsername();
            historicTaskMapper.updateIdentityType(processInstanceId,FlowableConstant.YY,userName);
        }
        HistoricProcessInstance processInstance =
                permissionService.validateReadPermissionOnProcessInstance(SecurityUtils.getUser().getUsername(), processInstanceId);
        Object renderedStartForm = null;
        if ("equipment".equals(processInstance.getProcessDefinitionKey())) {
            Map<String, Object> map = processInstanceService.getVariables(processInstanceId);
            String equipmentPlanId = map.get("equipmentPlanId").toString();
            //String equipmentPlanId = (String) runtimeService.getVariable(processInstanceId, "equipmentPlanId");
            if (StringUtils.isNotEmpty(equipmentPlanId)) {
                String equipmentPlanConfig = iEquipmentPlanApi.getEquipmentPlanConfig(equipmentPlanId);
                renderedStartForm = equipmentPlanConfig;
            }
        } else {

            renderedStartForm = formService.getRenderedStartForm(processInstance.getProcessDefinitionId());
        }
        Map<String, Object> variables = processInstanceService.getVariables(processInstance.getEndTime(), processInstanceId);
        Map<String, Object> ret = new HashMap<String, Object>(5);
        ret.put("processDefinitionKey", processInstance.getProcessDefinitionKey());
        boolean showBusinessKey = isShowBusinessKey(processInstance.getProcessDefinitionId());
        ret.put("showBusinessKey", showBusinessKey);
        ret.put(FlowableConstant.BUSINESS_KEY, processInstance.getBusinessKey());
        ret.put("renderedStartForm", renderedStartForm);
        ret.put("variables", variables);
        return Result.ok(ret);
    }



    @Autowired
    protected ActAssociationService actAssociationService;

    /**
     * 获取子流程相关详细信息
     *
     * @param processInstanceQueryVo
     * @return
     */
    @GetMapping(value = "/getProcessAssociationChildById")
    public Result<?> getProcessAssociationChildById(ProcessInstanceQueryVo processInstanceQueryVo) {
        LambdaQueryWrapper<ActAssociation> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(ActAssociation::getAssociationId);
        wrapper.eq(ActAssociation::getPrimaryId, processInstanceQueryVo.getPrimaryId());
        List<String> objects = (List<String>) (List) actAssociationService.listObjs(wrapper);
        if (objects.size() == 0) {
            return Result.ok();
        }
        return getAssociationList(objects,processInstanceQueryVo);
    }

    /**
     * 获取父流程相关详细信息
     *
     * @param processInstanceQueryVo
     * @return
     */
    @GetMapping(value = "/getProcessAssociationParentById")
    public Result<?> getProcessAssociationParentById(ProcessInstanceQueryVo processInstanceQueryVo) {
        LambdaQueryWrapper<ActAssociation> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(ActAssociation::getPrimaryId);
        wrapper.eq(ActAssociation::getAssociationId, processInstanceQueryVo.getAssociationId());
        List<String> objects = (List<String>) (List) actAssociationService.listObjs(wrapper);
        if (objects.size() == 0) {
            return Result.ok();
        }
       return getAssociationList(objects,processInstanceQueryVo);
    }

     Result getAssociationList(List<String> objects,ProcessInstanceQueryVo processInstanceQueryVo){
         Set<String> collect = new HashSet<>(objects);
         HistoricProcessInstanceQuery query = historyService.createHistoricProcessInstanceQuery();
         query.processInstanceIds(collect);
         FlowablePage page = this.pageList(processInstanceQueryVo, query, ProcInsListWrapper.class, allowedSortProperties,
                 HistoricProcessInstanceQueryProperty.START_TIME);
         return Result.ok(page);
     }

    /**
     * 我的已办结工单
     *
     * @param processInstanceQueryVo
     * @return
     */
    @GetMapping(value = "/completedInstanceList")
    public Result completedInstanceList(ProcessInstanceQueryVo processInstanceQueryVo) {
        FlowablePage flowablePage = new FlowablePage();
        List list=null;
        if (StringUtils.isNotEmpty(processInstanceQueryVo.getKeyword())){
             processInstanceQueryVo.setIsPage(false);
            List list1 = historicProcessInstanceMapper.completedInstanceList(SecurityUtils.getUser().getUsername(), processInstanceQueryVo);
            list = searchProcessInstanceEsQuery(list1, flowablePage, processInstanceQueryVo);
        }else{
            processInstanceQueryVo.setIsPage(true);
            processInstanceQueryVo.setPageNo(processInstanceQueryVo.getPageNo()-1);
            flowablePage.setCurrent(processInstanceQueryVo.getPageNo());
            flowablePage.setSize(processInstanceQueryVo.getPageSize());
            processInstanceQueryVo.setOffset(flowablePage.getOffset());
            flowablePage.setTotal(historicProcessInstanceMapper.getCompletedInstanceCount(SecurityUtils.getUser().getUsername(),processInstanceQueryVo));
            list = historicProcessInstanceMapper.completedInstanceList(SecurityUtils.getUser().getUsername(),processInstanceQueryVo);
        }
        IListWrapper listWrapper = SpringContextUtils.getBean(ProcInsListWrapper.class);
        list = listWrapper.execute(list);//填充、包装数据
        flowablePage.setRecords(list);
        return Result.ok(flowablePage);
    }

    /**
     * 我的未办结工单
     *
     * @param processInstanceQueryVo
     * @return
     */
    @GetMapping(value = "/unCompletedinstancelist")
    public Result unCompletedinstancelist(ProcessInstanceQueryVo processInstanceQueryVo) {
        FlowablePage flowablePage = new FlowablePage();
        List<HistoricTaskInstanceEntityImplVo> list=null;
        if (StringUtils.isNotEmpty(processInstanceQueryVo.getKeyword())){
            processInstanceQueryVo.setIsPage(false);
            List<HistoricTaskInstanceEntityImplVo> list1 = historicProcessInstanceMapper.unCompletedInstanceList(SecurityUtils.getUser().getUsername(), processInstanceQueryVo);
            list = searchProcessInstanceEsQuery(list1, flowablePage, processInstanceQueryVo);
        }else{
            processInstanceQueryVo.setIsPage(true);
            processInstanceQueryVo.setPageNo(processInstanceQueryVo.getPageNo()-1);
            flowablePage.setCurrent(processInstanceQueryVo.getPageNo());
            flowablePage.setSize(processInstanceQueryVo.getPageSize());
            processInstanceQueryVo.setOffset(flowablePage.getOffset());
            flowablePage.setTotal(historicProcessInstanceMapper.getUnCompletedInstanceCount(SecurityUtils.getUser().getUsername(),processInstanceQueryVo));
            list = historicProcessInstanceMapper.unCompletedInstanceList(SecurityUtils.getUser().getUsername(),processInstanceQueryVo);
        }
        for (HistoricTaskInstanceEntityImplVo historicTaskInstanceEntityImplVo : list) {
            if (historicTaskInstanceEntityImplVo.getTaskDueTime() != null) {
                try {
                    Object slaType = taskService.getVariable(historicTaskInstanceEntityImplVo.getTaskId(), "slaType");
                    if (slaType != null) {
                        historicTaskInstanceEntityImplVo.setSlaType(slaType.toString());
                    }
                    if (historicTaskInstanceEntityImplVo.getTaskEndTime().compareTo(historicTaskInstanceEntityImplVo.getTaskDueTime())<0){
                        long timeDiff = historicTaskInstanceEntityImplVo.getTaskEndTime().getTime() - historicTaskInstanceEntityImplVo.getTaskDueTime().getTime();
                        // 计算相差的小时数
                        long hours = timeDiff % (1000 * 24 * 60 * 60) / (1000 * 60 * 60);
                        // 计算相差的天数
                        long days = timeDiff / (24 * 60 * 60 * 1000);
                        historicTaskInstanceEntityImplVo.setOverTime(days + "d " + hours + "h");
                    }
                } catch (FlowableObjectNotFoundException e) {
                    break;
                }


            }


        }
        flowablePage.setRecords(list);
        return Result.ok(flowablePage);
    }

    @Autowired
    YqOpServiceProcessevaluateService yqOpServiceProcessevaluateService;
    @Autowired
    CommonListenerService commonService;


    /**
     * 查询当前登录人的工单统计信息
     *
     */
    @GetMapping(value = "/getProcessCount")
    public Result<?> getProcessCount() {
        HashMap<String, Object> map = new HashMap<>();
        //查询已办工单
        HistoricTaskInstanceQuery historicTaskInstanceQuery = historyService.createHistoricTaskInstanceQuery();
        historicTaskInstanceQuery.finished();
        historicTaskInstanceQuery.or().taskAssignee(SecurityUtils.getUser().getUsername()).taskOwner(SecurityUtils.getUser().getUsername());
        long completedInstanceCount = historicTaskInstanceQuery.count();
        //已办数量
        map.put("completedCount", completedInstanceCount);
        //查询未办工单
        TaskQuery query =taskService.createTaskQuery();
        query.taskCategory(FlowableConstant.CATEGORY_TODO);
        query.or().taskCandidateOrAssigned(SecurityUtils.getUser().getUsername()).taskOwner(SecurityUtils.getUser().getUsername()).endOr();
        long unCompletedCount = query.count();
        //未办工单数
        map.put("unCompletedCount", unCompletedCount);
        //查询拒绝工单次数
        long rejectCountByUser = commonService.getRejectCountByUser(SecurityUtils.getUser().getUsername());
        //拒绝工单次数
        map.put("rejectCount", rejectCountByUser);
        //获取无过期时间的任务
        HistoricTaskInstanceQuery historicTaskInstanceQuery2 = historyService.createHistoricTaskInstanceQuery();
        historicTaskInstanceQuery2.finished().withoutTaskDueDate();
        historicTaskInstanceQuery2.or().taskAssignee(SecurityUtils.getUser().getUsername()).taskOwner(SecurityUtils.getUser().getUsername());
        long noOverdueCount = historicTaskInstanceQuery2.count();
        //有过期的工单已办数量=已办工单总数-无过期的任务
        long overdue = completedInstanceCount - noOverdueCount;
        NumberFormat numberFormat = NumberFormat.getInstance();
        //获取未超时的任务信息
        Integer overdueTask = historicTaskMapper.countOverdueTask(SecurityUtils.getUser().getUsername());
        if (overdueTask==0){
            //及时处理率
            map.put("handTimelyRate", 0);
        }else{
            double handTimelyRate = Double.parseDouble(numberFormat.format((overdueTask * 1.0 / overdue) * 100).replace(",", ""));
            //及时处理率
            map.put("handTimelyRate", Math.round(handTimelyRate));
        }
        if (completedInstanceCount==0){
            //解决率
            map.put("resolutionRate", 0);
        }else{
            double handRate = Double.parseDouble(numberFormat.format((completedInstanceCount * 1.0 / (completedInstanceCount+unCompletedCount)) * 100).replace(",", ""));
            //解决率
            map.put("resolutionRate", Math.round( handRate));
        }
        List<String> strings = new ArrayList<>();
        strings.add(SecurityUtils.getUser().getUsername());
        //好评率
        double goodRate = yqOpServiceProcessevaluateService.getGoodRate(strings);
        map.put("goodRate",  Math.round(goodRate));

       //查询总任务耗时
        Long totalTaskTime = historicTaskMapper.getTotalTaskTime(SecurityUtils.getUser().getUsername());
         //计算平均处理时间
        if (totalTaskTime==null||totalTaskTime==0){
            map.put("averageTime", 0);
        }else{
           double totalhandleSecondH = (double) totalTaskTime / 3600000;
            double averageTime = Double.parseDouble(numberFormat.format((totalhandleSecondH / (completedInstanceCount+unCompletedCount))).replace(",", ""));
            BigDecimal bd = new BigDecimal(Double.toString(averageTime));
            bd = bd.setScale(2, RoundingMode.HALF_UP);
            //保留两位小数
            map.put("averageTime", bd.doubleValue());
        }
        return Result.ok(map);

    }

    //判断是否违反sla
    @GetMapping(value = "/isViolateSla")
    public Result<?> isViolateSla(String date){
        if (org.apache.commons.lang.StringUtils.isEmpty(date)) {
            date = DateUtils.getYearMonth(new Date());
        }
        //查询这个月的第一天和最后一天
        Date startTime = DateUtils.str2Date(date + "-1", DateUtils.date_sdf.get());
        Calendar cal = DateUtils.getDate(startTime);
        cal.roll(Calendar.DAY_OF_MONTH, -1);
        Date endTime = cal.getTime();
        HashMap<String, Boolean> hashMap = new HashMap<>();
        HistoricTaskInstanceQuery historicTaskInstanceQuery2 = historyService.createHistoricTaskInstanceQuery();
        historicTaskInstanceQuery2.finished().taskVariableExists("slaType");
        historicTaskInstanceQuery2.taskCompletedBefore(ObjectUtils.convertToDatetime(DateUtils.formatDate(endTime) + " 23:59:59")).taskCompletedAfter(ObjectUtils.convertToDatetime(DateUtils.formatDate(startTime) + " 00:00:00"));
        historicTaskInstanceQuery2.or().taskAssignee(SecurityUtils.getUser().getUsername()).taskOwner(SecurityUtils.getUser().getUsername());
        List<HistoricTaskInstance> list = historicTaskInstanceQuery2.list();
        if (!list.isEmpty()){
            for (HistoricTaskInstance historicTaskInstance : list) {
                //判断结束时间是否大于到期时间
                if (historicTaskInstance.getEndTime().compareTo(historicTaskInstance.getDueDate()) > 0) {
                    hashMap.put(DateUtils.formatDate(historicTaskInstance.getEndTime()),true);
                }else{
                    hashMap.put(DateUtils.formatDate(historicTaskInstance.getEndTime()),false);
                }

            }
        }
        return Result.ok(hashMap);
    }

    /**
     * 获取服务商的流程实例
     *
     * @param processInstanceQueryVo
     * @return
     */
    @GetMapping("/getProviderProcessInstance")
    public Result<?> getProviderProcessInstanceList(ProcessInstanceQueryVo processInstanceQueryVo) {
       //检测服务商用户角色并获取服务商的所有用户
        List<String> userList = commonService.checkUserIsProvider(SecurityUtils.getUser().getUsername());
        FlowablePage flowablePage = new FlowablePage();
        List<HistoricTaskInstanceEntityImplVo> list = null;
        if (StringUtils.isNotEmpty(processInstanceQueryVo.getKeyword())) {
            processInstanceQueryVo.setIsPage(false);
            List<HistoricProcessInstanceEntityImpl> list1 = processInstanceService.getAllProcessInstanceByUsers(processInstanceQueryVo,userList);
            list = searchProcessInstanceEsQuery(list1, flowablePage, processInstanceQueryVo);
        }else{
            processInstanceQueryVo.setIsPage(true);
            processInstanceQueryVo.setPageNo(processInstanceQueryVo.getPageNo()-1);
            flowablePage.setCurrent(processInstanceQueryVo.getPageNo());
            flowablePage.setSize(processInstanceQueryVo.getPageSize());
            processInstanceQueryVo.setOffset(flowablePage.getOffset());
            flowablePage.setTotal(processInstanceService.getAllProcessInstanceCountByUsers(processInstanceQueryVo,userList));
            list = processInstanceService.getAllProcessInstanceByUsers(processInstanceQueryVo,userList);
        }
        IListWrapper listWrapper = SpringContextUtils.getBean(ProcInsListWrapper.class);
        list = listWrapper.execute(list);//填充、包装数据
        flowablePage.setRecords(list);
        return Result.ok(flowablePage);
    }

    /**
     * 导出流程表单相关内容
     *
     */
    @AutoLog(value = "导出流程表单相关内容")
    @ApiOperation(value = "导出流程表单相关内容")
    @GetMapping(value = "/exportFrom")
    public Result  exportFrom(HttpServletResponse response,ProcessInstanceQueryVo processInstanceQueryVo) {
        processInstanceService.exportFrom(response, processInstanceQueryVo);
         return Result.ok("请稍等，后台服务正在处理，成功后，可前往下载管理页面查看下载任务");
    }



}

