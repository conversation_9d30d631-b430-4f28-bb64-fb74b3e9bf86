package com.yuanqiao.insight.modules.flowable.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yuanqiao.insight.common.constant.CommonConstant;
import com.yuanqiao.insight.common.core.util.CommonUtil;
import com.yuanqiao.insight.common.core.util.ObjectUtils;
import com.yuanqiao.insight.common.core.util.SecurityUtils;
import com.yuanqiao.insight.common.util.common.LocalCacheUtils;
import com.yuanqiao.insight.modules.flowable.common.CommentTypeEnum;
import com.yuanqiao.insight.modules.flowable.common.ResponseFactory;
import com.yuanqiao.insight.modules.flowable.common.cmd.AddCcIdentityLinkCmd;
import com.yuanqiao.insight.modules.flowable.common.cmd.BackUserTaskCmd;
import com.yuanqiao.insight.modules.flowable.common.cmd.CompleteTaskReadCmd;
import com.yuanqiao.insight.modules.flowable.common.enums.ButtonsEnum;
import com.yuanqiao.insight.modules.flowable.common.exception.FlowableNoPermissionException;
import com.yuanqiao.insight.modules.flowable.constant.FlowableConstant;
import com.yuanqiao.insight.modules.flowable.dto.UrgeDto;
import com.yuanqiao.insight.modules.flowable.mapper.HistoricTaskMapper;
import com.yuanqiao.insight.modules.flowable.service.FlowableTaskService;
import com.yuanqiao.insight.modules.flowable.service.IActZBusinessService;
import com.yuanqiao.insight.modules.flowable.util.FlowableUtils;
import com.yuanqiao.insight.modules.flowable.vo.*;
import com.yuanqiao.insight.modules.flowable.wapper.TaskListWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.constants.BpmnXMLConstants;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.*;
import org.flowable.common.engine.api.FlowableException;
import org.flowable.common.engine.api.FlowableIllegalArgumentException;
import org.flowable.common.engine.api.FlowableObjectNotFoundException;
import org.flowable.common.engine.impl.de.odysseus.el.ExpressionFactoryImpl;
import org.flowable.common.engine.impl.de.odysseus.el.util.SimpleContext;
import org.flowable.common.engine.impl.identity.Authentication;
import org.flowable.common.engine.impl.javax.el.ExpressionFactory;
import org.flowable.common.engine.impl.javax.el.PropertyNotFoundException;
import org.flowable.common.engine.impl.javax.el.ValueExpression;
import org.flowable.engine.*;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.repository.Model;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ActivityInstance;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.task.Comment;
import org.flowable.identitylink.api.IdentityLink;
import org.flowable.identitylink.api.IdentityLinkType;
import org.flowable.identitylink.api.history.HistoricIdentityLink;
import org.flowable.idm.api.Group;
import org.flowable.idm.api.User;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskInfo;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.api.CommonListenerService;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.system.vo.SysAnnouncement;
import org.jeecg.common.system.vo.SysAnnouncementSend;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020年3月23日
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class FlowableTaskServiceImpl implements FlowableTaskService {
    private final static ThreadLocal<SimpleDateFormat> simpleDateFormatThreadLocal = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
    private final ThreadLocal<ObjectMapper> objectMapperThreadLocal = ThreadLocal.withInitial(ObjectMapper::new);
    @Autowired
    protected IdentityService identityService;
    @Autowired
    protected RepositoryService repositoryService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected RuntimeService runtimeService;
    @Autowired
    protected HistoryService historyService;
    @Autowired
    protected PermissionServiceImpl permissionService;
    @Autowired
    protected ResponseFactory responseFactory;
    @Autowired
    protected ManagementService managementService;
    @Autowired
    protected FormService formService;
    @Autowired
    protected IActZBusinessService actZBusinessService;
    @Resource
    private ISysBaseAPI iSysBaseAPI;
    @Autowired
    private ActNodeServiceImpl actNodeService;
    @Autowired
    private HistoricTaskMapper historicTaskMapper;

    @Override
    public TaskResponse getTask(String taskId) {
        String userId = SecurityUtils.getUser().getUsername();
        HistoricTaskInstance taskHis = permissionService.validateReadPermissionOnTask(taskId, userId, true, true);
        if (taskHis == null) {
            return null;
        }
        TaskResponse rep = null;
        ProcessDefinition processDefinition = null;
        String formKey = null;
        Object renderedTaskForm = null;
        HistoricTaskInstance parentTask = null;
        if (StringUtils.isNotEmpty(taskHis.getProcessDefinitionId())) {
            processDefinition = repositoryService.getProcessDefinition(taskHis.getProcessDefinitionId());
            BpmnModel bpmnModel = repositoryService.getBpmnModel(taskHis.getProcessDefinitionId());
            FlowElement flowElement = bpmnModel.getFlowElement(taskHis.getTaskDefinitionKey());
            if (flowElement instanceof UserTask) {
                formKey = formService.getTaskFormKey(processDefinition.getId(), taskHis.getTaskDefinitionKey());
            }
            if (taskHis.getEndTime() == null && formKey != null && formKey.length() > 0) {
                renderedTaskForm = formService.getRenderedTaskForm(taskId);
            }
        }
        if (StringUtils.isNotEmpty(taskHis.getParentTaskId())) {
            parentTask =
                    historyService.createHistoricTaskInstanceQuery().taskId(taskHis.getParentTaskId()).singleResult();
        }
        rep = new TaskResponse(taskHis, processDefinition, parentTask, null);
        rep.setFormKey(formKey);
        rep.setRenderedTaskForm(renderedTaskForm);

        fillPermissionInformation(rep, taskHis, userId);
        // Populate the people
        populateAssignee(taskHis, rep);
        List<String> involvedUsers = getInvolvedUsers(taskId);
        rep.setInvolvedPeople(involvedUsers);
        if (!involvedUsers.isEmpty()) {
            List<String> userByNames = iSysBaseAPI.getRealNameByNames(involvedUsers);
            rep.setInvolvedPeopleNames(String.join(", ", userByNames));
        }
        Task task = null;
        if (taskHis.getEndTime() == null) {
            task = taskService.createTaskQuery().taskId(taskId).singleResult();
            rep.setSuspended(task.isSuspended());
            rep.setDelegationState(task.getDelegationState());
        }
        rep.setOwnerName(this.getUserName(taskHis.getOwner()));
        rep.setAssigneeName(this.getUserName(taskHis.getAssignee()));
        return rep;
    }

    @Override
    public TaskNodeResponse getTaskByNodeId(String nodeId, String processInsId) {
        TaskNodeResponse taskNodeResponse = new TaskNodeResponse();
        String userId = SecurityUtils.getUser().getUsername();
        HistoricTaskInstanceQuery query = historyService.createHistoricTaskInstanceQuery();
        query.taskDefinitionKey(nodeId);
        query.processInstanceId(processInsId);
        query.orderByTaskCreateTime().desc();
        List<HistoricTaskInstance> taskHisList = query.list();

        if (taskHisList == null || taskHisList.size() == 0) {
            taskNodeResponse.setTaskFlag(false);
            return taskNodeResponse;
        }
        HistoricTaskInstance historicTaskInstance = taskHisList.get(0);
        if (historicTaskInstance.getAssignee() != null) {
            LoginUser user = iSysBaseAPI.getUserByName(historicTaskInstance.getAssignee());
            if (user != null) {
                taskNodeResponse.setAssignee(user.getRealname());
            }
        }
        taskNodeResponse.setCreateTime(historicTaskInstance.getCreateTime());
        taskNodeResponse.setEndTime(historicTaskInstance.getEndTime());
        taskNodeResponse.setTaskId(historicTaskInstance.getId());
        taskNodeResponse.setTaskName(historicTaskInstance.getName());
        //获取候选人
        List<String> candidateUserNameList = new ArrayList<>();
        if (historicTaskInstance.getEndTime() == null) {
            List<IdentityLink> identityLinkList = taskService.getIdentityLinksForTask(historicTaskInstance.getId());
            if (identityLinkList != null && identityLinkList.size() > 0) {
                for (IdentityLink identityLink : identityLinkList) {
                    if ("candidate".equals(identityLink.getType())) {
                        if (StringUtils.isNotBlank(identityLink.getUserId())) {
                            LoginUser loginUser = iSysBaseAPI.getUserByName(identityLink.getUserId());
                            if (loginUser != null) {
                                candidateUserNameList.add(loginUser.getRealname());
                            }
                        }
                    }
                }
                taskNodeResponse.setCandidateUserNames(candidateUserNameList);
            }
        }
        List<Comment> comments = taskService.getTaskComments(historicTaskInstance.getId(), "WC");
        if (comments != null && comments.size() > 0) {
            taskNodeResponse.setCommentFullMessage(comments.get(0).getFullMessage());
        }
        taskNodeResponse.setTaskFlag(true);
        return taskNodeResponse;
    }

    @Override
    public List<TaskResponse> getSubTasks(String taskId) {
        String userId = SecurityUtils.getUser().getUsername();
        HistoricTaskInstance parentTask = permissionService.validateReadPermissionOnTask(taskId, userId, true, true);
        List<Task> subTasks = this.taskService.getSubTasks(taskId);
        List<TaskResponse> subTasksRepresentations = new ArrayList<>(subTasks.size());
        for (Task subTask : subTasks) {
            TaskResponse representation = new TaskResponse(subTask, parentTask);
            fillPermissionInformation(representation, subTask, userId);
            populateAssignee(subTask, representation);
            representation.setInvolvedPeople(getInvolvedUsers(subTask.getId()));
            subTasksRepresentations.add(representation);
        }
        return subTasksRepresentations;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskResponse updateTask(TaskUpdateRequest taskUpdateRequest) {
        String userId = SecurityUtils.getUser().getUsername();
        permissionService.validateReadPermissionOnTask(taskUpdateRequest.getId(), userId, false, false);
        Task task = getTaskNotNull(taskUpdateRequest.getId());
        task.setName(taskUpdateRequest.getName());
        task.setDescription(taskUpdateRequest.getDescription());
        task.setAssignee(taskUpdateRequest.getAssignee());
        task.setOwner(taskUpdateRequest.getOwner());
        task.setDueDate(taskUpdateRequest.getDueDate());
        task.setPriority(taskUpdateRequest.getPriority());
        task.setCategory(taskUpdateRequest.getCategory());
        taskService.saveTask(task);
        return new TaskResponse(task);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void involveUser(String taskId, String involveUserId) {
        Task task = getTaskNotNull(taskId);
        String userId = SecurityUtils.getUser().getUsername();
        permissionService.validateReadPermissionOnTask(task.getId(), userId, false, false);
        if (involveUserId != null && involveUserId.length() > 0) {
            taskService.addUserIdentityLink(taskId, involveUserId, IdentityLinkType.PARTICIPANT);
        } else {
            throw new FlowableException("User id is required");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeInvolvedUser(String taskId, String involveUserId) {
        Task task = getTaskNotNull(taskId);
        String userId = SecurityUtils.getUser().getUsername();
        permissionService.validateReadPermissionOnTask(task.getId(), userId, false, false);
        if (involveUserId != null && involveUserId.length() > 0) {
            taskService.deleteUserIdentityLink(taskId, involveUserId, IdentityLinkType.PARTICIPANT);
        } else {
            throw new FlowableException("User id is required");
        }
    }

    @Autowired
    CommonListenerService commonListenerService;
    LocalCacheUtils cacheUtils = LocalCacheUtils.getInstance();

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void claimTask(TaskRequest taskRequest) {
        String taskId = taskRequest.getTaskId();
        String userId = SecurityUtils.getUser().getUsername();

        TaskInfo task = permissionService.validateReadPermissionOnTask2(taskId, userId, false, false);
        if (task.getAssignee() != null && task.getAssignee().length() > 0) {
            throw new FlowableNoPermissionException("用户没有权限");
        }
        this.addComment(taskId, task.getProcessInstanceId(), userId, CommentTypeEnum.RL, taskRequest.getMessage());
        taskService.claim(taskId, userId);


    }

    /**
     * 移除消息通知中的处理按钮
     *
     * @param taskId
     */
    private void removeMessageHandleButton(String taskId) {
        List<SysAnnouncement> byBusId = iSysBaseAPI.getByBusId(taskId);
        for (SysAnnouncement sysAnnouncement : byBusId) {
            String busMap = sysAnnouncement.getBusMap();
            try {
                Map<String, Object> stringObjectMap = objectMapperThreadLocal.get().readValue(busMap, new TypeReference<Map<String, Object>>() {
                });
                stringObjectMap.remove("handleState");
                sysAnnouncement.setBusMap(objectMapperThreadLocal.get().writeValueAsString(stringObjectMap));
                iSysBaseAPI.updatesysAnnouncementById(sysAnnouncement);
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        String[] usernames = {sysAnnouncement.getUserIds()};
                        if (usernames != null) {
                            List<LoginUser> loginUsers = iSysBaseAPI.queryUserByNames(usernames);
                            if (loginUsers != null && loginUsers.size() > 0) {
                                String[] id = {loginUsers.get(0).getId()};
                                iSysBaseAPI.sendWebSocketMsg(id, "user");
                            }
                        }

                    }
                });
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unclaimTask(TaskRequest taskRequest) {
        String taskId = taskRequest.getTaskId();
        String userId = SecurityUtils.getUser().getUsername();
        TaskInfo task = this.getTaskNotNull(taskId);
        if (!userId.equals(task.getAssignee())) {
            throw new FlowableNoPermissionException("用户没有权限。");
        }
        if (FlowableConstant.CATEGORY_TO_READ.equals(task.getCategory())) {
            throw new FlowableNoPermissionException("用户无法取消订阅已读任务");
        }
        if (FlowableConstant.INITIATOR.equals(task.getTaskDefinitionKey())) {
            throw new FlowableNoPermissionException("发起人无法取消对任务的绑定");
        }

        this.addComment(taskId, task.getProcessInstanceId(), userId, CommentTypeEnum.QXRL, taskRequest.getMessage());
        taskService.unclaim(taskId);
        // 判断是否是协办取消认领
        if (permissionService.isTaskPending((Task) task)) {
            taskService.resolveTask(taskId, null);
        }

//        消息通知 恢复处理功能按钮
        List<SysAnnouncement> announcements = iSysBaseAPI.getByBusId(taskId);
        for (SysAnnouncement sysAnnouncement : announcements) {
            String busMap = sysAnnouncement.getBusMap();
            try {
                Map<String, Object> stringObjectMap = objectMapperThreadLocal.get().readValue(busMap, new TypeReference<Map<String, Object>>() {
                });
                stringObjectMap.put("handleState", 1);
                sysAnnouncement.setBusMap(objectMapperThreadLocal.get().writeValueAsString(stringObjectMap));
                iSysBaseAPI.updatesysAnnouncementById(sysAnnouncement);
                List<SysAnnouncementSend> sysAnnouncementSends = iSysBaseAPI.listBySysAnnouncementId(sysAnnouncement.getId());
                if (sysAnnouncementSends != null && sysAnnouncementSends.size() > 0) {
                    SysAnnouncementSend one = sysAnnouncementSends.get(0);
                    one.setReadFlag("0");
                    one.setReadTime(null);
                    iSysBaseAPI.updatesysAnnouncemenSendtById(one);
                    TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                        @Override
                        public void afterCommit() {
                            String[] usernames = {sysAnnouncement.getUserIds()};
                            List<LoginUser> loginUsers = iSysBaseAPI.queryUserByNames(usernames);
                            if (loginUsers != null && loginUsers.size() > 0) {
                                String[] id = {loginUsers.get(0).getId()};
                                iSysBaseAPI.sendWebSocketMsg(id, "user");
                            }
                        }
                    });
                }

            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }


        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addIdentiyLinkForUser(Task task, String userId, String linkType) {
        List<IdentityLink> identityLinks = taskService.getIdentityLinksForTask(task.getId());
        boolean isOldUserInvolved = false;
        for (IdentityLink identityLink : identityLinks) {
            isOldUserInvolved =
                    userId.equals(identityLink.getUserId()) && (identityLink.getType().equals(IdentityLinkType.PARTICIPANT) || identityLink.getType().equals(IdentityLinkType.CANDIDATE));
            if (isOldUserInvolved) {
                break;
            }
        }
        if (!isOldUserInvolved) {
            taskService.addUserIdentityLink(task.getId(), userId, linkType);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void adminAssign(TaskRequest taskRequest) {
        String taskId = taskRequest.getTaskId();
        String assignee = taskRequest.getUserId();
        String userId = SecurityUtils.getUser().getUsername();
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        this.addComment(taskId, task.getProcessInstanceId(), userId, CommentTypeEnum.ZB, taskRequest.getMessage());
        taskService.setAssignee(task.getId(), assignee);
        // 暂时转办人员不作为参与者
        // String oldAssignee = task.getAssignee();
        // // If the old assignee user wasn't part of the involved users yet, make it so
        // addIdentiyLinkForUser(task, oldAssignee, IdentityLinkType.PARTICIPANT);
        // // If the current user wasn't part of the involved users yet, make it so
        // addIdentiyLinkForUser(task, userId, IdentityLinkType.PARTICIPANT);

        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(task.getProcessInstanceId()).singleResult();


        HashMap<String, String> templateParam = new HashMap<>();
        templateParam.put("bpm_name", processInstance.getName());
        templateParam.put("bpm_task", task.getName());
        templateParam.put("datetime", simpleDateFormatThreadLocal.get().format(task.getCreateTime()));
        LoginUser userByUsername = iSysBaseAPI.getUserByName(userId);
        templateParam.put("bpm_form_user", userByUsername.getRealname());

    }

    /**
     * 转办
     *
     * @param taskRequest
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void assignTask(TaskRequest taskRequest) {
        String taskId = taskRequest.getTaskId();
        String assignee = taskRequest.getUserId();
        String userId = SecurityUtils.getUser().getUsername();
        Task task = permissionService.validateAssignPermissionOnTask(taskId, userId, assignee);
        this.addComment(taskId, task.getProcessInstanceId(), userId, CommentTypeEnum.ZB, taskRequest.getMessage());
        taskService.setAssignee(task.getId(), assignee);


        // 暂时转办人员不作为参与者
        // String oldAssignee = task.getAssignee();
        // // If the old assignee user wasn't part of the involved users yet, make it so
        // addIdentiyLinkForUser(task, oldAssignee, IdentityLinkType.PARTICIPANT);
        // // If the current user wasn't part of the involved users yet, make it so
        // addIdentiyLinkForUser(task, userId, IdentityLinkType.PARTICIPANT);

//        给新的处理人发通知
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(task.getProcessInstanceId()).singleResult();
        Model model = repositoryService.createModelQuery().deploymentId(processInstance.getDeploymentId()).singleResult();

        HashMap<String, String> templateParam = new HashMap<>(5);
        templateParam.put("bpm_name", processInstance.getName());
        templateParam.put("bpm_task", task.getName());
        LoginUser userByUsername = iSysBaseAPI.getUserByName(userId);
        templateParam.put("bpm_form_user", userByUsername.getRealname());
        templateParam.put("datetime", simpleDateFormatThreadLocal.get().format(new Date()));


        Object valueByKey = cacheUtils.getValueByKey(CommonConstant.DATA_DICT_KEY + "processNotification_transfer");
        if (valueByKey==null) {
            throw new FlowableException("未配置[processNotification_transfer]转办通知模板参数，请联系管理员");
        }
        iSysBaseAPI.sendNoticeByProcess(templateParam, valueByKey.toString(), new ArrayList<>(Collections.singletonList(assignee)));

    }

    /**
     * 委派任务
     *
     * @param taskRequest
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result delegateTask(TaskRequest taskRequest) {
        String taskId = taskRequest.getTaskId();
        String delegate = taskRequest.getUserId();
        String userId = SecurityUtils.getUser().getUsername();
        Task task = permissionService.validateDelegatePermissionOnTask(taskId, userId, delegate);
        this.addComment(taskId, task.getProcessInstanceId(), userId, CommentTypeEnum.WP, taskRequest.getMessage());
        taskService.delegateTask(task.getId(), delegate);


        // 暂时委派人员不作为参与者
        // String oldAssignee = task.getAssignee();
        // // If the old assignee user wasn't part of the involved users yet, make it so
        // addIdentiyLinkForUser(task, oldAssignee, IdentityLinkType.PARTICIPANT);
        // // If the current user wasn't part of the involved users yet, make it so
        // addIdentiyLinkForUser(task, userId, IdentityLinkType.PARTICIPANT);


        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(task.getProcessInstanceId()).singleResult();
        Model model = repositoryService.createModelQuery().deploymentId(processInstance.getDeploymentId()).singleResult();

        HashMap<String, String> templateParam = new HashMap<>(5);
        templateParam.put("bpm_name", processInstance.getName());
        templateParam.put("bpm_task", task.getName());
        templateParam.put("datetime", simpleDateFormatThreadLocal.get().format(task.getCreateTime()));
        LoginUser userByUsername = iSysBaseAPI.getUserByName(userId);
        templateParam.put("bpm_form_user", userByUsername.getRealname());


        Object valueByKey = cacheUtils.getValueByKey(CommonConstant.DATA_DICT_KEY + "processNotification_delegate");
        if (valueByKey==null) {
            throw new FlowableException("未配置[processNotification_delegate]委派通知模板参数，请联系管理员");
        }
        String templateCode=valueByKey.toString();
        iSysBaseAPI.sendNoticeByProcess(templateParam, templateCode, new ArrayList<>(Collections.singletonList(delegate)));
        return Result.ok("委派任务成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void completeTask(TaskRequest taskRequest) {
        String taskId = taskRequest.getTaskId();

        Task taskItem = taskService.createTaskQuery().taskId(taskId).singleResult();
        ProcessInstance processInstance = null;
        if (taskItem != null) {
            String processInstanceId = taskItem.getProcessInstanceId();
            processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
            if (processInstance.isSuspended()) {
                throw new FlowableException("该流程已被冻结。");
            }
        }
        String currUserId = SecurityUtils.getUser().getUsername();
        Task task = getTaskNotNull(taskId);
        //当前登录用户是否为任务所有者或执行者
        if (!permissionService.isTaskOwnerOrAssignee(currUserId, task)) {
            if (StringUtils.isEmpty(task.getScopeType()) && !permissionService.validateIfUserIsInitiatorAndCanCompleteTask(currUserId, task)) {
                throw new FlowableNoPermissionException("该用户没有权限");
            }
        }

        Map<String, Object> completeVariables = null;
        if (taskRequest.getValues() != null && !taskRequest.getValues().isEmpty()) {
            completeVariables = taskRequest.getValues();
            // 允许任务表单修改流程表单场景 begin
            // 与前端约定：流程表单变量名为 processInstanceFormData，且只有流程表单startFormKey=taskFormKey时才允许修改该变量的值，防止恶意节点修改流程表单内容
            if (completeVariables.containsKey(FlowableConstant.PROCESS_INSTANCE_FORM_DATA)) {
                String startFormKey = formService.getStartFormKey(task.getProcessDefinitionId());
                String taskFormKey = formService.getTaskFormKey(task.getProcessDefinitionId(),
                        task.getTaskDefinitionKey());
                boolean modifyProcessInstanceFormData =
                        CommonUtil.isNotEmptyStr(startFormKey) && CommonUtil.isNotEmptyStr(taskFormKey) && startFormKey.equals(taskFormKey);
                if (!modifyProcessInstanceFormData) {
                    throw new FlowableNoPermissionException("该用户没有权限");
                }
            }
            // 允许任务表单修改流程表单场景 end

            // 非会签用户节点，默认设置流程变量 __taskDefinitionKey__=currUserId，用于存储该节点执行人，且以最近的执行人为准
            UserTask userTask = (UserTask) FlowableUtils.getFlowElement(repositoryService,
                    task.getProcessDefinitionId(), task.getTaskDefinitionKey());
            if (userTask != null && !userTask.hasMultiInstanceLoopCharacteristics()) {
                completeVariables.put("__" + task.getTaskDefinitionKey() + "__", currUserId);
            }
        } else {
            // 非会签用户节点，默认设置流程变量 __taskDefinitionKey__=currUserId，用于存储该节点执行人，且以最近的执行人为准
            UserTask userTask = (UserTask) FlowableUtils.getFlowElement(repositoryService,
                    task.getProcessDefinitionId(), task.getTaskDefinitionKey());
            if (userTask != null && !userTask.hasMultiInstanceLoopCharacteristics()) {
                completeVariables = new HashMap<>(1);
                completeVariables.put("__" + task.getTaskDefinitionKey() + "__", currUserId);
            }
        }

        if (StringUtils.isNotBlank(taskRequest.getAssignNextNode())) {
            // : 2022/7/5 流程中可能多个指定节点 第一次使用后移除掉此流程变量
            completeVariables.put("assignNextNode", taskRequest.getAssignNextNode());
        }

        this.addComment(taskId, task.getProcessInstanceId(), currUserId,
                FlowableConstant.INITIATOR.equals(task.getTaskDefinitionKey()) ? CommentTypeEnum.CXTJ :
                        CommentTypeEnum.WC, taskRequest.getMessage());

        // 处理抄送
        if (CommonUtil.isNotEmptyObject(taskRequest.getCcUserIds())) {
            managementService.executeCommand(new AddCcIdentityLinkCmd(task.getProcessInstanceId(), task.getId(),
                    currUserId, taskRequest.getCcUserIds()));
            // 发送消息通知
            Map<String, String> templateParam = new HashMap<>();
            templateParam.put("process_name", Objects.requireNonNull(processInstance).getName());
            templateParam.put("datetime", simpleDateFormatThreadLocal.get().format(new Date()));
            templateParam.put("CC_people", SecurityUtils.getUser().getRealname());

            Object valueByKey = cacheUtils.getValueByKey(CommonConstant.DATA_DICT_KEY + "processNotification_carbonCopy");
            if (valueByKey==null) {
                throw new FlowableException("未配置[processNotification_carbonCopy]转办通知模板参数，请联系管理员");
            }
            iSysBaseAPI.sendNoticeByProcess(templateParam, valueByKey.toString(), Arrays.asList(taskRequest.getCcUserIds()));
        }

        if (task.getAssignee() == null || !task.getAssignee().equals(currUserId)) {
            taskService.setAssignee(taskId, currUserId);
        }
        // 判断是否是协办完成还是正常流转
        if (permissionService.isTaskPending(task)) {
            taskService.resolveTask(taskId, completeVariables);
            // 如果当前执行人是任务所有人，直接完成任务
            if (currUserId.equals(task.getOwner())) {
                taskService.complete(taskId, completeVariables);
            }
//            委派完成后通知ower
            HashMap<String, String> templateParam = new HashMap<>(5);
            templateParam.put("bpm_name", processInstance.getName());
            templateParam.put("bpm_task", task.getName());
            templateParam.put("datetime", simpleDateFormatThreadLocal.get().format(task.getCreateTime()));
            LoginUser userByUsername = iSysBaseAPI.getUserByName(currUserId);
            templateParam.put("bpm_assignee", userByUsername.getRealname());
            templateParam.put("comment", taskRequest.getMessage());

            Object valueByKey = cacheUtils.getValueByKey(CommonConstant.DATA_DICT_KEY + "processNotification_delegateComplete");
            if (valueByKey == null) {
                throw new FlowableException("未配置[processNotification_delegateComplete]任务委派通知模版参数,请联系管理员!");
            }
            String templateCode = valueByKey.toString();
            iSysBaseAPI.sendNoticeByProcess(templateParam, templateCode, new ArrayList<>(Collections.singletonList(task.getOwner())));
        } else {
            taskService.complete(taskId, completeVariables);
        }


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTask(String taskId) {
        HistoricTaskInstance task = this.getHistoricTaskInstanceNotNull(taskId);
        if (task.getEndTime() == null) {
            throw new FlowableException("Cannot delete task that is running");
        }
        historyService.deleteHistoricTaskInstance(task.getId());
    }

    /**
     * 终止
     *
     * @param taskRequest
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void stopProcessInstance(TaskRequest taskRequest) {
        String taskId = taskRequest.getTaskId();
        String userId = SecurityUtils.getUser().getUsername();
        Task task = permissionService.validateExcutePermissionOnTask(taskId, userId);
        ProcessInstance processInstance = permissionService.validateStopProcessInstancePermissionOnTask(taskId, userId);
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processInstance.getProcessDefinitionId());
        if (bpmnModel != null) {
            Process process = bpmnModel.getMainProcess();
            List<EndEvent> endNodes = process.findFlowElementsOfType(EndEvent.class, false);
            if (endNodes != null && endNodes.size() > 0) {
                this.addComment(taskId, processInstance.getProcessInstanceId(), userId, CommentTypeEnum.ZZ,
                        taskRequest.getMessage());
                String endId = endNodes.get(0).getId();
                List<Execution> executions =
                        runtimeService.createExecutionQuery().parentId(processInstance.getProcessInstanceId()).list();
                List<String> executionIds = new ArrayList<>();
                executions.forEach(execution -> executionIds.add(execution.getId()));
                runtimeService.createChangeActivityStateBuilder().moveExecutionsToSingleActivityId(executionIds,
                        endId).changeState();
            }
        }


    }

    @Override
    public String getTaskIdByProcessInstanceId(String processInstanceId) {
        return historicTaskMapper.getTaskIdByProcessInstanceId(processInstanceId);
    }

    @Override
    public String getTaskAssigneeByNodeId(String nodeId, String processInstanceId){
        return historicTaskMapper.getTaskAssigneeByNodeId(nodeId, processInstanceId);
    }

    @Override
    public List<FlowNodeResponse> getBackNodes(String taskId) {
        TaskEntity taskEntity = (TaskEntity) permissionService.validateExcutePermissionOnTask(taskId,
                SecurityUtils.getUser().getUsername());
        //TODO 因将退回按钮放置我的申请操作中，暂不校验是否拥有退回权限（下面方法校验的是当前任务的处理人是否有权限）
      //  permissionService.validateTaskHasButtonPermission(taskEntity, ButtonsEnum.BACK);
        String processInstanceId = taskEntity.getProcessInstanceId();
        String currActId = taskEntity.getTaskDefinitionKey();
        String processDefinitionId = taskEntity.getProcessDefinitionId();
        Process process = repositoryService.getBpmnModel(processDefinitionId).getMainProcess();
        FlowNode currentFlowElement = (FlowNode) process.getFlowElement(currActId, true);
        List<ActivityInstance> activitys =
                runtimeService.createActivityInstanceQuery().processInstanceId(processInstanceId).finished().orderByActivityInstanceStartTime().asc().list();
        List<String> activityIds =
                activitys.stream().filter(activity -> activity.getActivityType().equals(BpmnXMLConstants.ELEMENT_TASK_USER)).filter(activity -> !activity.getActivityId().equals(currActId)).map(ActivityInstance::getActivityId).distinct().collect(Collectors.toList());
        List<FlowNodeResponse> result = new ArrayList<>();
        for (String activityId : activityIds) {
//            退回的节点肯定有执行人assignee，寻找哪一个节点都可以
            Optional<ActivityInstance> instanceOptional = activitys.stream().filter(activityInstance -> activityInstance.getActivityId().equals(activityId)).max(Comparator.comparing(ActivityInstance::getStartTime));
            FlowNode toBackFlowElement = (FlowNode) process.getFlowElement(activityId, true);
            if (FlowableUtils.isReachable(process, toBackFlowElement, currentFlowElement)) {
                FlowNodeResponse vo = new FlowNodeResponse();
                vo.setNodeId(activityId);
                vo.setNodeName(toBackFlowElement.getName());
//                返回执行人，方便通知时知道要通知谁
                instanceOptional.ifPresent(activityInstance -> vo.setUserId(activityInstance.getAssignee()));
                instanceOptional.ifPresent(activityInstance -> vo.setUserName(iSysBaseAPI.getUserByName(activityInstance.getAssignee()) != null ? iSysBaseAPI.getUserByName(activityInstance.getAssignee()).getRealname() : null));
                result.add(vo);
            }
        }
        return result;
    }
    public static boolean isValid(String str) {
        return StringUtils.isNotBlank(str) && !"null".equalsIgnoreCase(str.trim());
    }
    /**
     * 退回
     *
     * @param taskRequest
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void backTask(TaskRequest taskRequest) {
        String taskId = taskRequest.getTaskId();
        String userId = SecurityUtils.getUser().getUsername();
        Task task = permissionService.validateExcutePermissionOnTask(taskId, userId);
        //TODO 因将退回按钮放置我的申请操作中，暂不校验是否拥有退回权限（下面方法校验的是当前任务的处理人是否有权限）
       // permissionService.validateTaskHasButtonPermission(task, ButtonsEnum.BACK);
        String backSysMessage = "退回到" + taskRequest.getActivityName() + "。";
        this.addComment(taskId, task.getProcessInstanceId(), userId, CommentTypeEnum.TH,
                backSysMessage +(isValid(taskRequest.getMessage())?taskRequest.getMessage():""));
        // 退回发起者处理,退回到发起者,默认设置任务执行人为发起者
        ProcessInstance initiator = runtimeService.createProcessInstanceQuery().processInstanceId(task.getProcessInstanceId()).singleResult();
        if (initiator.getProcessDefinitionKey().equals(FlowableConstant.ORDER_MODEL_KEY)) {
            taskService.setVariable(taskId, "auditBack", "true");
        }

        String targetRealActivityId = managementService.executeCommand(new BackUserTaskCmd(runtimeService,
                taskRequest.getTaskId(), taskRequest.getActivityId()));
        if (FlowableConstant.INITIATOR.equals(targetRealActivityId)) {
            List<Task> newTasks = taskService.createTaskQuery().processInstanceId(task.getProcessInstanceId()).list();
            for (Task newTask : newTasks) {
                // 约定：发起者节点为 __initiator__
                if (FlowableConstant.INITIATOR.equals(newTask.getTaskDefinitionKey())) {
                    if (ObjectUtils.isEmpty(newTask.getAssignee())) {
                        taskService.setAssignee(newTask.getId(), initiator.getStartUserId());
                    }
                }
            }
        }
//        与监听器中的消息提醒重复，暂时屏蔽
//        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
//            @Override
//            public void afterCommit() {
//                HashMap<String, String> templateParam = new HashMap<>(5);
//                templateParam.put("bpm_name", initiator.getName());
//                templateParam.put("bpm_task", task.getName());
//                templateParam.put("datetime", simpleDateFormatThreadLocal.get().format(task.getCreateTime()));
//                templateParam.put("remark", "申请退回");
//                HashMap<String, String> stringStringHashMap = new HashMap<>();
//                stringStringHashMap.put("taskId", task.getId());
//                stringStringHashMap.put("processInstanceId", task.getProcessInstanceId());
//                String s = JSONObject.toJSONString(stringStringHashMap);
//                BusTemplateMessageDTO message1 = new BusTemplateMessageDTO(userId, taskRequest.getUserId(), "任务回退通知", templateParam, "bpm_cuiban", "flowable", "", s);
//                iSysBaseAPI.sendBusTemplateAnnouncement(message1);
//            }
//        });

    }

    private void fillPermissionInformation(TaskResponse taskResponse, TaskInfo task, String userId) {
        verifyProcessInstanceStartUser(taskResponse, task);
        List<HistoricIdentityLink> taskIdentityLinks = historyService.getHistoricIdentityLinksForTask(task.getId());
        verifyCandidateGroups(taskResponse, userId, taskIdentityLinks);
        verifyCandidateUsers(taskResponse, userId, taskIdentityLinks);
    }

    private void verifyProcessInstanceStartUser(TaskResponse taskResponse, TaskInfo task) {
        if (task.getProcessInstanceId() != null) {
            HistoricProcessInstance historicProcessInstance =
                    historyService.createHistoricProcessInstanceQuery().processInstanceId(task.getProcessInstanceId()).singleResult();
            if (historicProcessInstance != null && StringUtils.isNotEmpty(historicProcessInstance.getStartUserId())) {
                taskResponse.setProcessInstanceStartUserId(historicProcessInstance.getStartUserId());
                taskResponse.setProcessInstanceStartUserName(iSysBaseAPI.getUserByName(historicProcessInstance.getStartUserId()).getRealname());
                BpmnModel bpmnModel = repositoryService.getBpmnModel(task.getProcessDefinitionId());
                FlowElement flowElement = bpmnModel.getFlowElement(task.getTaskDefinitionKey());
                if (flowElement instanceof UserTask) {
                    UserTask userTask = (UserTask) flowElement;
                    List<ExtensionElement> extensionElements = userTask.getExtensionElements().get("initiator-can" +
                            "-complete");
                    if (extensionElements != null && !extensionElements.isEmpty()) {
                        String value = extensionElements.get(0).getElementText();
                        if (StringUtils.isNotEmpty(value)) {
                            taskResponse.setInitiatorCanCompleteTask(value);
                        }
                    }
                }
            }
        }
    }

    private void verifyCandidateGroups(TaskResponse taskResponse, String userId,
                                       List<HistoricIdentityLink> taskIdentityLinks) {
        List<Group> userGroups = identityService.createGroupQuery().groupMember(userId).list();
        taskResponse.setMemberOfCandidateGroup(String.valueOf(userGroupsMatchTaskCandidateGroups(userGroups,
                taskIdentityLinks)));
    }

    private boolean userGroupsMatchTaskCandidateGroups(List<Group> userGroups,
                                                       List<HistoricIdentityLink> taskIdentityLinks) {
        for (Group group : userGroups) {
            for (HistoricIdentityLink identityLink : taskIdentityLinks) {
                if (identityLink.getGroupId() != null && identityLink.getType().equals(IdentityLinkType.CANDIDATE) && group.getId().equals(identityLink.getGroupId())) {
                    return true;
                }
            }
        }
        return false;
    }

    private void verifyCandidateUsers(TaskResponse taskResponse, String userId,
                                      List<HistoricIdentityLink> taskIdentityLinks) {
        taskResponse.setMemberOfCandidateUsers(String.valueOf(currentUserMatchesTaskCandidateUsers(userId,
                taskIdentityLinks)));
    }

    private boolean currentUserMatchesTaskCandidateUsers(String userId, List<HistoricIdentityLink> taskIdentityLinks) {
        for (HistoricIdentityLink identityLink : taskIdentityLinks) {
            if (identityLink.getUserId() != null && identityLink.getType().equals(IdentityLinkType.CANDIDATE) && identityLink.getUserId().equals(userId)) {
                return true;
            }
        }
        return false;
    }

    private String getUserName(String userId) {
        if (CommonUtil.isEmptyStr(userId)) {
            return null;
        }
        User user = identityService.createUserQuery().userId(userId).singleResult();
        if (user != null) {
            return user.getFirstName();
        }
        return null;
    }

    private List<String> getInvolvedUsers(String taskId) {
        List<HistoricIdentityLink> idLinks = historyService.getHistoricIdentityLinksForTask(taskId);
        List<String> result = new ArrayList<>(idLinks.size());

        for (HistoricIdentityLink link : idLinks) {
            // Only include users and non-assignee links
            if (link.getUserId() != null && !IdentityLinkType.ASSIGNEE.equals(link.getType())) {
                result.add(link.getUserId());
            }
        }
        return result;
    }

    private void populateAssignee(TaskInfo task, TaskResponse rep) {
        if (task.getAssignee() != null) {
            rep.setAssignee(task.getAssignee());
        }
    }

    @Override
    public Task getTaskNotNull(String taskId) {
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (task == null) {
            throw new FlowableObjectNotFoundException("Task with id: " + taskId + " does not exist");
        }
        return task;
    }

    @Override
    public HistoricTaskInstance getHistoricTaskInstanceNotNull(String taskId) {
        HistoricTaskInstance task = historyService.createHistoricTaskInstanceQuery().taskId(taskId).singleResult();
        if (task == null) {
            throw new FlowableObjectNotFoundException("Task with id: " + taskId + " does not exist");
        }
        return task;
    }

    @Override
    public void addComment(String taskId, String processInstanceId, String userId, CommentTypeEnum type,
                           String message) {
        Authentication.setAuthenticatedUserId(userId);
        type = type == null ? CommentTypeEnum.SP : type;
        message = (message == null || message.length() == 0) ? type.getName() : message;
        taskService.addComment(taskId, processInstanceId, type.toString(), message);
    }

    @Override
    public List<Comment> getComments(String taskId, String processInstanceId, String type, String userId) {
        List<Comment> comments = null;
        if (type == null || type.length() == 0) {
            // 以taskId为优先
            if (taskId != null && taskId.length() > 0) {
                comments = taskService.getTaskComments(taskId);
            } else if (processInstanceId != null && processInstanceId.length() > 0) {
                comments = taskService.getProcessInstanceComments(processInstanceId);
            } else {
                throw new FlowableIllegalArgumentException("taskId processInstanceId type are all empty");
            }
        } else {
            // 以taskId为优先
            if (taskId != null && taskId.length() > 0) {
                comments = taskService.getTaskComments(taskId, type);
            } else if (processInstanceId != null && processInstanceId.length() > 0) {
                comments = taskService.getProcessInstanceComments(processInstanceId, type);
            } else {
                comments = taskService.getCommentsByType(type);
            }
        }
        if (userId != null && userId.length() > 0 && comments != null && comments.size() > 0) {
            comments =
                    comments.stream().filter(comment -> userId.equals(comment.getUserId())).collect(Collectors.toList());
        }
        return comments;
    }

    private void validateIdentityLinkArguments(List<String> identityIds, String identityType) {
        if (identityIds == null || identityIds.size() == 0) {
            throw new FlowableIllegalArgumentException("identityId is null");
        }
        if (!FlowableConstant.IDENTITY_GROUP.equals(identityType) && !FlowableConstant.IDENTITY_USER.equals(identityType)) {
            throw new FlowableIllegalArgumentException("type must be group or user");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveTaskIdentityLink(IdentityRequest taskIdentityRequest) {
        Task task = getTaskNotNull(taskIdentityRequest.getTaskId());
        validateIdentityLinkArguments(taskIdentityRequest.getIdentityIds(), taskIdentityRequest.getIdentityType());
        if (FlowableConstant.IDENTITY_GROUP.equals(taskIdentityRequest.getIdentityType())) {
//            taskService.addGroupIdentityLink(task.getId(), taskIdentityRequest.getIdentityId(),
//                    IdentityLinkType.CANDIDATE);
        } else if (FlowableConstant.IDENTITY_USER.equals(taskIdentityRequest.getIdentityType())) {
            for (String userName : taskIdentityRequest.getIdentityIds()) {
                taskService.addUserIdentityLink(task.getId(), userName,
                        IdentityLinkType.CANDIDATE);
            }

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTaskIdentityLink(String taskId, String identityId, String identityType) {
        Task task = getTaskNotNull(taskId);
        //validateIdentityLinkArguments(identityId, identityType);
        if (FlowableConstant.IDENTITY_GROUP.equals(identityType)) {
            taskService.deleteGroupIdentityLink(task.getId(), identityId, IdentityLinkType.CANDIDATE);
        } else if (FlowableConstant.IDENTITY_USER.equals(identityType)) {
            taskService.deleteUserIdentityLink(task.getId(), identityId, IdentityLinkType.CANDIDATE);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void readTask(TaskRequest taskRequest) {
        String[] taskIds = taskRequest.getTaskIds();
        if (taskIds == null || taskIds.length == 0) {
            throw new FlowableException("taskIds is null or empty");
        }
        String userId = SecurityUtils.getUser().getUsername();
        for (String taskId : taskIds) {
            managementService.executeCommand(new CompleteTaskReadCmd(taskId, userId));
        }
    }

    /**
     * 查询未催办的信息
     *
     * @param proInsId
     * @return
     */
    @Override
    public TaskResponse queryNoTaskByQuery(String proInsId) {
        HistoricTaskInstanceQuery query = historyService.createHistoricTaskInstanceQuery();
        query.processInstanceId(proInsId);
        List<HistoricTaskInstance> list = query.list();
        if (list.isEmpty()) {
            return null;
        }
        TaskListWrapper bean = SpringContextUtils.getBean(TaskListWrapper.class);
        List<TaskResponse> taskResponseList = bean.execute(list);
        List<TaskResponse> todoList = taskResponseList.stream().filter(t -> t.getStatus().equals("todo")).collect(Collectors.toList());
        if (todoList.isEmpty()) {
            return null;
        }
        return todoList.get(0);
    }

    @Override
    public void addCommentByType(@Valid UrgeDto dto, Task task, ProcessInstance processInstance, HashMap<String, String> templateParam, CommentTypeEnum commentTypeEnum, String title, String templateCode) {
        List<String> users = new ArrayList<>();
        if (StringUtils.isNotBlank(dto.getUsername())) {
            users.add(dto.getUsername());
        } else if (dto.getCandidates() != null && dto.getCandidates().size() > 0) {
            users.addAll(dto.getCandidates());
        }
        iSysBaseAPI.sendNoticeByProcess(templateParam, templateCode, users);
        LoginUser user = (LoginUser) org.apache.shiro.SecurityUtils.getSubject().getPrincipal();
        addComment(dto.getTaskId(),
                processInstance.getProcessInstanceId(),
                user.getUsername(),
                commentTypeEnum,
                dto.getMsg()
        );
    }

    /**
     * 催办
     *
     * @param dto
     */
    @Override
    public void urge(UrgeDto dto) {
        Task task = taskService.createTaskQuery().taskId(dto.getTaskId()).singleResult();
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(task.getProcessInstanceId()).singleResult();
        HashMap<String, String> templateParam = new HashMap<>(5);
        templateParam.put("bpm_name", processInstance.getName());
        templateParam.put("bpm_task", task.getName());
        templateParam.put("datetime", simpleDateFormatThreadLocal.get().format(task.getCreateTime()));
        templateParam.put("remark", dto.getMsg());
        templateParam.put("delayTime", String.valueOf(DateUtil.between(task.getCreateTime(), new Date(), DateUnit.MINUTE)));
        Object valueByKey = cacheUtils.getValueByKey(CommonConstant.DATA_DICT_KEY + "processNotification_urge");
        if (valueByKey == null) {
            throw new FlowableException("未配置[processNotification_urge]催办通知模版参数，请联系管理员！");
        }
        addCommentByType(dto, task, processInstance, templateParam, CommentTypeEnum.CB, "催办通知", valueByKey.toString());
        taskService.setVariable(task.getId(), "urge", "1");

    }

    @Override
    public UserTask getNextTask(HistoricTaskInstance task, Map<String, Object> variables) throws PropertyNotFoundException {
        UserTask userTask = null;
        //当前任务信息
        if (task != null) {
            //获取流程发布Id信息
            String definitionId = runtimeService.createProcessInstanceQuery().processInstanceId(task.getProcessInstanceId()).singleResult().getProcessDefinitionId();
            //获取bpm对象
            BpmnModel bpmnModel = repositoryService.getBpmnModel(definitionId);
            //传节点定义key 获取当前节点
            FlowNode flowNode = (FlowNode) bpmnModel.getFlowElement(task.getTaskDefinitionKey());
            //输出连线
            List<SequenceFlow> outgoingFlows = flowNode.getOutgoingFlows();
            //遍历返回下一个节点信息
            for (SequenceFlow outgoingFlow : outgoingFlows) {
                //类型自己判断
                FlowElement targetFlowElement = outgoingFlow.getTargetFlowElement();
                //用户任务
                if (targetFlowElement instanceof UserTask) {
                    userTask = (UserTask) targetFlowElement;
                    break;
                } else if (targetFlowElement instanceof EndEvent) {
                    return null;
                } else {
                    Map<String, Object> proInsMap = runtimeService.getVariables(task.getProcessInstanceId());
                    if (variables != null) {
                        proInsMap.putAll(variables);
                    }
                    return setExclusiveGateway(targetFlowElement, proInsMap);
                }
            }
            return userTask;
        } else {
            return null;
        }
    }

    @Override
    @Transactional
    public void withdraw(HistoricTaskInstance task, HistoricActivityInstance historicActivityInstance) {
        String userId = SecurityUtils.getUser().getUsername();
        //添加记录
        this.addComment(historicActivityInstance.getTaskId(), historicActivityInstance.getProcessInstanceId(), userId, CommentTypeEnum.CH,
                historicActivityInstance.getActivityName() + "，撤回到" + task.getName());
        //执行退回操作
        managementService.executeCommand(new BackUserTaskCmd(runtimeService,
                historicActivityInstance.getTaskId(), task.getTaskDefinitionKey()));
        //退回完成查询撤回后的任务 设置处理人
        Task task1 = taskService.createTaskQuery().taskDefinitionKey(task.getTaskDefinitionKey())
                .processInstanceId(task.getProcessInstanceId())
                .orderByTaskCreateTime()
                .desc()
                .singleResult();
        taskService.setAssignee(task1.getId(), task.getAssignee());
        //删除消息通知
        iSysBaseAPI.deleteAnnouncementByBusId(historicActivityInstance.getTaskId());
    }

    /**
     * 拒单
     *
     * @param taskRequest
     */
    @Override
    public void rejectTask(TaskRequest taskRequest) {
        String taskId = taskRequest.getTaskId();
        //根据任务id获取任务信息
        Task nowTask = taskService.createTaskQuery().taskId(taskId).singleResult();
        String userId = SecurityUtils.getUser().getUsername();
        //根据当前登录id查询所属服务商
        String assignee = commonListenerService.getRejectNextUserByUserId(nowTask.getProcessInstanceId(), userId);
        if (!StringUtils.isEmpty(assignee)) {
            Task task = permissionService.validateAssignPermissionOnTask(taskId, userId, assignee);
            this.addComment(taskId, task.getProcessInstanceId(), userId, CommentTypeEnum.JD, "拒单自动分配下一处理人");
            taskService.setAssignee(task.getId(), assignee);
        } else {
            //无下一处理人拒单到上一节点
            List<HistoricTaskInstance> historicTasks = historyService.createHistoricTaskInstanceQuery()
                    .processInstanceId(nowTask.getProcessInstanceId())
                    .orderByHistoricTaskInstanceEndTime().desc()
                    .list();
            if (!historicTasks.isEmpty()) {
                HistoricTaskInstance previousTask = historicTasks.get(0);
                //添加记录
                this.addComment(taskId, nowTask.getProcessInstanceId(), userId, CommentTypeEnum.JD,
                        "未设置下一处理人，拒单回退到上一节点处理人");
                managementService.executeCommand(new BackUserTaskCmd(runtimeService,
                        taskId, previousTask.getTaskDefinitionKey()));
                Task task1 = taskService.createTaskQuery().taskDefinitionKey(previousTask.getTaskDefinitionKey())
                        .processInstanceId(previousTask.getProcessInstanceId())
                        .orderByTaskCreateTime()
                        .desc()
                        .singleResult();
                taskService.setAssignee(task1.getId(), previousTask.getAssignee());
            }
        }
        //添加拒单记录
        commonListenerService.addRejectTaskRecord(nowTask.getId(), nowTask.getProcessInstanceId(), userId);
    }

    private UserTask setExclusiveGateway(FlowElement targetFlow, Map proInsMap)  throws PropertyNotFoundException {

        List<SequenceFlow> targetFlows = ((Gateway) targetFlow).getOutgoingFlows();
        for (SequenceFlow sequenceFlow : targetFlows) {
            if (checkFormDataByRuleEl(sequenceFlow.getConditionExpression(), proInsMap)) {
                //目标节点信息
                FlowElement targetFlowElement = sequenceFlow.getTargetFlowElement();

                if (targetFlowElement instanceof UserTask) {
                    return (UserTask) targetFlowElement;
                } else if (targetFlowElement instanceof ServiceTask) {
                    return (UserTask) targetFlowElement;//不一定对 按照UserTask 来
                } else if (targetFlowElement instanceof Gateway) {
                    //递归寻找
                    setExclusiveGateway(targetFlowElement, proInsMap);
                } else {
                    return null;
                }
            }
        }
        return null;
    }

    private Boolean checkFormDataByRuleEl(String el, Map<String, Object> formData)  throws PropertyNotFoundException {
        ExpressionFactory factory = new ExpressionFactoryImpl();
        SimpleContext context = new SimpleContext();
        for (Object k : formData.keySet()) {
            if (!ObjectUtils.isEmpty(formData.get(k))) {
                context.setVariable(k.toString(), factory.createValueExpression(formData.get(k), formData.get(k).getClass()));
            }
        }

        ValueExpression e = factory.createValueExpression(context, el, Boolean.class);
        return (Boolean) e.getValue(context);
    }

}
