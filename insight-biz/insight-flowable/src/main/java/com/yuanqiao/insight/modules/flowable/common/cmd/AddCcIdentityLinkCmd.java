package com.yuanqiao.insight.modules.flowable.common.cmd;

import com.yuanqiao.insight.modules.flowable.constant.FlowableConstant;
import com.yuanqiao.insight.modules.flowable.vo.CcToVo;
import org.flowable.common.engine.api.FlowableIllegalArgumentException;
import org.flowable.common.engine.api.FlowableObjectNotFoundException;
import org.flowable.common.engine.impl.interceptor.Command;
import org.flowable.common.engine.impl.interceptor.CommandContext;
import org.flowable.engine.impl.persistence.entity.CommentEntity;
import org.flowable.engine.impl.persistence.entity.CommentEntityManager;
import org.flowable.engine.impl.persistence.entity.ExecutionEntity;
import org.flowable.engine.impl.persistence.entity.ExecutionEntityManager;
import org.flowable.engine.impl.util.CommandContextUtil;
import org.flowable.engine.impl.util.IdentityLinkUtil;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.SpringContextUtils;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class AddCcIdentityLinkCmd implements Command<Void>, Serializable {
    private static final long serialVersionUID = 1L;
    protected String processInstanceId;
    protected String taskId;
    protected String userId;
    protected CcToVo[] ccToVos;
    protected String[] ccUserIds;

    public AddCcIdentityLinkCmd(String processInstanceId, String taskId, String userId, String[] ccUserIds) {
        validateParams(processInstanceId, taskId, userId, ccUserIds);
        this.processInstanceId = processInstanceId;
        this.taskId = taskId;
        this.userId = userId;
        this.ccToVos = ccToVos;
        this.ccUserIds = ccUserIds;
    }

    protected void validateParams(String processInstanceId, String taskId, String userId, String[] ccUserIds) {
        if (processInstanceId == null) {
            throw new FlowableIllegalArgumentException("processInstanceId is null");
        }
        if (taskId == null) {
            throw new FlowableIllegalArgumentException("taskId is null");
        }
        if (userId == null) {
            throw new FlowableIllegalArgumentException("userId is null");
        }
        if (ccUserIds == null || ccUserIds.length == 0) {
            throw new FlowableIllegalArgumentException("ccUserIds is null or empty");
        }
    }

    @Override
    public Void execute(CommandContext commandContext) {
        ExecutionEntityManager executionEntityManager = CommandContextUtil.getExecutionEntityManager(commandContext);
        ExecutionEntity processInstance = executionEntityManager.findById(processInstanceId);
        if (processInstance == null) {
            throw new FlowableObjectNotFoundException("Cannot find process instance with id " + processInstanceId,
                    ExecutionEntity.class);
        }
        for (String userId : ccUserIds) {
            IdentityLinkUtil.createProcessInstanceIdentityLink(processInstance, userId, null, FlowableConstant.CC);
        }
        this.createCcComment(commandContext);
        return null;

    }

    protected void createCcComment(CommandContext commandContext) {
        CommentEntityManager commentEntityManager = CommandContextUtil.getCommentEntityManager(commandContext);
        CommentEntity comment = commentEntityManager.create();
        comment.setProcessInstanceId(processInstanceId);
        comment.setUserId(userId);
        comment.setType(FlowableConstant.CC);
        comment.setTime(CommandContextUtil.getProcessEngineConfiguration(commandContext).getClock().getCurrentTime());
        comment.setTaskId(taskId);
        comment.setAction("AddCcTo");
        ISysBaseAPI iSysBaseAPI = SpringContextUtils.getBean(ISysBaseAPI.class);
        List<LoginUser> loginUsers = iSysBaseAPI.queryUserByNames(ccUserIds);
        List<String> realNames = loginUsers.stream().map(LoginUser::getRealname).collect(Collectors.toList());
        String ccToStr = "抄送给：" + String.join(",", realNames);
        comment.setMessage(ccToStr);
        comment.setFullMessage(ccToStr);
        commentEntityManager.insert(comment);
    }

}
