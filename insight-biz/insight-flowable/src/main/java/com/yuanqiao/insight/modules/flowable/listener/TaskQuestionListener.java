package com.yuanqiao.insight.modules.flowable.listener;

import com.yuanqiao.insight.common.core.util.ObjectUtils;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;
import org.jeecg.common.system.api.CommonListenerService;
import org.jeecg.common.util.SpringContextUtils;

import java.util.Map;

/**
 * 创建我的问题功能
 */
public class TaskQuestionListener implements TaskListener {


    @Override
    public void notify(DelegateTask delegateTask) {
        CommonListenerService commonListenerService = SpringContextUtils.getBean(CommonListenerService.class);

        Map<String, Object> variables = delegateTask.getVariables();
        if (ObjectUtils.isNotEmpty(variables.get("alarmId"))) {
          return;
        }
        variables.put("processInstanceId", delegateTask.getProcessInstanceId());
        commonListenerService.saveQuestion(variables);
    }
}
