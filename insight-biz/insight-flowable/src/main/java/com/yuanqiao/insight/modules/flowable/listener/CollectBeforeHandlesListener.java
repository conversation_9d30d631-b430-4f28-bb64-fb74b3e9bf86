package com.yuanqiao.insight.modules.flowable.listener;

import org.flowable.engine.HistoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.service.delegate.DelegateTask;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.stereotype.Component;

import java.util.LinkedHashSet;

/**
 * 获取该节点之前所有处理人的账号 添加到流程变量中 <br>
 * 变量名称:handles
 */
@Component
public class CollectBeforeHandlesListener implements TaskListener {

    @Override
    public void notify(DelegateTask delegateTask) {
        RuntimeService runtimeService = SpringContextUtils.getBean(RuntimeService.class);
        HistoryService historyService = SpringContextUtils.getBean(HistoryService.class);

//        historyService.getHistoricIdentityLinksForProcessInstance(delegateTask.getProcessInstanceId());

        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(delegateTask.getProcessInstanceId()).singleResult();
        LinkedHashSet<String> handlers = new LinkedHashSet<>();
        handlers.add(processInstance.getStartUserId());
        historyService.createHistoricTaskInstanceQuery().processInstanceId(delegateTask.getProcessInstanceId()).list().stream()
                .filter(historicTaskInstance -> !historicTaskInstance.getId().equals(delegateTask.getId()))
                .forEach(historicTaskInstance -> handlers.add(historicTaskInstance.getAssignee()));
        runtimeService.setVariable(delegateTask.getProcessInstanceId(), "handles", handlers);
    }

}
