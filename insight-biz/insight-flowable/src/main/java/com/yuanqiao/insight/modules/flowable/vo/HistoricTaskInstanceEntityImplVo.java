package com.yuanqiao.insight.modules.flowable.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.flowable.task.service.impl.persistence.entity.HistoricTaskInstanceEntityImpl;
import org.jeecg.common.aspect.annotation.Dict;

import java.util.Date;

@Data
@ApiModel(value = "流程实例任务信息")
public class HistoricTaskInstanceEntityImplVo {
     @ApiModelProperty(value ="任务id")
     private String  taskId;

     @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
     @ApiModelProperty(value ="任务完成时间")
     private Date taskEndTime;
     @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
     @ApiModelProperty(value ="任务到期时间")
     private Date taskDueTime;
     @ApiModelProperty(value ="任务名称")
     private String taskName;
     @ApiModelProperty(value ="到期时间")
     protected String overTime;
     @ApiModelProperty(value ="sla类型")
     private String slaType;

     @ApiModelProperty(value ="实例id")
     private String  id;
     @ApiModelProperty(value ="实例名称")
     private String  name;
     @ApiModelProperty(value ="定义key")
     private String  processDefinitionKey;
     @ApiModelProperty(value ="定义名称")
     private String  processDefinitionName;
     @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
     @ApiModelProperty(value ="开始时间")
     private Date  startTime;
     @ApiModelProperty(value ="开始人")
     private String  startUserId;
     @ApiModelProperty(value ="开始人名称")
     private String  startUserName;

}
