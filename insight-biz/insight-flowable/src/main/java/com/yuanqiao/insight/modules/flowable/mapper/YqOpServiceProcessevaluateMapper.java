package com.yuanqiao.insight.modules.flowable.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.modules.flowable.entity.YqOpServiceProcessevaluate;

import java.util.List;


/**
 * @Description: 流程服务满意度评价
 * @Author: jeecg-boot
 * @Date:   2023-09-21
 * @Version: V1.0
 */
public interface YqOpServiceProcessevaluateMapper extends BaseMapper<YqOpServiceProcessevaluate> {
    void evaluateById(YqOpServiceProcessevaluate yqServiceEvaluate);

    IPage<YqOpServiceProcessevaluate> page(Page page, YqOpServiceProcessevaluate entity);

    Integer getEvaluateSum(List<String> userNames, List<String> attitudes);
}
