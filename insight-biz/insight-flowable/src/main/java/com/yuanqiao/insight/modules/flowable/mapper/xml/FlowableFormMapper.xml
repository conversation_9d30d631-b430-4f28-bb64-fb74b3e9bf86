<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuanqiao.insight.modules.flowable.mapper.FlowableFormMapper">

    <!-- 多表查询:根据条件得到多条记录List(查询条件按需修改!) -->
    <select id="list" resultType="com.yuanqiao.insight.modules.flowable.entity.FlowableForm">
        select
        a.FORM_KEY as formKey,
        a.FORM_NAME as formName,
        a.CREATE_BY as createBy,
        a.CREATE_DATE as createDate,
        a.CREATE_TIME as createTime,
        a.UPDATE_BY as updateBy,
        a.UPDATE_DATE as updateDate,
        a.UPDATE_TIME as updateTime

        from T_FLOWABLE_FORM a
        where 1=1
        <if test="entity.formKey != null and entity.formKey !=''">
            <![CDATA[	AND a.FORM_KEY = #{entity.formKey}	]]>
        </if>
        <if test="entity.formName != null and entity.formName !=''">
            <![CDATA[	AND a.FORM_NAME = #{entity.formName}	]]>
        </if>
        order by a.CREATE_TIME desc
    </select>

    <update id="updateWithFormKey">
UPDATE T_FLOWABLE_FORM SET form_name = #{entity.formName},
form_json = #{entity.formJson},
model_type = #{entity.modelType},
update_by = #{entity.updateBy},
update_time = #{entity.updateTime}
where form_key = #{entity.formKey}
    </update>
</mapper>
