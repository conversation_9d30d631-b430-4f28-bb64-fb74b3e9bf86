package com.yuanqiao.insight.modules.flowable.job;

import com.alibaba.druid.util.StringUtils;
import com.yuanqiao.insight.common.constant.CommonConstant;
import com.yuanqiao.insight.common.util.common.LocalCacheUtils;
import com.yuanqiao.insight.modules.flowable.common.CommentTypeEnum;
import com.yuanqiao.insight.modules.flowable.constant.FlowableConstant;
import com.yuanqiao.insight.modules.flowable.service.FlowableTaskService;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.jeecg.common.system.api.CommonListenerService;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.LoginUser;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 指定时间内未接单自动分配工程师
 */
@Component
@Slf4j
public class AutoAssignTimeOutJob implements Job {
    private final LocalCacheUtils cacheUtils = LocalCacheUtils.getInstance();

    @Autowired
    private ISysBaseAPI iSysBaseAPI;
    @Autowired
    protected TaskService taskService;
    @Autowired
    private FlowableTaskService flowableTaskService;
    @Autowired
    CommonListenerService commonListenerService;

    /**
     * 工程师在指定的时间内未接单，
     * 自动派给该服务商的第二个工程师。第二个工程师在指定时间内未接单，
     * 自动派给第二个服务商的工程师。
     * 在这个过程中，如果没有第二个工程师，
     * 直接选下个服务商的工程师，
     * 如果没有下个服务商了，
     * 再从第一个服务商的第一个工程师重新指派。
     *
     * @param jobExecutionContext
     * @throws JobExecutionException
     */
    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        Object parameter = jobExecutionContext.getMergedJobDataMap().get("parameter");
        if (Objects.isNull(parameter)) {
            log.error("获取指定时间内未接单自动分配工程师参数");
            parameter="10";
        }
        log.info("指定时间内未接单自动分配工程师时间间隔：{}", parameter);
       //获取流程节点
        Object valueByKey1 = cacheUtils.getValueByKey(CommonConstant.DATA_DICT_KEY + "ProcessNoHandleTimeOut_processNodeId");
        if (Objects.isNull(valueByKey1)) {
            log.error("配置字段中获取流程节点失败");
            return;
        }
        //流程节点
        String  processNodeId = valueByKey1.toString();
        //根据流程编码和节点信息查询当前任务是否超出parameter时间
        TaskQuery taskQuery = taskService.createTaskQuery().taskDefinitionKey(processNodeId).taskCategory(FlowableConstant.CATEGORY_TODO);
        taskQuery.orderByTaskCreateTime().asc().includeProcessVariables();
        List<Task> list = taskQuery.list();
        if (Objects.isNull(list)|| list.isEmpty()) {
            log.warn("指定时间内未接单自动分配工程师数量：{}", 0);
            return;
        }
        String finalParameter = parameter.toString();
        log.info("接单节点数量：{}", list.size());
        for (Task task : list) {
            //获取任务创建时间
            Object o = task.getProcessVariables().get("taskNewTime");
            if (Objects.isNull(o)) {
                o=task.getCreateTime();
            }else{
                //比较创建时间是否大于taskNewTime   当任务重新到了接单节点创建时间会发生变化
                Date date = task.getCreateTime();
                if (date.after((Date) o)) {
                    o = task.getCreateTime();
                }
            }
            //判断是否超时
            if (!isAfterSpecifiedTimePlusMinutes((Date) o, Integer.parseInt(finalParameter))){
                continue;
            }
            //下个用户
            String nextUser ="";
            //获取单位id
            Object userUnit =  task.getProcessVariables().get("userUnit");
            if (Objects.isNull(userUnit)) {
                continue;
            }
            //若为空分配给该工程师服务商的下一个工程师  若当前服务商无工程师 则通过部门查询下一个服务商
            HashMap<String, Object> hashMap = commonListenerService.getUserByTaskIdAndUser(task.getId(),task.getAssignee(),userUnit.toString());
            nextUser = (String) hashMap.get("user");
            if ((boolean)hashMap.get("isDelete")&&StringUtils.isEmpty(nextUser)){
                //isDelete为true说明第一轮循环结束 删除记录 并获取第一个处理人
                nextUser = commonListenerService.getOriginallyUserByTransfer(task.getId());
                commonListenerService.delTaskTimeOut(task.getId());

            }else if (StringUtils.isEmpty(nextUser)){
                continue;
            }else{
                //添加超时分配记录
                commonListenerService.addTaskTimeOut(task.getId(), task.getAssignee(),userUnit.toString());
            }
            //设置任务新时间
            taskService.setVariable(task.getId(),"taskNewTime",new Date());

            //获取工程师
            List<String> users = new ArrayList<>();
            users.add(nextUser);
            users.add(task.getAssignee());
            String assigneeName = "";
            String nextUserName = "";
            List<LoginUser> userByNames = iSysBaseAPI.getLoginUserByNames(users);
            for (LoginUser loginUser : userByNames) {
                if (loginUser.getUsername().equals(nextUser)) {
                    nextUserName = loginUser.getRealname();
                }
                if (loginUser.getUsername().equals(task.getAssignee())) {
                    assigneeName = loginUser.getRealname();
                }
            }
            //添加超期记录
            flowableTaskService.addComment(task.getId(), task.getProcessInstanceId(), "", CommentTypeEnum.CSJD, assigneeName+"接单超时"+parameter+"分钟，系统自动派送给"+nextUserName+"工程师");
            //设置任务指派
            taskService.setAssignee(task.getId(), nextUser);

        }


    }

    /**
     * 判断指定的Date对象是否超过了当前时间加上指定的分钟数
     *
     * @param date 要判断的Date对象
     * @param minutes 要加上的分钟数
     * @return 如果超过了，则返回true；否则返回false
     */
    public static boolean isAfterSpecifiedTimePlusMinutes(Date date, int minutes) {
        long currentTimeMillis = System.currentTimeMillis();
        long specifiedTimeMillis = date.getTime();
        long minutesInMillis = (long) minutes * 60 * 1000; // 将分钟转换为毫秒

        // 计算指定时间加上指定分钟数后的毫秒数
        long futureTimeMillis = specifiedTimeMillis + minutesInMillis;

        // 判断当前时间是否超过了这个未来时间
        return currentTimeMillis > futureTimeMillis;
    }

}
