package com.yuanqiao.insight.modules.flowable.util;

import com.yuanqiao.insight.common.core.util.DateUtil;
import org.jeecg.common.util.SpringContextUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Date;

public class OverdueTimeUtils {

    public static String getOverdueTimeStr() throws ClassNotFoundException, NoSuchMethodException, InvocationTargetException, IllegalAccessException{
            Class<?> configureDictMapper = Class.forName("org.jeecg.modules.system.mapper.ConfigureDictMapper");
            Object bean = SpringContextUtils.getApplicationContext().getBean(configureDictMapper);
            Method method = configureDictMapper.getMethod("queryDictValueByCodeAndText", String.class, String.class);
            String timeStr = (String) method.invoke(bean, "processOverdueTimeSet", "processOverdueTimeValue");
            int overdueTime = Integer.parseInt(timeStr);
            overdueTime = -overdueTime;//取负数
            Date date = DateUtil.addHour(null, overdueTime);
            timeStr = DateUtil.dateToStrTime(date);
            return timeStr;
    }
}
