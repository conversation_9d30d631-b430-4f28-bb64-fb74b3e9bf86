package com.yuanqiao.insight.modules.flowable.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020年3月24日
 */
@Data
public class HistoricProcessInstanceResponse {
    protected String id;
    protected String name;
    private String businessKey;
    protected String processDefinitionId;
    private String processDefinitionName;
    private String processDefinitionKey;
    private Integer processDefinitionVersion;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
    private Long durationInMillis;
    private String startUserId;
    private String startUserName;
    private String startActivityId;
    private String superProcessInstanceId;
    protected String tenantId;
    private String currTaskName;
    private boolean suspended;
    /**
     * 状态 0草稿默认 1处理中 2结束
     */
    private Integer status;
    /**
     * 结果状态 0未提交默认 1处理中 2通过 3驳回
     */
    private Integer result;

    /**
     * 申请单位---内蒙
     */
    private String unitName;

    /**
     * 申请人 ----内蒙
     */
    private String contactUserName;
}
