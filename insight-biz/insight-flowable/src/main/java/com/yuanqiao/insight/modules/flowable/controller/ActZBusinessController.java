package com.yuanqiao.insight.modules.flowable.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.modules.flowable.entity.ActZBusiness;
import com.yuanqiao.insight.modules.flowable.entity.ActZBusinessParam;
import com.yuanqiao.insight.modules.flowable.service.ActAssociationService;
import com.yuanqiao.insight.modules.flowable.service.IActZBusinessService;
import com.yuanqiao.insight.modules.flowable.service.ProcessDefinitionService;
import com.yuanqiao.insight.modules.flowable.service.ProcessInstanceService;
import com.yuanqiao.insight.modules.flowable.util.ESClient;
import com.yuanqiao.insight.modules.flowable.vo.ProcessInstanceRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.flowable.common.engine.api.FlowableException;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.repository.ProcessDefinition;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description: 流程业务草稿表
 * @Author: wyx
 * @Date: 2022-06-27
 * @Version: V1.0
 */
@Api(tags = "流程业务草稿表")
@RestController
@RequestMapping("/business/actZBusiness")
@Slf4j
public class ActZBusinessController extends JeecgController<ActZBusiness, IActZBusinessService> {
    @Autowired
    private IActZBusinessService actZBusinessService;
    @Autowired
    protected RepositoryService repositoryService;
    @Autowired
    protected TaskService taskService;

    @Autowired
    ProcessDefinitionService processDefinitionService;
    @Autowired
    protected ProcessInstanceService processInstanceService;

    @Autowired
    protected RuntimeService runtimeService;
    @Autowired
    protected HistoryService historyService;

    @Autowired
    protected ESClient esClient;
    @Autowired
    protected ActAssociationService actAssociationService;

    /**
     * 分页列表查询
     *
     * @param actZBusiness
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "流程业务草稿表-分页列表查询")
    @ApiOperation(value = "流程业务草稿表-分页列表查询", notes = "流程业务草稿表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<ActZBusiness>> queryPageList(ActZBusiness actZBusiness,
                                                     @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                     @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                     HttpServletRequest req) {
        QueryWrapper<ActZBusiness> queryWrapper = QueryGenerator.initQueryWrapper(actZBusiness, req.getParameterMap());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        queryWrapper.eq("create_by", sysUser.getUsername());
        queryWrapper.eq("del_flag", "0");
        Page<ActZBusiness> page = new Page<ActZBusiness>(pageNo, pageSize);
        IPage<ActZBusiness> pageList = actZBusinessService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    @AutoLog(value = "流程业务-提交申请")
    @ApiOperation(value = "流程业务-提交申请", notes = "流程业务-提交申请")
    @PostMapping(value = "/sbApply")
    public Result sbApply(@RequestBody ActZBusinessParam actZBusinessParam) {
        ActZBusiness actZBusiness = actZBusinessService.getById(actZBusinessParam.getId());
        if (actZBusiness == null) {
            return Result.error("actBusiness表中该id不存在");
        }
        actZBusinessService.sbApply(actZBusinessParam,actZBusiness);
        return Result.ok("提交成功");
    }

    @AutoLog(value = "流程业务-提交申请并保存")
    @ApiOperation(value = "流程业务-提交申请并保存", notes = "流程业务-提交申请并保存")
    @PostMapping(value = "/saveAndApply")
    public Result<?> saveAndApply(@RequestBody ActZBusinessParam actZBusinessParam) {
        ActZBusiness actZBusiness = actZBusinessService.getById(actZBusinessParam.getId());
        if (actZBusiness == null) {
            return Result.error("actBusiness表中该id不存在");
        }
        actZBusinessService.saveAndApply(actZBusinessParam,actZBusiness);
        return Result.ok("提交成功");

    }

    @AutoLog(value = "流程业务-撤回")
    @ApiOperation(value = "流程业务-撤回", notes = "流程业务-撤回")
    @PostMapping(value = "/cancel")
    public Result cancel(@RequestBody ActZBusinessParam actZBusinessParam) {
        actZBusinessService.cancel(actZBusinessParam, false);
        return Result.ok("撤回成功");
    }

    /**
     * 添加
     *
     * @param actZBusinessParam
     * @return
     */
    @AutoLog(value = "流程业务草稿表-添加")
    @ApiOperation(value = "流程业务草稿表-添加", notes = "流程业务草稿表-添加")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody ActZBusinessParam actZBusinessParam) {
        ActZBusiness actZBusiness = new ActZBusiness();
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(actZBusinessParam.getProcDefId()).singleResult();
        if (processDefinition == null) {
            throw new FlowableException("Process definition id is not found ");
        }
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //JSONObject jsonObject = JSON.parseObject(actZBusinessParam.getFormValue());
        String formKey = actZBusinessParam.getFormKey();
        if (formKey.contains(".form")) {
            int index = formKey.lastIndexOf(".");
            formKey = formKey.substring(0, index);
        }
        actZBusiness.setFormKey(formKey);
        actZBusiness.setFormValue(actZBusinessParam.getFormValue());
        actZBusiness.setCreateBy(sysUser.getUsername());
        actZBusiness.setCreateTime(new Date());
        actZBusiness.setProcDefId(actZBusinessParam.getProcDefId());
        actZBusiness.setProcessDefVersion(actZBusinessParam.getProcVersion());
        actZBusiness.setProcessType(actZBusinessParam.getCategory());
        actZBusiness.setProcessName(processDefinition.getName());

        actZBusinessService.save(actZBusiness);
        return Result.OK("暂存成功！");
    }

    @AutoLog(value = "流程业务草稿表-添加")
    @ApiOperation(value = "流程业务草稿表-添加", notes = "流程业务草稿表-添加")
    @PostMapping(value = "/start")
    public Result<?> start(@RequestBody ProcessInstanceRequest request) throws IOException {
        String start = actZBusinessService.start(request);
        if (StringUtils.isEmpty(start)){
            return Result.error("发起失败");
        }
        return Result.ok("发起成功");
    }


    /**
     * 编辑
     *
     * @param actZBusinessParam
     * @return
     */
    @AutoLog(value = "流程业务草稿表-编辑")
    @ApiOperation(value = "流程业务草稿表-编辑", notes = "流程业务草稿表-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody ActZBusinessParam actZBusinessParam) {
        ActZBusiness actZBusiness = actZBusinessService.getById(actZBusinessParam.getId());
        actZBusiness.setFormValue(actZBusinessParam.getFormValue());
        actZBusinessService.updateById(actZBusiness);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "流程业务草稿表-通过id删除")
    @ApiOperation(value = "流程业务草稿表-通过id删除", notes = "流程业务草稿表-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        actZBusinessService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "流程业务草稿表-批量删除")
    @ApiOperation(value = "流程业务草稿表-批量删除", notes = "流程业务草稿表-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.actZBusinessService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "流程业务草稿表-通过id查询")
    @ApiOperation(value = "流程业务草稿表-通过id查询", notes = "流程业务草稿表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        ActZBusiness actZBusiness = actZBusinessService.getBusinessById(id);
        if (actZBusiness == null) {
            return Result.error("未找到对应数据");
        }
        String jsonStr = actZBusiness.getFormValue();
        // jsonString转换成Map
        Map<String, Object> jsonMap = JSON.parseObject(jsonStr, new TypeReference<HashMap<String, Object>>() {
        });
        actZBusiness.setVariables(jsonMap);

        return Result.OK(actZBusiness);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param actZBusiness
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ActZBusiness actZBusiness) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        actZBusiness.setCreateBy(sysUser.getUsername());
        return super.exportXls(request, actZBusiness, ActZBusiness.class, "流程业务");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ActZBusiness.class);
    }

}
