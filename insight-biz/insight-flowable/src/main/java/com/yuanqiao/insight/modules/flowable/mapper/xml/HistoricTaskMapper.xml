<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.modules.flowable.mapper.HistoricTaskMapper">
    <insert id="insertHistoricTask" parameterType="org.flowable.task.service.impl.persistence.entity.HistoricTaskInstanceEntityImpl">
        insert into ACT_HI_TASKINST (
            ID_,
            REV_,
            TASK_DEF_ID_,
            PROC_DEF_ID_,
            PROC_INST_ID_,
            EXECUTION_ID_,
            SCOPE_ID_,
            SUB_SCOPE_ID_,
            SCOPE_TYPE_,
            SCOPE_DEFINITION_ID_,
            PROPAGATED_STAGE_INST_ID_,
            NAME_,
            PARENT_TASK_ID_,
            DESCRIPTION_,
            OWNER_,
            ASSIGNEE_,
            START_TIME_,
            CLAIM_TIME_,
            END_TIME_,
            DURATION_,
            DELETE_REASON_,
            TASK_DEF_KEY_,
            FORM_KEY_,
            PRIORITY_,
            DUE_DATE_,
            CATEGORY_,
            TENANT_ID_,
            LAST_UPDATED_TIME_
        ) values (
                     #{id ,jdbcType=VARCHAR},
                     1, #{taskDefinitionId, jdbcType=VARCHAR},
                     #{processDefinitionId, jdbcType=VARCHAR},
                     #{processInstanceId, jdbcType=VARCHAR},
                     #{executionId, jdbcType=VARCHAR},
                     #{scopeId, jdbcType=VARCHAR},
                     #{subScopeId, jdbcType=VARCHAR},
                     #{scopeType, jdbcType=VARCHAR},
                     #{scopeDefinitionId, jdbcType=VARCHAR},
                     #{propagatedStageInstanceId, jdbcType=VARCHAR},
                     #{name ,jdbcType=VARCHAR},
                     #{parentTaskId ,jdbcType=VARCHAR},
                     #{description ,jdbcType=VARCHAR},
                     #{owner ,jdbcType=VARCHAR},
                     #{assignee ,jdbcType=VARCHAR},
                     #{createTime, jdbcType=TIMESTAMP},
                     #{claimTime, jdbcType=TIMESTAMP},
                     #{endTime, jdbcType=TIMESTAMP},
                     #{durationInMillis ,jdbcType=BIGINT},
                     #{deleteReason ,jdbcType=VARCHAR},
                     #{taskDefinitionKey ,jdbcType=VARCHAR},
                     #{formKey ,jdbcType=VARCHAR},
                     #{priority, jdbcType=INTEGER},
                     #{dueDate, jdbcType=TIMESTAMP},
                     #{category, jdbcType=VARCHAR},
                     #{tenantId, jdbcType=VARCHAR},
                     #{lastUpdateTime, jdbcType=TIMESTAMP}
                 )
    </insert>

    <select id="listCcYyToMePage"  resultType="org.flowable.engine.impl.persistence.entity.HistoricProcessInstanceEntityImpl">
        SELECT DISTINCT
        RES.ID_,RES.PROC_INST_ID_,RES.BUSINESS_KEY_,RES.PROC_DEF_ID_,RES.START_TIME_,RES.END_TIME_,RES.NAME_,RES.START_USER_ID_,RES.END_ACT_ID_,
        DEF.TYPE_ AS callbackType,
        RES.PROC_INST_ID_ AS processInstanceId,
        REP.KEY_ AS processDefinitionKey
        FROM
            ACT_HI_PROCINST RES
                LEFT OUTER JOIN ACT_HI_IDENTITYLINK DEF ON RES.ID_ = DEF.PROC_INST_ID_
                LEFT OUTER JOIN ACT_RE_PROCDEF REP ON REP.ID_ = RES.PROC_DEF_ID_
        <if test="processInstanceQueryVo.title!=null and processInstanceQueryVo.title!=''">
            LEFT JOIN act_hi_varinst var ON RES.ID_ = var.PROC_INST_ID_
        </if>
        WHERE
            DEF.USER_ID_ = #{username}
        <if test="processInstanceQueryVo.title!=null and processInstanceQueryVo.title!=''">
            AND var.NAME_='title'
            AND var.TEXT_ LIKE concat(concat('%',#{processInstanceQueryVo.title}),'%')
        </if>
        <if test="processInstanceQueryVo.startUserId!=null and processInstanceQueryVo.startUserId!=''">
            AND RES.START_USER_ID_ = #{processInstanceQueryVo.startUserId}
        </if>
        <if test="processInstanceQueryVo.processInstanceName!=null and processInstanceQueryVo.processInstanceName!=''">
            AND RES.NAME_ LIKE concat(concat('%',#{processInstanceQueryVo.processInstanceName}),'%')
        </if>
        <if test="processInstanceQueryVo.status!=null and processInstanceQueryVo.status!=''">
            AND DEF.TYPE_ = #{processInstanceQueryVo.status}
        </if>
        <if test="processInstanceQueryVo.status==null">
            AND DEF.TYPE_ IN ( 'CC', 'YY' )
        </if>
        <if test="processInstanceQueryVo.taskCreatedAfter!=null and processInstanceQueryVo.taskCreatedAfter!=''">
            AND  RES.START_TIME_ &lt; #{processInstanceQueryVo.taskCreatedBefore} and RES.START_TIME_ &gt; #{processInstanceQueryVo.taskCreatedAfter}
        </if>
        <if test="processInstanceQueryVo.taskEndAfter!=null and processInstanceQueryVo.taskEndAfter!=''">
            AND  RES.END_TIME_ &lt; #{processInstanceQueryVo.taskEndBefore} and RES.END_TIME_ &gt; #{processInstanceQueryVo.taskEndAfter}
        </if>
        <if test="processInstanceQueryVo.processDefinitionKey!=null and processInstanceQueryVo.processDefinitionKey!=''">
            AND REP.KEY_ = #{processInstanceQueryVo.processDefinitionKey}
        </if>
        ORDER BY RES.START_TIME_ DESC
        LIMIT #{processInstanceQueryVo.pageSize} OFFSET #{processInstanceQueryVo.offset}
    </select>

    <select id="listCcYyToMe"  resultType="org.flowable.engine.impl.persistence.entity.HistoricProcessInstanceEntityImpl">
        SELECT DISTINCT
        RES.ID_,RES.PROC_INST_ID_,RES.BUSINESS_KEY_,RES.PROC_DEF_ID_,RES.START_TIME_,RES.END_TIME_,RES.NAME_,RES.START_USER_ID_,RES.END_ACT_ID_,
        DEF.TYPE_ AS callbackType,
        RES.PROC_INST_ID_ AS processInstanceId,
        REP.KEY_ AS processDefinitionKey
        FROM
        ACT_HI_PROCINST RES
        LEFT OUTER JOIN ACT_HI_IDENTITYLINK DEF ON RES.ID_ = DEF.PROC_INST_ID_
        LEFT OUTER JOIN ACT_RE_PROCDEF REP ON REP.ID_ = RES.PROC_DEF_ID_
        <if test="processInstanceQueryVo.title!=null and processInstanceQueryVo.title!=''">
            LEFT JOIN act_hi_varinst var ON RES.ID_ = var.PROC_INST_ID_
        </if>
        WHERE
        DEF.USER_ID_ = #{username}
        <if test="processInstanceQueryVo.title!=null and processInstanceQueryVo.title!=''">
            AND var.NAME_='title'
            AND var.TEXT_ LIKE concat(concat('%',#{processInstanceQueryVo.title}),'%')
        </if>
        <if test="processInstanceQueryVo.startUserId!=null and processInstanceQueryVo.startUserId!=''">
            AND RES.START_USER_ID_ = #{processInstanceQueryVo.startUserId}
        </if>
        <if test="processInstanceQueryVo.processInstanceName!=null and processInstanceQueryVo.processInstanceName!=''">
            AND RES.NAME_ LIKE concat(concat('%',#{processInstanceQueryVo.processInstanceName}),'%')
        </if>
        <if test="processInstanceQueryVo.status!=null and processInstanceQueryVo.status!=''">
            AND DEF.TYPE_ = #{processInstanceQueryVo.status}
        </if>
        <if test="processInstanceQueryVo.status==null">
            AND DEF.TYPE_ IN ( 'CC', 'YY' )
        </if>
        <if test="processInstanceQueryVo.taskCreatedAfter!=null and processInstanceQueryVo.taskCreatedAfter!=''">
            AND  RES.START_TIME_ &lt; #{processInstanceQueryVo.taskCreatedBefore} and RES.START_TIME_ &gt; #{processInstanceQueryVo.taskCreatedAfter}
        </if>
        <if test="processInstanceQueryVo.taskEndAfter!=null and processInstanceQueryVo.taskEndAfter!=''">
            AND  RES.END_TIME_ &lt; #{processInstanceQueryVo.taskEndBefore} and RES.END_TIME_ &gt; #{processInstanceQueryVo.taskEndAfter}
        </if>
        <if test="processInstanceQueryVo.processDefinitionKey!=null and processInstanceQueryVo.processDefinitionKey!=''">
            AND REP.KEY_ = #{processInstanceQueryVo.processDefinitionKey}
        </if>
         ORDER BY RES.START_TIME_ DESC
    </select>

    <select id="countCcYyToMe"  resultType="java.lang.Integer">
        SELECT
        COUNT(*)
        FROM
        ACT_HI_PROCINST RES
        LEFT OUTER JOIN ACT_RE_PROCDEF DEF ON RES.PROC_DEF_ID_ = DEF.ID_
        <if test="processInstanceQueryVo.title!=null and processInstanceQueryVo.title!=''">
            LEFT JOIN act_hi_varinst var ON RES.ID_ = var.PROC_INST_ID_
        </if>
        WHERE
        EXISTS (
        SELECT
        ID_
        FROM
        ACT_HI_IDENTITYLINK I
        WHERE
        I.PROC_INST_ID_ = RES.ID_
        AND I.USER_ID_ = #{username}
        <if test="processInstanceQueryVo.status!=null and processInstanceQueryVo.status!=''">
            AND I.TYPE_ = #{processInstanceQueryVo.status}
        </if>
        <if test="processInstanceQueryVo.status==null">
            AND I.TYPE_ IN ( 'CC', 'YY' )
        </if>
        )
        <if test="processInstanceQueryVo.title!=null and processInstanceQueryVo.title!=''">
            AND var.NAME_='title'
            AND var.TEXT_ LIKE concat(concat('%',#{processInstanceQueryVo.title}),'%')
        </if>
        <if test="processInstanceQueryVo.startUserId!=null and processInstanceQueryVo.startUserId!=''">
            AND RES.START_USER_ID_ = #{processInstanceQueryVo.startUserId}
        </if>
        <if test="processInstanceQueryVo.processInstanceName!=null and processInstanceQueryVo.processInstanceName!=''">
            AND RES.NAME_ LIKE concat(concat('%',#{processInstanceQueryVo.processInstanceName}),'%')
        </if>
        <if test="processInstanceQueryVo.taskCreatedAfter!=null and processInstanceQueryVo.taskCreatedAfter!=''">
            AND  RES.START_TIME_ &lt; #{processInstanceQueryVo.taskCreatedBefore} and RES.START_TIME_ &gt; #{processInstanceQueryVo.taskCreatedAfter}
        </if>
        <if test="processInstanceQueryVo.taskEndAfter!=null and processInstanceQueryVo.taskEndAfter!=''">
            AND  RES.END_TIME_ &lt; #{processInstanceQueryVo.taskEndBefore} and RES.END_TIME_ &gt; #{processInstanceQueryVo.taskEndAfter}
        </if>
        <if test="processInstanceQueryVo.processDefinitionKey!=null and processInstanceQueryVo.processDefinitionKey!=''">
            AND DEF.KEY_ = #{processInstanceQueryVo.processDefinitionKey}
        </if>
    </select>
    <update id="updateIdentityType"  >
        UPDATE ACT_HI_IDENTITYLINK  SET TYPE_=#{type} WHERE PROC_INST_ID_=#{id} AND TYPE_='CC' AND USER_ID_=#{userName}
    </update>

    <select id="countOverdueTask" resultType="java.lang.Integer">
        SELECT
        count( DISTINCT RES.ID_ )
        FROM
        ACT_HI_TASKINST RES
        WHERE
        RES.END_TIME_ IS NOT NULL
        AND RES.DUE_DATE_ IS NOT NULL
        AND RES.DUE_DATE_ &lt; RES.END_TIME_ AND
        (
        RES.OWNER_ = #{userName}
        OR RES.ASSIGNEE_ = #{userName})
    </select>
    <select id="countOverdueTasks" resultType="java.lang.Integer">
        SELECT
        count( DISTINCT RES.ID_ )
        FROM
        ACT_HI_TASKINST RES
        WHERE
        RES.END_TIME_ IS NOT NULL
        AND RES.DUE_DATE_ IS NOT NULL
        AND RES.DUE_DATE_ &lt; RES.END_TIME_ AND
        RES.ASSIGNEE_  in
        <foreach collection="userNames" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>

    </select>
     <select id="getTotalTaskTime" resultType="java.lang.Long">
         SELECT SUM(task.DURATION_)
         FROM
             act_hi_taskinst task
         WHERE task.ASSIGNEE_ = #{assignee} AND task.DURATION_ IS NOT NULL  AND task.END_TIME_ is not NULL
     </select>
    <select id="getTaskIdByProcessInstanceId" resultType="java.lang.String">
        SELECT
        ID_
        FROM
        ACT_HI_TASKINST
        WHERE
        PROC_INST_ID_ = #{processInstanceId}
        And
        END_TIME_ IS NULL
        ORDER BY
        START_TIME_ DESC
        LIMIT 1
    </select>

    <select id="getTaskAssigneeByNodeId" resultType="java.lang.String">
        SELECT
        assignee_
        FROM
        act_hi_taskinst
        WHERE
        PROC_INST_ID_ = #{processInstanceId}
        AND
        TASK_DEF_KEY_ = #{nodeId}
        ORDER BY
            START_TIME_ DESC
            LIMIT 1
    </select>
</mapper>
