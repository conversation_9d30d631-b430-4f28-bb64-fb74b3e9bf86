package com.yuanqiao.insight.modules.flowable.listener;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.common.constant.CommonConstant;
import com.yuanqiao.insight.common.core.util.ObjectUtils;
import com.yuanqiao.insight.common.core.util.SecurityUtils;
import com.yuanqiao.insight.common.util.common.LocalCacheUtils;
import com.yuanqiao.insight.modules.flowable.common.CommentTypeEnum;
import com.yuanqiao.insight.modules.flowable.constant.FlowableConstant;
import com.yuanqiao.insight.modules.flowable.mapper.VarInstUpdateMapper;
import com.yuanqiao.insight.modules.flowable.service.ActNodeService;
import com.yuanqiao.insight.modules.flowable.service.AssociationProcessStrategy;
import com.yuanqiao.insight.modules.flowable.service.impl.ActNodeServiceImpl;
import com.yuanqiao.insight.modules.flowable.util.ESClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.*;
import org.flowable.common.engine.api.FlowableException;
import org.flowable.common.engine.api.delegate.event.FlowableEngineEntityEvent;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.delegate.event.AbstractFlowableEngineEventListener;
import org.flowable.engine.delegate.event.FlowableMultiInstanceActivityEvent;
import org.flowable.engine.delegate.event.FlowableProcessStartedEvent;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.impl.persistence.entity.ExecutionEntity;
import org.flowable.engine.repository.Model;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.task.Comment;
import org.flowable.identitylink.api.history.HistoricIdentityLink;
import org.flowable.task.api.TaskInfo;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import org.flowable.variable.api.history.HistoricVariableInstance;
import org.jeecg.common.system.api.CommonListenerService;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.util.SpringContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 流程监听器
 */
@Slf4j
@Component
public class GlobalTaskListener extends AbstractFlowableEngineEventListener {

    private final static ThreadLocal<SimpleDateFormat> simpleDateFormatThreadLocal = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
    @Autowired
    private RepositoryService repositoryService;
    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private ActNodeService actNodeService;
    @Autowired
    private HistoryService historyService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private CommonListenerService commonListenerService;
    @Autowired
    private ISysBaseAPI iSysBaseAPI;
    private String id;

    LocalCacheUtils cacheUtils = LocalCacheUtils.getInstance();
    @Resource
    private VarInstUpdateMapper varinstUpdateMapper;


    @Autowired
    protected ESClient esClient;

    @Override
    protected void multiInstanceActivityStarted(FlowableMultiInstanceActivityEvent event) {
        super.multiInstanceActivityStarted(event);
    }


    /**
     * 发送消息方法
     *
     * @param taskEntity
     * @param toUsername  消息接收人的账号
     * @param handleState 处理状态 1认领 2处理
     * @param modelKey
     * @param elementId
     */
    private void sendMessage(TaskEntity taskEntity, String fromUsername, String toUsername, int handleState, String modelKey, String elementId) {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(taskEntity.getProcessInstanceId()).singleResult();
        HashMap<String, String> templateParam = new HashMap<>(5);
        templateParam.put("bpm_name", processInstance.getName());
        templateParam.put("bpm_task", taskEntity.getName());
        templateParam.put("datetime", simpleDateFormatThreadLocal.get().format(taskEntity.getCreateTime()));
        HashMap<String, Object> stringStringHashMap = new LinkedHashMap<>();
        stringStringHashMap.put("taskId", taskEntity.getId());
        stringStringHashMap.put("processInstanceId", taskEntity.getProcessInstanceId());
        stringStringHashMap.put("taskDefinitionKey", taskEntity.getTaskDefinitionKey());
        stringStringHashMap.put("processDefinitionVersion", processInstance.getProcessDefinitionVersion());
        stringStringHashMap.put("handleState", handleState);
        stringStringHashMap.put("modelKey", modelKey);
        String s = JSONObject.toJSONString(stringStringHashMap);
        Object valueByKey = cacheUtils.getValueByKey(CommonConstant.DATA_DICT_KEY + "processNotification_newTask");
        if (valueByKey == null) {
            throw new FlowableException("未配置[processNotification_newTask]新任务通知模板参数，请联系管理员！");
        }
        iSysBaseAPI.sendNoticeByProcess(templateParam,valueByKey.toString(),new ArrayList<>(Collections.singletonList(toUsername)));

    }


    /**
     * 监听任务创建
     *
     * @param event
     */
    @Override
    protected void taskCreated(FlowableEngineEntityEvent event) {
        super.taskCreated(event);

        String processInstanceIdValue = event.getProcessInstanceId();

        TaskEntity taskEntity = (TaskEntity) event.getEntity();

        log.info("任务创建ed监听" + taskEntity.getName());

        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(taskEntity.getProcessDefinitionId()).singleResult();
        Model model = repositoryService.createModelQuery().deploymentId(processDefinition.getDeploymentId()).singleResult();

        // todo-weichen 2022/10/10 执行到此处，el表达式已经做过解析；如果已经设置了处理人，不需要再去寻找处理人的逻辑，待验证对原有逻辑是否产生影响
        if (StringUtils.isNotBlank(taskEntity.getAssignee())) {
            //当有处理人时 发送消息
            try {
                sendMessage(taskEntity, "system", taskEntity.getAssignee(), 2, model.getKey(), null);
            } catch (Exception e) {
                log.info("同步发送第三方APP消息失败！" + e.getMessage());
            }
            return;
        }
        String fromUsername = SecurityUtils.getUser().getUsername();
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinition.getId());
        Process process = bpmnModel.getMainProcess();
        String nodeId = taskEntity.getTaskDefinitionKey();
        List<FlowElement> flowElements = process.getFlowElements().stream().filter(flowElement -> flowElement instanceof UserTask).collect(Collectors.toList());

        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(taskEntity.getProcessInstanceId()).singleResult();
        Map<String, Object> variables = processInstance.getProcessVariables();
        Object level = variables.get("sla_priority_level");
        for (FlowElement element : flowElements) {
            //添加sla状态类型值
            if (taskEntity.getTaskDefinitionKey().equals(element.getId())&&!element.getExtensionElements().isEmpty()) {
                ExtensionElement properties = element.getExtensionElements().get("properties").get(0);
                String slaLevelId = commonListenerService.getSlaTypeByElement(properties);
                if (StringUtils.isNotEmpty(slaLevelId)) {
                    Date createTime = taskEntity.getCreateTime();
                    HashMap<String, Object> hashMap = commonListenerService.getDueDataByLevelId((String) level, slaLevelId, createTime);
                    if (hashMap.get("dueDate")!=null){
                        taskService.setDueDate(taskEntity.getId(), (Date) hashMap.get("dueDate"));
                    }
                    if (hashMap.get("level") != null) {
                        taskService.setVariableLocal(taskEntity.getId(), "slaLevel", hashMap.get("level"));
                    }
                    if (hashMap.get("slaType") != null) {
                    taskService.setVariableLocal(taskEntity.getId(), "slaType", hashMap.get("slaType"));
                    }
                }
            }
            if (StringUtils.equals(element.getId(), taskEntity.getTaskDefinitionKey())) {
                List<HistoricTaskInstance> list = historyService.createHistoricTaskInstanceQuery().processInstanceId(processInstance.getProcessInstanceId()).list();
                // 约定：发起者节点为 __initiator__ ,则自动完成任务
                if (FlowableConstant.INITIATOR.equals(taskEntity.getTaskDefinitionKey()) && list.size() == 0) {
                    taskService.addComment(taskEntity.getId(), processInstance.getId(), CommentTypeEnum.TJ.toString(), "填写表单");
                    taskService.setAssignee(taskEntity.getId(), processInstance.getStartUserId());

                    if (runtimeService != null && taskEntity != null) {
                        ExecutionEntity executionEntity = (ExecutionEntity) runtimeService.createExecutionQuery().executionId(taskEntity.getExecutionId()).singleResult();
                        if (executionEntity != null) {

                            taskService.setVariable(taskEntity.getId(), FlowableConstant.PROCESS_EXECUTION_STATUS_KEY, FlowableConstant.PROCESS_EXECUTION_STATUS_RUN);
                            //      id = iSysBaseAPI.addServiceProcessEvaluate(FlowableConstant.PROCESS_EXECUTION_STATUS_RUN,  processInstanceIdValue);
                            taskService.complete(taskEntity.getId());
                        } else {
                            log.warn("executionEntity 为 空");
                        }
                    } else {
                        log.warn("runtimeService 或 taskEntity 为空");
                    }

                } else {
                    List<String> users1 = new ArrayList<>(10);
                    boolean backFlag = false;
                    String fromTaskId = "";
                    for (HistoricTaskInstance taskInstance : list) {
                        if (nodeId.equals(taskInstance.getTaskDefinitionKey())) {
                            fromTaskId = taskInstance.getId();
                            backFlag = true;
                        }
                    }
                    //判断该节点在任务设置中选择了节点选项
                    Boolean chooseNode = actNodeService.hasChooseNode(taskEntity.getTaskDefinitionKey(), model.getVersion());

                    //判断是否是上一节点是否指派了此节点的处理人
                    String assignNextNode = (String) taskEntity.getVariable("assignNextNode");
                    if (StringUtils.isNotBlank(assignNextNode)) {
                        String[] split = assignNextNode.split(",");
                        if (split.length > 1) {
                            for (String username : split) {
                                taskService.addCandidateUser(taskEntity.getId(), username);
                                try {
                                    sendMessage(taskEntity, fromUsername, username, 1, model.getKey(), element.getId());
                                } catch (Exception e) {
                                    log.info("同步发送第三方APP消息失败！" + e.getMessage());
                                }
                                users1.add(username);
                            }

                        } else {
                            taskService.setAssignee(taskEntity.getId(), assignNextNode);
                            try {
                                sendMessage(taskEntity, fromUsername, assignNextNode, 2, model.getKey(), element.getId());
                            } catch (Exception e) {
                                log.info("同步发送第三方APP消息失败！" + e.getMessage());
                            }
                            users1.add(assignNextNode);
                        }
                        //需要移除掉，否则该流程实例的后续用户任务都会走此判断分支 如果想要查看指派了那些人 看act_ru_identitylink表或act_hi_identitylink表
                        taskService.setVariable(taskEntity.getId(), "assignNextNode", null);
                    } else if (backFlag&&!chooseNode) { //退回
                        //查询之前任务的办理人
                        TaskInfo task = historyService.createHistoricTaskInstanceQuery().taskId(fromTaskId).singleResult();
                        String assignee = task.getAssignee();
                        //查询之前任务的候选人
                        List<HistoricIdentityLink> historicIdentityLinkList = historyService.getHistoricIdentityLinksForTask(fromTaskId);
                        List<String> candidateUsers = new ArrayList<>();
                        if (historicIdentityLinkList != null && historicIdentityLinkList.size() > 0) {
                            for (HistoricIdentityLink identityLink : historicIdentityLinkList) {
                                if ("candidate".equals(identityLink.getType())) {
                                    if (StringUtils.isNotBlank(identityLink.getUserId())) {
                                        candidateUsers.add(identityLink.getUserId());
                                    }
                                }
                            }
                        }
                        if (candidateUsers != null && candidateUsers.size() > 0) {
                            for (String candidate : candidateUsers) {
                                taskService.addCandidateUser(taskEntity.getId(), candidate);
                                try {
                                    sendMessage(taskEntity, fromUsername, candidate, 1, model.getKey(), element.getId());
                                } catch (Exception e) {
                                    log.info("同步发送第三方APP消息失败！" + e.getMessage());
                                }
                                users1.add(candidate);

                            }
                        } else if (assignee != null) {
                            taskService.setAssignee(taskEntity.getId(), assignee);
                            try {
                                sendMessage(taskEntity, fromUsername, assignee, 2, model.getKey(), element.getId());
                            } catch (Exception e) {
                                log.info("同步发送第三方APP消息失败！" + e.getMessage());
                            }
                            users1.add(assignee);
                        }
                    } else {

                        //判断是不是会签节点
                        // TODO: 2022/7/8   会签节点待处理
                        MultiInstanceLoopCharacteristics multiInstanceLoopCharacteristics = ((UserTask) element).getLoopCharacteristics();
                        if (multiInstanceLoopCharacteristics != null) {
                            //会签节点
                            //获取设置参数名称
                            /*List<String> users = (List<String>) taskService.getVariables(taskEntity.getId()).get("handles");
                            for (String user : users) {
                                taskService.setAssignee(taskEntity.getId(), user);
                            }*/
                        } else {
                            //List<Task> list = taskService.createTaskQuery().processInstanceId(taskEntity.getProcessInstanceId()).list();
                            List<String> users = actNodeService.getNodeUsersByTaskAndVersion(taskEntity.getTaskDefinitionKey(), processInstance.getId(), model.getVersion(), processDefinition.getKey());
                            if (users.size() > 1) {
                                //覆盖流程设计设定的处理人
                                taskService.setAssignee(taskEntity.getId(), null);
                                for (String user : users) {
                                    taskService.addCandidateUser(taskEntity.getId(), user);

                                    try {
                                        sendMessage(taskEntity, fromUsername, user, 1, model.getKey(), element.getId());
                                    } catch (Exception e) {
                                        log.info("同步发送第三方APP消息失败！" + e.getMessage());
                                    }
                                    users1.add(user);
                                }

                            } else if (users.size() == 1) {
                                taskService.setAssignee(taskEntity.getId(), users.get(0));
                                try {
                                    sendMessage(taskEntity, fromUsername, users.get(0), 2, model.getKey(), element.getId());
                                } catch (Exception e) {
                                    log.info("同步发送第三方APP消息失败！" + e.getMessage());
                                }
                                users1.add(users.get(0));
                            }
                        }
                    }
                }
            }
        }


    }

    /**
     * 监听任务完成
     *
     * @param event
     */
    @Override
    protected void taskCompleted(FlowableEngineEntityEvent event) {
        super.taskCompleted(event);

        TaskEntity taskEntity = (TaskEntity) event.getEntity();

        if (!FlowableConstant.INITIATOR.equals(taskEntity.getTaskDefinitionKey())) {
            String processInstanceId = taskEntity.getProcessInstanceId();
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
            String starter = historicProcessInstance.getStartUserId();
            List<Comment> comments = taskService.getTaskComments(taskEntity.getId(), "WC");
            log.info("完成任务ed监听");
            HashMap<String, String> templateParam = new HashMap<>(5);
            templateParam.put("bpm_name", historicProcessInstance.getName());
            templateParam.put("bpm_start_time", simpleDateFormatThreadLocal.get().format(historicProcessInstance.getStartTime()));
            templateParam.put("bpm_task", taskEntity.getName());
            String userRealName = iSysBaseAPI.getUserByName(taskEntity.getAssignee()).getRealname() + "【" + taskEntity.getAssignee() + "】";
            templateParam.put("bpm_assignee", userRealName);
            templateParam.put("datetime", simpleDateFormatThreadLocal.get().format(new Date()));
            if (comments != null && comments.size() > 0) {
                templateParam.put("comment", comments.get(0).getFullMessage());
            }

            Object valueByKey = cacheUtils.getValueByKey(CommonConstant.DATA_DICT_KEY + "processNotification_complete");
            if (valueByKey == null) {
                throw new FlowableException("未配置[processNotification_complete]任务完成通知模版参数,请联系管理员!");
            }
            iSysBaseAPI.sendNoticeByProcess(templateParam,valueByKey.toString(),new ArrayList<>(Collections.singletonList(starter)));

            List<HistoricVariableInstance> hisVals =
                    historyService.createHistoricVariableInstanceQuery().processInstanceId(processInstanceId).list();
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
            Map<String, Object> variables = new HashMap<>();
            if (hisVals.size() == 0) {
                variables = processInstance.getProcessVariables();
            } else {
                for (HistoricVariableInstance variableInstance : hisVals) {
                    variables.put(variableInstance.getVariableName(), variableInstance.getValue());
                }
            }

            //存入超时时间
            if (ObjectUtils.isNotEmpty(taskEntity.getDueDate())&&taskEntity.getDueDate().compareTo(new Date())<0){
                CommonListenerService commonListenerService = SpringContextUtils.getBean(CommonListenerService.class);
                commonListenerService.updateSlaTakeTimeByTask(taskEntity);
            }

            //存入ES
            JSONObject jsonObject = new JSONObject(variables);
            log.info("存入es " + jsonObject);
            try {
                esClient.updateOrInsert(processInstanceId, jsonObject.toString());
            } catch (IOException e) {
                e.printStackTrace();
            }

            /*if (historicProcessInstance.getProcessDefinitionKey().equals(FlowableConstant.ORDER_MODEL_KEY)){
                String taskDefinitionKey = taskEntity.getTaskDefinitionKey();
                String code = variables.get("serviceRequestCode").toString();
                String linkUsername = variables.get("linkUsername").toString();
                //添加待评价记录
                if (taskDefinitionKey.equals(FlowableConstant.DUTY_NODE_ID_PERSON) || taskDefinitionKey.equals(FlowableConstant.DUTY_NODE_ID_ROLE) || taskDefinitionKey.equals(FlowableConstant.ANSWER_OR_DISPATCH_ID)) {
                    iYqServiceEvaluateService.addServiceEvaluate(taskEntity.getAssignee(), code, historicProcessInstance.getId(), "值班人", new Date(), linkUsername);
                }
                if (taskDefinitionKey.equals(FlowableConstant.ORDER_NODE_ID)) {
                    iYqServiceEvaluateService.addServiceEvaluate(taskEntity.getAssignee(), code, historicProcessInstance.getId(), "工程师", new Date(), linkUsername);
                }
            }*/
        }

    }

    /**
     * 监听流程创建
     *
     * @param event
     */
    @Override
    protected void processCreated(FlowableEngineEntityEvent event) {
        super.processCreated(event);
        log.info("流程创建ed监听");
    }

    /**
     * 监听流程启动
     *
     * @param event
     */
    @Override
    protected void processStarted(FlowableProcessStartedEvent event) {
        super.processStarted(event);
        log.info("流程启动ed监听");
//        FlowableProcessEventImpl eventImpl = (FlowableProcessEventImpl) event;
//
//        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(eventImpl.getProcessDefinitionId()).singleResult();
//        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinition.getId());
//        List<Process>processList = bpmnModel.getProcesses();
//        Process process = processList.get(0);
//
//        Collection<FlowElement> flowElements =  process.getFlowElements();
//        HashMap<String, Object> variables = new HashMap<>();
//        for(FlowElement element : flowElements) {
//            if (element instanceof UserTask) {
//                String inputDataItem;
//                UserTask userTask = (UserTask) element;
//                //判断是不是会签节点
//                MultiInstanceLoopCharacteristics multiInstanceLoopCharacteristics = userTask.getLoopCharacteristics();
//                if (multiInstanceLoopCharacteristics != null) {
//                    //会签节点
//                    //获取设置参数名称
//                } else {
//                    List<String> userNames = actNodeService.getNodeByTask(userTask.getId(),eventImpl.getProcessInstanceId());
//                    if (userNames.size()==1) {
//                        userTask.setAssignee(userNames.get(0));
//                    }else if (userNames.size() >1 ) {
////                        覆盖流程设计设定的处理人
//                        userTask.setAssignee(null);
//                        userTask.setCandidateUsers(userNames);
//                    }
//                }
//            }
//        }
    }


    /**
     * 监听流程结束
     *
     * @param event
     */
    @Override
    protected void processCompleted(FlowableEngineEntityEvent event) {
        super.processCompleted(event);
        log.info("流程结束ed监听");
        //将告警信息中的处理状态改为处理完成
        ProcessInstance taskEntity = (ProcessInstance) event.getEntity();
        Map<String, Object> processVariables = taskEntity.getProcessVariables();
        String classOperation = (String) processVariables.get("classOperation");
        if (StringUtils.isNotEmpty(classOperation)){
            try {
                AssociationProcessStrategy associationProcessContent = getAssociationProcessContent(classOperation);
                associationProcessContent.updateEndProcess(event.getProcessInstanceId());
            } catch (Exception e) {
                log.warn("工单关联处理配置操作类不存在");
            }
        }
        //添加完成时耗时
        Date startTime = taskEntity.getStartTime();
        long durationInMillis = new Date().getTime() - startTime.getTime();
        log.info("总完成耗时:{}", durationInMillis);
        CommonListenerService commonListenerService = SpringContextUtils.getBean(CommonListenerService.class);
        commonListenerService.updateWorkOrderProcessDuration(taskEntity.getProcessInstanceId(), durationInMillis);

    }

    private AssociationProcessStrategy getAssociationProcessContent(String classOperation) throws ClassNotFoundException, InstantiationException, IllegalAccessException {
        Class<?> aClass = Class.forName(classOperation);
        return (AssociationProcessStrategy) aClass.newInstance();
    }

}
