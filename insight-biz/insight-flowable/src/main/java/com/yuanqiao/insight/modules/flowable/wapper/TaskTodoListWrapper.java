package com.yuanqiao.insight.modules.flowable.wapper;

import com.yuanqiao.insight.modules.flowable.common.ResponseFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020年3月22日
 */
@Component
public class TaskTodoListWrapper implements IListWrapper {

    @Autowired
    private ResponseFactory responseFactory;

    @SuppressWarnings({"rawtypes", "unchecked"})
    @Override
    public List execute(List list) {
        return responseFactory.createTaskResponseList(list);
    }
}
