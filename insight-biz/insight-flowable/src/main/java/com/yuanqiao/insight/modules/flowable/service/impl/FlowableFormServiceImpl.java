package com.yuanqiao.insight.modules.flowable.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuanqiao.insight.common.core.base.BaseServiceImpl;
import com.yuanqiao.insight.modules.flowable.entity.FlowableForm;
import com.yuanqiao.insight.modules.flowable.mapper.FlowableFormMapper;
import com.yuanqiao.insight.modules.flowable.service.FlowableFormService;
import org.springframework.stereotype.Service;

/**
 * 流程Service
 *
 * <AUTHOR>
 */
@Service
public class FlowableFormServiceImpl extends BaseServiceImpl<FlowableFormMapper, FlowableForm> implements FlowableFormService {
    @Override
    public IPage<FlowableForm> list(IPage<FlowableForm> page, FlowableForm flowableForm) {
        return page.setRecords(baseMapper.list(page, flowableForm));
    }

    @Override
    public void updateWithFormKey(FlowableForm flowableForm) {
        baseMapper.updateWithFormKey(flowableForm);
    }
}
