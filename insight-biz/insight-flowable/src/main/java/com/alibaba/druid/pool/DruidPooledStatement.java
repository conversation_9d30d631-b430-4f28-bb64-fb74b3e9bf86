//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.alibaba.druid.pool;

import com.alibaba.druid.VERSION;
import com.alibaba.druid.support.logging.Log;
import com.alibaba.druid.support.logging.LogFactory;
import com.alibaba.druid.util.JdbcUtils;
import com.alibaba.druid.util.MySqlUtils;
import org.apache.commons.lang3.StringUtils;

import java.net.SocketTimeoutException;
import java.sql.*;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class DruidPooledStatement extends PoolableWrapper implements Statement {
    private static final Log LOG = LogFactory.getLog(DruidPooledStatement.class);
    private final Statement stmt;
    protected DruidPooledConnection conn;
    protected List<ResultSet> resultSetTrace;
    protected boolean closed = false;
    protected int fetchRowPeak = -1;
    protected int exceptionCount = 0;

    public DruidPooledStatement(DruidPooledConnection conn, Statement stmt) {
        super(stmt);
        this.conn = conn;
        this.stmt = stmt;
    }

    protected void addResultSetTrace(ResultSet resultSet) {
        if (this.resultSetTrace == null) {
            this.resultSetTrace = new ArrayList(1);
        }

        this.resultSetTrace.add(resultSet);
    }

    protected void recordFetchRowCount(int fetchRowCount) {
        if (this.fetchRowPeak < fetchRowCount) {
            this.fetchRowPeak = fetchRowCount;
        }

    }

    public int getFetchRowPeak() {
        return this.fetchRowPeak;
    }

    protected SQLException checkException(Throwable error) throws SQLException {
        String sql = null;
        if (this instanceof DruidPooledPreparedStatement) {
            sql = ((DruidPooledPreparedStatement)this).getSql();
        }

        this.handleSocketTimeout(error);
        ++this.exceptionCount;
        return this.conn.handleException(error, sql);
    }

    protected SQLException checkException(Throwable error, String sql) throws SQLException {
        this.handleSocketTimeout(error);
        ++this.exceptionCount;
        return this.conn.handleException(error, sql);
    }

    protected void handleSocketTimeout(Throwable error) throws SQLException {
        if (this.conn != null && this.conn.transactionInfo == null && this.conn.holder != null) {
            DruidDataSource dataSource = null;
            DruidConnectionHolder holder = this.conn.holder;
            if (holder.dataSource instanceof DruidDataSource) {
                dataSource = (DruidDataSource)holder.dataSource;
            }

            if (dataSource != null) {
                if (dataSource.killWhenSocketReadTimeout) {
                    SQLException sqlException = null;
                    if (error instanceof SQLException) {
                        sqlException = (SQLException)error;
                    }

                    if (sqlException != null) {
                        Throwable cause = error.getCause();
                        boolean socketReadTimeout = cause instanceof SocketTimeoutException && "Read timed out".equals(cause.getMessage());
                        if (socketReadTimeout) {
                            if ("mysql".equals(dataSource.dbType)) {
                                String killQuery = MySqlUtils.buildKillQuerySql(this.conn.getConnection(), (SQLException)error);
                                if (killQuery != null) {
                                    DruidPooledConnection killQueryConn = null;
                                    Statement killQueryStmt = null;

                                    try {
                                        killQueryConn = dataSource.getConnection(1000L);
                                        if (killQueryConn != null) {
                                            killQueryStmt = killQueryConn.createStatement();
                                            killQueryStmt.execute(killQuery);
                                            if (LOG.isDebugEnabled()) {
                                                LOG.debug(killQuery + " success.");
                                            }

                                            return;
                                        }
                                    } catch (Exception var14) {
                                        LOG.warn(killQuery + " error.", var14);
                                        return;
                                    } finally {
                                        JdbcUtils.close(killQueryStmt);
                                        JdbcUtils.close(killQueryConn);
                                    }

                                }
                            }
                        }
                    }
                }
            }
        }
    }

    public DruidPooledConnection getPoolableConnection() {
        return this.conn;
    }

    public Statement getStatement() {
        return this.stmt;
    }

    protected void checkOpen() throws SQLException {
        if (this.closed) {
            Throwable disableError = null;
            if (this.conn != null) {
                disableError = this.conn.getDisableError();
            }

            if (disableError != null) {
                throw new SQLException("statement is closed", disableError);
            } else {
                throw new SQLException("statement is closed");
            }
        }
    }

    protected void clearResultSet() {
        if (this.resultSetTrace != null) {
            Iterator var1 = this.resultSetTrace.iterator();

            while(var1.hasNext()) {
                ResultSet rs = (ResultSet)var1.next();

                try {
                    if (!rs.isClosed()) {
                        rs.close();
                    }
                } catch (SQLException var4) {
                    LOG.error("clearResultSet error", var4);
                }
            }

            this.resultSetTrace.clear();
        }
    }

    public void incrementExecuteCount() {
        DruidPooledConnection conn = this.getPoolableConnection();
        if (conn != null) {
            DruidConnectionHolder holder = conn.getConnectionHolder();
            if (holder != null) {
                DruidAbstractDataSource dataSource = holder.getDataSource();
                if (dataSource != null) {
                    dataSource.incrementExecuteCount();
                }
            }
        }
    }

    public void incrementExecuteBatchCount() {
        DruidPooledConnection conn = this.getPoolableConnection();
        if (conn != null) {
            DruidConnectionHolder holder = conn.getConnectionHolder();
            if (holder != null) {
                if (holder.getDataSource() != null) {
                    DruidAbstractDataSource dataSource = holder.getDataSource();
                    if (dataSource != null) {
                        dataSource.incrementExecuteBatchCount();
                    }
                }
            }
        }
    }

    public void incrementExecuteUpdateCount() {
        DruidPooledConnection conn = this.getPoolableConnection();
        if (conn != null) {
            DruidConnectionHolder holder = conn.getConnectionHolder();
            if (holder != null) {
                DruidAbstractDataSource dataSource = holder.getDataSource();
                if (dataSource != null) {
                    dataSource.incrementExecuteUpdateCount();
                }
            }
        }
    }

    public void incrementExecuteQueryCount() {
        DruidPooledConnection conn = this.getPoolableConnection();
        if (conn != null) {
            DruidConnectionHolder holder = conn.getConnectionHolder();
            if (holder != null) {
                DruidAbstractDataSource dataSource = holder.getDataSource();
                if (dataSource != null) {
                    dataSource.incrementExecuteQueryCount();
                }
            }
        }
    }

    protected void transactionRecord(String sql) throws SQLException {
        this.conn.transactionRecord(sql);
    }

    public final ResultSet executeQuery(String sql) throws SQLException {
        this.checkOpen();
        this.incrementExecuteQueryCount();
        this.transactionRecord(sql);
        this.conn.beforeExecute();

        DruidPooledResultSet var4;
        try {
            ResultSet rs = this.stmt.executeQuery(sql);
            if (rs == null) {
                ResultSet var10 = rs;
                return var10;
            }

            DruidPooledResultSet poolableResultSet = new DruidPooledResultSet(this, rs);
            this.addResultSetTrace(poolableResultSet);
            var4 = poolableResultSet;
        } catch (Throwable var8) {
            this.errorCheck(var8);
            throw this.checkException(var8, sql);
        } finally {
            this.conn.afterExecute();
        }

        return var4;
    }

    public final int executeUpdate(String sql) throws SQLException {
        this.checkOpen();
        this.incrementExecuteUpdateCount();
        this.transactionRecord(sql);
        this.conn.beforeExecute();

        int var2;
        try {
            var2 = this.stmt.executeUpdate(sql);
        } catch (Throwable var6) {
            this.errorCheck(var6);
            throw this.checkException(var6, sql);
        } finally {
            this.conn.afterExecute();
        }

        return var2;
    }

    protected final void errorCheck(Throwable t) {
        String errorClassName = t.getClass().getName();
        if (errorClassName.endsWith(".CommunicationsException") && this.conn.holder != null && this.conn.holder.dataSource.testWhileIdle) {
            DruidConnectionHolder holder = this.conn.holder;
            DruidAbstractDataSource dataSource = holder.dataSource;
            long currentTimeMillis = System.currentTimeMillis();
            long lastActiveTimeMillis = holder.lastActiveTimeMillis;
            if (lastActiveTimeMillis < holder.lastKeepTimeMillis) {
                lastActiveTimeMillis = holder.lastKeepTimeMillis;
            }

            long idleMillis = currentTimeMillis - lastActiveTimeMillis;
            long lastValidIdleMillis = currentTimeMillis - holder.lastActiveTimeMillis;
            String errorMsg = "CommunicationsException, druid version " + VERSION.getVersionNumber() + ", jdbcUrl : " + dataSource.jdbcUrl + ", testWhileIdle " + dataSource.testWhileIdle + ", idle millis " + idleMillis + ", minIdle " + dataSource.minIdle + ", poolingCount " + dataSource.getPoolingCount() + ", timeBetweenEvictionRunsMillis " + dataSource.timeBetweenEvictionRunsMillis + ", lastValidIdleMillis " + lastValidIdleMillis + ", driver " + dataSource.driver.getClass().getName();
            if (dataSource.exceptionSorter != null) {
                errorMsg = errorMsg + ", exceptionSorter " + dataSource.exceptionSorter.getClass().getName();
            }

            LOG.error(errorMsg);
        }

    }

    public final int executeUpdate(String sql, int autoGeneratedKeys) throws SQLException {
        this.checkOpen();
        this.incrementExecuteUpdateCount();
        this.transactionRecord(sql);
        this.conn.beforeExecute();

        int var3;
        try {
            var3 = this.stmt.executeUpdate(sql, autoGeneratedKeys);
        } catch (Throwable var7) {
            this.errorCheck(var7);
            throw this.checkException(var7, sql);
        } finally {
            this.conn.afterExecute();
        }

        return var3;
    }

    public final int executeUpdate(String sql, int[] columnIndexes) throws SQLException {
        this.checkOpen();
        this.incrementExecuteUpdateCount();
        this.transactionRecord(sql);
        this.conn.beforeExecute();

        int var3;
        try {
            var3 = this.stmt.executeUpdate(sql, columnIndexes);
        } catch (Throwable var7) {
            this.errorCheck(var7);
            throw this.checkException(var7, sql);
        } finally {
            this.conn.afterExecute();
        }

        return var3;
    }

    public final int executeUpdate(String sql, String[] columnNames) throws SQLException {
        this.checkOpen();
        this.incrementExecuteUpdateCount();
        this.transactionRecord(sql);
        this.conn.beforeExecute();

        int var3;
        try {
            var3 = this.stmt.executeUpdate(sql, columnNames);
        } catch (Throwable var7) {
            this.errorCheck(var7);
            throw this.checkException(var7, sql);
        } finally {
            this.conn.afterExecute();
        }

        return var3;
    }

    public final boolean execute(String sql, int autoGeneratedKeys) throws SQLException {
        this.checkOpen();
        this.incrementExecuteCount();
        this.transactionRecord(sql);
        this.conn.beforeExecute();

        boolean var3;
        try {
            var3 = this.stmt.execute(sql, autoGeneratedKeys);
        } catch (Throwable var7) {
            this.errorCheck(var7);
            throw this.checkException(var7, sql);
        } finally {
            this.conn.afterExecute();
        }

        return var3;
    }

    public final boolean execute(String sql, int[] columnIndexes) throws SQLException {
        this.checkOpen();
        this.incrementExecuteCount();
        this.transactionRecord(sql);
        this.conn.beforeExecute();

        boolean var3;
        try {
            var3 = this.stmt.execute(sql, columnIndexes);
        } catch (Throwable var7) {
            this.errorCheck(var7);
            throw this.checkException(var7, sql);
        } finally {
            this.conn.afterExecute();
        }

        return var3;
    }

    public final boolean execute(String sql, String[] columnNames) throws SQLException {
        this.checkOpen();
        this.incrementExecuteCount();
        this.transactionRecord(sql);
        this.conn.beforeExecute();

        boolean var3;
        try {
            var3 = this.stmt.execute(sql, columnNames);
        } catch (Throwable var7) {
            this.errorCheck(var7);
            throw this.checkException(var7, sql);
        } finally {
            this.conn.afterExecute();
        }

        return var3;
    }

    public int getMaxFieldSize() throws SQLException {
        this.checkOpen();

        try {
            return this.stmt.getMaxFieldSize();
        } catch (Throwable var2) {
            throw this.checkException(var2);
        }
    }

    public void close() throws SQLException {
        if (!this.closed) {
            this.clearResultSet();
            if (this.stmt != null) {
                this.stmt.close();
            }

            this.closed = true;
            DruidConnectionHolder connHolder = this.conn.getConnectionHolder();
            if (connHolder != null) {
                connHolder.removeTrace(this);
            }

        }
    }

    public void setMaxFieldSize(int max) throws SQLException {
        this.checkOpen();

        try {
            this.stmt.setMaxFieldSize(max);
        } catch (Throwable var3) {
            throw this.checkException(var3);
        }
    }

    public final int getMaxRows() throws SQLException {
        this.checkOpen();

        try {
            return this.stmt.getMaxRows();
        } catch (Throwable var2) {
            throw this.checkException(var2);
        }
    }

    public void setMaxRows(int max) throws SQLException {
        this.checkOpen();

        try {
            this.stmt.setMaxRows(max);
        } catch (Throwable var3) {
            throw this.checkException(var3);
        }
    }

    public final void setEscapeProcessing(boolean enable) throws SQLException {
        this.checkOpen();

        try {
            this.stmt.setEscapeProcessing(enable);
        } catch (Throwable var3) {
            throw this.checkException(var3);
        }
    }

    public final int getQueryTimeout() throws SQLException {
        this.checkOpen();

        try {
            return this.stmt.getQueryTimeout();
        } catch (Throwable var2) {
            throw this.checkException(var2);
        }
    }

    public void setQueryTimeout(int seconds) throws SQLException {
        this.checkOpen();

        try {
            this.stmt.setQueryTimeout(seconds);
        } catch (Throwable var3) {
            throw this.checkException(var3);
        }
    }

    public final void cancel() throws SQLException {
        this.checkOpen();

        try {
            this.stmt.cancel();
        } catch (Throwable var2) {
            throw this.checkException(var2);
        }
    }

    public final SQLWarning getWarnings() throws SQLException {
        this.checkOpen();

        try {
            return this.stmt.getWarnings();
        } catch (Throwable var2) {
            throw this.checkException(var2);
        }
    }

    public final void clearWarnings() throws SQLException {
        this.checkOpen();

        try {
            this.stmt.clearWarnings();
        } catch (Throwable var2) {
            throw this.checkException(var2);
        }
    }

    public final void setCursorName(String name) throws SQLException {
        this.checkOpen();

        try {
            this.stmt.setCursorName(name);
        } catch (Throwable var3) {
            throw this.checkException(var3);
        }
    }

    @Override
    public final boolean execute(String sql) throws SQLException {
        checkOpen();

        incrementExecuteCount();
        transactionRecord(sql);

        try {
            if (StringUtils.isNotEmpty(sql)){
                sql = sql.replace("TRUE", "1");
                sql = sql.replace("FALSE", "0");
            }
            return stmt.execute(sql);
        } catch (Throwable t) {
            errorCheck(t);
            throw checkException(t, sql);
        }
    }

    public final ResultSet getResultSet() throws SQLException {
        this.checkOpen();

        try {
            ResultSet rs = this.stmt.getResultSet();
            if (rs == null) {
                return null;
            } else {
                DruidPooledResultSet poolableResultSet = new DruidPooledResultSet(this, rs);
                this.addResultSetTrace(poolableResultSet);
                return poolableResultSet;
            }
        } catch (Throwable var3) {
            throw this.checkException(var3);
        }
    }

    public final int getUpdateCount() throws SQLException {
        this.checkOpen();

        try {
            return this.stmt.getUpdateCount();
        } catch (Throwable var2) {
            throw this.checkException(var2);
        }
    }

    public final boolean getMoreResults() throws SQLException {
        this.checkOpen();

        try {
            boolean moreResults = this.stmt.getMoreResults();
            if (this.resultSetTrace != null && this.resultSetTrace.size() > 0) {
                ResultSet lastResultSet = (ResultSet)this.resultSetTrace.get(this.resultSetTrace.size() - 1);
                if (lastResultSet instanceof DruidPooledResultSet) {
                    DruidPooledResultSet pooledResultSet = (DruidPooledResultSet)lastResultSet;
                    pooledResultSet.closed = true;
                }
            }

            return moreResults;
        } catch (Throwable var4) {
            throw this.checkException(var4);
        }
    }

    public void setFetchDirection(int direction) throws SQLException {
        this.checkOpen();

        try {
            this.stmt.setFetchDirection(direction);
        } catch (Throwable var3) {
            throw this.checkException(var3);
        }
    }

    public final int getFetchDirection() throws SQLException {
        this.checkOpen();

        try {
            return this.stmt.getFetchDirection();
        } catch (Throwable var2) {
            throw this.checkException(var2);
        }
    }

    public void setFetchSize(int rows) throws SQLException {
        this.checkOpen();

        try {
            this.stmt.setFetchSize(rows);
        } catch (Throwable var3) {
            throw this.checkException(var3);
        }
    }

    public final int getFetchSize() throws SQLException {
        this.checkOpen();

        try {
            return this.stmt.getFetchSize();
        } catch (Throwable var2) {
            throw this.checkException(var2);
        }
    }

    public final int getResultSetConcurrency() throws SQLException {
        this.checkOpen();

        try {
            return this.stmt.getResultSetConcurrency();
        } catch (Throwable var2) {
            throw this.checkException(var2);
        }
    }

    public final int getResultSetType() throws SQLException {
        this.checkOpen();

        try {
            return this.stmt.getResultSetType();
        } catch (Throwable var2) {
            throw this.checkException(var2);
        }
    }

    public final void addBatch(String sql) throws SQLException {
        this.checkOpen();
        this.transactionRecord(sql);

        try {
            this.stmt.addBatch(sql);
        } catch (Throwable var3) {
            throw this.checkException(var3, sql);
        }
    }

    public final void clearBatch() throws SQLException {
        if (!this.closed) {
            try {
                this.stmt.clearBatch();
            } catch (Throwable var2) {
                throw this.checkException(var2);
            }
        }
    }

    public int[] executeBatch() throws SQLException {
        this.checkOpen();
        this.incrementExecuteBatchCount();
        this.conn.beforeExecute();

        int[] var1;
        try {
            var1 = this.stmt.executeBatch();
        } catch (Throwable var5) {
            this.errorCheck(var5);
            throw this.checkException(var5);
        } finally {
            this.conn.afterExecute();
        }

        return var1;
    }

    public final Connection getConnection() throws SQLException {
        this.checkOpen();
        return this.conn;
    }

    public final boolean getMoreResults(int current) throws SQLException {
        this.checkOpen();

        try {
            boolean results = this.stmt.getMoreResults(current);
            if (this.resultSetTrace != null && this.resultSetTrace.size() > 0) {
                ResultSet lastResultSet = (ResultSet)this.resultSetTrace.get(this.resultSetTrace.size() - 1);
                if (lastResultSet instanceof DruidPooledResultSet) {
                    DruidPooledResultSet pooledResultSet = (DruidPooledResultSet)lastResultSet;
                    pooledResultSet.closed = true;
                }
            }

            return results;
        } catch (Throwable var5) {
            throw this.checkException(var5);
        }
    }

    public final ResultSet getGeneratedKeys() throws SQLException {
        this.checkOpen();

        try {
            ResultSet rs = this.stmt.getGeneratedKeys();
            DruidPooledResultSet poolableResultSet = new DruidPooledResultSet(this, rs);
            this.addResultSetTrace(poolableResultSet);
            return poolableResultSet;
        } catch (Throwable var3) {
            throw this.checkException(var3);
        }
    }

    public final int getResultSetHoldability() throws SQLException {
        this.checkOpen();

        try {
            return this.stmt.getResultSetHoldability();
        } catch (Throwable var2) {
            throw this.checkException(var2);
        }
    }

    public final boolean isClosed() throws SQLException {
        return this.closed;
    }

    public final void setPoolable(boolean poolable) throws SQLException {
        if (!poolable) {
            throw new SQLException("not support");
        }
    }

    public final boolean isPoolable() throws SQLException {
        return false;
    }

    public String toString() {
        return this.stmt.toString();
    }

    public void closeOnCompletion() throws SQLException {
        this.stmt.closeOnCompletion();
    }

    public boolean isCloseOnCompletion() throws SQLException {
        return this.stmt.isCloseOnCompletion();
    }
}
