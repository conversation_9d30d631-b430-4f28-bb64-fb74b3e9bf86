{"priority": 500, "template": {"settings": {"index": {"lifecycle": {"name": "comm-lifecycle-policy"}, "number_of_shards": "3", "codec": "best_compression"}}, "mappings": {"properties": {"device_code": {"type": "keyword", "index": true}, "source_ip": {"type": "keyword", "index": true}, "trap_message": {"type": "text", "analyzer": "ik_max_word", "index": true}, "@timestamp": {"format": "yyyy-MM-dd HH:mm:ss", "type": "date", "index": true}}}}, "index_patterns": ["metrics-trap-*"], "data_stream": {}, "composed_of": [], "_meta": {"my-custom-meta-field": "More arbitrary metadata", "description": "Template for my time series data"}}