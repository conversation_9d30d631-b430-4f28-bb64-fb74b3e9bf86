{"priority": 550, "template": {"settings": {"index": {"lifecycle": {"name": "common-lifecycle-policy"}, "number_of_shards": "3", "codec": "best_compression"}}, "mappings": {"date_detection": true, "dynamic_date_formats": ["yyyy-MM-dd HH:mm:ss", "dd/MMM/yyyy:HH:mm:ss ZZZ"], "dynamic_templates": [{"string_analyzer": {"match_mapping_type": "string", "mapping": {"analyzer": "ik_max_word", "type": "text", "fields": {"raw": {"type": "keyword", "ignore_above": 256}}}}}], "properties": {"severity": {"type": "integer"}, "hostname": {"ignore_above": 256, "type": "keyword"}, "severityText": {"ignore_above": 256, "type": "keyword"}, "syslogtag": {"ignore_above": 256, "type": "keyword"}, "@timestamp": {"format": "yyyy-MM-dd HH:mm:ss", "index": true, "ignore_malformed": false, "store": false, "type": "date", "doc_values": true}, "sourceIP": {"type": "text", "fields": {"keyword": {"type": "keyword"}}}, "pri": {"type": "integer"}, "facilityText": {"ignore_above": 256, "type": "keyword"}, "source": {"analyzer": "ik_max_word", "type": "text", "fields": {" keyword": {"ignore_above": 256, "type": "keyword"}}}, "message": {"analyzer": "ik_max_word", "type": "text", "fields": {" keyword": {"ignore_above": 256, "type": "keyword"}}}, "facility": {"type": "integer"}, "device_code": {"analyzer": "ik_max_word", "type": "text", "fields": {" keyword": {"ignore_above": 256, "type": "keyword"}}}}}}, "index_patterns": ["syslog*"], "data_stream": {}, "composed_of": [], "_meta": {"my-custom-meta-field": "More arbitrary metadata", "description": "Template for my time series data"}}