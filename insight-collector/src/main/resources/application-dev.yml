server:
  port: 800
  tomcat:
    max-swallow-size: -1
  error:
    include-exception: true
    include-stacktrace: ALWAYS
    include-message: ALWAYS
  servlet:
    context-path: /insight-data
  compression:
    enabled: true
    min-response-size: 1024
    mime-types: application/javascript,application/json,application/xml,text/html,text/xml,text/plain,text/css,image/*

management:
 endpoints:
  web:
   exposure:
    include: metrics,httptrace

spring:
  servlet:
     multipart:
        max-file-size: 10MB
        max-request-size: 10MB
  jmx:
    enabled: true
  elasticsearch:
    index: variable_xj
    rest:
      uris:
        - http://**************:1351
        - http://**************:1352
        - http://**************:1353
      username: elastic
      password: 123456

  ## quartz定时任务,采用数据库方式
  quartz:
    job-store-type: jdbc
    #定时任务启动开关，true-开  false-关
    auto-startup: true
    #延迟1秒启动定时任务
    startup-delay: 1s
    #启动时更新己存在的Job
    overwrite-existing-jobs: true
    properties:
      org:
        quartz:
          scheduler:
            instanceName: CollectorPoc
            instanceId: AUTO
          jobStore:
            class: org.springframework.scheduling.quartz.LocalDataSourceJobStore
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            tablePrefix: QRTZ_
            isClustered: true
            misfireThreshold: 12000
            clusterCheckinInterval: 15000
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true
    jdbc:
      initialize-schema: never

  #json 时间戳统一转换
  jackson:
    date-format:   yyyy-MM-dd HH:mm:ss
    time-zone:   GMT+8
  jpa:
    open-in-view: false
  aop:
    proxy-target-class: true
  # 设置静态文件路径，js,css等
  mvc:
    static-path-pattern: /**
  resource:
    static-locations: classpath:/static/,classpath:/public/
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
        allow:
      web-stat-filter:
        enabled: true
      test-on-borrow: true
      test-while-idle: true
    dynamic:
      druid: # 全局druid参数，绝大部分值和默认保持一致。(现已支持的参数如下,不清楚含义不要乱设置)
        # 连接池的配置信息
        # 初始化大小，最小，最大
        initial-size: 5
        min-idle: 5
        maxActive: 20
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL   #适配瀚高需要删除"FROM DUAL"
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        # 打开PSCache，并且指定每个连接上PSCache的大小
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙 达梦数据库关闭 stat,wall
        filters: slf4j
        # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      datasource:
        master:
          #url: ***********************************************************************************************************************************************************************************
          url: ***********************************************************************************************************************************************************************************
          username: root
          password: 123456
          driver-class-name: com.mysql.cj.jdbc.Driver
            #          url: jdbc:dm://127.0.0.1:5236/SYSDBA
            #          username: SYSDBA
            #          password: SYSDBA
            #          driver-class-name: dm.jdbc.driver.DmDriver
            # 多数据源配置assets/assets
            #multi-datasource1:
            #url: ***************************************************************************************************************************************************************************************************************************
            #username: root
            #password: root
            #driver-class-name: com.mysql.cj.jdbc.Driver
            #达梦
            #          url: jdbc:dm://*************:5236/DMSERVER
            #          username: SYSDBA
            #          password: SYSDBA
            #          driver-class-name: dm.jdbc.driver.DmDriver
            #人大金仓
            #          url: *********************************************
            #          username: SYSTEM
            #          password: 123456
            #          driver-class-name: com.kingbase8.Driver
            #神通数据库
            #          url: ****************************************
            #          username: SYSDBA
            #          password: szoscar55
          #          driver-class-name: com.oscar.Driver
          #瀚高数据库
#            url: ********************************************************************************************************************************************************************************************************************************************
#            username: sysdba
#            password: Hello@1234
#            driver-class-name: com.highgo.jdbc.Driver
  #           南大数据库
  #          url: jdbc:gbasedbt-sqli://192.168.16.129:9088/insightP304:GBASEDBTSERVER=gbase01;db_locale=zh_cn.utf8;client_locale=zh_cn.utf8;NEWCODESET=utf-8,utf8,57372;
  #          username: gbasedbt
  #          password: GBase123
  #          driver-class-name: com.gbasedbt.jdbc.Driver
            #南大通用GBase8s-JYV8.6 S5.0
  #          url: ****************************************************************************************************************************************************************************************************************
  #          username: test_user
  #          password: gbase;123
  #          driver-class-name: org.postgresql.Driver
  #redis 配置
  redis:
    database: 0
    host: **************
    lettuce:
      pool:
        max-active: 10   #最大连接数据库连接数,设 0 为没有限制
        max-idle: 8     #最大等待连接中的数量,设 0 为没有限制
        max-wait: -1ms  #最大建立连接等待时间。如果超过此时间将接到异常。设为-1表示无限制。
        min-idle: 0     #最小等待连接中的数量,设 0 为没有限制
      shutdown-timeout: 10000ms
    jedis:
      pool:
        max-active: 20   #最大连接数据库连接数,设 0 为没有限制
        max-idle: 8     #最大等待连接中的数量,设 0 为没有限制
        max-wait: -1ms  #最大建立连接等待时间。如果超过此时间将接到异常。设为-1表示无限制。
        min-idle: 0     #最小等待连接中的数量,设 0 为没有限制
    password:
    port: 6379
    timeout: 10000
    stream:
      batch-size: 500 #一次最多获取多少条消息
      poll-timeout: 300 #超时时间，设置为0，表示不超时（超时后会抛出异常）,单位毫秒
      group: collector #消费者组
      consumer: auto #消费者,每个节点保持唯
      ack:
        batch-size: 100 #每次最多ack多少条消息
        interval: 100 #定时时间，每xms执行一次,单位毫秒
      idle:
        page-size: 300 #分页大小
        parallelism: 10 #并行度
        time: 15000 #空间时间，查询超过空闲Xms的pending信息
        interval: 30000 #定时时间，每xms执行一次,单位毫秒
#mybatis plus 设置
mybatis-plus:
  mapper-locations: classpath*:org/jeecg/modules/**/xml/*Mapper.xml,classpath*:com/yuanqiao/insight/**/**/**/xml/*Mapper*.xml
  global-config:
    # 关闭MP3.0自带的banner
    banner: false
    db-config:
      #主键类型
      id-type: ASSIGN_ID
      # 默认数据库表下划线命名
      table-underline: true
  configuration:
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 返回类型为Map,显示null对应的字段
    call-setters-on-nulls: true
#slf4j输出sql日志
logging:
  level:
    com.yuanqiao.insight.collection: debug
    com.yuanqiao.insight.product: debug
    com.yuanqiao.insight.acore: debug
    org.jeecg.modules.system.mapper: info
  config: config/logback-collector-spring.xml
work:
  push:
    isUdpOpen: true
    udpPort: 1236
    isTcpOpen: false
    tcpPort: 1237
    host:
    adaptor: pc,jhj,docket,all
  pull:
    isopen: true
  syslog:
    udp:
      isOpen: true
      port: 516
    tcp:
      isOpen: true
      port: 517
  snmpTrap:
      isListen: false

date-substring-length: 7 #取值为7和10。以2023-12-10 00:00:00为例，值为7，结果是2023-12；值为10,结果为2023-12-10

#终端设备在线状态、状态容器存储的时间
#redisTime:
#  time: 300 #秒

influxdb:
  username: admin
  password:
  openurl: http://**************:8086
  database: db-show

#离线时间规则
#statis:
#  time: 30 #分钟,必须大于0

#请求influxdb查询接口 频率
access_influxDB:
  #cron: 0/60 * * * * ?
  cron: 59 59 0-23 * * ?

#网关唯一标识（每个网关需要配置唯一且互不相同的标识）
gatewayCode: gw-device-001

#数据库驱动jar存放路径（数据库设备监控用）
#filePath:
#  connectJar: ${user.dir}/resource/connectJar/

#离线时间规则
#statis:
#  time: 30 #分钟,必须大于0

#终端设备在线状态、状态容器存储的时间
#redisTime:
#  time: 300 #秒

# 网关采集类型（当设置为  aLL 时候将会统计所有终端设备，off 为网关单独统计，与  gatewayStatistics 配合使用）
#gatewayType: aLL

#是否启用网关统计 （当 gatewayStatistics 为 true 时，开启统计，否则该网关不做统计）
#gatewayStatistics: false

#是否启用netty
#enableNetty: true

#终端设备Mac（如有多个则用逗号间隔 02:42:bb:a2:32:e0,02:42:bb:a2:32:e1）
#terminalMacs:

#终端名称前缀(非涉密: tg_ ,涉密： sm_ )
#terminalPrefix: tg_

#是否启用告警状态定时同步
enableAlarm: false
enableAlarmCron: 0/10 * * * * ?

liteflow:
  #ruleSource: config/*.xml
  substitute-cmp-class: com.yuanqiao.insight.flow.node.compont.common.SubCmp
  when-max-wait-seconds: 300
  print-banner: false

#jeecg专用配置
jeecg :
  # 本地：local\Minio：minio\阿里云：alioss
  uploadType: minio
  path :
    #文件上传根目录 设置
    upload: resource
    #webapp文件路径
    webapp: ${user.dir}/resource/default/insight-front
    # minio文件上传
  minio:
    minio_url: http://**************:9000
    minio_name: minio
    minio_pass: admin123!@#
    bucketName: collector
    externalNetwork: http://**************:9000
platform:
  uniqueCode: insight-product-87