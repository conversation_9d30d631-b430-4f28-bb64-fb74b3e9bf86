package com.yuanqiao.insight.utils;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.adaptor.factory.DeviceAdapterSupport;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.regex.Pattern;


/**
 * 正则 工具类
 * <AUTHOR>
 *
 */
@Slf4j
public class JudgeUtils {

    public static String Judge (String s){
        //log.info("com.yuanqiao.insight.utils.JudgeUtils.Judge(s={})", s);

        String regEx1 = "^.+\"ip\".*:.+\"type\".*:.+\"index\".*:.+\"timestamp\".*:.+\"value\".*:.+$";
        String regEx2 = "^.+\"index\".*:.+\"ip\".*:.+\"timestamp\".*:.+\"type\".*:.+\"value\".*:.+$";

        if (Pattern.compile(regEx1,Pattern.CASE_INSENSITIVE | Pattern.DOTALL).matcher(s).matches()){
            //
            return DeviceAdapterSupport.SM4P.getPrefix();
        }else if(Pattern.compile(regEx2,Pattern.CASE_INSENSITIVE | Pattern.DOTALL).matcher(s).matches()){
            //
            return DeviceAdapterSupport.TELEGRAF.getPrefix();
        }else{
            return null;
        }

    }
    public static boolean isJSON(String str) {
        //log.info("com.yuanqiao.insight.utils.JudgeUtils.isJSON(str={})", str);
        boolean result = false;
        try {
            JSON.parse(str);
            result = true;
        } catch (Exception e) {
            //log.info("com.yuanqiao.insight.utils.JudgeUtils.isJSON"+e.getMessage());
            result=false;
        }
        return result;
    }

    public static boolean jsonValue(String str) {
        //log.info("com.yuanqiao.insight.utils.JudgeUtils.jsonValue(str={})", str);
        JSONObject jsonObject = JSONObject.parseObject(str);
        if (jsonObject.getJSONArray("value").size()==0){
            return true;
        }
         return  false;
    }


    public static boolean jsonIndex(String str) {
        //log.info("com.yuanqiao.insight.utils.JudgeUtils.jsonValue(str={})", str);
        JSONObject jsonObject = JSONObject.parseObject(str);
        if (StringUtils.isEmpty(jsonObject.getString("index"))){
            return true;
        }
        return  false;
    }


}
