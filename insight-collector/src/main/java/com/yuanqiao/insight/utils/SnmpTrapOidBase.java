package com.yuanqiao.insight.utils;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * SNMP TRAP告警信息过滤，以及Trap字段映射
 */
public class SnmpTrapOidBase {

    /**
     * 匹配hex字符串e8:99:9a:e6:8b:9f:ef:9c:ba:43:50:55:e5:8d:a0:e7
     * @param hex
     * @return
     */
    public static boolean filerHexString(String hex){
        String pattern = "^([a-zA-Z0-9]{2}:)+[a-zA-Z0-9]{2}$";
        Pattern r = Pattern.compile(pattern);
        // 现在创建 matcher 对象
        Matcher m = r.matcher(hex);
        return m.find();
    }

    /**
     * SNMP TRAP 字段映射  OID->string
     * @return
     */
    public static Map<String, String> snmpTrapMapping(){
        Map<String, String> resMap = new HashMap<>();

        resMap.put("*******.4.1.42400.********.3.3.1","AlarmCsn");
        resMap.put("*******.4.1.42400.********.3.3.2","AlarmCategory");
        resMap.put("*******.4.1.42400.********.3.3.3","AlarmOccurTime");
        resMap.put("*******.4.1.42400.********.3.3.4","AlarmMoName");
        resMap.put("*******.4.1.42400.********.3.3.5","ProductId");
        resMap.put("*******.4.1.42400.********.3.3.6","AlarmNeType");
        resMap.put("*******.4.1.42400.********.3.3.7","AlarmNeDevId");
        resMap.put("*******.4.1.42400.********.3.3.8","AlarmDevCsn");
        resMap.put("*******.4.1.42400.********.3.3.9","AlId");
        resMap.put("*******.4.1.42400.********.3.3.10","AlarmType");
        resMap.put("*******.4.1.42400.********.3.3.11","AlarmLevel");
        resMap.put("*******.4.1.42400.********.3.3.12","AlarmRestore");
        resMap.put("*******.4.1.42400.********.3.3.13","AlarmConfirm");
        resMap.put("*******.4.1.42400.********.3.3.14","AlarmAckTime");
        resMap.put("*******.4.1.42400.********.3.3.15","AlarmRestoreTime");
        resMap.put("*******.4.1.42400.********.3.3.16","AlarmOperator");
        resMap.put("*******.4.1.42400.********.3.3.27","AlarmExtendInfo");
        resMap.put("*******.4.1.42400.********.3.3.28","AlarmProbablecause");
        resMap.put("*******.4.1.42400.********.3.3.29","AlarmProposedrepairactions");
        resMap.put("*******.4.1.42400.********.3.3.30","AlarmSpecificproblems");
        resMap.put("*******.4.1.42400.********.3.3.46","AlarmClearOperator");
        resMap.put("*******.4.1.42400.********.3.3.47","AlarmObjectInstanceType");
        resMap.put("*******.4.1.42400.********.3.3.48","AlarmClearCategory");
        resMap.put("*******.4.1.42400.********.3.3.49","AlarmClearType");
        resMap.put("*******.4.1.42400.********.3.3.50","AlarmServiceAffectFlag");
        resMap.put("*******.4.1.42400.********.3.3.51","AlarmAdditionalInfo");
        resMap.put("*******.4.1.42400.********.3.3.52","AlarmNeIpAddr");

        return resMap;
    }
}