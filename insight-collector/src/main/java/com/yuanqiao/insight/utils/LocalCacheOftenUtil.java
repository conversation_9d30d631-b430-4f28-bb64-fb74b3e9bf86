package com.yuanqiao.insight.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 *
 * 需要频繁修改的本地单例内存类
 *
 */
@Slf4j
public class LocalCacheOftenUtil {
    //创建一个本地的缓存Map，用于缓存数据
    private static Map localCacheStore = new ConcurrentHashMap();

    //一个私有的对象，非懒汉模式
    private static LocalCacheOftenUtil localCacheUtils = new LocalCacheOftenUtil();

    //构造方法私有化，外部不可以再new一个新的对象
    private LocalCacheOftenUtil() {
    }

    //静态方法，用于外部获得实当前例对象
    public static LocalCacheOftenUtil getInstance() {
        return localCacheUtils;
    }


    //从缓存中获取数据
    public Object getValueByKey(String key) {
        return localCacheStore.get(key);
    }

    //向缓存中添加数据
    public void putKeyWithValue(String key, Object value) {
        localCacheStore.put(key, value);
    }

    //判断缓存中是否存在某个Key
    public Boolean ifContainsKey(String key) {
        return localCacheStore.containsKey(key);
    }

}
