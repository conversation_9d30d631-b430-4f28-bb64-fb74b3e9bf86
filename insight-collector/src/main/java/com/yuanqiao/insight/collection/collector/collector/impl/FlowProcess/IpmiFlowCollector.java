package com.yuanqiao.insight.collection.collector.collector.impl.FlowProcess;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.collector.Collector;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.acore.scheduler.SchedulerManagerInter;
import com.yuanqiao.insight.acore.utils.SpringContextUtil;
import com.yuanqiao.insight.collection.collector.utils.IPMICodecUtils;
import com.yuanqiao.insight.service.collector.utils.DataPersistenceStorageUtils;
import com.yuanqiao.insight.service.flow.core.util.IpmiUtil;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import com.yuanqiao.insight.utils.SNMPMetadataUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.scheduling.annotation.Async;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class IpmiFlowCollector implements Collector {
    private final List<String> successMsgList = new ArrayList<>();
    private Device device;
    private SchedulerManagerInter schedulerManager;
    private SNMPMetadataUtils metadataUtils;
    private IPMICodecUtils codecUtils;
    private DataPersistenceStorageUtils storageUtils;
    private IpmiUtil ipmiUtil;

    public void init(Device device, SchedulerManagerInter schedulerManager) {
        //绑定设备
        this.device = device;
        //绑定调度管理器
        this.schedulerManager = schedulerManager;
        //初始化metadataUtils
        this.metadataUtils = (SNMPMetadataUtils) SpringContextUtil.getBean("SNMPMetadataUtils");
        //初始化数据解析组装工具类
        this.codecUtils = new IPMICodecUtils();
        //初始化监控数据持久化工具类
        this.storageUtils = (DataPersistenceStorageUtils) SpringContextUtil.getBean("dataPersistenceStorageUtils");
        //初始化连接参数
        if (device.getConnectParam() != null && !device.getConnectParam().isEmpty()) {
            Map<String, String> connMap = device.getConnectParam();
            ipmiUtil = IpmiUtil.init(connMap.get("ip"), connMap.get("username"), connMap.get("password"));
        }

        successMsgList.add("开机状态");
        successMsgList.add("关机状态");
    }

    @Async
    public void execute() {
        SimpleDateFormat time_sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timestampFormat = time_sdf.format(new Date());
        log.debug("-------- IPMI设备 " + device.getKey() + " 服务器监控任务开始执行...");
        //获取服务器传感器信息
        log.debug("开始获取并解析设备 " + device.getKey() + " 的数据...");
        JSONObject commonMap = new JSONObject();
        try {
            commonMap = ipmiUtil.getRespondByIPMIMap();
            Flow.flowCodecExecute(device.getKey(), commonMap);
            String powerStatus = ipmiUtil.action("powerstatus");
            if (StringUtils.isNotEmpty(powerStatus) && successMsgList.contains(powerStatus)) {
                commonMap.put("powerStatus", powerStatus);
            }
            log.debug("设备 " + device.getKey() + " VxIpmi直取的数据 ：" + commonMap.toString());
        } catch (Exception e) {
            commonMap.clear();
            log.error("设备 " + device.getKey() + " 监控异常！", e);
        }

        if (commonMap.isEmpty()) {
            commonMap.put(device.getProtocol() + "Status", "未连接");
        } else {
            //设备采用当前协议获取数据正常，协议连通性正常
            commonMap.put(device.getProtocol() + "Status", "已连接");

            //设备上线、心跳存入InfluxDB
            log.debug("设备 " + device.getKey() + " 连接成功，放入静态队列容器...");
            storageUtils.heartBeatMessageHandler(schedulerManager, device,timestampFormat);
        }

        //将物模型同步到本地缓存中
        List<ProertyMetadata> metadataList = metadataUtils.setMetadata2(device.getKey(), device.getProtocol());

        //数据组装
        JSONObject jsonObject = this.dataRebuild(device, metadataList, commonMap);
        log.debug("设备 " + device.getKey() + " 解析出的数据 ：" + jsonObject);

        //设置状态容器、发布设备数据事件
        log.debug("将设备 " + device.getKey() + " 的监控数据存入Redis_stca...");
        jsonObject.put("metadataList", metadataList.stream().map(p -> p.getCode()).collect(Collectors.toList()));
        schedulerManager.setStatusCache(device, jsonObject);

        //监控数据持久化到InfluxDB
        log.debug("将设备 " + device.getKey() + " 的监控数据存入Influx_DB...");
        storageUtils.dataStore(device, jsonObject, metadataList,timestampFormat);

        log.debug("********** END ********** " + "IPMI 设备 " + device.getKey() + " 监控任务执行完毕！！！");
    }


    /**
     * 监控数据重组
     *
     * @param device
     * @param metadataList
     * @param commonMap
     * @return
     */
    public JSONObject dataRebuild(Device device, List<ProertyMetadata> metadataList, Map<String, Object> commonMap) {
        JSONObject jsonObject = new JSONObject();
        String stcaKey = "stca:" + device.getKey();
        metadataList.forEach(metadata -> {
            codecUtils.switchByDataType(metadata, stcaKey, jsonObject, commonMap);
        });
        return jsonObject;
    }

}
