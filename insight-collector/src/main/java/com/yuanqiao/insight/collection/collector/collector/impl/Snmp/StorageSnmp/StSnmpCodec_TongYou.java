package com.yuanqiao.insight.collection.collector.collector.impl.Snmp.StorageSnmp;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.collection.collector.utils.SNMPCodecAndCollectUtils;
import com.yuanqiao.insight.collection.collector.utils.SNMPCodecInterface;
import com.yuanqiao.insight.common.util.common.TimeUtils;
import com.yuanqiao.insight.common.util.snmp.SNMPUtils;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class StSnmpCodec_TongYou implements SNMPCodecInterface {
    // 操作系统类型
    String osType;

    public void setOsType(String osType) {
        this.osType = osType;
    }

    //最外层jsonObject
    JSONObject jsonObject = new JSONObject();
    //公共结果集
    JSONObject commonMap = new JSONObject();

    @Override
    public JSONObject dataCodec(SNMPUtils snmpUtils, List<ProertyMetadata> metadataList, String stcaKey, Device device, SNMPCodecAndCollectUtils snmpCodecAndCollectUtils) {
        //清空外层jsonObject和公共结果集commonMap
        jsonObject.clear();
        commonMap.clear();

        //静态属性
        HashMap<String, Object> staticInfoMap = new HashMap<>();

        staticInfoMap.put("switchName", getName(snmpUtils).get("switchName"));
        staticInfoMap.put("switchDesc", getSysDesc(snmpUtils).get("switchDesc"));
        staticInfoMap.put("portNum", getPortNum(snmpUtils).get("portNum"));
        staticInfoMap.put("runTime", getRunTime(snmpUtils).get("runTime"));
        commonMap.put("staticInfo", staticInfoMap);
        commonMap.put("sysUpTime", getSysUpTime(snmpUtils).get("sysUpTime"));

        commonMap.put("diskRate", getDiskUsageRate(snmpUtils).get("diskRate"));
        commonMap.put("diskUsed", getDiskUsageRate(snmpUtils).get("diskUsed"));
        commonMap.put("diskTotal", getDiskUsageRate(snmpUtils).get("diskTotal"));

        //物理磁盘信息
        commonMap.put("physicalDiskInfo", getPhysicalDiskInfo(snmpUtils, "*******.4.1.42347.1.2.1").get("physicalDiskInfo"));
        //RAID组信息
        commonMap.put("raidGroupInfo", getRaidGroupInfoList(snmpUtils, "*******.4.1.42347.1.2.2").get("raidGroupInfo"));
        //虚拟磁盘信息
        commonMap.put("virtualDiskInfo", getVirtualDiskInfoList(snmpUtils, "*******.4.1.42347.1.2.3").get("virtualDiskInfo"));
        //逻辑单元信息
        commonMap.put("logicalUnitInfo", getLogicalUnitInfoList(snmpUtils, "*******.4.1.42347.1.2.4").get("logicalUnitInfo"));

        metadataList.forEach(item -> {
            if (StringUtils.isNotEmpty(item.getDataType())) {
                jsonObject = snmpCodecAndCollectUtils.switchByDataType(item, stcaKey, jsonObject, commonMap, snmpUtils);
            }
        });
        return jsonObject;
    }

    /* ---------------------------------------------------------------------------------------------------------------- */

    /**
     * 获取指定OID的所有实例值之和
     *
     * @param metricName 指标名称，用于日志记录
     * @param oid        SNMP OID
     * @param snmpUtils  SNMP工具类
     * @return 所有实例值的累加和（单位：字节）
     */
    private Long getTotalValueByOid(String metricName, String oid, SNMPUtils snmpUtils) {
        try {
            Map<String, String> pduWalk = snmpUtils.getPDUWalk(oid);
            if (pduWalk == null || pduWalk.isEmpty()) {
                log.warn("未获取到" + metricName + "相关OID数据，OID=" + oid);
                return 0L;
            }

            long total = 0;
            for (Map.Entry<String, String> entry : pduWalk.entrySet()) {
                total += Long.parseLong(entry.getValue());
            }
            return total;
        } catch (NumberFormatException e) {
            log.error("解析{}数值失败，OID={}", metricName, oid, e);
            return 0L;
        } catch (Exception e) {
            log.error("获取{}出错，OID={}", metricName, oid, e);
            return 0L;
        }
    }


    //获磁盘使用率
    public Map<String, Object> getDiskUsageRate(SNMPUtils snmpUtils) {
        HashMap<String, Object> resMap = new HashMap<>();
        try {
            long diskUsed = getTotalValueByOid("diskUsed", "*******.4.1.42347.1.6.5.1.9", snmpUtils);
            long diskFree = getTotalValueByOid("diskFree", "*******.4.1.42347.1.6.5.1.12", snmpUtils);
            long diskTotal = diskUsed + diskFree;
            double usageRate = (diskTotal > 0) ? (diskUsed * 100.0 / diskTotal) : 0.0;

            resMap.put("diskUsed", diskUsed);
            resMap.put("diskTotal", diskUsed + diskFree);
            resMap.put("diskRate", Math.round(usageRate * 100) / 100.0);
            return resMap;
        } catch (Exception e) {
            resMap.put("diskUsed", 0.0);
            resMap.put("diskTotal", 0.0);
            resMap.put("diskRate", 0.0);
            log.error("获取磁盘使用率出错{}", e.getMessage());
            return null;
        }
    }

    /**
     * 获取设备运行时长
     *
     * @param snmpUtils
     * @return
     */
    private Map<String, String> getSysUpTime(SNMPUtils snmpUtils) {
        HashMap<String, String> map = new HashMap<>();
        try {
            String sysUpTime = snmpUtils.getPDU("*******.2.1.1.3.0");
            if (StringUtils.isNotEmpty(sysUpTime) && !sysUpTime.equalsIgnoreCase("noSuchObject")
                    && !sysUpTime.equalsIgnoreCase("noSuchInstance")) {
                // 去除字符串中的逗号
                sysUpTime = sysUpTime.replace(",", "");

                // 使用正则表达式匹配时间信息
                Pattern pattern = Pattern.compile("(?:(\\d+) days?)?(?:\\s*,?\\s*(\\d+):(\\d+)(?::(\\d+(?:\\.\\d+)?))?)?");
                Matcher matcher = pattern.matcher(sysUpTime);

                int days = 0, hours = 0, minutes = 0, seconds = 0;
                double milliseconds = 0;

                if (matcher.find()) {
                    if (matcher.group(1) != null) {
                        days = Integer.parseInt(matcher.group(1));
                    }
                    if (matcher.group(2) != null) {
                        hours = Integer.parseInt(matcher.group(2));
                    }
                    if (matcher.group(3) != null) {
                        minutes = Integer.parseInt(matcher.group(3));
                    }
                    if (matcher.group(4) != null) {
                        String[] secondsParts = matcher.group(4).split("\\.");
                        seconds = Integer.parseInt(secondsParts[0]);
                        if (secondsParts.length > 1) {
                            milliseconds = Double.parseDouble("0." + secondsParts[1]);
                        }
                    }
                }
                // 计算总毫秒数
                BigInteger totalMilliseconds = BigInteger.valueOf(days * 24 * 60 * 60 * 1000L)
                        .add(BigInteger.valueOf(hours * 60 * 60 * 1000L))
                        .add(BigInteger.valueOf(minutes * 60 * 1000L))
                        .add(BigInteger.valueOf((long) (seconds * 1000 + milliseconds * 1000)));
                sysUpTime = TimeUtils.getDistanceTime(totalMilliseconds);
                map.put("sysUpTime", sysUpTime);
            }
        } catch (Exception e) {
            log.error("获取运行时长出错", e);
            map.put("sysUpTime", "");
        }
        return map;
    }


    //获取同有存储端口数
    private Map<String, Integer> getPortNum(SNMPUtils snmpUtils) {
        try {
            HashMap<String, Integer> map = new HashMap<>();
            // 同有存储端口数
            String portNum_String = snmpUtils.getPDU("*******.2.1.2.1.0");
            if (StringUtils.isEmpty(portNum_String)) {
                log.error("未获取到同有存储端口数...");
                map.put("portNum", 0);
            } else {
                Integer portNum = Integer.parseInt(portNum_String);
                map.put("portNum", portNum);
            }
            return map;
        } catch (Exception e) {
            log.error("获取同有存储端口数出错", e);
            HashMap<String, Integer> map = new HashMap<>();
            map.put("portNum", 0);
            return map;
        }
    }

    //获取同有存储名称
    private Map<String, String> getName(SNMPUtils snmpUtils) {
        try {
            HashMap<String, String> map = new HashMap<>();
            // 同有存储名称
            String ifName = snmpUtils.getPDU("*******.2.1.1.5.0");
            if (ifName == null || "".equals(ifName.trim()) || "null".equalsIgnoreCase(ifName.trim())) {
                log.error("未获取到同有存储名称...");
                map.put("switchName", "- -");
            } else {
                map.put("switchName", ifName);
            }
            return map;
        } catch (Exception e) {
            log.error("获取同有存储名称出错", e);
            HashMap<String, String> map = new HashMap<>();
            map.put("switchName", "- -");
            return map;
        }
    }

    //获取同有存储描述
    private Map<String, String> getSysDesc(SNMPUtils snmpUtils) {
        try {
            HashMap<String, String> map = new HashMap<>();
            // 同有存储描述
            String sysDesc = snmpUtils.getPDU("*******.2.1.1.1.0");
            if (sysDesc == null || "".equals(sysDesc.trim()) || "null".equalsIgnoreCase(sysDesc.trim())) {
                log.error("未获取到同有存储描述...");
                map.put("switchDesc", "- -");
            }
            map.put("switchDesc", sysDesc);
            return map;
        } catch (Exception e) {
            log.error("获取同有存储描述出错", e);
            HashMap<String, String> map = new HashMap<>();
            map.put("switchDesc", "- -");
            return map;
        }
    }

    //获取同有存储运行时间
    private Map<String, String> getRunTime(SNMPUtils snmpUtils) {
        try {
            HashMap<String, String> map = new HashMap<>();
            // 同有存储运行时间
            String sysRunTime = snmpUtils.getPDU("*******.2.1.1.3.0");
            if (sysRunTime == null || "".equals(sysRunTime.trim()) || "null".equalsIgnoreCase(sysRunTime.trim())) {
                log.error("未获取到鲸鲨双控运行时间...");
                map.put("runTime", "--");
            }
//            if (!"".equals(sysRunTime) && sysRunTime != null && !sysRunTime.isEmpty()) {
//                String[] categoryArr = sysRunTime.split(":");
//                String dayRep = categoryArr[0].replace("days", "天");
//                String seconds = categoryArr[2].substring(0, 2);
//                sysRunTime = dayRep + ":" + categoryArr[1] + ":" + seconds;
//            }
            map.put("runTime", sysRunTime);
            return map;
        } catch (Exception e) {
            log.error("获取同有存储运行时间出错", e);
            HashMap<String, String> map = new HashMap<>();
            map.put("runTime", "--");
            return map;
        }
    }

    /**
     * 物理磁盘信息
     *
     * @param snmpUtils
     * @return
     */
    public Map<String, Object> getPhysicalDiskInfo(SNMPUtils snmpUtils, String tableTopOid) {
        HashMap<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> physicalDiskInfoList = new ArrayList<>();

        try {
            JSONObject tableData = snmpUtils.getPduTableView(tableTopOid);
            if (tableData != null && !tableData.isEmpty()) {
                JSONArray dataArray = tableData.getJSONArray("dataList");
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject eleObject = dataArray.getJSONObject(i);
                    Map<String, Object> res = new HashMap<>();

                    //索引
                    res.put("location", eleObject.getString("*******.4.1.42347.1.2.1.1.1"));
                    //磁盘容量GB
                    res.put("size", eleObject.getString("*******.4.1.42347.1.2.1.1.2"));
                    //所属RAID组名称
                    res.put("raidGroup", eleObject.getString("*******.4.1.42347.1.2.1.1.3"));
                    //运行状态
                    res.put("statusCode", eleObject.getString("*******.4.1.42347.1.2.1.1.4"));
                    //健康状态
                    res.put("healthCode", eleObject.getString("*******.4.1.42347.1.2.1.1.5"));
                    //厂商名称
                    res.put("vendor", eleObject.getString("*******.4.1.42347.1.2.1.1.6"));
                    //序列号
                    res.put("serial", eleObject.getString("*******.4.1.42347.1.2.1.1.7"));
                    //磁盘类型
                    res.put("typeCode", eleObject.getString("*******.4.1.42347.1.2.1.1.8"));
                    physicalDiskInfoList.add(res);
                }
            }
            resultMap.put("physicalDiskInfo", physicalDiskInfoList);
            return resultMap;
        } catch (Exception e) {
            log.error("获取物理磁盘信息出错！", e);
            return null;
        }
    }

//    public Map<String, Object> getPhysicalDiskInfo(SNMPUtils snmpUtils) {
//        Map<String, Object> resMap = new HashMap<>();
//        try {
//            //磁盘物理位置
//            String location = snmpUtils.getPDU("*******.4.1.42347.1.2.1.1.1.0");
//            resMap.put("location", location);
//            //磁盘容量GB
//            String size = snmpUtils.getPDU("*******.4.1.42347.1.2.1.1.2.0");
//            resMap.put("size", size);
//            //所属RAID组名称
//            String raidGroup = snmpUtils.getPDU("*******.4.1.42347.1.2.1.1.3.0");
//            resMap.put("raidGroup", raidGroup);
//            //运行状态
//            String statusCode = snmpUtils.getPDU("*******.4.1.42347.1.2.1.1.4.0");
//            resMap.put("statusCode", statusCode);
//            //健康状态
//            String healthCode = snmpUtils.getPDU("*******.4.1.42347.1.2.1.1.5.0");
//            resMap.put("healthCode", healthCode);
//            //厂商名称
//            String vendor = snmpUtils.getPDU("*******.4.1.42347.1.2.1.1.6.0");
//            resMap.put("vendor", vendor);
//            //序列号
//            String serial = snmpUtils.getPDU("*******.4.1.42347.1.2.1.1.7.0");
//            resMap.put("serial", serial);
//            //磁盘类型
//            String typeCode = snmpUtils.getPDU("*******.4.1.42347.1.2.1.1.8.0");
//            resMap.put("typeCode", typeCode);
//
//        } catch (Exception e) {
//            log.error("获取物理磁盘信息出错", e);
//        }
//        return resMap;
//    }

    /**
     * 获取所有RAID组的详细信息列表
     *
     * @param snmpUtils   SNMP工具类实例，用于执行SNMP查询
     * @param tableTopOid RAID组表格的顶级OID
     * @return 包含所有RAID组信息的Map，每个RAID组为一个Map条目
     */
    public Map<String, Object> getRaidGroupInfoList(SNMPUtils snmpUtils, String tableTopOid) {
        HashMap<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> raidGroupInfoList = new ArrayList<>();

        try {
            JSONObject tableData = snmpUtils.getPduTableView(tableTopOid);
            if (tableData != null && !tableData.isEmpty()) {
                JSONArray dataArray = tableData.getJSONArray("dataList");

                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject eleObject = dataArray.getJSONObject(i);
                    Map<String, Object> res = new HashMap<>();

                    //名称
                    res.put("name", eleObject.getString("*******.4.1.42347.1.2.2.1.1"));
                    //总容量GB
                    res.put("total", eleObject.getString("*******.4.1.42347.1.2.2.1.2"));
                    // //空闲容量GB
                    res.put("free", eleObject.getString("*******.4.1.42347.1.2.2.1.3"));

                    //物理磁盘（PD）列表
                    res.put("physicalDisks", eleObject.getString("*******.4.1.42347.1.2.2.1.4"));
                    //虚拟磁盘（VD）列表
                    res.put("virtualDisks", eleObject.getString("*******.4.1.42347.1.2.2.1.5"));

                    //当前状态
                    res.put("statusCode", eleObject.getString("*******.4.1.42347.1.2.2.1.6"));
                    //健康状态
                    res.put("healthCode", eleObject.getString("*******.4.1.42347.1.2.2.1.7"));


                    raidGroupInfoList.add(res);
                }
            }

            resultMap.put("raidGroupInfo", raidGroupInfoList);
            return resultMap;
        } catch (Exception e) {
            log.error("获取RAID组信息列表出错！", e);
            return null;
        }
    }

//    /**
//     * 获取RAID组的详细信息
//     *
//     * @param snmpUtils SNMP工具类实例，用于执行SNMP查询
//     * @return 包含所有RAID组参数的Map，键为参数名，值为参数值
//     */
//    public Map<String, Object> getRaidGroupInfo(SNMPUtils snmpUtils) {
//        Map<String, Object> resMap = new HashMap<>();
//        try {
//            //名称
//            String name = snmpUtils.getPDU("*******.4.1.42347.1.2.2.1.1.0");
//            resMap.put("name", name);
//            //总容量GB
//            String total = snmpUtils.getPDU("*******.4.1.42347.1.2.2.1.2.0");
//            resMap.put("total", total);
//            //空闲容量GB
//            String free = snmpUtils.getPDU("*******.4.1.42347.1.2.2.1.3.0");
//            resMap.put("free", free);
//
//            //物理磁盘（PD）列表
//            String physicalDisks = snmpUtils.getPDU("*******.4.1.42347.1.2.2.1.4.0");
//            resMap.put("physicalDisks", physicalDisks);
//            //虚拟磁盘（VD）列表
//            String virtualDisks = snmpUtils.getPDU("*******.4.1.42347.1.2.2.1.5.0");
//            resMap.put("virtualDisks", virtualDisks);
//
//            //当前状态
//            String statusCode = snmpUtils.getPDU("*******.4.1.42347.1.2.2.1.6.0");
//            resMap.put("statusCode", statusCode);
//            //健康状态
//            String healthCode = snmpUtils.getPDU("*******.4.1.42347.1.2.2.1.7.0");
//            resMap.put("healthCode", healthCode);
//
//
//        } catch (Exception e) {
//            log.error("获取RAID组信息出错", e);
//        }
//        return resMap;
//    }

    /**
     * 获取所有虚拟磁盘的详细信息列表
     *
     * @param snmpUtils   SNMP工具类实例，用于执行SNMP查询
     * @param tableTopOid 虚拟磁盘表格的顶级OID
     * @return 包含所有虚拟磁盘信息的Map，每个虚拟磁盘为一个Map条目
     */
    public Map<String, Object> getVirtualDiskInfoList(SNMPUtils snmpUtils, String tableTopOid) {
        HashMap<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> virtualDiskInfoList = new ArrayList<>();

        try {
            JSONObject tableData = snmpUtils.getPduTableView(tableTopOid);
            if (tableData != null && !tableData.isEmpty()) {
                JSONArray dataArray = tableData.getJSONArray("dataList");

                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject eleObject = dataArray.getJSONObject(i);
                    Map<String, Object> res = new HashMap<>();

                    //名称
                    res.put("name", eleObject.getString("*******.4.1.42347.1.2.3.1.1"));
                    //总容量
                    res.put("size", eleObject.getString("*******.4.1.42347.1.2.3.1.2"));

                    //访问权限
                    res.put("accessRight", eleObject.getString("*******.4.1.42347.1.2.3.1.3"));
                    //优先级
                    res.put("priority", eleObject.getString("*******.4.1.42347.1.2.3.1.4"));
                    //后台操作速率
                    res.put("backgroundRate", eleObject.getString("*******.4.1.42347.1.2.3.1.5"));
                    //当前状态
                    res.put("statusCode", eleObject.getString("*******.4.1.42347.1.2.3.1.6"));
                    //健康状态
                    res.put("healthCode", eleObject.getString("*******.4.1.42347.1.2.3.1.7"));

                    virtualDiskInfoList.add(res);
                }
            }

            resultMap.put("virtualDiskInfo", virtualDiskInfoList);
            return resultMap;
        } catch (Exception e) {
            log.error("获取虚拟磁盘信息列表出错！", e);
            return null;
        }
    }

//    /**
//     * 虚拟磁盘信息
//     *
//     * @param snmpUtils
//     * @return
//     */
//    public Map<String, Object> getVirtualDiskInfo(SNMPUtils snmpUtils) {
//        Map<String, Object> resMap = new HashMap<>();
//        try {
//            //名称
//            String name = snmpUtils.getPDU("*******.4.1.42347.1.2.3.1.1.0");
//            resMap.put("name", name);
//            //总容量
//            String size = snmpUtils.getPDU("*******.4.1.42347.1.2.3.1.2.0");
//            resMap.put("size", size);
//
//            //访问权限
//            String accessRight = snmpUtils.getPDU("*******.4.1.42347.1.2.3.1.3.0");
//            resMap.put("accessRight", accessRight);
//            //优先级
//            String priority = snmpUtils.getPDU("*******.4.1.42347.1.2.3.1.4.0");
//            resMap.put("priority", priority);
//            //后台操作速率
//            String backgroundRate = snmpUtils.getPDU("*******.4.1.42347.1.2.3.1.5.0");
//            resMap.put("backgroundRate", backgroundRate);
//
//            //状态
//            String statusCode = snmpUtils.getPDU("*******.4.1.42347.1.2.3.1.6.0");
//            resMap.put("statusCode", statusCode);
//            //健康状态
//            String healthCode = snmpUtils.getPDU("*******.4.1.42347.1.2.3.1.7.0");
//            resMap.put("healthCode", healthCode);
//
//        } catch (Exception e) {
//            log.error("获取虚拟磁盘信息出错", e);
//        }
//        return resMap;
//    }

    /**
     * 获取所有逻辑单元（LUN）的详细信息列表
     *
     * @param snmpUtils   SNMP工具类实例，用于执行SNMP查询
     * @param tableTopOid LUN表格的顶级OID
     * @return 包含所有LUN信息的Map，每个LUN为一个Map条目
     */
    public Map<String, Object> getLogicalUnitInfoList(SNMPUtils snmpUtils, String tableTopOid) {
        HashMap<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> logicalUnitInfoList = new ArrayList<>();

        try {
            JSONObject tableData = snmpUtils.getPduTableView(tableTopOid);
            if (tableData != null && !tableData.isEmpty()) {
                JSONArray dataArray = tableData.getJSONArray("dataList");
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject eleObject = dataArray.getJSONObject(i);
                    Map<String, Object> res = new HashMap<>();

                    // 索引
                    res.put("index", eleObject.getString("*******.4.1.42347.*******.1"));
                    // 关联的主机
                    res.put("host", eleObject.getString("*******.4.1.42347.*******.2"));
                    // 关联的存储目标
                    res.put("target", eleObject.getString("*******.4.1.42347.*******.3"));
                    // LUN编号
                    res.put("lunNumber", eleObject.getString("*******.4.1.42347.*******.4"));
                    // 访问权限
                    res.put("permissionCode", eleObject.getString("*******.4.1.42347.*******.5"));

                    logicalUnitInfoList.add(res);
                }
            }
            resultMap.put("logicalUnitInfo", logicalUnitInfoList);
            return resultMap;
        } catch (Exception e) {
            log.error("获取逻辑单元信息列表出错！", e);
            return null;
        }
    }

//    /**
//     * 获取逻辑单元（LUN）的详细信息
//     *
//     * @param snmpUtils SNMP工具类实例，用于执行SNMP查询
//     * @return 包含所有LUN参数的Map，键为参数名，值为参数值
//     */
//    public Map<String, Object> getLogicalUnitInfo(SNMPUtils snmpUtils) {
//        Map<String, Object> resMap = new HashMap<>();
//        try {
//            //索引
//            String index = snmpUtils.getPDU("*******.4.1.42347.*******.1.0");
//            resMap.put("index", index);
//            //关联的主机
//            String host = snmpUtils.getPDU("*******.4.1.42347.*******.2.0");
//            resMap.put("host", host);
//            //关联的存储目标
//            String target = snmpUtils.getPDU("*******.4.1.42347.*******.3.0");
//            resMap.put("target", target);
//
//        } catch (Exception e) {
//            log.error("获取逻辑单元信息出错", e);
//        }
//        return resMap;
//    }


    /**
     * 网络接口信息
     *
     * @param snmpUtils
     * @param tableTopOid
     * @return
     */
    private Map<String, Object> getInterfaceInfoList(SNMPUtils snmpUtils, String tableTopOid) {
        HashMap<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> interfaceInfoList = new ArrayList<>();
        try {
            JSONObject tableData = snmpUtils.getPduTableView(tableTopOid);
            if (tableData != null && !tableData.isEmpty()) {
                JSONArray dataArray = tableData.getJSONArray("dataList");
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject eleObject = dataArray.getJSONObject(i);
                    Map<String, Object> res = new HashMap<>();

                    //索引
                    res.put("interfaceIndex", eleObject.getString("*******.4.1.38696.2.32.2.1.1"));
                    //网口名称
                    res.put("interfaceName", eleObject.getString("*******.4.1.38696.2.32.2.1.2"));
                    //网口模式
                    res.put("interfaceMode", eleObject.getString("*******.4.1.38696.2.32.2.1.3"));
                    //网口连接状态
                    res.put("interfaceLink", eleObject.getString("*******.4.1.38696.2.32.2.1.4"));
                    //网口启动状态
                    res.put("interfaceUp", eleObject.getString("*******.4.1.38696.2.32.2.1.5"));
                    //网口动/静态
                    res.put("interfaceBootproto", eleObject.getString("*******.4.1.38696.2.32.2.1.6"));

                    interfaceInfoList.add(res);
                }
            }
            resultMap.put("interfaceInfo", interfaceInfoList);
            return resultMap;
        } catch (Exception e) {
            log.error("获取网络接口信息出错！", e);
            return null;
        }
    }

    /**
     * 存储池信息
     *
     * @param snmpUtils
     * @param tableTopOid
     * @return
     */
    private Map<String, Object> getPoolInfoList(SNMPUtils snmpUtils, String tableTopOid) {
        HashMap<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> poolInfoList = new ArrayList<>();
        try {
            JSONObject tableData = snmpUtils.getPduTableView(tableTopOid);
            if (tableData != null && !tableData.isEmpty()) {
                JSONArray dataArray = tableData.getJSONArray("dataList");
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject eleObject = dataArray.getJSONObject(i);
                    Map<String, Object> res = new HashMap<>();

                    //索引
                    res.put("poolIndex", eleObject.getString("*******.4.1.38696.2.21.2.1.1"));
                    //存储池名称
                    res.put("poolName", eleObject.getString("*******.4.1.38696.2.21.2.1.2"));
                    //存储池使用率
                    res.put("poolUsed", eleObject.getString("*******.4.1.38696.2.21.2.1.3"));
                }
            }
            resultMap.put("poolInfo", poolInfoList);
            return resultMap;
        } catch (Exception e) {
            log.error("获取存储池信息出错！", e);
            return null;
        }
    }
}
