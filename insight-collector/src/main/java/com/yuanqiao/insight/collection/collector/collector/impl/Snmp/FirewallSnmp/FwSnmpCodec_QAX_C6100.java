package com.yuanqiao.insight.collection.collector.collector.impl.Snmp.FirewallSnmp;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.collection.collector.utils.SNMPCodecAndCollectUtils;
import com.yuanqiao.insight.collection.collector.utils.SNMPCodecInterface;
import com.yuanqiao.insight.common.util.common.TimeUtils;
import com.yuanqiao.insight.common.util.snmp.SNMPUtils;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigInteger;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class FwSnmpCodec_QAX_C6100 implements SNMPCodecInterface {

    // 操作系统类型
    String osType;

    public void setOsType(String osType) {
        this.osType = osType;
    }

    //最外层jsonObject
    JSONObject jsonObject = new JSONObject();
    //公共结果集
    JSONObject commonMap = new JSONObject();

    @Override
    public JSONObject dataCodec(SNMPUtils snmpUtils, List<ProertyMetadata> metadataList, String stcaKey, Device device, SNMPCodecAndCollectUtils snmpCodecAndCollectUtils) {
        //清空外层jsonObject和公共结果集commonMap
        jsonObject.clear();
        commonMap.clear();

        //静态信息
        Map<String, Object> staticInfoMap = new HashMap<>();
        staticInfoMap.put("runTime", getRunTime(snmpUtils).get("runTime"));
        staticInfoMap.put("sysDate", getSysDate(snmpUtils).get("sysDate"));
        commonMap.put("staticInfo", staticInfoMap);
        commonMap.put("sysUpTime", getSysUpTime(snmpUtils).get("sysUpTime"));

        //CPU信息
        Map<String, Object> cpuInfoMap = new HashMap<>();
        cpuInfoMap.put("userCpu", getUserCpu(snmpUtils).get("userCpu"));
        cpuInfoMap.put("systemCpu", getSystemCpu(snmpUtils).get("systemCpu"));
        cpuInfoMap.put("idleCpu", getIdleCpu(snmpUtils).get("idleCpu"));
        commonMap.put("cpuInfo", cpuInfoMap);

        commonMap.put("cpuRate", getCpuRate(cpuInfoMap).get("cpuRate"));

        //内存信息
        commonMap.put("memTotal", getMemTotal(snmpUtils).get("memTotal"));
        commonMap.put("memUsed", getMemUsed(snmpUtils).get("memUsed"));
        commonMap.put("memRate", getMemRate(commonMap).get("memRate"));

        //磁盘使用率
        commonMap.put("diskRate", getDiskRate(snmpUtils).get("diskRate"));

        //网络吞吐量
        commonMap.put("netInOut", getNetInAndOut(snmpUtils).get("netInOut"));
        //端口信息
        commonMap.put("portInfo", getPortInfoList(snmpUtils));

//        //平均负载信息
//        Map<String, Object> avgLoadInfoMap = new HashMap<>();
//        avgLoadInfoMap.put("lastOne", getLastOne(snmpUtils).get("lastOne"));
//        avgLoadInfoMap.put("lastFive", getLastFive(snmpUtils).get("lastFive"));
//        avgLoadInfoMap.put("LastFifteen", getLastFifteen(snmpUtils).get("LastFifteen"));
//        commonMap.put("avgLoadInfo", avgLoadInfoMap);
//
//        //TCP、UDP信息
//        Map<String, Object> tcpUdpInfoMap = new HashMap<>();
//        tcpUdpInfoMap.put("tcpConnState", getTcpConnState(snmpUtils).get("tcpConnState"));
//        tcpUdpInfoMap.put("tcpConnLocalAddress", getTcpConnLocalAddress(snmpUtils).get("tcpConnLocalAddress"));
//        tcpUdpInfoMap.put("tcpConnLocalPort", getTcpConnLocalPort(snmpUtils).get("tcpConnLocalPort"));
//        tcpUdpInfoMap.put("tcpConnRemAddress", getTcpConnRemAddress(snmpUtils).get("tcpConnRemAddress"));
//        tcpUdpInfoMap.put("tcpConnRemPort", getTcpConnRemPort(snmpUtils).get("tcpConnRemPort"));
//        tcpUdpInfoMap.put("udpLocalAddress", getUdpLocalAddress(snmpUtils).get("udpLocalAddress"));
//        tcpUdpInfoMap.put("udpLocalPort", getUdpLocalPort(snmpUtils).get("udpLocalPort"));
//        commonMap.put("tcpUdpInfo", tcpUdpInfoMap);

        //循环遍历物模型
        metadataList.forEach(item -> {
            if (StringUtils.isNotEmpty(item.getDataType())) {
                jsonObject = snmpCodecAndCollectUtils.switchByDataType(item, stcaKey, jsonObject, commonMap, snmpUtils);
            }
        });

        return jsonObject;
    }
    /**
     * 获取设备运行时长
     *
     * @param snmpUtils
     * @return
     */
    private Map<String, String> getSysUpTime(SNMPUtils snmpUtils) {
        HashMap<String, String> map = new HashMap<>();
        try {
            String sysUpTime = snmpUtils.getPDU("*******.*******.0");
            if (StringUtils.isNotEmpty(sysUpTime) && !sysUpTime.equalsIgnoreCase("noSuchObject")
                    && !sysUpTime.equalsIgnoreCase("noSuchInstance")) {
                // 去除字符串中的逗号
                sysUpTime = sysUpTime.replace(",", "");

                // 使用正则表达式匹配时间信息
                Pattern pattern = Pattern.compile("(?:(\\d+) days?)?(?:\\s*,?\\s*(\\d+):(\\d+)(?::(\\d+(?:\\.\\d+)?))?)?");
                Matcher matcher = pattern.matcher(sysUpTime);

                int days = 0, hours = 0, minutes = 0, seconds = 0;
                double milliseconds = 0;

                if (matcher.find()) {
                    if (matcher.group(1) != null) {
                        days = Integer.parseInt(matcher.group(1));
                    }
                    if (matcher.group(2) != null) {
                        hours = Integer.parseInt(matcher.group(2));
                    }
                    if (matcher.group(3) != null) {
                        minutes = Integer.parseInt(matcher.group(3));
                    }
                    if (matcher.group(4) != null) {
                        String[] secondsParts = matcher.group(4).split("\\.");
                        seconds = Integer.parseInt(secondsParts[0]);
                        if (secondsParts.length > 1) {
                            milliseconds = Double.parseDouble("0." + secondsParts[1]);
                        }
                    }
                }
                // 计算总毫秒数
                BigInteger totalMilliseconds = BigInteger.valueOf(days * 24 * 60 * 60 * 1000L)
                        .add(BigInteger.valueOf(hours * 60 * 60 * 1000L))
                        .add(BigInteger.valueOf(minutes * 60 * 1000L))
                        .add(BigInteger.valueOf((long) (seconds * 1000 + milliseconds * 1000)));
                sysUpTime = TimeUtils.getDistanceTime(totalMilliseconds);
                map.put("sysUpTime", sysUpTime);
            }
        } catch (Exception e) {
            log.error("获取运行时长出错", e);
            map.put("sysUpTime", "");
        }
        return map;
    }
    /* ---------------------------------------------------------------------------------------------------------------- */
    //获取项目顶级目录所属路径
    public static void main(String[] args) {
        System.out.println(System.getProperty("user.dir"));
    }
    /* ---------------------------------------------------------------------------------------------------------------- */

    //获取系统日期
    private Map<String, String> getSysDate(SNMPUtils snmpUtils) {
        try {
            HashMap<String, String> map = new HashMap<>();
            // 描述
            String sysDate = snmpUtils.getPDU(".*******.2.1.25.1.2");
            if (StringUtils.isNotEmpty(sysDate) && !sysDate.equalsIgnoreCase("noSuchInstance")  && !sysDate.equalsIgnoreCase("null")) {
                log.error("未获取到系统日期！");
                map.put("sysDate", "- -");
            } else {
                map.put("sysDate", sysDate);
            }
            return map;
        } catch (Exception e) {
            log.error("获取系统日期出错！", e);
            HashMap<String, String> map = new HashMap<>();
            map.put("sysDate", "- -");
            return map;
        }
    }

    //获取运行时间
    private Map<String, String> getRunTime(SNMPUtils snmpUtils) {
        try {
            HashMap<String, String> map = new HashMap<>();
            // 运行时间
            String sysRunTime = snmpUtils.getPDU(".*******.2.1.25.1.1");
            if (StringUtils.isNotEmpty(sysRunTime) && !sysRunTime.equalsIgnoreCase("noSuchInstance") && !sysRunTime.equalsIgnoreCase("null")) {
                log.error("未获取到运行时间！");
                map.put("runTime", "- -");
            } else {
                map.put("runTime", sysRunTime);
            }
            return map;
        } catch (Exception e) {
            log.error("获取运行时间出错！", e);
            HashMap<String, String> map = new HashMap<>();
            map.put("runTime", "- -");
            return map;
        }
    }


    //获取用户CPU占比
    private Map<String, Double> getUserCpu(SNMPUtils snmpUtils) {
        HashMap<String, Double> resMap = new HashMap<>();
        try {
            String userCpu = snmpUtils.getPDU(".*******.4.1.2021.11.9.0");
            if (StringUtils.isNotEmpty(userCpu) && !userCpu.equalsIgnoreCase("noSuchInstance") && !userCpu.equalsIgnoreCase("null")) {
                resMap.put("userCpu", Double.valueOf(userCpu));
            } else {
                resMap.put("userCpu", 0.0);
            }
            return resMap;
        } catch (Exception e) {
            log.error("获取用户CPU占比出错", e);
            resMap.put("userCpu", 0.0);
            return resMap;
        }
    }

    //获取系统CPU占比
    private Map<String, Double> getSystemCpu(SNMPUtils snmpUtils) {
        HashMap<String, Double> resMap = new HashMap<>();
        try {
            String systemCpu = snmpUtils.getPDU(".*******.4.1.2021.11.10.0");
            if (StringUtils.isNotEmpty(systemCpu) && !systemCpu.equalsIgnoreCase("noSuchInstance") && !systemCpu.equalsIgnoreCase("null")) {
                resMap.put("systemCpu", Double.valueOf(systemCpu));
            } else {
                resMap.put("systemCpu", 0.0);
            }
            return resMap;
        } catch (Exception e) {
            log.error("获取系统CPU占比出错", e);
            resMap.put("systemCpu", 0.0);
            return resMap;
        }
    }

    //获取空闲CPU占比
    private Map<String, Double> getIdleCpu(SNMPUtils snmpUtils) {
        HashMap<String, Double> resMap = new HashMap<>();
        try {
            String idleCpu = snmpUtils.getPDU("*******.4.1.19849.6.2.2.0");
            if (StringUtils.isNotEmpty(idleCpu) && !idleCpu.equalsIgnoreCase("noSunchInstance") && !idleCpu.equalsIgnoreCase("null")) {
                resMap.put("idleCpu", Double.valueOf(idleCpu));
            } else {
                resMap.put("idleCpu", 0.0);
            }
            return resMap;
        } catch (Exception e) {
            log.error("获取空闲CPU占比出错！", e);
            resMap.put("idleCpu", 0.0);
            return resMap;
        }
    }

    //获取cpu使用率
    private Map<String, Double> getCpuRate(Map<String, Object> cpuInfoMap) {
        HashMap<String, Double> resMap = new HashMap<>();
        try {
            Double userCpu = 0.0;
            Double systemCpu = 0.0;
            Double cpuRate = 0.0;
            if (cpuInfoMap.get("userCpu") != null) {
                userCpu = Double.parseDouble(cpuInfoMap.get("userCpu") + "");
            }
            if (cpuInfoMap.get("systemCpu") != null) {
                systemCpu = Double.parseDouble(cpuInfoMap.get("systemCpu") + "");
            }
            cpuRate = userCpu + systemCpu;
            resMap.put("cpuRate", cpuRate);
            return resMap;
        } catch (Exception e) {
            log.error("获取cpu平均使用率出错！", e);
            resMap.put("cpuRate", 0.0);
            return resMap;
        }
    }


    //获取实际内存总量
    private Map<String, Double> getMemTotal(SNMPUtils snmpUtils) {
        HashMap<String, Double> resMap = new HashMap<>();
        try {
            String memTotalReal = snmpUtils.getPDU(".*******.4.1.2021.4.5.0");
            if (StringUtils.isNotEmpty(memTotalReal) && !memTotalReal.equalsIgnoreCase("noSuchInstance") && !memTotalReal.equalsIgnoreCase("null")) {
                memTotalReal = String.format("%.2f", Double.parseDouble(memTotalReal));
                resMap.put("memTotal", Double.valueOf(memTotalReal));
            } else {
                resMap.put("memTotal", 0.0);
            }
        } catch (Exception e) {
            log.error("获取实际内存总量出错！", e);
            resMap.put("memTotal", 0.0);
        }
        return resMap;
    }

    //获取内存已使用量
    private Map<String, Double> getMemUsed(SNMPUtils snmpUtils) {
        HashMap<String, Double> resMap = new HashMap<>();
        try {
            String memUsed = snmpUtils.getPDU(".*******.4.1.2021.4.6.0");
            if (StringUtils.isNotEmpty(memUsed) && !memUsed.equalsIgnoreCase("noSuchInstance") && !memUsed.equalsIgnoreCase("null")) {
                memUsed = String.format("%.2f", Double.parseDouble(memUsed));
                resMap.put("memUsed", Double.valueOf(memUsed));
            } else {
                resMap.put("memUsed", 0.0);
            }
        } catch (Exception e) {
            log.error("获取内存已使用量出错！", e);
            resMap.put("memUsed", 0.0);
        }
        return resMap;

    }

    //获取内存使用率
    private Map<String, Double> getMemRate(JSONObject commonMap) {
        HashMap<String, Double> resMap = new HashMap<>();
        try {
            Double memTotalReal = 0.0;
            Double memUsedReal = 0.0;
            if (commonMap.get("memTotal") != null) {
                memTotalReal = Double.parseDouble(commonMap.get("memTotal") + "");
            }
            if (commonMap.get("memUsed") != null) {
                memUsedReal = Double.parseDouble(commonMap.get("memUsed") + "");
            }
            resMap.put("memRate", memUsedReal / memTotalReal * 100);
        } catch (Exception e) {
            log.error("获取内存使用率出错!", e);
            resMap.put("memRate", 0.0);
        }
        return resMap;
    }

    //获取磁盘使用率
    private Map<String, Double> getDiskRate(SNMPUtils snmpUtils) {
        HashMap<String, Double> resMap = new HashMap<>();
        try {
            String diskRate = snmpUtils.getPDU(".*******.4.1.2021.9.1.9");
            diskRate = String.format("%.2f", Double.parseDouble(diskRate));
            resMap.put("diskRate", Double.valueOf(diskRate));
        } catch (Exception e) {
            log.error("获取磁盘使用率出错！", e);
            resMap.put("diskRate", 0.0);
        }
        return resMap;

    }


    //获取网络吞吐量
    private Map<String, Double> getNetInAndOut(SNMPUtils snmpUtils) {
        try {
            HashMap<String, Double> map = new HashMap<>();
            //KeyMap
            Map<String, String> KeyMap = snmpUtils.getPDUWalk("*******.2.1.2.2.1.1");
            int[] is = new int[KeyMap.size()];
            int i = 0;
            for (Map.Entry<String, String> en : KeyMap.entrySet()) {
                is[i] = Integer.parseInt(en.getValue());
                i++;
            }
            Arrays.sort(is);

            // 吞吐量
            double inAndOut = 0.0;
            int isLength = is.length;
            for (int j = 0; j < isLength; j++) {
                // 输入和输出字节总数
                String inSpeed_String = snmpUtils.getPDU("*******.2.1.2.2.1.10." + is[j]);
                String OutSpeed_String = snmpUtils.getPDU("*******.2.1.2.2.1.16." + is[j]);
                double inAndOutSpeed = 0;

                if (inSpeed_String != null && inSpeed_String != "" && OutSpeed_String != null
                        && OutSpeed_String != "") {
                    inAndOutSpeed = Double.parseDouble(inSpeed_String) + Double.parseDouble(OutSpeed_String);
                }
                inAndOut += inAndOutSpeed;
            }
            if (Double.isNaN(inAndOut)) {
                log.error("未获取到网络吞吐量...");
                map.put("netInOut", 0.0);
            } else {
                inAndOut = inAndOut / 1024 / 1024 / 1024;
                map.put("netInOut", Double.parseDouble(String.format("%.2f", inAndOut)));
            }
            return map;
        } catch (Exception e) {
            log.error("获取网络吞吐量出错" + e.getMessage());
            HashMap<String, Double> map = new HashMap<>();
            map.put("netInOut", 0.0);
            return map;
        }
    }

    //获取端口详情
    private List<Map<String, Object>> getPortInfoList(SNMPUtils snmpUtils) {

        try {
            // 接口索引 主键
            Map<String, String> ifKeyMap = snmpUtils.getPDUWalk("*******.2.1.2.2.1.1");

            int[] is = new int[ifKeyMap.size()];
            int i = 0;
            for (Map.Entry<String, String> en : ifKeyMap.entrySet()) {
                is[i] = Integer.parseInt(en.getValue());
                i++;
            }
            Arrays.sort(is);

            //端口状态
            Map<String, String> portStatusMap = snmpUtils.getPDUWalk("*******.2.1.2.2.1.8");

            //端口描述
            Map<String, String> ifDescrProtosMap = snmpUtils.getPDUWalk("*******.2.1.2.2.1.2");

            // 带宽
            Map<String, String> ifSpeedProtosMap = snmpUtils.getPDUWalk("*******.2.1.2.2.1.5");

            //端口类型
            Map<String, String> portTypeMap = snmpUtils.getPDUWalk("*******.2.1.2.2.1.3");

            //接收数据包个数
            Map<String, String> IfInUcastPktsMap = snmpUtils.getPDUWalk("*******.2.1.2.2.1.11");

            //发送数据包个数
            Map<String, String> IfOutUcastPktsMap = snmpUtils.getPDUWalk("*******.2.1.2.2.1.17");

            //接收错误数据包
            Map<String, String> ifInErrorsMap = snmpUtils.getPDUWalk("*******.2.1.2.2.1.14");

            //发送的错误数据包
            Map<String, String> ifOutErrorsProtosMap = snmpUtils.getPDUWalk("*******.2.1.2.2.1.20");

            List<Map<String, Object>> portInfoList = new ArrayList<Map<String, Object>>();
            for (int j = 0; j < is.length; j++) {
                Map<String, Object> res = new HashMap<String, Object>();

                //端口类型
                res.put("portType", portTypeMap.get("*******.2.1.2.2.1.3." + is[j]));

                //端口状态
                String str = portStatusMap.get("*******.2.1.2.2.1.8." + is[j]);
                if (StringUtils.isEmpty(str)) {
                    res.put("portStatus", "其他");
                } else if ("1".equals(str.trim())) {
                    res.put("portStatus", "连接");
                } else if ("2".equals(str.trim())) {
                    res.put("portStatus", "关闭");
                } else {
                    // 3 == testing 表示当前接口不能转发任何运行状态的报文
                    res.put("portStatus", "其他");
                }

                // 带宽
                String bandWidth_string = ifSpeedProtosMap.get("*******.2.1.2.2.1.5." + is[j]);
                if (StringUtils.isNotEmpty(bandWidth_string)) {
                    Double bandWidth = Double.parseDouble(bandWidth_string);
                    res.put("bandWidth", String.format("%.2f", bandWidth / 8 / 1024 / 1024));
                }

                //输入/输出流量
                String inSpeed = snmpUtils.getPDU("*******.2.1.2.2.1.10." + is[j]);
                if (org.apache.commons.lang.StringUtils.isNotEmpty(inSpeed)) {
                    Double inputFlow = Double.parseDouble(inSpeed);
                    res.put("inputFlow", String.format("%.2f", inputFlow));
                }
                String outSpeed = snmpUtils.getPDU("*******.2.1.2.2.1.16." + is[j]);
                if (org.apache.commons.lang.StringUtils.isNotEmpty(outSpeed)) {
                    Double outputFlow = Double.parseDouble(outSpeed);
                    res.put("outputFlow", String.format("%.2f", outputFlow));
                }

                //输入/输出数据包数
                String IfInUcastPkts = IfInUcastPktsMap.get("*******.2.1.2.2.1.11." + is[j]);
                if (org.apache.commons.lang.StringUtils.isNotEmpty(IfInUcastPkts)) {
                    Double d = Double.parseDouble(IfInUcastPkts);
                    res.put("IfInUcastPkts", String.format("%.2f", d));
                }
                String IfOutUcastPkts = IfOutUcastPktsMap.get("*******.2.1.2.2.1.27." + is[j]);
                if (org.apache.commons.lang.StringUtils.isNotEmpty(IfOutUcastPkts)) {
                    Double d = Double.parseDouble(IfOutUcastPkts);
                    res.put("IfOutUcastPkts", String.format("%.2f", d));
                }

                //输入/输出错误包数
                String inErrorPackageString = ifInErrorsMap.get("*******.2.1.2.2.1.14." + is[j]);
                if (org.apache.commons.lang.StringUtils.isNotEmpty(inErrorPackageString)) {
                    Double inError = Double.parseDouble(inErrorPackageString);
                    res.put("inErrorPackage", String.format("%.2f", inError));
                }
                String outErrorPackageString = ifOutErrorsProtosMap.get("*******.2.1.2.2.1.20." + is[j]);
                if (org.apache.commons.lang.StringUtils.isNotEmpty(outErrorPackageString)) {
                    Double inError = Double.parseDouble(outErrorPackageString);
                    res.put("outErrorPackage", String.format("%.2f", inError));
                }

                //输入/输出丢失错误包数
                String inLossPackageString = snmpUtils.getPDU("*******.2.1.2.2.1.13." + is[j]);
                if (org.apache.commons.lang.StringUtils.isNotEmpty(inLossPackageString)) {
                    Double inLossPackage = Double.parseDouble(inLossPackageString);
                    res.put("inLossPackage", String.format("%.2f", inLossPackage));
                }
                String outLossPackageString = snmpUtils.getPDU("*******.2.1.2.2.1.19." + is[j]);
                if (org.apache.commons.lang.StringUtils.isNotEmpty(outLossPackageString)) {
                    Double outLossPackage = Double.parseDouble(outLossPackageString);
                    res.put("outLossPackage", String.format("%.2f", outLossPackage));
                }

                //输入单播报文的个数
                String inUcastPktsStr = snmpUtils.getPDU("*******.2.1.2.2.1.11." + is[j]);
                if (StringUtils.isNotEmpty(inUcastPktsStr) && !inUcastPktsStr.equals("noSuchInstance")) {
                    Double inUcastPkts = Double.parseDouble(inUcastPktsStr);
                    res.put("inUcastPkts", String.format("%.2f", inUcastPkts));
                }

                //输入非单播报文的个数
                String inNUcastPktsStr = snmpUtils.getPDU("*******.2.1.2.2.1.12." + is[j]);
                if (StringUtils.isNotEmpty(inNUcastPktsStr) && !inNUcastPktsStr.equals("noSuchInstance")) {
                    Double inNUcastPkts = Double.parseDouble(inNUcastPktsStr);
                    res.put("inNUcastPkts", String.format("%.2f", inNUcastPkts));
                }

                //输出单播报文的个数
                String outUcastPktsStr = snmpUtils.getPDU("*******.2.1.2.2.1.17." + is[j]);
                if (StringUtils.isNotEmpty(outUcastPktsStr) && !outUcastPktsStr.equals("noSuchInstance")) {
                    Double outUcastPkts = Double.parseDouble(outUcastPktsStr);
                    res.put("outUcastPkts", String.format("%.2f", outUcastPkts));
                }

                //输出非单播报文的个数
                String outNUcastPktsStr = snmpUtils.getPDU("*******.2.1.2.2.1.18." + is[j]);
                if (StringUtils.isNotEmpty(outNUcastPktsStr) && !outNUcastPktsStr.equals("noSuchInstance")) {
                    Double outNUcastPkts = Double.parseDouble(outNUcastPktsStr);
                    res.put("outNUcastPkts", String.format("%.2f", outNUcastPkts));
                }

                // 接口描述
                res.put("portDesc", ifDescrProtosMap.get("*******.2.1.2.2.1.2." + is[j]));

                //索引
                res.put("index", is[j]);

                portInfoList.add(res);
            }
            return portInfoList;
        } catch (Exception e) {
            log.error("获取端口信息出错！", e);
            return null;
        }
    }


    //获取过去1分钟的系统平均负载
    private Map<String, String> getLastOne(SNMPUtils snmpUtils) {
        HashMap<String, String> map = new HashMap<>();
        try {
            String lastOne = snmpUtils.getPDU(".*******.4.1.2021.10.1.3.1");
            if (StringUtils.isNotEmpty(lastOne) && !lastOne.equalsIgnoreCase("noSuchInstance")) {
                log.error("未获取到过去1分钟的系统平均负载！");
                lastOne = "- -";
            }
            map.put("lastOne", lastOne);
            return map;
        } catch (Exception e) {
            log.error("获取过去1分钟的系统平均负载出错！", e);
            map.put("lastOne", "- -");
            return map;
        }
    }

    //获取过去5分钟的系统平均负载
    private Map<String, String> getLastFive(SNMPUtils snmpUtils) {
        HashMap<String, String> map = new HashMap<>();
        try {
            String lastFive = snmpUtils.getPDU(".*******.4.1.2021.10.1.3.2");
            if (StringUtils.isNotEmpty(lastFive) && !lastFive.equalsIgnoreCase("noSuchInstance")) {
                log.error("未获取到过去5分钟的系统平均负载！");
                lastFive = "- -";
            }
            map.put("lastFive", lastFive);
            return map;
        } catch (Exception e) {
            log.error("获取过去5分钟的系统平均负载出错！", e);
            map.put("lastFive", "- -");
            return map;
        }
    }

    //获取过去15分钟的系统平均负载
    private Map<String, String> getLastFifteen(SNMPUtils snmpUtils) {
        HashMap<String, String> map = new HashMap<>();
        try {
            String lastFifteen = snmpUtils.getPDU(".*******.4.1.2021.10.1.3.2");
            if (StringUtils.isNotEmpty(lastFifteen) && !lastFifteen.equalsIgnoreCase("noSuchInstance")) {
                log.error("未获取到过去15分钟的系统平均负载！");
                lastFifteen = "- -";
            }
            map.put("lastFifteen", lastFifteen);
            return map;
        } catch (Exception e) {
            log.error("获取过去15分钟的系统平均负载出错！", e);
            map.put("lastFifteen", "- -");
            return map;
        }
    }


    //获取TCP连接状态
    private Map<String, String> getTcpConnState(SNMPUtils snmpUtils) {
        HashMap<String, String> map = new HashMap<>();
        try {
            String tcpConnState = snmpUtils.getPDU(".*******.********.1.1");
            if (StringUtils.isNotEmpty(tcpConnState) && !tcpConnState.equalsIgnoreCase("noSuchInstance")) {
                log.error("未获取到TCP连接状态！");
                tcpConnState = "- -";
            }
            map.put("tcpConnState", tcpConnState);
            return map;
        } catch (Exception e) {
            log.error("获取TCP连接状态出错！", e);
            map.put("tcpConnState", "- -");
            return map;
        }
    }

    //获取TCP本地连接地址
    private Map<String, String> getTcpConnLocalAddress(SNMPUtils snmpUtils) {
        HashMap<String, String> map = new HashMap<>();
        try {
            String tcpConnLocalAddress = snmpUtils.getPDU(".*******.********.1.2");
            if (StringUtils.isNotEmpty(tcpConnLocalAddress) && !tcpConnLocalAddress.equalsIgnoreCase("noSuchInstance")) {
                log.error("未获取到TCP本地连接地址！");
                tcpConnLocalAddress = "- -";
            }
            map.put("tcpConnLocalAddress", tcpConnLocalAddress);
            return map;
        } catch (Exception e) {
            log.error("获取TCP本地连接地址出错！", e);
            map.put("tcpConnLocalAddress", "- -");
            return map;
        }
    }

    //获取TCP本地端口
    private Map<String, String> getTcpConnLocalPort(SNMPUtils snmpUtils) {
        HashMap<String, String> map = new HashMap<>();
        try {
            String tcpConnLocalPort = snmpUtils.getPDU(".*******.********.1.3");
            if (StringUtils.isNotEmpty(tcpConnLocalPort) && !tcpConnLocalPort.equalsIgnoreCase("noSuchInstance")) {
                log.error("未获取到TCP本地端口！");
                tcpConnLocalPort = "- -";
            }
            map.put("tcpConnLocalPort", tcpConnLocalPort);
            return map;
        } catch (Exception e) {
            log.error("获取TCP本地端口出错！", e);
            map.put("tcpConnLocalPort", "- -");
            return map;
        }
    }

    //获取TCP远程地址
    private Map<String, String> getTcpConnRemAddress(SNMPUtils snmpUtils) {
        HashMap<String, String> map = new HashMap<>();
        try {
            String tcpConnRemAddress = snmpUtils.getPDU(".*******.********.1.4");
            if (StringUtils.isNotEmpty(tcpConnRemAddress) && !tcpConnRemAddress.equalsIgnoreCase("noSuchInstance")) {
                log.error("未获取到TCP远程地址！");
                tcpConnRemAddress = "- -";
            }
            map.put("tcpConnRemAddress", tcpConnRemAddress);
            return map;
        } catch (Exception e) {
            log.error("获取TCP远程地址出错！", e);
            map.put("tcpConnRemAddress", "- -");
            return map;
        }
    }

    //获取TCP远程端口
    private Map<String, String> getTcpConnRemPort(SNMPUtils snmpUtils) {
        HashMap<String, String> map = new HashMap<>();
        try {
            String tcpConnRemPort = snmpUtils.getPDU(".*******.********.1.5");
            if (StringUtils.isNotEmpty(tcpConnRemPort) && !tcpConnRemPort.equalsIgnoreCase("noSuchInstance")) {
                log.error("未获取到TCP远程端口！");
                tcpConnRemPort = "- -";
            }
            map.put("tcpConnRemPort", tcpConnRemPort);
            return map;
        } catch (Exception e) {
            log.error("获取TCP远程端口出错！", e);
            map.put("tcpConnRemPort", "- -");
            return map;
        }
    }

    //获取UDP本地连接地址
    private Map<String, String> getUdpLocalAddress(SNMPUtils snmpUtils) {
        HashMap<String, String> map = new HashMap<>();
        try {
            String udpLocalAddress = snmpUtils.getPDU(".*******.*******.1.1");
            if (StringUtils.isNotEmpty(udpLocalAddress) && !udpLocalAddress.equalsIgnoreCase("noSuchInstance")) {
                log.error("未获取到UDP本地连接地址！");
                udpLocalAddress = "- -";
            }
            map.put("udpLocalAddress", udpLocalAddress);
            return map;
        } catch (Exception e) {
            log.error("获取UDP本地连接地址出错！", e);
            map.put("udpLocalAddress", "- -");
            return map;
        }
    }

    //获取UDP本地端口
    private Map<String, String> getUdpLocalPort(SNMPUtils snmpUtils) {
        HashMap<String, String> map = new HashMap<>();
        try {
            String udpLocalPort = snmpUtils.getPDU(".*******.*******.1.2");
            if (StringUtils.isNotEmpty(udpLocalPort) && !udpLocalPort.equalsIgnoreCase("noSuchInstance")) {
                log.error("未获取到UDP本地端口！");
                udpLocalPort = "- -";
            }
            map.put("udpLocalPort", udpLocalPort);
            return map;
        } catch (Exception e) {
            log.error("获取UDP本地端口出错！", e);
            map.put("udpLocalPort", "- -");
            return map;
        }
    }

}
