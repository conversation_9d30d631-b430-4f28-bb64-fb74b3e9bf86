package com.yuanqiao.insight.collection.collector.utils;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.matadata.function.Calculator;
import com.yuanqiao.insight.acore.utils.SpringContextUtil;
import com.yuanqiao.insight.common.util.snmp.SNMPUtils;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;

import java.text.SimpleDateFormat;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/29
 */
@Slf4j
public class MetadataAssembleUtil {

    /**
     * 根据物模型的数据类型，区分数据组装
     *
     * @param metadata  物模型
     * @param stcaKey   状态容器key
     * @param commonMap 公共结果集
     * @return
     */
    public static JSONObject switchByDataType(ProertyMetadata metadata, String stcaKey, JSONObject commonMap) {
        JSONObject jsonObject;
        switch (metadata.getDataType()) {
            case "object":
                jsonObject = getObjectCodec(metadata, stcaKey, commonMap);
                break;
            case "array":
                jsonObject = getArrayCodec(metadata, stcaKey, commonMap);
                break;
            default:
                jsonObject = getOtherCodec(metadata, stcaKey, commonMap);
        }
        return jsonObject;
    }


    public static JSONObject getOtherCodec(ProertyMetadata item, String stcaKey, JSONObject commonMap) {
        JSONObject jsonObject = new JSONObject();
        JSONObject basicJsonObject = new JSONObject();

        basicJsonObject.put("code", item.getCode());
        basicJsonObject.put("name", item.getName());
        basicJsonObject.put("type", item.getDataType());
        basicJsonObject.put("unit", item.getUnit());
        if (commonMap.get(item.getCode()) != null) {
            basicJsonObject.put("value", commonMap.get(item.getCode()));
        } else {
            if (StringUtils.isNotEmpty(item.getDataType())) {
                switch (item.getDataType()) {
                    case "int":
                        basicJsonObject.put("value", 0);
                        break;
                    case "double":
                        basicJsonObject.put("value", 0.0);
                        break;
                    default:
                        basicJsonObject.put("value", "null");
                }
            } else {
                basicJsonObject.put("value", "null");
            }
        }

        if (StringUtils.isNotEmpty(item.getFuncName())) {
            Calculator calculator = (Calculator) SpringContextUtil.getBean(item.getFuncName());
            String[] funcParams = getPara(item.getFuncParam());
            Object execute = calculator.execute(commonMap, funcParams);
            basicJsonObject.put("value", execute);
        }

        String chart = item.getChart() + "";
        basicJsonObject.put("display", makeLineDisplay(stcaKey, item, basicJsonObject.get("value"), chart));

        jsonObject.put(stcaKey + "_" + item.getCode(), basicJsonObject);
        return jsonObject;
    }

    public static JSONObject getArrayCodec(ProertyMetadata item, String stcaKey, JSONObject commonMap) {
        JSONObject jsonObject = new JSONObject();
        JSONObject jsonObject2 = new JSONObject();
        JSONArray jsonArray = new JSONArray();

        jsonObject2.put("code", item.getCode());
        jsonObject2.put("name", item.getName());
        jsonObject2.put("type", item.getDataType());
        List<Map<String, Object>> procInfoMapList = (List<Map<String, Object>>) commonMap.get(item.getCode());
        List<ProertyMetadata> childMetadataList = item.getProertyMetadataList();
        if (CollUtil.isEmpty(procInfoMapList) || CollUtil.isEmpty(childMetadataList)) {
            jsonObject2.put("value", jsonArray);
        } else {
            for (Map<String, Object> map : procInfoMapList) {
                // 当前元素对象的结果
                JSONObject everyInfoObject = new JSONObject();
                // 封装无法直接读取到数据的指标
                List<ProertyMetadata> noDataMetadataList = childMetadataList.stream().filter(ele -> map.entrySet().stream().noneMatch(entry -> StringUtils.equals(entry.getKey(), ele.getCode()))).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(noDataMetadataList)) {
                    noDataMetadataList.forEach(nodataItem -> {
                        JSONObject dataObject = new JSONObject();
                        dataObject.put("name", nodataItem.getName());
                        dataObject.put("type", nodataItem.getDataType());
                        dataObject.put("value", "--");
                        dataObject.put("unit", nodataItem.getUnit());
                        dataObject.put("code", nodataItem.getCode());
                        dataObject.put("serial", nodataItem.getSerial());
                        dataObject.put("createTime", nodataItem.getCreateTime());
                        dataObject.put("originStep", nodataItem.getOriginStep());
                        everyInfoObject.put(nodataItem.getCode(), dataObject);
                    });
                }
                // 封装可以直接读取到数据的指标
                List<ProertyMetadata> dataMetadataList = childMetadataList.stream().filter(ele -> map.entrySet().stream().anyMatch(entry -> StringUtils.equals(entry.getKey(), ele.getCode()))).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(dataMetadataList)) {
                    for (Map.Entry<String, Object> entry : map.entrySet()) {
                        for (ProertyMetadata dataItem : dataMetadataList) {
                            if (entry.getKey().equals(dataItem.getCode())) {
                                JSONObject dataObject = new JSONObject();
                                dataObject.put("name", dataItem.getName());
                                dataObject.put("type", dataItem.getDataType());
                                dataObject.put("value", entry.getValue());
                                dataObject.put("unit", dataItem.getUnit());
                                dataObject.put("code", dataItem.getCode());
                                dataObject.put("serial", dataItem.getSerial());
                                dataObject.put("createTime", dataItem.getCreateTime());
                                dataObject.put("originStep", dataItem.getOriginStep());
                                everyInfoObject.put(dataItem.getCode(), dataObject);
                            }
                        }
                    }
                }
                // 封装当前元素对象
                jsonArray.add(everyInfoObject);
            }
            jsonObject2.put("value", jsonArray);

            // 为配置表达式的指标，重新赋值
            JSONArray jsonArray1 = (JSONArray) jsonObject2.get("value");
            for (ProertyMetadata childMetadata : childMetadataList) {
                if (StringUtils.isNotEmpty(childMetadata.getFuncName())) {
                    Calculator calculator = (Calculator) SpringContextUtil.getBean(childMetadata.getFuncName());
                    String[] funcParams = getPara(childMetadata.getFuncParam());
                    for (int i = 0; i < jsonArray1.size(); i++) {
                        if (jsonArray1.getJSONObject(i).get(childMetadata.getCode()) != null) {
                            Object execute = calculator.execute(jsonArray1.getJSONObject(i), funcParams);
                            jsonArray1.getJSONObject(i).getJSONObject(childMetadata.getCode()).put("value", execute);
                        }
                    }
                }
            }
            jsonObject2.put("value", jsonArray1);

            //组装页面展示数据格式
            jsonObject2.put("display", makeTableDisplay(jsonArray1, item));
        }
        jsonObject.put(stcaKey + "_" + item.getCode(), jsonObject2);
        return jsonObject;
    }

    public static JSONObject getObjectCodec(ProertyMetadata item, String stcaKey, JSONObject commonMap) {
        JSONObject jsonObject = new JSONObject();
        JSONObject jsonObject2 = new JSONObject();
        JSONArray valueArray = new JSONArray();
        JSONObject jsonObject3 = new JSONObject();

        jsonObject2.put("code", item.getCode());
        jsonObject2.put("name", item.getName());
        jsonObject2.put("type", item.getDataType());
        Map<String, String> infoMap = (Map<String, String>) commonMap.get(item.getCode());
        List<ProertyMetadata> childMetadataList = item.getProertyMetadataList();
        if (CollUtil.isEmpty(infoMap) || CollUtil.isEmpty(childMetadataList)) {
            jsonObject2.put("value", jsonObject3);
        } else {
            for (Map.Entry<String, String> entry : infoMap.entrySet()) {
                for (ProertyMetadata childMetadata : childMetadataList) {
                    if (entry.getKey().equals(childMetadata.getCode())) {
                        JSONObject dataObject = new JSONObject();
                        dataObject.put("name", childMetadata.getName());
                        dataObject.put("code", childMetadata.getCode());
                        dataObject.put("type", childMetadata.getDataType());
                        dataObject.put("value", entry.getValue());
                        dataObject.put("unit", childMetadata.getUnit());
                        dataObject.put("serial", childMetadata.getSerial());
                        dataObject.put("createTime", childMetadata.getCreateTime());
                        dataObject.put("originStep", childMetadata.getOriginStep());
                        jsonObject3.put(entry.getKey(), dataObject);
                    }
                }
            }

            for (ProertyMetadata obj : childMetadataList) {
                if (StringUtils.isNotEmpty(obj.getFuncName())) {
                    Calculator calculator = (Calculator) SpringContextUtil.getBean(obj.getFuncName());
                    String[] funcParams = getPara(obj.getFuncParam());
                    JSONObject env = new JSONObject();
                    for (Map.Entry entry : jsonObject3.entrySet()) {
                        env.put(String.valueOf(entry.getKey()), JSONObject.parseObject(entry.getValue() + "").get("value"));
                    }
                    Object execute = calculator.execute(env, funcParams);
                    JSONObject jsonObjectTemp = jsonObject3.getJSONObject(obj.getCode());
                    jsonObjectTemp.put("value", execute);
                    jsonObject3.put(obj.getCode(), jsonObjectTemp);
                }
            }

            valueArray.add(jsonObject3);
            jsonObject2.put("value", valueArray);
            jsonObject2.put("display", makeTableDisplay(valueArray, item));
        }
        jsonObject.put(stcaKey + "_" + item.getCode(), jsonObject2);
        return jsonObject;
    }

// **-----------------------------------------------------------------------------------------------------------------**

    //获取物模型子属性参数转为数组格式
    public static String[] getPara(String para) {
        String[] split = para.split(",");
        return split;
    }

    //物模型二级子属性oid
    //判断物模型的子属性在拓展表中是否有oid，如果有就优先根据oid读取数据，如果没有就在公共map中读取数据
    private static JSONObject getValue(ProertyMetadata metadata, JSONObject everyInfoObject, SNMPUtils snmpUtils) {
        try {
            JSONObject dataObject = new JSONObject();
            String pdu = snmpUtils.getPDU(metadata.getOid());
            dataObject.put("name", metadata.getName());
            dataObject.put("type", metadata.getDataType());
            dataObject.put("value", pdu);
            everyInfoObject.put(metadata.getCode(), dataObject);
        } catch (Exception e) {
            log.error("获取" + metadata.getCode() + "出错！", e);
        }
        return everyInfoObject;
    }

// **-----------------------------------------------------------------------------------------------------------------**

    //basic组装line格式的display
    public static JSONArray makeLineDisplay(String stcaKey, ProertyMetadata metadata, Object value, String chart) {
        RedisTemplate redisTemplate = (RedisTemplate) SpringContextUtil.getBean("redisTemplate");
        JSONObject dataObject = JSONObject.parseObject(String.valueOf(redisTemplate.opsForValue().get(stcaKey + "_" + metadata.getCode())));

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (dataObject != null && !dataObject.isEmpty() && chart.equals("line")) {
            return setLineDisplay(value, dataObject, metadata);
        } else {
            JSONArray jsonArray = new JSONArray();
            JSONObject map1 = new JSONObject();
            map1.put("timestamp", sdf.format(System.currentTimeMillis()));
            map1.put("value", value);
            map1.put("code", metadata.getCode());
            map1.put("unit", metadata.getUnit() != null ? metadata.getUnit() : "");
            map1.put("type", metadata.getChart() + "");
            map1.put("originStep", metadata.getOriginStep());
            jsonArray.add(map1);
            return jsonArray;
        }

    }

    public static JSONArray setLineDisplay(Object value, JSONObject jsonObject, ProertyMetadata metadata) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            JSONArray display = jsonObject.getJSONArray("display");
            JSONObject map1 = new JSONObject();
            if (display.size() >= 15) {
                display.remove(0);
                map1.put("timestamp", sdf.format(System.currentTimeMillis()));
                map1.put("value", value);
                map1.put("code", metadata.getCode());
                map1.put("unit", metadata.getUnit() != null ? metadata.getUnit() : "");
                map1.put("type", metadata.getChart() + "");
                map1.put("originStep", metadata.getOriginStep());
                display.add(map1);
            } else {
                map1.put("timestamp", sdf.format(System.currentTimeMillis()));
                map1.put("value", value);
                map1.put("code", metadata.getCode());
                map1.put("unit", metadata.getUnit() != null ? metadata.getUnit() : "");
                map1.put("type", metadata.getChart() + "");
                map1.put("originStep", metadata.getOriginStep());
                display.add(map1);
            }
            return display;
        } catch (Exception e) {
            JSONArray jsonArray = new JSONArray();
            JSONObject map1 = new JSONObject();
            map1.put("timestamp", sdf.format(System.currentTimeMillis()));
            map1.put("value", value);
            map1.put("code", metadata.getCode());
            map1.put("unit", metadata.getUnit() != null ? metadata.getUnit() : "");
            map1.put("type", metadata.getChart() + "");
            map1.put("originStep", metadata.getOriginStep());
            jsonArray.add(map1);
            return jsonArray;
        }

    }


    //array、object组装table格式的display
    public static JSONArray makeTableDisplay(JSONArray list, ProertyMetadata metadata) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        JSONArray jsonArrayOut = new JSONArray();
        JSONObject disObject = new JSONObject();
        disObject.put("type", metadata.getChart() + "");
        disObject.put("timestamp", sdf.format(System.currentTimeMillis()));
        JSONArray jsonArray = new JSONArray();
        for (int i = 0; i < list.size(); i++) {
            try {
                Map<String, Object> o = (Map<String, Object>) list.get(i);
                Collection<Object> values = o.values();
                String s = JSONArray.toJSONString(values);  //复制一份出来
                JSONArray array = JSONArray.parseArray(s);
                jsonArray.add(array);
            } catch (Exception e) {
                Double d = (Double) list.get(i);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("name", "cpu索引");
                jsonObject.put("value", i);
                jsonObject.put("unit", "");
                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put("name", "cpu使用率");
                jsonObject1.put("value", d);
                jsonObject1.put("unit", "%");
                JSONArray jsonArray1 = new JSONArray();
                jsonArray1.add(jsonObject);
                jsonArray1.add(jsonObject1);
                jsonArray.add(jsonArray1);
            }
        }
        disObject.put("value", jsonArray);
        jsonArrayOut.add(disObject);
        return jsonArrayOut;
    }
}
