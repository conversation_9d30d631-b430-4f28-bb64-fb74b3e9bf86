package com.yuanqiao.insight.collection.collector.collector.impl.MiddleWareMbsc;

import com.yuanqiao.insight.acore.collector.Collector;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.acore.scheduler.SchedulerManagerInter;
import com.yuanqiao.insight.acore.utils.SpringContextUtil;
import com.yuanqiao.insight.collection.collector.utils.MiddleWareCodecUtils;
import com.yuanqiao.insight.utils.SNMPMetadataUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import redis.clients.jedis.Jedis;

@Slf4j
@EnableAsync
public class RedisCollector implements Collector {
    private Device device;
    private SchedulerManagerInter schedulerManager;
    private RedisCodec redisCodeC;
    private SNMPMetadataUtils metadataUtils;
    private RedisTemplate redisTemplate;
    private MiddleWareCodecUtils middleWareCodecUtils;


    //初始化
    public void init(Device device, SchedulerManagerInter schedulerManager) {
        this.device = device;
        this.redisCodeC = new RedisCodec();
        this.schedulerManager = schedulerManager;
        this.metadataUtils = (SNMPMetadataUtils) SpringContextUtil.getBean("SNMPMetadataUtils");
        this.redisTemplate = (RedisTemplate) SpringContextUtil.getBean("redisTemplate");
        this.middleWareCodecUtils = (MiddleWareCodecUtils) SpringContextUtil.getBean("middleWareCodecUtils");

    }

    //执行方法
    @Async
    public void execute() {
        Jedis jedis = null;
        try {
            jedis = this.connect();
            if (jedis != null) {
                middleWareCodecUtils.collectorMainLine(jedis, "redis", metadataUtils, device, schedulerManager, redisTemplate, redisCodeC);
            }
        } catch (Exception e) {
            log.error("设备 " + device.getKey() + " 执行异常！", e);
        } finally {
            // 确保jedis连接被关闭
            if (jedis != null) {
                jedis.close();
            }
        }
    }

    private Jedis connect() {
        String ip = device.getConnectParam().get("ip");
        int port = Integer.parseInt(device.getConnectParam().get("port"));
        String password = device.getConnectParam().get("password");
        String type = device.getConnectParam().get("type");
        Jedis jedis = null;
        try {
            log.info("正在连接设备: {}:{}", ip, port);
            jedis = new Jedis(ip, port);
            if (StringUtils.isNotEmpty(type) && type.equals("1")) {
                jedis.auth(password);
            }
            log.info("设备 {} 连接成功", device.getKey());
            return jedis;
        } catch (Exception e) {
            if (jedis != null) {
                jedis.close();
            }
            log.error("设备 " + device.getKey() + " 连接异常！", e);
            return null;
        }
    }

    public static void main(String[] args) {
        Jedis jedis = new Jedis("*************", 6379);
        System.out.println(jedis.info());
    }
}
