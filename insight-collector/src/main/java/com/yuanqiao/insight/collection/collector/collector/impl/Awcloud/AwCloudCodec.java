package com.yuanqiao.insight.collection.collector.collector.impl.Awcloud;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.device.Device;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 云平台数据解析组装
 */
@Slf4j
public class AwCloudCodec {

    public JSONObject dataCodecObject(JSONObject jsonResult, Device device) {
        JSONObject commonMap = new JSONObject();
        JSONObject data = jsonData(jsonResult);
        String token = (String) data.get("authToken");
        String regionKey = (String) data.get("regionKey");
        String enterpriseUid = (String) data.get("enterpriseUid");
        if (token.isEmpty() && regionKey.isEmpty() && enterpriseUid.isEmpty()) {
            log.error("天熠云平台" + device.getKey() + "登录接口必选参数为空" + token + "," + regionKey + "," + enterpriseUid);
            return commonMap;
        } else {
            Map<String, Double> map = getDeviceOpenInfo(token, regionKey, enterpriseUid, device);
            commonMap.put("cpuRate", map.get("cpuRate"));
            commonMap.put("memRate", map.get("memRate"));
            commonMap.put("diskRate", map.get("diskRate"));
            commonMap.put("memTotal", map.get("memTotal"));
            commonMap.put("memUsed", map.get("memUsed"));
            commonMap.put("vpsInfo", getVpsInfoList(token, regionKey, enterpriseUid, device));
        }
        return commonMap;
    }

    private JSONObject jsonData(JSONObject jsonResult) {
        JSONObject jsonObject = (JSONObject) jsonResult.get("data");
        JSONObject jsonData = (JSONObject) jsonObject.get("data");
        return jsonData;
    }

    private JSONArray jsonArray(JSONObject jsonResult) {
        JSONObject jsonObject = (JSONObject) jsonResult.get("data");
        JSONArray jsonArray = (JSONArray) jsonObject.get("data");
        return jsonArray;
    }

    /**
     * 数据中心概况（文档1000页）
     * cpu，内存，disk
     *
     * @param device
     * @return
     */
    private HashMap<String, Double> getDeviceOpenInfo(String token, String regionKey, String enterpriseUid, Device device) {
        HashMap<String, Double> map = new HashMap<>();
        String url = "https://" + device.getConnectParam().get("ip") + ":" + device.getConnectParam().get("port") + "/awstack-resource/v1/os-hypervisors/statics";
        log.info("天熠虚拟化云平台数据中心概况URL：" + url);
        String result = HttpRequest.get(url)
                .header("Content-Type", "application/json; charset=UTF-8")
                .header("X-Auth-Token", token)
                .header("X-Register-Code", regionKey)
                .header("X-enterprise_uid-Code", enterpriseUid)
                .execute().body();

        log.error("当前云平台设备 【" + device.getKey() + "】数据中心概况接口返回值为：" + result);
        JSONObject jsonResult = JSONObject.parseObject(result);
        log.info("当前设备 【" + device.getKey() + "】数据中心概况接口code值" + jsonResult.get("code"));
        double memTotal = 0.0;
        double memUsed = 0.0;
        double mem = 0.0;
        double cpu = 0.0;
        double disk = 0.0;
        if (jsonResult != null && jsonResult.get("code").equals("0")) {
            JSONObject data = jsonData(jsonResult);
            //cpu核数
            String cpus = String.valueOf(data.get("vcpus"));
            String cpus_used = String.valueOf(data.get("vcpus_used"));
//            String cpus_lb = String.valueOf(data.get("vcpus_lb"));
            //内存
            String memory_mb = String.valueOf(data.get("memory_mb"));
            String memory_mb_used = String.valueOf(data.get("memory_mb_used"));
            String reserved_host_memory_mb = String.valueOf(data.get("reserved_host_memory_mb"));
            //磁盘
            String local_gb = String.valueOf(data.get("local_gb"));
            String local_gb_used = String.valueOf(data.get("local_gb_used"));
            memTotal = Double.parseDouble(memory_mb) - Double.parseDouble(reserved_host_memory_mb);
            memUsed = Double.parseDouble(memory_mb_used) - Double.parseDouble(reserved_host_memory_mb);
            mem = 100 * memUsed / memTotal;
            cpu = 100 * Double.parseDouble(cpus_used)/ Double.parseDouble(cpus);
            disk = 100 * (Double.parseDouble(local_gb_used)) / Double.parseDouble(local_gb);
            map.put("cpuRate", Double.valueOf(String.format("%.2f", cpu)));
            map.put("memRate", Double.valueOf(String.format("%.2f", mem)));
            map.put("diskRate", Double.valueOf(String.format("%.2f", disk)));
            map.put("memTotal", Double.valueOf(String.format("%.2f", memTotal / 1024)));
            map.put("memUsed", Double.valueOf(String.format("%.2f", memUsed / 1024)));
        } else {
            log.error("当前云平台设备 【" +device.getKey() + "】数据中心概况接口返回值为：" + jsonResult);
            map.put("cpuRate", cpu);
            map.put("memRate", mem);
            map.put("diskRate", disk);
            map.put("memTotal", memTotal);
            map.put("memUsed", memUsed);
        }
        return map;
    }

    /**
     * 云主机列表
     *
     * @param device
     * @return
     */
    private List<Map<String, Object>> getVpsInfoList(String token, String regionKey, String enterpriseUid, Device device) {
        List<Map<String, Object>> vpsInfoList = new ArrayList<Map<String, Object>>();
        String url = "https://" + device.getConnectParam().get("ip") + ":" + device.getConnectParam().get("port") + "/awstack-manage/v2/instance?pageNum=1&pageSize=" + Integer.MAX_VALUE;
        log.info("天熠虚拟化云平台云主机列表URL：" + url);
        String result = HttpRequest.get(url)
                .header("Content-Type", "application/json; charset=UTF-8")
                .header("X-Auth-Token", token)
                .header("X-Register-Code", regionKey)
                .header("X-enterprise_uid-Code", enterpriseUid)
                .execute().body();

        log.error("当前云平台设备 【" + device.getKey() + "】云平台云主机列表返回值为：" + result);
        JSONObject jsonResult = JSONObject.parseObject(result);
        log.info("当前设备 【" + device.getKey() + "】云平台云主机列表接口code值" + jsonResult.get("code"));
        if (jsonResult != null && jsonResult.get("code").equals("0")) {
            JSONArray jsonArray = jsonArray(jsonResult);
            for (int i = 0; i < jsonArray.size(); i++) {
                Map<String, Object> vps = new HashMap<String, Object>();
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                //UID
                String uId = String.valueOf(jsonObject.get("uid"));
                vps.put("uId", uId);
                //名字
                String awname = String.valueOf(jsonObject.get("name"));
                vps.put("awname", awname);
//                //描述
                String description = String.valueOf(jsonObject.get("description"));
                vps.put("description", description.equalsIgnoreCase("null") ? "" : description);
                //数据中心名称
                String regionKey1 = String.valueOf(jsonObject.get("regionKey"));
                vps.put("regionKey", regionKey1);
                //项目名称
                String projectName = String.valueOf(jsonObject.get("projectName"));
                vps.put("projectName", projectName);
                //部门名称
                String domainName = String.valueOf(jsonObject.get("domainName"));
                vps.put("domainName", domainName);
                String ip = "";
                JSONArray ipArray = (JSONArray) jsonObject.get("fixedIps");
                if (!ipArray.isEmpty()) {
                    ip = ipArray.getString(0);
                } else {
                    JSONArray ipsArray = (JSONArray) jsonObject.get("floatingIps");
                    if (!ipsArray.isEmpty()) {
                        ip = ipsArray.getString(0);
                    }
                }
                vps.put("ip", ip);
                //状态
                String status = String.valueOf(jsonObject.get("status"));
                if (status.equalsIgnoreCase("ACTIVE")) {
                    status = "运行";
                } else if (status.equalsIgnoreCase("SHUTOFF")) {
                    status = "关机";
                } else {
                    status = "其它";
                }
                vps.put("status", status);

//                String cpus = String.valueOf(jsonObject.get("vcpus"));
                String curCpus = String.valueOf(jsonObject.get("curCpus"));
                double cpuRate = 0.0;
                double memRate = 0.0;
//                if (Double.parseDouble(cpus) > 0) {
//                    cpuRate = 100 * Double.parseDouble(curCpus) / Double.parseDouble(cpus);
//                }
                vps.put("cpuRateOs", Double.valueOf(curCpus));
//                String ram = String.valueOf(jsonObject.get("ram"));
                String curRam = String.valueOf(jsonObject.get("curRam"));
//                if (Double.parseDouble(ram) > 0) {
//                    memRate = 100 * Double.parseDouble(curRam) / Double.parseDouble(ram);
//                }
                vps.put("memRateOs", Double.valueOf(curRam));
                vpsInfoList.add(vps);
            }
        } else {
            log.error("当前云平台设备 【" + device.getKey() + "】主机列表接口返回值为：" + jsonResult);
        }
        return vpsInfoList;
    }
}
