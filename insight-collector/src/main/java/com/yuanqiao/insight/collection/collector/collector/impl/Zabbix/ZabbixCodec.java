package com.yuanqiao.insight.collection.collector.collector.impl.Zabbix;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yuanqiao.insight.acore.utils.SpringContextUtil;
import com.yuanqiao.insight.collection.collector.utils.DataBaseCodecUtils;
import com.yuanqiao.insight.common.util.common.TimeUtils;
import com.yuanqiao.insight.service.device.entity.MomgZabbixAlarm;
import com.yuanqiao.insight.service.device.entity.MomgZabbixTriggerVo;
import com.yuanqiao.insight.service.device.service.impl.MomgZabbixAlarmServiceImpl;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import io.github.hengyunabc.zabbix.api.DefaultZabbixApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.C;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class ZabbixCodec {
    DataBaseCodecUtils dataBaseCodecUtils = new DataBaseCodecUtils();
    //最外层jsonObject
    JSONObject jsonObject = new JSONObject();
    //公共结果集
    JSONObject commonMap = new JSONObject();
    ZabbixUtils zabbixUtils = new ZabbixUtils();
    RedisTemplate redisTemplate = (RedisTemplate) SpringContextUtil.getBean("redisTemplate");
    MomgZabbixAlarmServiceImpl zabbixAlarmServiceImpl = (MomgZabbixAlarmServiceImpl) SpringContextUtil.getBean("momgZabbixAlarmServiceImpl");

    public JSONObject dataCodec(CustomZabbixApi zabbixApi, List<ProertyMetadata> metadataList, String deviceKey) {
        //清空外层jsonObject和公共结果集commonMap
        jsonObject.clear();
        commonMap.clear();

        try {
            // 主机
            JSONArray hostArray = zabbixUtils.getHosts(zabbixApi);
            redisTemplate.opsForValue().set("zabbix_" + deviceKey + "_hosts", hostArray.toJSONString());

            List<Map<String, Object>> hostInfoList = new ArrayList<>();
            List<MomgZabbixAlarm> insertAlarmList = new ArrayList<>();
            List<MomgZabbixAlarm> updateAlarmList = new ArrayList<>();
            for (int j = 0; j < hostArray.size(); j++) {
                JSONObject hostObject = hostArray.getJSONObject(j);
                HashMap<String, Object> infoMap = new HashMap<>();
                String hostName = hostObject.getString("name");
                infoMap.put("name", hostName);
                infoMap.put("host", hostObject.getString("host"));
                infoMap.put("ip", hostObject.getJSONArray("interfaces").getJSONObject(0).getString("ip"));
                hostInfoList.add(infoMap);

                // 指标
                JSONArray itemArray = zabbixUtils.getItemByHostId(zabbixApi, hostObject.getString("hostid"));
                redisTemplate.opsForValue().set("zabbix_" + hostObject.getString("hostid") + "_items", itemArray.toJSONString());

                // 告警
                JSONArray problemArray = zabbixUtils.getProblemsByHostId(zabbixApi, hostObject.getString("hostid"));
                Map<String, MomgZabbixTriggerVo> triggerVoMap = rebuildProblem(zabbixApi, hostObject.getString("hostid"));
                List<MomgZabbixAlarm> zabbixAlarmList = new ArrayList<>();
                List<String> zabbixAlarmIdList = new ArrayList<>();
                if (CollUtil.isNotEmpty(problemArray)) {
                    for (int k = 0; k < problemArray.size(); k++) {
                        JSONObject problemObject = problemArray.getJSONObject(k);
                        String eventid = problemObject.getString("eventid");
                        zabbixAlarmIdList.add(eventid);
                        MomgZabbixAlarm zabbixAlarm = new MomgZabbixAlarm();
                        zabbixAlarm.setId(eventid);
                        zabbixAlarm.setHostId(hostObject.getString("hostid"));
                        String objectId = problemObject.getString("objectid");
                        zabbixAlarm.setObjectId(objectId);
                        zabbixAlarm.setEventId(problemObject.getString("eventid"));
                        Long clock = problemObject.getLong("clock");
                        zabbixAlarm.setClock(problemObject.getString("clock"));
                        zabbixAlarm.setClockTime(new Date(problemObject.getLong("clock") * 1000L));
                        zabbixAlarm.setREventId(problemObject.getString("r_eventid"));
                        zabbixAlarm.setRClock(problemObject.getString("r_clock"));
                        Long rClock = problemObject.getLong("r_clock");
                        zabbixAlarm.setRClockTime(rClock == 0 ? null : new Date(rClock * 1000L));
                        zabbixAlarm.setStatus(rClock == 0 ? "问题" : "已解决");
                        String acknowledged = String.valueOf(Optional.ofNullable(problemObject.get("acknowledged")).orElse("0"));
                        zabbixAlarm.setAcknowledged(acknowledged.equals("0") ? "未确认" : "已确认");
                        zabbixAlarm.setDuration(rClock == 0 ? TimeUtils.getDistanceTime((new Date().getTime() - (clock * 1000L))) : TimeUtils.getDistanceTime((rClock - clock) * 1000L));

                        String problemName = problemObject.getString("name");
                        String problemSeverity = problemObject.getString("severity");
                        zabbixAlarm.setName(problemName);
                        zabbixAlarm.setSeverity(problemSeverity);
                        if (StringUtils.isEmpty(problemName) && StringUtils.isEmpty(problemSeverity) && CollUtil.isNotEmpty(triggerVoMap)) {
                            if (triggerVoMap.containsKey(objectId)) {
                                MomgZabbixTriggerVo triggerVo = triggerVoMap.get(objectId);
                                zabbixAlarm.setName(triggerVo.getDescription().replace("{HOST.NAME}", hostName));
                                zabbixAlarm.setSeverity(triggerVo.getPriority());
                            }
                        }

                        zabbixAlarmList.add(zabbixAlarm);
                    }
                    List<MomgZabbixAlarm> oldAlarmList = zabbixAlarmServiceImpl.list(new QueryWrapper<MomgZabbixAlarm>().eq("host_id", hostObject.getString("hostid")).in("id", zabbixAlarmIdList));
                    List<MomgZabbixAlarm> updateList = zabbixAlarmList.stream().filter(p -> oldAlarmList.stream().anyMatch(p1 -> StringUtils.equals(p.getEventId(), p1.getEventId()))).collect(Collectors.toList());
                    updateAlarmList.addAll(updateList);
                    List<MomgZabbixAlarm> insertList = zabbixAlarmList.stream().filter(p -> updateList.stream().noneMatch(p1 -> StringUtils.equals(p.getEventId(), p1.getEventId()))).collect(Collectors.toList());
                    insertAlarmList.addAll(insertList);
                }
            }
            zabbixAlarmServiceImpl.saveBatch(insertAlarmList);
            zabbixAlarmServiceImpl.updateBatchById(updateAlarmList);

            commonMap.put("hostList", hostInfoList);

            //循环遍历当前的物模型
            metadataList.forEach(item -> {
                if (StringUtils.isNotEmpty(item.getDataType())) {
                    jsonObject = dataBaseCodecUtils.switchByDataType(item, "stca:" + deviceKey, jsonObject, commonMap);
                }
            });

        } catch (Exception e) {
            log.error("Zabbix数据拉取、解析异常！", e);
        } finally {
            zabbixApi.destroy();
        }
        return jsonObject;
    }

    public Map<String, MomgZabbixTriggerVo> rebuildProblem(CustomZabbixApi zabbixApi, String hostId) {
        Map<String, MomgZabbixTriggerVo> triggerVoMap = new HashMap<>();
        JSONArray triggerArray = zabbixUtils.getTriggerByObjectId(zabbixApi, hostId);
        if (CollUtil.isNotEmpty(triggerArray)) {
            List<MomgZabbixTriggerVo> triggerVoList = JSONObject.parseArray(triggerArray.toJSONString(), MomgZabbixTriggerVo.class);
            triggerVoMap = triggerVoList.stream().collect(Collectors.toMap(MomgZabbixTriggerVo::getTriggerId, MomgZabbixTriggerVo -> MomgZabbixTriggerVo));
        }
        return triggerVoMap;
    }

}
