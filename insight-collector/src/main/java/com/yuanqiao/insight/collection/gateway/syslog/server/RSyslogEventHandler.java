package com.yuanqiao.insight.collection.gateway.syslog.server;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch.core.BulkRequest;
import co.elastic.clients.elasticsearch.core.BulkResponse;
import co.elastic.clients.elasticsearch.indices.ElasticsearchIndicesClient;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.utils.SpringContextUtil;
import com.yuanqiao.insight.collection.gateway.syslog.grok.GrokService;
import com.yuanqiao.insight.common.constant.CommonConstant;
import com.yuanqiao.insight.common.util.common.LocalCacheUtils;
import com.yuanqiao.insight.common.util.grok.GrokUtil;
import com.yuanqiao.insight.service.device.service.IDeviceInfoService;
import com.yuanqiao.insight.service.device.service.impl.DeviceInfoServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.graylog2.syslog4j.server.SyslogServerEventIF;
import org.graylog2.syslog4j.server.SyslogServerIF;
import org.graylog2.syslog4j.server.SyslogServerSessionEventHandlerIF;
import org.graylog2.syslog4j.util.SyslogUtility;
import org.jeecg.common.mq.RedisMq;
import org.jeecg.common.mq.stream.Streams;

import java.io.IOException;
import java.net.SocketAddress;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2023/8/1
 */
@Slf4j
public class RSyslogEventHandler implements SyslogServerSessionEventHandlerIF {

    private final static ElasticsearchClient client = SpringContextUtil.getBean(ElasticsearchClient.class);

    public final static ElasticsearchIndicesClient elasticsearchIndicesClient = SpringContextUtil.getBean(ElasticsearchIndicesClient.class);

    private final static IDeviceInfoService deviceInfoService = SpringContextUtil.getBean(DeviceInfoServiceImpl.class);

    private final static GrokService grokService = SpringContextUtil.getBean(GrokService.class);

    private final static LocalCacheUtils cacheUtils = LocalCacheUtils.getInstance();

    private final static RedisMq redisMq = SpringContextUtil.getBean(RedisMq.class);

    private final static Map<String, List<JSONObject>> buffer = new HashMap<>();

    //重写event方法
    @Override
    public void event(Object session, SyslogServerIF syslogServer, SocketAddress socketAddress, SyslogServerEventIF event) {
        //判断传输时间是否存在，不存在将现在的时间设置为传输时间
        Date date = event.getDate() == null ? new Date() : event.getDate();
        //将解析日志的生成端,<<3是要该数左移动三位计算
        String facility = SyslogUtility.getFacilityString(event.getFacility() << 3);
        //讲解析日志的级别，级别越大越低
        String level = SyslogUtility.getLevelString(event.getLevel());
        //获取当前的源设备IP
        String sourceIP = getIPAddress(socketAddress.toString());
        //获取到信息主体
        String msg = event.getMessage();
        final String protocol = syslogServer.getProtocol();
        try {

            final RSyslog rSyslog = RSyslogStarter.logMap.get(protocol);
            if (rSyslog != null) {
                rSyslog.num.incrementAndGet();
            }
            JSONObject msgObj = JSON.parseObject(msg);
            msgObj.put("@timestamp", DateUtil.formatDateTime(date));
            msgObj.put("sourceIP", sourceIP);
            msgObj.put("protocol", protocol);
            msgObj.put("create_time", DateUtil.formatDateTime(new Date()));
            final String syslogtag = msgObj.getString("syslogtag");
            JSONObject data;
            String deviceCode;
            String index;
            String grokCode;
            if (syslogtag.contains(":")) {
                grokCode = syslogtag.substring(0, syslogtag.indexOf(":"));
                deviceCode = syslogtag.substring(syslogtag.indexOf(":") + 1);
                index = "syslog-" + grokCode;
                createShareIndex("syslog-" + grokCode);
                final List<String> collect = grokService.getGrok(grokCode);
                data = GrokUtil.grok(collect, msgObj.getString("message"));
            } else {
                throw new RuntimeException("创建索引失败");
            }
            msgObj.put("data", data);
            msgObj.put("device_code", deviceCode);
            msgObj.put("log_category", grokCode);
            if (log.isDebugEnabled()) {
                log.debug("facility:{} , sourceIP: {} , date:{} , level:{} , protocol:{}", facility, sourceIP, date, level, protocol);
                log.debug("message:{}", msgObj.toJSONString());
            }
            int bufferSize = Integer.parseInt(String.valueOf(Optional.ofNullable(cacheUtils.getValueByKey(CommonConstant.DATA_DICT_KEY + "syslog_policy_buffer_size")).orElse(10)));
            int deprecateSize = Integer.parseInt(String.valueOf(Optional.ofNullable(cacheUtils.getValueByKey(CommonConstant.DATA_DICT_KEY + "syslog_policy_deprecate_size")).orElse(2)));
            final List<JSONObject> list = buffer.computeIfAbsent(deviceCode, i -> new ArrayList<>());
            if (list.size() < bufferSize) {
                list.add(msgObj);
            } else {
                for (int i = 0; i < deprecateSize; i++) {
                    list.remove(Integer.parseInt(String.format("%.0f", Math.random() * (list.size() - 1))));
                }
            }
            String key = String.format("COMMON^_^%s^_^syslog", deviceCode);
            JSONObject content = new JSONObject();
            content.put("deviceKey", key);
            redisMq.publish(Streams.ON_DATA, content);
            if (CollUtil.isNotEmpty(list)) {
                BulkRequest.Builder br = new BulkRequest.Builder();
                for (JSONObject jsonObject : list) {
                    br.operations(op -> op.create(c -> c.index(index).document(jsonObject)));
                }
                final BulkResponse bulk = client.bulk(br.build());
                if (bulk.errors()) {
                    log.error("保存syslog失败,error {}", bulk);
                }
                list.clear();
            }
        } catch (IOException e) {
            log.error("日志持久化失败", e);
        }
    }

    private static List<String> nginxPatterns() {
        List<String> patterns = new ArrayList<>();
        patterns.add("%{IPORHOST:remote_addr} - %{DATA:remote_user} \\[%{HTTPDATE:time_local}\\] \"%{WORD:request_method} %{DATA:uri}\\?%{DATA:param} HTTP/%{NUMBER:http_version}\" %{NUMBER:response_code} %{NUMBER:body_sent_bytes} \"%{DATA:http_referrer}\" \"%{DATA:http_user_agent}\" \"%{NUMBER:request_time:float}\" \"%{NUMBER:response_time:float}\"");
        patterns.add("%{IPORHOST:remote_addr} - %{DATA:remote_user} \\[%{HTTPDATE:time_local}\\] \"%{WORD:request_method} %{DATA:uri}\\?%{DATA:param} HTTP/%{NUMBER:http_version}\" %{NUMBER:response_code} %{NUMBER:body_sent_bytes} \"%{DATA:http_referrer}\" \"%{DATA:http_user_agent}\" \"%{NUMBER:request_time:float}\"");
        patterns.add("%{IPORHOST:remote_addr} - %{DATA:remote_user} \\[%{HTTPDATE:time_local}\\] \"%{WORD:request_method} %{DATA:uri}\\?%{DATA:param} HTTP/%{NUMBER:http_version}\" %{NUMBER:response_code} %{NUMBER:body_sent_bytes} \"%{DATA:http_referrer}\" \"%{DATA:http_user_agent}\" ");
        patterns.add("%{IPORHOST:remote_addr} - %{DATA:remote_user} \\[%{HTTPDATE:time_local}\\] \"%{WORD:request_method} %{DATA:uri} HTTP/%{NUMBER:http_version}\" %{NUMBER:response_code} %{NUMBER:body_sent_bytes} \"%{DATA:http_referrer}\" \"%{DATA:http_user_agent}\" \"%{NUMBER:request_time:float}\" \"%{NUMBER:response_time:float}\"");
        patterns.add("%{IPORHOST:remote_addr} - %{DATA:remote_user} \\[%{HTTPDATE:time_local}\\] \"%{WORD:request_method} %{DATA:uri} HTTP/%{NUMBER:http_version}\" %{NUMBER:response_code} %{NUMBER:body_sent_bytes} \"%{DATA:http_referrer}\" \"%{DATA:http_user_agent}\" \"%{NUMBER:request_time:float}\"");
        patterns.add("%{IPORHOST:remote_addr} - %{DATA:remote_user} \\[%{HTTPDATE:time_local}\\] \"%{WORD:request_method} %{DATA:uri} HTTP/%{NUMBER:http_version}\" %{NUMBER:response_code} %{NUMBER:body_sent_bytes} \"%{DATA:http_referrer}\" \"%{DATA:http_user_agent}\" ");
        return patterns;
    }

    //获取到该字符串里的ip地址
    private String getIPAddress(String bString) {
        String regEx = "((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3}";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(bString);
        if (m.find()) {
            return m.group();
        } else {
            return "";
        }
    }

    @Override
    public void initialize(SyslogServerIF syslogServer) {
        //System.out.println("实例化一个处理器");
    }

    @Override
    public void destroy(SyslogServerIF syslogServer) {
    }

    @Override
    public Object sessionOpened(SyslogServerIF syslogServer, SocketAddress socketAddress) {
        return null;
    }

    @Override
    public void exception(Object session, SyslogServerIF syslogServer, SocketAddress socketAddress, Exception exception) {
        exception.printStackTrace();
    }

    @Override
    public void sessionClosed(Object session, SyslogServerIF syslogServer, SocketAddress socketAddress, boolean timeout) {
    }

    public void createShareIndex(String index) throws IOException {
        boolean value = elasticsearchIndicesClient.exists(e -> e.index(index)).value();
        if (!value) {
            elasticsearchIndicesClient.createDataStream(createIndexRequest -> createIndexRequest.name(index));
        }
    }

}
