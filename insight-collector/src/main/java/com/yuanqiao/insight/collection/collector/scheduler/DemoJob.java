package com.yuanqiao.insight.collection.collector.scheduler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 示例带参定时任务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DemoJob implements Job {

//	@Autowired
//    DemoDeviceServiceImpl demoDeviceService;



	@Override
	public void execute(String pramrs)  {
		System.out.println(pramrs);
		//		//Map map=jobExecutionContext.getMergedJobDataMap();



		//		//		//获取请求参数
		//		//		//http、snmp请求
		//		//		//response 数据格式化
		//		//		//demoAdaptorService.onData((String) map.get("parameter"));
		//		//		//log.info(String.format("welcome %s! Jeecg-Boot 带参数定时任务 SampleParamJob !   时间:", this.parameter));
	}
}
