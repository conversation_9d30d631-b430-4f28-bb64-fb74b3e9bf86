package com.yuanqiao.insight.collection.collector.collector.impl.Awcloud;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.collector.Collector;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.acore.scheduler.SchedulerManagerInter;
import com.yuanqiao.insight.acore.utils.SpringContextUtil;
import com.yuanqiao.insight.collection.collector.utils.DataBaseCodecUtils;
import com.yuanqiao.insight.service.collector.utils.DataPersistenceStorageUtils;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import com.yuanqiao.insight.utils.SNMPMetadataUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 天熠虚拟化云平台（V6.3_01）(ARM)
 */
@Slf4j
@EnableAsync
public class AwCloudCollector implements Collector {
    private Device device;
    private SchedulerManagerInter schedulerManager;
    private RedisTemplate redisTemplate;
    private SNMPMetadataUtils metadataUtils;


    //初始化
    public void init(Device device, SchedulerManagerInter schedulerManager) {
        //绑定设备
        this.device = device;
        //绑定调度管理器
        this.schedulerManager = schedulerManager;
        //初始化RedisTemplate
        this.redisTemplate = (RedisTemplate) SpringContextUtil.getBean("redisTemplate");
        //初始化物模型工具类
        this.metadataUtils = SpringContextUtil.getBean(SNMPMetadataUtils.class);

    }

    //执行方法
    public void execute() {
        log.info("--------天熠虚拟化云平台监控任务执行了...");
        //初始化监控数据持久化存储工具类
        DataPersistenceStorageUtils storageUtils = (DataPersistenceStorageUtils) SpringContextUtil.getBean("dataPersistenceStorageUtils");
        //初始化最外层jsonObject
        JSONObject jsonObject = new JSONObject();
        //将物模型同步到本地缓存中
        List<ProertyMetadata> metadataList = new ArrayList<>();
        try {
            metadataList = metadataUtils.setMetadata2(device.getKey(), device.getProtocol());
        } catch (Exception e) {
            log.error("从本地缓存中获取物模型出现异常！", e);
        }

//        HttpClientUtil httpClientUtil = new HttpClientUtil();
        String url = "https://" + device.getConnectParam().get("ip") + ":" + device.getConnectParam().get("port") + "/awstack-user/v1/internal/login";
        log.info("天熠虚拟化云平台URL：" + url);
        JSONObject paramMap1 = new JSONObject();
        paramMap1.put("userName", device.getConnectParam().get("username"));
        paramMap1.put("password", device.getConnectParam().get("password"));
        paramMap1.put("enterpriseLoginName", "awcloud");
        String login = HttpRequest.post(url)
                .header("Content-Type", "application/json; charset=UTF-8")
                .body(paramMap1.toString())
                .execute().body();

        log.error("当前云平台设备 【" + device.getKey() + "】login登录接口返回值为：" + login);
        JSONObject jsonResult = JSONObject.parseObject(login);
        log.info("当前设备 【" + device.getKey() + "】接口code" + jsonResult.get("code"));
        if (jsonResult != null && jsonResult.get("code").equals("0")) {
            log.info("当前设备 【" + device.getKey() + "】 是否连接成功——snmpUtils.isConnectble()：" + true);
            SimpleDateFormat time_sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String timestampFormat = time_sdf.format(new Date());
            storageUtils.heartBeatMessageHandler(schedulerManager, device,timestampFormat);            //调用适配器, 解码数据
            AwCloudCodec awcloudCodec = new AwCloudCodec();
            JSONObject commonMap = awcloudCodec.dataCodecObject(jsonResult, device);
            log.info("开始获取并解析device_code：" + device.getKey() + " 的数据信息");
            JSONObject jsonObjectData = new JSONObject();
            DataBaseCodecUtils dataBaseCodecUtils = new DataBaseCodecUtils();
            for (ProertyMetadata metadata : metadataList) {
                if (StringUtils.isNotEmpty(metadata.getDataType())) {
                    jsonObject = dataBaseCodecUtils.switchByDataType(metadata, "stca:" + device.getKey(), jsonObjectData, commonMap);
                }
            }
            log.info("Codec解析出的数据：" + jsonObject);
            //将解析到的数据保存到redis状态容器
            if (!jsonObject.isEmpty()) {
                //解码得到的数据存到状态容器、发布设备数据事件
                schedulerManager.setStatusCache(device, jsonObject);
            } else {
                log.error("[{}]: 未解析到数据...", device.getKey());
            }
        } else {
            //连接失败, 调用ShedulerManager, 设置设备离线,
            log.error("当前云平台设备 【" + device.getKey() + " 】返回值为：" + jsonResult + "，设备连接失败，设置设备离线！");
            schedulerManager.setDeviceDown(device);
        }


    }

    public static void main(String[] args) {

        RestTemplate restTemplate = new RestTemplate();
        String url = "http://www.baidu.com/";
        HttpHeaders headers = new HttpHeaders();
        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(params, headers);
        ResponseEntity<String> String = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
        System.out.println("string========" + String);
        //        String result = "{\"code\":\"0\",\"message\":null,\"data\":{\"status\":0,\"msg\":\"OK\",\"data\":{\"id\":1,\"userUid\":\"d2edb4a622d94ce38831bd7067cc771c\",\"userName\":\"admin\",\"password\":null,\"enterpriseUid\":\"66666666666666666666666666666666\",\"enterpriseName\":null,\"enterpriseLoginName\":\"awcloud\",\"domainUid\":\"default\",\"domainName\":\"default\",\"defaultProjectUid\":\"3937681e206c4dc19735878995528176\",\"defaultProjectName\":\"admin\",\"keystoneUrl\":\"http://************:20001/keystone/v3/\",\"managementRole\":2,\"roleUid\":\"dd41933f59b8439694f8a986c09f2323\",\"roleName\":\"admin\",\"menuList\":null,\"regionUid\":\"8b4f22a536f845769820b5777d549238\",\"regionName\":\"default\",\"regionKey\":\"FFFFF\",\"status\":3,\"email\":\"<EMAIL>\",\"phone\":\"13100000000\",\"supportOtherClouds\":null,\"tunnelUrl\":\"http://************:20001/keystone/v3/\",\"token\":\"gAAAAABb6RTmUmgdWtLP-tIzCLR70Ler-vz8qwblZCrhuElwUATY0LKSQeqC-p5BiYmSGrLdaok80vu3LWGIirOQzcNkEgmze1th0rEELd0Y8rucVrlZCnA8NpYuqfjap - yMASVx7Ine8gvNQqPFvHWxGSxy4484vrJfquy_gDntBrEVIzmMIU \",\"expiresAt\":1542023494000,\"authToken\":\"bNbrJm7Y+DSW7x4viwl4dz4psor7MP537Ohkxy77fGZPH0Ms+lYsb6RZGY8ZT71vVdGyyjyNOIPy3NLR5xU / 1 CtWuSp2 + NCpoxHlRY6mW0s2iSpnlrtqILiV3cnqGb8VwifdZpAFEghXjU5RvyWC5UL4xwB9vwqZQlDCLrFmkoA = \",\"version\":\"2\",\"component\":null,\"platformStatus\":1,\"firstLogin\":1,\"createTime\":1541685482000,\"pwdLastUpdatedTime\":1541685482000,\"otherTunnelUrls\":[],\"verificationCode\":null,\"verificationKey\":null,\"backupService\":null,\"enabled\":1,\"installIronic\":null,\"installK8s\":null,\"cinderService\":null,\"isEnabledArbiter\":null,\"enabledCeph\":null,\"regionList\":null,\"isTopNonTrivial\":null,\"arbiterPromoteStep\":null,\"regionBusiAuth\":null,\"isCustom\":null,\"projectList\":[{\"projectName\":\"tempest-UsersV3TestJSON-307521233\",\"projectId\":\"fd3f745678eb445897b58c2196aee929\"},{\"projectName\":\"tempest-UsersV3TestJSON-1162488481\",\"projectId\":\"e84ca8eac1e14b7a8b95aa7b4aafed95\"},{\"projectName\":\"tempest-VolumeTypesV2Test-199626000\",\"projectId\":\"e35a5a18534544f8bd32ded7759fb5d0\"},{\"projectName\":\"tempest-UsersV3TestJSON-371284968\",\"projectId\":\"e337d6bce9b949b2bfc8175887e414ee\"},{\"projectName\":\"tempest-UsersV3TestJSON-1989724041\",\"projectId\":\"c90bc89c973a40359a07d3b92c0f9ef7\"},{\"projectName\":\"tempest-VolumeTypesV2Test-1730527800\",\"projectId\":\"9c7b36da670545b6ae215004f1afa531\"},{\"projectName\":\"tempest-VolumeTypesV2Test-455862215\",\"projectId\":\"9ba38319484049dbbedf3695e0312538\"},{\"projectName\":\"tempest-FlavorsV2NegativeTest-1126456337\",\"projectId\":\"881419161d5a432e9f10fa14d5392af4\"},{\"projectName\":\"tempest-SnapshotsActionsV1Test-769950288\",\"projectId\":\"6336d31b0c46411a9c7de41f09a0f997\"},{\"projectName\":\"tempest-VolumesV2ActionsTest-1608799570\",\"projectId\":\"54bccda2b3424f12a7aba8939aaea77e\"},{\"projectName\":\"tempest-ServersAdminTestJSON-384130450\",\"projectId\":\"3ce85b71ad134dee896d2497daeca10e\"},{\"projectName\":\"admin\",\"projectId\":\"3937681e206c4dc19735878995528176\"},{\"projectName\":\"tempest-UsersV3TestJSON-811561998\",\"projectId\":\"38e757748e1d4d1eba3ba0ba129a2d1b\"},{\"projectName\":\"tempest-UsersV3TestJSON-1001367758\",\"projectId\":\"3314fb1ce3c2419ea107e9a08282f661\"},{\"projectName\":\"tempest-VolumesV2ActionsTest-2146991057\",\"projectId\":\"068c2fc97b7f4362b2a841e58670c2a0\"},{\"projectName\":\"tempest-VolumesV2ActionsTest-901306810\",\"projectId\":\"0516ab6c73b845c8b5c591a955894050\"},{\"projectName\":\"tempest-UsersV3TestJSON-1610891505\",\"projectId\":\"0393e7d903a443589538d98f6b8a7b0c\"},{\"projectName\":\"tempest-VolumesV2ActionsTest-567363148\",\"projectId\":\"02772d82b8044a9cadcc1d2fd0ba08f1\"},{\"projectName\":\"project_lm\",\"projectId\":\"a29498f5f9714d38b83159a895197f97\"}],\"pwdExpiredDate\":null,\"pwdForceModify\":null},\"type\":null,\"total\":1}}";
//        JSONObject jsonObject = JSONObject.parseObject(result);
//        System.out.println("jsonObject ==" + jsonObject.get("code"));
//        if (!jsonObject.isEmpty()) {
//            System.out.println("jsonObject ==" + jsonObject.get("code"));
//            JSONObject data1 = (JSONObject) jsonObject.get("data");
//            JSONObject data2 = (JSONObject) data1.get("data");
//            String token = (String) data2.get("token");
//            System.out.println("data2 ==" + token);
//
//        }
    }
}
