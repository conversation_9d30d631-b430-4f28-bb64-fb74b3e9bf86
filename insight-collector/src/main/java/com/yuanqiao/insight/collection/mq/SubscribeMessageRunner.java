package com.yuanqiao.insight.collection.mq;

import com.yuanqiao.insight.collection.mq.listener.*;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.mq.stream.Streams;
import org.jeecg.common.mq.subscribe.Subscribe;
import org.jeecg.common.mq.subscribe.entity.SubscribeMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class SubscribeMessageRunner extends Subscribe implements CommandLineRunner {

    @Autowired
    private DeviceMonitoringStartListener deviceMonitoringStartListener;
    @Autowired
    private DeviceMonitoringStopListener deviceMonitoringStopListener;
    @Autowired
    private DeviceMonitoringChangeListener deviceMonitoringChangeListener;
    @Autowired
    private UpdateMetadataListener updateMetadataListener;
    @Autowired
    private AvailabilityChangeListener availabilityChangeListener;
    @Autowired
    private SshTelnetExecuteListener sshTelnetExecuteListener;
    @Autowired
    private BuildChainListener buildChainListener;
    @Autowired
    private RemoveChainListener removeChainListener;
    @Autowired
    private ConfigureDictRefreshListener configureDictRefreshListener;
    @Autowired
    private DataRepoterManageRefreshListener dataRepoterManageRefreshListener;
    @Autowired
    private SshTelnetTimedExecuteEnableListener sshTelnetTimedExecuteEnableListener;
    @Autowired
    private SshTelnetTimedExecuteResetListener sshTelnetTimedExecuteResetListener;
    @Autowired
    private SshTelnetTimedExecuteOneShotListener sshTelnetTimedExecuteOneShotListener;
    @Autowired
    private SshTelnetTimedExecuteStopListener sshTelnetTimedExecuteStopListener;
    @Autowired
    protected AbutmentPullJobControlListener abutmentPullJobControlListener;
    @Autowired
    private ScriptExecuteListener scriptExecuteListener;
    @Autowired
    private GbaseJobControlListener gbaseJobControlListener;

    @Override
    public void run(String... args) throws Exception {
        log.info("======开始订阅RedisMq消息======");
        new Thread(() -> {
            try {
                subscribeMessageEvent();
            } catch (Exception e) {
                log.error("[collector]订阅消息,发生错误: ", e);
            }
        }).start();
    }

    @Override
    public List<SubscribeMessage> assemblyData() {
        List<SubscribeMessage> messageList = new ArrayList<>();
        //订阅启动设备监控事件
        messageList.add(new SubscribeMessage(deviceMonitoringStartListener, Streams.DEVICE_MONITORING_START));
        //订阅关闭设备监控事件
        messageList.add(new SubscribeMessage(deviceMonitoringStopListener, Streams.DEVICE_MONITORING_STOP));
        //订阅关闭设备监控事件
        messageList.add(new SubscribeMessage(deviceMonitoringChangeListener, Streams.DEVICE_MONITORING_CHANGE));
        //订阅更新缓存物模型事件
        messageList.add(new SubscribeMessage(updateMetadataListener, Streams.UPDATE_METADATA));
        //订阅到业务可用性监控配置变更事件
        messageList.add(new SubscribeMessage(availabilityChangeListener, Streams.BUSINESS_AVAILABILITY_CHANGE));
        //订阅流程构建事件
        messageList.add(new SubscribeMessage(buildChainListener, Streams.CHAIN_BUILD));
        //订阅流程销毁事件
        messageList.add(new SubscribeMessage(removeChainListener, Streams.REMOVE_CHAIN));
        //订阅配置字典缓存刷新消息
        messageList.add(new SubscribeMessage(configureDictRefreshListener, Streams.CONFIGURE_DICT_REFRESH));
        //订阅数据上报管理缓存刷新消息
        messageList.add(new SubscribeMessage(dataRepoterManageRefreshListener, Streams.DATA_REPORT_MANAGE_REFRESH));
        //订阅到SSH、Telnet手动触发执行备份还原命令事件
        messageList.add(new SubscribeMessage(sshTelnetExecuteListener, Streams.SSH_TELNET_EXECUTE));
        //订阅到SSH、Telnet定时执行备份还原命令启用事件
        messageList.add(new SubscribeMessage(sshTelnetTimedExecuteEnableListener, Streams.SSH_TELNET_TIMED_EXECUTE_ENABLE));
        //订阅到SSH、Telnet定时执行备份还原命令禁用事件
        messageList.add(new SubscribeMessage(sshTelnetTimedExecuteStopListener, Streams.SSH_TELNET_TIMED_EXECUTE_STOP));
        //订阅到SSH、Telnet执行备份还原命令重置事件
        messageList.add(new SubscribeMessage(sshTelnetTimedExecuteResetListener, Streams.SSH_TELNET_TIMED_EXECUTE_RESET));
        //订阅到SSH、Telnet执行备份还原命令单次执行事件
        messageList.add(new SubscribeMessage(sshTelnetTimedExecuteOneShotListener, Streams.SSH_TELNET_TIMED_EXECUTE_ONESHOT));
        //订阅到第三方监控对接拉取任务管理事件
        messageList.add(new SubscribeMessage(abutmentPullJobControlListener, Streams.ABUTMENT_PULL_JOB_CONTROL));
        messageList.add(new SubscribeMessage(scriptExecuteListener, Streams.AUTO_CONTROL_SCRIPT_EXECUTE));
        messageList.add(new SubscribeMessage(gbaseJobControlListener, Streams.GBASE_JOB_CONTROL));
        return messageList;
    }


   /* 以下废弃,但是保留
   @Override
    public void run(String... args) throws Exception {
        log.info("======开始订阅RedisMq消息======");
        new Thread(() -> {
            try {
                Map<String, SubscribeMessage> message = new HashMap<>();
                //订阅启动设备监控事件
                message.put(Streams.DEVICE_MONITORING_START.getValue().getName(), new SubscribeMessage(deviceMonitoringStartListener, Streams.DEVICE_MONITORING_START));
                //订阅关闭设备监控事件
                message.put(Streams.DEVICE_MONITORING_STOP.getValue().getName(), new SubscribeMessage(deviceMonitoringStopListener, Streams.DEVICE_MONITORING_STOP));
                //订阅关闭设备监控事件
                message.put(Streams.DEVICE_MONITORING_CHANGE.getValue().getName(), new SubscribeMessage(deviceMonitoringChangeListener, Streams.DEVICE_MONITORING_CHANGE));
                //订阅更新缓存物模型事件
                message.put(Streams.UPDATE_METADATA.getValue().getName(), new SubscribeMessage(updateMetadataListener, Streams.UPDATE_METADATA));
                //订阅到业务可用性监控配置变更事件
                message.put(Streams.BUSINESS_AVAILABILITY_CHANGE.getValue().getName(), new SubscribeMessage(availabilityChangeListener, Streams.BUSINESS_AVAILABILITY_CHANGE));
                //订阅流程构建事件
                message.put(Streams.CHAIN_BUILD.getValue().getName(), new SubscribeMessage(buildChainListener, Streams.CHAIN_BUILD));
                //订阅流程销毁事件
                message.put(Streams.REMOVE_CHAIN.getValue().getName(), new SubscribeMessage(removeChainListener, Streams.REMOVE_CHAIN));
                //订阅配置字典缓存刷新消息
                message.put(Streams.CONFIGURE_DICT_REFRESH.getValue().getName(), new SubscribeMessage(configureDictRefreshListener, Streams.CONFIGURE_DICT_REFRESH));
                //订阅到SSH、Telnet手动触发执行备份还原命令事件
                message.put(Streams.SSH_TELNET_EXECUTE.getValue().getName(), new SubscribeMessage(sshTelnetExecuteListener, Streams.SSH_TELNET_EXECUTE));
                //订阅到SSH、Telnet定时执行备份还原命令启用事件
                message.put(Streams.SSH_TELNET_TIMED_EXECUTE_ENABLE.getValue().getName(), new SubscribeMessage(sshTelnetTimedExecuteEnableListener, Streams.SSH_TELNET_TIMED_EXECUTE_ENABLE));
                //订阅到SSH、Telnet定时执行备份还原命令禁用事件
                message.put(Streams.SSH_TELNET_TIMED_EXECUTE_STOP.getValue().getName(), new SubscribeMessage(sshTelnetTimedExecuteStopListener, Streams.SSH_TELNET_TIMED_EXECUTE_STOP));
                //订阅到SSH、Telnet执行备份还原命令重置事件
                message.put(Streams.SSH_TELNET_TIMED_EXECUTE_RESET.getValue().getName(), new SubscribeMessage(sshTelnetTimedExecuteResetListener, Streams.SSH_TELNET_TIMED_EXECUTE_RESET));
                //订阅到SSH、Telnet执行备份还原命令单次执行事件
                message.put(Streams.SSH_TELNET_TIMED_EXECUTE_ONESHOT.getValue().getName(), new SubscribeMessage(sshTelnetTimedExecuteOneShotListener, Streams.SSH_TELNET_TIMED_EXECUTE_ONESHOT));
                SubscribeMessageContext subscribeMessageContext = new SubscribeMessageContext(message);
                List<Object> nodeList = new ArrayList<>();
                for (String s : message.keySet()) {
                    nodeList.add(ELBus.node("cmpSubscribeMessage").tag(s));
                }
                ELWrapper el = ELBus.then(ELBus.when(nodeList.toArray()), "cmpSubscribeFinal");
                LiteFlowChainELBuilder.createChain().setChainId("collector").setEL(el.toEL()).build();
                FlowExecutor flowExecutor = InjectPretrain.getFlowExecutor();
                LiteflowResponse liteflowResponse = flowExecutor.execute2Resp("collector", null, subscribeMessageContext);
                if (!liteflowResponse.isSuccess()) {
                    Exception e = liteflowResponse.getCause();
                    log.error("流程[collector],发生错误: ", e);
                }
            } catch (Exception e) {
                log.error("[collector]订阅消息,发生错误: ", e);
            }
        }).start();
    }*/

}
