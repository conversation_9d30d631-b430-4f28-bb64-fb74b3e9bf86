package com.yuanqiao.insight.collection.collector.scheduler;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.yuanqiao.insight.acore.collector.Collector;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.acore.scheduler.SchedulerManagerInter;
import com.yuanqiao.insight.acore.utils.SpringContextUtil;
import com.yuanqiao.insight.common.util.common.LocalCacheUtils;
import com.yuanqiao.insight.monitoring.modules.business.entity.BusinessAvailability;
import com.yuanqiao.insight.monitoring.modules.business.entity.BusinessAvailabilityResult;
import com.yuanqiao.insight.monitoring.modules.business.service.IAutoTestService;
import com.yuanqiao.insight.monitoring.modules.business.service.IBusinessAvailabilityService;
import com.yuanqiao.insight.monitoring.modules.business.service.impl.AutoTestServiceImpl;
import com.yuanqiao.insight.monitoring.modules.business.service.impl.BusinessAvailabilityServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.util.DateUtils;
import org.springframework.context.ApplicationContext;

import java.util.Date;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @title: AvailabilityJob
 * @description: 可用性监控测试定时任务实现
 * @date 2023/8/21
 */
@Slf4j
public class AvailabilityJob implements Collector {

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 网关编码
     */
    private String gatewayCode;

    //创建一个可缓存的线程池。如果线程池的大小超过了处理任务所需要的线程，那么就会回收部分空闲（60秒不执行任务）的线程，
    //线程池
    private ExecutorService pools = Executors.newCachedThreadPool();

    /**
     * 可用性监控任务管理接口
     */
    private IBusinessAvailabilityService iBusinessAvailabilityService;

    /**
     * 自动化测试接口
     */
    private IAutoTestService iAutoTestService;

    /**
     * 缓存工具类，获取配置字典
     */
    LocalCacheUtils cacheUtils = LocalCacheUtils.getInstance();


    public AvailabilityJob(){
        this.iBusinessAvailabilityService = SpringContextUtil.getApplicationContext().getBean(BusinessAvailabilityServiceImpl.class);
        this.iAutoTestService = SpringContextUtil.getApplicationContext().getBean(AutoTestServiceImpl.class);

    }
    @Override
    public void init(Device device, SchedulerManagerInter schedulerManager) {

    }

    @Override
    public void init(Device device, SchedulerManagerInter schedulerManager, ApplicationContext applicationContext) {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void execute() {
       TaskIdRunnable runnable = new TaskIdRunnable();
        runnable.setBoby(this.taskId);
        runnable.setGatewayCode(this.gatewayCode);
        pools.execute(runnable);

    }

    //线程执行
    class TaskIdRunnable implements Runnable {
        //备份ID
        private String boby;
        /**
         * 网关编码
         */
        private String gatewayCode;
        public void setBoby(String boby) {
            this.boby = boby;
        }
        public void setGatewayCode(String gatewayCode) {
            this.gatewayCode = gatewayCode;
        }

        @Override
        public void run() {
            // 根据id获取可用性测试实配置对象
            BusinessAvailability businessAvailability = iBusinessAvailabilityService.getById(boby);
            //处于正常状态的进行测试
            if(businessAvailability.getStatus() == CommonConstant.AVAILABILITY_STATUS_NORMAL){
                //未锁定状态的进行测试(或者锁定超过1小时的，超长时间认为是上次测试异常结束，未能正常解锁)
                if(businessAvailability.getRunLocked()!= CommonConstant.AVAILABILITY_STATUS_LOCKED){
                    runTest(businessAvailability);
                }else{
                    if(businessAvailability.getLockTime()!=null){
                        int unlockLimited = 10; //解锁限定时间10分钟，锁定时间大于10分钟自定解锁
                        if(cacheUtils.getValueByKey(getDictKey("autoTestBrowser_unlockLimited"))!=null){
                            unlockLimited = Integer.valueOf((String) cacheUtils.getValueByKey(getDictKey("autoTestBrowser_unlockLimited")));
                        }else{
                            log.warn("--------------获取可用性测试配置的定时任务自动解除锁定限定时间失败，使用默认配置：10分钟 --------------");
                        }
                        //获取锁定分钟数
                        int durationMinute = DateUtils.dateDiff('m',new Date(),businessAvailability.getLockTime());
                        if(durationMinute > unlockLimited){
                            runTest(businessAvailability);
                        }
                    }else{
                        //锁定时间为空，说明未锁定过，直接执行
                        runTest(businessAvailability);
                    }
                }
            }
        }

        private void runTest(BusinessAvailability businessAvailability) {
            //锁定
            iBusinessAvailabilityService.update(new BusinessAvailability().setRunLocked(CommonConstant.AVAILABILITY_STATUS_LOCKED).setLockTime(new Date()),
                    new UpdateWrapper<BusinessAvailability>().lambda().eq(BusinessAvailability::getId, businessAvailability.getId()));

            //进行测试
            BusinessAvailabilityResult testResult = iAutoTestService.runCustomAvailabilityTest(businessAvailability,gatewayCode);
            BusinessAvailability newAvailability = new BusinessAvailability();
            // 测试完成，修改最新的执行结果并解锁
            newAvailability.setLastTestId(testResult.getId());
            newAvailability.setLastTestTime(testResult.getEndTime());
            newAvailability.setLastTestResult(testResult.getSuccess());
            newAvailability.setRunLocked(CommonConstant.AVAILABILITY_STATUS_UNLOCK);
            iBusinessAvailabilityService.update(newAvailability,
                    new UpdateWrapper<BusinessAvailability>().lambda().eq(BusinessAvailability::getId, businessAvailability.getId()));
        }


        /**
         * 获取完整的配置字典key
         * @param dictKey 某个字典项实际的字典key(去掉网关编码的部分)
         * @return
         */
        public String getDictKey(String dictKey) {
            if(Strings.isNotBlank(gatewayCode)){
                //网关中，加上网关编码
                log.warn("--------------job配置字典key:"+CommonConstant.LOCAL_CACHE_DICT_KEY_PREFIX + gatewayCode + "_" + dictKey+" --------------");
                return CommonConstant.LOCAL_CACHE_DICT_KEY_PREFIX + gatewayCode + "_" + dictKey;
            }else{
                //平台
                log.warn("--------------job配置字典key:"+CommonConstant.LOCAL_CACHE_DICT_KEY_PREFIX + dictKey+" --------------");
                return CommonConstant.LOCAL_CACHE_DICT_KEY_PREFIX + dictKey;
            }
        }
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getGatewayCode() {
        return gatewayCode;
    }

    public void setGatewayCode(String gatewayCode) {
        this.gatewayCode = gatewayCode;
    }

}
