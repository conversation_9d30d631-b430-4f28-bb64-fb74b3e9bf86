package com.yuanqiao.insight.collection.mq.listener;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.collector.Collector;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.collection.collector.collector.impl.sshOrTelnet.SshOrTelnetService;
import com.yuanqiao.insight.collection.collector.scheduler.SchedulerManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.mq.aspect.annotation.RedisMessageExtend;
import org.jeecg.common.mq.stream.Streams;
import org.jeecg.common.mq.utils.AnalyzeContent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.connection.stream.MapRecord;
import org.springframework.data.redis.stream.StreamListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SshTelnetTimedExecuteEnableListener implements StreamListener<String, MapRecord<String, String, String>> {
    @Autowired
    SshOrTelnetService sshOrTelnetService;
    @Autowired
    private SchedulerManager schedulerManager;
    @Value(value = "${gatewayCode}")
    private String gatewayCode;

    @Override
    @Async("stream-core-pool")
    @RedisMessageExtend(stream = Streams.SSH_TELNET_TIMED_EXECUTE_ENABLE)
    public void onMessage(MapRecord<String, String, String> message) {
        final Map<String, String> value = message.getValue();
        JSONObject dataObject = AnalyzeContent.getContent(value);

        try {
            if (dataObject != null) {
                if (StringUtils.isNotEmpty(dataObject.getString("taskId")) && StringUtils.isNotEmpty(dataObject.getString("executeCron")) && dataObject.getJSONObject("paramObject") != null) {
                    log.debug(" SSH_TELNET设备配置定时备份任务开启 -- TaskDataJson：" + dataObject.toJSONString());

                    JSONObject paramObject = dataObject.getJSONObject("paramObject");
                    JSONArray deviceArray = paramObject.getJSONArray("deviceArray").stream().filter(o -> ((JSONObject) o).getString("gatewayCode").equals(gatewayCode)).collect(Collectors.toCollection(JSONArray::new));
                    if (CollUtil.isNotEmpty(deviceArray)) {
                        Device device = new Device();
                        device.setId(dataObject.getString("taskId"));
                        device.setKey(dataObject.getString("taskName"));
                        paramObject.put("deviceArray", deviceArray);
                        device.setParamObject(paramObject);

                        Collector collector = (Collector) Class.forName("com.yuanqiao.insight.collection.collector.collector.impl.sshOrTelnet.SshOrTelnetCollector_Common").newInstance();
                        collector.init(device, null);
                        schedulerManager.addTask(dataObject.getString("taskId"), dataObject.getString("executeCron"), collector);
                    }
                }
            }
        } catch (Exception e) {
            log.error("设备配置定时备份任务开启异常！", e);
        }

    }

}

