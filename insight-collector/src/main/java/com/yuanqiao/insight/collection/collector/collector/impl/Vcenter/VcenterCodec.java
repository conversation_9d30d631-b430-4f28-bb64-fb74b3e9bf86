package com.yuanqiao.insight.collection.collector.collector.impl.Vcenter;

import com.alibaba.fastjson.JSONObject;
import com.vmware.vim25.AboutInfo;
import com.yuanqiao.insight.collection.collector.utils.DataBaseCodecUtils;
import com.yuanqiao.insight.common.util.vcenter.HostInfo;
import com.yuanqiao.insight.common.util.vcenter.VcenterTree;
import com.yuanqiao.insight.common.util.vm.VmInfo;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class VcenterCodec {
    DataBaseCodecUtils dataBaseCodecUtils = new DataBaseCodecUtils();
    //最外层jsonObject
    JSONObject jsonObject = new JSONObject();
    //公共结果集
    JSONObject commonMap = new JSONObject();

    public JSONObject dataCodecObject(List<ProertyMetadata> metadataList, String stcaKey, AboutInfo aboutInfo, List<HostInfo> hostInfoList, List<VmInfo> vmInfoList, List<VcenterTree> deviceVcenterList) {
        //清空外层jsonObject和公共结果集commonMap
        jsonObject.clear();
        commonMap.clear();

        //基本信息
        HashMap<String, Object> infoMap = new HashMap<>();
        infoMap.put("name", aboutInfo.getName());//名称
        infoMap.put("fullName", aboutInfo.getFullName());//全名
        infoMap.put("version", aboutInfo.getVersion());//版本
        infoMap.put("uuid", aboutInfo.getInstanceUuid());//uuid
        infoMap.put("osType", aboutInfo.getOsType());//操作系统
        infoMap.put("licenseProductName", aboutInfo.getLicenseProductName());//证书名字
        infoMap.put("licenseProductVersion", aboutInfo.getLicenseProductVersion());//证书版本
        infoMap.put("vendor", aboutInfo.getVendor());//厂商
        commonMap.put("staticInfo", infoMap);
        double cpuHostRate = 0.0;
        double memHostRate = 0.0;
        double diskHostRate = 0.0;
        if (!CollectionUtils.isEmpty(hostInfoList)) {
            for (HostInfo hostInfo : hostInfoList) {
                double cpuUsage = hostInfo.getCpuUsage();
                double memUsage = hostInfo.getMemUsage();
                cpuHostRate += cpuUsage;
                memHostRate += memUsage;
            }
            cpuHostRate = cpuHostRate / hostInfoList.size();
            memHostRate = memHostRate / hostInfoList.size();
        }
        commonMap.put("cpuRate", Double.parseDouble(String.format("%.2f", cpuHostRate)));
        commonMap.put("memRate", Double.parseDouble(String.format("%.2f", memHostRate)));
        //主机信息
        List<Map<String, Object>> hostInfoMap = new ArrayList<>();
        for (HostInfo hostInfo : hostInfoList) {
            Map<String, Object> res = new HashMap<>();
            res.put("name", hostInfo.getName());
            res.put("power", hostInfo.getPowerState());
            res.put("cpuModel", hostInfo.getCpuModel());
            res.put("osType", hostInfo.getOsType());
            res.put("cpuCores", hostInfo.getCpuCores());
            res.put("cpuUsage", hostInfo.getCpuUsage());
            res.put("memUsage", hostInfo.getMemUsage());
            res.put("runTime", hostInfo.getRunTime());
            hostInfoMap.add(res);
        }
        commonMap.put("hostInfo", hostInfoMap);
        //虚拟机信息
        List<Map<String, Object>> vmInfoMap = new ArrayList<>();
        for (VmInfo vmInfo : vmInfoList) {
            Map<String, Object> res = new HashMap<>();
            res.put("name", vmInfo.getName());
            res.put("ip", vmInfo.getIpAddress());
            res.put("power", vmInfo.getPowerState());
            res.put("cpuUsage", vmInfo.getCpuUsage());
            res.put("memUsage", vmInfo.getMemUsage());
            res.put("runTime", vmInfo.getRunTime());
            vmInfoMap.add(res);
        }
        commonMap.put("vmInfo", vmInfoMap);

        List<Map<String, Object>> deviceNumMap = new ArrayList<>();
        Map<String, Object> res = new HashMap<>();
        int datacenterNum = 0;
        int clusterNum = 0;
        for (VcenterTree deviceTree0 : deviceVcenterList) {
            if (deviceTree0.getType().equals("dataCenter")) {
                datacenterNum++;
            } else if (deviceTree0.getType().equals("cluster")) {
                clusterNum++;
            }
        }
        res.put("hostNum", hostInfoList.size());
        res.put("vmNum", vmInfoList.size());
        res.put("datacenterNum", datacenterNum);
        res.put("clusterNum", clusterNum);
        deviceNumMap.add(res);
        commonMap.put("deviceNum", deviceNumMap);
        metadataList.forEach(item -> {
            if (StringUtils.isNotEmpty(item.getDataType())) {
                jsonObject = dataBaseCodecUtils.switchByDataType(item, stcaKey, jsonObject, commonMap);
            }
        });
        return jsonObject;
    }
}
