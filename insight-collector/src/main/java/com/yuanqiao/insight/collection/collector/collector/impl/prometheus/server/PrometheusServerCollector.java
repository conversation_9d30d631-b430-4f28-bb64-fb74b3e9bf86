package com.yuanqiao.insight.collection.collector.collector.impl.prometheus.server;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.collector.Collector;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.acore.scheduler.SchedulerManagerInter;
import com.yuanqiao.insight.acore.utils.SpringContextUtil;
import com.yuanqiao.insight.collection.collector.collector.impl.prometheus.PrometheusQueryService;
import com.yuanqiao.insight.service.collector.utils.DataPersistenceStorageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Zabbix数据拉取
 */
@Slf4j
@EnableAsync
public class PrometheusServerCollector implements Collector {
    private Device device;
    private SchedulerManagerInter schedulerManager;
    private DataPersistenceStorageUtils storageUtils;
    private PrometheusServerCodec prometheusServerCodec;



    //初始化
    @Override
    public void init(Device device, SchedulerManagerInter schedulerManager) {
        this.device = device;
        this.schedulerManager = schedulerManager;
        this.storageUtils = (DataPersistenceStorageUtils) SpringContextUtil.getBean("dataPersistenceStorageUtils");
        this.prometheusServerCodec = (PrometheusServerCodec) SpringContextUtil.getBean("prometheusServerCodec");
    }


    //执行方法
    @Async
    @Override
    public void execute() {
        log.debug("-------- Prometheus Server设备 " + device.getKey() + " 监控目标拉取任务开始执行...");
        //初始化jsonObject
        SimpleDateFormat time_sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timestampForma = time_sdf.format(new Date());

        String serverUrl = device.getConnectParam().get("ip") + ":" + device.getConnectParam().get("port");

        //连接Server，发送HTTP请求
        JSONObject resultObject = PrometheusQueryService.queryAllTargets(serverUrl);
        if (resultObject != null) {
            //设备上线、心跳持久化到时序库
            log.debug("设备 " + device.getKey() + " 连接成功，放入静态队列容器...");
            storageUtils.heartBeatMessageHandler(schedulerManager, device, timestampForma);

            log.debug("设备 " + device.getKey() + " 解析出的数据 ：" + resultObject);

            JSONArray targetArray = PrometheusQueryService.analysisTargets(resultObject);
            if (!targetArray.isEmpty()) {
                prometheusServerCodec.dataCodec(targetArray, device, serverUrl);
            }
        } else {
            log.error("Prometheus Server设备监控目标拉取失败！");
        }
        log.debug("********** END ********** " + "Prometheus Server设备 " + device.getKey() + " 监控目标拉取任务执行完毕！！！");

    }

}
