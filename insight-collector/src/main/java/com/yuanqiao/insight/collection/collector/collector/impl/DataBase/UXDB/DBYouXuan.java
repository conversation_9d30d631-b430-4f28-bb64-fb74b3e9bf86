package com.yuanqiao.insight.collection.collector.collector.impl.DataBase.UXDB;

import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * 数据操作库处理工具类
 *
 * <AUTHOR>
 *
 */
@Slf4j
public class DBYouXuan {


	/**
	 * 获取版本信息
	 *
	 * @param conn
	 * @return
	 * @throws SQLException
	 */
	public static String getVersion(Connection conn) throws SQLException {
		final String query ="select version()";
		// 获取数据库操作对象
		PreparedStatement stmt = null;
		ResultSet rs = null;
		String str = "";
		String behind ="";
		try {
			stmt = conn.prepareStatement(query);
			rs = stmt.executeQuery();
			while (rs.next()) {
				str = rs.getString("version");
			}

		} catch (SQLException e) {
			e.printStackTrace();
			log.error("==============优炫数据库获取版本号出错==============");
			throw e;
		} finally {
			try {
				close(conn, stmt, rs);
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return str;

	}

	/**
	 * 数据库名称
	 *
	 * @param conn
	 * @return
	 * @throws SQLException
	 */
	public static String getName(Connection conn) throws SQLException {
		final String query ="select current_catalog,current_database() as dbName";
		// 获取数据库操作对象
		PreparedStatement stmt = null;
		ResultSet rs = null;
		String str = "";
		try {
			stmt = conn.prepareStatement(query);
			rs = stmt.executeQuery();
			while (rs.next()) {
				str = rs.getString("dbName");
			}
		} catch (SQLException e) {
			e.printStackTrace();
			log.error("==============优炫数据库获取数据库名称出错==============");
		} finally {
			try {
				close(conn, stmt, rs);
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return str;
	}

	/**
	 * 数据库当前连接数
	 *
	 * @param conn
	 * @return
	 * @throws SQLException
	 */
	public static Map<String,String> getNowConn(Connection conn) throws SQLException {
		final String query ="select max_conn, now_conn, max_conn-now_conn rest_conn from (select setting::int8 as max_conn,(select count(*) from ux_stat_activity) as now_conn from ux_settings where name = 'max_connections') t;";
		// 获取数据库操作对象
		PreparedStatement stmt = null;
		ResultSet rs = null;
		String str = "";
		List<Map<String, Object>> userList = new ArrayList<Map<String, Object>>();
		Map<String, String> res = new HashMap<String, String>();
		try {
			stmt = conn.prepareStatement(query);
			rs = stmt.executeQuery();
			while (rs.next()) {
				str = rs.getString("now_conn");
				res.put("nowConn",str);
				str = rs.getString("max_conn");
				res.put("maxConn",str);
				str = rs.getString("rest_conn");
				res.put("restConn",str);
			}
		} catch (SQLException e) {
			e.printStackTrace();
			log.error("==============优炫数据库获取数据库当前连接数出错==============");
		} finally {
			try {
				close(conn, stmt, rs);
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return res;
	}


	/**
	 * 查看数据库是否主库
	 *
	 * @param conn
	 * @return
	 */
	public static String getIsRecovery(Connection conn) {
		final String query ="select ux_is_in_recovery()";
		PreparedStatement stmt = null;
		ResultSet rs = null;
		String str = "";
		try {
			stmt = conn.prepareStatement(query);
			rs = stmt.executeQuery();
			while (rs.next()) {
				str = rs.getString("ux_is_in_recovery");

			}
			if (str.equals("f")){
				str = "是";
			}else {
				str = "否";
			}
		} catch (SQLException e) {
			e.printStackTrace();
			log.error("==============优炫数据库获取数据库是否主库出错==============");
		} finally {
			try {
				close(conn, stmt, rs);
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return str;
	}

	/**
	 * 查看数据库启动时间
	 *
	 * @param conn
	 * @return
	 */
	public static String getStartTime(Connection conn)  {
		final String query ="select pg_postmaster_start_time()";
		PreparedStatement stmt = null;
		ResultSet rs = null;
		String str = "";
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		try {
			stmt = conn.prepareStatement(query);
			rs = stmt.executeQuery();
			while (rs.next()) {
				str = rs.getString("ux_uxmaster_start_time");
				try {
					Date parse = format.parse(str);
					str = format.format(parse);
				} catch (ParseException e) {
					e.printStackTrace();
				}
			}
		} catch (SQLException e) {
			log.error("==============优炫数据库获取数据库启动时间出错==============");
			e.printStackTrace();
		} finally {
			try {
				close(conn, stmt, rs);
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return str;
	}
	/**
	 * 有阻塞的会话数量
	 *
	 * @param conn
	 * @return
	 */
	public static String getblockCount(Connection conn) {
		final String query ="select count(*) as blockCount from ux_catalog.ux_locks bl where NOT bl.granted";
		// 获取数据库操作对象
		PreparedStatement stmt = null;
		ResultSet rs = null;
		String str = "";
		try {
			stmt = conn.prepareStatement(query);
			rs = stmt.executeQuery();
			while (rs.next()) {
				str = rs.getString("blockCount");
			}
		} catch (SQLException e) {
			e.printStackTrace();
			log.error("==============优炫数据库获取数据库阻塞的会话数量出错==============");
		} finally {
			try {
				close(conn, stmt, rs);
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return str;
	}

	/**
	 * 获去表空间信息
	 *
	 * @param conn
	 * @return
	 */
	public static List<Map<String, Object>> getTableInfo(Connection conn) {
		final String query ="select spcname,ux_tablespace_location(oid),round(ux_tablespace_size(oid)/1024/1024/1024.0,2)||'GB' as tableSize from ux_tablespace order by ux_tablespace_size(oid) desc";
		List<Map<String, Object>> dbList = new ArrayList<Map<String, Object>>();
		PreparedStatement stmt = null;
		ResultSet rs = null;
		try {
			stmt = conn.prepareStatement(query);
			rs = stmt.executeQuery();
			while (rs.next()) {
				Map<String, Object> res = new HashMap<String, Object>();
				res.put("spcName", rs.getString("spcname"));
				res.put("tableSize", rs.getString("tableSize"));
				dbList.add(res);
			}
		} catch (SQLException e) {
			e.printStackTrace();
			log.error("==============优炫数据库获取数据库表空间信息出错==============");
		} finally {
			try {
				close(conn, stmt, rs);
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return dbList;
	}

	/**
	 * cpu top10
	 * type 1:cpuTop10;2:单次调用top10;
	 * @param conn
	 * @return
	 */
	public static List<Map<String, Object>> getTop10(Connection conn,Integer type) {

		String sql = "";
		List<Map<String, Object>> dbList = new ArrayList<Map<String, Object>>();
		PreparedStatement stmt = null;
		ResultSet rs = null;
		switch (type){
			case 1:
				sql = "select t1.datname, t2.query, t2.calls, t2.total_time, t2.total_time/t2.calls as avg_time from ux_database t1, ux_stat_statements t2 where t1.oid=t2.dbid order by t2.total_time desc limit 10";
				break;
			case 2:
				sql = "select t1.datname, t2.query, t2.calls, t2.total_time, t2.total_time/t2.calls as avg_time from ux_database t1, ux_stat_statements t2 where t1.oid=t2.dbid order by t2.calls desc limit 10";
				break;
		}
		try {
			stmt = conn.prepareStatement(sql);
			rs = stmt.executeQuery();

			while (rs.next()) {
				Map<String, Object> res = new HashMap<String, Object>();
				res.put("datename", rs.getString("datname"));
				//总耗时长
				double totalTime = rs.getDouble("total_time");
				res.put("totalTime", Double.parseDouble(String.format("%.2f",totalTime)));
				//执行次数
				res.put("calls", rs.getString("calls"));
				// 平均耗时
				double avgTime = rs.getDouble("avg_time");
				res.put("avgTime", Double.parseDouble(String.format("%.2f",avgTime)));
				// SQL语句
				res.put("query", rs.getString("query"));

				dbList.add(res);
			}

		} catch (SQLException e) {
			e.printStackTrace();
			log.error("==============优炫数据库获取数据库TOP10出错==============");
		} finally {
			try {
				close(conn, stmt, rs);
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return dbList;
	}


	/**
	 *缓存命中率
	 *
	 * @param conn
	 * @return
	 */
	public static Double getCacheHitRate(Connection conn) {
		final String query ="select blks_hit / (blks_hit + blks_read) cache_hit_rate,datname from ux_stat_database where datname not in ($$template0$$,$$template1$$)";
		PreparedStatement stmt = null;
		ResultSet rs = null;
		Double str = 0.0;
		try {
			stmt = conn.prepareStatement(query);
			rs = stmt.executeQuery();
			while (rs.next()) {
				str = Double.parseDouble(String.format("%.2f", rs.getDouble("cache_hit_rate")));
			}

		}catch (SQLException e) {
			e.printStackTrace();
			log.error("==============优炫数据库获取数据库缓存命中率出错==============");
		} finally {
			try {
				close(conn, stmt, rs);
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return str;
	}

	/**
	 *表占用磁盘空间大小
	 *
	 * @param conn
	 * @return
	 */
	public static Double getTableTatleSize(Connection conn) {
		final String query ="select sum(t.size) from (SELECT table_schema || '.' || table_name AS table_full_name, pg_total_relation_size('\"' || table_schema || '\".\"' || table_name || '\"')AS size FROM information_schema.tables ORDER BY pg_total_relation_size('\"' || table_schema || '\".\"' || table_name || '\"') DESC ) t";
		PreparedStatement stmt = null;
		ResultSet rs = null;
		Double str = 0.0;
		try {
			stmt = conn.prepareStatement(query);
			rs = stmt.executeQuery();
			while (rs.next()) {
				str = Double.parseDouble(String.format("%.2f", rs.getDouble("sum") / 1024 / 1024)) ;
			}

		}catch (SQLException e) {
			e.printStackTrace();
			log.error("==============优炫数据库获取数据库表占用磁盘空间大小出错==============");
		} finally {
			try {
				close(conn, stmt, rs);
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return str;
	}

	/**
	 * 清空Statement、ResultSet
	 *
	 * @param stmt
	 * @param rs
	 * @throws SQLException
	 */
	public static void clearNull(PreparedStatement stmt, ResultSet rs) throws SQLException {
		if (rs != null)
			rs = null;
		if (stmt != null)
			stmt = null;
	}

	/**
	 * 关闭Connection、Statement、ResultSet
	 *
	 * @param conn
	 * @param stmt
	 * @param rs
	 * @throws SQLException
	 */
	public static void close(Connection conn, PreparedStatement stmt, ResultSet rs) throws SQLException {
		if (rs != null) {
			rs.close();
			rs = null;
		}
		if (stmt != null) {
			stmt.close();
			stmt = null;
		}
		if (conn != null) {
			// conn.close();
			// conn = null;
		}
	}


}
