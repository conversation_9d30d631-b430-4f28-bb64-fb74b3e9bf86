package com.yuanqiao.insight.collection.collector.collector.impl.prometheus;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.collection.deviceconnectparamdelete.ConnParamDelete;
import com.yuanqiao.insight.collection.deviceconnparamsave.ConnParamSave;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.mq.RedisMq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class PrometheusQueryService {
    @Autowired
    private ConnParamSave connParamSave;
    @Autowired
    private ConnParamDelete connParamDelete;
    @Autowired
    RedisMq redisMq;
    private static final RestTemplate restTemplate = new RestTemplate();

    public static JSONObject queryMetricsByQl(String serverUrl, String promQL) {
        String url =  "http://" + serverUrl + "/api/v1/query?query={query}";

        //设置参数
        Map<String, String> param = new HashMap();
        param.put("query", promQL);

        try {
            //发起请求
            Map resultMap = restTemplate.getForObject(url, Map.class, param);
            //将map转为json
            JSONObject resultJson = JSON.parseObject(JSONObject.toJSONString(resultMap), JSONObject.class);
            String status = resultJson.getString("status");
            if (!"success".equals(status)) {
                log.error("资源监控请求失败! url:{}, param:{}", url, param);
                return null;
            }
            return resultJson;
        } catch (Exception e) {
            log.error("资源监控请求失败! url:{}, param:{}, error:{}", url, param, e);
            return null;
        }
    }

    public static JSONObject queryMetricsByInstance(String serverUrl, String instance) {
        String url =  "http://" + serverUrl + "/api/v1/query?query={query}";

        //设置参数
        Map<String, String> param = new HashMap();
        String query = "{instance='" + instance + "'}";
        param.put("query", query);

        try {
            //发起请求
            Map resultMap = restTemplate.getForObject(url, Map.class, param);
            //将map转为json
            JSONObject resultJson = JSON.parseObject(JSONObject.toJSONString(resultMap), JSONObject.class);
            String status = resultJson.getString("status");
            if (!"success".equals(status)) {
                log.error("资源监控请求失败! url:{}, param:{}", url, param);
                return null;
            }
            return resultJson;
        } catch (Exception e) {
            log.error("资源监控请求失败! url:{}, param:{}, error:{}", url, param, e);
            return null;
        }
    }

    public static JSONObject queryAllTargets(String serverUrl) {
        String url =  "http://" + serverUrl + "/api/v1/targets";

        try {
            //发起请求
            Map resultMap = restTemplate.getForObject(url, Map.class, new HashMap<>());
            //将map转为json
            JSONObject resultJson = JSON.parseObject(JSONObject.toJSONString(resultMap), JSONObject.class);
            String status = resultJson.getString("status");
            if (!"success".equals(status)) {
                log.error("资源监控请求失败! url:{}", url);
                return null;
            }
            return resultJson;
        } catch (Exception e) {
            log.error("资源监控请求失败! url:{}, error:{}", url, e);
            return null;
        }
    }

    public static Double analysisPromQLResult(JSONObject resultObject){
        try {
            JSONObject resultDataObject = resultObject.getJSONObject("data");
            JSONArray resultArray = resultDataObject.getJSONArray("result");
            JSONObject data = resultArray.getJSONObject(0);
            JSONArray valueArray = data.getJSONArray("value");
            return Double.parseDouble(String.valueOf(valueArray.get(1)));
        } catch (Exception e) {
            return null;
        }
    }

    public static JSONObject analysisMetrics(JSONObject metricObject){
        JSONObject jsonObject = new JSONObject();
        JSONObject metricDataObject = metricObject.getJSONObject("data");
        JSONArray metricArray = metricDataObject.getJSONArray("result");
        for (int i = 0; i < metricArray.size(); i++) {
            JSONObject eleMetric = metricArray.getJSONObject(i).getJSONObject("metric");
            JSONArray eleValues = metricArray.getJSONObject(i).getJSONArray("value");
            String metricName = eleMetric.getString("__name__");
            if (!metricName.startsWith("scrape") && !metricName.startsWith("promhttp") && !metricName.equalsIgnoreCase("up")) {
                eleMetric.put("value", eleValues.get(1));
                if (jsonObject.containsKey(metricName)) {
                    jsonObject.getJSONArray(metricName).add(eleMetric);
                } else {
                    JSONArray array = new JSONArray();
                    array.add(eleMetric);
                    jsonObject.put(metricName, array);
                }
            }
        }
        return jsonObject;
    }

    public static JSONArray analysisTargets(JSONObject targetObject){
        JSONArray resultArray = new JSONArray();
        JSONObject targetDataObject = targetObject.getJSONObject("data");
        JSONArray targetArray = targetDataObject.getJSONArray("activeTargets");
        for (int i = 0; i < targetArray.size(); i++) {
            JSONObject eleTarget = targetArray.getJSONObject(i);
            JSONObject eleObject = new JSONObject();
            eleObject.put("health", eleTarget.getString("health"));
            JSONObject labels = eleTarget.getJSONObject("labels");
            eleObject.put("instance", labels.getString("instance"));
            eleObject.put("job", labels.getString("job"));
            resultArray.add(eleObject);
        }
        return resultArray;
    }

    // 发布连接参数新增事件
    public void publishConnectSaveEvent(List<String> settingIds) {
        if (CollUtil.isNotEmpty(settingIds)) {
            connParamSave.reLoadJob(null, settingIds);
        }
    }

    // 发布连接参数删除事件
    public void publishDeviceDisableEvent(List<String> settingIds) {
        connParamDelete.reLoadJob(null, settingIds);
    }

    public static void main(String[] args) {
//        analysisTargets(Objects.requireNonNull(queryAllTargets("192.168.16.41:9090")));
        System.out.println();
        System.out.println();
        System.out.println();
        System.out.println(analysisMetrics(Objects.requireNonNull(queryMetricsByInstance("192.168.16.41:9090", "192.168.16.41:9100"))));
//        System.out.println(analysisPromQLResult(queryMetricsByQl("192.168.16.41:9090", "(1 - avg(irate(node_cpu_seconds_total{instance=\"192.168.16.41:9100\",mode=\"idle\"}[5m])))*100")));
    }

}