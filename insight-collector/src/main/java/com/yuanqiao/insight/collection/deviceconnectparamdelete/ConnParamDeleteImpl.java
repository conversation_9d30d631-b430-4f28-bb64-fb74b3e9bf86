package com.yuanqiao.insight.collection.deviceconnectparamdelete;

import cn.hutool.core.collection.CollUtil;
import com.yuanqiao.insight.collection.collector.scheduler.SchedulerManager;
import com.yuanqiao.insight.service.ScheduleSetting.entity.ScheduleSetting;
import com.yuanqiao.insight.service.ScheduleSetting.mapper.ScheduleSettingMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class ConnParamDeleteImpl implements ConnParamDelete {

    @Autowired
    SchedulerManager schedulerManager;
    @Autowired
    ScheduleSettingMapper scheduleSettingMapper;

    @Override
    public void reLoadJob(List<String> keyList, List<String> settingIdList) {
        log.info("开始处理连接参数删除、设备禁用事件...");

        // 根据设备标识撤销定时任务
        if (CollUtil.isNotEmpty(keyList)) {
            keyList.forEach(item -> {
                List<ScheduleSetting> oldScheduleSettings = scheduleSettingMapper.selectByDeviceCode(item);
                if (oldScheduleSettings != null && !oldScheduleSettings.isEmpty()) {
                    oldScheduleSettings.forEach(oldScheduleSetting -> {
                        schedulerManager.removeTask(oldScheduleSetting.getId());
                    });
                }
            });
        }
        // 根据任务ID撤销定时任务
        if (CollUtil.isNotEmpty(settingIdList)){
            settingIdList.forEach(item -> {
                schedulerManager.removeTask(item);
            });
        }

    }
}
