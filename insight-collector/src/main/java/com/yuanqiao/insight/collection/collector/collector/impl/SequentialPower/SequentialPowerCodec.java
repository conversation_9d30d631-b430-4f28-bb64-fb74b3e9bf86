package com.yuanqiao.insight.collection.collector.collector.impl.SequentialPower;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.acore.utils.SpringContextUtil;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;

import java.io.*;
import java.net.InetAddress;
import java.net.Socket;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class SequentialPowerCodec {

    //最外层jsonObject
    JSONObject jsonObject = new JSONObject();
    //公共结果集
    Map<String, Object> commonMap = new HashMap<>();

    public JSONObject dataCodecObject(RedisTemplate redisTemplate, Device device, List<ProertyMetadata> metadataList, String stcaKey) {
        //清空外层jsonObject和公共结果集commonMap
        jsonObject.clear();
        commonMap.clear();


        //循环遍历当前设备的物模型
        metadataList.forEach(item -> {
            // IP*_SYS  ==> IP109_SYS
            if (StringUtils.isNotEmpty(item.getOid()) && StringUtils.isNotEmpty(device.getConnectParam().get("ip"))) {
                String[] ips = device.getConnectParam().get("ip").split("\\.");
                String command = item.getOid().replace("*", ips[3]);
                log.info("*****> command：" + command);
                String respondStr = "";
                try {
                    respondStr = TCP_Client(device.getConnectParam().get("ip"), Integer.parseInt(device.getConnectParam().get("port")), command);
                } catch (IOException e) {
                    log.error("时序电源监控解码 -- TCP_Client() 异常！",e);
                }
                log.info("=====> respondStr：" + respondStr);
                if (StringUtils.isNotEmpty(respondStr)){
                    String judgeStr = command + "_ON";
                    if (respondStr.equals(judgeStr)){
                        commonMap.put("powerStatus","开启");
                    } else {
                        commonMap.put("powerStatus", "关闭");
                    }
                } else {
                    commonMap.put("powerStatus", "关闭");
                }
            } else {
                commonMap.put("powerStatus", "关闭");
            }
        });

        metadataList.forEach(metadata -> {
            if (StringUtils.isNotEmpty(metadata.getDataType()) && metadata.getDataType().equals("text")) {
                jsonObject = getTextCodec(metadata, stcaKey);
            }
        });

        return jsonObject;
    }

    /* ---------------------------------------------------------------------------------------------------------------- */

    private JSONObject getTextCodec(ProertyMetadata item, String stcaKey) {
        JSONObject jsonObject1 = new JSONObject();
        jsonObject1.put("name", item.getName());
        jsonObject1.put("type", item.getDataType());
        jsonObject1.put("unit", item.getUnit());
        if (commonMap.get(item.getCode()) != null) {
            jsonObject1.put("value", commonMap.get(item.getCode()));
        } else {
            jsonObject1.put("value", "关闭");
        }
        String chart = item.getChart() + "";
        jsonObject1.put("display", makeLineDisplay(stcaKey, item.getCode(), jsonObject1.get("value"), chart));

        jsonObject.put(stcaKey + "_" + item.getCode(), jsonObject1);
        return jsonObject;
    }

    public JSONArray makeLineDisplay(String stcaKey, String code, Object value, String chart) {
        RedisTemplate redisTemplate = (RedisTemplate) SpringContextUtil.getBean("redisTemplate");
        JSONObject dataObject = JSONObject.parseObject(String.valueOf(redisTemplate.opsForValue().get(stcaKey + "_" + code)));

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (dataObject != null && !dataObject.isEmpty()) {
            return setLineDisplay(value, dataObject, chart);
        } else {
            JSONArray jsonArray = new JSONArray();
            JSONObject map1 = new JSONObject();
            map1.put("timestamp", sdf.format(System.currentTimeMillis()));
            map1.put("value", value);
            map1.put("type", chart);
            jsonArray.add(map1);
            return jsonArray;
        }

    }

    private JSONArray setLineDisplay(Object value, JSONObject jsonObject, String chart) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            JSONArray display = jsonObject.getJSONArray("display");
            JSONObject map1 = new JSONObject();
            if (display.size() >= 15) {
                display.remove(0);
                map1.put("timestamp", sdf.format(System.currentTimeMillis()));
                map1.put("value", value);
                map1.put("type", chart);
                display.add(map1);
            } else {
                map1.put("timestamp", sdf.format(System.currentTimeMillis()));
                map1.put("value", value);
                map1.put("type", chart);
                display.add(map1);
            }
            return display;
        } catch (Exception e) {
            JSONArray jsonArray = new JSONArray();
            JSONObject map1 = new JSONObject();
            map1.put("timestamp", sdf.format(System.currentTimeMillis()));
            map1.put("value", value);
            map1.put("type", chart);
            jsonArray.add(map1);
            return jsonArray;
        }

    }

    /* ---------------------------------------------------------------------------------------------------------------- */
    //TCP客户端（统一抛异常）
    private static String TCP_Client(String ip, int port, String command) throws IOException {

        String info = "";

        BufferedReader bufferedReader = null;
        InputStreamReader inputStreamReader = null;
        InputStream inputStream = null;
        PrintWriter printWriter = null;
        OutputStream outputStream = null;
        Socket socket = null;
        try {
            //创建Socket对象
            socket = new Socket(InetAddress.getByName(ip), port);

            //根据输入输出流和服务端连接
            outputStream = socket.getOutputStream();//获取一个输出流，向服务端发送信息
            printWriter = new PrintWriter(outputStream);//将输出流包装成打印流
            log.info("客户端发送的请求：" + command);
            printWriter.print(command);
            printWriter.flush();
            socket.shutdownOutput();//关闭输出流

            inputStream = socket.getInputStream();//获取一个输入流，接收服务端的信息
            inputStreamReader = new InputStreamReader(inputStream);//包装成字符流，提高效率
            bufferedReader = new BufferedReader(inputStreamReader);//缓冲区
            String temp = null;
            while ((temp = bufferedReader.readLine()) != null) {
                info += temp;
            }
            log.info("来自服务端的响应信息：" + info);

        } catch (Exception e) {
            log.error(e + "");
            return "";
        }finally {
            //关闭资源
            if (bufferedReader != null) {
                bufferedReader.close();
            }
            if (inputStreamReader != null) {
                inputStreamReader.close();
            }
            if (inputStream != null) {
                inputStream.close();
            }
            if (printWriter != null) {
                printWriter.close();
            }
            if (outputStream != null) {
                outputStream.close();
            }
            if (socket != null) {
                socket.close();
            }
        }
        return info;
    }
    /* ---------------------------------------------------------------------------------------------------------------- */


}
