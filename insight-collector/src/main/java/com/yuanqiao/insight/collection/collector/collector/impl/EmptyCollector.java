package com.yuanqiao.insight.collection.collector.collector.impl;

import com.yuanqiao.insight.acore.collector.Collector;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.acore.scheduler.SchedulerManagerInter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class EmptyCollector implements Collector {

    @Override
    public void init(Device device, SchedulerManagerInter schedulerManager) {

    }

    @Override
    public void destroy() {

    }

    @Override
    public void execute() {

    }
}
