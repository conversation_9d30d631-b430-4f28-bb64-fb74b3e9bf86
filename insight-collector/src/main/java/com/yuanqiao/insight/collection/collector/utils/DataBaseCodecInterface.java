package com.yuanqiao.insight.collection.collector.utils;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import lombok.Getter;
import lombok.Setter;

import java.sql.Connection;
import java.util.List;


@Getter
@Setter
public abstract class DataBaseCodecInterface {

    private JSONObject extendData;

    /**
     * 定义数据库拉模式采集解码器
     * @param connection
     * @param metadataList 物模型集合
     * @param stcaKey 状态容器key
     * @return
     */
    public abstract JSONObject dataCodec(Connection connection, List<ProertyMetadata> metadataList, String stcaKey);
}
