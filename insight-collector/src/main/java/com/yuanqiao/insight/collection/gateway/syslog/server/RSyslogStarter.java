package com.yuanqiao.insight.collection.gateway.syslog.server;

import com.yuanqiao.insight.common.constant.enums.Protocol;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @date 2023/9/13
 */
@Slf4j
public class RSyslogStarter {

    public static final Map<String, RSyslog> logMap = new HashMap<>();
    private final static ExecutorService pools = Executors.newCachedThreadPool();

    public static void start(int port, Protocol protocol) {
        RSyslog rSyslog = logMap.get(protocol.getValue());
        if (rSyslog == null) {
            rSyslog = new RSyslog(port, protocol);
            logMap.put(protocol.getValue(), rSyslog);
        }
        rSyslog.start();
    }

    public static void stop(Protocol protocol) {
        final RSyslog rSyslog = logMap.get(protocol.getValue());
        if (rSyslog != null) {
            rSyslog.getServerIF().shutdown();
        }
    }

    public static long getSpeed(Protocol protocol) {
        RSyslog rSyslog = logMap.get(protocol.getValue());
        if (rSyslog != null) {
            return rSyslog.getSpeed();
        }
        return 0;
    }
}
