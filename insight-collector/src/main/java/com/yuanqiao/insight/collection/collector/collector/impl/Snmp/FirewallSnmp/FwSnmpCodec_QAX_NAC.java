package com.yuanqiao.insight.collection.collector.collector.impl.Snmp.FirewallSnmp;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.collection.collector.utils.SNMPCodecAndCollectUtils;
import com.yuanqiao.insight.collection.collector.utils.SNMPCodecInterface;
import com.yuanqiao.insight.common.util.common.TimeUtils;
import com.yuanqiao.insight.common.util.snmp.SNMPUtils;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigInteger;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class FwSnmpCodec_QAX_NAC implements SNMPCodecInterface {
    // 操作系统类型
    String osType;

    public void setOsType(String osType) {
        this.osType = osType;
    }

    //最外层jsonObject
    JSONObject jsonObject = new JSONObject();
    //公共结果集
    JSONObject commonMap = new JSONObject();

    /**
     * 定义SNMP拉模式采集解码器
     * @param snmpUtils
     * @param metadataList             物模型集合
     * @param stcaKey                  状态容器key
     * @param device
     * @param snmpCodecAndCollectUtils
     * @return
     */
    @Override
    public JSONObject dataCodec(SNMPUtils snmpUtils, List<ProertyMetadata> metadataList, String stcaKey, Device device, SNMPCodecAndCollectUtils snmpCodecAndCollectUtils) {
        //清空外层jsonObject和公共结果集commonMap
        jsonObject.clear();
        commonMap.clear();

        //静态属性
        HashMap<String, Object> staticInfoMap = new HashMap<>();

        staticInfoMap.put("deviceStart", getDeviceStart(snmpUtils).get("deviceStart"));
        staticInfoMap.put("byStart", getBypass(snmpUtils).get("byStart"));
        commonMap.put("staticInfo", staticInfoMap);
        commonMap.put("sysUpTime", getSysUpTime(snmpUtils).get("sysUpTime"));

        Map<String, Double> rateMap = getRateInfo(snmpUtils);
        commonMap.put("cpuRate", rateMap.get("cpuRate"));
        commonMap.put("memRate", rateMap.get("memRate"));
        commonMap.put("diskRate", rateMap.get("diskRate"));

        commonMap.put("portInfo", getPortInfoList(snmpUtils));

        //循环遍历当前防火墙的物模型
        metadataList.forEach(item -> {
            if (StringUtils.isNotEmpty(item.getDataType())) {
                jsonObject = snmpCodecAndCollectUtils.switchByDataType(item, stcaKey, jsonObject, commonMap, snmpUtils);
            }
        });

        return jsonObject;
    }
    /**
     * 获取设备运行时长
     *
     * @param snmpUtils
     * @return
     */
    private Map<String, String> getSysUpTime(SNMPUtils snmpUtils) {
        HashMap<String, String> map = new HashMap<>();
        try {
            String sysUpTime = snmpUtils.getPDU("1.3.6.1.2.1.1.3.0");
            if (StringUtils.isNotEmpty(sysUpTime) && !sysUpTime.equalsIgnoreCase("noSuchObject")
                    && !sysUpTime.equalsIgnoreCase("noSuchInstance")) {
                // 去除字符串中的逗号
                sysUpTime = sysUpTime.replace(",", "");

                // 使用正则表达式匹配时间信息
                Pattern pattern = Pattern.compile("(?:(\\d+) days?)?(?:\\s*,?\\s*(\\d+):(\\d+)(?::(\\d+(?:\\.\\d+)?))?)?");
                Matcher matcher = pattern.matcher(sysUpTime);

                int days = 0, hours = 0, minutes = 0, seconds = 0;
                double milliseconds = 0;

                if (matcher.find()) {
                    if (matcher.group(1) != null) {
                        days = Integer.parseInt(matcher.group(1));
                    }
                    if (matcher.group(2) != null) {
                        hours = Integer.parseInt(matcher.group(2));
                    }
                    if (matcher.group(3) != null) {
                        minutes = Integer.parseInt(matcher.group(3));
                    }
                    if (matcher.group(4) != null) {
                        String[] secondsParts = matcher.group(4).split("\\.");
                        seconds = Integer.parseInt(secondsParts[0]);
                        if (secondsParts.length > 1) {
                            milliseconds = Double.parseDouble("0." + secondsParts[1]);
                        }
                    }
                }
                // 计算总毫秒数
                BigInteger totalMilliseconds = BigInteger.valueOf(days * 24 * 60 * 60 * 1000L)
                        .add(BigInteger.valueOf(hours * 60 * 60 * 1000L))
                        .add(BigInteger.valueOf(minutes * 60 * 1000L))
                        .add(BigInteger.valueOf((long) (seconds * 1000 + milliseconds * 1000)));
                sysUpTime = TimeUtils.getDistanceTime(totalMilliseconds);
                map.put("sysUpTime", sysUpTime);
            }
        } catch (Exception e) {
            log.error("获取运行时长出错", e);
            map.put("sysUpTime", "");
        }
        return map;
    }
    //获取CPU 内存 磁盘使用列率
    private Map<String, Double> getRateInfo(SNMPUtils snmpUtils) {
        HashMap<String, Double> map = new HashMap<>();
        try {
            String rateInfo = "" + snmpUtils.getPDU(".1.3.6.1.4.1.2021.8.1.101.2");
            log.error("--------------奇安信NAC:资源使用情况---------"+rateInfo);
            //CPU使用率
            try {
                String cpuRate = rateInfo.split("CPU Usage:")[1].trim().split("%")[0];//结果实例  35.3
                map.put("cpuRate", Double.valueOf(cpuRate));
            } catch (Exception e) {
                map.put("cpuRate", 0.0);
                log.error("获取奇安信NACCPU出错", e);
            }
            //内存使用率
            try {
                String memRate = rateInfo.split("Memeory Usage:")[1].trim().split("%")[0];
                map.put("memRate", Double.valueOf(memRate));
            } catch (Exception e) {
                map.put("memRate", 0.0);
                log.error("获取奇安信NAC内存出错", e);
            }
            //硬盘使用率
            try {
                String diskRate = rateInfo.split("Disk Usage:")[1].trim().split("%")[0];
                map.put("diskRate", Double.valueOf(diskRate));
            } catch (Exception e) {
                map.put("diskRate", 0.0);
                log.error("获取奇安信NAC硬盘出错", e);
            }
            return map;
        } catch (Exception e) {
            log.error("获取奇安信LAS内存出错", e);
            map.put("cpuRate", 0.0);
            map.put("diskRate", 0.0);
            map.put("memRate", 0.0);
            return map;
        }

    }

    public static void main(String[] args) {
        String str = "= STRING: CPU Usage: 38.9%  Memory Usage: 56.9%  Disk Usage: 61.2%";
        String s1 = str.split("CPU Usage:")[1].trim().split("%")[0];
        String s2 = str.split("Memory Usage:")[1].trim().split("%")[0];
        String s3= str.split("Disk Usage:")[1].trim().split("%")[0];
        System.out.println("s1="+s1+"       s2="+s2+"         s3="+s3);

        String str1 = "access_bypass: 0  auth_bypass: 0";
        String trim = str1.split("access_bypass:")[1].trim().split("auth_bypass:")[0].trim();
        System.out.println(trim);

    }


    //获取设备状态信息
    private Map<String, String> getDeviceStart(SNMPUtils snmpUtils) {
        HashMap<String, String> map = new HashMap<>();
        try {
            String deviceStart = "" + snmpUtils.getPDU(".1.3.6.1.4.1.2021.8.1.101.4");
            log.error("--------------奇安信NAC设备状态---------"+deviceStart);
            if (!"noSuchObject".equalsIgnoreCase(deviceStart.trim())) {
                if (deviceStart == null || "".equals(deviceStart.trim()) || "null".equalsIgnoreCase(deviceStart.trim())) {
                    map.put("deviceStart", "--");
                } else {
                    map.put("deviceStart", deviceStart);
                }
            } else {
                map.put("deviceStart", "--");
            }
            return map;
        } catch (Exception e) {
            log.error("获取奇安信NAC设备状态出错", e);
            map.put("deviceStart", "--");
            return map;
        }
    }


    //获取bypass状态
    private Map<String, String> getBypass(SNMPUtils snmpUtils) {
        HashMap<String, String> map = new HashMap<>();
        try {
            String byPass = snmpUtils.getPDU(".1.3.6.1.4.1.2021.8.1.101.1");
            log.error("--------------奇安信NAC bypass状态---------"+byPass);
            String trim = byPass.split("access_bypass:")[1].trim().split("auth_bypass:")[0].trim();
            if (trim.equals("0")){
                map.put("byStart","开启");
            }else {
                map.put("byStart","关闭");
            }
            return map;
        } catch (Exception e) {
            log.error("获取奇安信NAC byStart出错", e);
            map.put("byStart", "--");
            return map;
        }
    }


    //获取端口详情
    private List<Map<String, Object>> getPortInfoList(SNMPUtils snmpUtils) {

        try {
            // 接口索引 主键
            Map<String, String> ifKeyMap = snmpUtils.getPDUWalk("1.3.6.1.2.1.2.2.1.1");

            int[] is = new int[ifKeyMap.size()];
            int i = 0;
            for (Map.Entry<String, String> en : ifKeyMap.entrySet()) {
                is[i] = Integer.parseInt(en.getValue());
                i++;
            }
            Arrays.sort(is);

            //端口状态
            Map<String, String> portStatusMap = snmpUtils.getPDUWalk("1.3.6.1.2.1.2.2.1.8");

            //端口描述
            Map<String, String> ifDescrProtosMap = snmpUtils.getPDUWalk("1.3.6.1.2.1.2.2.1.2");

            // 带宽
            Map<String, String> ifSpeedProtosMap = snmpUtils.getPDUWalk("1.3.6.1.2.1.2.2.1.5");

            //端口类型
            Map<String, String> portTypeMap = snmpUtils.getPDUWalk("1.3.6.1.2.1.2.2.1.3");

            //接收错误数据包
            Map<String, String> ifInErrorsMap = snmpUtils.getPDUWalk("1.3.6.1.2.1.2.2.1.14");

            //发送的错误数据包
            Map<String, String> ifOutErrorsProtosMap = snmpUtils.getPDUWalk("1.3.6.1.2.1.2.2.1.20");

            List<Map<String, Object>> portInfoList = new ArrayList<Map<String, Object>>();
            for (int j = 0; j < is.length; j++) {
                Map<String, Object> res = new HashMap<String, Object>();

                //端口类型
                res.put("portType", portTypeMap.get("1.3.6.1.2.1.2.2.1.3." + is[j]));

                //端口状态
                String str = portStatusMap.get("1.3.6.1.2.1.2.2.1.8." + is[j]);
                if (StringUtils.isEmpty(str)) {
                    res.put("portStatus", "其他");
                } else if ("1".equals(str.trim())) {
                    res.put("portStatus", "连接");
                } else if ("2".equals(str.trim())) {
                    res.put("portStatus", "关闭");
                } else {
                    // 3 == testing 表示当前接口不能转发任何运行状态的报文
                    res.put("portStatus", "其他");
                }

                // 带宽
                String bandWidth_string = ifSpeedProtosMap.get("1.3.6.1.2.1.2.2.1.5." + is[j]);
                if (StringUtils.isNotEmpty(bandWidth_string)) {
                    Double bandWidth = Double.parseDouble(bandWidth_string);
                    res.put("bandWidth", String.format("%.2f", bandWidth / 8 / 1024 / 1024));
                }

                //输入/输出流量
                String inSpeed = snmpUtils.getPDU("1.3.6.1.2.1.2.2.1.10." + is[j]);
                if (StringUtils.isNotEmpty(inSpeed)) {
                    Double inputFlow = Double.parseDouble(inSpeed);
                    res.put("inputFlow", String.format("%.2f", inputFlow));
                }
                String outSpeed = snmpUtils.getPDU("1.3.6.1.2.1.2.2.1.16." + is[j]);
                if (StringUtils.isNotEmpty(outSpeed)) {
                    Double outputFlow = Double.parseDouble(outSpeed);
                    res.put("outputFlow", String.format("%.2f", outputFlow));
                }

                //输入/输出错误包数
                String inErrorPackageString = ifInErrorsMap.get("1.3.6.1.2.1.2.2.1.14." + is[j]);
                if (StringUtils.isNotEmpty(inErrorPackageString)) {
                    Double inError = Double.parseDouble(inErrorPackageString);
                    res.put("inErrorPackage", String.format("%.2f", inError));
                }
                String outErrorPackageString = ifOutErrorsProtosMap.get("1.3.6.1.2.1.2.2.1.20." + is[j]);
                if (StringUtils.isNotEmpty(outErrorPackageString)) {
                    Double inError = Double.parseDouble(outErrorPackageString);
                    res.put("outErrorPackage", String.format("%.2f", inError));
                }

                //输入/输出丢失错误包数
                String inLossPackageString = snmpUtils.getPDU("1.3.6.1.2.1.2.2.1.13." + is[j]);
                if (StringUtils.isNotEmpty(inLossPackageString)) {
                    Double inLossPackage = Double.parseDouble(inLossPackageString);
                    res.put("inLossPackage", String.format("%.2f", inLossPackage));
                }
                String outLossPackageString = snmpUtils.getPDU("1.3.6.1.2.1.2.2.1.19." + is[j]);
                if (StringUtils.isNotEmpty(outLossPackageString)) {
                    Double outLossPackage = Double.parseDouble(outLossPackageString);
                    res.put("outLossPackage", String.format("%.2f", outLossPackage));
                }

                //输入单播报文的个数
                String inUcastPktsStr = snmpUtils.getPDU("1.3.6.1.2.1.2.2.1.11." + is[j]);
                if (StringUtils.isNotEmpty(inUcastPktsStr) && !inUcastPktsStr.equals("noSuchInstance")) {
                    Double inUcastPkts = Double.parseDouble(inUcastPktsStr);
                    res.put("inUcastPkts", String.format("%.2f", inUcastPkts));
                }

                //输入非单播报文的个数
                String inNUcastPktsStr = snmpUtils.getPDU("1.3.6.1.2.1.2.2.1.12." + is[j]);
                if (StringUtils.isNotEmpty(inNUcastPktsStr) && !inNUcastPktsStr.equals("noSuchInstance")) {
                    Double inNUcastPkts = Double.parseDouble(inNUcastPktsStr);
                    res.put("inNUcastPkts", String.format("%.2f", inNUcastPkts));
                }

                //输出单播报文的个数
                String outUcastPktsStr = snmpUtils.getPDU("1.3.6.1.2.1.2.2.1.17." + is[j]);
                if (StringUtils.isNotEmpty(outUcastPktsStr) && !outUcastPktsStr.equals("noSuchInstance")) {
                    Double outUcastPkts = Double.parseDouble(outUcastPktsStr);
                    res.put("outUcastPkts", String.format("%.2f", outUcastPkts));
                }

                //输出非单播报文的个数
                String outNUcastPktsStr = snmpUtils.getPDU("1.3.6.1.2.1.2.2.1.18." + is[j]);
                if (StringUtils.isNotEmpty(outNUcastPktsStr) && !outNUcastPktsStr.equals("noSuchInstance")) {
                    Double outNUcastPkts = Double.parseDouble(outNUcastPktsStr);
                    res.put("outNUcastPkts", String.format("%.2f", outNUcastPkts));
                }

                // 接口描述
                res.put("portDesc", ifDescrProtosMap.get("1.3.6.1.2.1.2.2.1.2." + is[j]));

                //索引
                res.put("index", is[j]);

                portInfoList.add(res);
            }
            log.error("--------------奇安信NAC设备端口信息---------"+portInfoList);
            return portInfoList;
        } catch (Exception e) {
            log.error("获取端口信息出错", e);
            return null;
        }
    }
    //获取端口详情
//    private List<Map<String, String>> getPortInfoList(SNMPUtils snmpUtils) {
//
//        try {
//            List<Map<String, String>> portInfoList = new ArrayList<Map<String, String>>();
//            Map<String, String> connNumMap = snmpUtils.getPDUWalk("1.3.6.1.4.1.32328.6.2.3.1.1.1");
//            int connNum = connNumMap.size();
//            for (int i = 0; i < connNum; i++) {
//                Map<String, String> connInfo = new HashMap<String, String>();
//
//                // 接口编号
//                connInfo.put("ifIndex", "" + snmpUtils.getPDU("1.3.6.1.4.1.32328.6.2.3.1.1.1." + i));
//
//                // 接口名称
//                connInfo.put("ifDescr", "" + snmpUtils.getPDU("1.3.6.1.4.1.32328.6.2.3.1.1.2." + i));
//
//                // 可传输的最大网络数据包(接口 MTU )
//                connInfo.put("ifMtu", "" + snmpUtils.getPDU("1.3.6.1.2.1.2.2.1.4." + i));
//
//                //接口类型
//                connInfo.put("ifType", "" + snmpUtils.getPDU("1.3.6.1.2.1.2.2.1.3." + i));
//
//
//                // 当前带宽/接口速率(单位：bit/s，需转为MB/s)
//                connInfo.put("ifSpeed", "" + String.format("%.2f", Double.parseDouble("" + snmpUtils.getPDU("1.3.6.1.2.1.2.2.1.5." + i)) / 8388608));
//
//                // 接口的启用状态(0或1)( enable(1)disable(0) )(未处理格式)
//                connInfo.put("ifStartState", "" + snmpUtils.getPDU("1.3.6.1.2.1.2.2.1.7." + i));
//
//
//                // 当前操作状态(未处理格式)
//                String ifOperStatus = snmpUtils.getPDU("1.3.6.1.2.1.2.2.1.8." + i);
//                connInfo.put("ifOperStatus", ifOperStatus);
//
//
//                // 接受的数据包数
//                String ifInPkts = "" + snmpUtils.getPDU("1.3.6.1.2.1.2.2.1.11." + i);
//                connInfo.put("ifInPkts", ifInPkts);
//
//                // 接收的总字节数(MB)
//                connInfo.put("ifInBytesNum", "" + String.format("%.2f", Double.parseDouble("" + snmpUtils.getPDU("1.3.6.1.2.1.2.2.1.10." + i)) / 1048576));
//
//                // 入站丢弃数据包与总数据包的比值
//                String ifInDiscards = "" + snmpUtils.getPDU("1.3.6.1.2.1.2.2.1.13." + i);
//                connInfo.put("ifInDiscardsRate", "" + String.format("%.2f", Double.parseDouble(ifInDiscards) / Double.parseDouble(ifInPkts)));
//
//                // 入站错误数据包与总数据包的比值
//                String ifInErrors = "" + snmpUtils.getPDU("1.3.6.1.2.1.2.2.1.14." + i);
//                connInfo.put("ifInErrorsRate", "" + String.format("%.2f", Double.parseDouble(ifInErrors) / Double.parseDouble(ifInPkts)));
//
//                // 传出的数据包数
//                String ifOutPkts = "" + snmpUtils.getPDU("1.3.6.1.2.1.2.2.1.17." + i);
//                connInfo.put("ifOutPkts", ifOutPkts);
//
//                // 发送的总字节数(MB)
//                connInfo.put("ifOutBytesNum", "" + String.format("%.2f", Double.parseDouble("" + snmpUtils.getPDU("1.3.6.1.2.1.2.2.1.16." + i)) / 1048576));
//
//                // 出站丢弃数据包与总数据包的比值
//                String ifOutDiscards = "" + snmpUtils.getPDU("1.3.6.1.2.1.2.2.1.19." + i);
//                connInfo.put("ifOutDiscardsRate", "" + String.format("%.2f", Double.parseDouble(ifOutDiscards) / Double.parseDouble(ifOutPkts)));
//
//                // 出站错误数据包与总数据包的比值
//                String ifOutErrors = "" + snmpUtils.getPDU("1.3.6.1.2.1.2.2.1.20." + i);
//                connInfo.put("ifOutErrorsRate", "" + String.format("%.2f", Double.parseDouble(ifOutErrors) / Double.parseDouble(ifOutPkts)));
//
//                // 入站流量速度(单位：bit/s)
//                connInfo.put("ifInSpeed", "" + String.format("%.2f", Double.parseDouble("" + snmpUtils.getPDU("1.3.6.1.4.1.32328.6.2.3.1.1.3." + i))));
//
//                // 出站流量速度(单位：bit/s)
//                connInfo.put("ifOutSpeed", "" + String.format("%.2f", Double.parseDouble("" + snmpUtils.getPDU("1.3.6.1.4.1.32328.6.2.3.1.1.4." + i))));
//
//                portInfoList.add(connInfo);
//            }
//            return portInfoList;
//        } catch (Exception e) {
//            log.error("获取端口信息出错", e);
//            return null;
//        }
//    }
}
