package com.yuanqiao.insight.collection.collector.collector.impl.MiddleWareMbsc;

import com.yuanqiao.insight.acore.collector.Collector;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.acore.scheduler.SchedulerManagerInter;
import com.yuanqiao.insight.acore.utils.SpringContextUtil;
import com.yuanqiao.insight.collection.collector.utils.MiddleWareCodecUtils;
import com.yuanqiao.insight.utils.SNMPMetadataUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jsoup.Connection;
import org.jsoup.Jsoup;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;

@Slf4j
@EnableAsync
public class NginxMbscController implements Collector {
    private Device device;
    private NginxMbscCodec nginxMbscCodec;
    private SchedulerManagerInter schedulerManager;
    private String ip;
    private int port;
    private String url = "";
    private SNMPMetadataUtils metadataUtils;
    private RedisTemplate redisTemplate;
    private MiddleWareCodecUtils middleWareCodecUtils;

    //初始化
    public void init(Device device, SchedulerManagerInter schedulerManager) {
        this.device = device;
        this.nginxMbscCodec = new NginxMbscCodec();
        this.schedulerManager = schedulerManager;
        this.metadataUtils = (SNMPMetadataUtils) SpringContextUtil.getBean("SNMPMetadataUtils");
        this.redisTemplate = (RedisTemplate) SpringContextUtil.getBean("redisTemplate");
        this.middleWareCodecUtils = (MiddleWareCodecUtils) SpringContextUtil.getBean("middleWareCodecUtils");

        ip = device.getConnectParam().get("ip");
        String strPort = device.getConnectParam().get("port").trim();
        if (StringUtils.isEmpty(strPort)) {
            url = "http://" + ip + "/nginx-status";
        } else {
            //这种方式需要配置server添加新的端口
            port = Integer.valueOf(strPort);
            url = "http://" + ip + ":" + port + "/";
        }

    }

    //执行方法
    @Async
    public void execute() {

        Connection conn = null;
        try {
            conn = Jsoup.connect(url);
        } catch (Exception e) {
            log.error("设备 " + device.getKey() + " Nginx连接异常！", e);
        }

        middleWareCodecUtils.collectorMainLine(conn, "Nginx", metadataUtils, device, schedulerManager, redisTemplate, nginxMbscCodec);

    }


}
