package com.yuanqiao.insight.collection.collector.collector.impl.MiddleWareMbsc;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.collection.collector.utils.DataBaseCodecUtils;
import com.yuanqiao.insight.collection.collector.utils.MiddleWareCodecInterface;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.EnableAsync;
import redis.clients.jedis.Jedis;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@EnableAsync
public class RedisCodec implements MiddleWareCodecInterface {
    DataBaseCodecUtils dataBaseCodecUtils = new DataBaseCodecUtils();

    //最外层jsonObject
    JSONObject jsonObject = new JSONObject();
    //公共结果集
    JSONObject commonMap = new JSONObject();

    @Override
    public JSONObject dataCodec(Object connection, List<ProertyMetadata> metadataList, Device device) {
        //清空外层jsonObject和公共结果集commonMap
        jsonObject.clear();
        commonMap.clear();
        try {
            Jedis jedis = (Jedis) connection;
            Map<String, String> mapInfo = this.parseInfo(jedis.info());
            HashMap<String, Object> staticInfoMap = new HashMap<>();
            staticInfoMap.put("version", getVersion(mapInfo));
            staticInfoMap.put("runTime", getTime(mapInfo));
            staticInfoMap.put("os", getOs(mapInfo));
            staticInfoMap.put("tcpPort", getPort(mapInfo));
            staticInfoMap.put("isCluster", getCluster(mapInfo));
            staticInfoMap.put("isAof", getAof(mapInfo));

            commonMap.put("staticInfo", staticInfoMap);
            commonMap.put("connect", getConnect(mapInfo));
            commonMap.put("connectTotalNum", getConnectTotal(mapInfo));
            commonMap.put("hitRate", getHitRate(mapInfo));
            commonMap.put("systemMemory", getSystemMemory(mapInfo));
            commonMap.put("usedMemory", getUsedMemory(mapInfo));
            commonMap.put("usedMemoryMax", getUsedMemoryMax(mapInfo));
            commonMap.put("expireKey", getExpireKey(mapInfo));
            commonMap.put("processed", getProcessed(mapInfo));
            commonMap.put("inSpeed", getNetInOut(mapInfo, jedis).get("inSpeed"));
            commonMap.put("outSpeed", getNetInOut(mapInfo, jedis).get("outSpeed"));

            jedis.close();
            //循环遍历物模型
            metadataList.forEach(item -> {
                if (StringUtils.isNotEmpty(item.getDataType())) {
                    jsonObject = dataBaseCodecUtils.switchByDataType(item, "stca:" + device.getKey(), jsonObject, commonMap);
                }
            });
        } catch (Exception e) {
            log.error("Redis Codec 获取监控数据异常！", e);
        }

        return jsonObject;
    }

    /**
     * @param info
     * @return
     */
    private Map<String, String> parseInfo(String info) {
        Map<String, String> infoMap = new HashMap<>();
        String[] lines = info.split("\n");
        for (String line : lines) {
            if (!line.isEmpty() && !line.startsWith("#")) {
                int colonIndex = line.indexOf(':');
                if (colonIndex > 0) {
                    String key = line.substring(0, colonIndex);
                    String value = line.substring(colonIndex + 1);
                    infoMap.put(key, value);
                }
            }
        }
        return infoMap;
    }

    /**
     * 获取redis版本
     */
    private String getVersion(Map<String, String> mapInfo) {
        String version = "";
        try {
            version = mapInfo.get("redis_version").trim();
        } catch (Exception e) {
            log.error("获取redis版本出错" + e.getMessage());
        }
        return version;
    }

    /**
     * 获取运行时长
     */
    private String getTime(Map<String, String> mapInfo) {
        String time = "";
        try {
            long second = Long.parseLong(mapInfo.get("uptime_in_seconds").trim());
            long milliseconds = second * 1000; // 将秒数转换为毫秒
            long days = TimeUnit.MILLISECONDS.toDays(milliseconds);
            long hours = TimeUnit.MILLISECONDS.toHours(milliseconds) % 24;
            long minutes = TimeUnit.MILLISECONDS.toMinutes(milliseconds) % 60;
            long seconds = TimeUnit.MILLISECONDS.toSeconds(milliseconds) % 60;
            time = days + "天" + hours + "时" + minutes + "分" + seconds + "秒";

        } catch (Exception e) {
            log.error("获取redis运行时长出错" + e.getMessage());
        }
        return time;
    }

    /**
     * 获取操作系统版本
     */
    private String getOs(Map<String, String> mapInfo) {
        String os = "";
        try {
            os = mapInfo.get("os").trim();
        } catch (Exception e) {
            log.error("获取操作系统版本出错" + e.getMessage());
        }
        return os;
    }

    /**
     * 获取操作系统版本
     */
    private String getPort(Map<String, String> mapInfo) {
        String tcpPort = "";
        try {
            tcpPort = mapInfo.get("tcp_port").trim();
        } catch (Exception e) {
            log.error("获取操作系统版本出错" + e.getMessage());
        }
        return tcpPort;
    }

    /**
     * 是否集群
     */
    private String getCluster(Map<String, String> mapInfo) {
        String isCluster = "否";
        try {
            String clu = mapInfo.get("cluster_enabled").trim();
            if (clu.equals("0")) {
                isCluster = "否";
            } else {
                isCluster = "是";
            }
        } catch (Exception e) {
            log.error("获取是否集群出错" + e.getMessage());
        }
        return isCluster;
    }

    /**
     * 客户端连接数
     */
    private String getConnect(Map<String, String> mapInfo) {
        String connectNum = "0";
        try {
            connectNum = mapInfo.get("connected_clients").trim();
        } catch (Exception e) {
            log.error("获取客户端连接数出错" + e.getMessage());
        }
        return connectNum;
    }

    /**
     * 接受连接总数
     */
    private String getConnectTotal(Map<String, String> mapInfo) {
        String connectTotalNum = "0";
        try {
            connectTotalNum = mapInfo.get("total_connections_received").trim();
        } catch (Exception e) {
            log.error("获取接受连接总数出错" + e.getMessage());
        }
        return connectTotalNum;
    }

    /**
     * 命中率
     */
    private double getHitRate(Map<String, String> mapInfo) {
        double hitRate = 0.0;
        try {
            double keyspaceHits = Double.parseDouble(mapInfo.get("keyspace_hits").trim());
            double keyspaceMisses = Double.parseDouble(mapInfo.get("keyspace_misses").trim());
            if (keyspaceMisses + keyspaceMisses > 0) {
                hitRate = keyspaceHits / (keyspaceHits + keyspaceMisses) * 100;
            }
            hitRate = Double.valueOf(String.format("%.2f", hitRate));
        } catch (Exception e) {
            log.error("获取命中率出错" + e.getMessage());
        }
        return hitRate;
    }

    /**
     * 操作系统内存
     */
    private double getSystemMemory(Map<String, String> mapInfo) {
        double systemMemory = 0.0;
        try {
            systemMemory = Double.parseDouble(mapInfo.get("total_system_memory").trim());
        } catch (Exception e) {
            log.error("获取操作系统内存出错" + e.getMessage());
        }
        return systemMemory;
    }

    /**
     * 已使用内存
     */
    private double getUsedMemory(Map<String, String> mapInfo) {
        double usedMemory = 0.0;
        try {
            usedMemory = Double.parseDouble(mapInfo.get("used_memory").trim());
        } catch (Exception e) {
            log.error("获取已使用内存出错" + e.getMessage());
        }
        return usedMemory;
    }

    /**
     * 使用内存最大值
     */
    private double getUsedMemoryMax(Map<String, String> mapInfo) {
        double usedMemoryMax = 0.0;
        try {
            usedMemoryMax = Double.parseDouble(mapInfo.get("used_memory_peak").trim());
        } catch (Exception e) {
            log.error("获取使用内存最大值出错" + e.getMessage());
        }
        return usedMemoryMax;
    }

    /**
     * AOF状态
     */
    private String getAof(Map<String, String> mapInfo) {
        String isAof = "关闭";
        try {
            String clu = mapInfo.get("aof_enabled").trim();
            if (clu.equals("0")) {
                isAof = "关闭";
            } else {
                isAof = "开启";
            }
        } catch (Exception e) {
            log.error("获取AOF状态出错" + e.getMessage());
        }
        return isAof;
    }

    /**
     * 过期的key数
     */
    private String getExpireKey(Map<String, String> mapInfo) {
        String expireKey = "0";
        try {
            expireKey = mapInfo.get("expired_keys").trim();
        } catch (Exception e) {
            log.error("获取过期的key数出错" + e.getMessage());
        }
        return expireKey;
    }

    /**
     * 命令调用次数
     */
    private String getProcessed(Map<String, String> mapInfo) {
        String processed = "0";
        try {
            processed = mapInfo.get("total_commands_processed").trim();
        } catch (Exception e) {
            log.error("获取命令调用次数出错" + e.getMessage());
        }
        return processed;
    }

    /**
     * 网络流量速率
     */
    private Map<String, Double> getNetInOut(Map<String, String> mapInfo, Jedis jedis) {
        Map<String, Double> map = new HashMap<>();
        try {
            long oneBeginTime = System.currentTimeMillis();

            double inBegin = Double.parseDouble(mapInfo.get("total_net_input_bytes").trim());
            double outBegin = Double.parseDouble(mapInfo.get("total_net_output_bytes").trim());
            Thread.sleep(5000);
            Map<String, String> mapInfo1 = this.parseInfo(jedis.info());
            double inAfter = Double.parseDouble(mapInfo1.get("total_net_input_bytes").trim());
            double outAfter = Double.parseDouble(mapInfo1.get("total_net_output_bytes").trim());
            long twoBeginTime = System.currentTimeMillis();
            long timeDiff = (twoBeginTime - oneBeginTime) / 1000;
            double in = (inAfter - inBegin) / timeDiff;
            double out = (outAfter - outBegin) / timeDiff;
            map.put("inSpeed", Double.valueOf(String.format("%.2f", in)));
            map.put("outSpeed", Double.valueOf(String.format("%.2f", out)));
        } catch (Exception e) {
            log.error("获取网络流量速率" + e.getMessage());
        }
        return map;
    }

}
