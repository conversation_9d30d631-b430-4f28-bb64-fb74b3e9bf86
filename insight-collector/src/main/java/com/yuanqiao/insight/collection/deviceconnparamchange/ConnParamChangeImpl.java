package com.yuanqiao.insight.collection.deviceconnparamchange;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.collection.collector.scheduler.SchedulerManager;
import com.yuanqiao.insight.service.ScheduleSetting.entity.ScheduleSetting;
import com.yuanqiao.insight.service.ScheduleSetting.mapper.ScheduleSettingMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

@Slf4j
@Component
public class ConnParamChangeImpl implements ConnParamChange {

    @Autowired
    SchedulerManager schedulerManager;
    @Autowired
    ScheduleSettingMapper scheduleSettingMapper;
    @Value(value = "${gatewayCode}")
    private String gatewayCode;

    @Override
    public void reLoadJob(List<String> oldIds, List<String> newKeys) {
        log.info("开始处理连接参数变更事件...");
        //停掉该设备原有的定时任务
        if (oldIds != null && !oldIds.isEmpty()) {
            oldIds.forEach(oldId -> {
                schedulerManager.removeTask(oldId);
            });
        }
        //组装新的设备信息，重新开启定时任务
        List<ScheduleSetting> newScheduleSettingList = scheduleSettingMapper.selectList(new QueryWrapper<ScheduleSetting>().in("device_code", newKeys).eq("status", 1).eq("gateway_code", gatewayCode));
        schedulerManager.taskInit(newScheduleSettingList);

    }

    private Device initSnmp(ScheduleSetting scheduleSetting, JSONObject conParam) {
        log.debug(" SNMP协议设备 -- item：" + scheduleSetting);
        Device device = new Device();
        if (StringUtils.isNotEmpty(conParam.getString("ip")) && StringUtils.isNotEmpty(conParam.getString("port")) && StringUtils.isNotEmpty(scheduleSetting.getCron())) {

            device.setId(scheduleSetting.getId());
            device.setKey(scheduleSetting.getDeviceCode());
            device.setProtocol(scheduleSetting.getProtocol());
            device.setJobClass(scheduleSetting.getJobClass());

            HashMap<String, String> map = new HashMap<>();

            //V1
            map.put("ip", conParam.getString("ip"));
            map.put("port", conParam.getString("port"));

            if (StringUtils.isNotEmpty(conParam.getString("version"))) {
                map.put("version", conParam.getString("version"));

                //V2
                if (conParam.getString("version").equalsIgnoreCase("V2")) {
                    if (StringUtils.isNotEmpty(conParam.getString("community"))) {
                        map.put("community", conParam.getString("community"));
                    }
                }

                //V3
                if (conParam.getString("version").equalsIgnoreCase("V3")) {
                    map.put("version", conParam.getString("version"));

                    if (StringUtils.isNotEmpty(conParam.getString("username"))) {
                        map.put("username", conParam.getString("username"));
                    }
                    if (StringUtils.isNotEmpty(conParam.getString("snmpAuthLevel"))) {
                        map.put("snmpAuthLevel", conParam.getString("snmpAuthLevel"));
                    }
                    if (StringUtils.isNotEmpty(conParam.getString("sAuth"))) {
                        map.put("sAuth", conParam.getString("sAuth"));
                    }
                    if (StringUtils.isNotEmpty(conParam.getString("sAuth_passwd"))) {
                        map.put("sAuth_passwd", conParam.getString("sAuth_passwd"));
                    }
                    if (StringUtils.isNotEmpty(conParam.getString("spriv"))) {
                        map.put("spriv", conParam.getString("spriv"));
                    }
                    if (StringUtils.isNotEmpty(conParam.getString("spriv_passwd"))) {
                        map.put("spriv_passwd", conParam.getString("spriv_passwd"));
                    }
                }

            }

            device.setConnectParam(map);
        }
        return device;
    }

    private Device initOtherDevice(ScheduleSetting scheduleSetting, JSONObject connectParam) {
        log.debug(" 其他类型协议设备 -- item：" + scheduleSetting);
        Device device = new Device();
        if (StringUtils.isNotEmpty(connectParam.getString("ip")) && StringUtils.isNotEmpty(connectParam.getString("port"))) {

            device.setId(scheduleSetting.getId());
            device.setKey(scheduleSetting.getDeviceCode());
            device.setJobClass(scheduleSetting.getJobClass());
            device.setProtocol(scheduleSetting.getProtocol());

            HashMap<String, String> map = new HashMap<>();
            map.put("ip", String.valueOf(connectParam.get("ip")));
            map.put("port", String.valueOf(connectParam.get("port")));
            if (StringUtils.isNotEmpty(connectParam.getString("username")) && StringUtils.isNotEmpty(connectParam.getString("password"))
                    && StringUtils.isNotEmpty(connectParam.getString("dbname"))) {
                map.put("username", connectParam.getString("username"));
                map.put("password", connectParam.getString("password"));
                map.put("dbname", connectParam.getString("dbname"));
            }
            device.setConnectParam(map);
        }
        return device;
    }

    private Device initTCPOrUDP(ScheduleSetting item, JSONObject conParam) {
        log.debug(" TCPUDP协议设备 -- item：" + item);
        Device device = new Device();
        if (StringUtils.isNotEmpty(conParam.getString("ip")) && StringUtils.isNotEmpty(conParam.getString("port"))
                && StringUtils.isNotEmpty(item.getCron())) {

            device.setId(item.getId());
            device.setKey(item.getDeviceCode());
            device.setProtocol(item.getProtocol());
            device.setJobClass(item.getJobClass());

            HashMap<String, String> map = new HashMap<>();
            map.put("ip", conParam.getString("ip"));
            map.put("port", conParam.getString("port"));
            if (StringUtils.isNotEmpty(conParam.getString("commandStr"))) {
                map.put("commandStr", conParam.getString("commandStr"));
            }
            if (StringUtils.isNotEmpty(conParam.getString("portNum"))) {
                map.put("portNum", conParam.getString("portNum"));
            }
            device.setConnectParam(map);
        }
        return device;
    }

    private Device initZGHostDevice(ScheduleSetting scheduleSetting, JSONObject connectParam) {
        log.debug(" ZGHost协议设备 -- item：" + scheduleSetting);
        Device device = new Device();
        if (StringUtils.isNotEmpty(connectParam.getString("host_ip")) && StringUtils.isNotEmpty(connectParam.getString("host_port"))
                && StringUtils.isNotEmpty(connectParam.getString("host_username")) && StringUtils.isNotEmpty(connectParam.getString("host_password"))) {

            device.setId(scheduleSetting.getId());
            device.setKey(scheduleSetting.getDeviceCode());
            device.setJobClass(scheduleSetting.getJobClass());
            device.setProtocol(scheduleSetting.getProtocol());

            HashMap<String, String> map = new HashMap<>();
            map.put("host_ip", String.valueOf(connectParam.get("host_ip")));
            map.put("host_port", String.valueOf(connectParam.get("host_port")));
            map.put("host_username", connectParam.getString("host_username"));
            map.put("host_password", connectParam.getString("host_password"));
            device.setConnectParam(map);
        }
        return device;
    }

    private Device initZGCloudDevice(ScheduleSetting scheduleSetting, JSONObject connectParam) {
        log.debug(" ZGCloud协议设备 -- item：" + scheduleSetting);
        Device device = new Device();
        if (StringUtils.isNotEmpty(connectParam.getString("cloud_IP")) && StringUtils.isNotEmpty(connectParam.getString("cloud_port"))
                && StringUtils.isNotEmpty(connectParam.getString("cloud_username")) && StringUtils.isNotEmpty(connectParam.getString("cloud_password"))) {

            device.setId(scheduleSetting.getId());
            device.setKey(scheduleSetting.getDeviceCode());
            device.setJobClass(scheduleSetting.getJobClass());
            device.setProtocol(scheduleSetting.getProtocol());

            HashMap<String, String> map = new HashMap<>();
            map.put("cloud_IP", String.valueOf(connectParam.get("cloud_IP")));
            map.put("cloud_port", String.valueOf(connectParam.get("cloud_port")));
            map.put("cloud_username", connectParam.getString("cloud_username"));
            map.put("cloud_password", connectParam.getString("cloud_password"));
            device.setConnectParam(map);
        }
        return device;
    }

    //初始化数据库设备
    private Device initDB(ScheduleSetting item, JSONObject conParam) {
        log.debug(" DBDriver协议设备 -- item：" + item);
        Device device = new Device();
        if (conParam.get("ip") != null && !conParam.get("ip").equals("")
                && conParam.get("port") != null && !conParam.get("port").equals("")
                && conParam.get("dbname") != null && !conParam.get("dbname").equals("")
                && conParam.get("username") != null && !conParam.get("username").equals("")
                && conParam.get("password") != null && !conParam.get("password").equals("")) {

            device.setId(item.getId());
            device.setKey(item.getDeviceCode());
            device.setProtocol(item.getProtocol());
            device.setJobClass(item.getJobClass());

            HashMap<String, String> map = new HashMap<>();
            map.put("ip", (String) conParam.get("ip"));
            map.put("port", (String) conParam.get("port"));
            map.put("dbname", (String) conParam.get("dbname"));
            map.put("username", (String) conParam.get("username"));
            map.put("password", (String) conParam.get("password"));
            device.setConnectParam(map);
        }
        return device;
    }

    //初始化JMX协议设备
    private Device initJMX(ScheduleSetting item, JSONObject conParam) {
        log.debug(" JMX协议设备 -- item：" + item);
        Device device = new Device();
        if (item.getJobClass().contains("TomcatMbscController")) {
            if (StringUtils.isNotEmpty(conParam.getString("ip"))
                    && StringUtils.isNotEmpty(conParam.getString("port"))
                    && StringUtils.isNotEmpty(item.getCron())) {

                device.setId(item.getId());
                device.setKey(item.getDeviceCode());
                device.setProtocol(item.getProtocol());
                device.setJobClass(item.getJobClass());

                HashMap<String, String> map = new HashMap<>();
                map.put("ip", (String) conParam.get("ip"));
                map.put("port", (String) conParam.get("port"));
                device.setConnectParam(map);
            }
        } else {
            if (StringUtils.isNotEmpty(conParam.getString("ip"))
                    && StringUtils.isNotEmpty(conParam.getString("port"))
                    && StringUtils.isNotEmpty(conParam.getString("username"))
                    && StringUtils.isNotEmpty(conParam.getString("password"))
                    && StringUtils.isNotEmpty(item.getCron())) {

                device.setId(item.getId());
                device.setKey(item.getDeviceCode());
                device.setProtocol(item.getProtocol());
                device.setJobClass(item.getJobClass());

                HashMap<String, String> map = new HashMap<>();
                map.put("ip", (String) conParam.get("ip"));
                map.put("port", (String) conParam.get("port"));
                map.put("username", (String) conParam.get("username"));
                map.put("password", (String) conParam.get("password"));
                device.setConnectParam(map);
            }
        }

        return device;
    }

    private Device initNginx(ScheduleSetting item, JSONObject conParam) {
        log.debug(" Nginx协议设备 -- item：" + item);
        Device device = new Device();

        if (StringUtils.isNotEmpty(conParam.getString("ip"))
                && StringUtils.isNotEmpty(conParam.getString("port"))
                && StringUtils.isNotEmpty(item.getCron())) {

            device.setId(item.getId());
            device.setKey(item.getDeviceCode());
            device.setProtocol(item.getProtocol());
            device.setJobClass(item.getJobClass());

            HashMap<String, String> map = new HashMap<>();
            map.put("ip", (String) conParam.get("ip"));
            map.put("port", (String) conParam.get("port"));
            device.setConnectParam(map);
        }
        return device;
    }

    //初始化IPMI服务器设备
    private Device initIpmi(ScheduleSetting item, JSONObject connectParam) {
        log.debug(" IPMI协议设备 -- item：" + item);
        Device device = new Device();
        if (StringUtils.isNotEmpty(connectParam.getString("ip").trim()) && StringUtils.isNotEmpty(connectParam.getString("username"))
                && StringUtils.isNotEmpty(connectParam.getString("password")) && StringUtils.isNotEmpty(item.getCron().trim())) {

            device.setId(item.getId());
            device.setKey(item.getDeviceCode());
            device.setProtocol(item.getProtocol());
            device.setJobClass(item.getJobClass());

            HashMap<String, String> map = new HashMap<>();
            map.put("ip", (String) connectParam.get("ip"));
            map.put("username", connectParam.getString("username"));
            map.put("password", connectParam.getString("password"));
            device.setConnectParam(map);
        }
        return device;
    }

}
