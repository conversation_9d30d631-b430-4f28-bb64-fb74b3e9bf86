package com.yuanqiao.insight.collection.collector.collector.impl.MessageQueues.RabbitMQ;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.collector.Collector;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.acore.scheduler.SchedulerManagerInter;
import com.yuanqiao.insight.acore.utils.SpringContextUtil;
import com.yuanqiao.insight.collection.collector.utils.DataBaseCodecUtils;
import com.yuanqiao.insight.common.util.api.RabbitMQRestApi;
import com.yuanqiao.insight.service.collector.utils.DataPersistenceStorageUtils;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import com.yuanqiao.insight.utils.SNMPMetadataUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.EnableAsync;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * RabbitMQ
 */
@Slf4j
@EnableAsync
public class RabbitMQCollector implements Collector {
    private Device device;
    private RabbitMQCodec rabbitMQCodec;
    private SchedulerManagerInter schedulerManager;
    private RedisTemplate redisTemplate;
    private SNMPMetadataUtils metadataUtils;


    //初始化
    public void init(Device device, SchedulerManagerInter schedulerManager) {
        //绑定设备
        this.device = device;
        //创建解码器
        this.rabbitMQCodec = new RabbitMQCodec();
        //ZStackCloudCodec
        this.schedulerManager = schedulerManager;
        //初始化RedisTemplate
        this.redisTemplate = (RedisTemplate) SpringContextUtil.getBean("redisTemplate");
        //初始化物模型工具类
        this.metadataUtils = SpringContextUtil.getBean(SNMPMetadataUtils.class);

    }

    //执行方法
    public void execute() {
        log.info("--------RabbitMQ监控任务执行了...");
        //初始化监控数据持久化存储工具类
        DataPersistenceStorageUtils storageUtils = (DataPersistenceStorageUtils) SpringContextUtil.getBean("dataPersistenceStorageUtils");
        //初始化最外层jsonObject
        JSONObject jsonObject = new JSONObject();
        //将物模型同步到本地缓存中
        List<ProertyMetadata> metadataList = new ArrayList<>();
        try {
            metadataList = metadataUtils.setMetadata2(device.getKey(), device.getProtocol());
        } catch (Exception e) {
            log.error("从本地缓存中获取物模型出现异常！", e);
        }
        try {
            String ip = device.getConnectParam().get("ip");
            String port = device.getConnectParam().get("port");
            String username = device.getConnectParam().get("username");
            String password = device.getConnectParam().get("password");

            // 调用多个接口
            String overView = RabbitMQRestApi.getRabbitMqOverView(ip, port, username, password);
            String vhosts = RabbitMQRestApi.getRabbitMqVhosts(ip, port, username, password);
            String channels = RabbitMQRestApi.getRabbitMqChannels(ip, port, username, password);
            String nodes = RabbitMQRestApi.getRabbitMqNodes(ip, port, username, password);
            String exchanges = RabbitMQRestApi.getRabbitMqExchanges(ip, port, username, password);
            String queues = RabbitMQRestApi.getRabbitMqQueues(ip, port, username, password);
            String permissions = RabbitMQRestApi.getRabbitMqPermissions(ip, port, username, password);
            String connections = RabbitMQRestApi.getRabbitMqConnections(ip, port, username, password);
            String bindings = RabbitMQRestApi.getRabbitMqBindings(ip, port, username, password);
            String users = RabbitMQRestApi.getRabbitMqUsers(ip, port, username, password);
            String featureFlags = RabbitMQRestApi.getRabbitMqFeatureFlags(ip, port, username, password);
            String policies = RabbitMQRestApi.getRabbitMqPolicies(ip, port, username, password);

            if (StringUtils.isNotEmpty(overView)) {
                SimpleDateFormat time_sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String timestampFormat = time_sdf.format(new Date());
                storageUtils.heartBeatMessageHandler(schedulerManager, device, timestampFormat);

                // 解码所有接口返回的数据
                JSONObject commonMap = rabbitMQCodec.dataCodecObject(
                        overView, vhosts, channels, nodes, exchanges, queues, permissions,connections,bindings,users,
                        featureFlags,policies
                );

                log.info("开始获取并解析device_code：" + device.getKey() + " 的数据信息");
                JSONObject jsonObjectData = new JSONObject();
                DataBaseCodecUtils dataBaseCodecUtils = new DataBaseCodecUtils();

                for (ProertyMetadata metadata : metadataList) {
                    if (StringUtils.isNotEmpty(metadata.getDataType())) {
                        log.info("解析device_code：" + device.getKey() + " 的数据信息");
                        jsonObject = dataBaseCodecUtils.switchByDataType(metadata, "stca:" + device.getKey()+"_"+device.getProtocol(), jsonObjectData, commonMap);
                    }
                }

                log.debug("Codec解析出的数据：" + jsonObject);
                if (jsonObject != null && !jsonObject.isEmpty()) {
                    //设置状态容器、发布设备数据事件
                    log.debug("将设备 " + device.getKey() + " 的监控数据存入Redis_stca...");
                    jsonObject.put("metadataList", metadataList.stream().map(ProertyMetadata::getCode).collect(Collectors.toList()));
                    schedulerManager.setStatusCache(device, jsonObject);

                    //监控数据持久化到ElasticSearch
                    log.debug("将设备 " + device.getKey() + " 的监控数据存入ElasticSearch...");
                    storageUtils.dataStore(device, jsonObject, metadataList, timestampFormat);
                }
            } else {
                log.error("当前RabbitMQ设备 【" + device.getKey() + " 】返回值为：空，设备连接失败，设置设备离线！");
                schedulerManager.setDeviceDown(device);
            }
        } catch (Exception e) {
            log.error("RabbitMQ登录或数据处理异常", e);
        }
    }
}
