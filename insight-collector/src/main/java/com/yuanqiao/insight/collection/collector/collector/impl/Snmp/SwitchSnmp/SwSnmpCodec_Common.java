package com.yuanqiao.insight.collection.collector.collector.impl.Snmp.SwitchSnmp;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.collection.collector.utils.PingIpUtils;
import com.yuanqiao.insight.collection.collector.utils.SNMPCodecAndCollectUtils;
import com.yuanqiao.insight.collection.collector.utils.SNMPCodecInterface;
import com.yuanqiao.insight.common.util.common.TimeUtils;
import com.yuanqiao.insight.common.util.snmp.SNMPUtils;
import com.yuanqiao.insight.common.util.snmp.SnmpConstant;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigInteger;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class SwSnmpCodec_Common implements SNMPCodecInterface {

    // 操作系统类型
    String osType;

    public void setOsType(String osType) {
        this.osType = osType;
    }

    //最外层jsonObject
    JSONObject jsonObject = new JSONObject();
    //公共结果集
    JSONObject commonMap = new JSONObject();

    @Override
    public JSONObject dataCodec(SNMPUtils snmpUtils, List<ProertyMetadata> metadataList, String stcaKey, Device device, SNMPCodecAndCollectUtils snmpCodecAndCollectUtils) {
        //清空外层jsonObject和公共结果集commonMap
        jsonObject.clear();
        commonMap.clear();

        //静态属性
        HashMap<String, Object> staticInfoMap = new HashMap<>();

        staticInfoMap.put("switchName", getName(snmpUtils).get("switchName"));
        staticInfoMap.put("switchDesc", getSysDesc(snmpUtils).get("switchDesc"));
        staticInfoMap.put("portNum", getPortNum(snmpUtils).get("portNum"));
        staticInfoMap.put("runTime", getRunTime(snmpUtils).get("runTime"));
        //staticInfoMap.put("switchTemp", getTemperature(snmpUtils).get("switchTemp"));
        //staticInfoMap.put("switchFanStatus", getFanStatus(snmpUtils).get("switchFanStatus"));
        commonMap.put("staticInfo", staticInfoMap);
        commonMap.put("sysUpTime", getSysUpTime(snmpUtils).get("sysUpTime"));

        //commonMap.put("avgPower", getAvgPower(snmpUtils).get("avgPower"));
        //commonMap.put("ratedPower", getRatedPower(snmpUtils).get("ratedPower"));
        //commonMap.put("nowPower", getNowPower(snmpUtils).get("nowPower"));
        commonMap.put("cpuRate", getCpuRate(snmpUtils).get("cpuRate"));
        commonMap.put("memRate", getMemRate(snmpUtils).get("memRate"));
        Map<String, Object> portInfoMap = getPortInfoList(snmpUtils, "*******.*******");
        commonMap.put("portInfo", portInfoMap.get("portInfo"));
        // 输入输出总流量
        commonMap.put("netInOut", portInfoMap.get("netInOut"));
        // 输入输出总速率
        commonMap.put("allSpeed", portInfoMap.get("inoutSpeed"));
        // 输入总速率
        commonMap.put("inSpeed", portInfoMap.get("inSpeed"));
        // 输出总速率
        commonMap.put("outSpeed", portInfoMap.get("outSpeed"));
        //ping响应时间
        String ip = device.getConnectParam().get("ip");
        String time = PingIpUtils.pingAvgTime(ip, 4, 2000);
        commonMap.put("time", time);
        //循环遍历当前交换机的物模型
        metadataList.forEach(item -> {
            if (StringUtils.isNotEmpty(item.getDataType())) {
                jsonObject = snmpCodecAndCollectUtils.switchByDataType(item, stcaKey, jsonObject, commonMap, snmpUtils);
            }
        });
        return jsonObject;
    }

    /* ---------------------------------------------------------------------------------------------------------------- */

    /**
     * 获取设备运行时长
     *
     * @param snmpUtils
     * @return
     */
    private Map<String, String> getSysUpTime(SNMPUtils snmpUtils) {
        HashMap<String, String> map = new HashMap<>();
        try {
            String sysUpTime = snmpUtils.getPDU("*******.*******.0");
            if (StringUtils.isNotEmpty(sysUpTime) && !sysUpTime.equalsIgnoreCase("noSuchObject")
                    && !sysUpTime.equalsIgnoreCase("noSuchInstance")) {
                // 去除字符串中的逗号
                sysUpTime = sysUpTime.replace(",", "");

                // 使用正则表达式匹配时间信息
                Pattern pattern = Pattern.compile("(?:(\\d+) days?)?(?:\\s*,?\\s*(\\d+):(\\d+)(?::(\\d+(?:\\.\\d+)?))?)?");
                Matcher matcher = pattern.matcher(sysUpTime);

                int days = 0, hours = 0, minutes = 0, seconds = 0;
                double milliseconds = 0;

                if (matcher.find()) {
                    if (matcher.group(1) != null) {
                        days = Integer.parseInt(matcher.group(1));
                    }
                    if (matcher.group(2) != null) {
                        hours = Integer.parseInt(matcher.group(2));
                    }
                    if (matcher.group(3) != null) {
                        minutes = Integer.parseInt(matcher.group(3));
                    }
                    if (matcher.group(4) != null) {
                        String[] secondsParts = matcher.group(4).split("\\.");
                        seconds = Integer.parseInt(secondsParts[0]);
                        if (secondsParts.length > 1) {
                            milliseconds = Double.parseDouble("0." + secondsParts[1]);
                        }
                    }
                }
                // 计算总毫秒数
                BigInteger totalMilliseconds = BigInteger.valueOf(days * 24 * 60 * 60 * 1000L)
                        .add(BigInteger.valueOf(hours * 60 * 60 * 1000L))
                        .add(BigInteger.valueOf(minutes * 60 * 1000L))
                        .add(BigInteger.valueOf((long) (seconds * 1000 + milliseconds * 1000)));
                sysUpTime = TimeUtils.getDistanceTime(totalMilliseconds);
                map.put("sysUpTime", sysUpTime);
            }
        } catch (Exception e) {
            log.error("获取运行时长出错", e);
            map.put("sysUpTime", "");
        }
        return map;
    }

    //获取对应oid的value值
    private Double getWalkValueByOid(String code, String oid, SNMPUtils snmpUtils) {
        Double value = 0.0;
        Map<String, String> pduWalk = null;
        try {
            pduWalk = snmpUtils.getPDUWalk(oid);
            if (pduWalk != null && pduWalk.size() > 0) {
                for (Map.Entry<String, String> entry : pduWalk.entrySet()) {
                    value += Long.parseLong(entry.getValue());
                }
            }
            return Double.valueOf(String.format("%.2f", value / 1024 / 1024 / 1024));
        } catch (Exception e) {
            log.error("获取" + code + "出错" + e.getMessage());
            return value;
        }
    }

    /* ---------------------------------------------------------------------------------------------------------------- */

    //获取交换机端口数
    private Map<String, Integer> getPortNum(SNMPUtils snmpUtils) {
        try {
            HashMap<String, Integer> map = new HashMap<>();
            // 交换机端口数
            String portNum_String = snmpUtils.getPDU("*******.2.1.2.1.0");
            if (StringUtils.isEmpty(portNum_String)) {
                log.error("未获取到交换机端口数...");
                map.put("portNum", 0);
            } else {
                Integer portNum = Integer.parseInt(portNum_String);
                map.put("portNum", portNum);
            }
            return map;
        } catch (Exception e) {
            log.error("获取交换机端口数出错" + e.getMessage());
            e.printStackTrace();
            HashMap<String, Integer> map = new HashMap<>();
            map.put("portNum", 0);
            return map;
        }
    }

    //获取交换机名称
    private Map<String, String> getName(SNMPUtils snmpUtils) {
        try {
            HashMap<String, String> map = new HashMap<>();
            // 交换机名称
            String ifName = snmpUtils.getPDU("*******.2.1.1.5.0");
            if (ifName == null || "".equals(ifName.trim()) || "null".equalsIgnoreCase(ifName.trim())) {
                log.error("未获取到交换机名称...");
                map.put("switchName", "- -");
            } else {
                map.put("switchName", ifName);
            }
            return map;
        } catch (Exception e) {
            log.error("获取交换机名称出错" + e.getMessage());
            HashMap<String, String> map = new HashMap<>();
            map.put("switchName", "- -");
            return map;
        }
    }

    //获取交换机描述
    private Map<String, String> getSysDesc(SNMPUtils snmpUtils) {
        try {
            HashMap<String, String> map = new HashMap<>();
            // 交换机描述
            String sysDesc = snmpUtils.getPDU("*******.2.1.1.1.0");
            if (sysDesc == null || "".equals(sysDesc.trim()) || "null".equalsIgnoreCase(sysDesc.trim())) {
                log.error("未获取到交换机描述...");
                map.put("switchDesc", "- -");
            }
            if (sysDesc.contains("S5720-36C-EI-AC")) {
                sysDesc = "S5720-36C-EI-AC Huawei";
            }
            map.put("switchDesc", sysDesc);
            return map;
        } catch (Exception e) {
            log.error("获取交换机描述出错" + e.getMessage());
            HashMap<String, String> map = new HashMap<>();
            map.put("switchDesc", "- -");
            return map;
        }
    }

    //获取交换机运行时间
    private Map<String, String> getRunTime(SNMPUtils snmpUtils) {
        try {
            HashMap<String, String> map = new HashMap<>();
            // 交换机运行时间
            String sysRunTime = snmpUtils.getPDU("*******.*******.0");
            if (sysRunTime == null || "".equals(sysRunTime.trim()) || "null".equalsIgnoreCase(sysRunTime.trim())) {
                log.error("未获取到交换机运行时间...");
                map.put("runTime", "--");
            }
//            if (!"".equals(sysRunTime) && sysRunTime != null && !sysRunTime.isEmpty()) {
//                String[] categoryArr = sysRunTime.split(":");
//                String dayRep = categoryArr[0].replace("days", "天");
//                String seconds = categoryArr[2].substring(0, 2);
//                sysRunTime = dayRep + ":" + categoryArr[1] + ":" + seconds;
//            }
            map.put("runTime", sysRunTime);
            return map;
        } catch (Exception e) {
            log.error("获取交换机运行时间出错" + e.getMessage());
            HashMap<String, String> map = new HashMap<>();
            map.put("runTime", "--");
            return map;
        }
    }

    //获取交换机温度
    private Map<String, String> getTemperature(SNMPUtils snmpUtils) {
        try {
            HashMap<String, String> map = new HashMap<>();
            String temp = "";
            String sysDesc = snmpUtils.getPDU("*******.2.1.1.1.0");
            if (sysDesc.contains("Huawei") || sysDesc.contains("S5720-36C-EI-AC") || sysDesc.contains("S5700-52C-SI")) {
                temp = snmpUtils.getPDU("*******.4.1.2011.5.25.31.1.1.1.1.11.67108873");
            }
            if (temp == null || temp.equals("") || temp.contains("noSuchInstance")) {
                //log.error("未获取到交换机温度...");
                map.put("switchTemp", "- -");
                return map;
            }
            map.put("switchTemp", temp);
            return map;
        } catch (Exception e) {
            log.error("获取交换机温度出错" + e.getMessage());
            HashMap<String, String> map = new HashMap<>();
            map.put("switchTemp", "- -");
            return map;
        }
    }

    //获取交换机风扇状态
    private Map<String, String> getFanStatus(SNMPUtils snmpUtils) {
        try {
            HashMap<String, String> map = new HashMap<>();
            String switchFanStatus = "";
            String sysDesc = snmpUtils.getPDU("*******.2.1.1.1.0");
            if (sysDesc.contains("Huawei") || sysDesc.contains("S5720-36C-EI-AC")) {
                switchFanStatus = snmpUtils.getPDU("*******.4.1.2011.5.25.31.1.1.10.1.7.0.7");
            }
            if (switchFanStatus == null || switchFanStatus.equals("")) {
                log.error("未获取到交换机风扇状态...");
                switchFanStatus = "- -";
            } else if (switchFanStatus.equals("1")) {
                switchFanStatus = "正常";
            } else {
                switchFanStatus = "异常";
            }
            map.put("switchFanStatus", switchFanStatus);
            return map;
        } catch (Exception e) {
            log.error("获取交换机风扇状态出错" + e.getMessage());
            HashMap<String, String> map = new HashMap<>();
            map.put("switchFanStatus", "- -");
            return map;
        }
    }

    //获取交换机平均功率
    private Map<String, Double> getAvgPower(SNMPUtils snmpUtils) {
        try {
            HashMap<String, Double> map = new HashMap<>();
            double avgPower = 0.0;
            String power = "";

            String sysDesc = snmpUtils.getPDU("*******.2.1.1.1.0");
            if (sysDesc.contains("Huawei")) {
                if (sysDesc.contains("S5720-36C-EI-AC") || sysDesc.contains("S5700-52C-SI") || sysDesc.contains("S6720S-26Q-EI-24S-AC")) {
                    power = snmpUtils.getPDU("*******.4.1.2011.6.157.1.3.0");
                }
            }
            if (power == null || power.equals("")) {
                log.error("未获取到交换机平均功率...");
                avgPower = 0.0;
            } else {
                avgPower = Double.parseDouble(power) / 1000;
            }
            map.put("avgPower", Double.valueOf(String.format("%.2f", avgPower)));
            return map;
        } catch (Exception e) {
            log.error("获取交换机平均功率出错" + e.getMessage());
            HashMap<String, Double> map = new HashMap<>();
            map.put("avgPower", 0.0);
            return map;
        }
    }

    //获取交换机额定功率
    private Map<String, Double> getRatedPower(SNMPUtils snmpUtils) {
        try {
            HashMap<String, Double> map = new HashMap<>();
            double ratedPower = 0.0;
            String power = "";
            String sysDesc = snmpUtils.getPDU("*******.2.1.1.1.0");
            if (sysDesc.contains("Huawei") || sysDesc.contains("S5720-36C-EI-AC")) {
                power = snmpUtils.getPDU("*******.4.1.2011.6.157.1.4.0");
            }
            if (power == null || power.equals("")) {
                log.error("未获取到交换机额定功率...");
                ratedPower = 0.0;
            } else {
                ratedPower = Double.parseDouble(power) / 1000;
            }
            map.put("ratedPower", Double.valueOf(String.format("%.2f", ratedPower)));
            return map;
        } catch (Exception e) {
            log.error("获取交换机额定功率出错" + e.getMessage());
            HashMap<String, Double> map = new HashMap<>();
            map.put("ratedPower", 0.0);
            return map;
        }
    }

    //获取交换机当前功率
    private Map<String, Double> getNowPower(SNMPUtils snmpUtils) {
        try {
            HashMap<String, Double> map = new HashMap<>();
            double nowPower = 0.0;
            String power = "";

            String sysDesc = snmpUtils.getPDU("*******.2.1.1.1.0");
            if (sysDesc.contains("Huawei")) {
                if (sysDesc.contains("S5720-36C-EI-AC") || sysDesc.contains("S5700-52C-SI") || sysDesc.contains("S6720S-26Q-EI-24S-AC")) {
                    power = snmpUtils.getPDU("*******.4.1.2011.6.157.1.6.0");
                }
            }
            if (power == null || power.equals("")) {
                log.error("未获取到交换机当前功率...");
                nowPower = 0.0;
            } else {
                nowPower = Double.parseDouble(power) / 1000;
            }
            map.put("nowPower", Double.valueOf(String.format("%.2f", nowPower)));
            return map;
        } catch (Exception e) {
            log.error("获取交换机当前功率出错" + e.getMessage());
            HashMap<String, Double> map = new HashMap<>();
            map.put("nowPower", 0.0);
            return map;
        }
    }


    //获取交换机cpu使用率
    private Map<String, Double> getCpuRate(SNMPUtils snmpUtils) {
        Map<String, String> cpuUtilizationMap = null;
        HashMap<String, Double> resMap = new HashMap<>();

        try {
            //初始化cpu平均使用率
            double cpuUtilization = 0.0;
            //获取交换机描述
            String sysDesc = snmpUtils.getPDU("*******.2.1.1.1.0");


            // 华为Quidway S9312交换机CPU使用率
            if (sysDesc.contains("Quidway") || sysDesc.contains("Huawei")) {
                if (null == cpuUtilizationMap) {
                    cpuUtilizationMap = snmpUtils.getPDUWalk("*******.4.1.2011.6.3.4.1.2");
                }
                if (cpuUtilizationMap != null && cpuUtilizationMap.size() > 0) {
                    for (Map.Entry<String, String> entry : cpuUtilizationMap.entrySet()) {
                        cpuUtilization += Integer.parseInt(entry.getValue());
                    }
                    cpuUtilization /= cpuUtilizationMap.size();
                }


                // H3C_S5120-52C-EI交换机CPU使用率
            } else if ((sysDesc.contains("S5120-52C")) || (sysDesc.contains("S5120V2-52P-LI")) || (sysDesc.contains("S7503")) || (sysDesc.contains("S7506X")) || (sysDesc.contains("S5130"))
                    || (sysDesc.contains("S5750") && sysDesc.contains("H3C"))
                    || (sysDesc.contains("S5800-32F")) || (sysDesc.contains("S6506R"))) {
                // hh3cEntityExtCpuUsage：CPU实体在最后1分钟内的利用率。
                if (null == cpuUtilizationMap) {
                    cpuUtilizationMap = snmpUtils.getPDUWalk("*******.4.1.25506.2.6.1.1.1.1.6");
                }
                int countOfCpu = 0;
                if (cpuUtilizationMap != null && cpuUtilizationMap.size() > 0) {
                    for (Map.Entry<String, String> entry : cpuUtilizationMap.entrySet()) {
                        // 如果某实体对象取值永远为零，那么该实体不是CPU实体
                        if (Integer.parseInt(entry.getValue()) > 0) {
                            cpuUtilization += Integer.parseInt(entry.getValue());
                            ++countOfCpu;
                        }
                    }
                    cpuUtilization /= countOfCpu;
                }


                // H3C_S3100-52P-SI交换机CPU使用率
            } else if (sysDesc.contains("S3100-52P-SI")) {
                // h3cEntityExtCpuUsage：CPU实体在最后1分钟内的利用率。
                if (null == cpuUtilizationMap) {
                    cpuUtilizationMap = snmpUtils.getPDUWalk("*******.4.1.2011.10.2.6.1.1.1.1.6");
                }
                int countOfCpu = 0;
                if (cpuUtilizationMap != null && cpuUtilizationMap.size() > 0) {
                    for (Map.Entry<String, String> entry : cpuUtilizationMap.entrySet()) {
                        // 如果某实体对象取值永远为零，那么该实体不是CPU实体
                        if (Integer.parseInt(entry.getValue()) > 0) {
                            cpuUtilization += Integer.parseInt(entry.getValue());
                            ++countOfCpu;
                        }
                    }
                    cpuUtilization /= countOfCpu;
                }


                // NSS4320交换机CPU使用率
            } else if (sysDesc.contains("NSS4320") || sysDesc.contains("NSS3320")) {
                // h3cEntityExtCpuUsage：CPU实体在最后1分钟内的利用率。
                if (null == cpuUtilizationMap) {
                    cpuUtilizationMap = snmpUtils.getPDUWalk("*******.4.1.5651.3.600.9.1.1.3");
                }
                int countOfCpu = 0;
                if (cpuUtilizationMap != null && cpuUtilizationMap.size() > 0) {
                    for (Map.Entry<String, String> entry : cpuUtilizationMap.entrySet()) {
                        // 如果某实体对象取值永远为零，那么该实体不是CPU实体
                        if (Integer.parseInt(entry.getValue()) > 0) {
                            cpuUtilization += Integer.parseInt(entry.getValue());
                            ++countOfCpu;
                        }
                    }
                    cpuUtilization /= countOfCpu;
                }


                // H3C_S3600-52P-SI交换机CPU使用率
            } else if (sysDesc.contains("S3600-52P-SI")) {
                // h3cEntityExtCpuUsage：CPU实体在最后1分钟内的利用率。
                if (null == cpuUtilizationMap) {
                    cpuUtilizationMap = snmpUtils.getPDUWalk("*******.4.1.2011.10.2.6.1.1.1.1.6");
                }
                int countOfCpu = 0;
                if (cpuUtilizationMap != null && cpuUtilizationMap.size() > 0) {
                    for (Map.Entry<String, String> entry : cpuUtilizationMap.entrySet()) {
                        // 如果某实体对象取值永远为零，那么该实体不是CPU实体
                        if (Integer.parseInt(entry.getValue()) > 0) {
                            cpuUtilization += Integer.parseInt(entry.getValue());
                            ++countOfCpu;
                        }
                    }
                    cpuUtilization /= countOfCpu;
                }


                // H3C S5500-交换机CPU使用率
            } else if (sysDesc.contains("S5500")) {
                // h3cEntityExtCpuUsage：CPU实体在最后1分钟内的利用率。
                if (null == cpuUtilizationMap) {
                    cpuUtilizationMap = snmpUtils.getPDUWalk("*******.4.1.2011.10.2.6.1.1.1.1.6");
                }
                int countOfCpu = 0;
                if (cpuUtilizationMap != null && cpuUtilizationMap.size() > 0) {
                    for (Map.Entry<String, String> entry : cpuUtilizationMap.entrySet()) {
                        // 如果某实体对象取值永远为零，那么该实体不是CPU实体
                        if (Integer.parseInt(entry.getValue()) > 0) {
                            cpuUtilization += Integer.parseInt(entry.getValue());
                            ++countOfCpu;
                        }
                    }
                    cpuUtilization /= countOfCpu;
                }


                // 宁波交换机__s5560
            } else if (sysDesc.contains("S5560") || sysDesc.contains("S7003") || sysDesc.contains("S6812")) {
                // h3cEntityExtCpuUsage：CPU实体在最后1分钟内的利用率。
                if (null == cpuUtilizationMap) {
                    cpuUtilizationMap = snmpUtils.getPDUWalk("*******.4.1.25506.2.6.1.1.1.1.6");
                }
                int countOfCpu = 0;
//                log.error("--------------交换机的cpu的采集值------------:"+cpuUtilizationMap);
//                log.error("--------------交换机的cpu的转换值------------:"+cpuUtilizationMap.entrySet());
                if (cpuUtilizationMap != null && cpuUtilizationMap.size() > 0) {
                    for (Map.Entry<String, String> entry : cpuUtilizationMap.entrySet()) {
                        // 如果某实体对象取值永远为零，那么该实体不是CPU实体
                        if (Integer.parseInt(entry.getValue()) > 0) {
                            cpuUtilization += Integer.parseInt(entry.getValue());
                            ++countOfCpu;
                        }
                    }
                    cpuUtilization /= countOfCpu;
                }


                // 国办项目中仰联交换机__G5600-48TE
            } else if (sysDesc.contains("G5600-48TE") || sysDesc.contains("G5600")) {
                // h3cEntityExtCpuUsage：CPU实体在最后1分钟内的利用率。
                if (null == cpuUtilizationMap) {
                    cpuUtilizationMap = snmpUtils.getPDUWalk("*******.4.1.46944.9.109.1.1.1.1.4");
                }
                int countOfCpu = 0;
                if (cpuUtilizationMap != null && cpuUtilizationMap.size() > 0) {
                    for (Map.Entry<String, String> entry : cpuUtilizationMap.entrySet()) {
                        // 如果某实体对象取值永远为零，那么该实体不是CPU实体
                        if (Integer.parseInt(entry.getValue()) > 0) {
                            cpuUtilization += Integer.parseInt(entry.getValue());
                            ++countOfCpu;
                        }
                    }
                    cpuUtilization /= countOfCpu;
                }


                // 德州项目中仰联交换机__g5600
            } else if (sysDesc.contains("5600")) {
                // 获取当前CPU的空闲百分比，按照最后一分钟CPU的实际使用状态计算获得
                Map<String, String> cpuFreeMap = snmpUtils.getPDUWalk("*******.4.1.27975.1.2.11");
                String cpuFreeStr = cpuFreeMap.get("*******.4.1.27975.1.2.11.0");
                // cpu实际使用率
                cpuUtilization = (100 - Integer.parseInt(cpuFreeStr));


                // 地质调查局锐捷交换机
            } else if ((sysDesc.contains("S8612E") && sysDesc.contains("Ruijie")) || sysDesc.contains("S7505")) {
                // CPU实体在最后1分钟内的利用率。
                String cpuUsed = snmpUtils.getPDU("*******.4.1.4881.1.1.10.2.36.1.1.2.0");
                if (cpuUsed == null || cpuUsed.equals("") || cpuUsed.contains("no")) {
                    cpuUtilization = 0;
                } else {
                    cpuUtilization = Double.parseDouble(cpuUsed);
                }


                // 迈普 S3120 交换机CPU使用率
            } else if (sysDesc.contains("S3120")) {

                if (null == cpuUtilizationMap) {
                    cpuUtilizationMap = snmpUtils.getPDUWalk("*******.4.1.5651.3.20.1.1.10.1");
                }
                int countOfCpu = 0;
                if (cpuUtilizationMap != null && cpuUtilizationMap.size() > 0) {
                    for (Map.Entry<String, String> entry : cpuUtilizationMap.entrySet()) {
                        // 如果某实体对象取值永远为零，那么该实体不是CPU实体
                        if (Integer.parseInt(entry.getValue()) > 0) {
                            cpuUtilization += Integer.parseInt(entry.getValue());
                            ++countOfCpu;
                        }
                    }
                    cpuUtilization /= countOfCpu;
                }


                // 锐捷交换机S5750
            } else if (sysDesc.contains("Ruijie") && sysDesc.contains("S5750")) {
                if (null == cpuUtilizationMap) {
                    cpuUtilizationMap = snmpUtils.getPDUWalk("*******.4.1.4881.1.1.10.2.36.1.1.8");
                }
                int countOfCpu = 0;
                if (cpuUtilizationMap != null && cpuUtilizationMap.size() > 0) {
                    for (Map.Entry<String, String> entry : cpuUtilizationMap.entrySet()) {
                        // 如果某实体对象取值永远为零，那么该实体不是CPU实体
                        if (Integer.parseInt(entry.getValue()) > 0) {
                            cpuUtilization += Integer.parseInt(entry.getValue());
                            ++countOfCpu;
                        }
                    }
                    cpuUtilization /= countOfCpu;
                }
            }
            if (cpuUtilization == 0) {
                log.error("未获取到交换机cpu使用率!");
                resMap.put("cpuRate", 0.0);
                return resMap;
            } else {
                resMap.put("cpuRate", Double.valueOf(String.format("%.2f", cpuUtilization)));
                return resMap;
            }
        } catch (Exception e) {
            log.error("获取交换机cpu平均使用率出错" + e.getMessage());
            resMap.put("cpuRate", 0.0);
            return resMap;
        }
    }

    //获取内存使用率
    private Map<String, Double> getMemRate(SNMPUtils snmpUtils) {
        try {
            HashMap<String, Double> resMap = new HashMap<>();
            // 内存总量
            double memTotal = 0;
            // 内存空闲量
            double memFree = 0;
            // 内存使用率
            double memUtilization = 0;

            // 内存使用率
            Map<String, String> memUtilizationMap = null;
            // 内存总量
            Map<String, String> memTotalMap = null;
            // 内存空闲
            Map<String, String> memFreeMap = null;

            //交换机系统描述
            String sysDesc = snmpUtils.getPDU("*******.2.1.1.1.0");

            // 华为Quidway S9312交换机内存使用率
            if (sysDesc.contains("Quidway") || sysDesc.contains("Huawei")) {
                if (sysDesc.contains("S3328") || sysDesc.contains("S6720S") || sysDesc.contains("S5700-52C-SI")) {
                    // hwMemoryDevSize：指示被管理对象的内存总量，单位是字节。包括每块板上空闲的内存量和已占用的内存量，即，是hwMemoryDevFree与hwMemoryDevRawSliceUsed的和。每块单板都有一个内存，内存大小因产品而异。
                    if (null == memTotalMap) {
                        memTotalMap = snmpUtils.getPDUWalk("*******.4.1.2011.6.3.5.1.1.2");
                        log.error("**** memTotalMap ：" + memTotalMap);
                    }
                    if (memTotalMap != null && memTotalMap.size() > 0) {
                        for (Map.Entry<String, String> entry : memTotalMap.entrySet()) {
                            memTotal += Integer.parseInt(entry.getValue());
                        }
                        log.error("**** memTotal ：" + memTotal);
                    }
                    memFreeMap = snmpUtils.getPDUWalk("*******.4.1.2011.6.3.5.1.1.3");
                    log.error("**** memFreeMap ：" + memFreeMap);
                    if (memFreeMap != null && memFreeMap.size() > 0) {
                        for (Map.Entry<String, String> entry : memFreeMap.entrySet()) {
                            memFree += Integer.parseInt(entry.getValue());
                        }
                        log.error("**** memFree ：" + memFree);
                    }
                    memUtilization = 100 * (memTotal - memFree) / memTotal;
                    log.error("**** memUtilization ：" + memUtilization);
                }


                // H3C_S5120-52C-EI交换机内存使用率
            } else if ((sysDesc.contains("H3C")) &&
                    ((sysDesc.contains("S5120-52C")) || (sysDesc.contains("S5120V2-52P-LI")) || (sysDesc.contains("S7503")) || (sysDesc.contains("S7506X")) || (sysDesc.contains("S5130"))
                            || (sysDesc.contains("S5750") || (sysDesc.contains("S5800-32F"))
                            || (sysDesc.contains("S6506R"))))
            ) {
                // hh3cEntityExtMemUsage：内存利用率
                if (null == memUtilizationMap) {
                    memUtilizationMap = snmpUtils.getPDUWalk("*******.4.1.25506.2.6.1.1.1.1.8");
                }
                int countOfMem = 0;
                if (memUtilizationMap != null && memUtilizationMap.size() > 0) {
                    for (Map.Entry<String, String> entry : memUtilizationMap.entrySet()) {
                        // 如果某实体对象取值永远为零，那么该实体不是内存实体
                        if (Integer.parseInt(entry.getValue()) > 0) {
                            memUtilization += Integer.parseInt(entry.getValue());
                            ++countOfMem;
                        }
                    }
                    memUtilization /= countOfMem;
                }


                // H3C_S3100-52P-SI交换机内存使用率
            } else if (sysDesc.contains("S3100-52P-SI")) {
                // h3cEntityExtMemUsage：内存利用率
                if (null == memUtilizationMap) {
                    memUtilizationMap = snmpUtils.getPDUWalk("*******.4.1.2011.10.2.6.1.1.1.1.8");
                }
                int countOfMem = 0;
                if (memUtilizationMap != null && memUtilizationMap.size() > 0) {
                    for (Map.Entry<String, String> entry : memUtilizationMap.entrySet()) {
                        // 如果某实体对象取值永远为零，那么该实体不是内存实体
                        if (Integer.parseInt(entry.getValue()) > 0) {
                            memUtilization += Integer.parseInt(entry.getValue());
                            ++countOfMem;
                        }
                    }
                    memUtilization /= countOfMem;
                }


                // H3C_NSS4320交换机内存使用率
            } else if (sysDesc.contains("NSS4320") || sysDesc.contains("NSS3320")) {// 4320
                // h3cEntityExtMemUsage：内存利用率
                if (null == memUtilizationMap) {
                    memUtilizationMap = snmpUtils.getPDUWalk("*******.4.1.5651.3.600.10.1.1.10");
                }
                int countOfMem = 0;
                if (memUtilizationMap != null && memUtilizationMap.size() > 0) {
                    for (Map.Entry<String, String> entry : memUtilizationMap.entrySet()) {
                        // 如果某实体对象取值永远为零，那么该实体不是内存实体
                        if (Integer.parseInt(entry.getValue()) > 0) {
                            memUtilization += Integer.parseInt(entry.getValue());
                            ++countOfMem;
                        }
                    }
                    memUtilization /= countOfMem;
                }


                // H3C_S3600-52P-SI交换机内存使用率
            } else if (sysDesc.contains("S3600-52P-SI")) {
                // h3cEntityExtMemUsage：内存利用率
                if (null == memUtilizationMap) {
                    memUtilizationMap = snmpUtils.getPDUWalk("*******.4.1.2011.10.2.6.1.1.1.1.8");
                }
                int countOfMem = 0;
                if (memUtilizationMap != null && memUtilizationMap.size() > 0) {
                    for (Map.Entry<String, String> entry : memUtilizationMap.entrySet()) {
                        // 如果某实体对象取值永远为零，那么该实体不是内存实体
                        if (Integer.parseInt(entry.getValue()) > 0) {
                            memUtilization += Integer.parseInt(entry.getValue());
                            ++countOfMem;
                        }
                    }
                    memUtilization /= countOfMem;
                }


                // H3C S5500-交换机内存使用率
            } else if (sysDesc.contains("S5500")) {
                // h3cEntityExtMemUsage：内存利用率
                if (null == memUtilizationMap) {
                    memUtilizationMap = snmpUtils.getPDUWalk("*******.4.1.2011.10.2.6.1.1.1.1.8");
                }
                int countOfMem = 0;
                if (memUtilizationMap != null && memUtilizationMap.size() > 0) {
                    for (Map.Entry<String, String> entry : memUtilizationMap.entrySet()) {
                        // 如果某实体对象取值永远为零，那么该实体不是内存实体
                        if (Integer.parseInt(entry.getValue()) > 0) {
                            memUtilization += Integer.parseInt(entry.getValue());
                            ++countOfMem;
                        }
                    }
                    memUtilization /= countOfMem;
                }


                // H3C S5560-   H3C S6812-  交换机 内存使用率
            } else if (sysDesc.contains("S5560") || sysDesc.contains("S7003") || sysDesc.contains("S6812")) {
                // h3cEntityExtMemUsage：内存利用率
                if (null == memUtilizationMap) {
                    memUtilizationMap = snmpUtils.getPDUWalk("*******.4.1.25506.2.6.1.1.1.1.8");
                }
                int countOfMem = 0;
//                log.error("----------交换机的内存采集值---------------"+memUtilizationMap);
//                log.error("----------交换机的内存转换值---------------"+memUtilizationMap.entrySet());
                if (memUtilizationMap != null && memUtilizationMap.size() > 0) {
                    for (Map.Entry<String, String> entry : memUtilizationMap.entrySet()) {
                        // 如果某实体对象取值永远为零，那么该实体不是内存实体
                        if (Integer.parseInt(entry.getValue()) > 0) {
                            memUtilization += Integer.parseInt(entry.getValue());
                            ++countOfMem;
                        }
                    }
                    memUtilization /= countOfMem;
                }


                // 国办项目中仰联交换机G5600-48TE
            } else if (sysDesc.contains("G5600-48TE") || sysDesc.contains("G5600")) {
                // h3cEntityExtMemUsage：内存利用率
                if (null == memUtilizationMap) {
                    memUtilizationMap = snmpUtils.getPDUWalk("*******.4.1.46944.9.48.1");
                }
                int countOfMem = 0;
                if (memUtilizationMap != null && memUtilizationMap.size() > 0) {
                    for (Map.Entry<String, String> entry : memUtilizationMap.entrySet()) {
                        // 如果某实体对象取值永远为零，那么该实体不是内存实体
                        if (Integer.parseInt(entry.getValue()) > 0) {
                            memUtilization += Integer.parseInt(entry.getValue());
                            ++countOfMem;
                        }
                    }
                    memUtilization /= countOfMem;
                }


                // 迈普 S3120 交换机内存使用率
            } else if (sysDesc.contains("S3120")) {
                if (null == memUtilizationMap) {
                    memUtilizationMap = snmpUtils.getPDUWalk("*******.4.1.5651.3.20.1.1.1.9");
                }
                int countOfMem = 0;
                if (memUtilizationMap != null && memUtilizationMap.size() > 0) {
                    for (Map.Entry<String, String> entry : memUtilizationMap.entrySet()) {
                        // 如果某实体对象取值永远为零，那么该实体不是内存实体
                        if (Integer.parseInt(entry.getValue()) > 0) {
                            memUtilization += Integer.parseInt(entry.getValue());
                            ++countOfMem;
                        }
                    }
                    memUtilization /= countOfMem;
                }


                // 锐捷RG-S5750 交换机内存使用率
            } else if (sysDesc.contains("Ruijie") && sysDesc.contains("S5750")) {
                if (null == memUtilizationMap) {
                    memUtilizationMap = snmpUtils.getPDUWalk("*******.4.1.4881.1.1.10.2.35.1.1.1.11");
                }
                int countOfMem = 0;
                if (memUtilizationMap != null && memUtilizationMap.size() > 0) {
                    for (Map.Entry<String, String> entry : memUtilizationMap.entrySet()) {
                        // 如果某实体对象取值永远为零，那么该实体不是内存实体
                        if (Integer.parseInt(entry.getValue()) > 0) {
                            memUtilization += Integer.parseInt(entry.getValue());
                            ++countOfMem;
                        }
                    }
                    memUtilization /= countOfMem;
                }


                // 锐捷RG-S7505 交换机内存使用率
            } else if (sysDesc.contains("S7505")) {
                if (null == memUtilizationMap) {
                    memUtilizationMap = snmpUtils.getPDUWalk("*******.4.1.4881.1.1.10.2.35.1.1.1.3");
                }
                int countOfMem = 0;
                if (memUtilizationMap != null && memUtilizationMap.size() > 0) {
                    for (Map.Entry<String, String> entry : memUtilizationMap.entrySet()) {
                        // 如果某实体对象取值永远为零，那么该实体不是内存实体
                        if (Integer.parseInt(entry.getValue()) > 0) {
                            memUtilization += Integer.parseInt(entry.getValue());
                            ++countOfMem;
                        }
                    }
                    memUtilization /= countOfMem;
                }
            }


            // 地质调查局锐捷交换机S8612E
            else if (sysDesc.contains("S8612E") && sysDesc.contains("Ruijie")) {
                // 内存利用率。
                if (null == memUtilizationMap) {
                    memUtilizationMap = snmpUtils.getPDUWalk("*******.4.1.4881.1.1.10.2.35.1.1.1.3");
                }
                int countOfMem = 0;
                if (memUtilizationMap != null && memUtilizationMap.size() > 0) {
                    for (Map.Entry<String, String> entry : memUtilizationMap.entrySet()) {
                        // 如果某实体对象取值永远为零，那么该实体不是内存实体
                        if (Integer.parseInt(entry.getValue()) > 0) {
                            memUtilization += Integer.parseInt(entry.getValue());
                            ++countOfMem;
                        }
                    }
                    memUtilization /= countOfMem;
                }
            }


            // 地质调查局锐捷交换机S5750C
            else if (sysDesc.contains("S5750") && sysDesc.contains("Ruijie")) {
                // 内存利用率。
                if (null == memUtilizationMap) {
                    memUtilizationMap = snmpUtils.getPDUWalk("*******.4.1.4881.1.1.10.2.35.1.1.1.3");
                }
                int countOfMem = 0;
                if (memUtilizationMap != null && memUtilizationMap.size() > 0) {
                    for (Map.Entry<String, String> entry : memUtilizationMap.entrySet()) {
                        // 如果某实体对象取值永远为零，那么该实体不是内存实体
                        if (Integer.parseInt(entry.getValue()) > 0) {
                            memUtilization += Integer.parseInt(entry.getValue());
                            ++countOfMem;
                        }
                    }
                    memUtilization /= countOfMem;
                }
            }


            // 德州项目中仰联交换机g5600
            else if (sysDesc.contains("5600")) {
                // 获取系统当前使用的内存
                String memTotalUsed = snmpUtils.getPDU("*******.4.1.27975.1.1.12.0");
                // 获取系统实际物理内存的总大小
                String memTotalReal = snmpUtils.getPDU("*******.4.1.27975.1.1.5.0");
                // 交换机实际内存使用率
                memUtilization = (Double.parseDouble(memTotalUsed) / Double.parseDouble(memTotalReal)) * 100;
            }

//            if (memUtilization == 0) {
//                log.error("未获取到交换机内存使用率或当前交换机内存使用率为0！");
//                resMap.put("memRate", 0.00);
//                return resMap;
//            } else {
            resMap.put("memRate", Double.valueOf(String.format("%.2f", memUtilization)));
            return resMap;
//            }
        } catch (Exception e) {
            log.error("获取交换机内存使用率出错" + e.getMessage());
            HashMap<String, Double> resMap = new HashMap<>();
            resMap.put("memRate", Double.valueOf(String.format("%.2f", 0.0)));
            return resMap;
        }
    }

    //获取网络吞吐量
    private Map<String, Double> getNetInAndOut(SNMPUtils snmpUtils) {
        try {
            HashMap<String, Double> map = new HashMap<>();
            //KeyMap
            Map<String, String> KeyMap = snmpUtils.getPDUWalk("*******.*******.1.1");
            int[] is = new int[KeyMap.size()];
            int i = 0;
            for (Map.Entry<String, String> en : KeyMap.entrySet()) {
                is[i] = Integer.parseInt(en.getValue());
                i++;
            }
            Arrays.sort(is);

            // 吞吐量
            double inAndOut = 0.0;
            int isLength = is.length;
            for (int j = 0; j < isLength; j++) {
                // 输入和输出字节总数
                String inSpeed_String = snmpUtils.getPDU("*******.*******.1.10." + is[j]);
                String OutSpeed_String = snmpUtils.getPDU("*******.*******.1.16." + is[j]);
                double inAndOutSpeed = 0;

                if (inSpeed_String != null && inSpeed_String != "" && OutSpeed_String != null
                        && OutSpeed_String != "") {
                    inAndOutSpeed = Double.parseDouble(inSpeed_String) + Double.parseDouble(OutSpeed_String);
                }
                inAndOut += inAndOutSpeed;
            }
            if (Double.isNaN(inAndOut)) {
                log.error("未获取到交换机网络吞吐量...");
                map.put("netInOut", 0.0);
            } else {
                map.put("netInOut", Double.parseDouble(String.format("%.2f", inAndOut / 1024 / 1024 / 1024)));
            }
            return map;
        } catch (Exception e) {
            log.error("获取交换机网络吞吐量出错" + e.getMessage());
            HashMap<String, Double> map = new HashMap<>();
            map.put("netInOut", 0.0);
            return map;
        }
    }

    /**
     * 获取端口详情
     *
     * @param snmpUtils
     * @return
     */
    private Map<String, Object> getPortInfoList(SNMPUtils snmpUtils, String tableTopOid) {

        HashMap<String, Object> resultMap = new HashMap<>();

        Double inAndOutBefore = 0.00;
        Double inAndOutAfter = 0.00;
        Double inTotalBefore = 0.00;
        Double inTotalAfter = 0.00;
        Double outTotalBefore = 0.00;
        Double outTotalAfter = 0.00;

        try {
            List<Map<String, Object>> portInfoList = new ArrayList<Map<String, Object>>();
            List<Map<String, Object>> portInfoTempList = new ArrayList<>();

            long oneBeginTime = System.currentTimeMillis();
            JSONObject tableData = snmpUtils.getPduTableView(tableTopOid);
            if (tableData != null && !tableData.isEmpty()) {
                JSONArray dataArray = tableData.getJSONArray("dataList");
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject eleObject = dataArray.getJSONObject(i);
                    Map<String, Object> res = new HashMap<>();

                    //索引
                    res.put("index", eleObject.getString(SnmpConstant.indexOid));

                    // 接口描述
                    res.put("portDesc", eleObject.getString(SnmpConstant.descriptionOid));

                    //端口类型
                    res.put("portType", eleObject.getString(SnmpConstant.typeOid));

                    //端口状态
                    String str = eleObject.getString(SnmpConstant.statusOid);
                    if ("1".equals(str.trim())) {
                        res.put("portStatus", "连接");
                    } else if ("2".equals(str.trim())) {
                        res.put("portStatus", "关闭");
                    } else {
                        res.put("portStatus", "其他");
                    }

                    // 带宽
                    String bandWidth_string = eleObject.getString(SnmpConstant.bandWidthOid);
                    if (StringUtils.isNotEmpty(bandWidth_string)) {
                        Double bandWidth = Double.parseDouble(bandWidth_string);
                        //原始单位为bit/s,转换为Mbit/s
                        res.put("bandWidth", Double.valueOf(String.format("%.2f", bandWidth)));
                    }

                    //输入流量
                    String inFlow = eleObject.getString(SnmpConstant.inFlowOid);
                    if (StringUtils.isNotEmpty(inFlow)) {
                        //原始单位为Byte,转换为MByte
                        Double inputFlow = Double.parseDouble(inFlow);
                        inAndOutBefore += inputFlow;
                        inTotalBefore += inputFlow;
                        res.put("inputFlow", String.format("%.2f", inputFlow));
                    }

                    //输出流量
                    String outFlow = eleObject.getString(SnmpConstant.outFlowOid);
                    if (StringUtils.isNotEmpty(outFlow)) {
                        //原始单位为Byte,转换为MByte
                        Double outputFlow = Double.parseDouble(outFlow);
                        inAndOutBefore += outputFlow;
                        outTotalBefore += outputFlow;
                        res.put("outputFlow", String.format("%.2f", outputFlow));
                    }

                    //输入错误包数
                    double inErrorPackage = 0.0;
                    String inErrorPackageString = eleObject.getString(SnmpConstant.inErrorsOid);
                    if (StringUtils.isNotEmpty(inErrorPackageString)) {
                        inErrorPackage = Double.parseDouble(inErrorPackageString);
                        res.put("inErrorPackage", String.format("%.2f", inErrorPackage));
                    }

                    //输出错误包数
                    double outErrorPackage = 0.0;
                    String outErrorPackageString = eleObject.getString(SnmpConstant.outErrorsOid);
                    if (StringUtils.isNotEmpty(outErrorPackageString)) {
                        outErrorPackage = Double.parseDouble(outErrorPackageString);
                        res.put("outErrorPackage", String.format("%.2f", outErrorPackage));
                    }

                    //输入丢失错误包数
                    double inLossPackage = 0.0;
                    String inLossPackageString = eleObject.getString(SnmpConstant.inLossPackageOid);
                    if (StringUtils.isNotEmpty(inLossPackageString)) {
                        inLossPackage = Double.parseDouble(inLossPackageString);
                        res.put("inLossPackage", String.format("%.2f", inLossPackage));
                    }

                    //输出丢失错误包数
                    double outLossPackage = 0.0;
                    String outLossPackageString = eleObject.getString(SnmpConstant.outLossPackageOid);
                    if (StringUtils.isNotEmpty(outLossPackageString)) {
                        outLossPackage = Double.parseDouble(outLossPackageString);
                        res.put("outLossPackage", String.format("%.2f", outLossPackage));
                    }

                    //输入单播报文的个数
                    double inUcastPkts = 0.0;
                    String inUcastPktsStr = eleObject.getString(SnmpConstant.inUcastPktsOid);
                    if (StringUtils.isNotEmpty(inUcastPktsStr) && !inUcastPktsStr.equals("noSuchInstance")) {
                        inUcastPkts = Double.parseDouble(inUcastPktsStr);
                        res.put("inUcastPkts", String.format("%.2f", inUcastPkts));
                    }

                    //输入非单播报文的个数
                    double inNUcastPkts = 0.0;
                    String inNUcastPktsStr = eleObject.getString(SnmpConstant.inNUcastPktsOid);
                    if (StringUtils.isNotEmpty(inNUcastPktsStr) && !inNUcastPktsStr.equals("noSuchInstance")) {
                        inNUcastPkts = Double.parseDouble(inNUcastPktsStr);
                        res.put("inNUcastPkts", String.format("%.2f", inNUcastPkts));
                    }

                    //输出单播报文的个数
                    double outUcastPkts = 0.0;
                    String outUcastPktsStr = eleObject.getString(SnmpConstant.outUcastPktsOid);
                    if (StringUtils.isNotEmpty(outUcastPktsStr) && !outUcastPktsStr.equals("noSuchInstance")) {
                        outUcastPkts = Double.parseDouble(outUcastPktsStr);
                        res.put("outUcastPkts", String.format("%.2f", outUcastPkts));
                    }

                    //输出非单播报文的个数
                    double outNUcastPkts = 0.0;
                    String outNUcastPktsStr = eleObject.getString(SnmpConstant.outNUcastPktsOid);
                    if (StringUtils.isNotEmpty(outNUcastPktsStr) && !outNUcastPktsStr.equals("noSuchInstance")) {
                        outNUcastPkts = Double.parseDouble(outNUcastPktsStr);
                        res.put("outNUcastPkts", String.format("%.2f", outNUcastPkts));
                    }

//                    //输出错误包率
//                    if (outUcastPkts > 0 && outNUcastPkts > 0) {
//                        double rate = outErrorPackage / (outUcastPkts + outNUcastPkts) * 100;
//                        res.put("outErrorPackageRate", Double.valueOf(String.format("%.2f", rate)));
//                    } else {
//                        res.put("outErrorPackageRate", 0.0);
//                    }
//
//                    //输入错误包率
//                    if (inUcastPkts > 0 && inNUcastPkts > 0) {
//                        double rate = inErrorPackage / (inUcastPkts + inNUcastPkts) * 100;
//                        res.put("inErrorPackageRate", Double.valueOf(String.format("%.2f", rate)));
//                    } else {
//                        res.put("inErrorPackageRate", 0.0);
//                    }
//
//                    //输出丢包率
//                    if (outUcastPkts > 0 && outNUcastPkts > 0) {
//                        double rate = outLossPackage / (outUcastPkts + outNUcastPkts) * 100;
//                        res.put("outLossPackageRate", Double.valueOf(String.format("%.2f", rate)));
//                    } else {
//                        res.put("outLossPackageRate", 0.0);
//                    }
//
//                    //输入丢包率
//                    if (inUcastPkts > 0 && inNUcastPkts > 0) {
//                        double rate = inLossPackage / (inUcastPkts + inNUcastPkts) * 100;
//                        res.put("inLossPackageRate", Double.valueOf(String.format("%.2f", rate)));
//                    } else {
//                        res.put("inLossPackageRate", 0.0);
//                    }

                    portInfoList.add(res);
                }
            }


            // 间隔一秒再次读取数据 -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=
            Thread.sleep(5000);

            long twoBeginTime = System.currentTimeMillis();
            JSONObject tableDataTemp = snmpUtils.getPduTableView(tableTopOid);
            if (tableDataTemp != null && !tableDataTemp.isEmpty()) {
                JSONArray dataArray = tableDataTemp.getJSONArray("dataList");
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject eleObject = dataArray.getJSONObject(i);
                    Map<String, Object> res = new HashMap<>();

                    //索引
                    res.put("index", eleObject.getString(SnmpConstant.indexOid));

                    //输入流量
                    String inFlow = eleObject.getString(SnmpConstant.inFlowOid);
                    if (StringUtils.isNotEmpty(inFlow)) {
                        //原始单位为Byte,转换为MByte
                        Double inputFlow = Double.parseDouble(inFlow);
                        inAndOutAfter += inputFlow;
                        inTotalAfter += inputFlow;
                        res.put("inputFlow", String.format("%.2f", inputFlow));
                    }

                    //输出流量
                    String outFlow = eleObject.getString(SnmpConstant.outFlowOid);
                    if (StringUtils.isNotEmpty(outFlow)) {
                        //原始单位为Byte,转换为MByte
                        Double outputFlow = Double.parseDouble(outFlow);
                        inAndOutAfter += outputFlow;
                        outTotalAfter += outputFlow;
                        res.put("outputFlow", String.format("%.2f", outputFlow));
                    }

                    //输入错误包数
                    double inErrorPackage = 0.0;
                    String inErrorPackageString = eleObject.getString(SnmpConstant.inErrorsOid);
                    if (StringUtils.isNotEmpty(inErrorPackageString)) {
                        inErrorPackage = Double.parseDouble(inErrorPackageString);
                        res.put("inErrorPackage", String.format("%.2f", inErrorPackage));
                    }

                    //输出错误包数
                    double outErrorPackage = 0.0;
                    String outErrorPackageString = eleObject.getString(SnmpConstant.outErrorsOid);
                    if (StringUtils.isNotEmpty(outErrorPackageString)) {
                        outErrorPackage = Double.parseDouble(outErrorPackageString);
                        res.put("outErrorPackage", String.format("%.2f", outErrorPackage));
                    }

                    //输入丢失错误包数
                    double inLossPackage = 0.0;
                    String inLossPackageString = eleObject.getString(SnmpConstant.inLossPackageOid);
                    if (StringUtils.isNotEmpty(inLossPackageString)) {
                        inLossPackage = Double.parseDouble(inLossPackageString);
                        res.put("inLossPackage", String.format("%.2f", inLossPackage));
                    }

                    //输出丢失错误包数
                    double outLossPackage = 0.0;
                    String outLossPackageString = eleObject.getString(SnmpConstant.outLossPackageOid);
                    if (StringUtils.isNotEmpty(outLossPackageString)) {
                        outLossPackage = Double.parseDouble(outLossPackageString);
                        res.put("outLossPackage", String.format("%.2f", outLossPackage));
                    }
                    //输入单播报文的个数
                    double inUcastPkts = 0.0;
                    String inUcastPktsStr = eleObject.getString(SnmpConstant.inUcastPktsOid);
                    if (StringUtils.isNotEmpty(inUcastPktsStr) && !inUcastPktsStr.equals("noSuchInstance")) {
                        inUcastPkts = Double.parseDouble(inUcastPktsStr);
                        res.put("inUcastPkts", String.format("%.2f", inUcastPkts));
                    }

                    //输入非单播报文的个数
                    double inNUcastPkts = 0.0;
                    String inNUcastPktsStr = eleObject.getString(SnmpConstant.inNUcastPktsOid);
                    if (StringUtils.isNotEmpty(inNUcastPktsStr) && !inNUcastPktsStr.equals("noSuchInstance")) {
                        inNUcastPkts = Double.parseDouble(inNUcastPktsStr);
                        res.put("inNUcastPkts", String.format("%.2f", inNUcastPkts));
                    }

                    //输出单播报文的个数
                    double outUcastPkts = 0.0;
                    String outUcastPktsStr = eleObject.getString(SnmpConstant.outUcastPktsOid);
                    if (StringUtils.isNotEmpty(outUcastPktsStr) && !outUcastPktsStr.equals("noSuchInstance")) {
                        outUcastPkts = Double.parseDouble(outUcastPktsStr);
                        res.put("outUcastPkts", String.format("%.2f", outUcastPkts));
                    }

                    //输出非单播报文的个数
                    double outNUcastPkts = 0.0;
                    String outNUcastPktsStr = eleObject.getString(SnmpConstant.outNUcastPktsOid);
                    if (StringUtils.isNotEmpty(outNUcastPktsStr) && !outNUcastPktsStr.equals("noSuchInstance")) {
                        outNUcastPkts = Double.parseDouble(outNUcastPktsStr);
                        res.put("outNUcastPkts", String.format("%.2f", outNUcastPkts));
                    }
                    portInfoTempList.add(res);
                }
            }

            long timeDiff = (twoBeginTime - oneBeginTime) / 1000;

            resultMap.put("netInOut", inAndOutAfter);
            Double inoutSpeedValue = (inAndOutAfter - inAndOutBefore) / timeDiff;
            resultMap.put("inoutSpeed", inoutSpeedValue < 0 ? 0 : inoutSpeedValue);
            Double inSpeedValue = (inTotalAfter - inTotalBefore) / timeDiff;
            resultMap.put("inSpeed", inSpeedValue < 0 ? 0 : inSpeedValue);
            Double outSpeedValue = (outTotalAfter - outTotalBefore) / timeDiff;
            resultMap.put("outSpeed", outSpeedValue < 0 ? 0 : outSpeedValue);

            List<Map<String, Object>> portInfoList2 = new ArrayList<>();
            for (Map<String, Object> map : portInfoList) {
                String str1 = (String) map.get("index");
                Integer index = Integer.valueOf(str1);
                for (Map<String, Object> map1 : portInfoTempList) {
                    String str2 = (String) map1.get("index");
                    Integer index1 = Integer.valueOf(str2);
                    if (Objects.equals(index, index1)) {
                        Double in = Double.valueOf(StringUtils.isNotEmpty(map.get("inputFlow") + "") ? map.get("inputFlow") + "" : "0");
                        Double out = Double.valueOf(StringUtils.isNotEmpty(map.get("outputFlow") + "") ? map.get("outputFlow") + "" : "0");
                        Double in1 = Double.valueOf(StringUtils.isNotEmpty(map1.get("inputFlow") + "") ? map1.get("inputFlow") + "" : "0");
                        Double out1 = Double.valueOf(StringUtils.isNotEmpty(map1.get("outputFlow") + "") ? map1.get("outputFlow") + "" : "0");

                        Double inOutSpeed1 = (in1 + out1 - in - out) / timeDiff;
                        map.put("inOutSpeed", inOutSpeed1);
                        Double inSpeed = (in1 - in) / timeDiff;
                        map.put("inSpeed", inSpeed);
                        Double outSpeed = (out1 - out) / timeDiff;
                        map.put("outSpeed", outSpeed);

                        //输入错误包数
                        Double inErrorPackage = Double.valueOf(StringUtils.isNotEmpty(map.get("inErrorPackage") + "") ? map.get("inErrorPackage") + "" : "0");
                        //输出错误包数
                        Double outErrorPackage = Double.valueOf(StringUtils.isNotEmpty(map.get("outErrorPackage") + "") ? map.get("outErrorPackage") + "" : "0");
                        //输入丢失错误包数
                        Double inLossPackage = Double.valueOf(StringUtils.isNotEmpty(map.get("inLossPackage") + "") ? map.get("inLossPackage") + "" : "0");
                        //输出丢失错误包数
                        Double outLossPackage = Double.valueOf(StringUtils.isNotEmpty(map.get("outLossPackage") + "") ? map.get("outLossPackage") + "" : "0");
                        //输入单播报文的个数
                        Double inUcastPkts = Double.valueOf(StringUtils.isNotEmpty(map.get("inUcastPkts") + "") ? map.get("inUcastPkts") + "" : "0");
                        //输入非单播报文的个数
                        Double inNUcastPkts = Double.valueOf(StringUtils.isNotEmpty(map.get("inNUcastPkts") + "") ? map.get("inNUcastPkts") + "" : "0");
                        //输出单播报文的个数
                        Double outUcastPkts = Double.valueOf(StringUtils.isNotEmpty(map.get("outUcastPkts") + "") ? map.get("outUcastPkts") + "" : "0");
                        //输出非单播报文的个数
                        Double outNUcastPkts = Double.valueOf(StringUtils.isNotEmpty(map.get("outNUcastPkts") + "") ? map.get("outNUcastPkts") + "" : "0");

                        Double inErrorPackage1 = Double.valueOf(StringUtils.isNotEmpty(map1.get("inErrorPackage") + "") ? map1.get("inErrorPackage") + "" : "0");
                        Double outErrorPackage1 = Double.valueOf(StringUtils.isNotEmpty(map1.get("outErrorPackage") + "") ? map1.get("outErrorPackage") + "" : "0");
                        Double inLossPackage1 = Double.valueOf(StringUtils.isNotEmpty(map1.get("inLossPackage") + "") ? map1.get("inLossPackage") + "" : "0");
                        Double outLossPackage1 = Double.valueOf(StringUtils.isNotEmpty(map1.get("outLossPackage") + "") ? map1.get("outLossPackage") + "" : "0");
                        Double inUcastPkts1 = Double.valueOf(StringUtils.isNotEmpty(map1.get("inUcastPkts") + "") ? map1.get("inUcastPkts") + "" : "0");
                        Double inNUcastPkts1 = Double.valueOf(StringUtils.isNotEmpty(map1.get("inNUcastPkts") + "") ? map1.get("inNUcastPkts") + "" : "0");
                        Double outUcastPkts1 = Double.valueOf(StringUtils.isNotEmpty(map1.get("outUcastPkts") + "") ? map1.get("outUcastPkts") + "" : "0");
                        Double outNUcastPkts1 = Double.valueOf(StringUtils.isNotEmpty(map1.get("outNUcastPkts") + "") ? map1.get("outNUcastPkts") + "" : "0");

                        double inNum = inUcastPkts1 + inNUcastPkts1 - inUcastPkts - inNUcastPkts;
                        double outNum = outUcastPkts1 + outNUcastPkts1 - outUcastPkts - outNUcastPkts;
                        //输出错误包率
                        if (outNum > 0) {
                            double rate = (outErrorPackage1 - outErrorPackage) / outNum * 100;
                            map.put("outErrorPackageRate", Double.valueOf(String.format("%.2f", rate)));
                        } else {
                            map.put("outErrorPackageRate", 0.0);
                        }

                        //输入错误包率
                        if (inNum > 0) {
                            double rate = (inErrorPackage1 - inErrorPackage) / inNum * 100;
                            map.put("inErrorPackageRate", Double.valueOf(String.format("%.2f", rate)));
                        } else {
                            map.put("inErrorPackageRate", 0.0);
                        }

                        //输出丢包率
                        if (outNum > 0) {
                            double rate = (outLossPackage1 - outLossPackage) / outNum * 100;
                            map.put("outLossPackageRate", Double.valueOf(String.format("%.2f", rate)));
                        } else {
                            map.put("outLossPackageRate", 0.0);
                        }

                        //输入丢包率
                        if (inNum > 0) {
                            double rate = (inLossPackage1 - inLossPackage) / inNum * 100;
                            map.put("inLossPackageRate", Double.valueOf(String.format("%.2f", rate)));
                        } else {
                            map.put("inLossPackageRate", 0.0);
                        }
                    }
                }
                portInfoList2.add(map);
            }

            resultMap.put("portInfo", portInfoList2);
            return resultMap;
        } catch (Exception e) {
            log.error("获取端口信息异常！", e);
            return null;
        }
    }
}
