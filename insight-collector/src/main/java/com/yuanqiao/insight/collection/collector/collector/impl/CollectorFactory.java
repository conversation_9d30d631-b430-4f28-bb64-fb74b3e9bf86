package com.yuanqiao.insight.collection.collector.collector.impl;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CollectorFactory {
    /*public static Collector getCollector(String protocol) {

        switch (protocol) {
            case "SNMP":
                return new OsSnmpCollector_Common();
            case "ZGCloudAPI":
                return new ZGCloudCollector();
            case "ZGHostAPI":
                return new ZGHostCollector();

        }
        log.error("没有找到采集器, 未知协议:" + protocol);
        return new EmptyCollector();
    }*/
}
