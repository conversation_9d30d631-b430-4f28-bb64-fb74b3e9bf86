package com.yuanqiao.insight.collection.collector.collector.impl.Snmp.StorageSnmp;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.collection.collector.utils.SNMPCodecAndCollectUtils;
import com.yuanqiao.insight.collection.collector.utils.SNMPCodecInterface;
import com.yuanqiao.insight.common.util.common.TimeUtils;
import com.yuanqiao.insight.common.util.snmp.SNMPUtils;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigInteger;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class StSnmpCodec_Shark_Double implements SNMPCodecInterface {
    // 操作系统类型
    String osType;

    public void setOsType(String osType) {
        this.osType = osType;
    }

    //最外层jsonObject
    JSONObject jsonObject = new JSONObject();
    //公共结果集
    JSONObject commonMap = new JSONObject();

    @Override
    public JSONObject dataCodec(SNMPUtils snmpUtils, List<ProertyMetadata> metadataList, String stcaKey, Device device, SNMPCodecAndCollectUtils snmpCodecAndCollectUtils) {
        //清空外层jsonObject和公共结果集commonMap
        jsonObject.clear();
        commonMap.clear();

        //静态属性
        HashMap<String, Object> staticInfoMap = new HashMap<>();

        staticInfoMap.put("switchName", getName(snmpUtils).get("switchName"));
        staticInfoMap.put("switchDesc", getSysDesc(snmpUtils).get("switchDesc"));
        staticInfoMap.put("portNum", getPortNum(snmpUtils).get("portNum"));
        staticInfoMap.put("runTime", getRunTime(snmpUtils).get("runTime"));
        commonMap.put("staticInfo", staticInfoMap);
        commonMap.put("sysUpTime", getSysUpTime(snmpUtils).get("sysUpTime"));

        //Raid阵列信息
        commonMap.put("raidInfo", Objects.requireNonNull(getRaidInfoList(snmpUtils, "1.3.6.1.4.1.38696.2.2.2")).get("raidInfo"));
        //磁盘SMART信息
        commonMap.put("diskSmartInfo", Objects.requireNonNull(getDiskSmartInfoList(snmpUtils, "1.3.6.1.4.1.38696.2.1.2")).get("diskSmartInfo"));
        //光纤通道信息
        commonMap.put("fcNotifyInfo", Objects.requireNonNull(getFcNotifyInfoList(snmpUtils, "1.3.6.1.4.1.38696.2.12.2")).get("fcNotifyInfo"));
        //网络接口信息
        commonMap.put("interfaceInfo", Objects.requireNonNull(getInterfaceInfoList(snmpUtils, "1.3.6.1.4.1.38696.2.6.2")).get("interfaceInfo"));
        //存储池信息
        commonMap.put("poolInfo", Objects.requireNonNull(getPoolInfoList(snmpUtils, "1.3.6.1.4.1.38696.2.5.2")).get("poolInfo"));

        metadataList.forEach(item -> {
            if (StringUtils.isNotEmpty(item.getDataType())) {
                jsonObject = snmpCodecAndCollectUtils.switchByDataType(item, stcaKey, jsonObject, commonMap, snmpUtils);
            }
        });
        return jsonObject;
    }

    /* ---------------------------------------------------------------------------------------------------------------- */

    /**
     * 获取设备运行时长
     *
     * @param snmpUtils
     * @return
     */
    private Map<String, String> getSysUpTime(SNMPUtils snmpUtils) {
        HashMap<String, String> map = new HashMap<>();
        try {
            String sysUpTime = snmpUtils.getPDU("1.3.6.1.2.1.1.3.0");
            if (StringUtils.isNotEmpty(sysUpTime) && !sysUpTime.equalsIgnoreCase("noSuchObject")
                    && !sysUpTime.equalsIgnoreCase("noSuchInstance")) {
                // 去除字符串中的逗号
                sysUpTime = sysUpTime.replace(",", "");

                // 使用正则表达式匹配时间信息
                Pattern pattern = Pattern.compile("(?:(\\d+) days?)?(?:\\s*,?\\s*(\\d+):(\\d+)(?::(\\d+(?:\\.\\d+)?))?)?");
                Matcher matcher = pattern.matcher(sysUpTime);

                int days = 0, hours = 0, minutes = 0, seconds = 0;
                double milliseconds = 0;

                if (matcher.find()) {
                    if (matcher.group(1) != null) {
                        days = Integer.parseInt(matcher.group(1));
                    }
                    if (matcher.group(2) != null) {
                        hours = Integer.parseInt(matcher.group(2));
                    }
                    if (matcher.group(3) != null) {
                        minutes = Integer.parseInt(matcher.group(3));
                    }
                    if (matcher.group(4) != null) {
                        String[] secondsParts = matcher.group(4).split("\\.");
                        seconds = Integer.parseInt(secondsParts[0]);
                        if (secondsParts.length > 1) {
                            milliseconds = Double.parseDouble("0." + secondsParts[1]);
                        }
                    }
                }
                // 计算总毫秒数
                BigInteger totalMilliseconds = BigInteger.valueOf(days * 24 * 60 * 60 * 1000L)
                        .add(BigInteger.valueOf(hours * 60 * 60 * 1000L))
                        .add(BigInteger.valueOf(minutes * 60 * 1000L))
                        .add(BigInteger.valueOf((long) (seconds * 1000 + milliseconds * 1000)));
                sysUpTime = TimeUtils.getDistanceTime(totalMilliseconds);
                map.put("sysUpTime", sysUpTime);
            }
        } catch (Exception e) {
            log.error("获取运行时长出错", e);
            map.put("sysUpTime", "");
        }
        return map;
    }


    //获取鲸鲨双控存储端口数
    private Map<String, Integer> getPortNum(SNMPUtils snmpUtils) {
        try {
            HashMap<String, Integer> map = new HashMap<>();
            // 鲸鲨双控存储端口数
            String portNum_String = snmpUtils.getPDU("1.3.6.1.2.1.2.1.0");
            if (StringUtils.isEmpty(portNum_String)) {
                log.error("未获取到鲸鲨双控存储端口数...");
                map.put("portNum", 0);
            } else {
                Integer portNum = Integer.parseInt(portNum_String);
                map.put("portNum", portNum);
            }
            return map;
        } catch (Exception e) {
            log.error("获取鲸鲨双控存储端口数出错", e);
            HashMap<String, Integer> map = new HashMap<>();
            map.put("portNum", 0);
            return map;
        }
    }

    //获取鲸鲨双控存储名称
    private Map<String, String> getName(SNMPUtils snmpUtils) {
        try {
            HashMap<String, String> map = new HashMap<>();
            // 鲸鲨双控存储名称
            String ifName = snmpUtils.getPDU("1.3.6.1.2.1.1.5.0");
            if (ifName == null || "".equals(ifName.trim()) || "null".equalsIgnoreCase(ifName.trim())) {
                log.error("未获取到鲸鲨双控存储名称...");
                map.put("switchName", "- -");
            } else {
                map.put("switchName", ifName);
            }
            return map;
        } catch (Exception e) {
            log.error("获取鲸鲨双控存储名称出错", e);
            HashMap<String, String> map = new HashMap<>();
            map.put("switchName", "- -");
            return map;
        }
    }

    //获取鲸鲨双控存储描述
    private Map<String, String> getSysDesc(SNMPUtils snmpUtils) {
        try {
            HashMap<String, String> map = new HashMap<>();
            // 鲸鲨双控存储描述
            String sysDesc = snmpUtils.getPDU("1.3.6.1.2.1.1.1.0");
            if (sysDesc == null || "".equals(sysDesc.trim()) || "null".equalsIgnoreCase(sysDesc.trim())) {
                log.error("未获取到鲸鲨双控存储描述...");
                map.put("switchDesc", "- -");
            }
            map.put("switchDesc", sysDesc);
            return map;
        } catch (Exception e) {
            log.error("获取鲸鲨双控存储描述出错", e);
            HashMap<String, String> map = new HashMap<>();
            map.put("switchDesc", "- -");
            return map;
        }
    }

    //获取鲸鲨双控存储运行时间
    private Map<String, String> getRunTime(SNMPUtils snmpUtils) {
        try {
            HashMap<String, String> map = new HashMap<>();
            // 鲸鲨双控存储运行时间
            String sysRunTime = snmpUtils.getPDU("1.3.6.1.2.1.1.3.0");
            if (sysRunTime == null || "".equals(sysRunTime.trim()) || "null".equalsIgnoreCase(sysRunTime.trim())) {
                log.error("未获取到鲸鲨双控运行时间...");
                map.put("runTime", "--");
            }
//            if (!"".equals(sysRunTime) && sysRunTime != null && !sysRunTime.isEmpty()) {
//                String[] categoryArr = sysRunTime.split(":");
//                String dayRep = categoryArr[0].replace("days", "天");
//                String seconds = categoryArr[2].substring(0, 2);
//                sysRunTime = dayRep + ":" + categoryArr[1] + ":" + seconds;
//            }
            map.put("runTime", sysRunTime);
            return map;
        } catch (Exception e) {
            log.error("获取鲸鲨双控存储运行时间出错", e);
            HashMap<String, String> map = new HashMap<>();
            map.put("runTime", "--");
            return map;
        }
    }


    /**
     * Raid阵列信息
     *
     * @param snmpUtils
     * @param tableTopOid
     * @return
     */
    private Map<String, Object> getRaidInfoList(SNMPUtils snmpUtils, String tableTopOid) {
        HashMap<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> raidInfoList = new ArrayList<>();
        try {
            JSONObject tableData = snmpUtils.getPduTableView(tableTopOid);
            if (tableData != null && !tableData.isEmpty()) {
                JSONArray dataArray = tableData.getJSONArray("dataList");
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject eleObject = dataArray.getJSONObject(i);
                    Map<String, Object> res = new HashMap<>();
                    //索引
                    res.put("mdRaidArrayIndex", eleObject.getString("1.3.6.1.4.1.38696.2.2.2.1.1"));
                    //设备名称
                    res.put("mdRaidArrayDev", eleObject.getString("1.3.6.1.4.1.38696.2.2.2.1.2"));
                    //版本号
                    res.put("mdRaidArrayVersion", eleObject.getString("1.3.6.1.4.1.38696.2.2.2.1.3"));
                    //raid UUID号
                    res.put("mdRaidArrayUUID", eleObject.getString("1.3.6.1.4.1.38696.2.2.2.1.4"));
                    //raid级别
                    res.put("mdRaidArrayLevel", eleObject.getString("1.3.6.1.4.1.38696.2.2.2.1.5"));
                    //阵列布局N/A
                    res.put("mdRaidArrayLayout", eleObject.getString("1.3.6.1.4.1.38696.2.2.2.1.6"));
                    //数据块大小
                    res.put("mdRaidArrayChunkSize", eleObject.getString("1.3.6.1.4.1.38696.2.2.2.1.7"));
                    //阵列的总容量
                    res.put("mdRaidArraySize", eleObject.getString("1.3.6.1.4.1.38696.2.2.2.1.8"));
                    //单个成员磁盘的大小
                    res.put("mdRaidArrayDeviceSize", eleObject.getString("1.3.6.1.4.1.38696.2.2.2.1.9"));
                    //raid健康状态
                    res.put("mdRaidArrayHealthOK", eleObject.getString("1.3.6.1.4.1.38696.2.2.2.1.10"));
                    //raid中是否有坏盘
                    res.put("mdRaidArrayHasFailedComponents", eleObject.getString("1.3.6.1.4.1.38696.2.2.2.1.11"));
                    //是否有热备盘
                    res.put("mdRaidArrayHasAvailableSpares  ", eleObject.getString("1.3.6.1.4.1.38696.2.2.2.1.12"));
                    //总盘数
                    res.put("mdRaidArrayTotalComponents", eleObject.getString("1.3.6.1.4.1.38696.2.2.2.1.13"));
                    //激活的盘数
                    res.put("mdRaidArrayActiveComponents", eleObject.getString("1.3.6.1.4.1.38696.2.2.2.1.14"));
                    //工作中的盘数
                    res.put("mdRaidArrayWorkingComponents", eleObject.getString("1.3.6.1.4.1.38696.2.2.2.1.15"));
                    //损坏的盘数
                    res.put("mdRaidArrayFailedComponents", eleObject.getString("1.3.6.1.4.1.38696.2.2.2.1.16"));
                    //热备盘数
                    res.put("mdRaidArraySpareComponents", eleObject.getString("1.3.6.1.4.1.38696.2.2.2.1.17"));
                    raidInfoList.add(res);
                }
            }
            resultMap.put("raidInfo", raidInfoList);
            return resultMap;
        } catch (Exception e) {
            log.error("获取Raid信息出错！", e);
            return null;
        }
    }


    /**
     * 磁盘SMART信息
     *
     * @param snmpUtils
     * @param tableTopOid
     * @return
     */
    private Map<String, Object> getDiskSmartInfoList(SNMPUtils snmpUtils, String tableTopOid) {
        HashMap<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> diskHealthInfoList = new ArrayList<>();
        try {
            JSONObject tableData = snmpUtils.getPduTableView(tableTopOid);
            if (tableData != null && !tableData.isEmpty()) {
                JSONArray dataArray = tableData.getJSONArray("dataList");
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject eleObject = dataArray.getJSONObject(i);
                    Map<String, Object> res = new HashMap<>();

                    //索引
                    res.put("smartCtlDeviceIndex", eleObject.getString("1.3.6.1.4.1.38696.2.1.2.1.1"));
                    //磁盘名称
                    res.put("smartCtlDeviceDev", eleObject.getString("1.3.6.1.4.1.38696.2.1.2.1.2"));
                    //磁盘厂商
                    res.put("smartCtlDeviceModelFamily", eleObject.getString("1.3.6.1.4.1.38696.2.1.2.1.3"));
                    //磁盘型号
                    res.put("smartCtlDeviceDeviceModel", eleObject.getString("1.3.6.1.4.1.38696.2.1.2.1.4"));
                    //磁盘序列号
                    res.put("smartCtlDeviceSerialNumber", eleObject.getString("1.3.6.1.4.1.38696.2.1.2.1.5"));
                    //磁盘容量
                    res.put("smartCtlDeviceUserCapacity", eleObject.getString("1.3.6.1.4.1.38696.2.1.2.1.6"));
                    //ATA版本号
                    res.put("smartCtlDeviceATAVersion", eleObject.getString("1.3.6.1.4.1.38696.2.1.2.1.7"));
                    //健康状态
                    res.put("smartCtlDeviceHealthOK", eleObject.getString("1.3.6.1.4.1.38696.2.1.2.1.8"));
                    //磁盘温度
                    res.put("temperatureCelsius", eleObject.getString("1.3.6.1.4.1.38696.2.1.2.1.9"));

                    diskHealthInfoList.add(res);
                }
            }
            resultMap.put("diskHealthInfo", diskHealthInfoList);
            return resultMap;
        } catch (Exception e) {
            log.error("获取磁盘SMART健康状态信息出错！", e);
            return null;
        }
    }

    /**
     * 光纤通道信息
     *
     * @param snmpUtils
     * @param tableTopOid
     * @return
     */
    private Map<String, Object> getFcNotifyInfoList(SNMPUtils snmpUtils, String tableTopOid) {
        HashMap<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> fcNotifyInfoList = new ArrayList<>();
        try {
            JSONObject tableData = snmpUtils.getPduTableView(tableTopOid);
            if (tableData != null && !tableData.isEmpty()) {
                JSONArray dataArray = tableData.getJSONArray("dataList");
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject eleObject = dataArray.getJSONObject(i);
                    Map<String, Object> res = new HashMap<>();

                    //索引
                    res.put("fcNotifyIndex", eleObject.getString("1.3.6.1.4.1.38696.2.12.2.1.1"));
                    //名称
                    res.put("fcName", eleObject.getString("1.3.6.1.4.1.38696.2.12.2.1.2"));
                    //状态
                    res.put("fcState", eleObject.getString("1.3.6.1.4.1.38696.2.12.2.1.3"));
                    //速率
                    res.put("fcSpeed", eleObject.getString("1.3.6.1.4.1.38696.2.12.2.1.4"));

                    fcNotifyInfoList.add(res);
                }
            }
            resultMap.put("fcNotifyInfo", fcNotifyInfoList);
            return resultMap;
        } catch (Exception e) {
            log.error("获取光纤通道信息出错！", e);
            return null;
        }
    }

    /**
     * 网络接口信息
     *
     * @param snmpUtils
     * @param tableTopOid
     * @return
     */
    private Map<String, Object> getInterfaceInfoList(SNMPUtils snmpUtils, String tableTopOid) {
        HashMap<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> interfaceInfoList = new ArrayList<>();
        try {
            JSONObject tableData = snmpUtils.getPduTableView(tableTopOid);
            if (tableData != null && !tableData.isEmpty()) {
                JSONArray dataArray = tableData.getJSONArray("dataList");
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject eleObject = dataArray.getJSONObject(i);
                    Map<String, Object> res = new HashMap<>();

                    //索引
                    res.put("interfaceIndex", eleObject.getString("1.3.6.1.4.1.38696.2.32.2.1.1"));
                    //网口名称
                    res.put("interfaceName", eleObject.getString("1.3.6.1.4.1.38696.2.32.2.1.2"));
                    //网口模式
                    res.put("interfaceMode", eleObject.getString("1.3.6.1.4.1.38696.2.32.2.1.3"));
                    //网口连接状态
                    res.put("interfaceLink", eleObject.getString("1.3.6.1.4.1.38696.2.32.2.1.4"));
                    //网口启动状态
                    res.put("interfaceUp", eleObject.getString("1.3.6.1.4.1.38696.2.32.2.1.5"));
                    //网口动/静态
                    res.put("interfaceBootproto", eleObject.getString("1.3.6.1.4.1.38696.2.32.2.1.6"));

                    interfaceInfoList.add(res);
                }
            }
            resultMap.put("interfaceInfo", interfaceInfoList);
            return resultMap;
        } catch (Exception e) {
            log.error("获取网络接口信息出错！", e);
            return null;
        }
    }

    /**
     * 存储池信息
     *
     * @param snmpUtils
     * @param tableTopOid
     * @return
     */
    private Map<String, Object> getPoolInfoList(SNMPUtils snmpUtils, String tableTopOid) {
        HashMap<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> poolInfoList = new ArrayList<>();
        try {
            JSONObject tableData = snmpUtils.getPduTableView(tableTopOid);
            if (tableData != null && !tableData.isEmpty()) {
                JSONArray dataArray = tableData.getJSONArray("dataList");
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject eleObject = dataArray.getJSONObject(i);
                    Map<String, Object> res = new HashMap<>();

                    //索引
                    res.put("poolIndex", eleObject.getString("1.3.6.1.4.1.38696.2.21.2.1.1"));
                    //存储池名称
                    res.put("poolName", eleObject.getString("1.3.6.1.4.1.38696.2.21.2.1.2"));
                    //存储池使用率
                    res.put("poolUsed", eleObject.getString("1.3.6.1.4.1.38696.2.21.2.1.3"));
                }
            }
            resultMap.put("poolInfo", poolInfoList);
            return resultMap;
        } catch (Exception e) {
            log.error("获取存储池信息出错！", e);
            return null;
        }
    }
}
