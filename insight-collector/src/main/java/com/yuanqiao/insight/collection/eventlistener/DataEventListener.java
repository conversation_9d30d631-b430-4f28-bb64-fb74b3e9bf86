package com.yuanqiao.insight.collection.eventlistener;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.statuscache.Attributes;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class DataEventListener{


    public void validateRules(String msg) {
        Attributes attributes =new Attributes();
        JSONObject jsonObject = JSONObject.parseObject(msg);
        attributes.setKey((String) jsonObject.get("key"));
        attributes.setAttributes((Map<String, Object>) jsonObject.get("attributes"));
       // alarmService.validateRules(attributes);
    }

}
