package com.yuanqiao.insight.collection.collector.collector.impl.MiddleWareMbsc;

import com.yuanqiao.insight.acore.collector.Collector;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.acore.scheduler.SchedulerManagerInter;
import com.yuanqiao.insight.acore.utils.SpringContextUtil;
import com.yuanqiao.insight.collection.collector.utils.MiddleWareCodecUtils;
import com.yuanqiao.insight.utils.SNMPMetadataUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;

import javax.management.MBeanServerConnection;
import javax.management.remote.JMXConnector;
import javax.management.remote.JMXConnectorFactory;
import javax.management.remote.JMXServiceURL;
import java.util.Hashtable;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 东方通7
 */
@Slf4j
@EnableAsync
public class TongWebMbscController implements Collector {
    private Device device;
    private TongWebMbscCodec tongWebMbscCodec;
    private SchedulerManagerInter schedulerManager;
    private JMXConnector connector = null;
    private JMXServiceURL serviceURL = null;
    private MBeanServerConnection mbsc;
    private String ip;
    private int port;
    private SNMPMetadataUtils metadataUtils;
    private RedisTemplate redisTemplate;
    private MiddleWareCodecUtils middleWareCodecUtils;
    /**
     * bes用户名
     */
    private String username = "";
    /**
     * bes密码
     */
    private String password = "";
    private String URL;
    public static volatile ReadWriteLock readWriteLock = new ReentrantReadWriteLock();

    //初始化
    @Override
    public void init(Device device, SchedulerManagerInter schedulerManager) {
        this.device = device;
        this.tongWebMbscCodec = new TongWebMbscCodec();
        this.schedulerManager = schedulerManager;
        this.metadataUtils = (SNMPMetadataUtils) SpringContextUtil.getBean("SNMPMetadataUtils");
        this.redisTemplate = (RedisTemplate) SpringContextUtil.getBean("redisTemplate");
        this.middleWareCodecUtils = (MiddleWareCodecUtils) SpringContextUtil.getBean("middleWareCodecUtils");

        ip = device.getConnectParam().get("ip");
        String strPort = device.getConnectParam().get("port");
        port = Integer.valueOf(strPort);
        username = device.getConnectParam().get("username");
        password = device.getConnectParam().get("password");
        log.error("username===" + username + ";password===" + password);
        URL = "service:jmx:rmi:///jndi/rmi://" + ip + ":" + port + "/jmxrmi";

    }


    //执行方法
    @Async
    public void execute() {

        try {
            this.getJMXConnector();
            mbsc = connector.getMBeanServerConnection();
        } catch (Exception e) {
            log.error("设备 " + device.getKey() + " MBSC连接异常！", e);
            try {
                connector.close();
            } catch (Exception e1) {
                log.error("设备 " + device.getKey() + " 关闭MBSC连接异常！", e1);
            }
            connector = null;
        }

        middleWareCodecUtils.collectorMainLine(mbsc, "TongWeb东方通", metadataUtils, device, schedulerManager, redisTemplate, tongWebMbscCodec);

    }

    /**
     * 连接JMX
     *
     * @return
     */
    public JMXConnector getJMXConnector() {
        if (connector == null) {
            if (serviceURL == null) {
                try {
                    serviceURL = new JMXServiceURL(URL);
                } catch (Exception e) {
                    log.error("new serviceURL exception:" + e);
                    serviceURL = null;
                    return null;
                }
            }
            Hashtable<String, Object> map = new Hashtable<String, Object>();
            String[] credentials = new String[]{username, password};
            map.put("jmx.remote.credentials", credentials);
            log.info("东方通中间件执行了正在连接测试-----------------------");
            try {
                readWriteLock.writeLock().lock();
                connector = JMXConnectorFactory.connect(serviceURL, map);
            } catch (Exception e) {
                log.error(ip + " , new connect exception:" + e);
                connector = null;
            } finally {
                readWriteLock.writeLock().unlock();
            }
        }
        return connector;

    }
}
