package com.yuanqiao.insight.collection.collector.collector.impl.MiddleWareMbsc;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.collection.collector.utils.DataBaseCodecUtils;
import com.yuanqiao.insight.collection.collector.utils.JMXUtils;
import com.yuanqiao.insight.collection.collector.utils.MiddleWareCodecInterface;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.management.MBeanServerConnection;
import javax.management.ObjectName;
import javax.management.openmbean.CompositeDataSupport;
import java.lang.management.MemoryUsage;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class KisMbscCodec implements MiddleWareCodecInterface{
    DataBaseCodecUtils dataBaseCodecUtils = new DataBaseCodecUtils();

    private DecimalFormat df1 = new DecimalFormat("0.00");
    //最外层jsonObject
    JSONObject jsonObject = new JSONObject();
    //公共结果集
    JSONObject commonMap = new JSONObject();

    @Override
    public JSONObject dataCodec(Object connection, List<ProertyMetadata> metadataList, Device device) {
        //清空外层jsonObject和公共结果集commonMap
        jsonObject.clear();
        commonMap.clear();

        MBeanServerConnection mbsc = (MBeanServerConnection) connection;

        //静态属性
        HashMap<String, Object> staticInfoMap = new HashMap<>();
        staticInfoMap.put("version", getVersion(mbsc).get("ApusicVersion"));
        staticInfoMap.put("startTime", getStartTime(mbsc).get("startTime"));
        staticInfoMap.put("runtime", getRunTime(mbsc).get("runtime"));
        staticInfoMap.put("vmVersion", getVmVersion(mbsc).get("vmVersion"));
        staticInfoMap.put("vmName", getVmName(mbsc).get("vmName"));
        staticInfoMap.put("vmVendor", getVmVendor(mbsc).get("vmVendor"));
        staticInfoMap.put("systemName", getSystemName(mbsc).get("systemName"));
        commonMap.put("staticInfo", staticInfoMap);
        commonMap.put("cpuRate", getCpuUse(mbsc).get("cpuUse"));
        commonMap.put("memTotal", getTotalPhysicalMemorySize(mbsc).get("totalMemory"));
        commonMap.put("memFrees", getFreePhysicalMemorySize(mbsc).get("freeMemory"));
        commonMap.put("memRate", getMemRate().get("memRate"));
        commonMap.put("maxMemory", getMaxHeapMemory(mbsc).get("maxHeapMemory"));
//        commonMap.put("nonMaxMemory", getMaxStackMemory(mbsc).get("maxStackMemory"));
        commonMap.put("jvmHeapUse", getHeapUse(mbsc).get("jvmHeapUse"));
        commonMap.put("jvmStackUse", getStackUse(mbsc).get("jvmStackUse"));

        //循环遍历当前防火墙的物模型
        metadataList.forEach(item -> {
            if (StringUtils.isNotEmpty(item.getDataType())) {
                jsonObject = dataBaseCodecUtils.switchByDataType(item, "stca:" + device.getKey(), jsonObject, commonMap);
            }
        });
        return jsonObject;
    }


    /**
     * 获取虚拟机启动时间
     *
     */
    private Map<String, String> getStartTime(MBeanServerConnection mbInfo) {
        HashMap<String, String> map = new HashMap<>();
        try {
            // -----------运行时--------
            ObjectName runtimeObjName = new ObjectName("java.lang:type=Runtime");
            // 运行时间
            Date starttime = new Date((Long) mbInfo.getAttribute(
                    runtimeObjName, "StartTime"));
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            map.put("startTime", df.format(starttime));
        } catch (Exception e) {
            // 状态告警
            log.error("获取虚拟机启动时间出错" + e.getMessage());
        }
        return map;
    }

    /**
     * 获取虚拟机运行时间
     *
     */
    private Map<String, String> getRunTime(MBeanServerConnection mbInfo) {
        HashMap<String, String> map = new HashMap<>();
        try {
            ObjectName runtimeObjName = new ObjectName("java.lang:type=Runtime");
            // SimpleDateFormat df = new SimpleDateFormat(
            // "yyyy-MM-dd HH:mm:ss");
            // 连续运行时间
            Long timespan = (Long) mbInfo
                    .getAttribute(runtimeObjName, "Uptime");
            String runtimeTemp = JMXUtils.formatTimeSpan(timespan);
            String[] runtimeArr = runtimeTemp.split("\\.")[0].split(":");
            String runtime = runtimeArr[0] + "小时 " + runtimeArr[1] + "分钟 "
                    + runtimeArr[2] + "秒";
            map.put("runtime",runtime);
        } catch (Exception e) {
            // 状态告警
            log.error("获取虚拟机运行时间出错" + e.getMessage());
        }
        return map;
    }
    /**
     * 获取软件版本
     *
     */
    private Map<String, String> getVersion(MBeanServerConnection mbInfo) {
        HashMap<String, String> map = new HashMap<>();
        try {
            ObjectName objName = new ObjectName("apusic:type=LicenseInfo");
            // 虚拟机名称
            String ApVersion =mbInfo.getAttribute(objName, "Edition").toString()+mbInfo.getAttribute(objName, "ProductVersion").toString();
            map.put("ApusicVersion", ApVersion);
        } catch (Exception e) {
            // 状态告警
            log.error("获取虚拟机名称出错" + e.getMessage());
        }
        return map;
    }
    /**
     * 获取虚拟机版本
     *
     */
    private Map<String, String> getVmVersion(MBeanServerConnection mbInfo) {
        HashMap<String, String> map = new HashMap<>();
        try {
            ObjectName runtimeObjName = new ObjectName("java.lang:type=Runtime");
            // 版本信息
            String VmVersion = (String) mbInfo.getAttribute(runtimeObjName,
                    "VmVersion");
            map.put("vmVersion", VmVersion);
        } catch (Exception e) {
            // 状态告警
            log.error("获取虚拟机版本出错" + e.getMessage());
        }
        return map;
    }

    /**
     * 获取虚拟机名称
     *
     */
    private Map<String, String> getVmName(MBeanServerConnection mbInfo) {
        HashMap<String, String> map = new HashMap<>();
        try {
            ObjectName runtimeObjName = new ObjectName("java.lang:type=Runtime");
            // 虚拟机名称
            String VmName = (String) mbInfo.getAttribute(runtimeObjName,
                    "VmName");
            map.put("vmName", VmName);
        } catch (Exception e) {
            // 状态告警
            log.error("获取虚拟机名称出错" + e.getMessage());
        }
        return map;
    }

    /**
     * 获取虚拟机厂商信息
     *
     */
    private Map<String, String> getVmVendor(MBeanServerConnection mbInfo) {
        HashMap<String, String> map = new HashMap<>();
        try {
            // -----------运行时--------
            ObjectName runtimeObjName = new ObjectName("java.lang:type=Runtime");
            // 厂商信息
            String VmVendor = (String) mbInfo.getAttribute(runtimeObjName,
                    "VmVendor");
            map.put("vmVendor", VmVendor);
        } catch (Exception e) {
            // 状态告警
            log.error("获取虚拟机厂商信息出错" + e.getMessage());
        }
        return map;
    }

    /**
     * 获取操作系统名称
     *
     */
    private Map<String, String> getSystemName(MBeanServerConnection mbInfo) {
        HashMap<String, String> map = new HashMap<>();
        try {
            ObjectName oprationObjName = new ObjectName(
                    "java.lang:type=OperatingSystem");
            // 操作系统名称
            map.put("systemName",
                    mbInfo.getAttribute(oprationObjName, "Name").toString());

        } catch (Exception e) {
            // 状态告警
            log.error("获取操作系统名称出错" + e.getMessage());
        }
        return map;
    }

    private Double memtatle = 0.0;
    private Double memFree=0.0;
    /**
     * 获取物理内存总量
     *
     */
    private Map<String, String> getTotalPhysicalMemorySize(MBeanServerConnection mbInfo) {
        HashMap<String, String> map = new HashMap<>();
        try {
            ObjectName oprationObjName = new ObjectName(
                    "java.lang:type=OperatingSystem");
            // 物理内存总量
            String totalPhysicalMemorySize = df1.format(Double.valueOf(mbInfo.getAttribute(
                    oprationObjName, "TotalPhysicalMemorySize")
                    .toString()) / 1024 / 1024 / 1024);
            map.put("totalMemory",totalPhysicalMemorySize);
            memtatle = Double.valueOf(totalPhysicalMemorySize);
        } catch (Exception e) {
            // 状态告警
            log.error("获取物理内存总量出错" + e.getMessage());
        }
        return map;
    }

    /**
     * 获取可用物理内存
     *
     */
    private Map<String, String> getFreePhysicalMemorySize(MBeanServerConnection mbInfo) {
        HashMap<String, String> map = new HashMap<>();
        try {
            ObjectName oprationObjName = new ObjectName(
                    "java.lang:type=OperatingSystem");
            // 可用物理内存
            String freePhysicalMemorySize = df1.format(Double.valueOf(mbInfo.getAttribute(
                    oprationObjName, "FreePhysicalMemorySize")
                    .toString()) / 1024 / 1024 / 1024);
            map.put("freeMemory",freePhysicalMemorySize);
            memFree = Double.valueOf(freePhysicalMemorySize);
        } catch (Exception e) {
            // 状态告警
            log.error("获取可用物理内存出错" + e.getMessage());
        }
        return map;
    }
    /**
     * 获取物理内存使用率
     *
     */
    private Map<String, Double> getMemRate() {
        HashMap<String, Double> map = new HashMap<>();
        try {
            double memRate = (memtatle - memFree) / memtatle * 100;
            String memRateValue = String.format("%.2f", memRate);
            map.put("memRate",Double.parseDouble(memRateValue));
        } catch (Exception e) {
            // 状态告警
            log.error("获取可用物理内存出错" + e.getMessage());
        }
        return map;
    }

    /**
     * 获取虚拟机堆内存总大小
     *
     */
    private Map<String, String> getMaxHeapMemory(MBeanServerConnection mbInfo) {
        HashMap<String, String> map = new HashMap<>();
        try {
            // 堆使用率
            ObjectName heapObjName = new ObjectName("java.lang:type=Memory");

            MemoryUsage heapMemoryUsage = MemoryUsage.from((CompositeDataSupport) mbInfo.getAttribute(heapObjName, "HeapMemoryUsage"));
            long maxMemory = heapMemoryUsage.getMax();// 堆最大
            map.put("maxHeapMemory", maxMemory / 1024 / 1024 +"");
        } catch (Exception e) {
            // 状态告警
            log.error("获取虚拟机堆内存总大小出错" + e.getMessage());
        }
        return map;
    }

    /**
     * 获取虚拟机栈内存总大小
     *
     */
    private Map<String, String> getMaxStackMemory(MBeanServerConnection mbInfo) {
        HashMap<String, String> map = new HashMap<>();
        try {
            // 堆使用率
            ObjectName heapObjName = new ObjectName("java.lang:type=Memory");
            MemoryUsage NonheapMemoryUsage = MemoryUsage
                    .from((CompositeDataSupport) mbInfo.getAttribute(
                            heapObjName, "NonHeapMemoryUsage"));
            long maxMemory = NonheapMemoryUsage.getMax();// 栈最大
            map.put("maxStackMemory", maxMemory / 1024 / 1024 + "");
        } catch (Exception e) {
            // 状态告警
            log.error("获取虚拟机栈内存总大小出错" + e.getMessage());
        }
        return map;
    }

    /**
     * 获取虚拟机堆使用率
     *
     */
    private Map<String, Double> getHeapUse(MBeanServerConnection mbInfo) {
//        HashMap<String, String> map = new HashMap<>();
        HashMap<String, Double> value = new HashMap<>();
        try {
            // 堆使用率
            ObjectName heapObjName = new ObjectName("java.lang:type=Memory");
            MemoryUsage heapMemoryUsage = MemoryUsage
                    .from((CompositeDataSupport) mbInfo.getAttribute(
                            heapObjName, "HeapMemoryUsage"));
            long commitMemory = heapMemoryUsage.getCommitted();// 堆当前分配
            long usedMemory = heapMemoryUsage.getUsed();
            String format = String.format("%.2f", (double) usedMemory * 100 / commitMemory);
//            map.put("jvmHeapUse",format);
            value.put("jvmHeapUse",Double.parseDouble(format));
//            map.put("jvm.heapCommitted", commitMemory / 1024 / 1024 + "MB");
//            map.put("jvm.heapUsed", usedMemory / 1024 / 1024 + "MB");
        } catch (Exception e) {
            // 状态告警
            log.error("获取虚拟机堆使用率出错" + e.getMessage());
        }
        return value;
    }

    /**
     * 获取虚拟机栈使用率
     *
     */
    private Map<String, Double> getStackUse(MBeanServerConnection mbInfo) {
//        HashMap<String, String> map = new HashMap<>();
        HashMap<String, Double> value = new HashMap<>();
        try {
            // 堆使用率
            ObjectName heapObjName = new ObjectName("java.lang:type=Memory");
            MemoryUsage nonheapMemoryUsage = MemoryUsage
                    .from((CompositeDataSupport) mbInfo.getAttribute(
                            heapObjName, "NonHeapMemoryUsage"));
            long noncommitMemory = nonheapMemoryUsage.getCommitted();
            long nonusedMemory = nonheapMemoryUsage.getUsed();
            String format = String.format("%.2f", (double) nonusedMemory * 100 / noncommitMemory);
//            map.put("jvmStackUse",format);
            value.put("jvmStackUse",Double.parseDouble(format));
//            map.put("jvm.stackCommitted", noncommitMemory / 1024 / 1024 + "MB");
//            map.put("jvm.stackUsed", nonusedMemory / 1024 / 1024 + "MB");
        } catch (Exception e) {
            // 状态告警
            log.error("获取虚拟机栈使用率出错" + e.getMessage());
        }
        return value;
    }

    /**
     * 获取虚拟cpu使用率
     *
     */
    private Map<String, Double> getCpuUse(MBeanServerConnection mbInfo) {
        HashMap<String, Double> map = new HashMap<>();
        try {
            // 堆使用率
            ObjectName operating = new ObjectName(
                    "java.lang:type=OperatingSystem");
            ObjectName run = new ObjectName("java.lang:type=Runtime");
            double AvailableProcessors = Double.valueOf(mbInfo.getAttribute(
                    operating, "AvailableProcessors").toString());
            double old, now, timeold, timenow;
            old = Double.valueOf(mbInfo.getAttribute(operating,
                    "ProcessCpuTime").toString());
            timeold = Double.valueOf(mbInfo.getAttribute(run, "Uptime")
                    .toString());
            Thread.currentThread();
            Thread.sleep(5000);// 毫秒
            now = Double.valueOf(mbInfo.getAttribute(operating,
                    "ProcessCpuTime").toString());
            timenow = Double.valueOf(mbInfo.getAttribute(run, "Uptime")
                    .toString());
            double rate = (now - old) * 100 / 1000000 / (timenow - timeold)
                    / AvailableProcessors;

            map.put("cpuUse", Double.parseDouble(String.format("%.2f", rate)) );
        } catch (Exception e) {
            // 状态告警
            log.error("获取虚拟机栈使用率出错" + e.getMessage());
        }
        return map;
    }


}
