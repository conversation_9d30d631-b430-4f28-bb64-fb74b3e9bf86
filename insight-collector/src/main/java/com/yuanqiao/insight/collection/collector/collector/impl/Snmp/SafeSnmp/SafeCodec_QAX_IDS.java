package com.yuanqiao.insight.collection.collector.collector.impl.Snmp.SafeSnmp;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.collection.collector.utils.SNMPCodecAndCollectUtils;
import com.yuanqiao.insight.collection.collector.utils.SNMPCodecInterface;
import com.yuanqiao.insight.common.util.common.TimeUtils;
import com.yuanqiao.insight.common.util.snmp.SNMPUtils;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigInteger;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Slf4j
public class SafeCodec_QAX_IDS implements SNMPCodecInterface {
    // 操作系统类型
    String osType;

    public void setOsType(String osType) {
        this.osType = osType;
    }

    //最外层jsonObject
    JSONObject jsonObject = new JSONObject();
    //公共结果集
    JSONObject commonMap = new JSONObject();

    /**
     * 定义SNMP拉模式采集解码器
     * @param snmpUtils
     * @param metadataList             物模型集合
     * @param stcaKey                  状态容器key
     * @param device
     * @param snmpCodecAndCollectUtils
     * @return
     */
    @Override
    public JSONObject dataCodec(SNMPUtils snmpUtils, List<ProertyMetadata> metadataList, String stcaKey, Device device, SNMPCodecAndCollectUtils snmpCodecAndCollectUtils) {
        //清空外层jsonObject和公共结果集commonMap
        jsonObject.clear();
        commonMap.clear();

        //静态属性
        HashMap<String, Object> staticInfoMap = new HashMap<>();

        staticInfoMap.put("sysName", getName(snmpUtils).get("sysName"));
        staticInfoMap.put("sysDesc", getSysDesc(snmpUtils).get("sysDesc"));
        staticInfoMap.put("portNum", getPortNum(snmpUtils).get("portNum"));
        commonMap.put("staticInfo", staticInfoMap);
        commonMap.put("sysUpTime", getSysUpTime(snmpUtils).get("sysUpTime"));

        commonMap.put("cpuRate", getCpuRate(snmpUtils).get("cpuRate"));
        commonMap.put("memRate", getMemRate(snmpUtils).get("memRate"));

        commonMap.put("diskInfo", getDiskInfoList(snmpUtils));


        //循环遍历当前的物模型
        metadataList.forEach(item -> {
            if (StringUtils.isNotEmpty(item.getDataType())) {
                jsonObject = snmpCodecAndCollectUtils.switchByDataType(item, stcaKey, jsonObject, commonMap, snmpUtils);
            }
        });



        return jsonObject;
    }

    /**
     * 获取设备运行时长
     *
     * @param snmpUtils
     * @return
     */
    private Map<String, String> getSysUpTime(SNMPUtils snmpUtils) {
        HashMap<String, String> map = new HashMap<>();
        try {
            String sysUpTime = snmpUtils.getPDU("1.3.6.1.2.1.1.3.0");
            if (StringUtils.isNotEmpty(sysUpTime) && !sysUpTime.equalsIgnoreCase("noSuchObject")
                    && !sysUpTime.equalsIgnoreCase("noSuchInstance")) {
                // 去除字符串中的逗号
                sysUpTime = sysUpTime.replace(",", "");

                // 使用正则表达式匹配时间信息
                Pattern pattern = Pattern.compile("(?:(\\d+) days?)?(?:\\s*,?\\s*(\\d+):(\\d+)(?::(\\d+(?:\\.\\d+)?))?)?");
                Matcher matcher = pattern.matcher(sysUpTime);

                int days = 0, hours = 0, minutes = 0, seconds = 0;
                double milliseconds = 0;

                if (matcher.find()) {
                    if (matcher.group(1) != null) {
                        days = Integer.parseInt(matcher.group(1));
                    }
                    if (matcher.group(2) != null) {
                        hours = Integer.parseInt(matcher.group(2));
                    }
                    if (matcher.group(3) != null) {
                        minutes = Integer.parseInt(matcher.group(3));
                    }
                    if (matcher.group(4) != null) {
                        String[] secondsParts = matcher.group(4).split("\\.");
                        seconds = Integer.parseInt(secondsParts[0]);
                        if (secondsParts.length > 1) {
                            milliseconds = Double.parseDouble("0." + secondsParts[1]);
                        }
                    }
                }
                // 计算总毫秒数
                BigInteger totalMilliseconds = BigInteger.valueOf(days * 24 * 60 * 60 * 1000L)
                        .add(BigInteger.valueOf(hours * 60 * 60 * 1000L))
                        .add(BigInteger.valueOf(minutes * 60 * 1000L))
                        .add(BigInteger.valueOf((long) (seconds * 1000 + milliseconds * 1000)));
                sysUpTime = TimeUtils.getDistanceTime(totalMilliseconds);
                map.put("sysUpTime", sysUpTime);
            }
        } catch (Exception e) {
            log.error("获取运行时长出错", e);
            map.put("sysUpTime", "");
        }
        return map;
    }
    //获取名称
    private Map<String, String> getName(SNMPUtils snmpUtils) {
        try {
            HashMap<String, String> map = new HashMap<>();
            // 名称
            String ifName = snmpUtils.getPDU("1.3.6.1.2.1.1.5.0");
            log.error("-------奇安信IDS 名称-------"+ifName);
            if (ifName == null || "".equals(ifName.trim()) || "null".equalsIgnoreCase(ifName.trim())) {
                log.error("未获取到名称...");
                map.put("sysName", "- -");
            } else {
                map.put("sysName", ifName);
            }
            return map;
        } catch (Exception e) {
            log.error("获取名称出错", e);
            HashMap<String, String> map = new HashMap<>();
            map.put("sysName", "- -");
            return map;
        }
    }

    //获取描述
    private Map<String, String> getSysDesc(SNMPUtils snmpUtils) {
        try {
            HashMap<String, String> map = new HashMap<>();
            // 描述
            String sysDesc = snmpUtils.getPDU("1.3.6.1.2.1.1.1.0");
            log.error("-------奇安信IDS 描述-------"+sysDesc);
            if (sysDesc == null || "".equals(sysDesc.trim()) || "null".equalsIgnoreCase(sysDesc.trim())) {
                log.error("未获取到描述...");
                map.put("sysDesc", "- -");
            }
            map.put("sysDesc", sysDesc);
            return map;
        } catch (Exception e) {
            log.error("获取描述出错", e);
            HashMap<String, String> map = new HashMap<>();
            map.put("sysDesc", "- -");
            return map;
        }
    }

    //获取端口数
    private Map<String, Integer> getPortNum(SNMPUtils snmpUtils) {
        try {
            HashMap<String, Integer> map = new HashMap<>();
            // 端口数
            String portNum_String = snmpUtils.getPDU("1.3.6.1.2.1.2.1.0");
            log.error("-------奇安信IDS 端口数-------"+portNum_String);
            if (StringUtils.isEmpty(portNum_String)) {
                log.error("未获取到端口数...");
                map.put("portNum", 0);
            } else {
                Integer portNum = Integer.parseInt(portNum_String);
                map.put("portNum", portNum);
            }
            return map;
        } catch (Exception e) {
            log.error("获取端口数出错", e);
            HashMap<String, Integer> map = new HashMap<>();
            map.put("portNum", 0);
            return map;
        }
    }


    //获取cpu使用率
    private Map<String, Double> getCpuRate(SNMPUtils snmpUtils) {
        HashMap<String, Double> resMap = new HashMap<>();
        try {
            //初始化cpu平均使用率
            double cpuUtilization = 0.0;
            String cpuUsed = snmpUtils.getPDU(".1.3.6.1.4.1.32328.3.4.5.0");
            log.error("-------奇安信IDS CPU使用率-------"+cpuUsed);
            cpuUtilization = Double.valueOf(cpuUsed);
            if (cpuUtilization == 0){
                log.error("未获取到CPU使用率或当前CPU使用率为0！");
                resMap.put("cpuRate", 0.00);
            }else {
                resMap.put("cpuRate", Double.valueOf(String.format("%.2f", cpuUtilization)));
            }
            log.error("-------奇安信IDS CPU使用率最终值-------"+Double.valueOf(String.format("%.2f", cpuUtilization)));
            return resMap;
        } catch (Exception e) {
            log.error("获取cpu平均使用率出错" + e.getMessage());
            resMap.put("cpuRate", 0.0);
            return resMap;
        }
    }

    //获取内存使用率
    private Map<String, Double> getMemRate(SNMPUtils snmpUtils) {
        HashMap<String, Double> resMap = new HashMap<>();
        try {
            // 内存使用率
            double memUtilization = 0;
            String memUse = snmpUtils.getPDU(".1.3.6.1.4.1.32328.3.4.2.16.0");
            log.error("-------奇安信IDS 内存使用率-------"+memUse);
            memUtilization = Double.valueOf(memUse);
           if (memUtilization == 0) {
                log.error("未获取到内存使用率或当前内存使用率为0！");
                resMap.put("memRate", 0.00);
            } else {
                resMap.put("memRate", Double.valueOf(String.format("%.2f", memUtilization)));
            }
            log.error("-------奇安信IDS 内存使用率最终值-------"+Double.valueOf(String.format("%.2f", memUtilization)));
            return resMap;
        } catch (Exception e) {
            log.error("获取内存使用率出错" + e.getMessage());
            resMap.put("memRate", 0.0);
            return resMap;
        }
    }


    //硬盘信息列表
    private List<Map<String, Object>> getDiskInfoList(SNMPUtils snmpUtils) {
        try {
            // 接口索引 主键
            Map<String, String> ifKeyMap = snmpUtils.getPDUWalk(".1.3.6.1.4.1.32328.3.4.3.1.1");

            int[] is = new int[ifKeyMap.size()];
            int i = 0;
            for (Map.Entry<String, String> en : ifKeyMap.entrySet()) {
                is[i] = Integer.parseInt(en.getValue());
                i++;
            }
            Arrays.sort(is);

            //路径
            Map<String, String> pathMap = snmpUtils.getPDUWalk(".1.3.6.1.4.1.32328.3.4.3.1.2");

            //总量
            Map<String, String> totalMap = snmpUtils.getPDUWalk(".1.3.6.1.4.1.32328.3.4.3.1.6");

            //可使用量
            Map<String, String> freeMap = snmpUtils.getPDUWalk(".1.3.6.1.4.1.32328.3.4.3.1.7");

            //已使用量
            Map<String, String> usedMap = snmpUtils.getPDUWalk(".1.3.6.1.4.1.32328.3.4.3.1.8");

            //使用率
            Map<String, String> usageMap = snmpUtils.getPDUWalk(".1.3.6.1.4.1.32328.3.4.3.1.9");

            List<Map<String, Object>> diskInfoList = new ArrayList<Map<String, Object>>();
            for (int j = 0; j < is.length; j++) {
                Map<String, Object> res = new HashMap<String, Object>();

                //索引
                res.put("index", j);

                //路径
                res.put("diskPath", pathMap.get(".1.3.6.1.4.1.32328.3.4.3.1.2." + is[j]));

                //总量
                res.put("diskTotal", totalMap.get(".1.3.6.1.4.1.32328.3.4.3.1.6." + is[j]));

                //可使用量
                res.put("diskAvail", freeMap.get(".1.3.6.1.4.1.32328.3.4.3.1.7." + is[j]));

                //已使用量
                res.put("diskUsed", usedMap.get(".1.3.6.1.4.1.32328.3.4.3.1.8." + is[j]));

                //使用率
                res.put("diskUsage", usageMap.get(".1.3.6.1.4.1.32328.3.4.3.1.9." + is[j]));

                diskInfoList.add(res);
            }
            return diskInfoList;
        } catch (Exception e) {
            log.error("获取磁盘信息出错！", e);
            return null;
        }
    }


    //获取网络吞吐量
    private Map<String, Double> getNetInAndOut(SNMPUtils snmpUtils) {
        try {
            HashMap<String, Double> map = new HashMap<>();
            //KeyMap
            Map<String, String> KeyMap = snmpUtils.getPDUWalk("1.3.6.1.2.1.2.2.1.1");
            int[] is = new int[KeyMap.size()];
            int i = 0;
            for (Map.Entry<String, String> en : KeyMap.entrySet()) {
                is[i] = Integer.parseInt(en.getValue());
                i++;
            }
            Arrays.sort(is);

            // 吞吐量
            double inAndOut = 0.0;
            int isLength = is.length;
            for (int j = 0; j < isLength; j++) {
                // 输入和输出字节总数
                String inSpeed_String = snmpUtils.getPDU("1.3.6.1.2.1.2.2.1.10." + is[j]);
                String OutSpeed_String = snmpUtils.getPDU("1.3.6.1.2.1.2.2.1.16." + is[j]);
                double inAndOutSpeed = 0;

                if (inSpeed_String != null && inSpeed_String != "" && OutSpeed_String != null
                        && OutSpeed_String != "") {
                    inAndOutSpeed = Double.parseDouble(inSpeed_String) + Double.parseDouble(OutSpeed_String);
                }
                inAndOut += inAndOutSpeed;
            }
            if (Double.isNaN(inAndOut)) {
                log.error("未获取到网络吞吐量...");
                map.put("netInOut", 0.0);
            } else {
                inAndOut = inAndOut / 1024 / 1024 / 1024;
                map.put("netInOut", Double.parseDouble(String.format("%.2f", inAndOut)));
            }
            log.error("-------奇安信IDS 网络吞吐量-------"+Double.parseDouble(String.format("%.2f", inAndOut)));
            return map;
        } catch (Exception e) {
            log.error("获取网络吞吐量出错" + e.getMessage());
            HashMap<String, Double> map = new HashMap<>();
            map.put("netInOut", 0.0);
            return map;
        }
    }

    //获取端口详情
    private List<Map<String, Object>> getPortInfoList(SNMPUtils snmpUtils) {

        try {
            // 接口索引 主键
            Map<String, String> ifKeyMap = snmpUtils.getPDUWalk("1.3.6.1.2.1.2.1.1");

            int[] is = new int[ifKeyMap.size()];
            int i = 0;
            for (Map.Entry<String, String> en : ifKeyMap.entrySet()) {
                is[i] = Integer.parseInt(en.getValue());
                i++;
            }
            Arrays.sort(is);

            //端口状态
            Map<String, String> portStatusMap = snmpUtils.getPDUWalk("1.3.6.1.2.1.2.1.8");

            //端口描述
            Map<String, String> ifDescrProtosMap = snmpUtils.getPDUWalk("1.3.6.1.2.1.2.1.2");

            // 带宽
            Map<String, String> ifSpeedProtosMap = snmpUtils.getPDUWalk("1.3.6.1.2.1.2.1.5");

            //端口类型
            Map<String, String> portTypeMap = snmpUtils.getPDUWalk("1.3.6.1.2.1.2.1.3");

            //接收错误数据包
            Map<String, String> ifInErrorsMap = snmpUtils.getPDUWalk("1.3.6.1.2.1.2.1.14");

            //发送的错误数据包
            Map<String, String> ifOutErrorsProtosMap = snmpUtils.getPDUWalk("1.3.6.1.2.1.2.1.20");

            List<Map<String, Object>> portInfoList = new ArrayList<Map<String, Object>>();
            for (int j = 0; j < is.length; j++) {
                Map<String, Object> res = new HashMap<String, Object>();

                //端口类型
                res.put("portType", portTypeMap.get("1.3.6.1.2.1.2.1.3." + is[j]));

                //端口状态
                String str = portStatusMap.get("1.3.6.1.2.1.2.1.8." + is[j]);
                if ("1".equals(str.trim())) {
                    res.put("portStatus", "连接");
                } else if ("2".equals(str.trim())) {
                    res.put("portStatus", "关闭");
                } else {
                    res.put("portStatus", "其他");
                }

                // 带宽
                String bandWidth_string = ifSpeedProtosMap.get("1.3.6.1.2.1.2.1.5." + is[j]);
                if (StringUtils.isNotEmpty(bandWidth_string)) {
                    Double bandWidth = Double.parseDouble(bandWidth_string);
                    res.put("bandWidth", String.format("%.2f", bandWidth / 8 / 1024 / 1024));
                }

                //输入/输出流量
                String inSpeed = snmpUtils.getPDU("1.3.6.1.2.1.2.1.10." + is[j]);
                if (org.apache.commons.lang.StringUtils.isNotEmpty(inSpeed)) {
                    Double inputFlow = Double.parseDouble(inSpeed);
                    res.put("inputFlow", String.format("%.2f", inputFlow * 8 / 1024 / 1024));
                }
                String outSpeed = snmpUtils.getPDU("1.3.6.1.2.1.2.1.16." + is[j]);
                if (org.apache.commons.lang.StringUtils.isNotEmpty(outSpeed)) {
                    Double outputFlow = Double.parseDouble(outSpeed);
                    res.put("outputFlow", String.format("%.2f", outputFlow * 8 / 1024 / 1024));
                }

                //输入/输出错误包数
                String inErrorPackageString = ifInErrorsMap.get("1.3.6.1.2.1.2.1.14." + is[j]);
                if (org.apache.commons.lang.StringUtils.isNotEmpty(inErrorPackageString)) {
                    Double inError = Double.parseDouble(inErrorPackageString);
                    res.put("inErrorPackage", String.format("%.2f", inError));
                }
                String outErrorPackageString = ifOutErrorsProtosMap.get("1.3.6.1.2.1.2.1.20." + is[j]);
                if (org.apache.commons.lang.StringUtils.isNotEmpty(outErrorPackageString)) {
                    Double inError = Double.parseDouble(outErrorPackageString);
                    res.put("outErrorPackage", String.format("%.2f", inError));
                }

                //输入/输出丢失错误包数
                String inLossPackageString = snmpUtils.getPDU("1.3.6.1.2.1.2.1.13." + is[j]);
                if (org.apache.commons.lang.StringUtils.isNotEmpty(inLossPackageString)) {
                    Double inLossPackage = Double.parseDouble(inLossPackageString);
                    res.put("inLossPackage", String.format("%.2f", inLossPackage));
                }
                String outLossPackageString = snmpUtils.getPDU("1.3.6.1.2.1.2.1.19." + is[j]);
                if (org.apache.commons.lang.StringUtils.isNotEmpty(outLossPackageString)) {
                    Double outLossPackage = Double.parseDouble(outLossPackageString);
                    res.put("outLossPackage", String.format("%.2f", outLossPackage));
                }

                //输入单播报文的个数
                String inUcastPktsStr = snmpUtils.getPDU("1.3.6.1.2.1.2.1.11." + is[j]);
                if (StringUtils.isNotEmpty(inUcastPktsStr) && !inUcastPktsStr.equals("noSuchInstance")) {
                    Double inUcastPkts = Double.parseDouble(inUcastPktsStr);
                    res.put("inUcastPkts", String.format("%.2f", inUcastPkts));
                }

                //输入非单播报文的个数
                String inNUcastPktsStr = snmpUtils.getPDU("1.3.6.1.2.1.2.1.12." + is[j]);
                if (StringUtils.isNotEmpty(inNUcastPktsStr) && !inNUcastPktsStr.equals("noSuchInstance")) {
                    Double inNUcastPkts = Double.parseDouble(inNUcastPktsStr);
                    res.put("inNUcastPkts", String.format("%.2f", inNUcastPkts));
                }

                //输出单播报文的个数
                String outUcastPktsStr = snmpUtils.getPDU("1.3.6.1.2.1.2.1.17." + is[j]);
                if (StringUtils.isNotEmpty(outUcastPktsStr) && !outUcastPktsStr.equals("noSuchInstance")) {
                    Double outUcastPkts = Double.parseDouble(outUcastPktsStr);
                    res.put("outUcastPkts", String.format("%.2f", outUcastPkts));
                }

                //输出非单播报文的个数
                String outNUcastPktsStr = snmpUtils.getPDU("1.3.6.1.2.1.2.1.18." + is[j]);
                if (StringUtils.isNotEmpty(outNUcastPktsStr) && !outNUcastPktsStr.equals("noSuchInstance")) {
                    Double outNUcastPkts = Double.parseDouble(outNUcastPktsStr);
                    res.put("outNUcastPkts", String.format("%.2f", outNUcastPkts));
                }

                // 接口描述
                res.put("portDesc", ifDescrProtosMap.get("1.3.6.1.2.1.2.1.2." + is[j]));

                //索引
                res.put("index", is[j]);

                portInfoList.add(res);
            }
            return portInfoList;
        } catch (Exception e) {
            log.error("获取端口信息出错", e);
            return null;
        }
    }
}
