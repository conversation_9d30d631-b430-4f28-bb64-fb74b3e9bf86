package com.yuanqiao.insight.collection.collector.collector.impl.Snmp.SafeSnmp;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.collection.collector.utils.SNMPCodecAndCollectUtils;
import com.yuanqiao.insight.collection.collector.utils.SNMPCodecInterface;
import com.yuanqiao.insight.common.util.common.TimeUtils;
import com.yuanqiao.insight.common.util.snmp.SNMPUtils;
import com.yuanqiao.insight.common.util.snmp.SnmpConstant;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigInteger;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 启明星辰天日志收集与分析系统V3.0（日志审计）
 */
@Slf4j
public class SafeSnmpCodec_QMXC_AUDIT implements SNMPCodecInterface {

    // 操作系统类型
    String osType;

    public void setOsType(String osType) {
        this.osType = osType;
    }

    //最外层jsonObject
    JSONObject jsonObject = new JSONObject();
    //公共结果集
    JSONObject commonMap = new JSONObject();

    @Override
    public JSONObject dataCodec(SNMPUtils snmpUtils, List<ProertyMetadata> metadataList, String stcaKey, Device device, SNMPCodecAndCollectUtils snmpCodecAndCollectUtils) {
        //清空外层jsonObject和公共结果集commonMap
        jsonObject.clear();
        commonMap.clear();

        //静态属性
        HashMap<String, Object> staticInfoMap = new HashMap<>();

        staticInfoMap.put("switchName", getName(snmpUtils).get("switchName"));
        staticInfoMap.put("switchDesc", getSysDesc(snmpUtils).get("switchDesc"));
        staticInfoMap.put("portNum", getPortNum(snmpUtils).get("portNum"));
        staticInfoMap.put("runTime", getRunTime(snmpUtils).get("runTime"));
        commonMap.put("staticInfo", staticInfoMap);
        commonMap.put("sysUpTime", getSysUpTime(snmpUtils).get("sysUpTime"));

        commonMap.put("cpuRate", getCpuRate(snmpUtils).get("cpuRate"));
        commonMap.put("memRate", getMemRate(snmpUtils).get("memRate"));

        Map<String, Object> portInfoMap = getPortInfoList(snmpUtils, "*******.*******");
        commonMap.put("portInfo", portInfoMap.get("portInfo"));
        // 输入输出总流量
        commonMap.put("netInOut", portInfoMap.get("netInOut"));
        // 输入输出总速率
        commonMap.put("allSpeed", portInfoMap.get("inoutSpeed"));
        // 输入总速率
        commonMap.put("inSpeed", portInfoMap.get("inSpeed"));
        // 输出总速率
        commonMap.put("outSpeed", portInfoMap.get("outSpeed"));
        //ping响应时间
//        String ip = device.getConnectParam().get("ip");
//        String time = PingIpUtils.pingAvgTime(ip, 4, 2000);
//        commonMap.put("time", time);

        //循环遍历当前启明星辰日志审计的物模型
        metadataList.forEach(item -> {
            if (StringUtils.isNotEmpty(item.getDataType())) {
                jsonObject = snmpCodecAndCollectUtils.switchByDataType(item, stcaKey, jsonObject, commonMap, snmpUtils);
            }
        });

        return jsonObject;
    }
    /**
     * 获取设备运行时长
     *
     * @param snmpUtils
     * @return
     */
    private Map<String, String> getSysUpTime(SNMPUtils snmpUtils) {
        HashMap<String, String> map = new HashMap<>();
        try {
            String sysUpTime = snmpUtils.getPDU("*******.*******.0");
            if (StringUtils.isNotEmpty(sysUpTime) && !sysUpTime.equalsIgnoreCase("noSuchObject")
                    && !sysUpTime.equalsIgnoreCase("noSuchInstance")) {
                // 去除字符串中的逗号
                sysUpTime = sysUpTime.replace(",", "");

                // 使用正则表达式匹配时间信息
                Pattern pattern = Pattern.compile("(?:(\\d+) days?)?(?:\\s*,?\\s*(\\d+):(\\d+)(?::(\\d+(?:\\.\\d+)?))?)?");
                Matcher matcher = pattern.matcher(sysUpTime);

                int days = 0, hours = 0, minutes = 0, seconds = 0;
                double milliseconds = 0;

                if (matcher.find()) {
                    if (matcher.group(1) != null) {
                        days = Integer.parseInt(matcher.group(1));
                    }
                    if (matcher.group(2) != null) {
                        hours = Integer.parseInt(matcher.group(2));
                    }
                    if (matcher.group(3) != null) {
                        minutes = Integer.parseInt(matcher.group(3));
                    }
                    if (matcher.group(4) != null) {
                        String[] secondsParts = matcher.group(4).split("\\.");
                        seconds = Integer.parseInt(secondsParts[0]);
                        if (secondsParts.length > 1) {
                            milliseconds = Double.parseDouble("0." + secondsParts[1]);
                        }
                    }
                }
                // 计算总毫秒数
                BigInteger totalMilliseconds = BigInteger.valueOf(days * 24 * 60 * 60 * 1000L)
                        .add(BigInteger.valueOf(hours * 60 * 60 * 1000L))
                        .add(BigInteger.valueOf(minutes * 60 * 1000L))
                        .add(BigInteger.valueOf((long) (seconds * 1000 + milliseconds * 1000)));
                sysUpTime = TimeUtils.getDistanceTime(totalMilliseconds);
                map.put("sysUpTime", sysUpTime);
            }
        } catch (Exception e) {
            log.error("获取运行时长出错", e);
            map.put("sysUpTime", "");
        }
        return map;
    }
    //获取启明星辰日志审计端口数
    private Map<String, Integer> getPortNum(SNMPUtils snmpUtils) {
        try {
            HashMap<String, Integer> map = new HashMap<>();
            // 启明星辰日志审计端口数
            String portNum_String = snmpUtils.getPDU("*******.2.1.2.1.0");
            if (StringUtils.isEmpty(portNum_String)) {
                log.error("未获取到启明星辰日志审计端口数...");
                map.put("portNum", 0);
            } else {
                Integer portNum = Integer.parseInt(portNum_String);
                map.put("portNum", portNum);
            }
            return map;
        } catch (Exception e) {
            log.error("获取启明星辰日志审计端口数出错", e);
            HashMap<String, Integer> map = new HashMap<>();
            map.put("portNum", 0);
            return map;
        }
    }

    //获取启明星辰日志审计名称
    private Map<String, String> getName(SNMPUtils snmpUtils) {
        try {
            HashMap<String, String> map = new HashMap<>();
            // 启明星辰日志审计名称
            String ifName = snmpUtils.getPDU("*******.2.1.1.5.0");
            if (ifName == null || "".equals(ifName.trim()) || "null".equalsIgnoreCase(ifName.trim())) {
                log.error("未获取到启明星辰日志审计名称...");
                map.put("switchName", "- -");
            } else {
                map.put("switchName", ifName);
            }
            return map;
        } catch (Exception e) {
            log.error("获取启明星辰日志审计名称出错", e);
            HashMap<String, String> map = new HashMap<>();
            map.put("switchName", "- -");
            return map;
        }
    }

    //获取启明星辰日志审计描述
    private Map<String, String> getSysDesc(SNMPUtils snmpUtils) {
        try {
            HashMap<String, String> map = new HashMap<>();
            // 启明星辰日志审计描述
            String sysDesc = snmpUtils.getPDU("*******.2.1.1.1.0");
            if (sysDesc == null || "".equals(sysDesc.trim()) || "null".equalsIgnoreCase(sysDesc.trim())) {
                log.error("未获取到启明星辰日志审计描述...");
                map.put("switchDesc", "- -");
            }
            map.put("switchDesc", sysDesc);
            return map;
        } catch (Exception e) {
            log.error("获取启明星辰日志审计描述出错", e);
            HashMap<String, String> map = new HashMap<>();
            map.put("switchDesc", "- -");
            return map;
        }
    }

    //获取启明星辰日志审计运行时间
    private Map<String, String> getRunTime(SNMPUtils snmpUtils) {
        try {
            HashMap<String, String> map = new HashMap<>();
            // 启明星辰日志审计运行时间
            String sysRunTime = snmpUtils.getPDU("*******.*******.0");
            if (sysRunTime == null || "".equals(sysRunTime.trim()) || "null".equalsIgnoreCase(sysRunTime.trim())) {
                log.error("未获取到启明星辰日志审计运行时间...");
                map.put("runTime", "--");
            }
//            if (!"".equals(sysRunTime) && sysRunTime != null && !sysRunTime.isEmpty()) {
//                String[] categoryArr = sysRunTime.split(":");
//                String dayRep = categoryArr[0].replace("days", "天");
//                String seconds = categoryArr[2].substring(0, 2);
//                sysRunTime = dayRep + ":" + categoryArr[1] + ":" + seconds;
//            }
            map.put("runTime", sysRunTime);
            return map;
        } catch (Exception e) {
            log.error("获取启明星辰日志审计运行时间出错", e);
            HashMap<String, String> map = new HashMap<>();
            map.put("runTime", "--");
            return map;
        }
    }

    //获取启明星辰日志审计cpu使用率
    private Map<String, Double> getCpuRate(SNMPUtils snmpUtils) {
        HashMap<String, Double> resMap = new HashMap<>();
        try {
            String cpuUse = snmpUtils.getPDU("*******.4.1.15227.200.*******.0");
            resMap.put("cpuRate", Double.valueOf(cpuUse));
            return resMap;
        } catch (Exception e) {
            log.error("获取启明星辰日志审计cpu平均使用率出错" + e.getMessage());
            resMap.put("cpuRate", 0.0);
            return resMap;
        }
    }

    //获取内存使用率
    private Map<String, Double> getMemRate(SNMPUtils snmpUtils) {

        try {
            HashMap<String, Double> resMap = new HashMap<>();
            String memRate = snmpUtils.getPDU("*******.4.1.15227.200.2.2.3.0");
            resMap.put("memRate", Double.valueOf(memRate));
            return resMap;
        } catch (Exception e) {
            log.error("获取启明星辰日志审计内存使用率出错" + e.getMessage());
            HashMap<String, Double> map = new HashMap<>();
            map.put("memRate", 0.0);
            return map;
        }
    }

    /**
     * 获取端口详情
     *
     * @param snmpUtils
     * @return
     */
    private Map<String, Object> getPortInfoList(SNMPUtils snmpUtils, String tableTopOid) {

        HashMap<String, Object> resultMap = new HashMap<>();

        Double inAndOutBefore = 0.00;
        Double inAndOutAfter = 0.00;
        Double inTotalBefore = 0.00;
        Double inTotalAfter = 0.00;
        Double outTotalBefore = 0.00;
        Double outTotalAfter = 0.00;

        try {
            List<Map<String, Object>> portInfoList = new ArrayList<Map<String, Object>>();
            List<Map<String, Object>> portInfoTempList = new ArrayList<>();

            long oneBeginTime = System.currentTimeMillis();
            JSONObject tableData = snmpUtils.getPduTableView(tableTopOid);
            if (tableData != null && !tableData.isEmpty()) {
                JSONArray dataArray = tableData.getJSONArray("dataList");
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject eleObject = dataArray.getJSONObject(i);
                    Map<String, Object> res = new HashMap<>();

                    //索引
                    res.put("index", eleObject.getString(SnmpConstant.indexOid));

                    // 接口描述
                    res.put("portDesc", eleObject.getString(SnmpConstant.descriptionOid));

                    //端口类型
                    res.put("portType", eleObject.getString(SnmpConstant.typeOid));

                    //端口状态
                    String str = eleObject.getString(SnmpConstant.statusOid);
                    if ("1".equals(str.trim())) {
                        res.put("portStatus", "连接");
                    } else if ("2".equals(str.trim())) {
                        res.put("portStatus", "关闭");
                    } else {
                        res.put("portStatus", "其他");
                    }

                    // 带宽
                    String bandWidth_string = eleObject.getString(SnmpConstant.bandWidthOid);
                    if (StringUtils.isNotEmpty(bandWidth_string)) {
                        Double bandWidth = Double.parseDouble(bandWidth_string);
                        //原始单位为bit/s,转换为Mbit/s
                        res.put("bandWidth", Double.valueOf(String.format("%.2f", bandWidth)));
                    }

                    //输入流量
                    String inFlow = eleObject.getString(SnmpConstant.inFlowOid);
                    if (StringUtils.isNotEmpty(inFlow)) {
                        //原始单位为Byte,转换为MByte
                        Double inputFlow = Double.parseDouble(inFlow);
                        inAndOutBefore += inputFlow;
                        inTotalBefore += inputFlow;
                        res.put("inputFlow", String.format("%.2f", inputFlow));
                    }

                    //输出流量
                    String outFlow = eleObject.getString(SnmpConstant.outFlowOid);
                    if (StringUtils.isNotEmpty(outFlow)) {
                        //原始单位为Byte,转换为MByte
                        Double outputFlow = Double.parseDouble(outFlow);
                        inAndOutBefore += outputFlow;
                        outTotalBefore += outputFlow;
                        res.put("outputFlow", String.format("%.2f", outputFlow));
                    }

                    //输入错误包数
                    double inErrorPackage = 0.0;
                    String inErrorPackageString = eleObject.getString(SnmpConstant.inErrorsOid);
                    if (StringUtils.isNotEmpty(inErrorPackageString)) {
                        inErrorPackage = Double.parseDouble(inErrorPackageString);
                        res.put("inErrorPackage", String.format("%.2f", inErrorPackage));
                    }

                    //输出错误包数
                    double outErrorPackage = 0.0;
                    String outErrorPackageString = eleObject.getString(SnmpConstant.outErrorsOid);
                    if (StringUtils.isNotEmpty(outErrorPackageString)) {
                        outErrorPackage = Double.parseDouble(outErrorPackageString);
                        res.put("outErrorPackage", String.format("%.2f", outErrorPackage));
                    }

                    //输入丢失错误包数
                    double inLossPackage = 0.0;
                    String inLossPackageString = eleObject.getString(SnmpConstant.inLossPackageOid);
                    if (StringUtils.isNotEmpty(inLossPackageString)) {
                        inLossPackage = Double.parseDouble(inLossPackageString);
                        res.put("inLossPackage", String.format("%.2f", inLossPackage));
                    }

                    //输出丢失错误包数
                    double outLossPackage = 0.0;
                    String outLossPackageString = eleObject.getString(SnmpConstant.outLossPackageOid);
                    if (StringUtils.isNotEmpty(outLossPackageString)) {
                        outLossPackage = Double.parseDouble(outLossPackageString);
                        res.put("outLossPackage", String.format("%.2f", outLossPackage));
                    }

                    //输入单播报文的个数
                    double inUcastPkts = 0.0;
                    String inUcastPktsStr = eleObject.getString(SnmpConstant.inUcastPktsOid);
                    if (StringUtils.isNotEmpty(inUcastPktsStr) && !inUcastPktsStr.equals("noSuchInstance")) {
                        inUcastPkts = Double.parseDouble(inUcastPktsStr);
                        res.put("inUcastPkts", String.format("%.2f", inUcastPkts));
                    }

                    //输入非单播报文的个数
                    double inNUcastPkts = 0.0;
                    String inNUcastPktsStr = eleObject.getString(SnmpConstant.inNUcastPktsOid);
                    if (StringUtils.isNotEmpty(inNUcastPktsStr) && !inNUcastPktsStr.equals("noSuchInstance")) {
                        inNUcastPkts = Double.parseDouble(inNUcastPktsStr);
                        res.put("inNUcastPkts", String.format("%.2f", inNUcastPkts));
                    }

                    //输出单播报文的个数
                    double outUcastPkts = 0.0;
                    String outUcastPktsStr = eleObject.getString(SnmpConstant.outUcastPktsOid);
                    if (StringUtils.isNotEmpty(outUcastPktsStr) && !outUcastPktsStr.equals("noSuchInstance")) {
                        outUcastPkts = Double.parseDouble(outUcastPktsStr);
                        res.put("outUcastPkts", String.format("%.2f", outUcastPkts));
                    }

                    //输出非单播报文的个数
                    double outNUcastPkts = 0.0;
                    String outNUcastPktsStr = eleObject.getString(SnmpConstant.outNUcastPktsOid);
                    if (StringUtils.isNotEmpty(outNUcastPktsStr) && !outNUcastPktsStr.equals("noSuchInstance")) {
                        outNUcastPkts = Double.parseDouble(outNUcastPktsStr);
                        res.put("outNUcastPkts", String.format("%.2f", outNUcastPkts));
                    }

//                    //输出错误包率
//                    if (outUcastPkts > 0 && outNUcastPkts > 0) {
//                        double rate = outErrorPackage / (outUcastPkts + outNUcastPkts) * 100;
//                        res.put("outErrorPackageRate", Double.valueOf(String.format("%.2f", rate)));
//                    } else {
//                        res.put("outErrorPackageRate", 0.0);
//                    }
//
//                    //输入错误包率
//                    if (inUcastPkts > 0 && inNUcastPkts > 0) {
//                        double rate = inErrorPackage / (inUcastPkts + inNUcastPkts) * 100;
//                        res.put("inErrorPackageRate", Double.valueOf(String.format("%.2f", rate)));
//                    } else {
//                        res.put("inErrorPackageRate", 0.0);
//                    }
//
//                    //输出丢包率
//                    if (outUcastPkts > 0 && outNUcastPkts > 0) {
//                        double rate = outLossPackage / (outUcastPkts + outNUcastPkts) * 100;
//                        res.put("outLossPackageRate", Double.valueOf(String.format("%.2f", rate)));
//                    } else {
//                        res.put("outLossPackageRate", 0.0);
//                    }
//
//                    //输入丢包率
//                    if (inUcastPkts > 0 && inNUcastPkts > 0) {
//                        double rate = inLossPackage / (inUcastPkts + inNUcastPkts) * 100;
//                        res.put("inLossPackageRate", Double.valueOf(String.format("%.2f", rate)));
//                    } else {
//                        res.put("inLossPackageRate", 0.0);
//                    }

                    portInfoList.add(res);
                }
            }


            // 间隔一秒再次读取数据 -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=
            Thread.sleep(5000);

            long twoBeginTime = System.currentTimeMillis();
            JSONObject tableDataTemp = snmpUtils.getPduTableView(tableTopOid);
            if (tableDataTemp != null && !tableDataTemp.isEmpty()) {
                JSONArray dataArray = tableDataTemp.getJSONArray("dataList");
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject eleObject = dataArray.getJSONObject(i);
                    Map<String, Object> res = new HashMap<>();

                    //索引
                    res.put("index", eleObject.getString(SnmpConstant.indexOid));

                    //输入流量
                    String inFlow = eleObject.getString(SnmpConstant.inFlowOid);
                    if (StringUtils.isNotEmpty(inFlow)) {
                        //原始单位为Byte,转换为MByte
                        Double inputFlow = Double.parseDouble(inFlow);
                        inAndOutAfter += inputFlow;
                        inTotalAfter += inputFlow;
                        res.put("inputFlow", String.format("%.2f", inputFlow));
                    }

                    //输出流量
                    String outFlow = eleObject.getString(SnmpConstant.outFlowOid);
                    if (StringUtils.isNotEmpty(outFlow)) {
                        //原始单位为Byte,转换为MByte
                        Double outputFlow = Double.parseDouble(outFlow);
                        inAndOutAfter += outputFlow;
                        outTotalAfter += outputFlow;
                        res.put("outputFlow", String.format("%.2f", outputFlow));
                    }

                    //输入错误包数
                    double inErrorPackage = 0.0;
                    String inErrorPackageString = eleObject.getString(SnmpConstant.inErrorsOid);
                    if (StringUtils.isNotEmpty(inErrorPackageString)) {
                        inErrorPackage = Double.parseDouble(inErrorPackageString);
                        res.put("inErrorPackage", String.format("%.2f", inErrorPackage));
                    }

                    //输出错误包数
                    double outErrorPackage = 0.0;
                    String outErrorPackageString = eleObject.getString(SnmpConstant.outErrorsOid);
                    if (StringUtils.isNotEmpty(outErrorPackageString)) {
                        outErrorPackage = Double.parseDouble(outErrorPackageString);
                        res.put("outErrorPackage", String.format("%.2f", outErrorPackage));
                    }

                    //输入丢失错误包数
                    double inLossPackage = 0.0;
                    String inLossPackageString = eleObject.getString(SnmpConstant.inLossPackageOid);
                    if (StringUtils.isNotEmpty(inLossPackageString)) {
                        inLossPackage = Double.parseDouble(inLossPackageString);
                        res.put("inLossPackage", String.format("%.2f", inLossPackage));
                    }

                    //输出丢失错误包数
                    double outLossPackage = 0.0;
                    String outLossPackageString = eleObject.getString(SnmpConstant.outLossPackageOid);
                    if (StringUtils.isNotEmpty(outLossPackageString)) {
                        outLossPackage = Double.parseDouble(outLossPackageString);
                        res.put("outLossPackage", String.format("%.2f", outLossPackage));
                    }
                    //输入单播报文的个数
                    double inUcastPkts = 0.0;
                    String inUcastPktsStr = eleObject.getString(SnmpConstant.inUcastPktsOid);
                    if (StringUtils.isNotEmpty(inUcastPktsStr) && !inUcastPktsStr.equals("noSuchInstance")) {
                        inUcastPkts = Double.parseDouble(inUcastPktsStr);
                        res.put("inUcastPkts", String.format("%.2f", inUcastPkts));
                    }

                    //输入非单播报文的个数
                    double inNUcastPkts = 0.0;
                    String inNUcastPktsStr = eleObject.getString(SnmpConstant.inNUcastPktsOid);
                    if (StringUtils.isNotEmpty(inNUcastPktsStr) && !inNUcastPktsStr.equals("noSuchInstance")) {
                        inNUcastPkts = Double.parseDouble(inNUcastPktsStr);
                        res.put("inNUcastPkts", String.format("%.2f", inNUcastPkts));
                    }

                    //输出单播报文的个数
                    double outUcastPkts = 0.0;
                    String outUcastPktsStr = eleObject.getString(SnmpConstant.outUcastPktsOid);
                    if (StringUtils.isNotEmpty(outUcastPktsStr) && !outUcastPktsStr.equals("noSuchInstance")) {
                        outUcastPkts = Double.parseDouble(outUcastPktsStr);
                        res.put("outUcastPkts", String.format("%.2f", outUcastPkts));
                    }

                    //输出非单播报文的个数
                    double outNUcastPkts = 0.0;
                    String outNUcastPktsStr = eleObject.getString(SnmpConstant.outNUcastPktsOid);
                    if (StringUtils.isNotEmpty(outNUcastPktsStr) && !outNUcastPktsStr.equals("noSuchInstance")) {
                        outNUcastPkts = Double.parseDouble(outNUcastPktsStr);
                        res.put("outNUcastPkts", String.format("%.2f", outNUcastPkts));
                    }
                    portInfoTempList.add(res);
                }
            }

            long timeDiff = (twoBeginTime - oneBeginTime) / 1000;

            resultMap.put("netInOut", inAndOutAfter);
            Double inoutSpeedValue = (inAndOutAfter - inAndOutBefore) / timeDiff;
            resultMap.put("inoutSpeed", inoutSpeedValue < 0 ? 0 : inoutSpeedValue);
            Double inSpeedValue = (inTotalAfter - inTotalBefore) / timeDiff;
            resultMap.put("inSpeed", inSpeedValue < 0 ? 0 : inSpeedValue);
            Double outSpeedValue = (outTotalAfter - outTotalBefore) / timeDiff;
            resultMap.put("outSpeed", outSpeedValue < 0 ? 0 : outSpeedValue);

            List<Map<String, Object>> portInfoList2 = new ArrayList<>();
            for (Map<String, Object> map : portInfoList) {
                String str1 = (String) map.get("index");
                Integer index = Integer.valueOf(str1);
                for (Map<String, Object> map1 : portInfoTempList) {
                    String str2 = (String) map1.get("index");
                    Integer index1 = Integer.valueOf(str2);
                    if (Objects.equals(index, index1)) {
                        Double in = Double.valueOf(StringUtils.isNotEmpty(map.get("inputFlow") + "") ? map.get("inputFlow") + "" : "0");
                        Double out = Double.valueOf(StringUtils.isNotEmpty(map.get("outputFlow") + "") ? map.get("outputFlow") + "" : "0");
                        Double in1 = Double.valueOf(StringUtils.isNotEmpty(map1.get("inputFlow") + "") ? map1.get("inputFlow") + "" : "0");
                        Double out1 = Double.valueOf(StringUtils.isNotEmpty(map1.get("outputFlow") + "") ? map1.get("outputFlow") + "" : "0");

                        Double inOutSpeed1 = (in1 + out1 - in - out) / timeDiff;
                        map.put("inOutSpeed", inOutSpeed1);
                        Double inSpeed = (in1 - in) / timeDiff;
                        map.put("inSpeed", inSpeed);
                        Double outSpeed = (out1 - out) / timeDiff;
                        map.put("outSpeed", outSpeed);

                        //输入错误包数
                        Double inErrorPackage = Double.valueOf(StringUtils.isNotEmpty(map.get("inErrorPackage") + "") ? map.get("inErrorPackage") + "" : "0");
                        //输出错误包数
                        Double outErrorPackage = Double.valueOf(StringUtils.isNotEmpty(map.get("outErrorPackage") + "") ? map.get("outErrorPackage") + "" : "0");
                        //输入丢失错误包数
                        Double inLossPackage = Double.valueOf(StringUtils.isNotEmpty(map.get("inLossPackage") + "") ? map.get("inLossPackage") + "" : "0");
                        //输出丢失错误包数
                        Double outLossPackage = Double.valueOf(StringUtils.isNotEmpty(map.get("outLossPackage") + "") ? map.get("outLossPackage") + "" : "0");
                        //输入单播报文的个数
                        Double inUcastPkts = Double.valueOf(StringUtils.isNotEmpty(map.get("inUcastPkts") + "") ? map.get("inUcastPkts") + "" : "0");
                        //输入非单播报文的个数
                        Double inNUcastPkts = Double.valueOf(StringUtils.isNotEmpty(map.get("inNUcastPkts") + "") ? map.get("inNUcastPkts") + "" : "0");
                        //输出单播报文的个数
                        Double outUcastPkts = Double.valueOf(StringUtils.isNotEmpty(map.get("outUcastPkts") + "") ? map.get("outUcastPkts") + "" : "0");
                        //输出非单播报文的个数
                        Double outNUcastPkts = Double.valueOf(StringUtils.isNotEmpty(map.get("outNUcastPkts") + "") ? map.get("outNUcastPkts") + "" : "0");

                        Double inErrorPackage1 = Double.valueOf(StringUtils.isNotEmpty(map1.get("inErrorPackage") + "") ? map1.get("inErrorPackage") + "" : "0");
                        Double outErrorPackage1 = Double.valueOf(StringUtils.isNotEmpty(map1.get("outErrorPackage") + "") ? map1.get("outErrorPackage") + "" : "0");
                        Double inLossPackage1 = Double.valueOf(StringUtils.isNotEmpty(map1.get("inLossPackage") + "") ? map1.get("inLossPackage") + "" : "0");
                        Double outLossPackage1 = Double.valueOf(StringUtils.isNotEmpty(map1.get("outLossPackage") + "") ? map1.get("outLossPackage") + "" : "0");
                        Double inUcastPkts1 = Double.valueOf(StringUtils.isNotEmpty(map1.get("inUcastPkts") + "") ? map1.get("inUcastPkts") + "" : "0");
                        Double inNUcastPkts1 = Double.valueOf(StringUtils.isNotEmpty(map1.get("inNUcastPkts") + "") ? map1.get("inNUcastPkts") + "" : "0");
                        Double outUcastPkts1 = Double.valueOf(StringUtils.isNotEmpty(map1.get("outUcastPkts") + "") ? map1.get("outUcastPkts") + "" : "0");
                        Double outNUcastPkts1 = Double.valueOf(StringUtils.isNotEmpty(map1.get("outNUcastPkts") + "") ? map1.get("outNUcastPkts") + "" : "0");

                        double inNum = inUcastPkts1 + inNUcastPkts1 - inUcastPkts - inNUcastPkts;
                        double outNum = outUcastPkts1 + outNUcastPkts1 - outUcastPkts - outNUcastPkts;
                        //输出错误包率
                        if (outNum > 0) {
                            double rate = (outErrorPackage1 - outErrorPackage) / outNum * 100;
                            map.put("outErrorPackageRate", Double.valueOf(String.format("%.2f", rate)));
                        } else {
                            map.put("outErrorPackageRate", 0.0);
                        }

                        //输入错误包率
                        if (inNum > 0) {
                            double rate = (inErrorPackage1 - inErrorPackage) / inNum * 100;
                            map.put("inErrorPackageRate", Double.valueOf(String.format("%.2f", rate)));
                        } else {
                            map.put("inErrorPackageRate", 0.0);
                        }

                        //输出丢包率
                        if (outNum > 0) {
                            double rate = (outLossPackage1 - outLossPackage) / outNum * 100;
                            map.put("outLossPackageRate", Double.valueOf(String.format("%.2f", rate)));
                        } else {
                            map.put("outLossPackageRate", 0.0);
                        }

                        //输入丢包率
                        if (inNum > 0) {
                            double rate = (inLossPackage1 - inLossPackage) / inNum * 100;
                            map.put("inLossPackageRate", Double.valueOf(String.format("%.2f", rate)));
                        } else {
                            map.put("inLossPackageRate", 0.0);
                        }
                    }
                }
                portInfoList2.add(map);
            }

            resultMap.put("portInfo", portInfoList2);
            return resultMap;
        } catch (Exception e) {
            log.error("获取端口信息异常！", e);
            return null;
        }
    }
}
