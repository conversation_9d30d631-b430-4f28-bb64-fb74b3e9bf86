package com.yuanqiao.insight.collection.collector.collector.impl;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.adapter.inter.DeviceCodec;
import com.yuanqiao.insight.acore.collector.Collector;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.acore.scheduler.SchedulerManagerInter;
import com.yuanqiao.insight.acore.statuscache.StatusData;
import com.yuanqiao.insight.collection.collector.utils.InfluxDbUtils;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.dto.Pong;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class TerminalInfluxdbCollector implements Collector, DeviceCodec {
    private Device device;
    private SchedulerManagerInter schedulerManager;
    private static String CPU_USAGE="cpu_usage";
    private static String DICK_USAGE="dick_usage";
    private static String MEM_USAGE="mem_usage";

    TerminalChildCollector terminalChildCollector;
    @Autowired
    private ApplicationContext applicationContext;
    @Override
    public void init(Device device, SchedulerManagerInter schedulerManager) {
        //绑定设备
        this.device = device;
        //绑定调度管理器
        this.schedulerManager = schedulerManager;
    }

    @Override
    public void init(Device device, SchedulerManagerInter schedulerManager,ApplicationContext applicationContext) {
        //绑定设备
        this.device = device;
        //绑定调度管理器
        this.schedulerManager = schedulerManager;
        this.applicationContext=applicationContext;
    }

    @Override
    public void execute() {
        Map<String, StatusData> map=new HashMap<>();
        InfluxDbUtils influxDbUtils=new InfluxDbUtils(device.getConnectParam().get("address"),device.getConnectParam().get("username"),device.getConnectParam().get("password"),device.getConnectParam().get("datebase"));
        Pong pong=influxDbUtils.influxDB.ping();
        if(pong==null){
            return;
        }
       //设置cpu信息
        getCpu(influxDbUtils,map);
        //设置硬盘信息
        getDisk(influxDbUtils,map);
        //设置内存信息
        getMem(influxDbUtils,map);
        for(Map.Entry<String,StatusData> entry:map.entrySet()){
            StatusData statusData=entry.getValue();
            terminalChildCollector= (TerminalChildCollector) applicationContext.getBean("terminalChildCollector");
            terminalChildCollector.execute(statusData,this.device.getRate());
        }






        //1. 获取指定influxdb数据库中的数据
        //2. 按照子设备分组, 循环处理设备
        //2.1. 把数据带入解码器
        //2.2. 调用ShedulerManager, 设置子设备状态: 离线, 在线
        //2.3. 调用ShedulerManager, 发布子设备数据事件
        //2.4. 调用ShedulerManager, 设置子状态容器


    }

    private void getMem(InfluxDbUtils influxDbUtils, Map<String, StatusData> map) {
        int r=this.device.getRate()+10;
        String sql="SELECT free,slab,host FROM \"mem\" WHERE time > now() - "+r+"s";
        Query CpuQuery = new Query(sql);
        QueryResult memResult=influxDbUtils.influxDB.query(CpuQuery);
        try{
            QueryResult.Series series=memResult.getResults().get(0).getSeries().get(0);
            List values=series.getValues();
            for(Object o:values){
                List value= (List) o;
                StatusData deviceStatus=map.get(value.get(3));
                if(deviceStatus==null){
                    deviceStatus=new StatusData();
                    deviceStatus.setKey((String) value.get(2));
                    map.put(deviceStatus.getKey(),deviceStatus);
                }
                Double mem=((Double) value.get(2)/(Double)value.get(1))*1000;
                DecimalFormat df   = new DecimalFormat("######0.00");
                deviceStatus.getData().put(MEM_USAGE,df.format(mem));
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }
    private void getCpu(InfluxDbUtils influxDbUtils,Map<String, StatusData> map){
        int r=this.device.getRate()+10;
        String sql="SELECT 100-\"usage_idle\",\"host\" FROM \"cpu\" WHERE time > now() - "+r+"s and cpu='cpu-total'";

        Query CpuQuery = new Query(sql);

        QueryResult cpuResult=influxDbUtils.influxDB.query(CpuQuery);
        try{
            QueryResult.Series series=cpuResult.getResults().get(0).getSeries().get(0);
            List values=series.getValues();
            for(Object o:values){
                List value= (List) o;
                StatusData deviceStatus=map.get(value.get(2));
                if(deviceStatus==null){
                    deviceStatus=new StatusData();
                    deviceStatus.setKey((String) value.get(2));
                    map.put(deviceStatus.getKey(),deviceStatus);
                }
                deviceStatus.getData().put(CPU_USAGE,value.get(1));
            }
        }catch (Exception e){

        }


    }
    private void getDisk(InfluxDbUtils influxDbUtils, Map<String, StatusData> map) {
        int r=this.device.getRate()+10;
        String sql="SELECT \"inodes_used\",\"inodes_total\",\"host\" FROM \"disk\" WHERE time > now() - "+r+"s";

        Query CpuQuery = new Query(sql);

        QueryResult diskResult=influxDbUtils.influxDB.query(CpuQuery);

        try{
            QueryResult.Series series=diskResult.getResults().get(0).getSeries().get(0);
            List values=series.getValues();
            for(Object o:values){
                List value= (List) o;
                StatusData deviceStatus=map.get(value.get(3));
                if(deviceStatus==null){
                    deviceStatus=new StatusData();
                    deviceStatus.setKey((String) value.get(2));
                    map.put(deviceStatus.getKey(),deviceStatus);
                }
                Double dick=((Double) value.get(1)/(Double)value.get(2));
                DecimalFormat df   = new DecimalFormat("######0.00");
                deviceStatus.getData().put(DICK_USAGE,df.format(dick));
            }
        }catch (Exception e){
        }
    }

    @Override
    public JSONObject decode(String prefix, String message, JSONObject metadataObject) {
        return null;
    }
}
