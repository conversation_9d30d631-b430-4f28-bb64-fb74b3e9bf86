package com.yuanqiao.insight.collection.collector.collector.impl.Snmp.OsSnmp;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.collection.collector.utils.SNMPCodecAndCollectUtils;
import com.yuanqiao.insight.collection.collector.utils.SNMPCodecInterface;
import com.yuanqiao.insight.common.util.common.TimeUtils;
import com.yuanqiao.insight.common.util.snmp.SNMPUtils;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class OsSnmpCodec_Common implements SNMPCodecInterface {

    /**
     * 读取设备数据，返回Jason格式
     *
     * @param snmpUtils
     * @param metadataList
     * @return
     */

    //最外层jsonObject
    JSONObject jsonObject = new JSONObject();
    //公共结果集
    JSONObject commonMap = new JSONObject();
    String osType;

    @Override
    public JSONObject dataCodec(SNMPUtils snmpUtils, List<ProertyMetadata> metadataList, String stcaKey, Device device, SNMPCodecAndCollectUtils snmpCodecAndCollectUtils) {
        jsonObject.clear();
        commonMap.clear();
//
//        String cpuInfoCode = "";
//        for (ProertyMetadata item : metadataList) {
//            // cpuInfo ：OID ：*******.2.1.25.3.3.1.2
//            if (StringUtils.isNotEmpty(item.getOid())) {
//                cpuInfoCode = item.getCode();
//                Map<String, String> pduWalk = new HashMap<String, String>();
//                try {
//                    pduWalk = snmpUtils.getPDUWalk(item.getOid());
//                } catch (Exception e) {
//                    log.error("OsSnmpCodec_Common.getPdu -- snmpUtils.getPDUWalk()异常！", e);
//                }
//                JSONArray jsonArray = new JSONArray();
//                for (Map.Entry<String, String> entry : pduWalk.entrySet()) {
//                    jsonArray.add(Double.parseDouble(String.format("%.2f", Double.parseDouble(entry.getValue()))));
//                }
//                JSONObject jsonObject1 = new JSONObject();
//                jsonObject1.put("name", item.getName());
//                jsonObject1.put("type", item.getDataType());
//                jsonObject1.put("value", jsonArray);
//                if (StringUtils.isNotEmpty(item.getFuncName())) {
//                    jsonObject1.put("value", new JSONArray());
//                }
//                jsonObject1.put("display", makeTableDisplay(jsonArray));
//                jsonObject.put(stcaKey + "_" + item.getCode(), jsonObject1);
//
//            }
//        }
        commonMap.put("sysUpTime", getSysUpTime(snmpUtils).get("sysUpTime"));

        commonMap.put("cpuRate", getCpuUsedRate(snmpUtils));
        commonMap.put("memRate", getPhyMemoryUsedRate(snmpUtils));
        commonMap.put("diskRate", getDiskUsedRate(snmpUtils));
        commonMap.put("process", getProcessInfo(snmpUtils));
        commonMap.put("disk", getDiskInfo(snmpUtils));
        commonMap.put("staticInfo", getStaticInfo(snmpUtils));
        commonMap.put("tcpCon", getTcpConnect(snmpUtils));
        commonMap.put("tcpInfo", getTcpInfoList(snmpUtils));
        commonMap.put("portInfo", getPortInfoList(snmpUtils));
        commonMap.put("serviceInfo", getServiceInfoList(snmpUtils));
        commonMap.put("bondInfo", getBondInfoList(snmpUtils));
        for (ProertyMetadata item : metadataList) {
            if (StringUtils.isNotEmpty(item.getDataType())) {
//                if (StringUtils.isNotEmpty(item.getDataType()) && !item.getCode().equals(cpuInfoCode)) {
                jsonObject = snmpCodecAndCollectUtils.switchByDataType(item, stcaKey, jsonObject, commonMap, snmpUtils);
            }
        }

        return jsonObject;

    }


    /* ------------------------------------------------------------------------------------------- */
    /**
     * 获取设备运行时长
     *
     * @param snmpUtils
     * @return
     */
    private Map<String, String> getSysUpTime(SNMPUtils snmpUtils) {
        HashMap<String, String> map = new HashMap<>();
        try {
            String sysUpTime = snmpUtils.getPDU("*******.*******.0");
            if (StringUtils.isNotEmpty(sysUpTime) && !sysUpTime.equalsIgnoreCase("noSuchObject")
                    && !sysUpTime.equalsIgnoreCase("noSuchInstance")) {
                // 去除字符串中的逗号
                sysUpTime = sysUpTime.replace(",", "");

                // 使用正则表达式匹配时间信息
                Pattern pattern = Pattern.compile("(?:(\\d+) days?)?(?:\\s*,?\\s*(\\d+):(\\d+)(?::(\\d+(?:\\.\\d+)?))?)?");
                Matcher matcher = pattern.matcher(sysUpTime);

                int days = 0, hours = 0, minutes = 0, seconds = 0;
                double milliseconds = 0;

                if (matcher.find()) {
                    if (matcher.group(1) != null) {
                        days = Integer.parseInt(matcher.group(1));
                    }
                    if (matcher.group(2) != null) {
                        hours = Integer.parseInt(matcher.group(2));
                    }
                    if (matcher.group(3) != null) {
                        minutes = Integer.parseInt(matcher.group(3));
                    }
                    if (matcher.group(4) != null) {
                        String[] secondsParts = matcher.group(4).split("\\.");
                        seconds = Integer.parseInt(secondsParts[0]);
                        if (secondsParts.length > 1) {
                            milliseconds = Double.parseDouble("0." + secondsParts[1]);
                        }
                    }
                }
                // 计算总毫秒数
                BigInteger totalMilliseconds = BigInteger.valueOf(days * 24 * 60 * 60 * 1000L)
                        .add(BigInteger.valueOf(hours * 60 * 60 * 1000L))
                        .add(BigInteger.valueOf(minutes * 60 * 1000L))
                        .add(BigInteger.valueOf((long) (seconds * 1000 + milliseconds * 1000)));
                sysUpTime = TimeUtils.getDistanceTime(totalMilliseconds);
                map.put("sysUpTime", sysUpTime);
            }
        } catch (Exception e) {
            log.error("获取运行时长出错", e);
            map.put("sysUpTime", "");
        }
        return map;
    }
    //array组装table格式的display
    public JSONArray makeTableDisplay(JSONArray list) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        JSONArray jsonArrayOut = new JSONArray();
        JSONObject disObject = new JSONObject();
        disObject.put("type", "table");
        disObject.put("timestamp", sdf.format(System.currentTimeMillis()));
        JSONArray jsonArray = new JSONArray();
        for (int i = 0; i < list.size(); i++) {
            try {
                Map<String, Object> o = (Map<String, Object>) list.get(i);
                Collection<Object> values = o.values();
                String s = JSONArray.toJSONString(values);  //复制一份出来
                JSONArray array = JSONArray.parseArray(s);
                jsonArray.add(array);
            } catch (Exception e) {
                Double d = (Double) list.get(i);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("name", "cpu索引");
                jsonObject.put("value", i);
                jsonObject.put("unit", "");
                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put("name", "cpu使用率");
                jsonObject1.put("value", d);
                jsonObject1.put("unit", "%");
                JSONArray jsonArray1 = new JSONArray();
                jsonArray1.add(jsonObject);
                jsonArray1.add(jsonObject1);
                jsonArray.add(jsonArray1);
            }
        }
        disObject.put("value", jsonArray);
        jsonArrayOut.add(disObject);
        return jsonArrayOut;
    }

    /* ------------------------------------------------------------------------------------------- */

    //CPU平均使用率
    private Double getCpuUsedRate(SNMPUtils snmpUtils) {
        try {
            Map<String, String> CPUMap = snmpUtils.getPDUWalk("*******.2.1.25.3.3.1.2");
//            log.error(" ****** CPUMap：" + CPUMap);
            // cpu使用率
            double cpuUsed = 0;
            int i = 0;
            if (CPUMap != null && CPUMap.size() > 0) {
                for (Map.Entry<String, String> e : CPUMap.entrySet()) {
//                    log.error(" ****** key ：" + e.getKey() + " **** value ：" + e.getValue());
                    cpuUsed += Integer.parseInt(e.getValue());
                }
                cpuUsed = cpuUsed / CPUMap.size();
//                log.error(" ****** cpuUsed：" + cpuUsed);
            } else {
                cpuUsed = 0;
            }
            return Double.parseDouble(String.format("%.2f", cpuUsed));
        } catch (Exception e) {
            log.error("获取CPU使用率出错", e);
            return null;
        }
    }

    //内存使用率
    private Double getPhyMemoryUsedRate(SNMPUtils snmpUtils) {
        try {

            //判断操作系统是否为Linux系统，Linux系统的内存使用率需要换算
            if (osType != null && !osType.equals("未知")) {
                if (osType.equals("linux") || osType.equals("中标麒麟") || osType.equals("银河麒麟")) {
                    double total = Double.parseDouble(snmpUtils.getPDU("*******.4.1.2021.4.5.0"));
                    double used = Double.parseDouble(snmpUtils.getPDU("*******.4.1.2021.4.15.0"));
                    double useRate = used / total * 100;
                    return Double.parseDouble(String.format("%.2f", useRate));
                }
            } else {
                // 主键
                Map<String, String> diskKeyList = snmpUtils.getPDUWalk(".*******.********.3.1.1");
                // 硬盘使用大小
                Map<String, String> diskUseList = snmpUtils.getPDUWalk("*******.********.3.1.6");
                // 硬盘大小
                Map<String, String> diskSizeList = snmpUtils.getPDUWalk("*******.********.3.1.5");
                // 硬盘类型
                Map<String, String> diskTypeList = snmpUtils.getPDUWalk("*******.********.3.1.2");

                double useRate = 0;
                if (diskKeyList != null && diskKeyList.size() > 0) {
                    for (Map.Entry<String, String> entrys : diskKeyList.entrySet()) {
                        String key = entrys.getValue();

                        // 取内存使用率
                        if ("*******.********.1.2".equals(diskTypeList.get("*******.********.3.1.2." + key))) {
                            String use = diskUseList.get("*******.********.3.1.6." + key);
//                            log.info("###内存已使用量：" + use);
                            String all = diskSizeList.get("*******.********.3.1.5." + key);
//                            log.info("###内存总量：" + all);
                            if ((use != null && !"".equals(use)) && (all != null && !"".equals(all))) {
                                useRate = (Double.parseDouble(use) / Double.parseDouble(all)) * 100;
//                                log.info("####内存使用率 ：" + Double.parseDouble(String.format("%.2f", useRate)));
                                return Double.parseDouble(String.format("%.2f", useRate));
                            } else {
                                log.error("内存使用率为空");
                                return null;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取内存使用率出错", e);
            return null;
        }
        return null;
    }

    //磁盘使用率
    private Double getDiskUsedRate(SNMPUtils snmpUtils) {
        try {
            // 主键
            Map<String, String> diskKeyList = snmpUtils.getPDUWalk(".*******.********.3.1.1");
            // 硬盘大小
            Map<String, String> diskSizeList = snmpUtils.getPDUWalk("*******.********.3.1.5");
            // 硬盘使用大小
            Map<String, String> diskUseList = snmpUtils.getPDUWalk("*******.********.3.1.6");
            // 硬盘分配单位大小
            Map<String, String> diskUtilsList = snmpUtils.getPDUWalk("*******.********.3.1.4");
            double usedRate = 0.0;
            double countSize = 0.0;
            double countUsed = 0.0;
            HashMap<String, Double> map = new HashMap<>();
            if (diskKeyList != null && diskKeyList.size() > 0) {
                for (Map.Entry<String, String> entrys : diskKeyList.entrySet()) {
                    String key = entrys.getValue();
                    String diskSize = diskSizeList.get("*******.********.3.1.5." + key);
                    String diskUtils = diskUtilsList.get("*******.********.3.1.4." + key);
                    if (null != diskSize) {
                        countSize += (Double.parseDouble(diskSize) * Double.parseDouble(diskUtils)) / 1024 / 1024;
                        String diskUse = diskUseList.get("*******.********.3.1.6." + key);
                        countUsed += Double.parseDouble(diskUse) * Double.parseDouble(diskUtils) / 1024 / 1024;
                    }
                }
//                log.info("$$$$硬盘已使用量：" + countUsed);
//                log.info("$$$$硬盘总量：" + countSize);
                usedRate = countUsed / countSize * 100;
//                log.info("$$$$硬盘使用率：" + usedRate);
                return Double.parseDouble(String.format("%.2f", usedRate));
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("获取磁盘使用率出错", e);
            return null;
        }
    }

    //进程信息
    private List<Map<String, Object>> getProcessInfo(SNMPUtils snmpUtils) {

        try {
            Map<String, String> processInfoMap = snmpUtils.getPDUWalk("*******.2.1.25.4.2.1.2");//name
            Iterator<String> iter = processInfoMap.values().iterator();
            Iterator<String> nameiter = processInfoMap.keySet().iterator();
            List<Map<String, Object>> processList = new ArrayList<Map<String, Object>>();

            Map<String, String> processCPUList = snmpUtils.getPDUWalk("*******.2.1.25.5.1.1.1");//cpu time
            //进程cpu使用率    （当前进程耗费的cpu总时长/所有进程cpu总时长）*100  厘秒数
            //进程cpu总时间
            double cpuCount = 0;
            if (processInfoMap != null && processInfoMap.size() > 0) {
                for (Map.Entry<String, String> e : processCPUList.entrySet()) {
                    if (e != null && e.getValue() != null && !"".equals(e.getValue()) && !"Null".equals(e.getValue()) && !"null".equals(e.getValue())) {
                        cpuCount += getUnsignedLong(Long.valueOf(e.getValue()));
                    }
                }
                while (iter.hasNext() && nameiter.hasNext()) {
                    Map<String, Object> res = new HashMap<String, Object>();
                    String processName = iter.next();
                    String processListtest = nameiter.next();
                    int processPIDindex = processListtest.lastIndexOf(".");
                    String processPID = processListtest.substring(processPIDindex);
                    String runPerMemId = "*******.2.1.25.5.1.1.2" + processPID;//mem
                    String runPercpuId = "*******.2.1.25.5.1.1.1" + processPID;//cpu
                    String runPathId = "*******.2.1.25.4.2.1.4" + processPID;//path
                    String runStatus = "*******.2.1.25.4.2.1.7" + processPID;//path
                    String runCmd = "*******.2.1.25.4.2.1.5" + processPID;//path

                    //另一种无需截取冒号的正则 “\b([0-9A-Za-z]{2}[:])+([0-9A-Za-z]{2})\b”

                    // -------------------------pName-----------------------------
                    String regex = "^[A-Fa-f0-9]*$";
                    if (processName.replace(":", "").matches(regex)) {
                        processName = getChinese(processName);
                    }
                    res.put("procName", processName);

                    // -------------------------PID-----------------------------
                    res.put("procId", processPID.substring(processPID.indexOf(".") + 1));

                    // -------------------------path-----------------------------
                    String Path = snmpUtils.getPDU(runPathId);
                    if (Path.replace(":", "").matches(regex)) {
                        Path = getChinese(Path);
                    }
                    res.put("procPath", Path);
                    res.put("procCmd", snmpUtils.getPDU(runCmd));
                    // -------------------------运行占用的内存-----------------------------
                    String mem = snmpUtils.getPDU(runPerMemId);
                    if (isNumeric(mem)) {
                        res.put("procMem", Double.parseDouble(mem));
                    }

                    // -------------------------运行时CPU使用率--------------------------------
                    String cpu = snmpUtils.getPDU(runPercpuId);
                    if (isNumeric(cpu)) {
                        res.put("procCpu", String.format("%.2f", (Double.valueOf(cpu) / cpuCount) * 100));
                    }

                    String status = snmpUtils.getPDU(runStatus);
                    if ("1".equals(status)) {
                        status = "运行";
                    } else if ("2".equals(status)) {
                        status = "就绪";
                    } else if ("3".equals(status)) {
                        status = "阻塞";
                    } else if ("4".equals(status)) {
                        status = "挂起";
                    } else {
                        status = "其它";
                    }
                    res.put("procStatus", status);

                    processList.add(res);
                }
            }
            if (processList != null && processList.size() != 0) {
                return processList;
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("获取进程信息列表出错" + e.getMessage());
        }
        return null;
    }

    //硬盘信息
    private List<Map<String, Object>> getDiskInfo(SNMPUtils snmpUtils) {
        try {
            // --------------------------处理硬盘信息----------------------------------
            List<Map<String, Object>> diskInfoList = new ArrayList<Map<String, Object>>();
            // 主键
            Map<String, String> diskKeyList = snmpUtils.getPDUWalk(".*******.********.3.1.1");
            // 硬盘大小
            Map<String, String> diskSizeList = snmpUtils.getPDUWalk("*******.********.3.1.5");
            // 硬盘名字
            Map<String, String> diskNameList = snmpUtils.getPDUWalk("*******.********.3.1.3");
            // 硬盘使用大小
            Map<String, String> diskUseList = snmpUtils.getPDUWalk("*******.********.3.1.6");
            // 硬盘分配单位大小
            Map<String, String> diskUtilsList = snmpUtils.getPDUWalk("*******.********.3.1.4");

            // 遍历主键获取对应值
            if (diskKeyList != null && diskKeyList.size() > 0) {
                for (Map.Entry<String, String> entrys : diskKeyList.entrySet()) {
                    String key = entrys.getValue();

                    //分别读取每个盘的信息，封装成Map并放入list集合中
                    Map<String, Object> res = new HashMap<String, Object>();
                    String diskName = diskNameList.get("*******.********.3.1.3." + key);
                    res.put("diskName", toStringHex(diskName.split(":")[0]));

                    // 获取格式化后的磁盘名称
                    String formattedDiskName = res.get("diskName").toString().toLowerCase();

                    // 只过滤内存相关挂载点
                    if (isMemoryRelatedMount(formattedDiskName)) {
                        continue;
                    }

                    String diskUtils = diskUtilsList.get("*******.********.3.1.4." + key);
                    String diskSize = diskSizeList.get("*******.********.3.1.5." + key);

                    if (null != diskSize && isNumeric(diskSize)) {
                        double countSize = (Double.parseDouble(diskSize) * Double.parseDouble(diskUtils));
                        String diskUse = diskUseList.get("*******.********.3.1.6." + key);
                        double countUsed = Double.parseDouble(diskUse) * Double.parseDouble(diskUtils);
                        double countNotUsed = (Double.parseDouble(diskSize) - Double.parseDouble(diskUse)) * Double.parseDouble(diskUtils);

                        res.put("diskSize", countSize);
                        res.put("diskUsed", countUsed);
                        res.put("diskNotUsed", countNotUsed);

                        if ("0".equals(diskSize)) {
                            res.put("diskUsedRate", Double.parseDouble("0"));
                            res.put("diskNotUsedRate", Double.parseDouble("0"));
                        } else {
                            res.put("diskUsedRate", Double.parseDouble(String.format("%.2f", countUsed / countSize * 100)));
                            res.put("diskNotUsedRate", Double.parseDouble(String.format("%.2f", countNotUsed / countSize * 100)));
                        }
                    }

                    // 将过滤后的磁盘信息添加到结果列表
                    diskInfoList.add(res);
                }
            }

            // 磁盘排序（使用Java 8 Stream API简化代码）
            Collections.sort(diskInfoList, Comparator.comparing(m -> m.get("diskName").toString()));

            return diskInfoList.isEmpty() ? null : diskInfoList;

        } catch (Exception e) {
            log.error("获取硬盘信息列表出错", e);
        }
        return null;
    }

    /**
     * 判断是否为内存相关挂载点
     *
     * @param diskName 磁盘名称（已转为小写）
     * @return true表示需要排除的内存相关挂载点
     */
    private boolean isMemoryRelatedMount(String diskName) {
        // 内存相关关键词
        Set<String> memoryKeywords = new HashSet<>(Arrays.asList("memory", "swap", "physical memory", "virtual memory"));

        // 检查是否包含内存相关关键词
        for (String keyword : memoryKeywords) {
            if (diskName.contains(keyword)) {
                return true;
            }
        }
        return false;
    }


    //获取设备静态信息
    private Map<String, String> getStaticInfo(SNMPUtils snmpUtils) {
        HashMap<String, String> map = new HashMap<>();
        try {
            //主机名称
            String hostName = snmpUtils.getPDU("*******.*******.0");
            //log.error("*-*-*--*---*-*--*- hostName：" + hostName);
            //系统描述
            String sysDesc = snmpUtils.getPDU("*******.*******.0");
            //log.error("*-*-*--*---*-*--*- sysDesc：" + sysDesc);
            //系统内核版本
            String kernelVersion = "";
            if (StringUtils.isNotEmpty(sysDesc)) {
                try {
                    kernelVersion = sysDesc.substring(sysDesc.indexOf("Software:") + 9, sysDesc.indexOf("(Build"));
                } catch (Exception e) {
                    kernelVersion = sysDesc.split(" ")[2];
                }
            }
            //log.error("*-*-*--*---*-*--*- kernelVersion：" + kernelVersion);

            //系统运行时间
            String sysRunTime = "";
            sysRunTime = snmpUtils.getPDU("*******.********.1.0");
            if (StringUtils.isEmpty(sysRunTime) || sysRunTime.equals("noSuchInstance")) {
                sysRunTime = snmpUtils.getPDU("*******.*******.0");
            }
            //log.error("*-*-*--*---*-*--*- sysRunTime：" + sysRunTime);
            //内存信息
            String memDataString = "";
            memDataString = snmpUtils.getPDU("*******.********.2.0");

            map.put("hostName", hostName);
            map.put("sysDesc", sysDesc);
            map.put("kernelVersion", kernelVersion);
            map.put("sysRunTime", sysRunTime);
            map.put("memDataString", memDataString);
        } catch (Exception e) {
            log.error("获取系统静态信息出错", e);
        }
        return map;
    }

    /* ------------------------------------------------------------------------------------------- */

    //判断是否数字
    private static boolean isNumeric(String str) {
        if ("".equals(str)) {
            return false;
        }
        if (str == null) {
            return false;
        }
        for (int i = str.length(); --i >= 0; ) {
            if (!Character.isDigit(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    //中文处理
    private static String getChinese(String octetString) {
        try {
            String[] temps = octetString.split(":");
            byte[] bs = new byte[temps.length];
            for (int i = 0; i < temps.length; i++)
                bs[i] = (byte) Integer.parseInt(temps[i], 16);
            return new String(bs, "GBK");
        } catch (Exception e) {
            /*logger.error(e.getMessage());*/
            return null;
        }
    }

    /**
     * 无符号转换
     */
    public static double getUnsignedLong(long value) throws IOException {
        if (value >= 0) return new BigDecimal(value).doubleValue();

        long lowValue = value & 0x7fffffffffffffffL;
        return BigDecimal.valueOf(lowValue).add(BigDecimal.valueOf(Long.MAX_VALUE)).add(BigDecimal.valueOf(1)).doubleValue();
    }

    //字符转换
    private static String toStringHex(String s) {
        char startstr = s.charAt(0);
        String returnStr = "";
        s = s.replace(":", "");
        if (Character.isDigit(startstr)) {
            byte[] baKeyword = new byte[s.length() / 2];
            for (int i = 0; i < baKeyword.length; i++) {
                try {
                    baKeyword[i] = (byte) (0xff & Integer.parseInt(s.substring(i * 2, i * 2 + 2), 16));
                } catch (Exception e) {
                    log.error("" + e.getMessage());
                }
            }
            try {
                s = new String(baKeyword, "ISO-8859-1");// UTF-16le:Not
            } catch (Exception e1) {
                log.error("" + e1.getMessage());
            }
            returnStr = s;
        } else {
            returnStr = s;
        }
        return returnStr;
    }

    //OSType操作系统
    public void setOsType(String osType) {
        this.osType = osType;
    }

    //获取物模型子属性参数转为数组格式
    private String[] getPara(String para) {
        String[] split = para.split(",");
        return split;
    }

    //物模型二级子属性oid
    //判断物模型的子属性在拓展表中是否有oid，如果有就优先根据oid读取数据，如果没有就在公共map中读取数据
    private static JSONObject getValue(ProertyMetadata metadata, JSONObject everyInfoObject, SNMPUtils snmpUtils) {
        try {
            String pdu = snmpUtils.getPDU(metadata.getOid());
            everyInfoObject.put(metadata.getCode(), pdu);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return everyInfoObject;
    }

    //获取对应oid的value值
    private Double getWalkValueByOid(String code, Map<String, String> pduWalk) {
        Double value = 0.0;
        try {
            if (pduWalk != null && pduWalk.size() > 0) {
                for (Map.Entry<String, String> entry : pduWalk.entrySet()) {
                    value += Integer.parseInt(entry.getValue());
                }
            }
            return value;
        } catch (Exception e) {
            log.error("获取" + code + "出错" + e.getMessage());
            return value;
        }
    }

    /**
     * 获取tcp信息
     *
     * @param snmpUtils
     * @return
     */
    private List<Map<String, Object>> getTcpInfoList(SNMPUtils snmpUtils) {
        try {
            List<Map<String, Object>> tcpInfoList = new ArrayList<Map<String, Object>>();
            // 接口索引 状态
            Map<String, String> stateMap = snmpUtils.getPDUWalk("*******.********.1.1");
            //本地地址
            Map<String, String> localAddressMap = snmpUtils.getPDUWalk("*******.********.1.2");
            //本地端口
            Map<String, String> localPortMap = snmpUtils.getPDUWalk("*******.********.1.3");
            // 远程地址
            Map<String, String> remAddressMap = snmpUtils.getPDUWalk("*******.********.1.4");
            //远程端口
            Map<String, String> remPortMap = snmpUtils.getPDUWalk("*******.********.1.5");

            for (Map.Entry<String, String> entry : stateMap.entrySet()) {
                String keyString = entry.getKey().replace("*******.********.1.1.", "");
                Map<String, Object> res = new HashMap<String, Object>();
                String state = entry.getValue();
                if ("1".equals(state)) {
                    state = "closed";
                } else if ("2".equals(state)) {
                    state = "listen";
                } else if ("3".equals(state)) {
                    state = "synSent ";
                } else if ("4".equals(state)) {
                    state = "synReceived ";
                } else if ("5".equals(state)) {
                    state = "established ";
                } else if ("6".equals(state)) {
                    state = "finWait1 ";
                } else if ("7".equals(state)) {
                    state = "finWait2 ";
                } else if ("8".equals(state)) {
                    state = "closeWait ";
                } else if ("9".equals(state)) {
                    state = "lastAck ";
                } else if ("10".equals(state)) {
                    state = "closing ";
                } else if ("11".equals(state)) {
                    state = "timeWait ";
                } else if ("12".equals(state)) {
                    state = "deleteTCB ";
                } else {
                    state = "other";
                }
                //状态
                res.put("state", state);
                res.put("localAddress", localAddressMap.get("*******.********.1.2." + keyString));
                res.put("localPort", localPortMap.get("*******.********.1.3." + keyString));
                res.put("remAddress", remAddressMap.get("*******.********.1.4." + keyString));
                res.put("remPort", remPortMap.get("*******.********.1.5." + keyString));

                tcpInfoList.add(res);
            }
            return tcpInfoList;
        } catch (Exception e) {
            log.error("获取tcp信息出错", e);
            return null;
        }
    }

    private HashMap<String, Object> getTcpConnect(SNMPUtils snmpUtils) {
        HashMap<String, Object> map = new HashMap<>();
        try {
            String connect = snmpUtils.getPDU("*******.*******.0");
            String connectError = snmpUtils.getPDU("*******.********.0");
            if (StringUtils.isNotEmpty(connect) && StringUtils.isNotEmpty(connectError)) {
                map.put("connect", connect);
                map.put("connectError", connectError);
            } else {
                log.error("未获取到tcp连接数...");
                map.put("connect", "0");
                map.put("connectError", "0");
            }
            return map;
        } catch (Exception e) {
            log.error("获取tcp连接数出错！", e);
            map.put("connect", "0");
            map.put("connectError", "0");
            return map;
        }
    }

    /**
     * 获取端口详情
     *
     * @param snmpUtils
     * @return
     */
    private List<Map<String, Object>> getPortInfoList(SNMPUtils snmpUtils) {

        try {
            List<Map<String, Object>> portInfoList = new ArrayList<Map<String, Object>>();
            // 接口索引 主键
            Map<String, String> connNumMap = snmpUtils.getPDUWalk("*******.2.1.2.2.1.1");
            //端口状态
            Map<String, String> portStatusMap = snmpUtils.getPDUWalk("*******.2.1.2.2.1.8");
            //端口描述
            Map<String, String> ifDescrProtosMap = snmpUtils.getPDUWalk("*******.2.1.2.2.1.2");
            // 带宽
            Map<String, String> ifSpeedProtosMap = snmpUtils.getPDUWalk("*******.2.1.2.2.1.5");
            //端口类型
            Map<String, String> portTypeMap = snmpUtils.getPDUWalk("*******.2.1.2.2.1.3");
            //接收错误数据包
            Map<String, String> ifInErrorsMap = snmpUtils.getPDUWalk("*******.2.1.2.2.1.14");
            //发送的错误数据包
            Map<String, String> ifOutErrorsProtosMap = snmpUtils.getPDUWalk("*******.2.1.2.2.1.20");
            //输入流量
            Map<String, String> inSpeedMap = snmpUtils.getPDUWalk("*******.2.1.2.2.1.10");
            //输出流量
            Map<String, String> outSpeedMap = snmpUtils.getPDUWalk("*******.2.1.2.2.1.16");
            //输入丢失错误包数
            Map<String, String> inLossPackageMap = snmpUtils.getPDUWalk("*******.2.1.2.2.1.13");
            //输出丢失错误包数
            Map<String, String> outLossPackageMap = snmpUtils.getPDUWalk("*******.2.1.2.2.1.19");
            for (Map.Entry<String, String> entrys : connNumMap.entrySet()) {
                String keyString = entrys.getKey().replace("*******.2.1.2.2.1.1.", "");
                Map<String, Object> res = new HashMap<String, Object>();
                //端口类型
                res.put("portType", portTypeMap.get("*******.2.1.2.2.1.3." + keyString));
                //端口状态
                String str = portStatusMap.get("*******.2.1.2.2.1.8." + keyString);
                if ("1".equals(str.trim())) {
                    res.put("portStatus", "连接");
                } else if ("2".equals(str.trim())) {
                    res.put("portStatus", "关闭");
                } else {
                    res.put("portStatus", "其他");
                }
                // 带宽
                String bandWidth_string = ifSpeedProtosMap.get("*******.2.1.2.2.1.5." + keyString);
                if (StringUtils.isNotEmpty(bandWidth_string)) {
                    Double bandWidth = Double.parseDouble(bandWidth_string);
                    res.put("bandWidth", String.format("%.2f", bandWidth / 1024 / 1024 / 1024));
                }

                //输入/输出流量
                String inSpeed = inSpeedMap.get("*******.2.1.2.2.1.10." + keyString);
                if (StringUtils.isNotEmpty(inSpeed)) {
                    Double inputFlow = Double.parseDouble(inSpeed);
                    res.put("inputFlow", String.format("%.2f", inputFlow * 1024 / 1024 / 1024));
                }
                String outSpeed = outSpeedMap.get("*******.2.1.2.2.1.16." + keyString);
                if (StringUtils.isNotEmpty(outSpeed)) {
                    Double outputFlow = Double.parseDouble(outSpeed);
                    res.put("outputFlow", String.format("%.2f", outputFlow * 1024 / 1024 / 1024));
                }

                //输入/输出错误包数
                String inErrorPackageString = ifInErrorsMap.get("*******.2.1.2.2.1.14." + keyString);
                if (StringUtils.isNotEmpty(inErrorPackageString)) {
                    Double inError = Double.parseDouble(inErrorPackageString);
                    res.put("inErrorPackage", String.format("%.2f", inError));
                }
                String outErrorPackageString = ifOutErrorsProtosMap.get("*******.2.1.2.2.1.20." + keyString);
                if (StringUtils.isNotEmpty(outErrorPackageString)) {
                    Double inError = Double.parseDouble(outErrorPackageString);
                    res.put("outErrorPackage", String.format("%.2f", inError));
                }

                //输入/输出丢失错误包数
                String inLossPackageString = inLossPackageMap.get("*******.2.1.2.2.1.13." + keyString);
                if (StringUtils.isNotEmpty(inLossPackageString)) {
                    Double inLossPackage = Double.parseDouble(inLossPackageString);
                    res.put("inLossPackage", String.format("%.2f", inLossPackage));
                }
                String outLossPackageString = outLossPackageMap.get("*******.2.1.2.2.1.19." + keyString);
                if (StringUtils.isNotEmpty(outLossPackageString)) {
                    Double outLossPackage = Double.parseDouble(outLossPackageString);
                    res.put("outLossPackage", String.format("%.2f", outLossPackage));
                }
                // 接口描述
                res.put("portDesc", ifDescrProtosMap.get("*******.2.1.2.2.1.2." + keyString));
                //索引
                res.put("index", keyString);

                portInfoList.add(res);
            }
            return portInfoList;
        } catch (Exception e) {
            log.error("获取端口信息出错", e);
            return null;
        }
    }

    /**
     * 获取服务信息
     * 因服务信息是私有oid，官方没有公共mib,此方法为多方资料总结性查询结果。
     * 名字：*******.4.1.77.1.2.3.1.1
     * 服务状态：*******.4.1.77.1.2.3.1.3     1：正在运行  other
     * 启动类型：*******.4.1.77.1.2.3.1.5   1：自动启动   2：手动启动   other
     *
     * @param snmpUtils
     * @return
     */
    private List<Map<String, Object>> getServiceInfoList(SNMPUtils snmpUtils) {
        try {
            List<Map<String, Object>> tcpInfoList = new ArrayList<Map<String, Object>>();
            // 接口索引 名字
            Map<String, String> nameMap = snmpUtils.getPDUWalk("*******.4.1.77.1.2.3.1.1");
            //运行状态
            Map<String, String> statusMap = snmpUtils.getPDUWalk("*******.4.1.77.1.2.3.1.3");
            //启动类型自动手动
            Map<String, String> typeMap = snmpUtils.getPDUWalk("*******.4.1.77.1.2.3.1.5");

            for (Map.Entry<String, String> entry : nameMap.entrySet()) {
                String keyString = entry.getKey().replace("*******.4.1.77.1.2.3.1.1.", "");
                Map<String, Object> res = new HashMap<String, Object>();
                res.put("serviceName", entry.getValue());
                String status = statusMap.get("*******.4.1.77.1.2.3.1.3." + keyString);
                if ("1".equals(status)) {
                    status = "正在运行";
                } else {
                    status = "其它";
                }
                res.put("servicesStatus", status);
                String type = typeMap.get("*******.4.1.77.1.2.3.1.5." + keyString);
                if ("1".equals(type)) {
                    type = "自动";
                } else if ("2".equals(type)) {
                    type = "手动";
                } else {
                    type = "其它";
                }
                res.put("servicesType", type);
                tcpInfoList.add(res);
            }
            return tcpInfoList;
        } catch (Exception e) {
            log.error("获取tcp信息出错", e);
            return null;
        }
    }

    /**
     * 获取网卡bond信息
     *
     * @param snmpUtils
     * @return
     */
    private List<Map<String, Object>> getBondInfoList(SNMPUtils snmpUtils) {

        try {
            List<Map<String, Object>> bondInfoList = new ArrayList<Map<String, Object>>();
            // 网卡信息  名字
            Map<String, String> nameMap = snmpUtils.getPDUWalk("*******.2.1.31.1.1.1.1");
            //网络接口接收到的多播数据包的数量
            Map<String, String> inMultiMap = snmpUtils.getPDUWalk("*******.2.1.31.1.1.1.2");
            //网络接口接收到的广播数据包的数量
            Map<String, String> inBroadMap = snmpUtils.getPDUWalk("*******.2.1.31.1.1.1.3");
            //网络接口发送到的多播数据包的数量
            Map<String, String> outMultiMap = snmpUtils.getPDUWalk("*******.2.1.31.1.1.1.4");
            //网络接口发送到的广播数据包的数量
            Map<String, String> outBroadMap = snmpUtils.getPDUWalk("*******.2.1.31.1.1.1.5");
            //网络接口当前带宽的估计值b/s
            Map<String, String> speed = snmpUtils.getPDUWalk("*******.2.1.31.1.1.1.15");
            //网络接口是否处于混杂模式1：true；2false
            Map<String, String> isHybridMap = snmpUtils.getPDUWalk("*******.2.1.31.1.1.1.16");
            //网络接口子层是否具有物理连接器1：true；2false
            Map<String, String> isConnectMap = snmpUtils.getPDUWalk("*******.2.1.31.1.1.1.17");
            //网络接口的别名（alias）对象
            Map<String, String> aliasMap = snmpUtils.getPDUWalk("*******.2.1.31.1.1.1.18");

            for (Map.Entry<String, String> entrys : nameMap.entrySet()) {
                String keyString = entrys.getKey().replace("*******.2.1.31.1.1.1.1.", "");
                Map<String, Object> res = new HashMap<String, Object>();
                res.put("name", entrys.getValue());
                res.put("inMulti", inMultiMap.get("*******.2.1.31.1.1.1.2." + keyString));
                res.put("inBroad", inBroadMap.get("*******.2.1.31.1.1.1.3." + keyString));
                res.put("outMulti", outMultiMap.get("*******.2.1.31.1.1.1.4." + keyString));
                res.put("outBroad", outBroadMap.get("*******.2.1.31.1.1.1.5." + keyString));
                // 带宽
                res.put("speed", speed.get("*******.2.1.31.1.1.1.15." + keyString));
                String isHybrid = isHybridMap.get("*******.2.1.31.1.1.1.16." + keyString);
                if ("2".equals(isHybrid)) {
                    res.put("isHybrid", "否");
                } else if ("1".equals(isHybrid)) {
                    res.put("isHybrid", "是");
                }
                String isConnect = isConnectMap.get("*******.2.1.31.1.1.1.17." + keyString);
                if ("2".equals(isConnect)) {
                    res.put("isConnect", "否");
                } else if ("1".equals(isConnect)) {
                    res.put("isConnect", "是");
                }
                res.put("alias", aliasMap.get("*******.2.1.31.1.1.1.18." + keyString));
                bondInfoList.add(res);
            }
            return bondInfoList;
        } catch (Exception e) {
            log.error("获取网卡bond信息出错", e);
            return null;
        }
    }
}
