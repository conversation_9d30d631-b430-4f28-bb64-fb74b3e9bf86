package com.yuanqiao.insight.collection.collector.collector.impl;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.adapter.inter.DeviceCodec;
import com.yuanqiao.insight.acore.collector.Collector;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.acore.scheduler.SchedulerManagerInter;

public class InfluxdbCollector implements Collector, DeviceCodec {
    private Device device;
    private SchedulerManagerInter schedulerManager;

    @Override
    public void init(Device device, SchedulerManagerInter schedulerManager) {
        //绑定设备
        this.device = device;
        //绑定调度管理器
        this.schedulerManager = schedulerManager;
    }

    @Override
    public void execute() {
        //1. 获取指定influxdb数据库中的数据
        //2. 按照子设备分组, 循环处理设备
        //2.1. 把数据带入解码器
        //2.2. 调用<PERSON><PERSON>lerManager, 设置子设备状态: 离线, 在线
        //2.3. 调用<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 发布子设备数据事件
        //2.4. 调用She<PERSON><PERSON>Manager, 设置子状态容器


    }

    @Override
    public JSONObject decode(String prefix, String message, JSONObject metadataObject) {
        System.out.println("---------------null");
        return null;
    }
}
