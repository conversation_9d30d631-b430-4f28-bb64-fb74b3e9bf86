package com.yuanqiao.insight.collection.collector.collector.impl.Snmp.StorageSnmp;

import com.yuanqiao.insight.acore.collector.Collector;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.acore.scheduler.SchedulerManagerInter;
import com.yuanqiao.insight.acore.utils.SpringContextUtil;
import com.yuanqiao.insight.collection.collector.utils.SNMPCodecAndCollectUtils;
import com.yuanqiao.insight.common.util.snmp.SNMPUtils;
import com.yuanqiao.insight.utils.SNMPMetadataUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.scheduling.annotation.Async;

import static com.yuanqiao.insight.collection.config.SnmpConstant.RETRY;
import static com.yuanqiao.insight.collection.config.SnmpConstant.TIMEOUT;

/**
 * 华为存储
 * HWAWEI OceanStor9000   v5.7.0
 */
@Slf4j
public class StSnmpCollector_HW_OceanStor9000 implements Collector {

    private Device device;
    private StSnmpCodec_HW_OceanStor9000 StSnmpCodec;
    private SchedulerManagerInter schedulerManager;
    private SNMPUtils snmpUtils;
    private SNMPMetadataUtils metadataUtils;


    //初始化
    @Override
    public void init(Device device, SchedulerManagerInter schedulerManager) {
        //绑定设备
        this.device = device;
        //创建解码器
        this.StSnmpCodec = new StSnmpCodec_HW_OceanStor9000();
        //绑定调度管理器
        this.schedulerManager = schedulerManager;
        //初始化SNMPUtils
        if (StringUtils.isEmpty(device.getConnectParam().get("version"))) {
            //V1
            log.info("为 *- " + device.getKey() + " -* 设备监控任务初始化 SNMP - 【 V1 】 SNMPUtils工具类...");
            this.snmpUtils = new SNMPUtils(device.getConnectParam().get("ip"), Integer.parseInt(device.getConnectParam().get("port")));
        } else {
            if (device.getConnectParam().get("version").equalsIgnoreCase("V2")) {
                try {
                    //V2
                    log.info("为 *- " + device.getKey() + " -* 设备监控任务初始化 SNMP - 【 V2 】 SNMPUtils工具类...");
                    this.snmpUtils = new SNMPUtils(device.getConnectParam().get("ip"), Integer.parseInt(device.getConnectParam().get("port")), device.getConnectParam().get("community"), TIMEOUT, RETRY);
                } catch (Exception e) {
                    log.error("V2 snmpUtils初始化异常！", e);
                }
            }

            if (device.getConnectParam().get("version").equalsIgnoreCase("V3")) {
                try {
                    //V3
                    log.info("为 *- " + device.getKey() + " -* 设备监控任务初始化 SNMP - 【 V3 】 SNMPUtils工具类...");
                    //ip地址、端口号、snmp版本、超时时间、重试次数、用户名、安全级别、认证协议、认证密码、加密协议、加密密码
                    this.snmpUtils = new SNMPUtils(device.getConnectParam().get("ip"), Integer.parseInt(device.getConnectParam().get("port")), device.getConnectParam().get("version"), TIMEOUT, RETRY,
                            device.getConnectParam().get("username"), device.getConnectParam().get("snmpAuthLevel"), device.getConnectParam().get("sAuth"), device.getConnectParam().get("sAuth_passwd"), device.getConnectParam().get("spriv"), device.getConnectParam().get("spriv_passwd"));
                } catch (Exception e) {
                    log.error("V3 snmpUtils初始化异常！", snmpUtils, e);
                }
            }
        }

        //初始化metadataUtils
        this.metadataUtils = (SNMPMetadataUtils) SpringContextUtil.getBean("SNMPMetadataUtils");
    }

    //执行方法
    @Override
    @Async
    public void execute() {


        //调用 snmpCodecAndCollectUtils.collectorMainLine 执行拉模式采集器主线流程
        new SNMPCodecAndCollectUtils().collectorMainLine(metadataUtils, device, snmpUtils, schedulerManager, StSnmpCodec, new SNMPCodecAndCollectUtils());

    }

}
