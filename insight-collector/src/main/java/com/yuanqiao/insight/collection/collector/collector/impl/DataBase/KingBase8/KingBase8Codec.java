package com.yuanqiao.insight.collection.collector.collector.impl.DataBase.KingBase8;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.collection.collector.utils.DataBaseCodecInterface;
import com.yuanqiao.insight.collection.collector.utils.DataBaseCodecUtils;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;

@Slf4j
public class KingBase8Codec extends DataBaseCodecInterface {

    //封装全部数据的外层JSONObject
    JSONObject jsonObject = new JSONObject();
    //公共结果集
    JSONObject commonMap = new JSONObject();
    //解码工具
    DataBaseCodecUtils dataBaseCodecUtils = new DataBaseCodecUtils();

    @Override
    public JSONObject dataCodec(Connection connection, List<ProertyMetadata> metadataList, String stcaKey) {
        //清空jsonObject和commonMap
        jsonObject.clear();
        commonMap.clear();

        HashMap<Object, Object> staticInfo = new HashMap<>();
        try {
            //commonMap.put("upTime", DBKingbase8.getStartTime(con));
            //commonMap.put("runTime", DBKingbase8.getRunningTime(con));
            //commonMap.put("resultCacheInfo",DBKingbase8.getResultCache(con));
            //commonMap.put("log", DBKingbase8.getLogFileInfo(con));
            staticInfo.put("version", DBKingbase8.getVersion(connection));
            staticInfo.put("connNum", DBKingbase8.getConnectNumber(connection));
            commonMap.put("staticInfo", staticInfo);
            commonMap.put("cacheInfo", DBKingbase8.getCacheInfo(connection));
            commonMap.put("connInfo", DBKingbase8.getConnInfo(connection));
            commonMap.put("tbInfo", DBKingbase8.getTBInfo(connection));
        } catch (SQLException e) {
            log.error("KingBase8 数据库监控数据获取出现异常！", e);
        }

        //根据物模型解析数据库数据
        for (ProertyMetadata metadata : metadataList) {
            if (StringUtils.isNotEmpty(metadata.getDataType())) {
                jsonObject = dataBaseCodecUtils.switchByDataType(metadata, stcaKey, jsonObject, commonMap);
            }
        }
        return jsonObject;
    }
}