package com.yuanqiao.insight.collection.collector.collector.impl.Snmp.FirewallSnmp;


import com.yuanqiao.insight.acore.collector.Collector;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.acore.scheduler.SchedulerManagerInter;
import com.yuanqiao.insight.acore.utils.SpringContextUtil;
import com.yuanqiao.insight.collection.collector.utils.SNMPCodecAndCollectUtils;
import com.yuanqiao.insight.common.util.snmp.SNMPUtils;
import com.yuanqiao.insight.utils.SNMPMetadataUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.scheduling.annotation.Async;

import static com.yuanqiao.insight.collection.config.SnmpConstant.RETRY;
import static com.yuanqiao.insight.collection.config.SnmpConstant.TIMEOUT;

/**
 * 网御星云防火墙
 * PowerV6000-F3510E-FG1
 */
@Slf4j
public class FwCollector_PV6_F3510E implements Collector {
    private Device device;
    private FwCodec_PV6_F3510E firewallSnmpCodec;
    private SchedulerManagerInter schedulerManager;
    private SNMPUtils snmpUtils;
    private SNMPMetadataUtils metadataUtils;

    private SNMPCodecAndCollectUtils snmpCodecAndCollectUtils;


    //初始化
    public void init(Device device, SchedulerManagerInter schedulerManager) {
        //绑定设备
        this.device = device;
        //创建解码器
        this.firewallSnmpCodec = new FwCodec_PV6_F3510E();
        //初始化设备操作系统类型
        this.firewallSnmpCodec.setOsType(device.getConnectParam().get("osType"));
        //绑定调度管理器
        this.schedulerManager = schedulerManager;
        //初始化SNMPUtils
        if (StringUtils.isEmpty(device.getConnectParam().get("version"))) {
            //V1
            log.info("为 *- " + device.getKey() + " -* 设备监控任务初始化 SNMP - 【 V1 】 SNMPUtils工具类...");
            this.snmpUtils = new SNMPUtils(device.getConnectParam().get("ip"), Integer.parseInt(device.getConnectParam().get("port")));
        } else {
            if (device.getConnectParam().get("version").equalsIgnoreCase("V2")) {
                try {
                    //V2
                    log.info("为 *- " + device.getKey() + " -* 设备监控任务初始化 SNMP - 【 V2 】 SNMPUtils工具类...");
                    this.snmpUtils = new SNMPUtils(device.getConnectParam().get("ip"), Integer.parseInt(device.getConnectParam().get("port")), device.getConnectParam().get("community"), TIMEOUT, RETRY);
                } catch (Exception e) {
                    log.error("V2 snmpUtils初始化异常！", e);
                }
            }

            if (device.getConnectParam().get("version").equalsIgnoreCase("V3")) {
                try {
                    //V3
                    log.info("为 *- " + device.getKey() + " -* 设备监控任务初始化 SNMP - 【 V3 】 SNMPUtils工具类...");
                    //ip地址、端口号、snmp版本、超时时间、重试次数、用户名、安全级别、认证协议、认证密码、加密协议、加密密码
                    this.snmpUtils = new SNMPUtils(device.getConnectParam().get("ip"), Integer.parseInt(device.getConnectParam().get("port")), device.getConnectParam().get("version"), TIMEOUT, RETRY,
                            device.getConnectParam().get("username"), device.getConnectParam().get("snmpAuthLevel"), device.getConnectParam().get("sAuth"), device.getConnectParam().get("sAuth_passwd"), device.getConnectParam().get("spriv"), device.getConnectParam().get("spriv_passwd"));
                    log.info(" *** 为 *- " + device.getKey() + " -* 设备监控任务初始化的【* SNMPUtils连通性 *】：" + this.snmpUtils.isConnectble());
                } catch (Exception e) {
                    log.error("V3 snmpUtils初始化异常！", snmpUtils, e);
                }
            }
        }


        //初始化metadataUtils
        this.metadataUtils = (SNMPMetadataUtils) SpringContextUtil.getBean("SNMPMetadataUtils");


        //初始化snmpCodecAndCollectUtils
        //this.snmpCodecAndCollectUtils = (SNMPCodecAndCollectUtils) SpringContextUtil.getBean("snmpCodecAndCollectUtils");

    }

    //执行方法
    @Async
    public void execute() {


        //调用 snmpCodecAndCollectUtils.collectorMainLine 执行拉模式采集器主线流程
        new SNMPCodecAndCollectUtils().collectorMainLine(metadataUtils, device, snmpUtils, schedulerManager, firewallSnmpCodec, new SNMPCodecAndCollectUtils());

    }


}
