package com.yuanqiao.insight.collection.collector.collector.impl.Awcloud;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.common.util.cloud.ZStackRestApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 云平台数据解析组装
 */
@Slf4j
public class ZStackCloudCodec {

    public JSONObject dataCodecObject(String token, Device device) {
        JSONObject commonMap = new JSONObject();
        String ip = device.getConnectParam().get("ip");
        String port = device.getConnectParam().get("port");
        log.error("zstack云平台token==========：" + token);
        if (StringUtils.isNotEmpty(token)) {
            Map<String, Double> map = getDataCenterInfo(ip, port, token);
            commonMap.put("cpuRate", map.get("cpuRate"));
            commonMap.put("memRate", map.get("memRate"));
            commonMap.put("memTotal", map.get("memTotal"));
            commonMap.put("memUsed", map.get("memUsed"));
            commonMap.put("vpsInfo", getVpsInfoList(ip, port, token));
            commonMap.put("volumeInfo", getVolumeInfoList(ip, port, token));
            commonMap.put("zoneInfo", getZoneInfoList(ip, port, token));
            commonMap.put("clusterInfo", getClusterInfoList(ip, port, token));
            commonMap.put("hostInfo", getHostInfoList(ip, port, token));
//            log.error("zstack云主机commonMap========" + JSONObject.toJSONString(commonMap));
        }
        return commonMap;
    }

    /**
     * ZStack云平台数据中心概况
     * cpu，内存，disk
     *
     * @return
     */
    private HashMap<String, Double> getDataCenterInfo(String ip, String port, String token) {
        HashMap<String, Double> map = new HashMap<>();
        double memTotal = 0.0;
        double memFree = 0.0;
        double mem = 0.0;
        double cpu = 0.0;
        String url = "http://" + ip + ":" + port + "/zstack/v1/hosts/capacities/cpu-memory?all=true";
        log.error("zstack云平台数据中心概况URL：" + url);
        try {
            JSONObject jsonObject = ZStackRestApi.getDataCenterInfo(ip, port, token);
            if (jsonObject != null) {
                //cpu核数
                String cpus = jsonObject.getString("totalCpu");
                String cpus_free = jsonObject.getString("availableCpu");
                if (StringUtils.isNotEmpty(cpus) && StringUtils.isNotEmpty(cpus_free)) {
                    cpu = 100 * (Double.parseDouble(cpus) - Double.parseDouble(cpus_free)) / Double.parseDouble(cpus);
                }
                //内存
                String memory_mb = jsonObject.getString("totalMemory");
                String memory_mb_free = jsonObject.getString("availableMemory");
                if (StringUtils.isNotEmpty(memory_mb) && StringUtils.isNotEmpty(memory_mb_free)) {
                    memTotal = Double.parseDouble(memory_mb);
                    memFree = Double.parseDouble(memory_mb_free);
                    mem = 100 * (memTotal - memFree) / memTotal;
                }
                map.put("cpuRate", Double.valueOf(String.format("%.2f", cpu)));
                map.put("memRate", Double.valueOf(String.format("%.2f", mem)));
                map.put("memTotal", Double.valueOf(String.format("%.2f", memTotal)));
                map.put("memUsed", Double.valueOf(String.format("%.2f", memTotal - memFree)));
            }
        } catch (Exception e) {
            map.put("cpuRate", cpu);
            map.put("memRate", mem);
            map.put("memTotal", memTotal);
            map.put("memFree", memFree);
            log.error("zstack平台数据中心概况异常" + e);
        }
        return map;
    }

    /**
     * 云主机列表
     *
     * @return
     */
    private List<Map<String, Object>> getVpsInfoList(String ip, String port, String token) {
        List<Map<String, Object>> vpsInfoList = new ArrayList<Map<String, Object>>();
        String url = "http://" + ip + ":" + port + "/zstack/v1/vm-instances";
        log.error("ZStack云平台云主机列表URL：" + url);
        JSONObject data = ZStackRestApi.getServerInfo(ip, port, token);
        if (data != null) {
            JSONArray jsonArray = (JSONArray) data.get("inventories");
            for (int i = 0; i < jsonArray.size(); i++) {
                Map<String, Object> vps = new HashMap<String, Object>();
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String uId = jsonObject.getString("uuid");
                vps.put("uId", uId);
                //名字
                String uname = jsonObject.getString("name");
                vps.put("uname", uname);
                //状态
                String status = jsonObject.getString("state");
                if (status.equalsIgnoreCase("Running")) {
                    status = "运行";
                } else if (status.equalsIgnoreCase("Stopped")) {
                    status = "关机";
                } else {
                    status = "其它";
                }
                vps.put("status", status);
                //操作系统
                String platform = jsonObject.getString("platform");
                vps.put("platform", platform);
                //架构
                String architecture = jsonObject.getString("architecture");
                vps.put("architecture", architecture);
                //ip
                String vmIp = jsonObject.getJSONArray("vmNics").getJSONObject(0).getString("ip");
                vps.put("ip", vmIp);
                String mac = jsonObject.getJSONArray("vmNics").getJSONObject(0).getString("mac");
                vps.put("mac", mac);
                double cpuRate = 0.0;
                double memRate = 0.0;
                double diskRate = 0.0;
                double diskRead = 0.0;
                double diskWrite = 0.0;
                double netIn = 0.0;
                double netOut = 0.0;
                //cpu使用率
                String cpu = getDataDetailInfo(ip, port, token, uId, "ZStack/VM", "CPUUsedUtilization");
//                log.error("zstack云平台设备云主机列表cpu为：" + cpu);
                if (StringUtils.isNotEmpty(cpu)) {
                    cpuRate = Double.parseDouble(cpu);
                }
                vps.put("cpuRate", String.format("%.2f", cpuRate));
                //内存使用率
                String memFreeB = getDataDetailInfo(ip, port, token, uId, "ZStack/VM", "MemoryFreeBytes");
                String memUsedB = getDataDetailInfo(ip, port, token, uId, "ZStack/VM", "MemoryUsedBytes");
//                log.error("zstack云平台设备云主机列表memFreeB为：" + memFreeB);
//                log.error("zstack云平台设备云主机列表memUsedB为：" + memUsedB);
                if (StringUtils.isNotEmpty(memFreeB) && StringUtils.isNotEmpty(memUsedB)) {
                    double memFree = Double.parseDouble(memFreeB);
                    double memUsed = Double.parseDouble(memUsedB);
                    memRate = 100 * memUsed / (memUsed + memFree);
                }
                vps.put("memRate", String.format("%.2f", memRate));
                //磁盘使用率
                String diskUse = getDataDetailInfo(ip, port, token, uId, "ZStack/VM", "DiskUsedCapacityInPercent");
//                log.error("zstack云平台设备云主机列表磁盘使用率为：" + diskUse);
                if (StringUtils.isNotEmpty(diskUse)) {
                    diskRate = Double.parseDouble(diskUse);
                }
                vps.put("diskRate", String.format("%.2f", diskRate));
                //磁盘读速率B/s
                String DiskRead = getDataDetailInfo(ip, port, token, uId, "ZStack/VM", "DiskReadBytes");
//                log.error("zstack云平台设备云主机列表diskRead为：" + DiskRead);
                if (StringUtils.isNotEmpty(DiskRead)) {
                    diskRead = Double.parseDouble(DiskRead);
                }
                vps.put("diskRead", diskRead);
                //磁盘写速率B/s
                String DiskWrite = getDataDetailInfo(ip, port, token, uId, "ZStack/VM", "DiskWriteBytes");
//                log.error("zstack云平台设备云主机列表DiskWrite为：" + DiskWrite);
                if (StringUtils.isNotEmpty(DiskWrite)) {
                    diskWrite = Double.parseDouble(DiskWrite);
                }
                vps.put("diskWrite", diskWrite);
                //网络入速率B/s
                String NetworkIn = getDataDetailInfo(ip, port, token, uId, "ZStack/VM", "NetworkInBytes");
//                log.error("zstack云平台设备云主机列表NetworkIn为：" + NetworkIn);
                if (StringUtils.isNotEmpty(NetworkIn)) {
                    netIn = Double.parseDouble(NetworkIn);
                }
                vps.put("netIn", netIn);
                //网络出速率B/s
                String NetworkOut = getDataDetailInfo(ip, port, token, uId, "ZStack/VM", "NetworkOutBytes");
//                log.error("zstack云平台设备云主机列表NetworkOut为：" + NetworkOut);
                if (StringUtils.isNotEmpty(NetworkOut)) {
                    netOut = Double.parseDouble(NetworkOut);
                }
                vps.put("netOut", netOut);

                vpsInfoList.add(vps);
            }
        }
        return vpsInfoList;
    }

    /**
     * 云盘接口
     *
     * @return
     */
    private List<Map<String, Object>> getVolumeInfoList(String ip, String port, String token) {
        List<Map<String, Object>> volumeInfoList = new ArrayList<Map<String, Object>>();
        String url = "http://" + ip + ":" + port + "/zstack/v1/volumes";
        log.error("ZStack云平台云盘接口URL：" + url);
        JSONObject data = ZStackRestApi.getVolumeInfo(ip, port, token);
        if (data != null) {
            JSONArray jsonArray = (JSONArray) data.get("inventories");
            for (int i = 0; i < jsonArray.size(); i++) {
                Map<String, Object> vps = new HashMap<String, Object>();
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String uId = jsonObject.getString("uuid");
                vps.put("uId", uId);
                //名字
                String name = jsonObject.getString("name");
                vps.put("name", name);
                //描述
                String description = jsonObject.getString("description");
                vps.put("description", description);
                //云盘路径
                String installPath = jsonObject.getString("installPath");
                vps.put("installPath", installPath);
                //云盘类型
                String type = jsonObject.getString("type");
                vps.put("type", type);
                //云盘格式
                String format = jsonObject.getString("format");
                vps.put("format", format);
                //云盘大小
                String size = jsonObject.getString("size");
                vps.put("size", size);
                //云盘真实容量
                String actualSize = jsonObject.getString("actualSize");
                vps.put("actualSize", actualSize);
                //云盘是否开启
                String state = jsonObject.getString("state");
                vps.put("state", state);
                //云盘状态
                String status = jsonObject.getString("status");
                vps.put("status", status);
                volumeInfoList.add(vps);
            }
        }
        return volumeInfoList;
    }

    /**
     * 镜像接口（未测通）
     *
     * @return
     */
    private List<Map<String, Object>> getImagesInfoList(String ip, String port, String token) {
        List<Map<String, Object>> imagesInfoList = new ArrayList<Map<String, Object>>();
        String url = "http://" + ip + ":" + port + "/zstack/v1/images";
        log.error("ZStack云平台镜像接口URL：" + url);
        JSONObject data = ZStackRestApi.getImagesInfo(ip, port, token);
        if (data != null) {
            JSONArray jsonArray = (JSONArray) data.get("inventories");
            for (int i = 0; i < jsonArray.size(); i++) {
                Map<String, Object> vps = new HashMap<String, Object>();
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String uId = jsonObject.getString("uuid");
                vps.put("uId", uId);
                //名字
                String name = jsonObject.getString("name");
                vps.put("name", name);
                //镜像架构
                String platform = jsonObject.getString("platform");
                vps.put("platform", platform);
                //镜像路径
                String installPath = jsonObject.getString("url");
                vps.put("installPath", installPath);
                //镜像类型
                String type = jsonObject.getString("type");
                vps.put("type", type);
                //镜像格式
                String format = jsonObject.getString("format");
                vps.put("format", format);
                //镜像大小
                String size = jsonObject.getString("size");
                vps.put("size", size);
                //镜像真实容量
                String actualSize = jsonObject.getString("actualSize");
                vps.put("actualSize", actualSize);
                //镜像是否开启
                String state = jsonObject.getString("state");
                vps.put("state", state);
                //镜像状态
                String status = jsonObject.getString("status");
                vps.put("status", status);
                imagesInfoList.add(vps);
            }
        }
        return imagesInfoList;
    }

    /**
     * 区域接口
     *
     * @return
     */
    private List<Map<String, Object>> getZoneInfoList(String ip, String port, String token) {
        List<Map<String, Object>> zoneInfoList = new ArrayList<Map<String, Object>>();
        String url = "http://" + ip + ":" + port + "/zstack/v1/zones";
        log.error("ZStack云平台区域接口URL：" + url);
        JSONObject data = ZStackRestApi.getZoneInfo(ip, port, token);
        if (data != null) {
            JSONArray jsonArray = (JSONArray) data.get("inventories");
            for (int i = 0; i < jsonArray.size(); i++) {
                Map<String, Object> vps = new HashMap<String, Object>();
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String uId = jsonObject.getString("uuid");
                vps.put("uId", uId);
                //名字
                String name = jsonObject.getString("name");
                vps.put("name", name);
                //区域描述
                String description = jsonObject.getString("description");
                vps.put("description", description);
                //区域类型
                String type = jsonObject.getString("type");
                vps.put("type", type);
                //区域是否开启
                String state = jsonObject.getString("state");
                vps.put("state", state);
                zoneInfoList.add(vps);
            }
        }
        return zoneInfoList;
    }

    /**
     * 集群接口
     *
     * @return
     */
    private List<Map<String, Object>> getClusterInfoList(String ip, String port, String token) {
        List<Map<String, Object>> clusterInfoList = new ArrayList<Map<String, Object>>();
        String url = "http://" + ip + ":" + port + "/zstack/v1/clusters";
        log.error("ZStack云平台集群接口URL：" + url);
        JSONObject data = ZStackRestApi.getClusterInfo(ip, port, token);
        if (data != null) {
            JSONArray jsonArray = (JSONArray) data.get("inventories");
            for (int i = 0; i < jsonArray.size(); i++) {
                Map<String, Object> vps = new HashMap<String, Object>();
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String uId = jsonObject.getString("uuid");
                vps.put("uId", uId);
                //名字
                String name = jsonObject.getString("name");
                vps.put("name", name);
                //集群描述
                String description = jsonObject.getString("description");
                vps.put("description", description);
                //集群类型
                String type = jsonObject.getString("type");
                vps.put("type", type);
                //集群是否开启
                String state = jsonObject.getString("state");
                vps.put("state", state);
                //管理程序类型
                String hypervisorType = jsonObject.getString("hypervisorType");
                vps.put("hypervisorType", hypervisorType);
                //区域UUID
                String zoneUuid = jsonObject.getString("zoneUuid");
                vps.put("zoneUuid", zoneUuid);
                //架构
                String architecture = jsonObject.getString("architecture");
                vps.put("architecture", architecture);
                clusterInfoList.add(vps);
            }
        }
        return clusterInfoList;
    }

    /**
     * 物理机接口
     *
     * @return
     */
    private List<Map<String, Object>> getHostInfoList(String ip, String port, String token) {
        List<Map<String, Object>> clusterInfoList = new ArrayList<Map<String, Object>>();
        String url = "http://" + ip + ":" + port + "/zstack/v1/hosts";
        log.error("ZStack云平台物理机接口URL：" + url);
        JSONObject data = ZStackRestApi.getHostInfo(ip, port, token);
        if (data != null) {
            JSONArray jsonArray = (JSONArray) data.get("inventories");
            for (int i = 0; i < jsonArray.size(); i++) {
                Map<String, Object> vps = new HashMap<String, Object>();
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String uId = jsonObject.getString("uuid");
                vps.put("uId", uId);
                //用户名
                String username = jsonObject.getString("username");
                vps.put("username", username);
                //ssh端口
                String sshPort = jsonObject.getString("sshPort");
                vps.put("sshPort", sshPort);
                //名字
                String name = jsonObject.getString("name");
                vps.put("name", name);
                //物理机描述
                String description = jsonObject.getString("description");
                vps.put("description", description);
                //物理机是否开启
                String state = jsonObject.getString("state");
                vps.put("state", state);
                //物理机状态
                String status = jsonObject.getString("status");
                vps.put("status", status);
                //管理程序类型
                String hypervisorType = jsonObject.getString("hypervisorType");
                vps.put("hypervisorType", hypervisorType);
                //管理ip
                String managementIp = jsonObject.getString("managementIp");
                vps.put("managementIp", managementIp);
                //区域UUID
                String zoneUuid = jsonObject.getString("zoneUuid");
                vps.put("zoneUuid", zoneUuid);
                //集群UUID
                String clusterUuid = jsonObject.getString("clusterUuid");
                vps.put("clusterUuid", clusterUuid);
                //架构
                String architecture = jsonObject.getString("architecture");
                vps.put("architecture", architecture);
                //cpu数量
                String cpuNum = jsonObject.getString("cpuNum");
                vps.put("cpuNum", cpuNum);
                //总cpu容量
                String totalCpuCapacity = jsonObject.getString("totalCpuCapacity");
                vps.put("totalCpuCapacity", totalCpuCapacity);
                //可用cpu容量
                String availableCpuCapacity = jsonObject.getString("availableCpuCapacity");
                vps.put("availableCpuCapacity", availableCpuCapacity);
                //总内存容量
                String totalMemoryCapacity = jsonObject.getString("totalMemoryCapacity");
                vps.put("totalMemoryCapacity", totalMemoryCapacity);
                //可用内存容量
                String availableMemoryCapacity = jsonObject.getString("availableMemoryCapacity");
                vps.put("availableMemoryCapacity", availableMemoryCapacity);
                clusterInfoList.add(vps);
            }
        }
        return clusterInfoList;
    }

    /**
     * 根据监控参数获取监控数据
     *
     * @param ip
     * @param port
     * @param token
     * @param uuid
     * @param namespace
     * @param metricName
     * @return
     */
    private String getDataDetailInfo(String ip, String port, String token, String uuid, String
            namespace, String metricName) {
        String value = "";
        try {
            JSONObject data = ZStackRestApi.getSeverDataInfo(ip, port, token, uuid, namespace, metricName);
            if (data != null) {
                if (!data.getJSONArray("data").isEmpty())
                    value = data.getJSONArray("data").getJSONObject(0).getString("value");
            }
        } catch (Exception e) {
            log.error("zstack平台根据监控参数" + metricName + "获取监控数据异常" + e);
        }
        return value;
    }
}
