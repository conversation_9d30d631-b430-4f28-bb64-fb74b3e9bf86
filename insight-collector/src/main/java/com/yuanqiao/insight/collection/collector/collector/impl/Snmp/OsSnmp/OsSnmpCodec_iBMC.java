package com.yuanqiao.insight.collection.collector.collector.impl.Snmp.OsSnmp;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.collection.collector.utils.SNMPCodecAndCollectUtils;
import com.yuanqiao.insight.collection.collector.utils.SNMPCodecInterface;
import com.yuanqiao.insight.common.util.common.TimeUtils;
import com.yuanqiao.insight.common.util.snmp.SNMPUtils;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigInteger;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class OsSnmpCodec_iBMC implements SNMPCodecInterface {

    // 操作系统类型
    String osType;

    public void setOsType(String osType) {
        this.osType = osType;
    }

    //最外层jsonObject
    JSONObject jsonObject = new JSONObject();
    //公共结果集
    JSONObject commonMap = new JSONObject();

    @Override
    public JSONObject dataCodec(SNMPUtils snmpUtils, List<ProertyMetadata> metadataList, String stcaKey, Device device, SNMPCodecAndCollectUtils snmpCodecAndCollectUtils) {
        //清空外层jsonObject和公共结果集commonMap
        jsonObject.clear();
        commonMap.clear();

        //静态属性
        HashMap<String, Object> staticInfoMap = new HashMap<>();

        staticInfoMap.put("switchName", getName(snmpUtils).get("switchName"));
        staticInfoMap.put("switchDesc", getSysDesc(snmpUtils).get("switchDesc"));
        staticInfoMap.put("portNum", getPortNum(snmpUtils).get("portNum"));
        staticInfoMap.put("runTime", getRunTime(snmpUtils).get("runTime"));
        commonMap.put("staticInfo", staticInfoMap);
        commonMap.put("sysUpTime", getSysUpTime(snmpUtils).get("sysUpTime"));
        commonMap.put("cpuRate", getCpuRate(snmpUtils).get("cpuRate"));
        commonMap.put("memRate", getMemRate(snmpUtils).get("memRate"));
        commonMap.put("netInOut", getNetInAndOut(snmpUtils).get("netInOut"));
        //电源信息
        commonMap.put("powerInfo", Objects.requireNonNull(powerInfoList(snmpUtils, "1.3.6.1.4.1.2011.2.235.1.1.6.50")).get("powerInfo"));
        //风扇信息
        commonMap.put("fanInfo", Objects.requireNonNull(fanInfoList(snmpUtils, "1.3.6.1.4.1.2011.2.235.1.1.8.50")).get("fanInfo"));
        //磁盘阵列信息
        commonMap.put("diskInfo", Objects.requireNonNull(diskInfoList(snmpUtils, ".1.3.6.1.4.1.2011.2.235.1.1.39.50")).get("diskInfo"));

        metadataList.forEach(item -> {
            if (StringUtils.isNotEmpty(item.getDataType())) {
                jsonObject = snmpCodecAndCollectUtils.switchByDataType(item, stcaKey, jsonObject, commonMap, snmpUtils);
            }
        });

        return jsonObject;
    }

    /**
     * 获取设备运行时长
     *
     * @param snmpUtils
     * @return
     */
    private Map<String, String> getSysUpTime(SNMPUtils snmpUtils) {
        HashMap<String, String> map = new HashMap<>();
        try {
            String sysUpTime = snmpUtils.getPDU("1.3.6.1.2.1.1.3.0");
            if (StringUtils.isNotEmpty(sysUpTime) && !sysUpTime.equalsIgnoreCase("noSuchObject")
                    && !sysUpTime.equalsIgnoreCase("noSuchInstance")) {
                // 去除字符串中的逗号
                sysUpTime = sysUpTime.replace(",", "");

                // 使用正则表达式匹配时间信息
                Pattern pattern = Pattern.compile("(?:(\\d+) days?)?(?:\\s*,?\\s*(\\d+):(\\d+)(?::(\\d+(?:\\.\\d+)?))?)?");
                Matcher matcher = pattern.matcher(sysUpTime);

                int days = 0, hours = 0, minutes = 0, seconds = 0;
                double milliseconds = 0;

                if (matcher.find()) {
                    if (matcher.group(1) != null) {
                        days = Integer.parseInt(matcher.group(1));
                    }
                    if (matcher.group(2) != null) {
                        hours = Integer.parseInt(matcher.group(2));
                    }
                    if (matcher.group(3) != null) {
                        minutes = Integer.parseInt(matcher.group(3));
                    }
                    if (matcher.group(4) != null) {
                        String[] secondsParts = matcher.group(4).split("\\.");
                        seconds = Integer.parseInt(secondsParts[0]);
                        if (secondsParts.length > 1) {
                            milliseconds = Double.parseDouble("0." + secondsParts[1]);
                        }
                    }
                }
                // 计算总毫秒数
                BigInteger totalMilliseconds = BigInteger.valueOf(days * 24 * 60 * 60 * 1000L)
                        .add(BigInteger.valueOf(hours * 60 * 60 * 1000L))
                        .add(BigInteger.valueOf(minutes * 60 * 1000L))
                        .add(BigInteger.valueOf((long) (seconds * 1000 + milliseconds * 1000)));
                sysUpTime = TimeUtils.getDistanceTime(totalMilliseconds);
                map.put("sysUpTime", sysUpTime);
            }
        } catch (Exception e) {
            log.error("获取运行时长出错", e);
            map.put("sysUpTime", "");
        }
        return map;
    }

    /* ---------------------------------------------------------------------------------------------------------------- */


    //获取服务器端口数
    private Map<String, Integer> getPortNum(SNMPUtils snmpUtils) {
        try {
            HashMap<String, Integer> map = new HashMap<>();
            // 服务器端口数
            String portNum_String = snmpUtils.getPDU("1.3.6.1.2.1.2.1.0");
            if (StringUtils.isEmpty(portNum_String)) {
                log.error("未获取到服务器端口数...");
                map.put("portNum", 0);
            } else {
                Integer portNum = Integer.parseInt(portNum_String);
                map.put("portNum", portNum);
            }
            return map;
        } catch (Exception e) {
            log.error("获取服务器端口数出错", e);
            HashMap<String, Integer> map = new HashMap<>();
            map.put("portNum", 0);
            return map;
        }
    }

    //获取服务器名称
    private Map<String, String> getName(SNMPUtils snmpUtils) {
        try {
            HashMap<String, String> map = new HashMap<>();
            // 服务器名称
            String ifName = snmpUtils.getPDU("1.3.6.1.2.1.1.5.0");
            if (ifName == null || "".equals(ifName.trim()) || "null".equalsIgnoreCase(ifName.trim())) {
                log.error("未获取到服务器名称...");
                map.put("switchName", "- -");
            } else {
                map.put("switchName", ifName);
            }
            return map;
        } catch (Exception e) {
            log.error("获取服务器名称出错", e);
            HashMap<String, String> map = new HashMap<>();
            map.put("switchName", "- -");
            return map;
        }
    }

    //获取服务器描述
    private Map<String, String> getSysDesc(SNMPUtils snmpUtils) {
        try {
            HashMap<String, String> map = new HashMap<>();
            // 服务器描述
            String sysDesc = snmpUtils.getPDU("1.3.6.1.2.1.1.1.0");
            if (sysDesc == null || "".equals(sysDesc.trim()) || "null".equalsIgnoreCase(sysDesc.trim())) {
                log.error("未获取到服务器描述...");
                map.put("switchDesc", "- -");
            }
            if (sysDesc.contains("S5720-36C-EI-AC")) {
                sysDesc = "S5720-36C-EI-AC Huawei";
            }
            map.put("switchDesc", sysDesc);
            return map;
        } catch (Exception e) {
            log.error("获取服务器描述出错", e);
            HashMap<String, String> map = new HashMap<>();
            map.put("switchDesc", "- -");
            return map;
        }
    }

    //获取服务器运行时间
    private Map<String, String> getRunTime(SNMPUtils snmpUtils) {
        try {
            HashMap<String, String> map = new HashMap<>();
            // 服务器运行时间
            String sysRunTime = snmpUtils.getPDU("1.3.6.1.2.1.1.3.0");
            if (sysRunTime == null || "".equals(sysRunTime.trim()) || "null".equalsIgnoreCase(sysRunTime.trim())) {
                log.error("未获取到服务器运行时间...");
                map.put("runTime", "--");
            }
//            if (!"".equals(sysRunTime) && sysRunTime != null && !sysRunTime.isEmpty()) {
//                String[] categoryArr = sysRunTime.split(":");
//                String dayRep = categoryArr[0].replace("days", "天");
//                String seconds = categoryArr[2].substring(0, 2);
//                sysRunTime = dayRep + ":" + categoryArr[1] + ":" + seconds;
//            }
            map.put("runTime", sysRunTime);
            return map;
        } catch (Exception e) {
            log.error("获取服务器运行时间出错", e);
            HashMap<String, String> map = new HashMap<>();
            map.put("runTime", "--");
            return map;
        }
    }

    //获取服务器cpu使用率
    private Map<String, Double> getCpuRate(SNMPUtils snmpUtils) {
        HashMap<String, Double> resMap = new HashMap<>();
        try {
            //初始化cpu平均使用率
            double cpuUtilization = 0.0;
            ArrayList<String> keyList = new ArrayList<>();
            Map<String, String> cpuUtilizationMap = snmpUtils.getPDUWalk("1.3.6.1.4.1.2011.2.235.1.1.1.23");
            if (cpuUtilizationMap != null && cpuUtilizationMap.size() > 0) {
                for (Map.Entry<String, String> entry : cpuUtilizationMap.entrySet()) {
                    if (!entry.getValue().equals("0")) {
                        cpuUtilization += Long.parseLong(entry.getValue());
                    } else {
                        keyList.add(entry.getKey());
                    }
                }
                for (String key : keyList) {
                    cpuUtilizationMap.remove(key);
                }

                cpuUtilization /= cpuUtilizationMap.size();
            }
            if (cpuUtilization == 0) {
                log.error("未获取到服务器cpu使用率或当前CPU使用率为0！");
                resMap.put("cpuRate", 0.0);
                return resMap;
            } else {
                //cpuUtilization = cpuUtilization * 100;
                resMap.put("cpuRate", Double.valueOf(String.format("%.2f", cpuUtilization)));
                return resMap;
            }
        } catch (Exception e) {
            log.error("获取IBMC---cpu平均使用率出错", e);
            resMap.put("cpuRate", 0.0);
            return resMap;
        }
    }

    //获取内存使用率
    private Map<String, Double> getMemRate(SNMPUtils snmpUtils) {
        HashMap<String, Double> resMap = new HashMap<>();
        try {
            //初始化内存平均使用率
            double memUtilization = 0.0;
            Map<String, String> memUtilizationMap = snmpUtils.getPDUWalk("1.3.6.1.4.1.2011.2.235.1.1.1.25");
            if (memUtilizationMap != null && memUtilizationMap.size() > 0) {
                for (Map.Entry<String, String> entry : memUtilizationMap.entrySet()) {
                    memUtilization += Long.parseLong(entry.getValue());
                }
                memUtilization /= memUtilizationMap.size();
            }
            if (memUtilization == 0) {
                log.error("未获取到服务器内存使用率!");
                resMap.put("memRate", 0.0);
                return resMap;
            } else {
                resMap.put("memRate", Double.valueOf(String.format("%.2f", memUtilization)));
                return resMap;
            }
        } catch (Exception e) {
            log.error("获取IBMC内存平均使用率出错", e);
            resMap.put("memRate", 0.0);
            return resMap;
        }
    }

    //获取网络吞吐量
    private Map<String, Double> getNetInAndOut(SNMPUtils snmpUtils) {
        try {
            HashMap<String, Double> map = new HashMap<>();
            //KeyMap
            Map<String, String> KeyMap = snmpUtils.getPDUWalk("1.3.6.1.2.1.2.2.1.1");
            int[] is = new int[KeyMap.size()];
            int i = 0;
            for (Map.Entry<String, String> en : KeyMap.entrySet()) {
                is[i] = Integer.parseInt(en.getValue());
                i++;
            }
            Arrays.sort(is);

            // 吞吐量
            double inAndOut = 0.0;
            int isLength = is.length;
            for (int j = 0; j < isLength; j++) {
                // 输入和输出字节总数
                String inSpeed_String = snmpUtils.getPDU("1.3.6.1.2.1.2.2.1.10." + is[j]);
                String OutSpeed_String = snmpUtils.getPDU("1.3.6.1.2.1.2.2.1.16." + is[j]);
                double inAndOutSpeed = 0;

                if (inSpeed_String != null && inSpeed_String != "" && OutSpeed_String != null && OutSpeed_String != "") {
                    inAndOutSpeed = Double.parseDouble(inSpeed_String) + Double.parseDouble(OutSpeed_String);
                }
                inAndOut += inAndOutSpeed;
            }
            if (Double.isNaN(inAndOut)) {
                log.error("未获取到服务器网络吞吐量...");
                map.put("netInOut", 0.0);
            } else {
                inAndOut = inAndOut / 1024 / 1024 / 1024;
                map.put("netInOut", Double.parseDouble(String.format("%.2f", inAndOut)));
            }
            return map;
        } catch (Exception e) {
            log.error("获取服务器网络吞吐量出错", e);
            HashMap<String, Double> map = new HashMap<>();
            map.put("netInOut", 0.0);
            return map;
        }
    }

    private Map<String, Object> powerInfoList(SNMPUtils snmpUtils, String tableTopOid) {
        HashMap<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> powerInfoList = new ArrayList<Map<String, Object>>();
        try {
            JSONObject tableData = snmpUtils.getPduTableView(tableTopOid);
            if (tableData != null && !tableData.isEmpty()) {
                JSONArray dataArray = tableData.getJSONArray("dataList");
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject eleObject = dataArray.getJSONObject(i);
                    Map<String, Object> res = new HashMap<>();

                    //索引
                    res.put("powerIndex", eleObject.getString("1.3.6.1.4.1.2011.2.235.1.1.6.50.1.1"));
                    //制造商
                    res.put("powerManufacture", eleObject.getString("1.3.6.1.4.1.2011.2.235.1.1.6.50.1.2"));
                    //电源模式
                    res.put("powerInputMode", eleObject.getString("1.3.6.1.4.1.2011.2.235.1.1.6.50.1.3"));
                    //电源型号
                    res.put("powerMode", eleObject.getString("1.3.6.1.4.1.2011.2.235.1.1.6.50.1.4"));
                    //固件版本号
                    res.put("powerVersion", eleObject.getString("1.3.6.1.4.1.2011.2.235.1.1.6.50.1.5"));
                    //额定功率（W）
                    res.put("powerRating", eleObject.getString("1.3.6.1.4.1.2011.2.235.1.1.6.50.1.6"));
                    //电源状态
                    res.put("powerStatus", eleObject.getString("1.3.6.1.4.1.2011.2.235.1.1.6.50.1.7"));
                    //实时功率（W）
                    res.put("powerInputPower", eleObject.getString("1.3.6.1.4.1.2011.2.235.1.1.6.50.1.8"));
                    //在位状态
                    res.put("powerPresence", eleObject.getString("1.3.6.1.4.1.2011.2.235.1.1.6.50.1.9"));
                    //通信协议
                    res.put("powerProtocol", eleObject.getString("1.3.6.1.4.1.2011.2.235.1.1.6.50.1.10"));
                    powerInfoList.add(res);
                }
            }
            resultMap.put("powerInfo", powerInfoList);
            return resultMap;
        } catch (Exception e) {
            log.error("获取电源信息异常！", e);
            return null;
        }
    }

    private Map<String, Object> fanInfoList(SNMPUtils snmpUtils, String tableTopOid) {
        HashMap<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> fanInfoList = new ArrayList<Map<String, Object>>();
        try {
            JSONObject tableData = snmpUtils.getPduTableView(tableTopOid);
            if (tableData != null && !tableData.isEmpty()) {
                JSONArray dataArray = tableData.getJSONArray("dataList");
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject eleObject = dataArray.getJSONObject(i);
                    Map<String, Object> res = new HashMap<>();

                    //风扇索引
                    res.put("fanIndex", eleObject.getString("1.3.6.1.4.1.2011.2.235.1.1.8.50.1.1"));
                    //风扇转速（单位：RPM，转 / 分钟）
                    res.put("fanSpeed", eleObject.getString("1.3.6.1.4.1.2011.2.235.1.1.8.50.1.2"));
                    //风扇在位状态
                    res.put("fanPresence", eleObject.getString("1.3.6.1.4.1.2011.2.235.1.1.8.50.1.3"));
                    //风扇状态
                    res.put("fanStatus", eleObject.getString("1.3.6.1.4.1.2011.2.235.1.1.8.50.1.4"));
                    //风扇物理位置
                    res.put("fanLocation", eleObject.getString("1.3.6.1.4.1.2011.2.235.1.1.8.50.1.5"));
                    //风扇功能描述
                    res.put("fanFunction", eleObject.getString("1.3.6.1.4.1.2011.2.235.1.1.8.50.1.6"));
                    //风扇设备名称
                    res.put("fanDeviceName", eleObject.getString("1.3.6.1.4.1.2011.2.235.1.1.8.50.1.7"));
                    //风扇转速百分比
                    res.put("fanSpeedRatio", eleObject.getString("1.3.6.1.4.1.2011.2.235.1.1.8.50.1.8"));

                    fanInfoList.add(res);
                }
            }
            resultMap.put("fanInfo", fanInfoList);
            return resultMap;
        } catch (Exception e) {
            log.error("获取风扇信息异常！", e);
            return null;
        }
    }

    private Map<String, Object> diskInfoList(SNMPUtils snmpUtils, String tableTopOid) {
        HashMap<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> diskInfoList = new ArrayList<Map<String, Object>>();
        try {
            JSONObject tableData = snmpUtils.getPduTableView(tableTopOid);
            if (tableData != null && !tableData.isEmpty()) {
                JSONArray dataArray = tableData.getJSONArray("dataList");
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject eleObject = dataArray.getJSONObject(i);
                    Map<String, Object> res = new HashMap<>();

                    //RAID 控制器索引
                    res.put("diskControllerIndex", eleObject.getString(".1.3.6.1.4.1.2011.2.235.1.1.39.50.1.1"));
                    //磁盘阵列索引
                    res.put("diskArrayIndex", eleObject.getString("1.3.6.1.4.1.2011.2.235.1.1.39.50.1.2"));
                    //磁盘阵列已使用空间（单位：MB）
                    res.put("diskArrayUsed", eleObject.getString("1.3.6.1.4.1.2011.2.235.1.1.39.50.1.3"));
                    //磁盘阵列空闲空间（单位：MB）
                    res.put("diskArrayFree", eleObject.getString("1.3.6.1.4.1.2011.2.235.1.1.39.50.1.4"));
                    //逻辑磁盘（LD）数量
                    res.put("diskArrayLDCount", eleObject.getString("1.3.6.1.4.1.2011.2.235.1.1.39.50.1.5"));
                    //逻辑磁盘ID列表（逗号分隔的 LD ID）
                    res.put("diskArrayLDId", eleObject.getString("1.3.6.1.4.1.2011.2.235.1.1.39.50.1.6"));
                    //物理磁盘（PD）数量
                    res.put("diskArrayPDCount", eleObject.getString("1.3.6.1.4.1.2011.2.235.1.1.39.50.1.7"));
                    //物理磁盘ID列表（逗号分隔的 PD ID）
                    res.put("diskArrayPDId", eleObject.getString("1.3.6.1.4.1.2011.2.235.1.1.39.50.1.8"));

                    diskInfoList.add(res);
                }
            }
            resultMap.put("diskInfo", diskInfoList);
            return resultMap;
        } catch (Exception e) {
            log.error("获取磁盘阵列信息异常！", e);
            return null;
        }
    }
}
