package com.yuanqiao.insight.collection.collector.collector.impl.DataBase.ShenTong;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.collector.Collector;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.acore.scheduler.SchedulerManagerInter;
import com.yuanqiao.insight.acore.utils.SpringContextUtil;
import com.yuanqiao.insight.collection.collector.utils.DataBaseCodecUtils;
import com.yuanqiao.insight.service.flow.core.util.DBUtils;
import com.yuanqiao.insight.utils.SNMPMetadataUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;

@Slf4j
@EnableAsync
public class ShenTongCollector implements Collector {

    private Device device;
    private ShenTongCodec shenTongCodec;
    private SchedulerManagerInter schedulerManager;
    private RedisTemplate redisTemplate;
    private SNMPMetadataUtils metadataUtils;

    String protocol;
    String ip;
    String port;
    String dbname;
    String dbusername;
    String dbpassword;
    String url;
    String driverClass;
    String jarUrl;
    DBUtils dbUtils;

    //初始化
    public void init(Device device, SchedulerManagerInter schedulerManager) {
        //绑定设备
        this.device = device;
        //创建解码器
        this.shenTongCodec = new ShenTongCodec();
        //绑定调度管理器
        this.schedulerManager = schedulerManager;
        //初始化RedisTemplate
        this.redisTemplate = (RedisTemplate) SpringContextUtil.getBean("redisTemplate");
        //初始化物模型工具类
        this.metadataUtils = SpringContextUtil.getBean(SNMPMetadataUtils.class);

        this.protocol = device.getProtocol();
        this.ip = device.getConnectParam().get("ip");
        this.port = device.getConnectParam().get("port");
        this.dbname = device.getConnectParam().get("dbname");
        this.url = protocol + "//" + ip + ":" + port + "/" + dbname;
        this.dbusername = device.getConnectParam().get("username");
        this.dbpassword = device.getConnectParam().get("password");
        this.driverClass = "com.oscar.Driver";
        this.jarUrl = device.getConnectParam().get("jarUrl");
        this.dbUtils = new DBUtils(driverClass, dbusername, dbpassword, url, jarUrl, "ShenTong");

    }

    @Async
    public void execute() {
        log.debug("-------- ShenTong数据库 " + device.getKey() + " 监控任务执行了...");
        DataBaseCodecUtils dataBaseCodecUtils = new DataBaseCodecUtils();
        dataBaseCodecUtils.init(dbUtils.getDbType(), dbUtils, metadataUtils, device, schedulerManager, redisTemplate, shenTongCodec, new JSONObject());
        try {
            dataBaseCodecUtils.collectorMainLine();
        } catch (Exception e) {
            log.error("ShenTong数据库 " + device.getKey() + " 监控任务执行异常！", e);
            dataBaseCodecUtils.collectorMainLine();
        }

    }
}
