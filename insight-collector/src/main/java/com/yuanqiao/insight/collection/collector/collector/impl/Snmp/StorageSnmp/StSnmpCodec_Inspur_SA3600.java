package com.yuanqiao.insight.collection.collector.collector.impl.Snmp.StorageSnmp;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.collection.collector.utils.SNMPCodecAndCollectUtils;
import com.yuanqiao.insight.collection.collector.utils.SNMPCodecInterface;
import com.yuanqiao.insight.common.util.common.TimeUtils;
import com.yuanqiao.insight.common.util.snmp.SNMPUtils;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigInteger;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class StSnmpCodec_Inspur_SA3600 implements SNMPCodecInterface {
    // 操作系统类型
    String osType;

    public void setOsType(String osType) {
        this.osType = osType;
    }

    //最外层jsonObject
    JSONObject jsonObject = new JSONObject();
    //公共结果集
    JSONObject commonMap = new JSONObject();

    @Override
    public JSONObject dataCodec(SNMPUtils snmpUtils, List<ProertyMetadata> metadataList, String stcaKey, Device device, SNMPCodecAndCollectUtils snmpCodecAndCollectUtils) {
        //清空外层jsonObject和公共结果集commonMap
        jsonObject.clear();
        commonMap.clear();

        //静态属性
        HashMap<String, Object> staticInfoMap = new HashMap<>();

        staticInfoMap.put("switchName", getName(snmpUtils).get("switchName"));
        staticInfoMap.put("switchDesc", getSysDesc(snmpUtils).get("switchDesc"));
        staticInfoMap.put("portNum", getPortNum(snmpUtils).get("portNum"));
        staticInfoMap.put("runTime", getRunTime(snmpUtils).get("runTime"));
        commonMap.put("staticInfo", staticInfoMap);
        commonMap.put("sysUpTime", getSysUpTime(snmpUtils).get("sysUpTime"));

        commonMap.put("cpuRate", getCpuRate(snmpUtils).get("cpuRate"));
        commonMap.put("memTotal", getMemTotal(snmpUtils).get("memTotal"));
        commonMap.put("memFree", getMemFree(snmpUtils).get("memFree"));
        commonMap.put("memRate", getMemRate(snmpUtils).get("memRate"));
        commonMap.put("netInOut", getNetInAndOut(snmpUtils).get("netInOut"));
        commonMap.put("portInfo", getPortInfoList(snmpUtils));

        commonMap.put("psuInfo", getPsuInfoList(snmpUtils));
        commonMap.put("diskInfo", getDiskInfoList(snmpUtils));
        //nosuchobject
//        commonMap.put("fanInfo", getFanInfoList(snmpUtils));
        metadataList.forEach(item -> {
            if (StringUtils.isNotEmpty(item.getDataType())) {
                jsonObject = snmpCodecAndCollectUtils.switchByDataType(item, stcaKey, jsonObject, commonMap, snmpUtils);
            }
        });
        return jsonObject;
    }

    /* ---------------------------------------------------------------------------------------------------------------- */

    /**
     * 获取设备运行时长
     *
     * @param snmpUtils
     * @return
     */
    private Map<String, String> getSysUpTime(SNMPUtils snmpUtils) {
        HashMap<String, String> map = new HashMap<>();
        try {
            String sysUpTime = snmpUtils.getPDU("*******.2.1.1.3.0");
            if (StringUtils.isNotEmpty(sysUpTime) && !sysUpTime.equalsIgnoreCase("noSuchObject")
                    && !sysUpTime.equalsIgnoreCase("noSuchInstance")) {
                // 去除字符串中的逗号
                sysUpTime = sysUpTime.replace(",", "");

                // 使用正则表达式匹配时间信息
                Pattern pattern = Pattern.compile("(?:(\\d+) days?)?(?:\\s*,?\\s*(\\d+):(\\d+)(?::(\\d+(?:\\.\\d+)?))?)?");
                Matcher matcher = pattern.matcher(sysUpTime);

                int days = 0, hours = 0, minutes = 0, seconds = 0;
                double milliseconds = 0;

                if (matcher.find()) {
                    if (matcher.group(1) != null) {
                        days = Integer.parseInt(matcher.group(1));
                    }
                    if (matcher.group(2) != null) {
                        hours = Integer.parseInt(matcher.group(2));
                    }
                    if (matcher.group(3) != null) {
                        minutes = Integer.parseInt(matcher.group(3));
                    }
                    if (matcher.group(4) != null) {
                        String[] secondsParts = matcher.group(4).split("\\.");
                        seconds = Integer.parseInt(secondsParts[0]);
                        if (secondsParts.length > 1) {
                            milliseconds = Double.parseDouble("0." + secondsParts[1]);
                        }
                    }
                }
                // 计算总毫秒数
                BigInteger totalMilliseconds = BigInteger.valueOf(days * 24 * 60 * 60 * 1000L)
                        .add(BigInteger.valueOf(hours * 60 * 60 * 1000L))
                        .add(BigInteger.valueOf(minutes * 60 * 1000L))
                        .add(BigInteger.valueOf((long) (seconds * 1000 + milliseconds * 1000)));
                sysUpTime = TimeUtils.getDistanceTime(totalMilliseconds);
                map.put("sysUpTime", sysUpTime);
            }
        } catch (Exception e) {
            log.error("获取运行时长出错", e);
            map.put("sysUpTime", "");
        }
        return map;
    }

    //获取对应oid的value值
    private Double getWalkValueByOid(String code, String oid, SNMPUtils snmpUtils) {
        Double value = 0.0;
        Map<String, String> pduWalk = null;
        try {
            pduWalk = snmpUtils.getPDUWalk(oid);
            if (pduWalk != null && pduWalk.size() > 0) {
                for (Map.Entry<String, String> entry : pduWalk.entrySet()) {
                    value += Long.parseLong(entry.getValue());
                }
            }
            return Double.valueOf(String.format("%.2f", value / 1024 / 1024 / 1024));
        } catch (Exception e) {
            log.error("获取" + code + "出错" + e.getMessage());
            return value;
        }
    }

    /* ---------------------------------------------------------------------------------------------------------------- */


    //获取inspur-sa3600端口数
    private Map<String, Integer> getPortNum(SNMPUtils snmpUtils) {
        try {
            HashMap<String, Integer> map = new HashMap<>();
            // inspur-sa3600端口数
            String portNum_String = snmpUtils.getPDU("*******.2.1.2.1.0");
            if (StringUtils.isEmpty(portNum_String)) {
                log.error("未获取到inspur-sa3600端口数...");
                map.put("portNum", 0);
            } else {
                Integer portNum = Integer.parseInt(portNum_String);
                map.put("portNum", portNum);
            }
            return map;
        } catch (Exception e) {
            log.error("获取inspur-sa3600端口数出错", e);
            HashMap<String, Integer> map = new HashMap<>();
            map.put("portNum", 0);
            return map;
        }
    }

    //获取inspur-sa3600名称
    private Map<String, String> getName(SNMPUtils snmpUtils) {
        try {
            HashMap<String, String> map = new HashMap<>();
            // inspur-sa3600名称
            String ifName = snmpUtils.getPDU("*******.2.1.1.5.0");
            if (ifName == null || "".equals(ifName.trim()) || "null".equalsIgnoreCase(ifName.trim())) {
                log.error("未获取到inspur-sa3600名称...");
                map.put("switchName", "- -");
            } else {
                map.put("switchName", ifName);
            }
            return map;
        } catch (Exception e) {
            log.error("获取inspur-sa3600名称出错", e);
            HashMap<String, String> map = new HashMap<>();
            map.put("switchName", "- -");
            return map;
        }
    }

    //获取inspur-sa3600描述
    private Map<String, String> getSysDesc(SNMPUtils snmpUtils) {
        try {
            HashMap<String, String> map = new HashMap<>();
            // inspur-sa3600描述
            String sysDesc = snmpUtils.getPDU("*******.2.1.1.1.0");
            if (sysDesc == null || "".equals(sysDesc.trim()) || "null".equalsIgnoreCase(sysDesc.trim())) {
                log.error("未获取到inspur-sa3600描述...");
                map.put("switchDesc", "- -");
            }
            map.put("switchDesc", sysDesc);
            return map;
        } catch (Exception e) {
            log.error("获取inspur-sa3600描述出错", e);
            HashMap<String, String> map = new HashMap<>();
            map.put("switchDesc", "- -");
            return map;
        }
    }

    //获取inspur-sa3600运行时间
    private Map<String, String> getRunTime(SNMPUtils snmpUtils) {
        try {
            HashMap<String, String> map = new HashMap<>();
            // inspur-sa3600运行时间
            String sysRunTime = snmpUtils.getPDU("*******.2.1.1.3.0");
            if (sysRunTime == null || "".equals(sysRunTime.trim()) || "null".equalsIgnoreCase(sysRunTime.trim())) {
                log.error("未获取到inspur-sa3600运行时间...");
                map.put("runTime", "--");
            }
//            if (!"".equals(sysRunTime) && sysRunTime != null && !sysRunTime.isEmpty()) {
//                String[] categoryArr = sysRunTime.split(":");
//                String dayRep = categoryArr[0].replace("days", "天");
//                String seconds = categoryArr[2].substring(0, 2);
//                sysRunTime = dayRep + ":" + categoryArr[1] + ":" + seconds;
//            }
            map.put("runTime", sysRunTime);
            return map;
        } catch (Exception e) {
            log.error("获取inspur-sa3600运行时间出错", e);
            HashMap<String, String> map = new HashMap<>();
            map.put("runTime", "--");
            return map;
        }
    }

    //获取inspur-sa3600cpu使用率
    private Map<String, Double> getCpuRate(SNMPUtils snmpUtils) {
        HashMap<String, Double> resMap = new HashMap<>();
        try {
            //初始化cpu平均使用率
            double cpuUtilization = 0.0;
            String cpuUse = snmpUtils.getPDU("*******.4.1.19621.1.107.2.1.2.0");
            if (StringUtils.isNotEmpty(cpuUse) && !cpuUse.equalsIgnoreCase("noSuchInstance") && !cpuUse.equalsIgnoreCase("noSuchObject")) {
                if (cpuUse.contains("%")) {
                    cpuUse = cpuUse.replace("%", "");
                }
                cpuUtilization = Double.parseDouble(cpuUse);
            }

            if (cpuUtilization == 0) {
                log.error("未获取到存储cpu使用率");
                resMap.put("cpuRate", 0.0);
                return resMap;
            } else {
                resMap.put("cpuRate", Double.valueOf(String.format("%.2f", cpuUtilization)));
                return resMap;
            }
        } catch (Exception e) {
            log.error("获取存储cpu平均使用率出错", e);
            resMap.put("cpuRate", 0.0);
            return resMap;
        }
    }

    //获取内存使用率
    private Map<String, Double> getMemRate(SNMPUtils snmpUtils) {

        HashMap<String, Double> resMap = new HashMap<>();
        try {
            // 内存总量
            double memTotal = 0;
            // 内存已用
            double memUse = 0;
            //初始化内存平均使用率
            double memUtilization = 0.0;
            String total = snmpUtils.getPDU("*******.4.1.19621.1.101.1.1.0");
            String use = snmpUtils.getPDU("*******.4.1.19621.1.101.1.2.0");
            if (StringUtils.isNotEmpty(total) && !total.equalsIgnoreCase("noSuchInstance") && StringUtils.isNotEmpty(use) && !use.equalsIgnoreCase("noSuchInstance")) {
                if (total.contains("TB")) {
                    total = total.replace("TB", "");
                }
                if (use.contains("TB")) {
                    use = use.replace("TB", "");
                }
                memTotal = Double.parseDouble(total);
                memUse = Double.parseDouble(use);
                memUtilization = 100 * memUse / memTotal;
                resMap.put("memRate", Double.parseDouble(String.format("%.2f", memUtilization)));
            } else {
                resMap.put("memRate", 0.0);
            }
            return resMap;
        } catch (Exception e) {
            log.error("获取浪潮SA3600内存平均使用率出错", e);
            resMap.put("memRate", 0.0);
            return resMap;
        }
    }

    //获取内存总量
    private Map<String, Double> getMemTotal(SNMPUtils snmpUtils) {
        HashMap<String, Double> resMap = new HashMap<>();
        try {
            String memTotal = snmpUtils.getPDU("*******.4.1.19621.1.101.1.1.0");
            if (StringUtils.isNotEmpty(memTotal) && !memTotal.equalsIgnoreCase("noSuchInstance")) {
                if (memTotal.contains("TB")) {
                    memTotal = memTotal.replace("TB", "");
                }
                resMap.put("memTotal", Double.valueOf(memTotal));
            } else {
                resMap.put("memTotal", 0.0);
            }
            return resMap;
        } catch (Exception e) {
            log.error("获取内存总量出错！", e);
            resMap.put("memTotal", 0.0);
            return resMap;
        }
    }

    //获取内空闲量
    private Map<String, Double> getMemFree(SNMPUtils snmpUtils) {
        HashMap<String, Double> resMap = new HashMap<>();
        try {
            String memFree = snmpUtils.getPDU("*******.4.1.19621.1.101.1.3.0");
            if (StringUtils.isNotEmpty(memFree) && !memFree.equalsIgnoreCase("noSuchInstance")) {
                if (memFree.contains("TB")) {
                    memFree = memFree.replace("TB", "");
                }
                resMap.put("memFree", Double.valueOf(memFree));
            } else {
                resMap.put("memFree", 0.0);
            }
            return resMap;
        } catch (Exception e) {
            log.error("获取内存空闲量出错！", e);
            resMap.put("memFree", 0.0);
            return resMap;
        }
    }

    //获取网络吞吐量
    private Map<String, Double> getNetInAndOut(SNMPUtils snmpUtils) {
        try {
            HashMap<String, Double> map = new HashMap<>();
            //KeyMap
            Map<String, String> KeyMap = snmpUtils.getPDUWalk("*******.2.1.2.2.1.1");
            int[] is = new int[KeyMap.size()];
            int i = 0;
            for (Map.Entry<String, String> en : KeyMap.entrySet()) {
                is[i] = Integer.parseInt(en.getValue());
                i++;
            }
            Arrays.sort(is);

            // 吞吐量
            double inAndOut = 0.0;
            int isLength = is.length;
            for (int j = 0; j < isLength; j++) {
                // 输入和输出字节总数
                String inSpeed_String = snmpUtils.getPDU("*******.2.1.2.2.1.10." + is[j]);
                String OutSpeed_String = snmpUtils.getPDU("*******.2.1.2.2.1.16." + is[j]);

                double inAndOutSpeed = 0;

                if (inSpeed_String != null && inSpeed_String != "" && OutSpeed_String != null && OutSpeed_String != "") {
                    inAndOutSpeed = Double.parseDouble(inSpeed_String) + Double.parseDouble(OutSpeed_String);
                }
                inAndOut += inAndOutSpeed;
            }
            if (Double.isNaN(inAndOut)) {
                log.error("未获取到inspur-sa3600网络吞吐量...");
                map.put("netInOut", 0.0);
            } else {
                inAndOut = inAndOut / 1024 / 1024 / 1024;
                map.put("netInOut", Double.parseDouble(String.format("%.2f", inAndOut)));
            }
            return map;
        } catch (Exception e) {
            log.error("获取inspur-sa3600网络吞吐量出错", e);
            HashMap<String, Double> map = new HashMap<>();
            map.put("netInOut", 0.0);
            return map;
        }
    }

    //获取端口详情
    private List<Map<String, Object>> getPortInfoList(SNMPUtils snmpUtils) {

        try {
            // 接口索引 主键
            Map<String, String> ifKeyMap = snmpUtils.getPDUWalk("*******.2.1.2.2.1.1");

            int[] is = new int[ifKeyMap.size()];
            int i = 0;
            for (Map.Entry<String, String> en : ifKeyMap.entrySet()) {
                is[i] = Integer.parseInt(en.getValue());
                i++;
            }
            Arrays.sort(is);

            //端口状态
            Map<String, String> portStatusMap = snmpUtils.getPDUWalk("*******.2.1.2.2.1.8");

            //端口描述
            Map<String, String> ifDescrProtosMap = snmpUtils.getPDUWalk("*******.2.1.2.2.1.2");

            // 带宽
            Map<String, String> ifSpeedProtosMap = snmpUtils.getPDUWalk("*******.2.1.2.2.1.5");

            //端口类型
            //Map<String, String> portTypeMap = snmpUtils.getPDUWalk("*******.2.1.2.2.1.3");

            //接收错误数据包
            Map<String, String> ifInErrorsMap = snmpUtils.getPDUWalk("*******.2.1.2.2.1.14");

            //发送的错误数据包
            Map<String, String> ifOutErrorsProtosMap = snmpUtils.getPDUWalk("*******.2.1.2.2.1.20");

            List<Map<String, Object>> portInfoList = new ArrayList<Map<String, Object>>();
            for (int j = 0; j < is.length; j++) {
                Map<String, Object> res = new HashMap<String, Object>();

                //端口类型
                //res.put("portType", portTypeMap.get("*******.2.1.2.2.1.3." + is[j]));

                //端口状态
                String str = portStatusMap.get("*******.2.1.2.2.1.8." + is[j]);
                if ("1".equals(str.trim())) {
                    res.put("portStatus", "连接");
                } else if ("2".equals(str.trim())) {
                    res.put("portStatus", "关闭");
                } else {
                    res.put("portStatus", "其他");
                }

                // 带宽
                String bandWidth_string = ifSpeedProtosMap.get("*******.2.1.2.2.1.5." + is[j]);
                if (StringUtils.isNotEmpty(bandWidth_string)) {
                    Double bandWidth = Double.parseDouble(bandWidth_string);
                    res.put("bandWidth", String.format("%.2f", bandWidth / 8 / 1024 / 1024));
                }

                //输入/输出流量
                String inSpeed = snmpUtils.getPDU("*******.2.1.2.2.1.10." + is[j]);
                if (org.apache.commons.lang.StringUtils.isNotEmpty(inSpeed)) {
                    Double inputFlow = Double.parseDouble(inSpeed);
                    res.put("inputFlow", String.format("%.2f", inputFlow * 8 / 1024 / 1024));
                }
                String outSpeed = snmpUtils.getPDU("*******.2.1.2.2.1.16." + is[j]);
                if (org.apache.commons.lang.StringUtils.isNotEmpty(outSpeed)) {
                    Double outputFlow = Double.parseDouble(outSpeed);
                    res.put("outputFlow", String.format("%.2f", outputFlow * 8 / 1024 / 1024));
                }

                //输入/输出错误包数
                String inErrorPackageString = ifInErrorsMap.get("*******.2.1.2.2.1.14." + is[j]);
                if (org.apache.commons.lang.StringUtils.isNotEmpty(inErrorPackageString)) {
                    Double inError = Double.parseDouble(inErrorPackageString);
                    res.put("inErrorPackage", String.format("%.2f", inError));
                }
                String outErrorPackageString = ifOutErrorsProtosMap.get("*******.2.1.2.2.1.20." + is[j]);
                if (org.apache.commons.lang.StringUtils.isNotEmpty(outErrorPackageString)) {
                    Double inError = Double.parseDouble(outErrorPackageString);
                    res.put("outErrorPackage", String.format("%.2f", inError));
                }

                //输入/输出丢失错误包数
                String inLossPackageString = snmpUtils.getPDU("*******.2.1.2.2.1.13." + is[j]);
                if (org.apache.commons.lang.StringUtils.isNotEmpty(inLossPackageString)) {
                    Double inLossPackage = Double.parseDouble(inLossPackageString);
                    res.put("inLossPackage", String.format("%.2f", inLossPackage));
                }
                String outLossPackageString = snmpUtils.getPDU("*******.2.1.2.2.1.19." + is[j]);
                if (org.apache.commons.lang.StringUtils.isNotEmpty(outLossPackageString)) {
                    Double outLossPackage = Double.parseDouble(outLossPackageString);
                    res.put("outLossPackage", String.format("%.2f", outLossPackage));
                }

                //输入单播报文的个数
                String inUcastPktsStr = snmpUtils.getPDU("*******.2.1.2.2.1.11." + is[j]);
                if (StringUtils.isNotEmpty(inUcastPktsStr) && !inUcastPktsStr.equals("noSuchInstance")) {
                    Double inUcastPkts = Double.parseDouble(inUcastPktsStr);
                    res.put("inUcastPkts", String.format("%.2f", inUcastPkts));
                }

                //输入非单播报文的个数
                String inNUcastPktsStr = snmpUtils.getPDU("*******.2.1.2.2.1.12." + is[j]);
                if (StringUtils.isNotEmpty(inNUcastPktsStr) && !inNUcastPktsStr.equals("noSuchInstance")) {
                    Double inNUcastPkts = Double.parseDouble(inNUcastPktsStr);
                    res.put("inNUcastPkts", String.format("%.2f", inNUcastPkts));
                }

                //输出单播报文的个数
                String outUcastPktsStr = snmpUtils.getPDU("*******.2.1.2.2.1.17." + is[j]);
                if (StringUtils.isNotEmpty(outUcastPktsStr) && !outUcastPktsStr.equals("noSuchInstance")) {
                    Double outUcastPkts = Double.parseDouble(outUcastPktsStr);
                    res.put("outUcastPkts", String.format("%.2f", outUcastPkts));
                }

                //输出非单播报文的个数
                String outNUcastPktsStr = snmpUtils.getPDU("*******.2.1.2.2.1.18." + is[j]);
                if (StringUtils.isNotEmpty(outNUcastPktsStr) && !outNUcastPktsStr.equals("noSuchInstance")) {
                    Double outNUcastPkts = Double.parseDouble(outNUcastPktsStr);
                    res.put("outNUcastPkts", String.format("%.2f", outNUcastPkts));
                }

                // 接口描述
                res.put("portDesc", ifDescrProtosMap.get("*******.2.1.2.2.1.2." + is[j]));

                //索引
                res.put("index", is[j]);

                portInfoList.add(res);
            }
            return portInfoList;
        } catch (Exception e) {
            log.error("获取端口信息出错", e);
            return null;
        }
    }

    /**
     * 电源信息
     *
     * @param snmpUtils
     * @return
     */
    private List<Map<String, Object>> getPsuInfoList(SNMPUtils snmpUtils) {

        try {
            List<Map<String, Object>> psuInfoList = new ArrayList<Map<String, Object>>();
            // 索引 主键
            Map<String, String> psuIdMap = snmpUtils.getPDUWalk("*******.4.1.19621.1.108.6.1.1");
            //电源ID
            Map<String, String> enclosureIdMap = snmpUtils.getPDUWalk("*******.4.1.19621.1.108.6.1.2");
            //电源状态
            Map<String, String> psuStatusMap = snmpUtils.getPDUWalk("*******.4.1.19621.1.108.6.1.3");
            //电源输入状态
            Map<String, String> psuInputMap = snmpUtils.getPDUWalk("*******.4.1.19621.1.108.6.1.4");
            //电源输出状态
            Map<String, String> psuOutMap = snmpUtils.getPDUWalk("*******.4.1.19621.1.108.6.1.5");
            //需要电源输入
            Map<String, String> psuInputPowerMap = snmpUtils.getPDUWalk("*******.4.1.19621.1.108.6.1.6");
            //不需要电源输入
            Map<String, String> psuOutPowerMap = snmpUtils.getPDUWalk("*******.4.1.19621.1.108.6.1.7");
            //是否可以删除
            Map<String, String> redundantMap = snmpUtils.getPDUWalk("*******.4.1.19621.1.108.6.1.8");
            //电源日志序列号
            Map<String, String> errorLogMap = snmpUtils.getPDUWalk("*******.4.1.19621.1.108.6.1.9");
            //编号
            Map<String, String> fruNumMap = snmpUtils.getPDUWalk("*******.4.1.19621.1.108.6.1.10");
            for (Map.Entry<String, String> entrys : psuIdMap.entrySet()) {
                String keyString = entrys.getKey().replace("*******.4.1.19621.1.108.6.1.1.", "");
                Map<String, Object> res = new HashMap<String, Object>();
                //索引
                res.put("index", keyString);
                res.put("enclosureId", enclosureIdMap.get("*******.4.1.19621.1.108.6.1.2." + keyString));
                res.put("psuStatus", psuStatusMap.get("*******.4.1.19621.1.108.6.1.3." + keyString));
                res.put("psuInput", psuInputMap.get("*******.4.1.19621.1.108.6.1.4." + keyString));
                res.put("psuOut", psuOutMap.get("*******.4.1.19621.1.108.6.1..5." + keyString));
                res.put("psuInputPower", psuInputPowerMap.get("*******.4.1.19621.1.108.6.1.6." + keyString));
                res.put("psuOutPower", psuOutPowerMap.get("*******.4.1.19621.1.108.6.1.7." + keyString));
                res.put("redundant", redundantMap.get("*******.4.1.19621.1.108.6.1.8." + keyString));
                res.put("errorLog", errorLogMap.get("*******.4.1.19621.1.108.6.1.9." + keyString));
                res.put("fruNum", fruNumMap.get("*******.4.1.19621.1.108.6.1.10." + keyString));

                psuInfoList.add(res);
            }
            return psuInfoList;
        } catch (Exception e) {
            log.error("获取电源信息出错", e);
            return null;
        }
    }

    /**
     * 磁盘信息
     *
     * @param snmpUtils
     * @return
     */
    private List<Map<String, Object>> getDiskInfoList(SNMPUtils snmpUtils) {
        List<Map<String, Object>> diskInfoList = new ArrayList<Map<String, Object>>();
        try {
            // 磁盘域id 索引
            Map<String, String> indexMap = snmpUtils.getPDUWalk("*******.4.1.19621.*********.1");
            //磁盘域名称
            Map<String, String> nameMap = snmpUtils.getPDUWalk("*******.4.1.19621.*********.2");
            //磁盘域健康状态  0：未知；1：正常；2：故障
            Map<String, String> statusMap = snmpUtils.getPDUWalk("*******.4.1.19621.*********.3");
            //磁盘容量
            Map<String, String> capacityMap = snmpUtils.getPDUWalk("*******.4.1.19621.*********.4");
            //磁盘模式
            Map<String, String> modeMap = snmpUtils.getPDUWalk("*******.4.1.19621.*********.5");
            //磁盘组id
            Map<String, String> groupIdMap = snmpUtils.getPDUWalk("*******.4.1.19621.*********.6");
            //磁盘组名字
            Map<String, String> groupNameMap = snmpUtils.getPDUWalk("*******.4.1.19621.*********.7");
            //磁盘ctrlLun信息
            Map<String, String> ctrlLunMap = snmpUtils.getPDUWalk("*******.4.1.19621.*********.8");
            //磁盘控制器名字
            Map<String, String> controllerNametMap = snmpUtils.getPDUWalk("*******.4.1.19621.*********.9");
            //磁盘UID
            Map<String, String> uidMap = snmpUtils.getPDUWalk("*******.4.1.19621.*********.10");
            //磁盘Tier
            Map<String, String> tierMap = snmpUtils.getPDUWalk("*******.4.1.19621.*********.11");
            //磁盘加密
            Map<String, String> encriptyMap = snmpUtils.getPDUWalk("*******.4.1.19621.*********.12");
            //磁盘SiteId
            Map<String, String> siteIdMap = snmpUtils.getPDUWalk("*******.4.1.19621.*********.13");
            //磁盘Site名字
            Map<String, String> siteNameMap = snmpUtils.getPDUWalk("*******.4.1.19621.*********.14");
            //磁盘分布式
            Map<String, String> distributedMap = snmpUtils.getPDUWalk("*******.4.1.19621.*********.15");
            //磁盘重复数据
            Map<String, String> dupeMap = snmpUtils.getPDUWalk("*******.4.1.19621.*********.16");

            for (Map.Entry<String, String> entrys : indexMap.entrySet()) {
                String keyString = entrys.getKey().replace("*******.4.1.19621.*********.1.", "");
                Map<String, Object> res = new HashMap<String, Object>();
                //索引
                res.put("index", keyString);
                String name = nameMap.get("*******.4.1.19621.*********.2." + keyString);
                if (StringUtils.isNotEmpty(name)) {
                    res.put("name", name);
                }
                String status = statusMap.get("*******.4.1.19621.*********.3." + keyString);
                if (StringUtils.isNotEmpty(status)) {
                    res.put("status", status);
                }
                String capacity = capacityMap.get("*******.4.1.19621.*********.4." + keyString);
                if (StringUtils.isNotEmpty(capacity)) {
                    res.put("capacity", capacity);
                }
                String mode = modeMap.get("*******.4.1.19621.*********.5." + keyString);
                if (StringUtils.isNotEmpty(mode)) {
                    res.put("mode", mode);
                }
                String groupId = groupIdMap.get("*******.4.1.19621.*********.6." + keyString);
                if (StringUtils.isNotEmpty(groupId)) {
                    res.put("groupId", groupId);
                }
                String groupName = groupNameMap.get("*******.4.1.19621.*********.7." + keyString);
                if (StringUtils.isNotEmpty(groupName)) {
                    res.put("groupName", groupName);
                }
                String ctrlLun = ctrlLunMap.get("*******.4.1.19621.*********.8." + keyString);
                if (StringUtils.isNotEmpty(ctrlLun)) {
                    res.put("ctrlLun", ctrlLun);
                }
                String controllerName = controllerNametMap.get("*******.4.1.19621.*********.9." + keyString);
                if (StringUtils.isNotEmpty(ctrlLun)) {
                    res.put("controllerName", controllerName);
                }
                String uid = uidMap.get("*******.4.1.19621.*********.10." + keyString);
                if (StringUtils.isNotEmpty(uid)) {
                    res.put("uid", uid);
                }
                String tier = tierMap.get("*******.4.1.19621.*********.11." + keyString);
                if (StringUtils.isNotEmpty(tier)) {
                    res.put("tier", tier);
                }
                String encripty = encriptyMap.get("*******.4.1.19621.*********.12." + keyString);
                if (StringUtils.isNotEmpty(encripty)) {
                    res.put("encripty", encripty);
                }
                String siteId = siteIdMap.get("*******.4.1.19621.*********.13." + keyString);
                if (StringUtils.isNotEmpty(siteId)) {
                    res.put("siteId", siteId);
                }
                String siteName = siteNameMap.get("*******.4.1.19621.*********.13." + keyString);
                if (StringUtils.isNotEmpty(siteName)) {
                    res.put("siteName", siteName);
                }
                String distributed = distributedMap.get("*******.4.1.19621.*********.15." + keyString);
                if (StringUtils.isNotEmpty(distributed)) {
                    res.put("distributed", distributed);
                }
                String dupe = dupeMap.get("*******.4.1.19621.*********.16." + keyString);
                if (StringUtils.isNotEmpty(dupe)) {
                    res.put("dupe", dupe);
                }
                diskInfoList.add(res);
            }
        } catch (Exception e) {
            log.error("获取磁盘信息出错", e);
        }
        return diskInfoList;
    }

    /**
     * 风扇信息
     *
     * @param snmpUtils
     * @return
     */
    private List<Map<String, Object>> getFanInfoList(SNMPUtils snmpUtils) {

        try {
            List<Map<String, Object>> fanInfoList = new ArrayList<Map<String, Object>>();
            //  索引
            Map<String, String> indexMap = snmpUtils.getPDUWalk("*******.4.1.19621.1.108.7.1.1");
            //风扇id
            Map<String, String> fanIdMap = snmpUtils.getPDUWalk("*******.4.1.19621.1.108.7.1.2");
            //风扇状态
            Map<String, String> fanStatusMap = snmpUtils.getPDUWalk("*******.4.1.19621.1.108.7.1.3");
            //故障灯状态
            Map<String, String> ledStatusMap = snmpUtils.getPDUWalk("*******.4.1.19621.1.108.7.1.4");
            //风扇日志序列号
            Map<String, String> errorLogMap = snmpUtils.getPDUWalk("*******.4.1.19621.1.108.7.1.5");
            //风扇fru标识
            Map<String, String> fruIdMap = snmpUtils.getPDUWalk("*******.4.1.19621.1.108.7.1.6");

            for (Map.Entry<String, String> entrys : indexMap.entrySet()) {
                String keyString = entrys.getKey().replace("*******.4.1.19621.1.108.7.1.1.", "");
                Map<String, Object> res = new HashMap<String, Object>();
                //索引
                res.put("index", entrys.getValue());
                res.put("fanId", fanIdMap.get("*******.4.1.19621.1.108.7.1.2." + keyString));
                res.put("fanStatus", fanStatusMap.get("*******.4.1.19621.1.108.7.1.3." + keyString));
                res.put("ledStatus", ledStatusMap.get("*******.4.1.19621.1.108.7.1.4." + keyString));
                res.put("errorLog", errorLogMap.get("*******.4.1.19621.1.108.7.1.5." + keyString));
                res.put("fruId", fruIdMap.get("*******.4.1.19621.1.108.7.1.6." + keyString));
                fanInfoList.add(res);
            }
            return fanInfoList;
        } catch (Exception e) {
            log.error("获取风扇信息出错", e);
            return null;
        }
    }
}
