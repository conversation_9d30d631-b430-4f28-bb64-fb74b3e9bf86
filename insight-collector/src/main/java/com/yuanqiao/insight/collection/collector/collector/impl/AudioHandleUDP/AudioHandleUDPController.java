package com.yuanqiao.insight.collection.collector.collector.impl.AudioHandleUDP;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.collector.Collector;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.acore.scheduler.SchedulerManagerInter;
import com.yuanqiao.insight.acore.utils.SpringContextUtil;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import com.yuanqiao.insight.utils.SNMPMetadataUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.List;

@Slf4j
@EnableAsync
public class AudioHandleUDPController implements Collector {
    private Device device;
    private AudioHandleUDPCodec audioHandleUDPCodec;
    private SchedulerManagerInter schedulerManager;

    //初始化
    public void init(Device device, SchedulerManagerInter schedulerManager) {
        //绑定设备
        this.device = device;
        //绑定解码器
        this.audioHandleUDPCodec = new AudioHandleUDPCodec();
        //绑定调度管理器
        this.schedulerManager = schedulerManager;
    }

    //执行方法
    @Async
    public void execute() {
        log.debug("--------UDP音频管理器监控任务执行了...");

        //将数据库中的物模型同步到redis中
        log.info("将数据库中的物模型同步到redis中...");
        SNMPMetadataUtils metadataUtils = (SNMPMetadataUtils) SpringContextUtil.getBean("SNMPMetadataUtils");
        metadataUtils.setMetadata(device.getKey());

        //初始化最外层jsonObject
        JSONObject jsonObject = null;

        //从缓存中获取物模型
        List<ProertyMetadata> metadataList = null;
        RedisTemplate redisTemplate = (RedisTemplate) SpringContextUtil.getBean("redisTemplate");


        log.info("当前正在连接的设备ip为：" + device.getConnectParam().get("ip") + " 设备端口号为：" + device.getConnectParam().get("port"));

        //根据设备唯一标识，去缓存中获取该设备所属的物模型
        String key = "dema:" + device.getKey();
        if (redisTemplate.hasKey(key)) {
            metadataList = redisTemplate.opsForList().range(key, 0, -1);

            //调用适配器, 解码数据
            log.info("开始获取device_code：" + device.getKey() + " 的信息");
            jsonObject = audioHandleUDPCodec.dataCodecObject(device, metadataList, "stca:" + device.getKey());

            if (jsonObject != null && !jsonObject.isEmpty()) {
/*                //解析到数据, 调用ShedulerManager, 设置设备在线,
                this.schedulerManager.setDeviceUp();*/
                //解析到数据，调用ShedulerManager, 将设备标识放入静态队列容器,
                this.schedulerManager.putIntoContainer(device);
                log.info("Codec解析出的数据：" + jsonObject);
            } else {
                log.error("未解析到数据！");
                //未解析到数据, 调用ShedulerManager, 设置设备离线,
                this.schedulerManager.setDeviceDown(device);
                return;
            }
        } else {
            log.error("在缓存中未找到该设备的物模型！");
            //未找到物模型, 调用ShedulerManager, 设置设备离线,
            this.schedulerManager.setDeviceDown(device);
            return;
        }


        //调用ShedulerManager, 发布设备数据事件
        this.schedulerManager.publishDataEvent(device.getKey());

        //调用ShedulerManager, 设置状态容器
        this.schedulerManager.setStatusCache(device, jsonObject);

    }

}
