package com.yuanqiao.insight.collection.collector.collector.impl.MessageQueues.RabbitMQ;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.device.Device;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.*;

/**
 * RabbitMQ 数据解析与组装工具类
 */
@Slf4j
public class RabbitMQCodec {


    public JSONObject dataCodecObject(String overView, String vhosts, String channels,
                                      String nodes, String exchanges, String queues,
                                      String permissions,String connections,String bindings,String users,
                                      String featureFlags,String policies) {
        JSONObject outcomeMap = new JSONObject();

        processOverview(overView, outcomeMap);
        processVHosts(vhosts, outcomeMap);
        processChannels(channels, outcomeMap);
        processNodes(nodes, outcomeMap);
        processExchanges(exchanges, outcomeMap);
        processQueues(queues, outcomeMap);
        processPermissions(permissions, outcomeMap);
        processConnections(connections, outcomeMap);
        processBindings(bindings, outcomeMap);
        processUsers(users, outcomeMap);
        processFeatureFlags(featureFlags, outcomeMap);
        processPolicies(policies, outcomeMap);
        return outcomeMap;
    }


    public void processOverview(String overView, JSONObject outcomeMap) {
        JSONObject overviewJson = JSONObject.parseObject(overView);
        // 解析 overview
        if (CollUtil.isNotEmpty(overviewJson)) {
            JSONObject overviewMap = new JSONObject();
            overviewMap.put("rabbitmqVersion", overviewJson.getString("rabbitmq_version"));
            overviewMap.put("erlangVersion", overviewJson.getString("erlang_version"));
            overviewMap.put("clusterName", overviewJson.getString("cluster_name"));
            overviewMap.put("ratesMode", overviewJson.getString("rates_mode"));

            JSONObject objectTotals = overviewJson.getJSONObject("object_totals");
            if (objectTotals != null) {
                overviewMap.put("totalExchanges", objectTotals.getInteger("exchanges"));
                overviewMap.put("totalQueues", objectTotals.getInteger("queues"));
                overviewMap.put("totalConnections", objectTotals.getInteger("connections"));
                overviewMap.put("totalChannels", objectTotals.getInteger("channels"));
            }

           /* JSONArray listeners = overviewJson.getJSONArray("listeners");
            if (listeners != null && !listeners.isEmpty()) {
                List<Map<String, Object>> listenerList = new ArrayList<>();
                for (int i = 0; i < listeners.size(); i++) {
                    JSONObject listener = listeners.getJSONObject(i);
                    Map<String, Object> listenerInfo = new HashMap<>();
                    listenerInfo.put("protocol", listener.getString("protocol"));
                    listenerInfo.put("ipAddress", listener.getString("ip_address"));
                    listenerInfo.put("port", listener.getInteger("port"));
                    listenerList.add(listenerInfo);
                }
                overviewMap.put("listeners", listenerList);
            }*/
            outcomeMap.put("overView", overviewMap);
        }

    }

    public void processVHosts(String vhosts, JSONObject outcomeMap) {
        JSONArray vhostsArray = JSONArray.parseArray(vhosts);
        // 解析 vhosts
        if (CollUtil.isNotEmpty(vhostsArray)) {
            JSONArray vhostsResultArray = new JSONArray();

            for (int i = 0; i < vhostsArray.size(); i++) {
                JSONObject vhost = vhostsArray.getJSONObject(i);
                JSONObject vhostMap = new JSONObject();

                vhostMap.put("name", vhost.getString("name"));
                vhostMap.put("tracing", vhost.getBoolean("tracing"));

                vhostMap.put("sendOct", vhost.getLongValue("send_oct"));

                // 提取 sendOctDetails 中的 rate 字段，并做空值保护
                JSONObject sendOctDetails = vhost.getJSONObject("send_oct_details");
                if (sendOctDetails != null && sendOctDetails.containsKey("rate")) {
                    vhostMap.put("sendOctRate", sendOctDetails.getDoubleValue("rate"));
                } else {
                    vhostMap.put("sendOctRate", 0.0);
                }

                vhostMap.put("recvOct", vhost.getLongValue("recv_oct"));

                JSONObject recvOctDetails = vhost.getJSONObject("recv_oct_details");
                if (recvOctDetails != null && recvOctDetails.containsKey("rate")) {
                    vhostMap.put("recvOctRate", recvOctDetails.getDoubleValue("rate"));
                } else {
                    vhostMap.put("recvOctRate", 0.0);
                }
                vhostMap.put("messagesReady", vhost.getLongValue("messages_ready"));
                vhostMap.put("messagesUnacknowledged", vhost.getLongValue("messages_unacknowledged"));
                vhostMap.put("totalMessages", vhost.getLongValue("messages"));

                JSONObject messageStats = vhost.getJSONObject("message_stats");
                if (messageStats != null) {
                    vhostMap.put("publishCount", messageStats.getLongValue("publish"));
                    vhostMap.put("deliverCount", messageStats.getLongValue("deliver"));
                    vhostMap.put("ackCount", messageStats.getLongValue("ack"));
                }

                vhostsResultArray.add(vhostMap);
            }

            outcomeMap.put("vhosts", vhostsResultArray);
        }
    }

    public void processChannels(String channels, JSONObject outcomeMap) {
        JSONArray channelsArray = JSONArray.parseArray(channels);
        if (CollUtil.isNotEmpty(channelsArray)) {
            JSONArray processedChannelsArray = new JSONArray();
            for (int i = 0; i < channelsArray.size(); i++) {
                JSONObject channelJson = channelsArray.getJSONObject(i);
                JSONObject channelFlat = new JSONObject();

                // 基础字段
                channelFlat.put("name", channelJson.getString("name"));
                channelFlat.put("vhost", channelJson.getString("vhost"));
                channelFlat.put("user", channelJson.getString("user"));
                channelFlat.put("number", channelJson.getInteger("number"));
                channelFlat.put("state", channelJson.getString("state"));

                // connection_details
                JSONObject connectionDetails = channelJson.getJSONObject("connection_details");
                if (connectionDetails != null) {
                    channelFlat.put("peerHost", connectionDetails.getString("peer_host"));
                    channelFlat.put("peerPort", connectionDetails.getInteger("peer_port"));
                    channelFlat.put("connectionName", connectionDetails.getString("name"));
                }

                // message_stats
                JSONObject messageStats = channelJson.getJSONObject("message_stats");
                if (messageStats != null) {
                    channelFlat.put("publishCount", messageStats.getLongValue("publish"));
                    channelFlat.put("deliverGetCount", messageStats.getLongValue("deliver_get"));
                    channelFlat.put("ackCount", messageStats.getLongValue("ack"));
                }

                // 其他字段保持不变
                channelFlat.put("reductions", channelJson.getLongValue("reductions"));
                channelFlat.put("reductionsRate", channelJson.getJSONObject("reductions_details").getDoubleValue("rate"));
                channelFlat.put("prefetchCount", channelJson.getLongValue("prefetch_count"));
                channelFlat.put("consumerCount", channelJson.getLongValue("consumer_count"));

                processedChannelsArray.add(channelFlat);
            }
            outcomeMap.put("channels", processedChannelsArray);
        }
    }

    public void processNodes(String nodes, JSONObject outcomeMap) {
        JSONArray nodesArray = JSONArray.parseArray(nodes);
        if (CollUtil.isNotEmpty(nodesArray)) {
            JSONArray processedNodesArray = new JSONArray();
            for (int i = 0; i < nodesArray.size(); i++) {
                JSONObject nodeJson = nodesArray.getJSONObject(i);
                JSONObject nodeFlat = new JSONObject();

                // 基础字段
                nodeFlat.put("name", nodeJson.getString("name"));
                nodeFlat.put("nodeType", nodeJson.getString("type"));
                nodeFlat.put("running", nodeJson.getBooleanValue("running"));

                long uptimeMs = nodeJson.getLongValue("uptime");
                long totalHours = uptimeMs / 3600000;     // 总小时数
                long minutes = (uptimeMs / 60000) % 60;    // 总分钟 % 60
                long seconds = (uptimeMs / 1000) % 60;     // 总秒 % 60
                String uptimeFormatted = String.format("%02d:%02d:%02d", totalHours, minutes, seconds);
                nodeFlat.put("uptime", uptimeFormatted);

                nodeFlat.put("osPid", nodeJson.getString("os_pid"));

                // 文件描述符
                nodeFlat.put("fdUsed", nodeJson.getLongValue("fd_used"));
                nodeFlat.put("fdTotal", nodeJson.getLongValue("fd_total"));

                // socket 使用
                nodeFlat.put("socketsUsed", nodeJson.getLongValue("sockets_used"));
                nodeFlat.put("socketsTotal", nodeJson.getLongValue("sockets_total"));

                // 内存使用
                nodeFlat.put("memUsed", nodeJson.getLongValue("mem_used"));
                nodeFlat.put("memLimit", nodeJson.getLongValue("mem_limit"));
                //nodeFlat.put("memAlarm", nodeJson.getBooleanValue("mem_alarm"));

                // 磁盘空间
                nodeFlat.put("diskFree", nodeJson.getLongValue("disk_free"));
                nodeFlat.put("diskFreeLimit", nodeJson.getLongValue("disk_free_limit"));
                //nodeFlat.put("diskFreeAlarm", nodeJson.getBooleanValue("disk_free_alarm"));

                // 进程相关
                nodeFlat.put("procUsed", nodeJson.getLongValue("proc_used"));
                nodeFlat.put("procTotal", nodeJson.getLongValue("proc_total"));

                // IO & GC 指标
                nodeFlat.put("contextSwitchesRate", nodeJson.getJSONObject("context_switches_details").getDoubleValue("rate"));
                nodeFlat.put("ioWriteBytesRate", nodeJson.getJSONObject("io_write_bytes_details").getDoubleValue("rate"));
                nodeFlat.put("ioReadBytesRate", nodeJson.getJSONObject("io_read_bytes_details").getDoubleValue("rate"));
                nodeFlat.put("gcReclaimedRate", nodeJson.getJSONObject("gc_bytes_reclaimed_details").getDoubleValue("rate"));

                processedNodesArray.add(nodeFlat);
            }

            outcomeMap.put("nodes", processedNodesArray);
        }
    }


    public void processExchanges(String exchanges, JSONObject outcomeMap) {
        JSONArray exchangesArray = JSONArray.parseArray(exchanges);
        // 解析 exchanges
        if (CollUtil.isNotEmpty(exchangesArray)) {
            JSONArray processedExchangesArray = new JSONArray();
            for (int i = 0; i < exchangesArray.size(); i++) {
                JSONObject exchangeJson = exchangesArray.getJSONObject(i);
                JSONObject exchangeFlat = new JSONObject();

                // 基本字段
                exchangeFlat.put("name", exchangeJson.getString("name"));
                exchangeFlat.put("vhost", exchangeJson.getString("vhost"));
                exchangeFlat.put("exchangesType", exchangeJson.getString("type"));
                exchangeFlat.put("durable", exchangeJson.getBooleanValue("durable"));
                exchangeFlat.put("autoDelete", exchangeJson.getBooleanValue("auto_delete"));
                exchangeFlat.put("internal", exchangeJson.getBooleanValue("internal"));
                exchangeFlat.put("arguments", exchangeJson.getJSONObject("arguments").toString());

                // message_stats
                JSONObject stats = exchangeJson.getJSONObject("message_stats");
                if (stats != null) {
                    exchangeFlat.put("publishOut", stats.getLongValue("publish_out"));
                    exchangeFlat.put("publishOutRate", stats.getJSONObject("publish_out_details").getDoubleValue("rate"));
                    exchangeFlat.put("publishIn", stats.getLongValue("publish_in"));
                    exchangeFlat.put("publishInRate", stats.getJSONObject("publish_in_details").getDoubleValue("rate"));
                } else {
                    exchangeFlat.put("publishOut", 0L);
                    exchangeFlat.put("publishOutRate", 0.0);
                    exchangeFlat.put("publishIn", 0L);
                    exchangeFlat.put("publishInRate", 0.0);
                }

                processedExchangesArray.add(exchangeFlat);
            }
            outcomeMap.put("exchanges", processedExchangesArray);
        }
    }

    public void processQueues(String queues, JSONObject outcomeMap) {
        JSONArray queuesArray = JSONArray.parseArray(queues);
        // 解析 queues
        if (CollUtil.isNotEmpty(queuesArray)) {
            JSONArray processedQueuesArray = new JSONArray();

            for (int i = 0; i < queuesArray.size(); i++) {
                JSONObject queueJson = queuesArray.getJSONObject(i);
                JSONObject queueMap = new JSONObject();

                queueMap.put("name", queueJson.getString("name"));
                queueMap.put("vhost", queueJson.getString("vhost"));
                queueMap.put("durable", queueJson.getBooleanValue("durable"));
                queueMap.put("autoDelete", queueJson.getBooleanValue("auto_delete"));
                queueMap.put("exclusive", queueJson.getBooleanValue("exclusive"));
                queueMap.put("arguments", queueJson.getJSONObject("arguments").toString());
                queueMap.put("node", queueJson.getString("node"));
                queueMap.put("state", queueJson.getString("state"));
                queueMap.put("consumers", queueJson.getIntValue("consumers"));
                queueMap.put("consumerUtilisation", queueJson.getDoubleValue("consumer_utilisation"));

                queueMap.put("messages", queueJson.getLongValue("messages"));
                queueMap.put("messagesReady", queueJson.getLongValue("messages_ready"));
                queueMap.put("messagesUnacknowledged", queueJson.getLongValue("messages_unacknowledged"));
                queueMap.put("memory", queueJson.getLongValue("memory"));

                if (queueJson.containsKey("message_stats")) {
                    JSONObject stats = queueJson.getJSONObject("message_stats");

                    queueMap.put("publish", stats.getLongValue("publish"));
                    queueMap.put("publishRate", stats.getJSONObject("publish_details").getDoubleValue("rate"));

                    queueMap.put("deliver", stats.getLongValue("deliver"));
                    queueMap.put("deliverRate", stats.getJSONObject("deliver_details").getDoubleValue("rate"));

                    queueMap.put("ack", stats.getLongValue("ack"));
                    queueMap.put("ackRate", stats.getJSONObject("ack_details").getDoubleValue("rate"));

                    queueMap.put("redeliver", stats.getLongValue("redeliver"));
                    queueMap.put("redeliverRate", stats.getJSONObject("redeliver_details").getDoubleValue("rate"));
                } else {
                    queueMap.put("publish", 0L);
                    queueMap.put("publishRate", 0.0);

                    queueMap.put("deliver", 0L);
                    queueMap.put("deliverRate", 0.0);

                    queueMap.put("ack", 0L);
                    queueMap.put("ackRate", 0.0);

                    queueMap.put("redeliver", 0L);
                    queueMap.put("redeliverRate", 0.0);
                }

                queueMap.put("reductions", queueJson.getLongValue("reductions"));
                queueMap.put("reductionsRate", queueJson.getJSONObject("reductions_details").getDoubleValue("rate"));

                processedQueuesArray.add(queueMap);
            }

            outcomeMap.put("queues", processedQueuesArray);
        }
    }


    private void processPermissions(String permissions, JSONObject outcomeMap) {
        JSONArray permissionsArray = JSONArray.parseArray(permissions);
        if (CollUtil.isNotEmpty(permissionsArray)) {
            JSONArray processedPermissionsArray = new JSONArray();
            for (int i = 0; i < permissionsArray.size(); i++) {
                JSONObject permissionJson = permissionsArray.getJSONObject(i);
                JSONObject permissionFlat = new JSONObject();

                // 用户名
                permissionFlat.put("user", permissionJson.getString("user"));

                // 虚拟主机
                permissionFlat.put("vhost", permissionJson.getString("vhost"));

                // 权限配置
                permissionFlat.put("configureRegex", permissionJson.getString("configure"));
                permissionFlat.put("writeRegex", permissionJson.getString("write"));
                permissionFlat.put("readRegex", permissionJson.getString("read"));

                processedPermissionsArray.add(permissionFlat);
            }
            outcomeMap.put("permissions", processedPermissionsArray);
        }
    }


    private void processConnections(String connections, JSONObject outcomeMap) {
        JSONArray connectionsArray = JSONArray.parseArray(connections);
        if (CollUtil.isNotEmpty(connectionsArray)) {
            JSONArray processedConnectionsArray = new JSONArray();
            for (int i = 0; i < connectionsArray.size(); i++) {
                JSONObject connJson = connectionsArray.getJSONObject(i);
                JSONObject connFlat = new JSONObject();

                // 基础信息
                connFlat.put("authMechanism", connJson.getString("auth_mechanism"));
                connFlat.put("channelMax", connJson.getInteger("channel_max"));
                connFlat.put("channels", connJson.getInteger("channels"));

                // 客户端信息
                JSONObject clientProps = connJson.getJSONObject("client_properties");
                if (clientProps != null) {
                    JSONObject capabilities = clientProps.getJSONObject("capabilities");
                    connFlat.put("clientPlatform", clientProps.getString("platform"));
                    connFlat.put("clientProduct", clientProps.getString("product"));
                    connFlat.put("clientVersion", clientProps.getString("version"));

                    if (capabilities != null) {
                        connFlat.put("authFailureClose", capabilities.getBooleanValue("authentication_failure_close"));
                        connFlat.put("basicNack", capabilities.getBooleanValue("basic.nack"));
                        connFlat.put("connectionBlocked", capabilities.getBooleanValue("connection.blocked"));
                        connFlat.put("consumerCancelNotify", capabilities.getBooleanValue("consumer_cancel_notify"));
                        connFlat.put("exchangeBindings", capabilities.getBooleanValue("exchange_exchange_bindings"));
                        connFlat.put("publisherConfirms", capabilities.getBooleanValue("publisher_confirms"));
                    }
                }

                // 连接时间
                connFlat.put("connectedAt", connJson.getLongValue("connected_at"));
                // 网络信息
                connFlat.put("host", connJson.getString("host"));
                connFlat.put("peerHost", connJson.getString("peer_host"));
                connFlat.put("peerPort", connJson.getInteger("peer_port"));
                connFlat.put("port", connJson.getInteger("port"));
                connFlat.put("name", connJson.getString("name"));
                connFlat.put("node", connJson.getString("node"));
                connFlat.put("protocol", connJson.getString("protocol"));
                connFlat.put("connectionsType", connJson.getString("type"));
                connFlat.put("user", connJson.getString("user"));
                connFlat.put("vhost", connJson.getString("vhost"));
                connFlat.put("state", connJson.getString("state"));

                // IO 指标
                connFlat.put("recvCnt", connJson.getLongValue("recv_cnt"));
                connFlat.put("recvOct", connJson.getLongValue("recv_oct"));
                connFlat.put("recvOctRate", connJson.getJSONObject("recv_oct_details").getDoubleValue("rate"));

                connFlat.put("sendCnt", connJson.getLongValue("send_cnt"));
                connFlat.put("sendOct", connJson.getLongValue("send_oct"));
                connFlat.put("sendOctRate", connJson.getJSONObject("send_oct_details").getDoubleValue("rate"));
                connFlat.put("sendPend", connJson.getInteger("send_pend"));

                // GC 信息
                JSONObject gc = connJson.getJSONObject("garbage_collection");
                if (gc != null) {
                    connFlat.put("gcFullSweepAfter", gc.getInteger("fullsweep_after"));
                    connFlat.put("gcMinHeapSize", gc.getInteger("min_heap_size"));
                    connFlat.put("gcMinorGcs", gc.getInteger("minor_gcs"));
                }

                // SSL/TLS
                connFlat.put("ssl", connJson.getBooleanValue("ssl"));

                processedConnectionsArray.add(connFlat);
            }
            outcomeMap.put("connections", processedConnectionsArray);
        }
    }

    private void processBindings(String bindings, JSONObject outcomeMap) {
        JSONArray bindingsArray = JSONArray.parseArray(bindings);
        if (CollUtil.isNotEmpty(bindingsArray)) {
            JSONArray processedBindingsArray = new JSONArray();
            for (int i = 0; i < bindingsArray.size(); i++) {
                JSONObject bindingJson = bindingsArray.getJSONObject(i);
                JSONObject bindingFlat = new JSONObject();

                // 基本信息
                bindingFlat.put("source", bindingJson.getString("source"));
                bindingFlat.put("vhost", bindingJson.getString("vhost"));
                bindingFlat.put("destination", bindingJson.getString("destination"));
                bindingFlat.put("destinationType", bindingJson.getString("destination_type"));
                bindingFlat.put("routingKey", bindingJson.getString("routing_key"));
                bindingFlat.put("propertiesKey", bindingJson.getString("properties_key"));

                // arguments 是一个对象
                bindingFlat.put("arguments", bindingJson.getJSONObject("arguments").toString());

                processedBindingsArray.add(bindingFlat);
            }
            outcomeMap.put("bindings", processedBindingsArray);
        }
    }

    private void processUsers(String users, JSONObject outcomeMap) {
        JSONArray usersArray = JSONArray.parseArray(users);
        if (CollUtil.isNotEmpty(usersArray)) {
            JSONArray processedUsersArray = new JSONArray();
            for (int i = 0; i < usersArray.size(); i++) {
                JSONObject userJson = usersArray.getJSONObject(i);
                JSONObject userFlat = new JSONObject();

                userFlat.put("name", userJson.getString("name"));
                userFlat.put("passwordHash", userJson.getString("password_hash"));
                userFlat.put("hashAlgorithm", userJson.getString("hashing_algorithm"));
                userFlat.put("tags", userJson.getString("tags"));

                // limits 是对象，可转为字符串存储
                userFlat.put("limits", userJson.getJSONObject("limits").toString());

                processedUsersArray.add(userFlat);
            }

            outcomeMap.put("users", processedUsersArray);
        }
    }

    private void processFeatureFlags(String featureFlags, JSONObject outcomeMap) {
        JSONArray featureFlagsArray = JSONArray.parseArray(featureFlags);
        if (CollUtil.isNotEmpty(featureFlagsArray)) {
            JSONArray processedFeatureFlagsArray = new JSONArray();
            for (int i = 0; i < featureFlagsArray.size(); i++) {
                JSONObject flagJson = featureFlagsArray.getJSONObject(i);
                JSONObject flagFlat = new JSONObject();

                // 提取字段
                flagFlat.put("name", flagJson.getString("name"));
                flagFlat.put("state", flagJson.getString("state"));
                flagFlat.put("stability", flagJson.getString("stability"));
                flagFlat.put("providedBy", flagJson.getString("provided_by"));
                flagFlat.put("description", flagJson.getString("desc"));
                flagFlat.put("docUrl", flagJson.getString("doc_url"));

                processedFeatureFlagsArray.add(flagFlat);
            }
            outcomeMap.put("featureFlags", processedFeatureFlagsArray);
        }
    }

        public void processPolicies(String policies, JSONObject outcomeMap) {
            JSONArray policiesArray = JSONArray.parseArray(policies);
            if (CollUtil.isNotEmpty(policiesArray)) {
                JSONArray processedPoliciesArray = new JSONArray();
                for (int i = 0; i < policiesArray.size(); i++) {
                    JSONObject policyJson = policiesArray.getJSONObject(i);
                    JSONObject policyFlat = new JSONObject();

                    policyFlat.put("name", policyJson.getString("name"));
                    policyFlat.put("vhost", policyJson.getString("vhost"));
                    policyFlat.put("pattern", policyJson.getString("pattern"));
                    policyFlat.put("applyTo", policyJson.getString("apply_to"));
                    policyFlat.put("definition", policyJson.getJSONObject("definition").toString());
                    policyFlat.put("priority", policyJson.getInteger("priority"));

                    processedPoliciesArray.add(policyFlat);
                }
                outcomeMap.put("policies", processedPoliciesArray);
            }
        }


}
