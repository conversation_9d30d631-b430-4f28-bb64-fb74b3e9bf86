package com.yuanqiao.insight.collection.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

//@Configuration
public class ScheduledConfig implements SchedulingConfigurer{

    @Autowired
    private TaskScheduler myThreadPoolTaskScheduler;

    @Override
    public void configureTasks(ScheduledTaskRegistrar scheduledTaskRegistrar) {
        //简单粗暴的方式直接指定
        //scheduledTaskRegistrar.setScheduler(Executors.newScheduledThreadPool(5));
        //也可以自定义的线程池，方便线程的使用与维护，这里不多说了
        scheduledTaskRegistrar.setTaskScheduler(myThreadPoolTaskScheduler);
    }
}
