package com.yuanqiao.insight.collection.collector.collector.impl.MatrixUDP;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.acore.utils.SpringContextUtil;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;

import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class MatrixUDPCodec {

    //最外层jsonObject
    JSONObject jsonObject = new JSONObject();
    //公共结果集
    Map<String, Object> commonMap = new HashMap<>();
    //矩阵设备地址
    String deviceAddress = "";

    public JSONObject dataCodecObject(Device device, List<ProertyMetadata> metadataList, String stcaKey) {
        //清空外层jsonObject和公共结果集commonMap
        jsonObject.clear();
        commonMap.clear();

        Map<String, Object> basicInfo = getBasicInfo(device.getConnectParam().get("ip"), Integer.valueOf(device.getConnectParam().get("port")), "(Q)");
        if (StringUtils.isNotEmpty((String) basicInfo.get("Equipment address"))) {
            deviceAddress = getStringBetweenS1S2ByRgex("1,", ",D", String.valueOf(basicInfo.get("Equipment address")));
            log.info("deviceAddress：" + deviceAddress);
            basicInfo.put("Equipment address", deviceAddress);
            commonMap.put("basicInfo", basicInfo);

            metadataList.forEach(metadata -> {
                if (StringUtils.isNotEmpty(metadata.getOid()) && StringUtils.isNotEmpty(metadata.getCode())) {
                    switch (metadata.getCode()) {
                        case "netConnection":
                            commonMap.put("netConnection", getNetConn(device.getConnectParam().get("ip"), Integer.valueOf(device.getConnectParam().get("port")), metadata.getOid().replace("*", deviceAddress)));
                            break;
                        case "relationOfChannel":
                            Map<String, Object> interfaceStatusMap = new HashMap<>();
                            Map<String, Object> videoTopologyMap = new HashMap<>();
                            Map<String, Object> workStatusMap = new HashMap<>();
                            getRelationOfChannel(device.getConnectParam().get("ip"), Integer.valueOf(device.getConnectParam().get("port")), metadata.getOid(), interfaceStatusMap, videoTopologyMap, workStatusMap);
                            commonMap.put("interfaceStatus", interfaceStatusMap);
                            commonMap.put("relationOfChannel", videoTopologyMap);
                            if (workStatusMap.size() > 0) {
                                commonMap.put("workStatus", "在线");
                            } else {
                                commonMap.put("workStatus", "离线");
                            }
                            break;
                        case "inputPortOccupancy":
                            // 物模型中的oid (2,*,#,D,C) ==> (2,0,1,D,C) / (2,1,1,D,C)
                            Map<String, String> inputPortOccupancyMap = new HashMap();
                            Map<String, String> outputPortOccupancyMap = new HashMap();
                            getPortOccupancy(Integer.valueOf(device.getConnectParam().get("portNum")), device.getConnectParam().get("ip"), Integer.valueOf(device.getConnectParam().get("port")), metadata.getOid().replace("*", "0").replace("#", deviceAddress), inputPortOccupancyMap, metadata.getOid().replace("*", "1").replace("#", deviceAddress), outputPortOccupancyMap);
                            commonMap.put("inputPortOccupancy", inputPortOccupancyMap);
                            commonMap.put("outputPortOccupancy", outputPortOccupancyMap);
                            break;
                    }
                }
            });

            metadataList.forEach(metadata -> {
                if (StringUtils.isNotEmpty(metadata.getDataType())) {
                    if (metadata.getDataType().equals("object")) {
                        jsonObject = getObjectCodec(metadata, stcaKey);
                    } else if (metadata.getDataType().equals("text")) {
                        jsonObject = getTextCodec(metadata, stcaKey);
                    }
                }
            });
        }

        return jsonObject;
    }

    /* ---------------------------------------------------------------------------------------------------------------- */

    private JSONObject getTextCodec(ProertyMetadata item, String stcaKey) {
        JSONObject jsonObject1 = new JSONObject();
        jsonObject1.put("name", item.getName());
        jsonObject1.put("type", item.getDataType());
        jsonObject1.put("unit", item.getUnit());
        if (commonMap.get(item.getCode()) != null) {
            jsonObject1.put("value", commonMap.get(item.getCode()));
        } else {
            jsonObject1.put("value", "- -");
        }

        String chart = item.getChart() + "";
        jsonObject1.put("display", makeLineDisplay(stcaKey, item.getCode(), jsonObject1.get("value"), chart));

        jsonObject.put(stcaKey + "_" + item.getCode(), jsonObject1);
        return jsonObject;
    }

    public JSONArray makeLineDisplay(String stcaKey, String code, Object value, String chart) {
        RedisTemplate redisTemplate = (RedisTemplate) SpringContextUtil.getBean("redisTemplate");
        JSONObject dataObject = JSONObject.parseObject(String.valueOf(redisTemplate.opsForValue().get(stcaKey + "_" + code)));

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (dataObject != null && !dataObject.isEmpty()) {
            return setLineDisplay(value, dataObject, chart);
        } else {
            JSONArray jsonArray = new JSONArray();
            JSONObject map1 = new JSONObject();
            map1.put("timestamp", sdf.format(System.currentTimeMillis()));
            map1.put("value", value);
            map1.put("type", chart);
            jsonArray.add(map1);
            return jsonArray;
        }

    }

    private JSONArray setLineDisplay(Object value, JSONObject jsonObject, String chart) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            JSONArray display = jsonObject.getJSONArray("display");
            JSONObject map1 = new JSONObject();
            if (display.size() >= 15) {
                display.remove(0);
                map1.put("timestamp", sdf.format(System.currentTimeMillis()));
                map1.put("value", value);
                map1.put("type", chart);
                display.add(map1);
            } else {
                map1.put("timestamp", sdf.format(System.currentTimeMillis()));
                map1.put("value", value);
                map1.put("type", chart);
                display.add(map1);
            }
            return display;
        } catch (Exception e) {
            JSONArray jsonArray = new JSONArray();
            JSONObject map1 = new JSONObject();
            map1.put("timestamp", sdf.format(System.currentTimeMillis()));
            map1.put("value", value);
            map1.put("type", chart);
            jsonArray.add(map1);
            return jsonArray;
        }

    }


    private JSONObject getObjectCodec(ProertyMetadata item, String stcaKey) {
        JSONObject jsonObject2 = new JSONObject();
        JSONObject jsonObject3 = new JSONObject();

        Map<String, String> infoMap = (Map<String, String>) commonMap.get(item.getCode());
        List<ProertyMetadata> childMetadataList = item.getProertyMetadataList();
        if (infoMap == null || infoMap.isEmpty() || childMetadataList == null || childMetadataList.isEmpty()) {
            jsonObject2.put("name", item.getName());
            jsonObject2.put("type", item.getDataType());
            jsonObject2.put("value", jsonObject3);
        } else {
            for (Map.Entry<String, String> entry : infoMap.entrySet()) {
                for (ProertyMetadata childMetadata : childMetadataList) {
                    if (entry.getKey().equals(childMetadata.getCode())) {
                        jsonObject3.put(entry.getKey(), entry.getValue());
                    }
                }
            }
            jsonObject2.put("name", item.getName());
            jsonObject2.put("type", item.getDataType());
            jsonObject2.put("value", jsonObject3);
            jsonObject2.put("display", makeTableDisplayForObject(jsonObject3, childMetadataList));
        }
        jsonObject.put(stcaKey + "_" + item.getCode(), jsonObject2);
        return jsonObject;
    }

    private JSONArray makeTableDisplayForObject(JSONObject jsonObjectParam, List<ProertyMetadata> childMetadataList) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        JSONArray jsonArrayOut = new JSONArray();
        JSONArray jsonArray = new JSONArray();
        JSONObject disObject = new JSONObject();
        disObject.put("type", "table");
        disObject.put("timestamp", sdf.format(System.currentTimeMillis()));

        for (ProertyMetadata obj : childMetadataList) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("name", "标识");
            jsonObject.put("value", obj.getName());
            jsonObject.put("unit", "");
            JSONObject jsonObject1 = new JSONObject();
            jsonObject1.put("name", "值");
            jsonObject1.put("value", jsonObjectParam.getString(obj.getCode()));
            jsonObject1.put("unit", obj.getUnit());
            JSONArray jsonArray1 = new JSONArray();
            jsonArray1.add(jsonObject);
            jsonArray1.add(jsonObject1);
            jsonArray.add(jsonArray1);
        }
        disObject.put("value", jsonArray);
        jsonArrayOut.add(disObject);
        return jsonArrayOut;
    }

    /* ---------------------------------------------------------------------------------------------------------------- */

    //矩阵基本信息
    private Map<String, Object> getBasicInfo(String ip, Integer port, String command) {
        Map<String, Object> basicInfoMap = new HashMap();
        String originalString = UDP__Client(ip, port, command);
        log.info("服务端响应(混合矩阵基本信息)：" + originalString);
        if (StringUtils.isNotEmpty(originalString)) {
            String dataString = originalString.substring(1, originalString.indexOf(")"));
            List<String> keyValueList = Arrays.asList(dataString.split("\n"));
            for (int i = 0; i < keyValueList.size(); i++) {
                String key = keyValueList.get(i).substring(0, keyValueList.get(i).indexOf(":"));
                String value = "--";
                if (keyValueList.get(i).contains("\r")) {
                    value = keyValueList.get(i).substring(keyValueList.get(i).indexOf(":") + 1, keyValueList.get(i).indexOf("\r"));
                } else {
                    value = keyValueList.get(i).substring(keyValueList.get(i).indexOf(":") + 1, keyValueList.get(i).length());
                }
                if (StringUtils.isNotEmpty(value)) {
                    basicInfoMap.put(key, value);
                } else {
                    basicInfoMap.put(key, "--");
                }
            }
        }
        return basicInfoMap;
    }

    //矩阵网络连接信息
    private Map<String, Object> getNetConn(String ip, Integer port, String command) {
        Map<String, Object> netConnMap = new HashMap();
        String originalString = UDP__Client(ip, port, command);
        log.info("服务端响应(混合矩阵网络参数信息)：" + originalString);
        String dataString = originalString.substring(1, originalString.indexOf(")"));
        List<String> dataList = Arrays.asList(dataString.split(","));

        String ipString = dataList.subList(0, 4).toString();
        ipString = ipString.substring(1, ipString.indexOf("]")).replace(",", ".");
        netConnMap.put("ip", ipString);

        String subMaskString = dataList.subList(4, 8).toString();
        subMaskString = subMaskString.substring(1, subMaskString.indexOf("]")).replace(",", ".");
        netConnMap.put("subMask", subMaskString);

        String gateWayString = dataList.subList(8, 12).toString();
        gateWayString = gateWayString.substring(1, gateWayString.indexOf("]")).replace(",", ".");
        netConnMap.put("gateWay", gateWayString);

        String macString = dataList.subList(12, 18).toString();
        macString = macString.substring(1, macString.indexOf("]")).replace(",", ":");
        netConnMap.put("mac", macString);

        return netConnMap;
    }

    //矩阵通道关系信息
    private void getRelationOfChannel(String ip, Integer port, String command, Map<String, Object> interfaceStatusMap, Map<String, Object> videoTopologyMap, Map<String, Object> workStatusMap) {
        String originalString = UDP__Client(ip, port, command);
        log.info("服务端响应(混合矩阵通道关系信息)：" + originalString);
        List<String> inputList = Arrays.asList(getStringBetweenS1S2ByRgex("\\(", ",D", originalString).split(","));
        log.info("inputList：" + inputList.toString());
        List<String> commandStrList = Arrays.asList(getStringBetweenS1S2ByRgex("\\(", ",O", command).split(","));
        log.info("commandStrList：" + commandStrList.toString());
        log.info("commandStrList.size()：" + commandStrList.size());
        for (int i = 1; i <= commandStrList.size(); i++) {
            log.info("i：" + i);
            String inputChannel = inputList.get(i - 1);
            log.info("inputChannel：" + inputChannel);
            if (StringUtils.isNotEmpty(inputChannel)) {
                videoTopologyMap.put(i + "", inputChannel);
                interfaceStatusMap.put(i + "", "正常");
                workStatusMap.put("workStatus", "在线");
            } else {
                videoTopologyMap.put(i + "", "- -");
                interfaceStatusMap.put(i + "", "异常");
            }
        }
    }

    //矩阵通道占用情况
    private void getPortOccupancy(Integer portNum, String ip, Integer port, String inputCommand, Map<String, String> inputPortOccupancyMap, String outputCommand, Map<String, String> outputPortOccupancyMap) {

        if (StringUtils.isNotEmpty(inputCommand)) {
            String inputOriginalRespond = UDP__Client(ip, port, inputCommand);
            log.info("服务端响应（混合矩阵输入通道占用情况）：" + inputOriginalRespond);
            List<String> inputOccupancyList = Arrays.asList(getStringBetweenS1S2ByRgex("\\(2,0,", ",1,D", inputOriginalRespond).trim().split(","));
            log.info("inputOccupancyList：" + inputOccupancyList);
            for (int i = 1; i <= portNum; i++) {
                log.info("i：" + i);
                String occupancy = inputOccupancyList.get(i - 1).trim();
                log.info("occupancy：" + occupancy);
                if (StringUtils.isNotEmpty(occupancy)) {
                    if (occupancy.equals("1")) {
                        inputPortOccupancyMap.put(i + "", "已占用");
                    } else if (occupancy.equals("0")) {
                        inputPortOccupancyMap.put(i + "", "未占用");
                    }
                } else {
                    inputPortOccupancyMap.put(i + "", "- -");
                }
            }
        }

        if (StringUtils.isNotEmpty(outputCommand)) {
            String outputOriginalRespond = UDP__Client(ip, port, outputCommand);
            log.info("服务端响应（混合矩阵输出通道占用情况）：" + outputOriginalRespond);
            List<String> outputOccupancyList = Arrays.asList(getStringBetweenS1S2ByRgex("\\(2,1,", ",1,D", outputOriginalRespond).trim().split(","));
            log.info("outputOccupancyList：" + outputOccupancyList);
            for (int i = 1; i <= portNum; i++) {
                log.info("i：" + i);
                String occupancy = outputOccupancyList.get(i - 1).trim();
                log.info("occupancy：" + occupancy);
                if (StringUtils.isNotEmpty(occupancy)) {
                    if (occupancy.equals("1")) {
                        outputPortOccupancyMap.put(i + "", "已占用");
                    } else if (occupancy.equals("0")) {
                        outputPortOccupancyMap.put(i + "", "未占用");
                    }
                } else {
                    outputPortOccupancyMap.put(i + "", "- -");
                }
            }
        }
    }

    /* ---------------------------------------------------------------------------------------------------------------- */

    //UDP客户端（统一抛异常）
    private String UDP__Client(String ip, int port, String command) {

        /**
         向服务端发送数据
         **/
        DatagramSocket socket = null;
        try {
            //1定义服务端的地址、端口号、数据
            InetAddress address = InetAddress.getByName(ip);
            int remotePort = port;
            byte[] data = command.getBytes();

            //2创建数据报，包含发送的数据信息
            DatagramPacket packet = new DatagramPacket(data, data.length, address, remotePort);

            //3创建DatagramSocket对象
            socket = new DatagramSocket();

            //4向服务端发送数据报
            socket.send(packet);
            String request = new String(data, 0, packet.getLength());
            log.info("客户端发送的请求:" + request);


            /**
             接收服务端响应的数据
             **/
            //1创建数据报，用于接收服务端响应的数据
            byte[] data2 = new byte[1024];
            DatagramPacket packet2 = new DatagramPacket(data2, data2.length);

            //2接收服务端响应的数据（10s内无响应设备离线）
            socket.setSoTimeout(10000);
            socket.receive(packet2);

            //3读取数据
            String reply = new String(data2, 0, packet2.getLength());
            log.info("来自服务端的响应:" + reply);

            return reply;

        } catch (Exception e) {
            log.error("MatrixUDPCodec -- UDPClient客户端异常！", e);
            e.printStackTrace();
            return "";
        } finally {
            //4关闭资源
            if (socket != null) {
                socket.close();
            }
        }
    }

    //获取两个字符串中间的内容
    private static String getStringBetweenS1S2ByRgex(String s1, String s2, String str) {
        Pattern pattern = Pattern.compile(s1 + "(.*?)" + s2);
        Matcher matcher = pattern.matcher(str);
        while (matcher.find()) {
            //System.out.println(matcher.group(1));
            return matcher.group(1);
        }
        return "";
    }

    /* ---------------------------------------------------------------------------------------------------------------- */


}
