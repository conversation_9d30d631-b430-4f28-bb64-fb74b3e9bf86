package com.yuanqiao.insight.collection.collector.utils;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.common.util.snmp.SNMPUtils;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;

import java.util.List;

public interface SNMPCodecInterface {

    /**
     * 定义SNMP拉模式采集解码器
     * @param snmpUtils
     * @param metadataList 物模型集合
     * @param stcaKey 状态容器key
     * @return
     */
    public JSONObject dataCodec(SNMPUtils snmpUtils, List<ProertyMetadata> metadataList, String stcaKey, Device device, SNMPCodecAndCollectUtils snmpCodecAndCollectUtils);
}
