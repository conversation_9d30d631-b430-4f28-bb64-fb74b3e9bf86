package com.yuanqiao.insight.collection.collector.collector.impl.FlowProcess;

import com.alibaba.fastjson.JSON;
import com.yuanqiao.insight.acore.collector.Collector;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.acore.scheduler.SchedulerManagerInter;
import com.yuanqiao.insight.acore.utils.SpringContextUtil;
import com.yuanqiao.insight.collection.collector.utils.DataBaseCodecUtils;
import com.yuanqiao.insight.service.flow.core.util.DBUtils;
import com.yuanqiao.insight.utils.SNMPMetadataUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.Map;

@Slf4j
@EnableAsync
public class DBFlowCollector implements Collector {


    private DataBaseCodecUtils dataBaseCodecUtils;
    private Device device;

    //初始化
    public void init(Device device, SchedulerManagerInter schedulerManager) {
        //初始化RedisTemplate
        RedisTemplate redisTemplate = (RedisTemplate) SpringContextUtil.getBean("redisTemplate");
        //初始化物模型工具类
        SNMPMetadataUtils metadataUtils = SpringContextUtil.getBean(SNMPMetadataUtils.class);
        String protocol = device.getProtocol();
        device.getConnectParam().put("protocol", protocol);
        Map<String, String> connectParam = device.getConnectParam();

        DBUtils dbUtils = DBUtils.iniDBUtils(JSON.parseObject(JSON.toJSONString(connectParam)));
        dataBaseCodecUtils = new DataBaseCodecUtils();
        dataBaseCodecUtils.init(dbUtils.getDbType(), dbUtils, metadataUtils, device, schedulerManager, redisTemplate);
        this.device = device;
    }

    @Async
    public void execute() {

        log.info("-------- JDBC_Flow " + device.getKey() + " 数据库监控任务执行了...");
        try {
            if (dataBaseCodecUtils == null) {
                throw new RuntimeException("dataBaseCodecUtils初始化失败！");
            }
            dataBaseCodecUtils.collectorMainLineForFlow();
        } catch (Exception e) {
            log.error("JDBC_Flow " + device.getKey() + " 监控任务执行异常！", e);
        }
    }
}
