package com.yuanqiao.insight.collection.collector.collector.impl.Snmp.SafeSnmp;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.collection.collector.utils.SNMPCodecAndCollectUtils;
import com.yuanqiao.insight.collection.collector.utils.SNMPCodecInterface;
import com.yuanqiao.insight.common.util.common.TimeUtils;
import com.yuanqiao.insight.common.util.snmp.SNMPUtils;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class SafeSnmpCodec_SG implements SNMPCodecInterface {

    // 操作系统类型
    String osType;

    public void setOsType(String osType) {
        this.osType = osType;
    }

    //最外层jsonObject
    JSONObject jsonObject = new JSONObject();
    //公共结果集
    JSONObject commonMap = new JSONObject();

    @Override
    public JSONObject dataCodec(SNMPUtils snmpUtils, List<ProertyMetadata> metadataList, String stcaKey, Device device, SNMPCodecAndCollectUtils snmpCodecAndCollectUtils) {
        //清空外层jsonObject和公共结果集commonMap
        jsonObject.clear();
        commonMap.clear();

        //静态属性
        HashMap<String, Object> staticInfoMap = new HashMap<>();

        staticInfoMap.put("switchName", getName(snmpUtils).get("switchName"));
        staticInfoMap.put("switchDesc", getSysDesc(snmpUtils).get("switchDesc"));
        staticInfoMap.put("portNum", getPortNum(snmpUtils).get("portNum"));
        staticInfoMap.put("runTime", getRunTime(snmpUtils).get("runTime"));
        commonMap.put("staticInfo", staticInfoMap);
        commonMap.put("sysUpTime", getSysUpTime(snmpUtils).get("sysUpTime"));

        commonMap.put("cpuRate", getCpuRate(snmpUtils).get("cpuRate"));
        commonMap.put("memRate", getMemRate(snmpUtils).get("memRate"));
        commonMap.put("netInOut", getNetInAndOut(snmpUtils).get("netInOut"));
        commonMap.put("portInfo", getPortInfoList(snmpUtils));

        //循环遍历当前山石网科负载均衡的物模型
        metadataList.forEach(item -> {
            if (StringUtils.isNotEmpty(item.getDataType())) {
                jsonObject = snmpCodecAndCollectUtils.switchByDataType(item, stcaKey, jsonObject, commonMap, snmpUtils);
            }
        });

        return jsonObject;
    }
    /**
     * 获取设备运行时长
     *
     * @param snmpUtils
     * @return
     */
    private Map<String, String> getSysUpTime(SNMPUtils snmpUtils) {
        HashMap<String, String> map = new HashMap<>();
        try {
            String sysUpTime = snmpUtils.getPDU("1.3.6.1.2.1.1.3.0");
            if (StringUtils.isNotEmpty(sysUpTime) && !sysUpTime.equalsIgnoreCase("noSuchObject")
                    && !sysUpTime.equalsIgnoreCase("noSuchInstance")) {
                // 去除字符串中的逗号
                sysUpTime = sysUpTime.replace(",", "");

                // 使用正则表达式匹配时间信息
                Pattern pattern = Pattern.compile("(?:(\\d+) days?)?(?:\\s*,?\\s*(\\d+):(\\d+)(?::(\\d+(?:\\.\\d+)?))?)?");
                Matcher matcher = pattern.matcher(sysUpTime);

                int days = 0, hours = 0, minutes = 0, seconds = 0;
                double milliseconds = 0;

                if (matcher.find()) {
                    if (matcher.group(1) != null) {
                        days = Integer.parseInt(matcher.group(1));
                    }
                    if (matcher.group(2) != null) {
                        hours = Integer.parseInt(matcher.group(2));
                    }
                    if (matcher.group(3) != null) {
                        minutes = Integer.parseInt(matcher.group(3));
                    }
                    if (matcher.group(4) != null) {
                        String[] secondsParts = matcher.group(4).split("\\.");
                        seconds = Integer.parseInt(secondsParts[0]);
                        if (secondsParts.length > 1) {
                            milliseconds = Double.parseDouble("0." + secondsParts[1]);
                        }
                    }
                }
                // 计算总毫秒数
                BigInteger totalMilliseconds = BigInteger.valueOf(days * 24 * 60 * 60 * 1000L)
                        .add(BigInteger.valueOf(hours * 60 * 60 * 1000L))
                        .add(BigInteger.valueOf(minutes * 60 * 1000L))
                        .add(BigInteger.valueOf((long) (seconds * 1000 + milliseconds * 1000)));
                sysUpTime = TimeUtils.getDistanceTime(totalMilliseconds);
                map.put("sysUpTime", sysUpTime);
            }
        } catch (Exception e) {
            log.error("获取运行时长出错", e);
            map.put("sysUpTime", "");
        }
        return map;
    }
    //获取山石网科负载均衡端口数
    private Map<String, Integer> getPortNum(SNMPUtils snmpUtils) {
        try {
            HashMap<String, Integer> map = new HashMap<>();
            // 山石网科负载均衡端口数
            String portNum_String = snmpUtils.getPDU("1.3.6.1.2.1.2.1.0");
            if (StringUtils.isEmpty(portNum_String)) {
                log.error("未获取到山石网科负载均衡端口数...");
                map.put("portNum", 0);
            } else {
                Integer portNum = Integer.parseInt(portNum_String);
                map.put("portNum", portNum);
            }
            return map;
        } catch (Exception e) {
            log.error("获取山石网科负载均衡端口数出错", e);
            HashMap<String, Integer> map = new HashMap<>();
            map.put("portNum", 0);
            return map;
        }
    }

    //获取山石网科负载均衡名称
    private Map<String, String> getName(SNMPUtils snmpUtils) {
        try {
            HashMap<String, String> map = new HashMap<>();
            // 山石网科负载均衡名称
            String ifName = snmpUtils.getPDU("1.3.6.1.2.1.1.5.0");
            if (ifName == null || "".equals(ifName.trim()) || "null".equalsIgnoreCase(ifName.trim())) {
                log.error("未获取到山石网科负载均衡名称...");
                map.put("switchName", "- -");
            } else {
                map.put("switchName", ifName);
            }
            return map;
        } catch (Exception e) {
            log.error("获取山石网科负载均衡名称出错", e);
            HashMap<String, String> map = new HashMap<>();
            map.put("switchName", "- -");
            return map;
        }
    }

    //获取山石网科负载均衡描述
    private Map<String, String> getSysDesc(SNMPUtils snmpUtils) {
        try {
            HashMap<String, String> map = new HashMap<>();
            // 山石网科负载均衡描述
            String sysDesc = snmpUtils.getPDU("1.3.6.1.2.1.1.1.0");
            if (sysDesc == null || "".equals(sysDesc.trim()) || "null".equalsIgnoreCase(sysDesc.trim())) {
                log.error("未获取到山石网科负载均衡描述...");
                map.put("switchDesc", "- -");
            }
            map.put("switchDesc", sysDesc);
            return map;
        } catch (Exception e) {
            log.error("获取山石网科负载均衡描述出错", e);
            HashMap<String, String> map = new HashMap<>();
            map.put("switchDesc", "- -");
            return map;
        }
    }

    //获取山石网科负载均衡运行时间
    private Map<String, String> getRunTime(SNMPUtils snmpUtils) {
        try {
            HashMap<String, String> map = new HashMap<>();
            // 山石网科负载均衡运行时间
            String sysRunTime = snmpUtils.getPDU("1.3.6.1.2.1.1.3.0");
            if (sysRunTime == null || "".equals(sysRunTime.trim()) || "null".equalsIgnoreCase(sysRunTime.trim())) {
                log.error("未获取到山石网科负载均衡运行时间...");
                map.put("runTime", "--");
            }
//            if (!"".equals(sysRunTime) && sysRunTime != null && !sysRunTime.isEmpty()) {
//                String[] categoryArr = sysRunTime.split(":");
//                String dayRep = categoryArr[0].replace("days", "天");
//                String seconds = categoryArr[2].substring(0, 2);
//                sysRunTime = dayRep + ":" + categoryArr[1] + ":" + seconds;
//            }
            map.put("runTime", sysRunTime);
            return map;
        } catch (Exception e) {
            log.error("获取山石网科负载均衡运行时间出错", e);
            HashMap<String, String> map = new HashMap<>();
            map.put("runTime", "--");
            return map;
        }
    }

    //获取cpu使用率
    private Map<String, Double> getCpuRate(SNMPUtils snmpUtils) {
        HashMap<String, Double> resMap = new HashMap<>();
        try {
            //初始化cpu平均使用率
            double cpuUtilization = 0.0;
            String cpuUsed = snmpUtils.getPDU("1.3.6.1.4.1.28557.2.2.1.3.0");
            log.error("山石网科负载均衡cpu使用率============="+cpuUsed);
            cpuUtilization = Double.valueOf(cpuUsed);
            if (cpuUtilization == 0) {
                log.error("未获取到山石网科负载均衡CPU使用率或当前CPU使用率为0！");
                resMap.put("cpuRate", 0.00);
            } else {
                resMap.put("cpuRate", Double.valueOf(String.format("%.2f", cpuUtilization)));
            }
            return resMap;
        } catch (Exception e) {
            log.error("获取山石网科负载均衡cpu平均使用率出错" + e.getMessage());
            resMap.put("cpuRate", 0.0);
            return resMap;
        }
    }

    //获取内存使用率
    private Map<String, Double> getMemRate(SNMPUtils snmpUtils) {
        HashMap<String, Double> resMap = new HashMap<>();
        try {
            // 内存使用率
            double memUtilization = 0;
            String memUse = snmpUtils.getPDU("1.3.6.1.4.1.28557.2.2.1.17.0");
            log.error("山石网科负载均衡内存使用率============="+memUse);
            memUtilization = Double.valueOf(memUse);
            if (memUtilization == 0) {
                log.error("未获取到山石网科负载均衡内存使用率或当前内存使用率为0！");
                resMap.put("memRate", 0.00);
            } else {
                resMap.put("memRate", Double.valueOf(String.format("%.2f", memUtilization)));
            }
            return resMap;
        } catch (Exception e) {
            log.error("获取山石网科负载均衡内存使用率出错" + e.getMessage());
            resMap.put("cpuRate", 0.0);
            return resMap;
        }
    }

    /**
     * 获取网络吞吐量
     *
     * @param snmpUtils
     * @return
     */
    private Map<String, Double> getNetInAndOut(SNMPUtils snmpUtils) {
        try {
            HashMap<String, Double> map = new HashMap<>();
            // 吞吐量
            double inAndOut = 0.0;
            Map<String, String> connNumMap = snmpUtils.getPDUWalk("1.3.6.1.2.1.2.2.1.1");
            //输入流量
            Map<String, String> inSpeedMap = snmpUtils.getPDUWalk("1.3.6.1.2.1.2.2.1.10");
            //输出流量
            Map<String, String> outSpeedMap = snmpUtils.getPDUWalk("1.3.6.1.2.1.2.2.1.16");
            for (Map.Entry<String, String> entrys : connNumMap.entrySet()) {
                String keyString = entrys.getKey().replace("1.3.6.1.2.1.2.2.1.1.", "");
                //输入/输出流量
                String inSpeed_String = inSpeedMap.get("1.3.6.1.2.1.2.2.1.10." + keyString);
                String OutSpeed_String = outSpeedMap.get("1.3.6.1.2.1.2.2.1.16." + keyString);
                double inAndOutSpeed = 0;
                if (inSpeed_String != null && inSpeed_String != "" && OutSpeed_String != null
                        && OutSpeed_String != "") {
                    inAndOutSpeed = Double.parseDouble(inSpeed_String) + Double.parseDouble(OutSpeed_String);
                }
                inAndOut += inAndOutSpeed;
            }
            if (Double.isNaN(inAndOut)) {
                log.error("未获取到山石网科负载均衡网络吞吐量...");
                map.put("netInOut", 0.0);
            } else {
                inAndOut = inAndOut / 1024 / 1024 / 1024;
                map.put("netInOut", Double.parseDouble(String.format("%.2f", inAndOut)));
            }
            return map;
        } catch (Exception e) {
            log.error("获取山石网科负载均衡网络吞吐量出错" + e.getMessage());
            HashMap<String, Double> map = new HashMap<>();
            map.put("netInOut", 0.0);
            return map;
        }
    }

    /**
     * 获取端口详情
     *
     * @param snmpUtils
     * @return
     */
    private List<Map<String, Object>> getPortInfoList(SNMPUtils snmpUtils) {

        try {
            List<Map<String, Object>> portInfoList = new ArrayList<Map<String, Object>>();
            // 接口索引 主键
            Map<String, String> connNumMap = snmpUtils.getPDUWalk("1.3.6.1.2.1.2.2.1.1");
            //端口状态
            Map<String, String> portStatusMap = snmpUtils.getPDUWalk("1.3.6.1.2.1.2.2.1.8");
            //端口描述
            Map<String, String> ifDescrProtosMap = snmpUtils.getPDUWalk("1.3.6.1.2.1.2.2.1.2");
            // 带宽
            Map<String, String> ifSpeedProtosMap = snmpUtils.getPDUWalk("1.3.6.1.2.1.2.2.1.5");
            //端口类型
            Map<String, String> portTypeMap = snmpUtils.getPDUWalk("1.3.6.1.2.1.2.2.1.3");
            //接收错误数据包
            Map<String, String> ifInErrorsMap = snmpUtils.getPDUWalk("1.3.6.1.2.1.2.2.1.14");
            //发送的错误数据包
            Map<String, String> ifOutErrorsProtosMap = snmpUtils.getPDUWalk("1.3.6.1.2.1.2.2.1.20");
            //输入流量
            Map<String, String> inSpeedMap = snmpUtils.getPDUWalk("1.3.6.1.2.1.2.2.1.10");
            //输出流量
            Map<String, String> outSpeedMap = snmpUtils.getPDUWalk("1.3.6.1.2.1.2.2.1.16");
            //输入丢失错误包数
            Map<String, String> inLossPackageMap = snmpUtils.getPDUWalk("1.3.6.1.2.1.2.2.1.13");
            //输出丢失错误包数
            Map<String, String> outLossPackageMap = snmpUtils.getPDUWalk("1.3.6.1.2.1.2.2.1.19");
            for (Map.Entry<String, String> entrys : connNumMap.entrySet()) {
                String keyString = entrys.getKey().replace("1.3.6.1.2.1.2.2.1.1.", "");
                Map<String, Object> res = new HashMap<String, Object>();
                //端口类型
                res.put("portType", portTypeMap.get("1.3.6.1.2.1.2.2.1.3." + keyString));
                //端口状态
                String str = portStatusMap.get("1.3.6.1.2.1.2.2.1.8." + keyString);
                if ("1".equals(str.trim())) {
                    res.put("portStatus", "连接");
                } else if ("2".equals(str.trim())) {
                    res.put("portStatus", "关闭");
                } else {
                    res.put("portStatus", "其他");
                }
                // 带宽
                String bandWidth_string = ifSpeedProtosMap.get("1.3.6.1.2.1.2.2.1.5." + keyString);
                if (StringUtils.isNotEmpty(bandWidth_string)) {
                    Double bandWidth = Double.parseDouble(bandWidth_string);
                    res.put("bandWidth", String.format("%.2f", bandWidth / 1024 / 1024 / 1024));
                }

                //输入/输出流量
                String inSpeed = inSpeedMap.get("1.3.6.1.2.1.2.2.1.10." + keyString);
                if (StringUtils.isNotEmpty(inSpeed)) {
                    Double inputFlow = Double.parseDouble(inSpeed);
                    res.put("inputFlow", String.format("%.2f", inputFlow * 1024 / 1024 / 1024));
                }
                String outSpeed = outSpeedMap.get("1.3.6.1.2.1.2.2.1.16." + keyString);
                if (StringUtils.isNotEmpty(outSpeed)) {
                    Double outputFlow = Double.parseDouble(outSpeed);
                    res.put("outputFlow", String.format("%.2f", outputFlow * 1024 / 1024 / 1024));
                }

                //输入/输出错误包数
                String inErrorPackageString = ifInErrorsMap.get("1.3.6.1.2.1.2.2.1.14." + keyString);
                if (StringUtils.isNotEmpty(inErrorPackageString)) {
                    Double inError = Double.parseDouble(inErrorPackageString);
                    res.put("inErrorPackage", String.format("%.2f", inError));
                }
                String outErrorPackageString = ifOutErrorsProtosMap.get("1.3.6.1.2.1.2.2.1.20." + keyString);
                if (StringUtils.isNotEmpty(outErrorPackageString)) {
                    Double inError = Double.parseDouble(outErrorPackageString);
                    res.put("outErrorPackage", String.format("%.2f", inError));
                }

                //输入/输出丢失错误包数
                String inLossPackageString = inLossPackageMap.get("1.3.6.1.2.1.2.2.1.13." + keyString);
                if (StringUtils.isNotEmpty(inLossPackageString)) {
                    Double inLossPackage = Double.parseDouble(inLossPackageString);
                    res.put("inLossPackage", String.format("%.2f", inLossPackage));
                }
                String outLossPackageString = outLossPackageMap.get("1.3.6.1.2.1.2.2.1.19." + keyString);
                if (StringUtils.isNotEmpty(outLossPackageString)) {
                    Double outLossPackage = Double.parseDouble(outLossPackageString);
                    res.put("outLossPackage", String.format("%.2f", outLossPackage));
                }
                // 接口描述
                res.put("portDesc", ifDescrProtosMap.get("1.3.6.1.2.1.2.2.1.2." + keyString));
                //索引
                res.put("index", keyString);

                portInfoList.add(res);
            }
            return portInfoList;
        } catch (Exception e) {
            log.error("获取端口信息出错", e);
            return null;
        }
    }
}
