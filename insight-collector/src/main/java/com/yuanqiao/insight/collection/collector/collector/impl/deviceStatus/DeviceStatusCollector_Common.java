package com.yuanqiao.insight.collection.collector.collector.impl.deviceStatus;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.collector.Collector;
import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.acore.scheduler.SchedulerManagerInter;
import com.yuanqiao.insight.acore.utils.SpringContextUtil;
import com.yuanqiao.insight.service.collector.utils.DataPersistenceStorageUtils;
import com.yuanqiao.insight.collection.collector.utils.SNMPCodecAndCollectUtils;
import com.yuanqiao.insight.collection.config.InfluxDBProperties;
import com.yuanqiao.insight.common.util.common.RedisUtils;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import com.yuanqiao.insight.utils.SNMPMetadataUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.influxdb.InfluxDB;
import org.influxdb.InfluxDBFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 设备状态特殊化处理适配器
 */
@Slf4j
@EnableAsync
public class DeviceStatusCollector_Common implements Collector {
    private Device device;
    private SchedulerManagerInter schedulerManager;
    private InfluxDB influxDB;
    private SNMPMetadataUtils metadataUtils;
    private RedisUtils redisUtils;
    private SNMPCodecAndCollectUtils snmpCodecAndCollectUtils;

    @Value(value = "${influxdb.username}")
    private String username = InfluxDBProperties.influxdbUsername;//用户名
    @Value("${influxdb.password}")
    private String password = InfluxDBProperties.influxdbPassword;//密码
    @Value("${influxdb.openurl}")
    private String openurl = InfluxDBProperties.influxdbOpenurl;//连接地址
    @Value("${influxdb.database}")
    private String database = InfluxDBProperties.influxdbDatabase;//数据库名称

    //初始化
    public void init(Device device, SchedulerManagerInter schedulerManager) {
        //绑定设备
        this.device = device;
        //绑定调度管理器
        this.schedulerManager = schedulerManager;

        //初始化influxDB连接
        influxDB = InfluxDBFactory.connect(openurl, username, password);

        //初始化metadataUtils
        metadataUtils = (SNMPMetadataUtils) SpringContextUtil.getBean("SNMPMetadataUtils");

        //初始化redisUtils
        redisUtils = (RedisUtils) SpringContextUtil.getBean("redisUtils");

        //初始化snmpCodecAndCollectUtils
        snmpCodecAndCollectUtils = (SNMPCodecAndCollectUtils) SpringContextUtil.getBean("snmpCodecAndCollectUtils");

    }

    //执行方法
    @Async
    public void execute() {
        //初始化监控数据持久化存储工具类
        DataPersistenceStorageUtils storageUtils = (DataPersistenceStorageUtils) SpringContextUtil.getBean("dataPersistenceStorageUtils");
        SimpleDateFormat time_sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timestampFormated = time_sdf.format(new Date());
        log.info("-------- SPEC设备 " + device.getKey() + " 状态监控任务开始执行...");
        //初始化最外层jsonObject
        JSONObject jsonObject = new JSONObject();
        //公共结果Map
        JSONObject commonMap = new JSONObject();

        List<ProertyMetadata> metadataList = metadataUtils.setMetadata2(device.getKey(), device.getProtocol());

        log.debug("开始获取并解析设备 " + device.getKey() + " 的数据...");
        //读取redis中的状态键
        String onstKey = "onst:" + device.getKey();
        if (redisUtils.hasKey(onstKey)) {
            Map<Object, Object> onstMap = redisUtils.hmget(onstKey);
            if ((int) onstMap.get("st") == 1) {
                commonMap.put("deviceStatus", "在线");
            } else if ((int) onstMap.get("st") == 2) {
                commonMap.put("deviceStatus", "告警");
            } else {
                commonMap.put("deviceStatus", "离线");
            }
        } else {
            commonMap.put("deviceStatus", "离线");
        }

        //根据不同数据格式，组装监控数据
        for (ProertyMetadata item : metadataList) {
            if (StringUtils.isNotEmpty(item.getDataType())) {
                jsonObject = snmpCodecAndCollectUtils.switchByDataType(item, "stca:" + device.getKey(), jsonObject, commonMap, null);
            }
        }
        log.debug("设备 " + device.getKey() + " 解析出的数据 ：" + jsonObject);

        //设置状态容器、发布设备数据事件
        log.debug("将设备 " + device.getKey() + " 的监控数据存入Redis_stca...");
        schedulerManager.setStatusCache(device, jsonObject);

        //监控数据持久化存储
        log.debug("将设备 " + device.getKey() + " 的监控数据存入Elasticsearch...");
        storageUtils.dataStore(device, jsonObject, metadataList,timestampFormated);

        log.debug("********** END ********** " + "SPEC 设备 " + device.getKey() + " 监控任务执行完毕！！！");
    }

}
