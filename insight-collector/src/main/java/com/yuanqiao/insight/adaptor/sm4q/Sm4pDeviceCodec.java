package com.yuanqiao.insight.adaptor.sm4q;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.adapter.inter.DeviceCodec;
import com.yuanqiao.insight.acore.matadata.function.Calculator;
import com.yuanqiao.insight.acore.utils.SpringContextUtil;
import com.yuanqiao.insight.service.product.entity.ProertyMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Component
public class Sm4pDeviceCodec implements DeviceCodec {
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public JSONObject decode(String prefix, String message, JSONObject metadataObject) {
        return getParameter(prefix, message);
    }

    public JSONObject getParameter(String prefix, String message) {
        JSONObject devMapObject = JSONObject.parseObject(message);

/*        log.info("当前设备推送过来的数据信息Index为： "+devMapObject.getString("index"));
        if (devMapObject.getString("index").equals("1000")) {
            JSONObject data = new JSONObject();
            data.put("deviceKey", devMapObject.getString("ip"));
            data.put("type", devMapObject.getString("type"));
            eventBus = SpringContextUtil.getBean(EventBus.class);
            eventBus.publish(EventFactory.getUDPHeartBeat(data));
            log.info("data： " + data);
        }*/

        JSONArray valueList = devMapObject.getJSONArray("value");
        String ip = devMapObject.getString("ip");
        String key = "dema:" + prefix + ip;
        String key2 = "stca:" + prefix + ip;

        Map<String, Object> devMap1 = new HashMap<>();
        if (Boolean.TRUE.equals(redisTemplate.hasKey(key2))) {
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(key2);
            devMap1 = Optional.of(entries)
                    .map(x -> x.entrySet().stream()
                            .collect(Collectors.toMap(m -> m.getKey() + "", Map.Entry::getValue)))
                    .orElse(new HashMap<>());
        }
        try {
            List<Object> range = redisTemplate.opsForList().range(key, 0, -1);
            List<ProertyMetadata> proertyMetadataList = Optional.ofNullable(range)
                    .map(JSON::toJSONString)
                    .map(x -> JSON.parseArray(x, ProertyMetadata.class))
                    .orElse(new ArrayList<>());
            for (ProertyMetadata p : proertyMetadataList) {
                String index = p.getIndexes();
                JSONArray valueLists = new JSONArray();
                if (StringUtils.isNotEmpty(index)) {
                    if (index.equals(devMapObject.getString("index"))) {
                        for (Object o : valueList) {
                            JSONObject valueMap = new JSONObject();
                            valueLists.add(valueMap);
                            JSONObject newMap = new JSONObject();
                            devMap1.put(p.getCode(), newMap);
                            newMap.put("name", p.getName());
                            newMap.put("type", p.getDataType());
                            newMap.put("value", valueLists);
                            for (ProertyMetadata childProperty : p.getProertyMetadataList()) {  //物模型子模型
                                JSONObject jsonObject = new JSONObject();
                                String code = childProperty.getCode();
                                String name = childProperty.getName();
                                String type = childProperty.getDataType();
                                //通过code获取设备传过来的数据中取value
                                Map<String, Object> map = (Map) o;
                                jsonObject.put("name", name);
                                jsonObject.put("type", type);
                                String code1 = String.valueOf(map.get(code));
                                jsonObject.put("unit", childProperty.getUnit());
                                jsonObject.put("value", getType(type, code1));
                                valueMap.put(code, jsonObject);
                                if (StringUtils.isNotEmpty(childProperty.getFuncName())) {
                                    Calculator calculator = (Calculator) SpringContextUtil.getBean(childProperty.getFuncName());
                                    String[] para = getPara(childProperty.getFuncParam());
                                    Object execute = calculator.execute(valueMap, para);
                                    jsonObject.put("value", execute);
                                }
                            }
                            newMap.put("display", getArrayList(valueLists));
                        }
                    }
                } else {
                    JSONObject newMap = new JSONObject();
                    devMap1.put(p.getCode(), newMap);
                    newMap.put("name", p.getName());
                    newMap.put("type", p.getDataType());
                    newMap.put("unit", p.getUnit());
                    newMap.put("value", 0);
                    Object execute = null;
                    if (StringUtils.isNotEmpty(p.getFuncName())) {
                        Calculator calculator = (Calculator) SpringContextUtil.getBean(p.getFuncName());
                        String[] para = getPara(p.getFuncParam());
                        execute = calculator.execute(devMap1, para);
                        newMap.put("value", execute);
                    }
                    newMap.put("display", getlist(key2, p.getCode(), execute, p.getChart() + ""));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info("com.yuanqiao.insight.adaptor.sm4q.Sm4pDeviceCodec.getParameter" + e.getMessage());

        }
        return new JSONObject(devMap1);
    }

    /**
     * 类型转换
     *
     * @param type
     * @param code
     * @return
     */
    public Object getType(String type, String code) {

        try {
            if ("int".equals(type)) {
                double v = Double.parseDouble(code);
                int i = new Double(v).intValue();
                return i;
            } else if ("long".equals(type)) {
                return Long.parseLong(code);
            } else if ("float".equals(type)) {
                return Float.parseFloat(type);
            } else if ("double".equals(type)) {
                return Double.parseDouble(code);
            } else if ("text".equals(type)) {
                return code;
            } else if ("bool".equals(type)) {
                return Boolean.parseBoolean(code);
            } else {
                return code;
            }
        } catch (Exception e) {
            log.info("com.yuanqiao.insight.adaptor.sm4q.Sm4pDeviceCodec.getType" + e.getMessage());

        }
        return 0;
    }


    public String[] getPara(String para) {

        return para.split(",");
    }


    public JSONArray getlist(String key, String code, Object value, String chart) {

        Map<Object, Object> entries = redisTemplate.opsForHash().entries(key);
        Map<String, Object> map = Optional.of(entries)
                .map(x -> x.entrySet().stream()
                        .collect(Collectors.toMap(m -> m.getKey() + "", Map.Entry::getValue)))
                .orElse(new HashMap<>());

        if (map.size() > 0) {
            JSONObject jsonObject = (JSONObject) map.get(code);
            return setDisplay(value, jsonObject, chart);
        } else {
            JSONArray jsonArray = new JSONArray();
            JSONObject map1 = new JSONObject();
            map1.put("timestamp", sdf.format(System.currentTimeMillis()));
            map1.put("value", value);
            map1.put("type", chart);
            jsonArray.add(map1);
            return jsonArray;
        }

    }


    public JSONArray setDisplay(Object value, JSONObject jsonObject, String chart) {

        try {
            JSONArray display = jsonObject.getJSONArray("display");
            JSONObject map1 = new JSONObject();
            if (display.size() >= 15) {
                display.remove(0);
                map1.put("timestamp", sdf.format(System.currentTimeMillis()));
                map1.put("value", value);
                map1.put("type", chart);
                display.add(map1);
            } else {
                map1.put("timestamp", sdf.format(System.currentTimeMillis()));
                map1.put("value", value);
                map1.put("type", chart);
                display.add(map1);
            }
            return display;
        } catch (Exception e) {
            JSONArray jsonArray = new JSONArray();
            JSONObject map1 = new JSONObject();
            map1.put("timestamp", sdf.format(System.currentTimeMillis()));
            map1.put("value", value);
            map1.put("type", chart);
            jsonArray.add(map1);
            return jsonArray;
        }

    }


    public JSONArray getArrayList(JSONArray list) {

        JSONArray jsonArrayOut = new JSONArray();
        JSONObject disObject = new JSONObject();
        disObject.put("timestamp", sdf.format(System.currentTimeMillis()));
        disObject.put("type", "table");
        JSONArray jsonArray = new JSONArray();
        for (int i = 0; i < list.size(); i++) {
            Map<String, Object> o = (Map<String, Object>) list.get(i);
            Collection<Object> values = o.values();
            String s = JSONArray.toJSONString(values);  //复制一份出来
            JSONArray array = JSONArray.parseArray(s);
            jsonArray.add(array);
        }
        disObject.put("value", jsonArray);
        jsonArrayOut.add(disObject);
        return jsonArrayOut;
    }


}
