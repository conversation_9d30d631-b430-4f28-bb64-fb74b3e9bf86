package com.yuanqiao.insight.adaptor.telegraf;

import com.yuanqiao.insight.acore.adapter.NormalDeviceAdapter;
import com.yuanqiao.insight.adaptor.factory.DeviceAdapterSupport;
import org.springframework.stereotype.Component;

/**
 * telegraf-适配器
 */
@Component
public class TelegrafDeviceAdapter extends NormalDeviceAdapter<TelegrafDeviceCodec,TelegrafDeviceService> {

    @Override
    public boolean support(String support) {
        super.setPREFIX(support);
        return DeviceAdapterSupport.TELEGRAF.getPrefix().equals(support);
    }
}
