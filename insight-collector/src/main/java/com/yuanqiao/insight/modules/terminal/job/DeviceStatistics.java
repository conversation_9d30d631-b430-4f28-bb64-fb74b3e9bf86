package com.yuanqiao.insight.modules.terminal.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.yuanqiao.insight.common.constant.CommonConstant;
import com.yuanqiao.insight.common.util.common.LocalCacheUtils;
import com.yuanqiao.insight.service.device.entity.DeviceInfo;
import com.yuanqiao.insight.service.momgDeviceStatis.entity.MomgDeviceStatis;
import com.yuanqiao.insight.service.momgDeviceStatis.entity.Time;
import com.yuanqiao.insight.service.momgDeviceStatis.service.IMomgDeviceStatisService;
import com.yuanqiao.insight.service.momgDeviceStatis.utils.StatisticsUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/11/30
 */
@Slf4j
@Component
public class DeviceStatistics {

    @Autowired
    private IMomgDeviceStatisService momgDeviceStatisServiceImpl;
    private String timePoint;
    private long staticsTime;
    private List<DeviceInfo> deviceInfoList;

    private static final LocalCacheUtils cacheUtils = LocalCacheUtils.getInstance();

    public void initData(List<DeviceInfo> deviceInfoList, String timePoint, long staticsTime) {
        this.deviceInfoList = deviceInfoList;
        this.timePoint = timePoint;
        this.staticsTime = staticsTime;
    }

    @Transactional(rollbackFor = Exception.class)
    public void execute() {
        List<MomgDeviceStatis> deviceStatisList = new ArrayList<>();
        String terminalProductCategory = (String) Optional.ofNullable(cacheUtils.getValueByKey(String.format("%sdeviceStatisticSetting_terminalProductCategory", CommonConstant.DATA_DICT_KEY))).orElse("");
        String exclusionProductCategory = (String) Optional.ofNullable(cacheUtils.getValueByKey(String.format("%sdeviceStatisticSetting_exclusionProductCategory", CommonConstant.DATA_DICT_KEY))).orElse("");

        for (DeviceInfo device : deviceInfoList) {
            String deviceId = device.getId();
            String deviceCode = device.getDeviceCode();
            final String categoryCode = device.getCategoryCode();
            List<Time> times;
            if (exclusionProductCategory.contains(categoryCode)) {
                continue;
            }
            try {
                if (terminalProductCategory.contains(categoryCode)) {
                    times = StatisticsUtils.getDeviceDateList("metrics-terminal-udp"
                            , "dynamic"
                            , deviceCode
                            , timePoint);
                } else {
                /*times = StatisticsUtils.getDeviceDateList("server_heartbeat", ""
                        , deviceCode
                        , timePoint);
                List<Time> t2 = StatisticsUtils.getDeviceDateList(String.format("metrics-%s-snmp", StringUtils.lowerCase(categoryCode))
                        , "sysUpTime"
                        , deviceCode
                        , timePoint);

                StatisticsUtils.mergeList(times, t2);*/
                    times = StatisticsUtils.getDeviceTime(deviceCode, categoryCode, timePoint);
                }
                MomgDeviceStatis deviceStatis = new MomgDeviceStatis();
                if (CollUtil.isEmpty(times)) {
                    deviceStatis.setCreateTime(DateUtil.now());
                    deviceStatis.setDeviceType("关机");
                } else {
                    deviceStatis = StatisticsUtils.createDeviceStatistic(times, staticsTime);
                }
                deviceStatis.setDeviceId(deviceId);
                deviceStatis.setDeviceCode(deviceCode);
                deviceStatis.setDeviceCategory(categoryCode);
                deviceStatis.setCreateTime(timePoint);
                deviceStatisList.add(deviceStatis);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        momgDeviceStatisServiceImpl.saveDeviceStatis(timePoint, deviceStatisList);
    }

    public static void main(String[] args) {
        /*TerminalStatistics terminalStatistics = new TerminalStatistics();
        List<TerminalDevice> terminalDevice = new ArrayList<>();
        TerminalDevice terminalDevice1 = new TerminalDevice();
        terminalDevice1.setDeviceId("1407140155450019842");
        terminalDevice1.setDeviceCode("00:0c:29:d4:98:36");
        terminalDevice.add(terminalDevice1);
        TerminalDevice terminalDevice2 = new TerminalDevice();
        terminalDevice2.setDeviceId("1407140155450019842");
        terminalDevice2.setDeviceCode("00:23:9e:07:19:db");
        terminalDevice.add(terminalDevice2);
        terminalStatistics.initData(terminalDevice, "2023-11-30");
        List<Time> times = new ArrayList<>();
        times.add(new Time(DateUtil.parse("2023-11-30 6:32:23"), TimeUtils.getMillisecond("7小时 27分 2秒")));
        times.add(new Time(DateUtil.parse("2023-11-30 6:32:52"), TimeUtils.getMillisecond("7小时 28分 24秒")));
        times.add(new Time(DateUtil.parse("2023-11-30 6:33:56"), TimeUtils.getMillisecond("7小时 29分 23秒")));
        times.add(new Time(DateUtil.parse("2023-11-30 6:34:49"), TimeUtils.getMillisecond("7小时 30分 23秒")));

        times.add(new Time(DateUtil.parse("2023-11-30 17:32:23"), TimeUtils.getMillisecond("2小时 27分 2秒")));
        times.add(new Time(DateUtil.parse("2023-11-30 17:32:52"), TimeUtils.getMillisecond("2小时 28分 24秒")));
        times.add(new Time(DateUtil.parse("2023-11-30 17:33:56"), TimeUtils.getMillisecond("2小时 29分 23秒")));
        times.add(new Time(DateUtil.parse("2023-11-30 17:34:49"), TimeUtils.getMillisecond("2小时 30分 23秒")));

        times.add(new Time(DateUtil.parse("2023-11-30 21:34:49"), TimeUtils.getMillisecond("3小时 30分 23秒")));
        times.add(new Time(DateUtil.parse("2023-11-30 21:35:28"), TimeUtils.getMillisecond("3小时 31分 1秒")));

        MomgDeviceStatis momgDeviceStatis = terminalStatistics.createDeviceStatistic(times);
*/
       /* TerminalStatistics terminalStatistics = new TerminalStatistics();
        FileReader fileReader = new FileReader("D:\\YuanQiao\\db3.json");
        String result = fileReader.readString();
        //System.out.println(result);
        JSONArray jsonArray = JSON.parseArray(result);
        List<Time> times = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            final JSONObject jsonObject = jsonArray.getJSONObject(i);
            Time time = new Time();
            time.heartbeatDate = jsonObject.getDate("createTime");
            time.sysUpTime = TimeUtils.getMillisecond(jsonObject.getString("sysUptime"));
            times.add(time);
        }
        MomgDeviceStatis momgDeviceStatis = terminalStatistics.createDeviceStatistic(times);
        System.out.println(JSON.toJSONString(momgDeviceStatis));
        System.out.println(TimeUtils.getDistanceTime(momgDeviceStatis.getOnTotal()).replace(" ", ""));*/

    }

}
