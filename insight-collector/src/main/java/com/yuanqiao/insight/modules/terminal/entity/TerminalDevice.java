package com.yuanqiao.insight.modules.terminal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 终端设备表
 * @Author: jeecg-boot
 * @Date:   2021-05-10
 * @Version: V1.0
 */
@Data
@TableName("momg_terminal")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="momg_terminal对象", description="终端设备表")
public class TerminalDevice implements Serializable {
    private static final long serialVersionUID = 1L;
    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    /**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
    /**资产id*/
//	@Excel(name = "资产id", width = 15)
    @ApiModelProperty(value = "资产id")
    private String assetsId;
    /**名称*/
    @Excel(name = "终端名称", width = 30)
    @ApiModelProperty(value = "名称")
    private String name;
    /*sn号*/
    @Excel(name = "SN",width = 15)
    private String sn;
    /**设备ip*/
    @Excel(name = "终端IP", width = 15)
    @ApiModelProperty(value = "设备ip")
    private String ip;
    /**mac地址*/
    @Excel(name = "MAC地址（XX-XX-XX-XX-XX-XX）", width = 20)
    @ApiModelProperty(value = "mac地址")
    private String macAddr;
    /**cpu平台架构*/
    @Excel(name = "架构", width = 15)
    @ApiModelProperty(value = "cpu平台架构")
    private String cpuArch;
    /**是否启用，0：未激活，1：已激活*/
//    @Excel(name = "激活状态，0：未激活，1：已激活", width = 15)
    @Excel(name = "激活状态", width = 15,dicCode = "enable")
    @ApiModelProperty(value = "是否启用，0：未激活，1：已激活")
    private Integer enable;
    /**使用人*/
    @Excel(name = "使用者", width = 15)
    @ApiModelProperty(value = "使用人")
    private String username;
    /**单位ID*/
    @Excel(name = "单位", width = 15,dictTable ="sys_depart",dicText = "depart_name",dicCode = "id")
    @ApiModelProperty(value = "单位ID")
    private String deptId;
    //    @Excel(name = "单位", width = 15)
    @TableField(exist = false)
    private java.lang.String deptName;
    /**描述*/
//	@Excel(name = "描述", width = 15)
    @ApiModelProperty(value = "描述")
    private String description;
    /**删除标识1:删除*/
//	@Excel(name = "删除标识1:删除", width = 15)
    @ApiModelProperty(value = "删除标识1:删除")
    private Integer delflag;


    /**物理位置ID*/
//	@Excel(name = "物理位置ID", width = 15)
    @ApiModelProperty(value = "物理位置ID")
    private String positionId;
    /**位置标签*/
//	@Excel(name = "位置标签", width = 15)
    @ApiModelProperty(value = "位置标签")
    private String positionTag;

    // 终端类型
    //0-服务器,6-桌面机,8-笔记本
    @Excel(name = "终端类型", width = 15,dicCode = "resources_type")
    private Integer terminalType;
    @TableField(exist = false)
    private String terminalTypeName;

    /**终端所有的mac和ip的关系*/
    private String macIp; //添加或者注册的时候把macIp添加上,
    /**cpu类型*/
//	@Excel(name = "cpu类型", width = 15)
    @ApiModelProperty(value = "cpu类型")
    private String cpuType;
    /**操作系统类型*/
//	@Excel(name = "操作系统类型", width = 15)
    @ApiModelProperty(value = "操作系统类型")
    private String osType;
    /**终端唯一标识*/
//    @Excel(name = "终端唯一标识", width = 15)
    @ApiModelProperty(value = "终端唯一标识")
    private String uniqueCode;
    @TableField(exist = false)
    private java.lang.String assetsName;

    @TableField(exist = false)
    private java.lang.String deptAddress;


    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;


    private String phone,remarks,userDepartment,administrator,adminDepartment,address,addrDetail;

    private Integer addrId;

    @TableField(exist = false)
    private String mac;

    /**状态*/
//    @Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态")
    private Integer status;

    @TableField(exist = false)
    private String grafanaUrl;

    @TableField(exist = false)
    private String deviceId;

    @TableField(exist = false)
    private String deviceCode;
}
