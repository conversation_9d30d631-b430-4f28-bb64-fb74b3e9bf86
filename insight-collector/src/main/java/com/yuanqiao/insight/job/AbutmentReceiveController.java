package com.yuanqiao.insight.job;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/abutmentListener")
public class AbutmentReceiveController {

    @Autowired
    private AbutmentReceiveService abutmentService;

    /**
     * 第三方系统对接（设备、告警）
     *
     * @return
     */
    @PostMapping("/dataAbutment")
    public Result<?> dataAbutment(@RequestBody JSONObject dataObject) {
        return abutmentService.dataAbutment(dataObject);
    }

}


