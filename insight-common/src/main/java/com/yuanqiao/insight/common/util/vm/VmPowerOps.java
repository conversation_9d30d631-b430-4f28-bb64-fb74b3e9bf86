/*
 * *******************************************************
 * Copyright VMware, Inc. 2013, 2016.  All Rights Reserved.
 * SPDX-License-Identifier: MIT
 * *******************************************************
 *
 * DISCLAIMER. THIS PROGRAM IS PROVIDED TO YOU "AS IS" WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, WHETHER ORAL OR WRITTEN,
 * EXPRESS OR IMPLIED. THE AUTHOR SPECIFICALLY DISCLAIMS ANY IMPLIED
 * WARRANTIES OR CONDITIONS OF MERCHANTABILITY, SATISFACTORY QUALITY,
 * NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR PURPOSE.
 */
package com.yuanqiao.insight.common.util.vm;

import com.vmware.vim25.ManagedObjectReference;
import com.vmware.vim25.ServiceContent;
import com.vmware.vim25.VimPortType;

public class VmPowerOps {
    private final VimPortType vimPort;
    private final WaitForValues waitForValues;

    public VmPowerOps(VimPortType vimPort, ServiceContent serviceContent) {
        this.vimPort = vimPort;
        this.waitForValues = new WaitForValues(vimPort, serviceContent);
    }

    /**
     * Powers on VM and wait for power on operation to complete
     *
     * @param vmMor vm MoRef
     */
    public boolean powerOnVM(ManagedObjectReference vmMor) {
        try {
            ManagedObjectReference taskmor = vimPort.powerOnVMTask(vmMor, null);
            return waitForValues.getTaskResultAfterDone(taskmor);
        } catch (Exception e) {
            System.out.println("Reason :" + e.getLocalizedMessage());
            return false;
        }
    }

    /**
     * Powers off VM and waits for power off operation to complete
     *
     * @param vmMor vm MoRef
     */
    public boolean powerOffVM(ManagedObjectReference vmMor) {
        try {
            ManagedObjectReference taskmor = vimPort.powerOffVMTask(vmMor);
            return waitForValues.getTaskResultAfterDone(taskmor);
        } catch (Exception e) {
            System.out.println("Reason :" + e.getLocalizedMessage());
            return false;
        }
    }
    public boolean powerResetVM(ManagedObjectReference vmMor) {
        try {
            ManagedObjectReference taskmor = vimPort.resetVMTask(vmMor);
            return waitForValues.getTaskResultAfterDone(taskmor);
        } catch (Exception e) {
            System.out.println("Reason :" + e.getLocalizedMessage());
            return false;
        }
    }

    /**
     * Powers on vApp and waits for the the power on operation to complete
     *
     * @param vAppMor vApp MoRef
     */
    public boolean powerOnVApp(ManagedObjectReference vAppMor) {
        try {
            ManagedObjectReference taskmor = vimPort.powerOnVAppTask(vAppMor);
            return waitForValues.getTaskResultAfterDone(taskmor);
        } catch (Exception e) {
            System.out.println("Reason :" + e.getLocalizedMessage());
            return false;
        }
    }

    /**
     * Powers off vApp and waits for the power off operation to complete
     *
     * @param vAppMor vApp MoRef
     */
    public boolean powerOffVApp(ManagedObjectReference vAppMor) {
        try {
            ManagedObjectReference taskmor = vimPort.powerOffVAppTask(vAppMor,
                true);
            return waitForValues.getTaskResultAfterDone(taskmor);
        } catch (Exception e) {
            System.out.println("Reason :" + e.getLocalizedMessage());
            return false;
        }
    }

}
