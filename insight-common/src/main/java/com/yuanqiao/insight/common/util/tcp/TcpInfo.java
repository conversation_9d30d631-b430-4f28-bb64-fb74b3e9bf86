package com.yuanqiao.insight.common.util.tcp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @title: TcpInfo
 * @projectName jeecg-boot-parent
 * @description: TODO
 * @date 2021/3/15-13:55
 */
@ApiModel(value="TcpInfo对象", description="Tcp测试对象")
public class TcpInfo {
    @ApiModelProperty(value="ip")
    private String ip;// ip
    @ApiModelProperty(value="端口")
    private Integer tcpPort;//端口

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getTcpPort() {
        return tcpPort;
    }

    public void setTcpPort(Integer tcpPort) {
        this.tcpPort = tcpPort;
    }
}
