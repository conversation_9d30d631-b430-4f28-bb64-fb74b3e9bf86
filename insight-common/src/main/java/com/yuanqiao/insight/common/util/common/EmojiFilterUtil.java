package com.yuanqiao.insight.common.util.common;

public class EmojiFilterUtil {

    private static final String regex = "[\\p{So}\\p{Cn}]";
    //private static final String regex = "[\\p{So}\\p{Cs}\\uD83C\\uD83D\\uD83E\\u2600-\\u27BF\\uFE0F]";

    // 使用正则表达式去除 emoji 表情
    public static String removeEmoji(String input) {
        if (input == null) {
            return null;
        }
        // 正则表达式来匹配 Unicode 表情符号
        return input.replaceAll(regex, "");
    }

    public static boolean containsEmoji(String input) {
        if (input == null) {
            return false;
        }
        input = input.replaceAll("\\s+", "");
        // 正则表达式来匹配 Unicode 表情符号
        return input.matches(".*"+regex+".*");
    }

    public static void main(String[] args) {
        System.out.println(containsEmoji("{\"text\":\"test🤣\"}"));
    }
}
