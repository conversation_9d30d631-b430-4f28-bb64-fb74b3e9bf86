package com.yuanqiao.insight.common.util.jmx;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.management.remote.JMXConnector;
import javax.management.remote.JMXConnectorFactory;
import javax.management.remote.JMXServiceURL;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: JmxTestUtil
 * @projectName jeecg-boot-parent
 * @description: TODO
 * @date 2021/3/16-9:35
 */
public class JmxTestUtil {
    private static final Logger logger = LoggerFactory.getLogger(JmxTestUtil.class);

    public static String getConnectionStatus(JmxInfo jmxInfo) {
        String connectionStatus = "";
        String ip = jmxInfo.getIp();
        if (null != ip) {
            String port = jmxInfo.getPort();
            String type = jmxInfo.getType();
            String name = jmxInfo.getName();
            String pwd = jmxInfo.getPwd();
            JMXServiceURL serviceURL;
            JMXConnector connector = null;
            String url = "";
            if ("AS".equals(type)) {
                url = "service:jmx:rmi://" + ip + ":" + port + "/jndi/rmi://" + ip + ":" + port + "/jmxrmi";
            } else if ("apusic".equals(type)) {
                url = "service:jmx:iiop:///jndi/corbaname::1.2@" + ip + ":" + port + "#jmx/rmi/RMIConnectorServer";
            } else if ("Jboss".equals(type)) {
                url = "service:jmx:rmi:///jndi/rmi://" + ip + ":" + port + "/jmxrmi";
            } else if ("Tomcat".equals(type)) {
                url = "service:jmx:rmi:///jndi/rmi://" + ip + ":" + port + "/jmxrmi";
            } else if ("TongWeb".equals(type)) {
                url = "service:jmx:rmi:///jndi/rmi://" + ip + ":" + port + "/server";
            } else if ("Websphere".equals(type)) {
                url = "service:jmx:rmi:///jndi/rmi://" + ip + ":" + port + "/jmxrmi";
            } else if ("TongWeb5".equals(type)) {
                url = "service:jmx:rmi:///jndi/rmi://" + ip + ":" + port + "/jmxrmi";
            }else if ("BES".equals(type)) {
                url = "service:jmx:rmi:///jndi/rmi://" + ip + ":" + port + "/jmxrmi";
            }
            if (!"".equals(url)) {
                try {
                    serviceURL = new JMXServiceURL(url);
                    Map<String, String[]> map = new HashMap<String, String[]>();
                    String[] credentials = new String[]{name, pwd};
                    //修改连接AS时，无用户名密码连接失败问题，zhcht 2015-2-12
                    if (!"".equals(name)) {
                        map.put("jmx.remote.credentials", credentials);
                    }
                    connector = JMXConnectorFactory.connect(serviceURL, map);
                    connector.close();
                    System.out.println("连接成功！");
                    connectionStatus = "连接成功！";
                } catch (IOException e) {
                    connectionStatus = "连接失败！<br>通信错误。";
                    logger.error("连接失败！<br>通信错误。",e);
                } catch (SecurityException e) {
                    connectionStatus = "连接失败！<br>用户名密码错误或安全等级过高。";
                    logger.error("连接失败！<br>用户名密码错误或安全等级过高。",e);
                } catch (Exception e) {
                    connectionStatus = "连接失败！<br>通信错误。";
                    logger.error("连接失败！<br>通信错误。",e);
                }
            }
        }
        return connectionStatus;
    }

}
