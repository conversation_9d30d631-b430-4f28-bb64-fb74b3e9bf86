package com.yuanqiao.insight.common.util.ssh;

import lombok.Data;

import java.util.Map;

@Data
public class SshConnection {

    private String hostname;
    private Integer port;
    private String username;
    private String password;
    private Integer timeout;
    private Long shellAwaitTime;
    private boolean isNull;

    public SshConnection(String hostname, Integer port, String username, String password, Integer timeout, Long shellAwaitTime) {
        this.hostname = hostname;
        this.port = port;
        this.username = username;
        this.password = password;
        this.timeout = timeout;
        this.shellAwaitTime = shellAwaitTime;
    }

    public SshConnection(Map<String, String> connectInfo) {
        this.hostname = connectInfo.get("ip");
        this.port = Integer.parseInt(connectInfo.get("port"));
        this.username = connectInfo.get("sshUsername");
        this.password = connectInfo.get("sshPassword");
        this.timeout = Integer.parseInt(connectInfo.get("timeout"));
        this.isNull = isEmpty(this.hostname)
                || isEmpty(this.username)
                || this.port == null
                || this.timeout == null
                || isEmpty(this.password);
    }

    public static boolean isEmpty(String cs) {
        return cs == null || cs.trim().isEmpty();
    }
}

