/*
 *
 * Package:  com.yqit.insight.utils
 * FileName: SnmpUtils.java
 *
 */

package com.yuanqiao.insight.common.util.snmp;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.snmp4j.*;
import org.snmp4j.event.ResponseEvent;
import org.snmp4j.mp.MPv3;
import org.snmp4j.mp.SnmpConstants;
import org.snmp4j.security.*;
import org.snmp4j.smi.*;
import org.snmp4j.transport.DefaultUdpTransportMapping;
import org.snmp4j.util.DefaultPDUFactory;
import org.snmp4j.util.TableEvent;
import org.snmp4j.util.TableUtils;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 描述：
 * <p>
 * &nbsp;&nbsp;&nbsp;&nbsp;SNMP封装类，用于获取、设置SNMP PDU信息
 * </p>
 * 创建日期 2013-7-23
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public class SNMPUtils {
    private static final Logger logger = LoggerFactory
            .getLogger(SNMPUtils.class);


    private static String LEVEL_1 = "1";//snmp级别1
    private static String LEVEL_2 = "2";//snmp级别2
    private static String LEVEL_3 = "3";//snmp级别3
    /**
     * 正常状态
     */
    public static final int TIMEOUT = 4000;

    /**
     * 禁用状态
     */
    public static final int RETRY = 2;
    /**
     * 块同步锁
     */
    private final byte[] lock = new byte[0];

    /**
     * SNMP port(default port 161)
     */
    private int m_port = 161;

    /**
     * SNMP agent IP
     */
    private String m_ip;

    /**
     * SNMP community
     */
    private String m_community = "public";

    /**
     * SNMP version
     */
    private int m_version = SnmpConstants.version2c;

    /**
     * SNMP
     */
    private Snmp m_snmp = null;

    /**
     * SNMP target Address
     */
    private Address m_targetAddress = null;

    /**
     * SNMP Community Target
     */
    private CommunityTarget m_communityTarget = null;
    /**
     * SNMP V3 Target
     */
    private UserTarget userTarget = null;

    /**
     * max time out(default time 60s)
     */
    private int m_timeout = 6000;

    /**
     * retry time after connection failed(default retry times 5)
     */
    private int m_retry = 5;

    /**
     * 构造函数
     *
     * @param ip : 代理IP
     */
    public SNMPUtils(String ip) {

        if (ip != null) {
            m_ip = ip;
        }
        m_port = 161;
        m_timeout = 6000;
        m_retry = 5;

        try {
            initCommon();
        } catch (Exception e) {
        }
    }

    public static SNMPUtils InitSNMPUtils(Map map) {
        if (LEVEL_1.equals(map.get("level"))) {
            return new SNMPUtils((String) map.get("remoteIp"), Integer.parseInt((String) map.get("remotePort")));
        } else {
            return null;
        }
    }

    /**
     * 构造函数
     *
     * @param ip   : 代理IP
     * @param port : SNMP端口
     */
    public SNMPUtils(String ip, int port) {

        if (ip != null) {
            m_ip = ip;
        }
        if (port > 0) {
            m_port = port;
        }
        m_timeout = 6000;
        m_retry = 5;

        try {
            initCommon();
        } catch (Exception e) {
        }
    }

    /**
     * 构造函数
     *
     * @param ip        : 代理IP
     * @param port      : 代理SNMP端口
     * @param community ：SNMP 团体名
     * @param timeout   : SNMP 连接失败超时时间
     * @param retry     ：SNMP连接失败重试次数
     * @throws Exception
     */
    public SNMPUtils(String ip, int port, String community, Integer timeout,
                     Integer retry) throws Exception {

        if (ip != null) {
            m_ip = ip;
        }
        if (port > 0) {
            m_port = port;
        }

        if (community != null && community.length() > 0) {
            m_community = community;
        }

        if (timeout != null) {
            m_timeout = timeout;
        }
        if (retry != null) {
            m_retry = retry;
        }
        initCommon();
    }

    /**
     * 构造函数
     *
     * @param ip        : 代理IP
     * @param port      : 代理SNMP端口
     * @param community ：SNMP 团体名
     * @param timeout   : SNMP 连接失败超时时间
     * @param retry     ：SNMP连接失败重试次数
     * @throws Exception
     */
    public SNMPUtils(String ip, int port, String version, String community,
                     Integer timeout, Integer retry) throws Exception {

        if (ip != null) {
            m_ip = ip;
        }
        if (port > 0) {
            m_port = port;
        }

        if (version.equals("V1")) {
            m_version = SnmpConstants.version1;
        }
        if (community != null && community.length() > 0) {
            m_community = community;
        }

        if (timeout != null) {
            m_timeout = timeout;
        }
        if (retry != null) {
            m_retry = retry;
        }
        initCommon();
    }

    /**
     * 构造函数
     *
     * @param ip        : 代理IP
     * @param port      : 代理SNMP端口
     * @param version   : SNMP协议版本
     * @param community ：SNMP 团体名
     * @param timeout   : SNMP 连接失败超时时间
     * @param retry     ：SNMP连接失败重试次数
     */
    public SNMPUtils(String ip, int port, int version, String community,
                     Integer timeout, Integer retry) {

        if (ip != null) {
            m_ip = ip;
        }
        if (port > 0) {
            m_port = port;
        }
        // ensure the valid of version
        if (version != SnmpConstants.version1
                && version != SnmpConstants.version2c
                && version != SnmpConstants.version3) {
            m_version = SnmpConstants.version2c;
        } else {
            m_version = version;
        }

        if (community != null && community.length() > 0) {
            m_community = community;
        }

        if (timeout != null) {
            m_timeout = timeout;
        }
        if (retry != null) {
            m_retry = retry;
        }

        try {
            initCommon();
        } catch (Exception e) {
        }
    }


    public SNMPUtils(String ip, int port, String version, Integer timeout, Integer retry,
                     String uName, String snmpAuthLevel, String sAuth, String sAuth_passwd, String spriv, String spriv_passwd) {

        if (ip != null) {
            m_ip = ip;
        }
        if (port > 0) {
            m_port = port;
        }

        if ("V3".equalsIgnoreCase(version)) {
            m_version = SnmpConstants.version3;
        } else {
            logger.info("SNMPUtils:版本不准确，初始化错误.");
            return;
        }

        if (timeout != null) {
            m_timeout = timeout;
        }
        if (retry != null) {
            m_retry = retry;
        }

        OctetString snmpUName = new OctetString(uName);//用户名
        int securityLevel = 0;//认证级别
        OID authProtocol = null;//认证协议
        OID privProtocol = null;//加密协议
        OctetString authPassphrase = null;//认证协议密码
        OctetString privPassphrase = null;//加密协议密码

        if ("".equals(snmpAuthLevel) || null == snmpAuthLevel) {
            logger.info("SNMPUtils:认证级别不能为空.");
        } else if ("noAuthNoPriv".equalsIgnoreCase(snmpAuthLevel)) {
            securityLevel = SecurityLevel.NOAUTH_NOPRIV;//不认证不加密
        } else {
            if ("MD5".equalsIgnoreCase(sAuth)) {
                authProtocol = AuthMD5.ID;
            } else if ("SHA".equalsIgnoreCase(sAuth)) {
                authProtocol = AuthSHA.ID;
            } else if ("SHA-256".equalsIgnoreCase(sAuth)) {
                authProtocol = AuthHMAC192SHA256.ID;
            } else if ("SHA-512".equalsIgnoreCase(sAuth)) {
                authProtocol = AuthHMAC384SHA512.ID;
            } else {
                logger.info("SNMPUtils:认证协议指定错误.");
                return;
            }
            if (sAuth_passwd == null || sAuth_passwd.equals("")) {
                logger.info("SNMPUtils:认证密码不能为空.");
                return;
            }
            authPassphrase = new OctetString(sAuth_passwd);

            if ("AuthNoPriv".equalsIgnoreCase(snmpAuthLevel)) {
                securityLevel = SecurityLevel.AUTH_NOPRIV;//认证不加密
            } else if ("AuthPriv".equalsIgnoreCase(snmpAuthLevel)) {
                securityLevel = SecurityLevel.AUTH_PRIV;//认证加密
                if (spriv == null || spriv.equals("")) {
                    logger.info("SNMPUtils:加密协议不能为空.");
                    return;
                } else if ("DES".equalsIgnoreCase(spriv)) {
                    privProtocol = PrivDES.ID;
                } else if ("3DES".equalsIgnoreCase(spriv)) {
                    privProtocol = Priv3DES.ID;
                } else if ("AES128".equalsIgnoreCase(spriv)) {
                    privProtocol = PrivAES128.ID;
                } else if ("AES192".equalsIgnoreCase(spriv)) {
                    privProtocol = PrivAES192.ID;
                } else if ("AES256".equalsIgnoreCase(spriv)) {
                    privProtocol = PrivAES256.ID;
                } else {
                    logger.info("SNMPUtils:加密协议指定错误.");
                }
                if (spriv_passwd == null || spriv_passwd.equals("")) {
                    logger.info("加密密码不能为空.");
                    return;
                }
                privPassphrase = new OctetString(spriv_passwd);

            } else {
                logger.info("SNMPUtils:认证级别指定错误.");
                return;
            }

            UsmUser user = new UsmUser(snmpUName, authProtocol, authPassphrase, privProtocol, privPassphrase);


            try {
                m_snmp = new Snmp(new DefaultUdpTransportMapping());
            } catch (Exception e) {
                logger.error("SNMPUtils:new Snmp异常:" + e);
                m_snmp = null;
                return;
            }

//			USM usm = new USM(SecurityProtocols.getInstance(), new OctetString(MPv3.createLocalEngineID()), 0);
//	        SecurityModels.getInstance().addSecurityModel(usm);
            try {
                m_snmp.listen();
            } catch (IOException e) {
                logger.error("SNMPUtils:snmp listern异常:" + e);
                m_snmp = null;
                return;
            }
            if (m_snmp.getUSM() == null) {
                // 生成snmpv3的engineID
                byte[] localEngineID = MPv3.createLocalEngineID();
                USM usm = new USM(SecurityProtocols.getInstance(), new OctetString(localEngineID), 0);
                SecurityModels.getInstance().addSecurityModel(usm);
            }
            m_snmp.getUSM().addUser(snmpUName, user);

            userTarget = new UserTarget();
            userTarget.setVersion(SnmpConstants.version3);
            userTarget.setAddress(new UdpAddress(m_ip + "/" + m_port));
            userTarget.setSecurityLevel(securityLevel);
            userTarget.setSecurityName(snmpUName);
            userTarget.setTimeout(m_timeout);
            userTarget.setRetries(m_retry);


        }

    }

    public static SNMPUtils initSNMPUtils(JSONObject connectParam) {
        String ip = connectParam.getString("ip");
        Integer port = connectParam.getInteger("port");
        String community = connectParam.getString("community");
        String version = connectParam.getString("version");
        String username = connectParam.getString("username");
        String snmpAuthLevel = connectParam.getString("snmpAuthLevel");
        String sAuth = connectParam.getString("sAuth");
        String sAuthPasswd = connectParam.getString("sAuth_passwd");
        String spriv = connectParam.getString("spriv");
        String sprivPasswd = connectParam.getString("spriv_passwd");
        try {
            return create(ip, port, community, version, username, snmpAuthLevel, sAuth, sAuthPasswd, spriv, sprivPasswd);
        } catch (Exception e) {
            log.error("snmpUtils初始化异常！", e);
        }
        return null;
    }

    public static SNMPUtils buildSNMPUtils(SnmpInfo snmpInfo) throws Exception {
        String ip = snmpInfo.getIp();
        Integer port = snmpInfo.getSnmpPort();
        String community = snmpInfo.getSnmpRName();
        String version = snmpInfo.getStype();
        String username = snmpInfo.getSnmpUName();
        String snmpAuthLevel = snmpInfo.getSnmpAuthLevel();
        String sAuth = snmpInfo.getSAuth();
        String sAuthPasswd = snmpInfo.getSAuthPasswd();
        String spriv = snmpInfo.getSpriv();
        String sprivPasswd = snmpInfo.getSprivPasswd();
        return create(ip, port, community, version, username, snmpAuthLevel, sAuth, sAuthPasswd, spriv, sprivPasswd);
    }

    private static SNMPUtils create(String ip
            , Integer port
            , String community
            , String version
            , String username
            , String snmpAuthLevel
            , String sAuth
            , String sAuthPasswd
            , String spriv
            , String sprivPasswd) throws Exception {

        if (StringUtils.isEmpty(version) || "V1".equalsIgnoreCase(version)) {
            //V1
            return new SNMPUtils(ip, port);
        } else if ("V2".equalsIgnoreCase(version)) {
            //V2
            Assert.notBlank(community, "[community failed] - this String argument must have text; it must not be null, empty, or blank");
            return new SNMPUtils(ip, port, community, TIMEOUT, RETRY);
        } else if ("V3".equalsIgnoreCase(version)) {
            //V3
            //ip地址、端口号、snmp版本、超时时间、重试次数、用户名、安全级别、认证协议、认证密码、加密协议、加密密码
            Assert.notBlank(username, "[username failed] - this String argument must have text; it must not be null, empty, or blank");
            Assert.notBlank(snmpAuthLevel, "[snmpAuthLevel failed] - this String argument must have text; it must not be null, empty, or blank");
            Assert.notBlank(sAuth, "[sAuth failed] - this String argument must have text; it must not be null, empty, or blank");
            Assert.notBlank(sAuthPasswd, "[sAuthPasswd failed] - this String argument must have text; it must not be null, empty, or blank");
            Assert.notBlank(spriv, "[spriv failed] - this String argument must have text; it must not be null, empty, or blank");
            Assert.notBlank(sprivPasswd, "[sprivPasswd failed] - this String argument must have text; it must not be null, empty, or blank");
            return new SNMPUtils(ip, port, version, TIMEOUT, RETRY,
                    username, snmpAuthLevel, sAuth, sAuthPasswd, spriv, sprivPasswd);
        } else {
            throw new Exception("版本错误");
        }

    }

    /**
     * 初始化 Address, CommunityTarget
     *
     * @throws Exception 初始化地址失败时报该异常
     */
    private void initCommon() throws Exception {
        try {
            // Set agent IP and port
            m_targetAddress = GenericAddress.parse("udp:" + m_ip + "/"
                    + String.valueOf(m_port));

            // set Community target's community, time out, retry times, SNMP
            // version
            m_communityTarget = new CommunityTarget();
            m_communityTarget.setCommunity(new OctetString(m_community));
            m_communityTarget.setAddress(m_targetAddress);
            m_communityTarget.setTimeout(m_timeout);
            m_communityTarget.setRetries(m_retry);
            m_communityTarget.setVersion(m_version);

            // create SNMP
            TransportMapping transport = new DefaultUdpTransportMapping();
            m_snmp = new Snmp(transport);
            transport.listen();
        } catch (IOException e) {
            throw new Exception(e);
        }
    }

    public void destroy() throws Exception {
        try {
            if (m_snmp != null) {
                m_snmp.close();
            }
        } catch (IOException e) {
            logger.error("SNMPUtils:关闭snmp异常：" + e);

        }
    }

    /**
     * 解析SNMP协议相应信息
     *
     * @param event : SNMP response Event
     * @return : SNMP 相应返回的信息
     * @throws Exception 解析SNMP协议相应信息失败时报该异常
     */
    private List<String> parseResponse(ResponseEvent event)
            throws Exception {
        // 定义函数返回值
        List<String> list = new ArrayList<String>();

        // 解析SNMP协议返回信息
        if (event != null && event.getResponse() != null) {
            Vector recVBs = event.getResponse()
                    .getVariableBindings();
            VariableBinding recVB;
            for (int ii = 0; ii < recVBs.size(); ++ii) {
                recVB = (VariableBinding) recVBs.get(ii);
                list.add(recVB.getVariable().toString());
            }
        }

        return list;
    }

    /**
     * 解析SNMP协议相应信息
     *
     * @param event : SNMP response Event
     * @return : SNMP相应返回的信息<"OID", "VALUE">
     * @throws Exception 解析SNMP协议相应信息失败时报该异常
     */
    private Map<String, String> parseResponseMap(ResponseEvent event)
            throws Exception {
        // 定义函数返回值
        Map<String, String> values = new HashMap<String, String>();

        // 解析SNMP协议返回信息
        if (event != null && event.getResponse() != null) {
            Vector recVBs = event.getResponse()
                    .getVariableBindings();
            VariableBinding recVB;
            for (int ii = 0; ii < recVBs.size(); ++ii) {
                recVB = (VariableBinding) recVBs.get(ii);
                values.put(recVB.getOid().toString(), recVB.getVariable()
                        .toString());
            }
        }

        return values;
    }

    /**
     * 设置PDU
     *
     * @param oid
     * @param value
     * @throws Exception 报该异常
     */
    public boolean setPDU(String oid, String value) throws Exception {
        // create and set PDU
        PDU pdu = new PDU();
        pdu.add(new VariableBinding(new OID(oid), new OctetString(value)));
        pdu.setType(PDU.SET);
        boolean resultFlag = true;

        // 添加块锁，使得在设置PDU值时，别的进程不能读或写PDU
        try {
            synchronized (lock) {
                m_snmp.send(pdu, m_communityTarget);
            }
        } catch (IOException e) {
            resultFlag = false;
            throw new Exception(e);
        }
        return resultFlag;
    }

    /**
     * 取一个OID的PDU值
     *
     * @param oid
     * @return : OID对应的值
     * @throws Exception 取一个OID的PDU值失败时报该异常
     */
    public String getPDU(String oid) throws Exception {
        // 定义返回值
        String value = "";

        // 创建设置PDU
        PDU pdu = createPDU();
        pdu.add(new VariableBinding(new OID(oid)));
        pdu.setType(PDU.GET);
        // 添加块锁，使得在读取PDU值时，别的进程不能读或写PDU
        try {
            synchronized (lock) {
                ResponseEvent respEvent = m_snmp.send(pdu, getTarget());
                List<String> list = parseResponse(respEvent);
                if (list.size() >= 1) {
                    value = list.get(0);
                }
            }
        } catch (IOException e) {
            throw new Exception(e);
        }
        return value;
    }

    /**
     * 判断是否能够连接
     *
     * @return
     */
    public PDU createPDU() {
        PDU pdu = null;
        if (m_version == SnmpConstants.version3) {
            pdu = new ScopedPDU();
        } else {
            pdu = new PDU();
        }
        //pdu.setType(PDU.GET);
        return pdu;

    }

    /**
     * 判断设备是否连接成功
     *
     * @return
     */
    public Target getTarget() {
        if (m_version == SnmpConstants.version3) {
            return userTarget;
        } else {
            return m_communityTarget;
        }
    }

    public boolean isConnectble() {
        // 创建设置PDU
        PDU pdu = createPDU();
        pdu.add(new VariableBinding(new OID("")));
        pdu.setType(PDU.GET);
        // 添加块锁，使得在读取PDU值时，别的进程不能读或写PDU
        try {
            synchronized (lock) {
                ResponseEvent respEvent = m_snmp.send(pdu, getTarget());
                if (respEvent.getResponse() == null) {
                    return false;
                }
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            return false;
        }
        return true;
    }

    /**
     * 取一个OID的PDU值
     *
     * @param oid
     * @return : OID与值，<"OID", "VALUE">
     * @throws Exception 取一个OID的PDU值失败时报此异常
     */
    public Map<String, String> getPDUMap(String oid) throws Exception {
        // 返回值
        Map<String, String> value = null;
        // 创建设置PDU
        PDU pdu = new PDU();
        pdu.add(new VariableBinding(new OID(oid)));
        pdu.setType(PDU.GET);

        // 添加块锁，使得在读取PDU值时，别的进程不能读或写PDU
        try {
            synchronized (lock) {
                ResponseEvent respEvent = m_snmp.send(pdu, m_communityTarget);
                value = parseResponseMap(respEvent);
            }
        } catch (IOException e) {
            throw new Exception(e);
        }

        return value;
    }

    /**
     * 取多个OID的值
     *
     * @param listOid : OID列表
     * @return :与传入的OID序列对应的OID值序列
     * @throws Exception 取多个OID的值时报此异常
     */
    public List<String> getPDU(List<String> listOid) throws Exception {
        // 定义返回值
        List<String> list = null;

        // 创建设置PDU
        PDU pdu = new PDU();
        for (int ii = 0; ii < listOid.size(); ii++) {
            pdu.add(new VariableBinding(new OID(listOid.get(ii))));
        }
        pdu.setType(PDU.GET);

        // 添加块锁，使得在读取PDU值时，别的进程不能读或写PDU
        try {
            synchronized (lock) {
                ResponseEvent respEvent = m_snmp.send(pdu, m_communityTarget);
                list = parseResponse(respEvent);
            }
        } catch (IOException e) {
            throw new Exception(e);
        }

        return list;
    }

    /**
     * 取多个OID的值
     *
     * @param oids : OID列表
     * @return :<"OID","VALUE">序列，OID及其对应值序列
     * @throws Exception 取多个OID的值失败时报此异常
     */
    public Map<String, String> getPDUMap(List<String> oids)
            throws Exception {
        // 定义返回值
        Map<String, String> values = null;

        // 创建设置PDU
        PDU pdu = new PDU();
        for (int ii = 0; ii < oids.size(); ii++) {
            pdu.add(new VariableBinding(new OID(oids.get(ii))));
        }
        pdu.setType(PDU.GET);

        // 添加块锁，使得在读取PDU值时，别的进程不能读或写PDU
        try {
            synchronized (lock) {
                ResponseEvent respEvent = m_snmp.send(pdu, m_communityTarget);
                values = parseResponseMap(respEvent);
            }
        } catch (IOException e) {
            throw new Exception("" + e);
        }

        return values;
    }

    /**
     * 查询指定OID（SNMP协议中的对象标识）入口的所有OID树信息
     *
     * @param oid
     * @return
     * @throws Exception 查询指定OID（SNMP协议中的对象标识）入口的所有OID树信息失败时报此异常
     */
    public Map<String, String> getPDUWalk(String oid) throws Exception {
        Map<String, String> values = new HashMap<String, String>();
        try {
            synchronized (lock) {
                OID targetOid = new OID(oid);
                //PDU pdu = new PDU();
                PDU pdu = createPDU();
                pdu.add(new VariableBinding(targetOid));
                while (true) {
                    // VariableBinding vb = new VariableBinding(new OID("1"));
                    ResponseEvent responseEvent = m_snmp.getNext(pdu, getTarget());
                    if (responseEvent == null) {
                        break;
                    }

                    pdu = responseEvent.getResponse();
                    if (pdu == null || pdu.getErrorStatus() != 0) {
                        break;
                    }

                    VariableBinding vb = pdu.get(0);
                    if (vb.getOid() == null || !vb.getOid().startsWith(targetOid) || Null.isExceptionSyntax(vb.getVariable().getSyntax())) {
                        break;
                    }

                    values.put(vb.getOid().toString(), vb.getVariable().toString());
                    pdu.setRequestID(new Integer32(0));
                    pdu.set(0, vb);
                }
            }
        } catch (IOException e) {
            return null;
        }

        return values;
    }

    /**
     * TableUtils获取表格数据
     *
     * @param oid
     * @return
     */
    public Map<String, String> getPduTable(String oid) {

        Map<String, String> valueMap = new HashMap<>();

        try {
            TableUtils utils = new TableUtils(m_snmp, new DefaultPDUFactory(PDU.GETBULK));
            OID[] columnOid = new OID[]{new OID(oid)};
            List<TableEvent> tableEvents = null;
            if (m_version == 3) {
                tableEvents = utils.getTable(userTarget, columnOid, null, null);
            } else {
                tableEvents = utils.getTable(m_communityTarget, columnOid, null, null);
            }
            for (TableEvent tableEvent : tableEvents) {
                VariableBinding[] everyVbList = tableEvent.getColumns();
                if (null == everyVbList) {
                    continue;
                }
                VariableBinding vb = everyVbList[0];
                valueMap.put(vb.getOid() + "", vb.getVariable() + "");
            }
        } catch (Exception e) {
            logger.error("SNMPUtils::getWalkTable 异常！", e);
        }

        return valueMap;
    }

    /**
     * 根据TableUtils获取的表格数据组装table_view
     *
     * @param oid
     * @return
     */
    public JSONObject getPduTableView(String oid) throws Exception {

        Map<String, String> values = getPduTable(oid);

        JSONObject resObject = new JSONObject();
        Map<Integer, JSONObject> dataMap = new TreeMap<>();
        List<JSONObject> columnList = new ArrayList<>();
        for (Map.Entry<String, String> part : values.entrySet()) {
            JSONObject everyObject = new JSONObject();
            everyObject.put("title", part.getKey().substring(0, part.getKey().lastIndexOf(".")));
            everyObject.put("dataIndex", part.getKey().substring(0, part.getKey().lastIndexOf(".")));
            JSONObject tempObject = new JSONObject();
            tempObject.put("customRender", "name");
            everyObject.put("scopedSlots", tempObject);
            columnList.add(everyObject);

            String partName = part.getKey().substring(part.getKey().lastIndexOf(".") + 1);
            JSONObject elemObject = dataMap.get(Integer.parseInt(partName));
            if (elemObject == null) {
                elemObject = new JSONObject();
            }
            elemObject.put(part.getKey().substring(0, part.getKey().lastIndexOf(".")), part.getValue());
            dataMap.put(Integer.parseInt(partName), elemObject);
        }
        resObject.put("dataList", dataMap.values());
        columnList = columnList.stream().distinct().collect(Collectors.toList());
        columnList.sort((o1, o2) -> {
            Integer a = Integer.parseInt(o1.getString("title").substring(o1.getString("title").lastIndexOf(".") + 1));
            Integer b = Integer.parseInt(o2.getString("title").substring(o2.getString("title").lastIndexOf(".") + 1));
            return a.compareTo(b);
        });
        resObject.put("columnList", columnList);
//        System.out.println(resObject);
        return resObject;
    }

    public List<Map<String,Object>> snmpWalkTable(String oid) {

        TableUtils utils = new TableUtils(m_snmp, new DefaultPDUFactory(PDU.GETBULK));
        OID[] columnOid = new OID[]{new OID(oid)};
        List<TableEvent> tableEvents = null;
        if (m_version == 3) {
            tableEvents = utils.getTable(userTarget, columnOid, null, null);
        } else {
            tableEvents = utils.getTable(m_communityTarget, columnOid, null, null);
        }


        String nOid = oid.substring(1);

        List<Map<String,Object>> ja = new ArrayList<>();
        for (TableEvent tableEvent : tableEvents) {
            VariableBinding[] vb = tableEvent.getColumns();
            if (null == vb) {
                continue;
            }
            if (vb[0].getOid().toString().contains(nOid + ".1.1.")) {
                Map<String,Object> jo = new LinkedHashMap<>();
                jo.put("id", StringUtils.substringAfter(vb[0].getOid().toString(), nOid + ".1.1."));
                jo.put("1.1", vb[0].getVariable().toString());
                ja.add(jo);
            } else {
                //id为一位的存在bug
                //List<Map> jos = ja.stream().filter((Map map) -> vb[0].getOid().toString().equals(StringUtils.substringBefore(vb[0].getOid().toString(), map.get("id").toString()) + map.get("id").toString())).collect(Collectors.toList());
                String index = tableEvent.getIndex().toString();
                String id = index.substring(index.indexOf(".", index.indexOf(".") + 1) + 1);
                List<Map<String,Object>> jos = ja.stream().filter((Map<String,Object> map) -> map.get("id").equals(id)).collect(Collectors.toList());
                if (!jos.isEmpty()) {
                    Map<String,Object> jo = jos.get(0);
                    jo.put(StringUtils.substringBeforeLast(tableEvent.getIndex().toString(), "." + jo.get("id").toString()), vb[0].getVariable().toString());
                }
            }
        }
        return ja;
    }

    public int getM_port() {
        return m_port;
    }

    public void setM_port(int m_port) {
        this.m_port = m_port;
    }

    public String getM_ip() {
        return m_ip;
    }

    public void setM_ip(String m_ip) {
        this.m_ip = m_ip;
    }

    public String getM_community() {
        return m_community;
    }

    public void setM_community(String m_community) {
        this.m_community = m_community;
    }
}
