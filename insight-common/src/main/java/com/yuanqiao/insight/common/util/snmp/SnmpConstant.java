package com.yuanqiao.insight.common.util.snmp;

/**
 * SnmpConstant 常量
 */
public interface SnmpConstant {

    // 接口索引 主键
    public static final String indexOid = "*******.*******.1.1";
    //端口状态
    public static final String statusOid = "*******.*******.1.8";
    //端口描述
    public static final String descriptionOid = "*******.*******.1.2";
    // 带宽
    public static final String bandWidthOid = "*******.*******.1.5";
    //端口类型
    public static final String typeOid = "*******.*******.1.3";
    //接收错误数据包
    public static final String inErrorsOid = "*******.*******.1.14";
    //发送的错误数据包
    public static final String outErrorsOid = "*******.*******.1.20";
    //输入流量
    public static final String inFlowOid = "*******.*******.1.10";
    //输出流量
    public static final String outFlowOid = "*******.*******.1.16";
    //输入丢失错误包数
    public static final String inLossPackageOid = "*******.*******.1.13";
    //输出丢失错误包数
    public static final String outLossPackageOid = "*******.*******.1.19";
    //输入单播报文的个数
    public static final String inUcastPktsOid = "*******.*******.1.11";
    //输入非单播报文的个数
    public static final String inNUcastPktsOid = "*******.*******.1.12";
    //输出单播报文的个数
    public static final String outUcastPktsOid = "*******.*******.1.17";
    //输出非单播报文的个数
    public static final String outNUcastPktsOid = "*******.*******.1.18";
}
