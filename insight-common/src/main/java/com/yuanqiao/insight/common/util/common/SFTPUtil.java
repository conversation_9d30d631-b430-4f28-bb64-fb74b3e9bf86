package com.yuanqiao.insight.common.util.common;

import com.jcraft.jsch.*;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @title: SFTPUtil           SFTP连接工具类
 * @projectName jeecg-boot-parent
 * @description: TODO
 * @date 2021/5/24-17:21
 */
@Slf4j
public class SFTPUtil {
    /**
     * 连接sftp服务器
     *
     * @param host     远程主机ip地址
     * @param port     sftp连接端口，null 时为默认端口
     * @param user     用户名
     * @param password 密码
     * @return
     * @throws JSchException
     */
    public static Session connect(String host, Integer port, String user, String password) throws JSchException {
        Session session = null;
        try {
            JSch jsch = new JSch();
            if (port != null) {
                session = jsch.getSession(user, host, port.intValue());
            } else {
                session = jsch.getSession(user, host);
            }
            session.setPassword(password);
            //设置第一次登陆的时候提示，可选值:(ask | yes | no)
            session.setConfig("StrictHostKeyChecking", "no");
            //30秒连接超时
            session.connect(30000);
        } catch (JSchException e) {
            log.error("获取SFTP连接时发生异常！", e);
        }
        return session;
    }

    /**
     * 上传文件
     *
     * @param directory  上传的目录
     * @param uploadFile 要上传的文件
     * @param sftp       sftp
     */
    public static boolean upload(String directory, String uploadFile, ChannelSftp sftp) {
        try {
            sftp.cd(directory);
            File file = new File(uploadFile);
            FileInputStream fileInputStream = new FileInputStream(file);
            sftp.put(fileInputStream, file.getName());
            fileInputStream.close();
            return true;
        } catch (Exception e) {
            log.error("sftp文件上传异常！", e);
            return false;
        }
    }

    /**
     * sftp上传文件(夹)
     *
     * @param directory  目标url
     * @param path       本地要上传的路径 url
     * @param uploadFile 要上传的文件夹和文件url
     * @param sftp       sftp
     * @throws Exception
     */
    public static void upload(String directory, String path, String uploadFile, ChannelSftp sftp) throws Exception {
        File file = new File(uploadFile);
        if (file.exists()) {
            //这里有点投机取巧，因为ChannelSftp无法去判读远程linux主机的文件路径,无奈之举
            try {
                Vector content = sftp.ls(directory);
                if (content == null) {
                    sftp.mkdir(directory);

                }
            } catch (SftpException e) {//循环创建文件夹
                String infor = directory.replace(path, "");
                String infor1[] = path.split("/");
                if (path.equals("")) {
                    sftp.mkdir(directory);
                } else {
                    for (int i = 1; i < infor1.length; i++) {
                        infor = infor + "/" + infor1[i];
                        try {
                            Vector content1 = sftp.ls(infor);
                        } catch (Exception e1) {
                            sftp.mkdir(infor);
                        }
                    }
                }

            }
            //进入目标路径
            sftp.cd(directory);
            if (file.isFile()) {
                InputStream ins = new FileInputStream(file);
                //中文名称的
                sftp.put(ins, new String(file.getName().getBytes(), "UTF-8"));
                ins.close();
            } else {
                File[] files = file.listFiles();
                for (File file2 : files) {
                    String dir = file2.getAbsolutePath();
                    String uploadPath = directory;
                    if (file2.isDirectory()) {
                        String str = dir.substring(dir.lastIndexOf(file2.separator));
                        uploadPath = (directory + str).replace("\\", "/");
                    }
                    upload(uploadPath, "", dir, sftp);
                }
            }
        }
    }

    /**
     * 获取文件创建时间
     *
     * @param srcFile 文件完全路径
     * @param sftp    ChannelSftp
     * @throws SftpException,ParseException
     */
    public static String getTime(String srcFile, ChannelSftp sftp) throws Exception {
        String dateTime = null;
        Date date = null;
        try {
            dateTime = sftp.stat(srcFile).getMtimeString();
            SimpleDateFormat formatter = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyy", Locale.US);
            date = (Date) formatter.parse(dateTime);
        } catch (SftpException e) {
            throw new Exception(e);
        } catch (ParseException e) {
            throw new Exception(e);
        }
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date);
    }

    /**
     * 获取文件大小
     *
     * @param srcFile 文件完全路径
     * @param sftp    ChannelSftp
     * @throws SftpException
     */
    public static long getSize(String srcFile, ChannelSftp sftp) throws SftpException {
        long size = 0L;
        size = sftp.stat(srcFile).getSize();
        return size;
    }

    /**
     * 判断文件还是文件夹
     */
    public static boolean isFile(String name, ChannelSftp sftp) {
        Vector conts = null;
        try {
            conts = sftp.ls(name);
        } catch (SftpException e) {
            return true;
        }
        if (conts.size() == 1) {
            return true;
        }
        return false;
    }

    /**
     * sftp下载文件（夹）
     *
     * @param srcFile  下载文件完全路径
     * @param saveFile 保存文件路径
     * @param sftp     ChannelSftp
     * @throws Exception
     */
    public static void download(String srcFile, String saveFile, ChannelSftp sftp) throws Exception {
        Vector conts = null;
        try {
            conts = sftp.ls(srcFile);
        } catch (SftpException e) {
            throw new Exception("ChannelSftp sftp罗列文件发生错误", e);
        }
        File file = new File(saveFile);
        if (!file.exists()) {
            file.mkdirs();
        }
        if (isFile(srcFile, sftp)) {
            try {
                sftp.get(srcFile, saveFile);
            } catch (SftpException e) {
                throw new Exception("ChannelSftp sftp下载文件发生错误", e);
            }
        } else {
            //文件夹(路径)
            Iterator iterator = conts.iterator();
            while (iterator.hasNext()) {
                String srcFileBak = "";
                String saveFileBak = "";
                ChannelSftp.LsEntry obj = (ChannelSftp.LsEntry) iterator.next();
                String filename = new String(obj.getFilename().getBytes(), "UTF-8");
                boolean fileType = obj.getLongname().indexOf("d") == 0 ? false : true;//判断是文件还是文件夹
                if (fileType) {
                    String[] arrs = filename.split("\\.");
                    if ((arrs.length > 0) && (arrs[0].length() > 0)) {
                        srcFileBak = (srcFile + System.getProperty("file.separator") + filename).replace("\\", "/");
                    } else {
                        continue;
                    }
                    download(srcFileBak, saveFile, sftp);
                } else {
                    String[] arrs = filename.split("\\.");
                    if ((arrs.length > 0) && (arrs[0].length() > 0)) {
                        srcFileBak = (srcFile + System.getProperty("file.separator") + filename).replace("\\", "/");
                        saveFileBak = (saveFile + System.getProperty("file.separator") + filename).replace("\\", "/");
                        download(srcFileBak, saveFileBak, sftp);
                    } else {
                        continue;
                    }
                }
            }
        }
    }

    /**
     * 回去对应文件夹下的所有
     *
     * @param srcFile
     * @param sftp
     */
    public static List<String> getFiles(String srcFile, ChannelSftp sftp) {
        List<String> list = new ArrayList<>();
        try {
            Vector conts = sftp.ls(srcFile);
            //文件夹(路径)
            Iterator iterator = conts.iterator();
            while (iterator.hasNext()) {
                ChannelSftp.LsEntry obj = (ChannelSftp.LsEntry) iterator.next();
                String filename = new String(obj.getFilename().getBytes(), "UTF-8");
                System.out.println(filename);
                list.add(filename);
            }
        } catch (Exception e) {
            log.error("SFTPUtil##getFiles(srcFile={})", srcFile, e);
//            e.printStackTrace();
        }
        return list;
    }

    public static void main(String[] args) {

        try {
            Session session = connect("192.168.16.208", 22,
                    "root", "123456");
            Channel channel = session.openChannel("sftp");
            channel.connect();
            ChannelSftp sftp = (ChannelSftp) channel;
            SFTPUtil.getFiles("/usr/local/nginx/logs", sftp);
//            SFTPUtil.upload("/usr/local/nginx/confBak/测试/", "","D:\\opt\\upFiles\\aIfile\\backupTemp\\1396728990693957633\\source", sftp);
//            SFTPUtil.download("/usr/local/jeecg-boot","D://opt//upFiles//aIfile/backupTemp/1397444189650485249/source",sftp);
            if (sftp != null) {
                sftp.disconnect();
                sftp = null;
            }
            if (session != null) {
                session.disconnect();
                session = null;
            }
            if (channel != null) {
                channel.disconnect();
                channel = null;
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
