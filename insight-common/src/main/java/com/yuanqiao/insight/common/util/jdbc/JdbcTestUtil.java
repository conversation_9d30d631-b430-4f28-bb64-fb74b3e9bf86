package com.yuanqiao.insight.common.util.jdbc;

import com.yuanqiao.insight.common.constant.CommonConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @title: JdbcTestUtil
 * @projectName jeecg-boot-parent
 * @description: TODO
 * @date 2021/3/16-16:14
 */
public class JdbcTestUtil {
    private static final Logger logger = LoggerFactory.getLogger(JdbcTestUtil.class);

    public static String getConnectionStatus(JdbcConnectInfo jdbcConnectInfo) {
        String connectionStatus = "";
        String ip =  jdbcConnectInfo.getIp();
        if(null!=ip) {
            String port = jdbcConnectInfo.getPort();
            String type = jdbcConnectInfo.getType();
            String db = jdbcConnectInfo.getDb();
            String name = jdbcConnectInfo.getDbUName();
            String pwd = jdbcConnectInfo.getDbPwd();
            String url = "";
            String driver = "";
            //out.println(type);
            if ("oracle".equals(type)) {
                driver = CommonConstant.ORACLE_DRIVER;
                url = "jdbc:oracle:thin:@" + ip + ":" + port + "/" + db;
            } else if ("db2".equals(type)) {
                driver = CommonConstant.DB2_DRIVER;
                url = "jdbc:db2://" + ip + ":" + port + "/" + db;
            } else if ("mysql".equals(type)) {
                driver = CommonConstant.MYSQL_DRIVER;
                url = "jdbc:mysql://" + ip + ":" + port + "/" + db + "?socketTimeout=2000&serverTimezone=UTC";
            } else if ("sqlserver".equals(type)) {
                driver = CommonConstant.SQLSERVER_DRIVER;
                url = "jdbc:jtds:sqlserver://" + ip + ":" + port + ";DatabaseName=" + db;
            } else if ("sybase".equals(type)) {
                driver = CommonConstant.SYBASE_DRIVER;
                url = "jdbc:jtds:sybase://" + ip + ":" + port + ";DatabaseName=" + db;
            } else if ("kb".equals(type)) {
                driver = CommonConstant.KB_DRIVER;
                url = "jdbc:kingbase://" + ip + ":" + port + "/" + db;
            } else if ("kb8".equals(type)) {
                driver = CommonConstant.KB8_DRIVER;
                url = "jdbc:kingbase8://" + ip + ":" + port + "/" + db;
            } else if ("dm".equals(type)) {
                driver = CommonConstant.DM_DRIVER;
                url = "jdbc:dm://" + ip + ":" + port + "/" + db;
            } else if ("st".equals(type)) {
                driver = CommonConstant.ST_DRIVER;
                url = "jdbc:oscar://" + ip + ":" + port + "/" + db;
            } else if ("highgo".equals(type)) {
                driver = CommonConstant.HIGHGO_DRIVER;
                url = "jdbc:highgo://" + ip + ":" + port + "/" + db;
            } else if ("gbase".equals(type)) {
                driver = CommonConstant.GBASE_DRIVER;
                url = "jdbc:gbasedbt-sqli://" + ip + ":" + port + "/" + db;
            }

            try {
                Class.forName(driver);//.newInstance();
                DriverManager.setLoginTimeout(1); //秒数
                try {
                    Connection conn = DriverManager.getConnection(url, name, pwd);
                    conn.close();
                    connectionStatus ="连接成功！";
                } catch (SQLException e) {
                    connectionStatus = "连接失败！<br>";
                    connectionStatus += "" + e.getMessage();
                    logger.error("JdbcTestUtil##getConnectionStatus()",e);
                } catch (Exception e) {
                    connectionStatus ="连接失败！<br>";
                    connectionStatus +=e.getMessage();
                    logger.error("JdbcTestUtil##getConnectionStatus()",e);
                }
            } catch (Exception e) {
                connectionStatus = "连接失败！<br>";
                connectionStatus += driver + " is not found!<br>";
                connectionStatus +=e.getMessage();
                e.printStackTrace();
                logger.error("JdbcTestUtil##getConnectionStatus()",e);
            }
        }
        return connectionStatus;
    }
}
