package com.yuanqiao.insight.common.util.ssh;


import cn.hutool.core.io.IoUtil;
import cn.hutool.extra.ssh.ChannelType;
import cn.hutool.extra.ssh.JschUtil;
import com.alibaba.fastjson.JSONObject;
import com.jcraft.jsch.Channel;
import com.jcraft.jsch.ChannelExec;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.yuanqiao.insight.common.constant.CommonConstant;
import com.yuanqiao.insight.common.util.common.LocalCacheUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.io.PrintStream;
import java.util.Objects;
import java.util.Optional;

/**
 * Ssh操作器,基于jsch
 */
@Slf4j
@Component
public class SshUtils {

    private ChannelExec channelExec = null;
    private InputStream execInputStream = null;
    private static LocalCacheUtils cacheUtils = LocalCacheUtils.getInstance();
    private static Session session = null;
    private Channel channelShell = null;
    private InputStream shellInputStream = null;
    private PrintStream shellCommander = null;


    public boolean isConnection(SshConnection sshConnection) {
        Session session = null;
        try {
            session = JschUtil.openSession(sshConnection.getHostname(), sshConnection.getPort(), sshConnection.getUsername(), sshConnection.getPassword(), sshConnection.getTimeout());
            channelExec = (ChannelExec) session.openChannel("exec");
            execInputStream = channelExec.getInputStream();
            channelExec.connect();
            return true;
        } catch (Exception e) {
            log.error("初始化SSH连接异常！", e);
            return false;
        } finally {
            disExecConnect(session);
        }
    }

    /**
     * 服务器设备 -- Jsch-Exec
     */

    // 执行命令，读取响应
    public String doSshExecExecute(SshConnection sshConnection, String cmd) {
        Session session = null;
        String var = "";
        try {
            //打开exec通道
            session = JschUtil.openSession(sshConnection.getHostname(), sshConnection.getPort(), sshConnection.getUsername(), sshConnection.getPassword(), sshConnection.getTimeout());
            initExecConnect(session);

            // 发送命令，返回响应
            return sendExecCommand(cmd);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            disExecConnect(session);
        }
        return var;
    }

    // 初始化Jsch_Exec通道连接
    public void initExecConnect(Session session) {
        try {
            java.util.Properties config = new java.util.Properties();
            config.put("StrictHostKeyChecking", "no");
            session.setConfig(config);
            channelExec = (ChannelExec) session.openChannel("exec");
        } catch (Exception e) {
            log.error("初始化SSH连接异常！", e);
        }
    }

    // Exec 发送命令，返回响应
    public String sendExecCommand(String cmd) throws IOException, InterruptedException, JSchException {
        channelExec.setCommand(cmd);
        channelExec.connect();
        InputStream in = channelExec.getInputStream();
        //InputStream err = channelExec.getErrStream();
        // 读取标准输出
        byte[] buffer = new byte[1024];
        StringBuilder output = new StringBuilder();
        while (true) {
            while (in.available() > 0) {
                int len = in.read(buffer, 0, 1024);
                if (len < 0) break;
                output.append(new String(buffer, 0, len));
            }
            if (channelExec.isClosed()) {
                if (in.available() > 0) continue;
                break;
            }
            Thread.sleep(100);
        }
        // 读取错误输出
        /*StringBuilder error = new StringBuilder();
        while (err.available() > 0) {
            int len = err.read(buffer, 0, 1024);
            error.append(new String(buffer, 0, len));
        }*/
        return output.toString();
    }

    // Exec 关闭连接
    public void disExecConnect(Session session) {
        IoUtil.close(execInputStream);
        if (channelExec != null) {
            channelExec.disconnect();
        }
        if (session != null) {
            session.disconnect();
        }
    }


    // *-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*

    /**
     * 网络设备 -- jsch-Shell
     */
    // 对外暴露的统一登录、执行命令、解析响应方法
    public JSONObject doSshShellExecute(SshConnection sshConnection, String[] cmds) {
        return doSshShellExecute(sshConnection, cmds, true);
    }

    /**
     * 网络设备 -- jsch-Shell
     */
    // 对外暴露的统一登录、执行命令、解析响应方法
    public JSONObject doSshShellExecute(SshConnection sshConnection, String[] cmds, boolean isConfigBack) {
        JSONObject resultObj = new JSONObject();
        Boolean flag = true;
        Session session = null;
        StringBuilder resultSb = new StringBuilder("");
        try {
            // 初始化Jsch_Shell通道连接
            session = JschUtil.openSession(sshConnection.getHostname(), sshConnection.getPort(), sshConnection.getUsername(), sshConnection.getPassword(), sshConnection.getTimeout());
            initShellConnect(session);

            Long awaitMillis = Optional.ofNullable(sshConnection.getShellAwaitTime()).orElse(5000L);

            // 循环执行命令
            for (String cmd : cmds) {
                String resStr = sendShellCommand(shellInputStream, shellCommander, cmd.trim(), awaitMillis);
                resultSb.append(resStr);

                // 截取FTP命令，处理超时连接
                if (isConfigBack && cmd.trim().startsWith("ftp") && !resStr.contains("Connected")) {
                    resultSb.append("\r\nError: Connect to the remote host time out.\r\n");
                    flag = false;
                    break;
                }

                if (isConfigBack && (resStr.contains("Error") || resStr.contains("error"))) {
                    flag = false;
                    break;
                }

/*                if (channelExec != null) {
                    int exitStatus = channelExec.getExitStatus();
                    System.out.println("Exit status: " + exitStatus);

                    if (exitStatus != 0) {
                        flag = false;
                        break;
                    }
                }*/
            }

            if (flag) {
                resultObj.put("resultFlag", true);
            } else {
                resultObj.put("resultFlag", false);
            }
            resultObj.put("resultMsg", resultSb.toString().replaceAll("\r\n", "<br/>"));

        } catch (Exception e) {
            log.error("SSHUtils执行命令异常！", e);
            resultObj.put("resultFlag", false);
            resultObj.put("resultMsg", e.getMessage());
        } finally {
            // 关闭连接
            disShellConnect(session);
        }

        return resultObj;
    }

    // 初始化Jsch_Shell通道连接
    public void initShellConnect(Session session) {
        try {
            //将原有最大缓冲区容量默认32*1024 修改为配置字典中指定值
            Object valueByKey = cacheUtils.getValueByKey(CommonConstant.DATA_DICT_KEY + "shell_streamMaxInputBufferSize");
            if (!Objects.isNull(valueByKey) && StringUtils.isNotEmpty(String.valueOf(valueByKey).trim())) {
                session.setConfig("max_input_buffer_size", String.valueOf(valueByKey).trim());
            }
            session.setConfig("StrictHostKeyChecking", "no");
            //打开shell通道
            channelShell = session.openChannel(ChannelType.SHELL.getValue());
            //连接
            channelShell.connect();
            channelShell.setOutputStream(System.out);
            //输入流
            shellInputStream = channelShell.getInputStream();
            //输出流
            shellCommander = new PrintStream(channelShell.getOutputStream(), true);
        } catch (Exception e) {
            log.error("初始化SSH连接异常！", e);
        }
    }

    // Shell 发送命令,返回响应
    public String sendShellCommand(InputStream inputStream, PrintStream commander, String command, Long awaitMillis) {
        //连接等待
        awaitShell(inputStream, awaitMillis);
        //执行命令
        commander.println(command);
        //执行等待
        awaitShell(inputStream, awaitMillis);
        //转换为String
        String resultStr = readShellUtil(inputStream);
        return resultStr;
    }

    // Shell 动态等待响应
    public void awaitShell(InputStream inputStream, Long awaitMillis) {
        try {
            int available = inputStream.available();
            long l = System.currentTimeMillis();
            while (true) {
                if (available != inputStream.available()) {
                    available = inputStream.available();
                    Thread.sleep(1000L);
                    l = System.currentTimeMillis();
                } else {
                    if (System.currentTimeMillis() - l >= awaitMillis) {
                        break;
                    }
                }
            }
        } catch (IOException | InterruptedException e) {
            log.error("Shell 动态等待响应异常！", e);
        }
    }

    // Shell 输入流解析
    public String readShellUtil(InputStream inputStream) {
        StringBuffer sb = new StringBuffer();
        byte[] buf = new byte[4096];
        int len = 0;
        try {
            while (true) {
                if (inputStream.available() != 0) {
                    while ((len = inputStream.read(buf)) != -1) {
                        String s = new String(buf, 0, len);
                        sb.append(s);
                        if (inputStream.available() == 0) {
                            break;
                        }
                    }
                } else {
                    break;
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return sb.toString();
    }

    // 关闭Shell连接
    public void disShellConnect(Session session) {
        IoUtil.close(shellCommander);
        IoUtil.close(shellInputStream);
        if (channelShell != null) {
            channelShell.disconnect();
        }
        if (session != null) {
            session.disconnect();
        }
    }


    public static void main(String[] args) throws IOException, JSchException {

        /**
         * 网络设备
         */
        // 执行普通命令，查看目录
        new SshUtils().doSshShellExecute(new SshConnection("************35", 22, "root", "Admin@135", 5000, 5000L), new String[]{"lspci -vvv", "cat /etc/os-version"}, false);
        // 备份
//        System.out.println(doSshShellExecute(new SshConnection("************", 22, "admin", "yq20220731", 10000), new String[]{"ftp **************", "yq", "yq123456", "bin", "put vrpcfg.zip aa_vrpcfg.zip", "bye"}));
        // 还原
//        System.out.println(doSshShellExecute(new SshConnection("************", 22, "admin", "yq20220731", 10000), new String[]{"ftp **************", "yq", "yq123456", "bin", "get yq_license.lic yq_license_test.lic", "bye"}));

    }
}
