package com.yuanqiao.insight.common.util.ping;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @title: PingInfo
 * @projectName jeecg-boot-parent
 * @description: TODO
 * @date 2021/3/15-15:11
 */
@ApiModel(value="PingInfo对象", description="Ping测试对象")
public class PingInfo {
    @ApiModelProperty(value = "IP")
    private String ip; // ip
    @ApiModelProperty(value = "发送次数")
    private Integer pingCount = 4;
    @ApiModelProperty(value = "超时时间")
    private Integer timeOut = 100;
    @ApiModelProperty(value = "数据包大小")
    private Integer pingPacklen = 32;

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getPingCount() {
        return pingCount;
    }

    public void setPingCount(Integer pingCount) {
        this.pingCount = pingCount;
    }

    public Integer getTimeOut() {
        return timeOut;
    }

    public void setTimeOut(Integer timeOut) {
        this.timeOut = timeOut;
    }

    public Integer getPingPacklen() {
        return pingPacklen;
    }

    public void setPingPacklen(Integer pingPacklen) {
        this.pingPacklen = pingPacklen;
    }
}
