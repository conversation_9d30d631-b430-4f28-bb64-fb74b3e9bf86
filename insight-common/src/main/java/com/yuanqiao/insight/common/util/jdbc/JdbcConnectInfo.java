package com.yuanqiao.insight.common.util.jdbc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @title: JdbcUtil
 * @projectName jeecg-boot-parent
 * @description: TODO
 * @date 2021/3/16-16:22
 */
@ApiModel(value="JdbcInfo对象", description="jdbc测试对象")
public class JdbcConnectInfo {
    @ApiModelProperty(value = "IP")
    private String ip; // ip
    @ApiModelProperty(value = "类型")
    private String type;//类型
    @ApiModelProperty(value = "端口")
    private String port;//端口
    @ApiModelProperty(value = "数据实例")
    private String db;//数据实例
    @ApiModelProperty(value = "用户名")
    private String dbUName;//用户名
    @ApiModelProperty(value = "密码")
    private String  dbPwd;//密码

    public String getSql() {
        return sql;
    }

    public void setSql(String sql) {
        this.sql = sql;
    }

    @ApiModelProperty(value = "自定义sql")
    private String sql;

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public String getDb() {
        return db;
    }

    public void setDb(String db) {
        this.db = db;
    }

    public String getDbUName() {
        return dbUName;
    }

    public void setDbUName(String dbUName) {
        this.dbUName = dbUName;
    }

    public String getDbPwd() {
        return dbPwd;
    }

    public void setDbPwd(String dbPwd) {
        this.dbPwd = dbPwd;
    }
}
