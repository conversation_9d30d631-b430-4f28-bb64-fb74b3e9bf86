package com.yuanqiao.insight.common.util.ssh;

import lombok.Data;

@Data
public class ConnectParamVo {

    private String protocol;
    private String hostname;
    private Integer port;
    private String sshUsername;
    private String sshPassword;
    private String sftpUsername;
    private String sftpPassword;
    private Integer timeout;
    private Long shellAwaitTime;
    private String errorMsg;

    public ConnectParamVo() {}

    public ConnectParamVo(String protocol, String hostname, String sshUsername, String sshPassword, String sftpUsername, String sftpPassword, Integer port, Integer timeout, Long shellAwaitTime) {
        this.protocol = protocol;
        this.hostname = hostname;
        this.port = port;
        this.sshUsername = sshUsername;
        this.sshPassword = sshPassword;
        this.sftpUsername = sftpUsername;
        this.sftpPassword = sftpPassword;
        this.timeout = timeout;
        this.shellAwaitTime = shellAwaitTime;
    }

}

