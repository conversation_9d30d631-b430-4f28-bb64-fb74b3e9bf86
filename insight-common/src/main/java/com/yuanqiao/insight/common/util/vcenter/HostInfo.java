package com.yuanqiao.insight.common.util.vcenter;

import lombok.Getter;
import lombok.Setter;

/**
 * Vcenter主机信息
 */
@Getter
@Setter
public class HostInfo {
    private String id;
    private String name;
    //操作系统
    private String osType;
    //cpu型号
    private String cpuModel;
    //硬件型号
    private String model;
    //硬件制造商
    private String vendor;
    //CPU核数
    private String cpuCores;
    //电源状态
    private String powerState;
    //设备uuid
    private String uuid;
    //每 CPU 速度（MHz）
    private String cpuMhz;
    //网卡数
    private String numNics;
    //主机CPU处理频率
    private double overallCpu;
    //主机内存大小
    private double memSize;
    //主机内存已用
    private double overallMem;
    //主机内存使用率
    private double cpuUsage;
    //主机内存使用率
    private double memUsage;
    //主机磁盘使用率
    private double diskUsage;
    //启动时间
    private String runTime;
    //cpu已用GHz
    private double cpuUse;
    //cpu可用GHz
    private double cpuFree;
    //cpu容量GHz
    private double cpuTotal;
    //内存已用GB
    private double memUse;
    //内存可用GB
    private double memFree;
    //内存容量GB
    private double memTotal;
}
