package com.yuanqiao.insight.common.util.common;

public class RespUtil {

    // 构建 RESP 命令
    public static String createCommand(String command, String... args) {
        StringBuilder sb = new StringBuilder();
        sb.append("*").append(args.length + 1).append("\r\n"); // 参数个数
        sb.append("$").append(command.length()).append("\r\n").append(command).append("\r\n"); // 命令

        for (String arg : args) {
            sb.append("$").append(arg.length()).append("\r\n").append(arg).append("\r\n"); // 参数
        }
        return sb.toString();
    }

    // 示例：解析 RESP 响应
    public static String parseResponse(String response) {
        if (response.startsWith("+")) { // 状态响应
            return response.substring(1).trim();
        } else if (response.startsWith("$")) { // 字符串响应
            String[] parts = response.split("\r\n");
            if (parts.length > 1 && !parts[1].equals("-1")) {
                return parts[1];
            }
            return ""; // 空字符串
        } else if (response.startsWith("-")) { // 错误响应
            throw new RuntimeException("Redis error: " + response.substring(1).trim());
        } else {
            throw new IllegalArgumentException("Unknown response type: " + response);
        }
    }

    public static void main(String[] args) {
        String command = createCommand("get", "insight_empowerNumber");
        System.out.println("RESP Command: " + command);

        // 示例响应（假设来自 Redis 的响应）
        String exampleResponse = "\"252a56c3fa54e3d1f5caf0067d0938e5\"";
        String parsedResponse = parseResponse(exampleResponse);
        System.out.println("Parsed Response: " + parsedResponse);
    }
}