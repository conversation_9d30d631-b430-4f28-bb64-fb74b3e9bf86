package com.yuanqiao.insight.common.filter;

import com.alibaba.fastjson.serializer.ValueFilter;

import java.math.BigDecimal;

public class BigDecimalValueFilter implements ValueFilter {
    @Override
    public Object process(Object o, String name, Object value) {
        //o是待转换的对象，name是字段名，value是字段值
        if (null != value && value instanceof BigDecimal) {
            return ((BigDecimal) value).setScale(3).toString();
        }
        return value;
    }
}