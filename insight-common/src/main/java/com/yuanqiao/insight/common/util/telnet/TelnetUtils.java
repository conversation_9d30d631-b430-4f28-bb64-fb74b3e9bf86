package com.yuanqiao.insight.common.util.telnet;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.telnet.TelnetClient;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.PrintStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Telnet操作器,基于commons-net-2.2.jar
 */
@Slf4j
@Component
public class TelnetUtils {

    private static PrintStream out = null; // 向服务器写入 命令
    private static TelnetClient telnet = null;

    /**
     * 对外暴露的统一登录、执行命令、解析响应方法
     *
     * @param telnetInfo
     * @return
     */
    public static JSONObject doTelnetExecute(TelnetInfo telnetInfo) {
        JSONObject resultObj = new JSONObject();
        Boolean flag = true;

        StringBuilder respondSb = new StringBuilder("");
        respondSb.append("\r\n");
        try {
            // 创建telnet连接
            JSONObject connectObject = initConnect(telnetInfo.getTimeout(), telnetInfo.getIp(), telnetInfo.getPort(), telnetInfo.getUsername(), telnetInfo.getPassword());

            // 登录成功后执行命令
            if (connectObject.getBoolean("resultFlag")) {
                String commandStr = telnetInfo.getCommand();
                if (StringUtils.isNotEmpty(commandStr)) {
                    String[] cmds = commandStr.split("&&");
                    for (int i = 0; i < cmds.length; i++) {
                        //截取put命令，获取备份目标路径
                        if (cmds[i].trim().startsWith("put")) {
                            String[] split1 = cmds[i].trim().split(" ");
                            resultObj.put("configureFile", split1[split1.length - 1].trim());
                        }

                        // 发送命令，返回响应
                        String respondStr = sendCommand(cmds[i].trim());
                        respondSb.append(respondStr);

                        // 截取FTP命令，处理超时连接
                        if (cmds[i].trim().startsWith("ftp") && !respondStr.contains("Connected")) {
                            respondSb.append("\r\nError: Connect to the remote host time out.\r\n");
                            flag = false;
                            break;
                        }

                        if (respondStr.contains("Error") || respondStr.contains("error")) {
                            flag = false;
                            break;
                        }
                    }

                    if (flag) {
                        resultObj.put("resultFlag", true);
                    } else {
                        resultObj.put("resultFlag", false);
                    }
                    resultObj.put("resultMsg", respondSb.toString().replaceAll("\r\n", "<br/>"));

                }
            } else {
                resultObj.put("resultFlag", false);
                resultObj.put("resultMsg", connectObject.getString("resultMsg").replaceAll("\r\n", "<br/>"));
            }
        } catch (Exception e) {
            log.error("TelnetUtils 登录、执行命令、解析响应异常！", e);
            resultObj.put("resultFlag", false);
            resultObj.put("resultMsg", e.getMessage());
        } finally {
            // 关闭连接
            disConnect();
        }

        return resultObj;
    }

    /**
     * 初始化连接
     *
     * @param timeout
     * @param ip
     * @param port
     */
    public static JSONObject initConnect(Integer timeout, String ip, Integer port, String username, String password) {
        JSONObject connectObject = new JSONObject();
        StringBuilder sb = new StringBuilder("");
        try {
            telnet = new TelnetClient();
            telnet.setDefaultTimeout(timeout);
            telnet.connect(ip, port);
            out = new PrintStream(telnet.getOutputStream());

            sb.append(readUtil()).append("\r\n");
            write(username);
            sb.append(readUtil()).append("\r\n");
            write(password);
            sb.append(readUtil()).append("\r\n");
            if (StringUtils.isEmpty(sb.toString()) || !sb.toString().contains("last login information")) {
                connectObject.put("resultFlag", false);
                connectObject.put("resultMsg", sb.toString());
            } else {
                connectObject.put("resultFlag", true);
                connectObject.put("resultMsg", sb.toString());
            }
        } catch (Exception e) {
            log.error("初始化Telnet连接异常！", e);
            connectObject.put("resultFlag", false);
            connectObject.put("resultMsg", e.getMessage());
        }
        return connectObject;
    }

    /**
     * su切换用户
     *
     * @param password
     */
    public static boolean su(String user, String password) {
        Boolean bool = true;
        try {
            write("su" + " - " + user);
            readUtil();
            write(password);
            String rs = readUtil();
            if (rs != null && !rs.contains("[" + user + "@")) {
                log.error("su - " + user + " 失败！");
                bool = false;
            }
        } catch (Exception e) {
            bool = false;
            log.error("su - " + user + " 异常！", e);
        }
        return bool;
    }

    /**
     * 发送命令,返回执行结果
     *
     * @param command
     * @return
     */
    public static String sendCommand(String command) {
        try {
            write(command);
            return readUtil();
        } catch (Exception e) {
            log.error("TelnetUtils 发送命令,返回执行结果异常！", e);
            return e.getMessage();
        }
    }

    /**
     * 写操作
     *
     * @param value
     */
    public static void write(String value) {
        try {
            out.println(value);
            out.flush();
        } catch (Exception e) {
            log.error("TelnetUtils 内容写入服务端异常！", e);
        }
    }

    /**
     * 读取分析响应结果
     *
     * @return
     */
    public static String readUtil() {
        StringBuffer sb = new StringBuffer();
        try {
            byte[] buf = new byte[4096];
            int len = 0;
            Thread.sleep(750L);
            while ((len = telnet.getInputStream().read(buf)) != -1) {
                String s = new String(buf, 0, len);
                sb.append(s);
                Thread.sleep(750L);
                if (telnet.getInputStream().available() == 0) {
                    break;
                }
            }
        } catch (Exception e) {
            log.error("TelnetUtils 读取分析响应结果异常！", e);
            return e.getMessage();
        }
        return sb.toString();
    }

    /**
     * 关闭连接
     */
    public static void disConnect() {
        try {
            if (telnet != null && telnet.isConnected()) {
                telnet.disconnect();
            }
        } catch (Exception e) {

        }
    }


    /**
     * &&-表示前面命令执行成功在执行后面命令; ||表示前面命令执行失败了在执行后面命令; ";"表示一次执行两条命令
     * <p>
     * 由于安全考虑，telnet不允许root用户直接登录，需要您先建立一个普通用户，使用这个用户登录，再转换到root用户。 su - root
     * 为了测试的方便，也可以允许root用户登录，但是最好不要对公网开放使用 -- vim /etc/pam.d/login 将文件中的 auth required pam_securetty.so 加上“#”注释掉，就可以root用户登录
     * </p>
     *
     * @param args
     */
    public static void main(String[] args) throws IOException {

        HashMap<String, String> map = new HashMap<>();
        map.put("a", "1");
        map.put("b", "2");
        map.put("c", "3");

        ArrayList<String> list = new ArrayList<>();
        list.add("a");
        list.add("b");

        List<String> listMapCommonElement = list.stream().filter(l -> map.entrySet().stream().anyMatch(m -> m.getKey().equals(l))).collect(Collectors.toList());
        System.out.println(listMapCommonElement);

        Map<String, String> mapListCommonElement = map.entrySet().stream().filter(m -> list.stream().anyMatch(l -> l.equals(m.getKey()))).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        System.out.println(mapListCommonElement);

//        TelnetInfo telnetInfo = new TelnetInfo("192.168.16.1", 23, 10000, "admin", "yq20220731", "dis cur");
//        JSONObject resultObj = TelnetUtils.doTelnetExecute(telnetInfo);
//        System.out.println(resultObj.getBoolean("resultFlag"));
//        System.out.println(" == ** == ** == ");
//        System.out.println(resultObj.getString("resultMsg"));

    }


}
