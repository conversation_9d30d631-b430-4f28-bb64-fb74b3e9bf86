package com.yuanqiao.insight.common.constant;

/**
 * <AUTHOR>
 * @title: CommonConstant
 * @projectName jeecg-boot-parent
 * @description: TODO
 * @date 2021/3/16-16:33
 */
public interface CommonConstant {
    //
    String ORACLE_DRIVER = "oracle.jdbc.driver.OracleDriver";

    String DB2_DRIVER = "com.ibm.db2.jcc.DB2Driver";

    String MYSQL_DRIVER = "com.mysql.cj.jdbc.Driver";

    String SQLSERVER_DRIVER = "net.sourceforge.jtds.jdbc.Driver";

    String SYBASE_DRIVER = "net.sourceforge.jtds.jdbc.Driver";

    String KB_DRIVER = "com.kingbase.Driver";

    String KB8_DRIVER = "com.kingbase8.Driver";

    String DM_DRIVER = "dm.jdbc.driver.DmDriver";

    String ST_DRIVER = "com.oscar.Driver";

    String HIGHGO_DRIVER = "com.highgo.jdbc.Driver";

    String GBASE_DRIVER = "com.gbasedbt.jdbc.Driver";
    //工单状态(0:待分配,1:待处理,2已处理:)
    Integer DEVOPS_ORDER_INFO_STATE_0 = 0;
    Integer DEVOPS_ORDER_INFO_STATE_1 = 1;
    Integer DEVOPS_ORDER_INFO_STATE_2 = 2;

    String DEVOPS_ORDER_INFO_STATE_CH0 = "未分配";
    String DEVOPS_ORDER_INFO_STATE_CH1 = "待处理";
    String DEVOPS_ORDER_INFO_STATE_CH2 = "已处理";

    String DEVOPS_ORDER_INFO_SOURCE_1 = "1";
    String DEVOPS_ORDER_INFO_SOURCE_2 = "2";
    String DEVOPS_ORDER_INFO_SOURCE_3 = "3";

    String DEVOPS_ORDER_INFO_SOURCE_TEXT_1 = "人工报障";
    String DEVOPS_ORDER_INFO_SOURCE_TEXT_2 = "设备告警";
    String DEVOPS_ORDER_INFO_SOURCE_TEXT_3 = "系统管理基础平台";



    //排班管理的状态 (0:未排班,1:已排班)
    Integer ARRANGEMENT_STATUS_UNSCHEDULED = 0;
    Integer ARRANGEMENT_STATUS_SCHEDULED = 1;

    String ARRANGEMENT_STATUS_UNSCHEDULED_NAME = "未排班";
    //值班状态（0:未值班 1:值班中 2:已值班 3:未打卡）
    Integer ARRANGEMENT_SHIFT_STATUS_NOT_ON_DUTY = 0;
    Integer ARRANGEMENT_SHIFT_STATUS_ON_DUTY = 1;
    Integer ARRANGEMENT_SHIFT_STATUS_END_DUTY = 2;
    Integer ARRANGEMENT_SHIFT_STATUS_NO_RECORD = 3;

    String ARRANGEMENT_SHIFT_STATUS_NOT_ON_DUTY_NAME = "未值班";
    String ARRANGEMENT_SHIFT_STATUS_ON_DUTY_NAME = "值班中";
    String ARRANGEMENT_SHIFT_STATUS_END_DUTY_NAME = "已值班";
    String ARRANGEMENT_SHIFT_STATUS_NO_RECORD_NAME = "未打卡";

    //离线
    String DEVICE_STATUS_OFFLINE = "0";
    //在线
    String DEVICE_STATUS_ONLINE = "1";
    //告警
    String DEVICE_STATUS_ALARM = "2";
    //
    String DEVICE_STATUS_ALARM_COMM = "10";//一般告警

    String DEVICE_STATUS_ALARM_SER = "20";//严重告警


    String TASK_STATUS_FULFIL = "1";//完成
    String TASK_STATUS_FAIL = "2";//失败
    String TASK_STATUS_FULFIL_TO_START = "3";//待开始
    String TASK_STATUS_FULFIL_EXECUTING = "4";//执行中


    String TASK_FILE_SUFFIX="xls";

    String TASK_EXECUTE_TYPE_1="1";
    String TASK_EXECUTE_TYPE_TEXT_1 = "周期执行";
    String TASK_EXECUTE_TYPE_2="2";
    String TASK_EXECUTE_TYPE_TEXT_2 = "单次执行";

    //ssh
    String RESOURCE_CONTYPE_SSH = "1";
    //wind
    String RESOURCE_CONTYPE_TELNET = "2";

    //工单统计维度  d:日  w:星期  m:月  y:年
    String ORDER_STATDIMEN_D ="d";
    String ORDER_STATDIMEN_W ="w";
    String ORDER_STATDIMEN_M ="m";
    String ORDER_STATDIMEN_Y ="y";

    /**
     * 正常状态
     */
    public static final Integer STATUS_NORMAL = 0;

    /**
     * 禁用状态
     */
    public static final Integer STATUS_DISABLE = -1;

    /**
     * 删除标志
     */
    public static final Integer DEL_FLAG_1 = 1;

    /**
     * 未删除
     */
    public static final Integer DEL_FLAG_0 = 0;

    /**
     * 系统日志类型： 登录
     */
    public static final int LOG_TYPE_1 = 1;

    /**
     * 系统日志类型： 操作
     */
    public static final int LOG_TYPE_2 = 2;

    /**
     * 操作日志类型： 查询
     */
    public static final int OPERATE_TYPE_1 = 1;

    /**
     * 操作日志类型： 添加
     */
    public static final int OPERATE_TYPE_2 = 2;

    /**
     * 操作日志类型： 更新
     */
    public static final int OPERATE_TYPE_3 = 3;

    /**
     * 操作日志类型： 删除
     */
    public static final int OPERATE_TYPE_4 = 4;

    /**
     * 操作日志类型： 倒入
     */
    public static final int OPERATE_TYPE_5 = 5;

    /**
     * 操作日志类型： 导出
     */
    public static final int OPERATE_TYPE_6 = 6;


    /** {@code 500 Server Error} (HTTP/1.0 - RFC 1945) */
    public static final Integer SC_INTERNAL_SERVER_ERROR_500 = 500;
    /** {@code 200 OK} (HTTP/1.0 - RFC 1945) */
    public static final Integer SC_OK_200 = 200;

    /**访问权限认证未通过 510*/
    public static final Integer SC_JEECG_NO_AUTHZ=510;

    /** 登录用户Shiro权限缓存KEY前缀 */
    public static String PREFIX_USER_SHIRO_CACHE  = "shiro:cache:org.jeecg.config.shiro.ShiroRealm.realm.authorizationCache:";
    /** 登录用户Token令牌缓存KEY前缀 */
    public static final String PREFIX_USER_TOKEN  = "prefix_user_token_";
    /** Token缓存时间：3600秒即一小时 */
    public static final int  TOKEN_EXPIRE_TIME  = 3600;


    /**
     *  0：一级菜单
     */
    public static final Integer MENU_TYPE_0  = 0;
    /**
     *  1：子菜单
     */
    public static final Integer MENU_TYPE_1  = 1;
    /**
     *  2：按钮权限
     */
    public static final Integer MENU_TYPE_2  = 2;

    /**通告对象类型（USER:指定用户，ALL:全体用户）*/
    public static final String MSG_TYPE_UESR  = "USER";
    public static final String MSG_TYPE_ALL  = "ALL";

    /**发布状态（0未发布，1已发布，2已撤销）*/
    public static final String NO_SEND  = "0";
    public static final String HAS_SEND  = "1";
    public static final String HAS_CANCLE  = "2";

    /**阅读状态（0未读，1已读）*/
    public static final String HAS_READ_FLAG  = "1";
    public static final String NO_READ_FLAG  = "0";

    /**优先级（L低，M中，H高）*/
    public static final String PRIORITY_L  = "L";
    public static final String PRIORITY_M  = "M";
    public static final String PRIORITY_H  = "H";

    /**
     * 短信模板方式  0 .登录模板、1.注册模板、2.忘记密码模板
     */
    public static final String SMS_TPL_TYPE_0  = "0";
    public static final String SMS_TPL_TYPE_1  = "1";
    public static final String SMS_TPL_TYPE_2  = "2";

    /**
     * 状态(0无效1有效)
     */
    public static final String STATUS_0 = "0";
    public static final String STATUS_1 = "1";

    /**
     * 同步工作流引擎1同步0不同步
     */
    public static final Integer ACT_SYNC_1 = 1;
    public static final Integer ACT_SYNC_0 = 0;

    /**
     * 消息类型1:通知公告2:系统消息
     */
    public static final String MSG_CATEGORY_1 = "1";
    public static final String MSG_CATEGORY_2 = "2";

    /**
     * 是否配置菜单的数据权限 1是0否
     */
    public static final Integer RULE_FLAG_0 = 0;
    public static final Integer RULE_FLAG_1 = 1;

    /**
     * 是否用户已被冻结 1正常(解冻) 2冻结
     */
    public static final Integer USER_UNFREEZE = 1;
    public static final Integer USER_FREEZE = 2;

    /**字典翻译文本后缀*/
    public static final String DICT_TEXT_SUFFIX = "_dictText";

    /**
     * 表单设计器主表类型
     */
    public static final Integer DESIGN_FORM_TYPE_MAIN = 1;

    /**
     * 表单设计器子表表类型
     */
    public static final Integer DESIGN_FORM_TYPE_SUB = 2;

    /**
     * 表单设计器URL授权通过
     */
    public static final Integer DESIGN_FORM_URL_STATUS_PASSED = 1;

    /**
     * 表单设计器URL授权未通过
     */
    public static final Integer DESIGN_FORM_URL_STATUS_NOT_PASSED = 2;

    /**
     * 表单设计器新增 Flag
     */
    public static final String DESIGN_FORM_URL_TYPE_ADD = "add";
    /**
     * 表单设计器修改 Flag
     */
    public static final String DESIGN_FORM_URL_TYPE_EDIT = "edit";
    /**
     * 表单设计器详情 Flag
     */
    public static final String DESIGN_FORM_URL_TYPE_DETAIL = "detail";
    /**
     * 表单设计器复用数据 Flag
     */
    public static final String DESIGN_FORM_URL_TYPE_REUSE = "reuse";
    /**
     * 表单设计器编辑 Flag （已弃用）
     */
    public static final String DESIGN_FORM_URL_TYPE_VIEW = "view";

    /**
     * online参数值设置（是：Y, 否：N）
     */
    public static final String ONLINE_PARAM_VAL_IS_TURE = "Y";
    public static final String ONLINE_PARAM_VAL_IS_FALSE = "N";

    /**
     * 文件上传类型（本地：local，Minio：minio，阿里云：alioss）
     */
    public static final String UPLOAD_TYPE_LOCAL = "local";
    public static final String UPLOAD_TYPE_MINIO = "minio";
    public static final String UPLOAD_TYPE_OSS = "alioss";

    /**
     * 文档上传自定义桶名称
     */
    public static final String UPLOAD_CUSTOM_BUCKET = "eoafile";
    /**
     * 文档上传自定义路径
     */
    public static final String UPLOAD_CUSTOM_PATH = "eoafile";
    /**
     * 文件外链接有效天数
     */
    public static final Integer UPLOAD_EFFECTIVE_DAYS = 1;

    /**
     * 员工身份 （1:普通员工  2:上级）
     */
    public static final Integer USER_IDENTITY_1 = 1;
    public static final Integer USER_IDENTITY_2 = 2;

    /** sys_user 表 username 唯一键索引 */
    public static final String SQL_INDEX_UNIQ_SYS_USER_USERNAME = "uniq_sys_user_username";
    /** sys_user 表 work_no 唯一键索引 */
    public static final String SQL_INDEX_UNIQ_SYS_USER_WORK_NO = "uniq_sys_user_work_no";
    /** sys_user 表 phone 唯一键索引 */
    public static final String SQL_INDEX_UNIQ_SYS_USER_PHONE = "uniq_sys_user_phone";
    /** sys_user 表 email 唯一键索引 */
    public static final String SQL_INDEX_UNIQ_SYS_USER_EMAIL = "uniq_sys_user_email";
    /** sys_quartz_job 表 job_class_name 唯一键索引 */
    public static final String SQL_INDEX_UNIQ_JOB_CLASS_NAME = "uniq_job_class_name";
    /** sys_position 表 code 唯一键索引 */
    public static final String SQL_INDEX_UNIQ_CODE = "uniq_code";
    /** sys_role 表 code 唯一键索引 */
    public static final String SQL_INDEX_UNIQ_SYS_ROLE_CODE = "uniq_sys_role_role_code";
    /** sys_depart 表 code 唯一键索引 */
    public static final String SQL_INDEX_UNIQ_DEPART_ORG_CODE = "uniq_depart_org_code";
    /**
     * 在线聊天 是否为默认分组
     */
    public static final String IM_DEFAULT_GROUP = "1";
    /**
     * 在线聊天 图片文件保存路径
     */
    public static final String IM_UPLOAD_CUSTOM_PATH = "imfile";
    /**
     * 在线聊天 用户状态
     */
    public static final String IM_STATUS_ONLINE = "online";

    /**
     * 在线聊天 SOCKET消息类型
     */
    public static final String IM_SOCKET_TYPE = "chatMessage";

    /**
     * 在线聊天 是否开启默认添加好友 1是 0否
     */
    public static final String IM_DEFAULT_ADD_FRIEND = "1";

    /**
     * 在线聊天 用户好友缓存前缀
     */
    public static final String IM_PREFIX_USER_FRIEND_CACHE = "im_prefix_user_friend_";

    /**
     * 考勤补卡业务状态 （1：同意  2：不同意）
     */
    public static final String SIGN_PATCH_BIZ_STATUS_1 = "1";
    public static final String SIGN_PATCH_BIZ_STATUS_2 = "2";

    /**
     * 公文文档上传自定义路径
     */
    public static final String UPLOAD_CUSTOM_PATH_OFFICIAL = "officialdoc";
    /**
     * 公文文档下载自定义路径
     */
    public static final String DOWNLOAD_CUSTOM_PATH_OFFICIAL = "officaldown";

    /**
     * WPS存储值类别(1 code文号 2 text（WPS模板还是公文发文模板）)
     */
    public static final String WPS_TYPE_1="1";
    public static final String WPS_TYPE_2="2";


    public final static String X_ACCESS_TOKEN = "X-Access-Token";

    /**
     * 多租户 请求头
     */
    public final static String TENANT_ID = "tenant_id";

    /**
     * 微服务读取配置文件属性 服务地址
     */
    public final static String CLOUD_SERVER_KEY = "spring.cloud.nacos.discovery.server-addr";

    /**
     * 第三方登录 验证密码/创建用户 都需要设置一个操作码 防止被恶意调用
     */
    public final static String THIRD_LOGIN_CODE = "third_login_code";

    //数据字典存储键
    public static final String DATA_DICT_KEY = "data_dict_key_";

    String ZY_ACCESS_TOKEN = "sys:zy:access_token";

    String ZJG_ACCESS_TOKEN = "sys:zjg:access_token";

    String ZJG_TOKEN = "sys:zjg:token:";

    String SOFTWARE_QUEUE_KEY = "opmg:software:event:queue:";
}
