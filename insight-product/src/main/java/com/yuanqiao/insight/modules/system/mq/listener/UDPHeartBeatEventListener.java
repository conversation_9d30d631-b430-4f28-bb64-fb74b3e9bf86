package com.yuanqiao.insight.modules.system.mq.listener;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.monitoring.modules.device.hearbeat.UDPHeartBeat;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.mq.aspect.annotation.RedisMessageExtend;
import org.jeecg.common.mq.stream.Streams;
import org.jeecg.common.mq.utils.AnalyzeContent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.stream.MapRecord;
import org.springframework.data.redis.stream.StreamListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Map;


@Slf4j
@Component
public class UDPHeartBeatEventListener implements StreamListener<String, MapRecord<String, String, String>> {
    @Autowired
    private UDPHeartBeat udpHeartBeat;

    @Override
    @Async("stream-core-pool")
    @RedisMessageExtend(stream = Streams.UDP_HEARTBEAT)
    public void onMessage(MapRecord<String, String, String> message) {
        log.info("调用处理心跳事件的方法...");
        final Map<String, String> value = message.getValue();
        JSONObject jsonObject = AnalyzeContent.getContent(value);
        udpHeartBeat.reLoadJob(jsonObject);
    }
}
