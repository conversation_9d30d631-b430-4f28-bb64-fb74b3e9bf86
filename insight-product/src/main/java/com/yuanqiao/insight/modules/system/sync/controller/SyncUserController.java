package com.yuanqiao.insight.modules.system.sync.controller;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yuanqiao.insight.modules.system.sync.entity.SyncUser;
import lombok.AllArgsConstructor;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.system.entity.SysRole;
import org.jeecg.modules.system.entity.SysUserRole;
import org.jeecg.modules.system.entity.SysUsers;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@AllArgsConstructor
@RequestMapping("/sync/user")
public class SyncUserController {

    private final ISysUserService userService;

    @RequestMapping("/getAllUser")
    public Result<?> getAllUser() {
        List<SyncUser> syncUserList = userService.selectJoinList(SyncUser.class, new MPJLambdaWrapper<SysUsers>()
                .select(SysUsers::getUsername)
                .selectAs(SysUsers::getRealname, SyncUser::getRealName)
                .selectCollection(SysRole.class
                        , SyncUser::getRoles
                        , map -> map.result(SysRole::getRoleName)
                                .result(SysRole::getRoleCode))
                .leftJoin(SysUserRole.class, SysUserRole::getUserId, SysUsers::getId)
                .leftJoin(SysRole.class, SysRole::getId, SysUserRole::getRoleId));
        return Result.OK(syncUserList);
    }

}
