package com.yuanqiao.insight.modules.system.quartz.job;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.dataReport.util.DataReportUtils;
import com.yuanqiao.insight.common.util.common.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.SpringContextUtils;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 数据汇聚失败重试定时任务
 *
 * <AUTHOR>
 * @title: AlarmAssetsTaskJob
 * @projectName jeecg-boot-parent
 * @description: TODO
 * @date 2021/5/18
 */
@Slf4j
@Service
public class RetryReportTaskJob implements Job {

    @Autowired
    private DataReportUtils dataReportUtils;
    @Autowired
    private RedisUtils redisUtils;

    String reportErrorRecordKey = "REPORT:ERROR:RECORD";

    @Override
    @Async("quartz-core-pool")
    public void execute(JobExecutionContext context) {
        run();
    }

    public void run() {
//        System.out.println(" ----- 重试上报任务开始执行 ----- ");
        // 读取所有失败上报数据记录
        Map<String, Object> mapByPrefix = redisUtils.batchGetKVByPrefix(reportErrorRecordKey);
        Map<String, JSONObject> convertedMap = new HashMap<>();
        mapByPrefix.forEach((key, value) -> {
            convertedMap.put(key, JSONObject.parseObject(value.toString()));
        });
        List<Map.Entry<String, JSONObject>> entries = new ArrayList<>(convertedMap.entrySet());

        // 根据时间对失败记录Map排序（优先上报更新时间最新的数据，减少处理时的冗余修改）
        entries.sort((o1, o2) -> o2.getValue().getLong("timestamp").compareTo(o1.getValue().getLong("timestamp")));

        // 逐条重试上报
        for (Map.Entry<String, JSONObject> entry : entries) {
//            System.out.println(" ----- 重试上报 ：" + entry.getKey() + " --- " + entry.getValue().toJSONString());
            dataReportUtils.pushData(entry.getValue().toJSONString());
            redisUtils.del(entry.getKey());
        }
//        System.out.println(" ----- 重试上报任务执行完毕 ----- ");
    }

}
