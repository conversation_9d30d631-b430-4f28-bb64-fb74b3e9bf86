package com.yuanqiao.insight.modules.system.license.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.common.util.quartz.QuartzUtil;
import com.yuanqiao.insight.modules.system.license.entity.SysLicenseInfo;
import com.yuanqiao.insight.modules.system.license.mapper.SysLicenseInfoMapper;
import com.yuanqiao.insight.modules.system.license.service.ISysLicenseInfoService;
import lombok.RequiredArgsConstructor;
import org.jeecg.common.constant.CommonConstant;
import org.springframework.stereotype.Service;

/**
 * @Description: 授权信息
 * @Author: jeecg-boot
 * @Date:   2024-03-28
 * @Version: V1.0
 */
@Service
@RequiredArgsConstructor
public class SysLicenseInfoServiceImpl extends ServiceImpl<SysLicenseInfoMapper, SysLicenseInfo> implements ISysLicenseInfoService {


    private final QuartzUtil quartzUtil;

    private static final String JOB_CLASS_NAME = "com.yuanqiao.insight.modules.system.license.job.LicenseAlarmJob";

    @Override
    public void saveLicenseInfo(SysLicenseInfo sysLicenseInfo) {
        boolean success = this.save(sysLicenseInfo);
        if (success) {
            if (CommonConstant.STATUS_NORMAL.equals(Integer.parseInt(sysLicenseInfo.getTaskStatus()))) {
                quartzUtil.schedulerAdd(sysLicenseInfo.getId(), JOB_CLASS_NAME, sysLicenseInfo.getTaskCron().trim(), sysLicenseInfo.getId());
            }
        }
    }

    @Override
    public void updateLicenseInfo(SysLicenseInfo sysLicenseInfo) {
        if (CommonConstant.STATUS_NORMAL.equals(Integer.parseInt(sysLicenseInfo.getTaskStatus()))) {
            quartzUtil.schedulerDelete(sysLicenseInfo.getId());
            quartzUtil.schedulerAdd(sysLicenseInfo.getId(), JOB_CLASS_NAME, sysLicenseInfo.getTaskCron().trim(), sysLicenseInfo.getId());
        } else {
            quartzUtil.schedulerDelete(sysLicenseInfo.getId());
        }
        this.baseMapper.updateById(sysLicenseInfo);
    }

    @Override
    public void delete(String id) {
        quartzUtil.schedulerDelete(id);
        this.removeById(id);
    }

    @Override
    public void pauseJob(SysLicenseInfo sysLicenseInfo) {
        quartzUtil.schedulerDelete(sysLicenseInfo.getId());
        sysLicenseInfo.setTaskStatus(CommonConstant.STATUS_DISABLE + "");
        super.baseMapper.updateById(sysLicenseInfo);
    }

    @Override
    public void resumeJob(SysLicenseInfo sysLicenseInfo) {
        quartzUtil.schedulerDelete(sysLicenseInfo.getId());
        quartzUtil.schedulerAdd(sysLicenseInfo.getId(), JOB_CLASS_NAME, sysLicenseInfo.getTaskCron().trim(), sysLicenseInfo.getId());
        sysLicenseInfo.setTaskStatus(CommonConstant.STATUS_NORMAL + "");
        super.baseMapper.updateById(sysLicenseInfo);
    }
}
