package com.yuanqiao.insight.modules.system.license.msg;

/**
 * 返回状态码
 * <AUTHOR>
 *
 */
public interface MessageStatus {


	public static final int DEFAULT_SUCCESS_NUM=200;//默认成功码
	public static final int DEFAULT_INFO_NUM=3000;//默认提醒码
	public static final int DEFAULT_WARNING_NUM=6000;//默认警告码
	public static final int DEFAULT_ERROR_NUM=9999;//默认错误码

	public static final int NOT_TOKEN_NUM=1101;//没有token返回码


}
