package com.yuanqiao.insight.modules.system.license.common;

import com.yqit.insight.license.server.LicenseCheckModel;
import com.yuanqiao.insight.common.util.common.LocalCacheUtils;
import com.yuanqiao.insight.modules.system.license.license.CustomLicenseManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Order(value = 1)
public class LicenseCheckRunner implements ApplicationRunner {
    @Autowired
    private CustomLicenseManager customLicenseManager;
    @Autowired
    private LicenseInfoComponent licenseInfoComponent;
    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("项目启动，加载并安装证书......");
        //获取当前服务器硬件信息，保存至本地缓存
        LocalCacheUtils cacheUtils = LocalCacheUtils.getInstance();
        LicenseCheckModel serverInfo = customLicenseManager.getServerInfos();
        cacheUtils.putKeyWithValue("serverInfo", serverInfo);

        //开始安装加载证书
        licenseInfoComponent.installLicense();
    }
}
