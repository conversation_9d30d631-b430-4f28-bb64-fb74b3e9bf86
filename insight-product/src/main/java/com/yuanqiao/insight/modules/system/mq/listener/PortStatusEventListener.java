package com.yuanqiao.insight.modules.system.mq.listener;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.service.device.entity.DeviceInfo;
import com.yuanqiao.insight.service.device.mapper.DeviceInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.mq.RedisMq;
import org.jeecg.common.mq.aspect.annotation.RedisMessageExtend;
import org.jeecg.common.mq.stream.Streams;
import org.jeecg.common.mq.utils.AnalyzeContent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.stream.MapRecord;
import org.springframework.data.redis.stream.StreamListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Map;


@Slf4j
@Component
public class PortStatusEventListener implements StreamListener<String, MapRecord<String, String, String>> {
    @Autowired
    DeviceInfoMapper deviceInfoMapper;
    @Autowired
    private RedisMq redisMq;

    @Override
    @Async("stream-core-pool")
    @RedisMessageExtend(stream = Streams.PORT_STATUS)
    public void onMessage(MapRecord<String, String, String> message) {
        log.info("开始处理矩阵端口状态广播事件...");
        final Map<String, String> value = message.getValue();
        JSONObject jsonObject = AnalyzeContent.getContent(value);
        JSONObject dataJsonObject = jsonObject.getJSONObject("data");
        String deviceKey = dataJsonObject.getString("deviceId");
        DeviceInfo deviceInfo = deviceInfoMapper.findAllByCode(deviceKey);
        if (deviceInfo != null && deviceInfo.getDelflag() == 0) {
            dataJsonObject.put("deviceId", deviceInfo.getId());
            jsonObject.put("data", dataJsonObject);
            //WebSocket.sendAllMessage(jsonObject.toJSONString());
            redisMq.publish(Streams.WEBSOCKET_PUBLICIZE, jsonObject);
        }
    }
}
