package com.yuanqiao.insight.modules.system.mq.listener;

import com.yuanqiao.insight.acore.alarm.AlarmEngine;
import org.jeecg.common.mq.aspect.annotation.RedisMessageExtend;
import org.jeecg.common.mq.stream.Streams;
import org.jeecg.common.mq.utils.AnalyzeContent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.stream.MapRecord;
import org.springframework.data.redis.stream.StreamListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/10/11
 */
@Component
public class DataEventListener implements StreamListener<String, MapRecord<String, String, String>> {
    @Autowired
    private AlarmEngine alarmEngine;

    @Override
    @Async("stream-core-pool")
    @RedisMessageExtend(stream = Streams.ON_DATA)
    public void onMessage(MapRecord<String, String, String> message) {
        final Map<String, String> value = message.getValue();
        alarmEngine.onStatus(AnalyzeContent.getContent(value).toJSONString());
    }
}
