package com.yuanqiao.insight.modules.system.license.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.modules.system.license.entity.SysLicenseAlarmInfo;
import com.yuanqiao.insight.modules.system.license.mapper.SysLicenseAlarmInfoMapper;
import com.yuanqiao.insight.modules.system.license.service.ISysLicenseAlarmInfoService;
import org.springframework.stereotype.Service;

/**
 * @Description: 授权告警记录
 * @Author: jeecg-boot
 * @Date:   2024-03-28
 * @Version: V1.0
 */
@Service
public class SysLicenseAlarmInfoServiceImpl extends ServiceImpl<SysLicenseAlarmInfoMapper, SysLicenseAlarmInfo> implements ISysLicenseAlarmInfoService {

}
