package com.yuanqiao.insight.config;

import lombok.extern.slf4j.Slf4j;
import org.quartz.JobListener;
import org.quartz.Scheduler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/26
 */
@Slf4j
@Configuration
public class QuartzConfig {

    @Autowired
    private List<JobListener> jobListeners;

    @Bean
    public Scheduler scheduler(Scheduler scheduler) {
        try {
            for (JobListener jobListener : jobListeners) {
                scheduler.getListenerManager().addJobListener(jobListener);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return scheduler;
    }

}
