server:
  port: 8091
  tomcat:
    max-swallow-size: -1
  error:
    include-exception: true
    include-stacktrace: ALWAYS
    include-message: ALWAYS
  servlet:
    context-path: /insight-api
  compression:
    enabled: true
    min-response-size: 1024
    mime-types: application/javascript,application/json,application/xml,text/html,text/xml,text/plain,text/css,image/*

#management:
#  endpoints:
#    web:
#      exposure:
#        include: metrics,httptrace

spring:
  servlet:
    multipart:
      max-file-size: 1024MB
      max-request-size: 1024MB
  jmx:
    enabled: true
  elasticsearch:
    index: variable_xj
    rest:
      uris:
        - http://**************:9393
      username: elastic
      password: 123456
      read-timeout: 60s
  mail:
    host: smtp.163.com
    username: jeec<PERSON>@163.com
    password: ??
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
  ## quartz定时任务,采用数据库方式
  quartz:
    job-store-type: jdbc
    #定时任务启动开关，true-开  false-关
    auto-startup: true
    #延迟1秒启动定时任务
    startup-delay: 1s
    #启动时更新己存在的Job
    overwrite-existing-jobs: true
    properties:
      org:
        quartz:
          scheduler:
            instanceName: ProductScheduler
            instanceId: AUTO
          jobStore:
            class: org.springframework.scheduling.quartz.LocalDataSourceJobStore
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            tablePrefix: QRTZ_
            isClustered: true
            misfireThreshold: 12000
            clusterCheckinInterval: 15000
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true
    jdbc:
      initialize-schema: never
  #json 时间戳统一转换
  jackson:
    date-format:   yyyy-MM-dd HH:mm:ss
    time-zone:   GMT+8
  jpa:
    open-in-view: false
    # 配置hibernate方言
    properties:
      hibernate:
        #dialect: org.hibernate.dialect.DmDialect
        dialect: org.hibernate.dialect.MySQL5Dialect
        #database-platform: org.hibernate.dialect.DmDialect
        database-platform: org.hibernate.dialect.MySQL5Dialect
  aop:
    proxy-target-class: true
  #配置freemarker
  #  freemarker:
  #    # 设置模板后缀名
  #    suffix: .ftl
  #    # 设置文档类型
  #    content-type: text/html
  #    # 设置页面编码格式
  #    charset: UTF-8
  #    # 设置页面缓存
  #    cache: false
  #    prefer-file-system-access: false
  #    # 设置ftl文件路径
  #    template-loader-path:
  #    - classpath:/templates
  # 设置静态文件路径，js,css等
  mvc:
    static-path-pattern: /**
    async:
      request-timeout: 120000
  resource:
    static-locations: classpath:/static/,classpath:/public/
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
        allow:
      web-stat-filter:
        enabled: true
      test-on-borrow: true
      test-while-idle: true
    dynamic:
      druid: # 全局druid参数，绝大部分值和默认保持一致。(现已支持的参数如下,不清楚含义不要乱设置)
        # 连接池的配置信息
        # 初始化大小，最小，最大
        initial-size: 5
        min-idle: 5
        maxActive: 20
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        # 打开PSCache，并且指定每个连接上PSCache的大小
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙 达梦数据库关闭 stat,wall
        filters: slf4j
        # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      datasource:
        master:
          #url: ***********************************************************************************************************************************************************************************
          url: jdbc:mysql://**************:3306/insight_t414?characterEncoding=UTF-8&useUnicode=true&useSSL=false&tinyInt1isBit=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
          username: root
          password: 123456
          driver-class-name: com.mysql.cj.jdbc.Driver
            #          url: jdbc:dm://127.0.0.1:5236/SYSDBA
            #          username: SYSDBA
            #          password: SYSDBA
            #          driver-class-name: dm.jdbc.driver.DmDriver
            # 多数据源配置assets/assets
            #multi-datasource1:
            #url: ***************************************************************************************************************************************************************************************************************************
            #username: root
            #password: root
            #driver-class-name: com.mysql.cj.jdbc.Driver
            #达梦
            #url: jdbc:dm://**************:5236/SYSDBA
            #username: SYSDBA
            #password: SYSDBA1234
            #driver-class-name: dm.jdbc.driver.DmDriver
            #人大金仓
            #          url: *********************************************
            #          username: SYSTEM
            #          password: 123456
            #          driver-class-name: com.kingbase8.Driver
            #神通数据库
            #          url: ****************************************
            #          username: SYSDBA
            #          password: szoscar55
            #          driver-class-name: com.oscar.Driver
          #瀚高数据库
  #          url: **********************************************************************************************************************************************************************************************************************
  #          username: sysdba
  #          password: Yq@12345678
  #          driver-class-name: com.highgo.jdbc.Driver
  #           南大数据库
  #          url: jdbc:gbasedbt-sqli://192.168.16.129:9088/insightP304:GBASEDBTSERVER=gbase01;db_locale=zh_cn.utf8;client_locale=zh_cn.utf8;NEWCODESET=utf-8,utf8,57372;
  #          username: gbasedbt
  #          password: GBase123
  #          driver-class-name: com.gbasedbt.jdbc.Driver
             #南大通用GBase8s-JYV8.6 S5.0
  #          url: ****************************************************************************************************************************************************************************************************************
  #          username: test_user
  #          password: gbase;123
  #          driver-class-name: org.postgresql.Driver
  #redis 配置
  redis:
    database: 0
    host: *************
    lettuce:
      pool:
        max-active: 10   #最大连接数据库连接数,设 0 为没有限制
        max-idle: 8     #最大等待连接中的数量,设 0 为没有限制
        max-wait: -1ms  #最大建立连接等待时间。如果超过此时间将接到异常。设为-1表示无限制。
        min-idle: 0     #最小等待连接中的数量,设 0 为没有限制
      shutdown-timeout: 5000ms
    jedis:
      pool:
        max-active: 20   #最大连接数据库连接数,设 0 为没有限制
        max-idle: 8     #最大等待连接中的数量,设 0 为没有限制
        max-wait: -1ms  #最大建立连接等待时间。如果超过此时间将接到异常。设为-1表示无限制。
        min-idle: 0     #最小等待连接中的数量,设 0 为没有限制
      shutdown-timeout: 1000ms
    password:
    port: 6379
    timeout: 100000
    stream:
      batch-size: 5 #一次最多获取多少条消息
      poll-timeout: 300 #超时时间，设置为0，表示不超时（超时后会抛出异常）,单位毫秒
      group: product #消费者组
      consumer: auto #消费者,每个节点保持唯一,使用auto自动生成唯一编号
      ack:
        batch-size: 25 #每次最多ack多少条消息
        interval: 50 #定时时间，每xms执行一次,单位毫秒
      idle:
        page-size: 300 #分页大小
        parallelism: 10 #并行度
        time: 15000 #空间时间，查询超过空闲Xms的pending信息
        interval: 20000 #定时时间，每xms执行一次,单位毫秒
influxdb:
  username: admin
  password: admin
  openurl: http://*************:8086
  database: db-show
    #数据库方言
    #达梦：org.hibernate.dialect.DmDialect
    #mysql：org.hibernate.dialect.MySQL5Dialect
    #人大金仓： org.hibernate.dialect.Kingbase8Dialect
    #Oracle : org.hibernate.dialect.OracleDialect
    #神通：org.hibernate.dialect.OscarDialect
    #瀚高：org.hibernate.dialect.PostgreSQLDialect
    #南大：org.hibernate.dialect.GBasedbtDialect
    #hibernate:
    #  dialect: org.hibernate.dialect.DmDialect
    # 该dbType设置暂时在使用【南大通用、瀚高】时需要设置，其他情况可不设置,若设置必须和方言对应
    #mysql: MySql数据库
    #mariadb: MariaDB数据库
    #oracle: Oracle11g及以下数据库(高版本推荐使用ORACLE_NEW)
    #oracle12c: Oracle12c+数据库
    #db2: DB2数据库
    #h2: H2数据库
    #hsql: HSQL数据库
    #sqlite: SQLite数据库
    #postgresql: Postgre数据库、瀚高数据库
    #sqlserver2005: SQLServer2005数据库
    #sqlserver: SQLServer数据库
    #dm: 达梦数据库
    #xugu: 虚谷数据库
    #kingbasees: 人大金仓数据库
    #phoenix: Phoenix HBase数据库
    #zenith: Gauss数据库
    #clickhouse: clickhouse 数据库
    #gbase: 南大通用数据库
    #oscar: 神通数据库
    #sybase: Sybase ASE 数据库
    #oceanbase: OceanBase 数据库
    #Firebird: Firebird 数据库
  #other: 其他数据库
  #dbType: postgresql
#mybatis plus 设置
mybatis-plus:
  mapper-locations: classpath*:org/jeecg/modules/**/xml/*Mapper.xml,classpath*:com/yuanqiao/insight/**/**/**/xml/*Mapper*.xml
  global-config:
    # 关闭MP3.0自带的banner
    banner: false
    db-config:
      #主键类型
      id-type: ASSIGN_ID
      # 默认数据库表下划线命名
      table-underline: true
  configuration:
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 返回类型为Map,显示null对应的字段
    call-setters-on-nulls: true

mybatis-plus-join:
  banner: false
#jeecg专用配置
jeecg :
  # 本地：local\Minio：minio\阿里云：alioss
  uploadType: minio
  path :
    #文件上传根目录 设置
    upload: resource
    #webapp文件路径
    webapp: ${user.dir}/resource/default/insight-front
  shiro:
    excludeUrls: /test/jeecgDemo/demo3,/test/jeecgDemo/redisDemo/**,/visual/**,/map/**,/jmreport/bigscreen2/**
    #阿里云oss存储配置  对象存储
    #  oss:
    #    endpoint: oss-cn-beijing.aliyuncs.com
    #    accessKey: ??
    #    secretKey: ??
    #    bucketName: jeecgos
    #    staticDomain: ??
    # ElasticSearch 6设置
    #  elasticsearch:
    #    cluster-name: jeecg-ES
    #    cluster-nodes: 127.0.0.1:9200
    #    check-enabled: false
    # 表单设计器配置vmList
    #  desform:
    #    # 主题颜色（仅支持 16进制颜色代码）
    #    theme-color: "#1890ff"
    #    # 文件、图片上传方式，可选项：qiniu（七牛云）、system（跟随系统配置）
    #    upload-type: system
    # 在线预览文件服务器地址配置
    #  file-view-domain: 127.0.0.1:8012
    # minio文件上传
  minio:
    minio_url: http://**************:9000
    minio_name: minio
    minio_pass: admin123!@#
    bucketName: insight
    externalNetwork: http://**************:9000
    #大屏报表参数设置
    #  jmreport:
    #    mode: dev
    #    #是否需要校验token
    #    is_verify_token: false
    #    #必须校验方法
    #    verify_methods: remove,delete,save,add,update
    #  #Wps在线文档
    #  wps:
    #    domain: https://wwo.wps.cn/office/
    #    appid: ??
    #    appsecret: ??
    #xxl-job配置 分布式调度
    #  xxljob:
    #    enabled: false
    #    adminAddresses: http://127.0.0.1:9080/xxl-job-admin
    #    appname: ${spring.application.name}
    #    accessToken: ''
    #    address: 127.0.0.1:30007
    #    ip: 127.0.0.1
    #    port: 30007
    #    logPath: logs/jeecg/job/jobhandler/
    #    logRetentionDays: 30
    #自定义路由配置 yml nacos database
  #  route:
  #    config:
  #      data-id: jeecg-gateway-router
  #      group: DEFAULT_GROUP
  #      data-type: yml
  #分布式锁配置
#  redisson:
#    address: 127.0.0.1:6379
#    password: redis
#    type: STANDALONE
#    enabled: true
flowable:
  check-process-definitions: false
  #启用作业执行器
  async-executor-activate: false
  #启用异步执行器
  job-executor-activate: false
  database-schema-update: false
#cas单点登录
cas:
  prefixUrl: http://localhost:8443/cas
  logout: http://localhost:8443/cas/logout #注销地址
#输出sql日志
logging:
  level:
    #org.jeecg.modules.system.mapper : info
    #com.yuanqiao.insight.service.device.mapper : debug
    #    com.yuanqiao.insight.acore.alarm.mapper : debug
    org.jeecg.modules.system.mapper.SysDictMapper : debug
    #com.yuanqiao.insight.service.device.mapper : debug
    #com.yuanqiao.insight.service.product.mapper : debug
  config: config/logback-product-spring.xml
#enable swagger
swagger:
  enable: false
  production: false
  basic:
    enable: false
    username: jeecg
    password: jeecg1314
#第三方登录
#justauth:
#  enabled: true
#  type:
#    GITHUB:
#      client-id: ??
#      client-secret: ??
#      redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/github/callback
#    WECHAT_ENTERPRISE:
#      client-id: ??
#      client-secret: ??
#      redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/wechat_enterprise/callback
#      agent-id: 1000002
#    DINGTALK:
#      client-id: ??
#      client-secret: ??
#      redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/dingtalk/callback
#    WECHAT_OPEN:
#      client-id: ??
#      client-secret: ??
#      redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/wechat_open/callback
#  cache:
#    type: default
#    prefix: 'demo::'
#    timeout: 1h

#小程序配置
#mp:
#  enable: true
#  AppId: wx18680a73d3011e09
#  AppSecret: 6be8452137b1b6034241027158023677

itil:
  appkey: sdyqitilfwpt20190731
  verdueNumber: 15000000
  inpath: /inRoutePath
  outpath: /outRoutePath

opmg:
  #一次排几个星期的班
  #  arrangement:
  #    weekCount: 2
  aIfilePath: resource/aIfile
  syApplPath: resource/syAppl
  #巡检
  aIInspectFilePath: resource/aIfile/inspect

#数据中心报表
dataCenter:
  reportFilePath: resource/reportForm

#最近一次告警时间与当前时间的差值大于过期时间则修改状态为在线
#expiration:
#  time: 180 #秒

#修改设备状态为告警时的键过期时间
#redisTime:
#  time: 300 #秒

#资产预警频率
alarm_assets:
  cron: 0 0 21 * * ?
  #cron: 0/30 * * * * ?

#紫光云虚拟机管理——扫描主机池下所有虚拟机 频率
vmList:
  cron: 0 0 18 * * ?
  #cron: 0/20 * * * * ?

#紫光云主机管理——扫描所有主机 频率
hostList:
  cron: 0 0 18 * * ?
  #cron: 0/20 * * * * ?

  #请求influxdb查询接口 频率
  #access_influxDB:
  #  cron: 0 0 18 * * ?
  #cron: 0/20 * * * * ?

#台账大屏  模板路径
excel:
  planPath: 'resource/default/xlsx/plan.xlsx'
  assetsPath: 'resource/default/xlsx/assets.xlsx'
  monthPath: 'resource/default/xlsx/month.xls'
  terminalPath: 'static/xlsx/terminal.xlsx'
  categoryPath: 'static/xlsx/assCategory.xlsx'
  deptPath: 'static/xlsx/dept.xlsx'
  cmdbAssetsPath: 'static/xlsx/cmdbAssets.xlsx'
  assets2Path: 'static/xlsx/assets2.xlsx'
  extendFormPath: 'static/xlsx/extendForm.xlsx'
  supplierPath: 'static/xlsx/cmdbSupplier.xlsx'
  itInnovatePath: 'static/xlsx/cmdbItInnovate.xlsx'
  statusPath: 'static/xlsx/status.xlsx'
  relationPath: 'static/xlsx/relation.xlsx'
  backupResourcePath: 'static/xlsx/backupResource.xlsx'
  ipManagePath: 'static/xlsx/ipManage.xlsx'
  stockPath: 'static/xlsx/stock.xlsx'
  messageTemplate: 'static/xlsx/messageTemplate.xlsx'
  dictTemplate: 'static/xlsx/dictTemplate.xlsx'
  wpsTemplate: 'static'
  ipAuditResultWord: 'static/templates/ipAuditResult.docx'
#项目所在的市 //毕节 520500 //呼和浩特 150100 //全国 100000 //六盘水520200  上海市310100
#sysArea: '100000'

#ssh连接测试用到的本地IP
#ssh:
#  ip: **************

#告警确认操作控制标识 process / order
#alarm_confirm:
#  flag : process

#微信公众号参数配置
#weixin:
#  TOKEN:
#  APPID:
#  APPSECRET:
#  DOMAINURL:

#沈阳-指标考核、应用系统监控相关请求秘钥
#X-Secret:

#
##盛事通公众号配置
#shenyang:
#  APPID:
#  SENDURL:
#  function:
#  secretkey:
#  GETOPENIDURL:

# 0 0 0 * * ? 明天 0点：0分：0秒    0 0 0 1 * ?每个月 1日：0点：0分：0秒
report:
  #update: 0 0 0 30 2 ?
  update: 0 0 18 * * ?
  #SY定时拉取用
  save: 0 0/30 * * * ?
  #  服务器报表用-暂未用
  server: 0 0 0 1 1 ?

#License相关配置
license:
  subject: yq_license
  publicAlias: publicCert
  storePass: public_insight2023
  licensePath: ${user.dir}/resource/default/yq_license.lic
  publicKeysStorePath: ${user.dir}/resource/default/yq_publicCerts.keystore

#是否开启审核
#verifyStatus: false

#沈阳资产接口IP和端口
#syResources:
#  ip:
#  post:

#定时删除10天前的数据以及统计在线率
#地市ip，定时任务无法获取前端传输的值，需要定在配置文件 六盘水：520200   遵义： 520300  沈阳： 210100  上海： 310100  榆林： 610800
deviceStatis:
  cron: 0 0 2 * * ?
#  cityId: 520200

#是否是sm  0（不启用三员）、1（启用三员）:
#insight:
#  isThreePowers: 0

#终端名称前缀 -- 终端注册使用(非涉密: tg_ ,涉密： sm_ )
#terminalPrefix: tg_

#Ukey 接口地址
#Ukey:
#  ip: 127.0.0.1
#  post: 10318

#一级:公司
#  二级:模块
#    三级:参数
yuanqiao:
  core:
    aspect: true
#配置内置tomcat jMX
#  product:
#      jmx:
#        rmi:
#          host: 127.0.0.1
#          port: 1097

liteflow:
  #ruleSource: config/*.xml
  substitute-cmp-class: com.yuanqiao.insight.service.flow.node.compont.common.SubCmp
  when-max-wait-seconds: 300
  print-banner: false

#attachmentPath: ${user.dir}/resource/

oapUrl: **************:12800/graphql

yq:
  flowable:
    font-name: 宋体
  logo: false

ftpServer:
  isEnable: true
  serverPort: 21
knowledge:
  isParentAndChildTopicAuthorityIndependence: false

platform:
   uniqueCode: insight-product-local