package com.yuanqiao.insight.config.aviator.extend;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorString;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/9/21
 */
public class LowerCaseFunction extends AbstractFunction {

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject arg) {
        Object resultKey = arg.getValue(env);
        return new AviatorString((String.valueOf(resultKey)).toLowerCase());
    }

    @Override
    public String getName() {
        return "lowerCase";
    }
}
