package com.yuanqiao.insight.monitoring.modules.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 自动测试步骤结果
 * <AUTHOR>
 */
@Data
public class AutoTestStepResult  implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 步骤名称
     */
    @ApiModelProperty(value = "步骤名称")
    private String name;

    /**
     * 是否成功
     */
    @ApiModelProperty(value = "是否成功")
    private Integer success;

    /**
     * 测试开始时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss:SSS")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss:SSS")
    @ApiModelProperty(value = "测试开始时间")
    private Date startTime;

    /**
     * 测试结束时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss:SSS")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss:SSS")
    @ApiModelProperty(value = "测试结束时间")
    private Date endTime;

    /**
     * 测试耗时（毫秒数）
     */
    @ApiModelProperty(value = "测试耗时（毫秒数）")
    private Integer durationMilliseconds;

    /**
     * 提示信息
     */
    @ApiModelProperty(value = "提示信息")
    private String message;

    /**
     * 错误输出信息
     */
    @ApiModelProperty(value = "错误输出信息")
    private String errorInfo;

}
