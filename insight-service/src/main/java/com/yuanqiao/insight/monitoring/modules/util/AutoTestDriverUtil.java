package com.yuanqiao.insight.monitoring.modules.util;

import com.google.common.collect.ImmutableList;
import com.yuanqiao.insight.common.util.common.LocalCacheUtils;
import com.yuanqiao.insight.monitoring.modules.business.enums.AutoTestBrowserType;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.jeecg.common.constant.CommonConstant;
import org.openqa.selenium.Proxy;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.firefox.FirefoxDriver;
import org.openqa.selenium.firefox.FirefoxOptions;
import org.openqa.selenium.support.ui.ExpectedCondition;
import org.openqa.selenium.support.ui.WebDriverWait;

/**
 * 自动化测试驱动工具封装类
 *
 * @Author: wangtao
 * @Date: 2023/7/28 14:37
 * @Version: 1.0
 * @Description: 自动化测试驱动工具封装类
 */
@Slf4j
public class AutoTestDriverUtil {
    //文件版本,防止多线程缓存文件和用户文件共享,导致创建错误------删除，缓存文件后会导致内存消耗大，而且第二次测试会缓存上次的登录信息，导致登录操作失败，暂时删除，后期增加逻辑判断功能后，可以判断是否已经登录
    //private  static  AtomicInteger fileSerial=new AtomicInteger(0);

    //浏览器驱动
    private WebDriver driver;

    /**
     * 网关编码
     */
    private String gatewayCode;

    /**
     * 缓存工具类，获取配置字典
     */
    LocalCacheUtils cacheUtils = LocalCacheUtils.getInstance();

    public AutoTestDriverUtil(String gatewayCode) {
        init(gatewayCode);
    }

    private void init(String gatewayCode) {
        //网关编码
        if(Strings.isNotBlank(gatewayCode)){
            this.gatewayCode = gatewayCode;
        }
        String browserType = "Chrome";  //默认使用谷歌浏览器
        if(cacheUtils.getValueByKey(getDictKey("autoTestBrowser_browserType"))!=null){
            browserType = String.valueOf(cacheUtils.getValueByKey(getDictKey("autoTestBrowser_browserType")));
        }else{
            log.warn("--------------获取可用性测试配置的浏览器类型失败，使用默认配置：谷歌浏览器（Chrome）--------------");
        }

        // 获取浏览器类型
        AutoTestBrowserType browser = AutoTestBrowserType.getByCode(browserType);

        switch (browser) {
            case CHROME:
                initChrome();
                break;
            case FIREFOX:
                initFirefox();
                break;
            default:
                initChrome();
                break;
        }
    }


    /**
     * 初始化谷歌浏览器
     */
    private void initChrome() {
        //浏览器驱动地址
        String path = String.valueOf(cacheUtils.getValueByKey(getDictKey("autoTestBrowser_Chrome_driverPath")));
        //是否显示浏览器 1显示、0不显示
        Integer showBrowser = 0; //默认不显示
        if(cacheUtils.getValueByKey(getDictKey("autoTestBrowser_showBrowser"))!=null){
            showBrowser = Integer.valueOf((String) cacheUtils.getValueByKey(getDictKey("autoTestBrowser_showBrowser")));
        }else{
            log.warn("--------------获取可用性测试配置的是否显示浏览器失败，使用默认配置：不显示浏览器--------------");
        }
        //是否显示图片 1显示、0不显示
        Integer showImg = 1; //默认显示
        if(cacheUtils.getValueByKey(getDictKey("autoTestBrowser_showImg"))!=null){
            showImg = Integer.valueOf((String) cacheUtils.getValueByKey(getDictKey("autoTestBrowser_showImg")));
        }else{
            log.warn("--------------获取可用性测试配置的是否显示图片失败，使用默认配置：显示图片--------------");
        }
        System.setProperty("webdriver.chrome.driver", path); //设置驱动
        System.setProperty("webdriver.chrome.whitelistedIps", ""); //将所有IP加入白名单

        // 浏览器配置
        ChromeOptions options = new ChromeOptions();
        if (showBrowser == 0) {
            options.addArguments("--headless"); //无浏览器模式
            //无浏览器模式-最大化窗口  ,防止有些元素被隐藏
            int screenWidth = 1920;
            int screenHeight = 1080;
            if(cacheUtils.getValueByKey(getDictKey("autoTestBrowser_screenWidth"))!=null){
                screenWidth = Integer.valueOf((String) cacheUtils.getValueByKey(getDictKey("autoTestBrowser_screenWidth")));
            }else{
                log.warn("--------------获取可用性测试配置的无UI界面模式的屏幕宽度失败，使用默认配置：1920px --------------");
            }
            if(cacheUtils.getValueByKey(getDictKey("autoTestBrowser_screenHeight"))!=null){
                screenWidth = Integer.valueOf((String) cacheUtils.getValueByKey(getDictKey("autoTestBrowser_screenHeight")));
            }else{
                log.warn("--------------获取可用性测试配置的无UI界面模式的屏幕高度失败，使用默认配置：1080px --------------");
            }
            options.addArguments("window-size=" + screenWidth + "," + screenHeight);
        }

        //设置浏览器启动文件路径，绝对路径
        //浏览器地址
        if(cacheUtils.getValueByKey(getDictKey("autoTestBrowser_Chrome_browserPath")) != null){
            String browserPath = String.valueOf(cacheUtils.getValueByKey(getDictKey("autoTestBrowser_Chrome_browserPath")));
            if(browserPath.length()>0){
                log.info("--------------自动化测试浏览器地址--------------" + browserPath);
                options.setBinary(browserPath);
            }
        }

        log.info("--------------自动化测试浏览器驱动地址--------------" + path);
        log.info("--------------自动化测试是否显示浏览器--------------" + showBrowser);

        options.addArguments("--disable-gpu"); // 谷歌文档提到需要加上这个属性来规避bug
        options.addArguments("--disable-software-rasterizer"); //禁用3D软件光栅化器
        options.addArguments("--no-sandbox");// 为了让linux root用户也能执行
        // 优化参数
        options.addArguments("--disable-dev-shm-usage"); //解决在某些VM环境中，/dev/shm分区太小，导致Chrome失败或崩溃
        if (showImg == 0) {
            options.addArguments("blink-settings=imagesEnabled=false"); //禁止加图片,如果爬取图片的话,这个不能禁用
            options.addArguments("--disable-images");
        }

        // 去掉缓存，防止测试的用户信息缓存无法清理
//        String tmpdir = System.getProperty("java.io.tmpdir");
//        String dir = tmpdir + File.separator + "chrome_file_data_cache"+File.separator+fileSerial.incrementAndGet();
//        File file1 = new File(dir+File.separator + "data");
//        if(file1.exists()){
//            file1.mkdirs();
//        }
//        File file2 = new File(dir+File.separator + "cache");
//        if(file2.exists()){
//            file1.mkdirs();
//        }


        //options.addArguments("--user-data-dir=" + file1.getAbsolutePath()); //解决打开页面出现data;空白页面情况,因为没有缓存目录
        //options.addArguments("--disk-cache-dir=" + file2.getAbsolutePath()); //指定Cache路径
        //options.addArguments("--incognito") ; //无痕模式
        options.addArguments("--disable-plugins"); //禁用插件,加快速度
        options.addArguments("--disable-extensions"); //禁用扩展
        options.addArguments("--disable-popup-blocking"); //关闭弹窗拦截
        options.addArguments("--ignore-certificate-errors"); //  禁现窗口最大化
        options.addArguments("--allow-running-insecure-content");  //关闭https提示 32位
        options.addArguments("--remote-allow-origins=*"); //解决跨域
        //options.addArguments("--disable-infobars");  //禁用浏览器正在被自动化程序控制的提示(chromeV76以下版本)

        //排除的配置
        options.setExperimentalOption("excludeSwitches", ImmutableList.of("--enable-automation")); //禁用浏览器正在被自动化程序控制的提示(chromeV76及以上版本)
        //随机设置请求头
        // options.addArguments("--user-agent=" + UserAgent.getUserAgentWindows());
        proxy(options, false); //设置代理 ,true 开启代理
        driver = new ChromeDriver(options);//实例化
        if (showBrowser == 1) {
            driver.manage().window().maximize(); //界面的方式, 最大化窗口, 防止有些元素被隐藏,无界面就不要使用了
        }
    }


    /**
     * 初始化火狐浏览器
     */
    private void initFirefox() {
        //浏览器驱动地址
        String path = String.valueOf(cacheUtils.getValueByKey(getDictKey("autoTestBrowser_Firefox_driverPath")));
        //是否显示浏览器 1显示、0不显示
        Integer showBrowser = 0; //默认不显示
        if(cacheUtils.getValueByKey(getDictKey("autoTestBrowser_showBrowser"))!=null){
            showBrowser = Integer.valueOf((String) cacheUtils.getValueByKey(getDictKey("autoTestBrowser_showBrowser")));
        }else{
            log.warn("--------------获取可用性测试配置的是否显示浏览器失败，使用默认配置：不显示浏览器--------------");
        }
        //是否显示图片 1显示、0不显示
        Integer showImg = 1; //默认显示
        if(cacheUtils.getValueByKey(getDictKey("autoTestBrowser_showImg"))!=null){
            showImg = Integer.valueOf((String) cacheUtils.getValueByKey(getDictKey("autoTestBrowser_showImg")));
        }else{
            log.warn("--------------获取可用性测试配置的是否显示图片失败，使用默认配置：显示图片--------------");
        }
        System.setProperty("webdriver.gecko.driver", path);

        // 浏览器配置
        FirefoxOptions options = new FirefoxOptions();
        if (showBrowser == 0) {
            options.addArguments("-headless"); //无浏览器模式
            //无浏览器模式-最大化窗口  ,防止有些元素被隐藏
            int screenWidth = 1920;
            int screenHeight = 1080;
            if(cacheUtils.getValueByKey(getDictKey("autoTestBrowser_screenWidth"))!=null){
                screenWidth = Integer.valueOf((String) cacheUtils.getValueByKey(getDictKey("autoTestBrowser_screenWidth")));
            }else{
                log.warn("--------------获取可用性测试配置的无UI界面模式的屏幕宽度失败，使用默认配置：1920px --------------");
            }
            if(cacheUtils.getValueByKey(getDictKey("autoTestBrowser_screenHeight"))!=null){
                screenWidth = Integer.valueOf((String) cacheUtils.getValueByKey(getDictKey("autoTestBrowser_screenHeight")));
            }else{
                log.warn("--------------获取可用性测试配置的无UI界面模式的屏幕高度失败，使用默认配置：1080px --------------");
            }
            options.addArguments("window-size=" + screenWidth + "," + screenHeight);
        }

        //设置浏览器启动文件路径，绝对路径
        //浏览器地址
        if(cacheUtils.getValueByKey(getDictKey("autoTestBrowser_Firefox_browserPath")) != null){
            String browserPath = String.valueOf(cacheUtils.getValueByKey(getDictKey("autoTestBrowser_Firefox_browserPath")));
            if(browserPath.length()>0){
                log.info("--------------自动化测试浏览器地址--------------" + browserPath);
                options.setBinary(browserPath);
            }
        }

        log.info("--------------自动化测试浏览器驱动地址--------------" + path);
        log.info("--------------自动化测试是否显示浏览器--------------" + showBrowser);

        options.addArguments("-disable-software-rasterizer"); //禁用3D软件光栅化器
        //options.addArguments("--no-sandbox");// 为了让linux root用户也能执行
        // 优化参数
        options.addArguments("-disable-dev-shm-usage"); //解决在某些VM环境中，/dev/shm分区太小，导致Chrome失败或崩溃
        if (showImg == 0) {
            options.addArguments("blink-settings=imagesEnabled=false"); //禁止加图片,如果爬取图片的话,这个不能禁用
            options.addArguments("-disable-images");
        }
        options.addArguments("-disable-extensions"); //禁用扩展
        options.addArguments("-ignore-certificate-errors"); //  禁现窗口最大化

        driver = new FirefoxDriver(options);//实例化
        if (showBrowser == 1) {
            driver.manage().window().maximize(); //界面的方式, 最大化窗口, 防止有些元素被隐藏,无界面就不要使用了
        }
    }


    /**
     * 根据网关编码构建驱动
      */
    public static AutoTestDriverUtil build(String gatewayCode) {
        return new AutoTestDriverUtil(gatewayCode);
    }


    /**
     * 获取浏览器驱动
     * @return
     */
    public WebDriver getDriver() {
        return driver;
    }

    // 显示等待,是为了解决隐式等待遗留的问题,比如元素显示了,但是内部的文本没有显示出来,可能文本是通过ajax异步的会比较慢
    public WebElement wait(int seconds, ExpectedCondition<WebElement> expectedCondition) {
        WebDriverWait webDriverWait = new WebDriverWait(driver, seconds);
        //返回null或者false,等待500毫秒继续尝试,直到过期
        WebElement until = webDriverWait.until(expectedCondition);

        return until;
    }

    /**
     * 代理设置 自行扩展, 从接口中读取,或者从文件中读取都行
     */

    private void proxy(ChromeOptions options, boolean pd) {
        if (pd) {
            String prox = "101.200.127.149:" + 3129;
            Proxy p = new Proxy();
            p.setHttpProxy(prox);//http
//        p.setFtpProxy(prox); //ftp
//        p.setSslProxy(prox);//ssl
//        p.setSocksProxy(prox); //SOCKS
//        p.setSocksUsername("");
//        p.setSocksPassword("");
            options.setProxy(p);
        }
    }


    /**
     * 获取完整的配置字典key
     * @param dictKey 某个字典项实际的字典key(去掉网关编码的部分)
     * @return
     */
    public String getDictKey(String dictKey) {
        if(Strings.isNotBlank(gatewayCode)){
            //网关中，加上网关编码
            log.warn("--------------配置字典key:"+CommonConstant.LOCAL_CACHE_DICT_KEY_PREFIX + gatewayCode + "_" + dictKey+" --------------");
            return CommonConstant.LOCAL_CACHE_DICT_KEY_PREFIX + gatewayCode + "_" + dictKey;
        }else{
            //平台
            log.warn("--------------配置字典key:"+CommonConstant.LOCAL_CACHE_DICT_KEY_PREFIX + dictKey+" --------------");
            return CommonConstant.LOCAL_CACHE_DICT_KEY_PREFIX + dictKey;
        }
    }
}


