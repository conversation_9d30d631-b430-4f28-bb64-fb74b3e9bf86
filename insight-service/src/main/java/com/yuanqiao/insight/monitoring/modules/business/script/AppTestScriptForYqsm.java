package com.yuanqiao.insight.monitoring.modules.business.script;

import com.google.common.collect.ImmutableList;
import com.yuanqiao.insight.monitoring.modules.business.entity.BusinessAvailabilityResult;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;

/**
 * 完整的自动化测试脚本
 * <AUTHOR>
 */
@Slf4j
public class AppTestScriptForYqsm {
    public BusinessAvailabilityResult runTest() throws InterruptedException {
        BusinessAvailabilityResult result = new BusinessAvailabilityResult();

        System.getProperties().setProperty("webdriver.chrome.driver", "E:\\work\\selenium-test\\webdriver\\chromedriver.exe"); //设置驱动
        System.setProperty("webdriver.chrome.whitelistedIps", ""); //将所有IP加入白名单
        ChromeOptions options  = new ChromeOptions();
        options.addArguments("--headless"); //无浏览器模式
        //无浏览器模式-最大化窗口  ,防止有些元素被隐藏
        int screenWidth = java.awt.Toolkit.getDefaultToolkit().getScreenSize().width;
        int screenHeight = java.awt.Toolkit.getDefaultToolkit().getScreenSize().height;
        options.addArguments("window-size=" + screenWidth + "," + screenHeight);
        //options.addArguments("--start-maximized"); //最大化运行（全屏窗口）,确保元素能正常显示
        options.addArguments("--disable-gpu"); // 谷歌文档提到需要加上这个属性来规避bug
        // options.addArguments("--disable-infobars"); //禁用浏览器正在被自动化程序控制的提示(chromeV76以下版本)
        // options.addArguments("--disable-software-rasterizer"); //禁用3D软件光栅化器
        // options.addArguments("--no-sandbox");// 取消沙盒模式，为了让linux root用户也能执行
        // options.addArguments("--disable-dev-shm-usage"); //解决在某些VM环境中，/dev/shm分区太小，导致Chrome失败或崩溃
        options.addArguments("--remote-allow-origins=*"); //

        //排除的配置
        options.setExperimentalOption("excludeSwitches", ImmutableList.of("--enable-automation")); //禁用浏览器正在被自动化程序控制的提示(chromeV76及以上版本)



        WebDriver driver = new ChromeDriver(options);

        log.info("--------------启动浏览器--------------");

        driver.get("http://192.168.16.211:288/user/login?redirect=%2F");
        driver.findElement(By.cssSelector(".ant-row:nth-child(1) .ant-input")).sendKeys("admin");
        driver.findElement(By.cssSelector(".ant-row:nth-child(2) .ant-input")).sendKeys("123456");
        driver.findElement(By.cssSelector(".login-button")).click();

        log.info("--------------登录成功--------------");

        // 显示等待，关闭提示框
        {
            WebDriverWait wait = new WebDriverWait(driver, 10);
            wait.until(ExpectedConditions.visibilityOfElementLocated(By.cssSelector(".ant-notification-close-icon > svg")));
        }
        driver.findElement(By.cssSelector(".ant-notification-close-icon > svg")).click();

        // 显示等待
        {
            WebDriverWait wait = new WebDriverWait(driver, 10);
            wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath("//div[@id=\'app\']/section/aside/div/ul/li[2]/div")));
        }
        driver.findElement(By.xpath("//div[@id=\'app\']/section/aside/div/ul/li[2]/div")).click();
        {
            WebDriverWait wait = new WebDriverWait(driver, 10);
            wait.until(ExpectedConditions.visibilityOfElementLocated(By.linkText("我的待办")));
        }
        driver.findElement(By.linkText("我的待办")).click();
        {
            WebDriverWait wait = new WebDriverWait(driver, 10);
            wait.until(ExpectedConditions.visibilityOfElementLocated(By.linkText("我的已办")));
        }
        driver.findElement(By.linkText("我的已办")).click();

        log.info("--------------进入我的待办--------------");


        driver.findElement(By.cssSelector(".ant-col:nth-child(1) .ant-input")).sendKeys("入职");

        {
            WebDriverWait wait = new WebDriverWait(driver, 10);
            wait.until(ExpectedConditions.elementToBeClickable(By.xpath("//button[contains(.,\'查询\')]"))); //无法解决信息框遮盖按钮无法点击的问题

        }
        //强制等待，不建议使用
        // Thread.sleep(1000*3); //由于右上角的提示框挡住了查询按钮，等待3秒钟，提示框消失后再点击查询按钮

        driver.findElement(By.xpath("//button[contains(.,\'查询\')]")).click();

        log.info("--------------查询列表--------------");


        driver.findElement(By.cssSelector(".ant-btn:nth-child(2)")).click();
        driver.findElement(By.cssSelector(".logout_title > span")).click();
        driver.findElement(By.cssSelector(".ant-btn-primary:nth-child(2)")).click();

        log.info("--------------退出登录--------------");

        driver.close(); //close关闭浏览器当前标签页，quit关闭所有浏览器窗口


        result.setSuccess(1);
        result.setMessage("测试成功");
        return result;
    }

}
