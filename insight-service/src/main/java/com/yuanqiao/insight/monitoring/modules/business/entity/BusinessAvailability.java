package com.yuanqiao.insight.monitoring.modules.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 业务可用性监控任务主表
 * <AUTHOR>
 * @date 2023/6/17
 */
@Getter
@Setter
@TableName("momg_business_availability")
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class BusinessAvailability implements Serializable {

    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /**业务主表id*/
    @ApiModelProperty(value = "业务主表id")
    private String bizInfoId;

    /**
     * 业务名称
     */
    @TableField(exist = false)
    private java.lang.String bizInfoName;

    /**可用性测试任务名称*/
    @ApiModelProperty(value = "可用性测试任务名称")
    private String name;

    /** cron表达式*/
    @ApiModelProperty(value = "cron表达式")
    private String cron;

    /**描述*/
    @ApiModelProperty(value = "描述")
    private String description;

    /**测试步骤配置*/
    @ApiModelProperty(value = "测试步骤配置")
    private String stepConfig;

    /**上次测试时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "上次测试时间")
    private Date lastTestTime;

    @TableField(exist = false)
    public String lastTestTime_begin,lastTestTime_end;



    /**上次执行结果ID*/
    @ApiModelProperty(value = "上次执行结果ID")
    private String lastTestId;

    /**
     * 上次测试结果
     * （1成功 0失败）
     */
    @ApiModelProperty(value = "上次测试结果")
    private Integer lastTestResult;

    /**
     * 是否启用（1启用 0不启用）
     */
    @ApiModelProperty(value = "是否启用")
    @Dict(dicCode = "dict_item_status")
    private Integer status;

    /**
     * 运行锁
     * 1测试进行中，锁定，不可重复运行测试；0等待中，可运行测试
     */
    @ApiModelProperty(value = "运行状态锁")
    private Integer runLocked;

    /**锁定时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "锁定时间")
    private Date lockTime;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**更新时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


}
