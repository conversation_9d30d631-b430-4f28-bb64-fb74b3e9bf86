<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.monitoring.modules.business.mapper.BusinessAvailabilityMapper">

    <select id="getAvailabilityListByGateway" resultType="com.yuanqiao.insight.monitoring.modules.business.entity.BusinessAvailability">
        SELECT
            a.*
        FROM
            momg_business_availability a
            LEFT JOIN momg_business_info i ON i.id = a.biz_info_id
            LEFT JOIN momg_device_info d ON d.device_code = i.business_node
            LEFT JOIN momg_gateway2device gd ON gd.device_id = d.id
            LEFT JOIN momg_device_info g ON g.id = gd.gateway_id
        WHERE
            g.device_code = #{ gatewayCode }
            <if test="status!=null and status!=''">
                AND a.status = #{ status }
            </if>
            <if test="ids!=null">
                AND a.id IN
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{ id }
                </foreach>
            </if>
    </select>

</mapper>
