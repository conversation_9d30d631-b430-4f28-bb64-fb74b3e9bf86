package com.yuanqiao.insight.monitoring.modules.business.enums;

import org.jeecg.common.util.oConvertUtils;

/**
 * 自动化测试交互方式枚举
 * <AUTHOR>
 */
public enum AutoTestInteractionType {
    // 交互方式
    GET("get","访问页面"),
    CLOSE("close","退出当前页"),
    QUIT("quit","关闭浏览器所有页面"),
    FIND("find","查询元素"),
    CLICK("click","点击"),
    SEND_KEYS("sendKeys","发送键值"),
    CLEAR("clear","清理"),
    HOVER("hover","悬停"),
    SLEEP("sleep","强制睡眠等待"),
    BACK("back","浏览器后退"),
    FORWARD("forward","浏览器前进"),
    REFRESH("refresh","浏览器刷新"),
    FRAME("frame","进入iframe"),
    DEFAULT_CONTENT("defaultContent","退出iframe，切换回默认页面");

    private String code;
    private String name;

    private AutoTestInteractionType(String code, String name){
        this.code = code;
        this.name = name;
    }

    public String getCode(){
        return code;
    }

    public String getName(){
        return name;
    }

    /**
     * 根据code属性获取枚举值
     * @param code
     * @return
     */
    public static AutoTestInteractionType getByCode(String code) {
        if (oConvertUtils.isEmpty(code)) {
            return null;
        }
        for (AutoTestInteractionType val : values()) {
            if (val.getCode().equals(code)) {
                return val;
            }
        }
        return null;
    }

}
