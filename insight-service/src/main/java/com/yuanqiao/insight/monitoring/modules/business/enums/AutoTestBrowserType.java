package com.yuanqiao.insight.monitoring.modules.business.enums;

import org.jeecg.common.util.oConvertUtils;

/**
 * 自动化测试浏览器类型枚举
 * <AUTHOR>
 */
public enum AutoTestBrowserType {
    // 交互方式
    CHROME("Chrome","谷歌浏览器"),
    FIREFOX("Firefox","火狐浏览器");


    private String code;
    private String name;

    private AutoTestBrowserType(String code, String name){
        this.code = code;
        this.name = name;
    }

    public String getCode(){
        return code;
    }

    public String getName(){
        return name;
    }

    /**
     * 根据code属性获取枚举值
     * @param code
     * @return
     */
    public static AutoTestBrowserType getByCode(String code) {
        if (oConvertUtils.isEmpty(code)) {
            return null;
        }
        for (AutoTestBrowserType val : values()) {
            if (val.getCode().equals(code)) {
                return val;
            }
        }
        return null;
    }

}
