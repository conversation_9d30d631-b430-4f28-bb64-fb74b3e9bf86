package com.yuanqiao.insight.monitoring.modules.business.service.impl;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.yuanqiao.insight.monitoring.modules.business.entity.BusinessAvailabilityResult;
import com.yuanqiao.insight.monitoring.modules.business.mapper.BusinessAvailabilityResultMapper;
import com.yuanqiao.insight.monitoring.modules.business.service.IBusinessAvailabilityResultService;
import org.springframework.stereotype.Service;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/6/17
 */
@Service
public class BusinessAvailabilityResultServiceImpl extends MPJBaseServiceImpl<BusinessAvailabilityResultMapper, BusinessAvailabilityResult> implements IBusinessAvailabilityResultService {

    /**
     * 保存可用性监控测试执行结果
     * @param businessAvailabilityResult
     */
    @Override
    public void saveAvailabilityResult(BusinessAvailabilityResult businessAvailabilityResult) {
        this.save(businessAvailabilityResult);
    }

    /**
     * 批量删除执行结果
     * @param ids
     * @return
     */
    @Override
    public boolean deleteBatchAvailabilityResult(String ids) {
        return this.removeByIds(Arrays.asList(ids.split(",")));
    }
}
