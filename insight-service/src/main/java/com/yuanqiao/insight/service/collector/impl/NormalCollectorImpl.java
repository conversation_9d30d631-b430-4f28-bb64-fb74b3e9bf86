package com.yuanqiao.insight.service.collector.impl;

import com.yuanqiao.insight.service.collector.Collector;
import com.yuanqiao.insight.service.device.Device;

public class NormalCollectorImpl implements Collector {
    @Override
    public void execute(String key) {
        //根据key获取设备
        Device device = new Device();
        //获取设备状态信息
        String data = device.pullData();
        System.out.println("收集器已取到设备状态信息：" + data);
        //通知设备管理器数据到了
        // TODO 编解码

        //todo 转移到管理框架中
        deviceManager.onData(data);
    }
}
