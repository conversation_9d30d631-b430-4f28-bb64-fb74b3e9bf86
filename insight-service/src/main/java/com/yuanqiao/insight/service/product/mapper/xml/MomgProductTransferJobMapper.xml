<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.service.product.mapper.MomgProductTransferJobMapper">

    <select id="selectAllInfo" resultType="com.yuanqiao.insight.service.product.entity.MomgProductTransferJob">

        select ptj.product_id, ptj.unit, ptj.rate, ptj.transfer_protocol_id, ptj.product_job_id, t.code as transferProtocol, t.type as collectType, j.value as jobValue
        from momg_product_transfer_job ptj
        left join momg_transfer_protocol t
        on ptj.transfer_protocol_id = t.id
        left join momg_product_job j
        on ptj.product_job_id = j.id
        where ptj.product_id = #{productId};

    </select>

    <select id="selectAllInfoByUDP" resultType="com.yuanqiao.insight.service.product.entity.MomgProductTransferJob">

        select ptj.product_id, ptj.unit, ptj.rate, ptj.transfer_protocol_id, ptj.product_job_id, t.code as transferProtocol, t.type as collectType, j.value as jobValue
        from momg_product_transfer_job ptj
        left join momg_transfer_protocol t
        on ptj.transfer_protocol_id = t.id
        left join momg_product_job j
        on ptj.product_job_id = j.id
        where ptj.product_id = #{productId} AND t.code='UDP';

    </select>

    <select id="findInfoByCode" resultType="com.yuanqiao.insight.service.device.entity.DeviceInfo">

        select d.*, mptj.rate, mptj.unit
        from
        momg_device_info d
        left join
        momg_product_transfer_job mptj
        on
        d.product_id = mptj.product_id
        left join
        momg_transfer_protocol t
        on
        mptj.transfer_protocol_id = t.id
        where
        d.device_code = #{deviceCode}
        and
        t.code = #{protocol}

    </select>

    <select id="selectAllInfoByProductIds" resultType="com.yuanqiao.insight.service.product.entity.MomgProductTransferJob">


        select ptj.*,
               t.code  as transferProtocol,
               j.value as jobValue
        from momg_product_transfer_job ptj
                 left join momg_transfer_protocol t
                           on ptj.transfer_protocol_id = t.id
                 left join momg_product_job j
                           on ptj.product_job_id = j.id
        <where>
            t.code = #{transferProtocol}

        </where>

    </select>

</mapper>
