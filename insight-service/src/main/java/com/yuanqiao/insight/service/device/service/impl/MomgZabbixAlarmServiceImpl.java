package com.yuanqiao.insight.service.device.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.service.device.entity.MomgZabbixAlarm;
import com.yuanqiao.insight.service.device.mapper.MomgZabbixAlarmMapper;
import com.yuanqiao.insight.service.device.service.IMomgZabbixAlarmService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @Description: Zabbix告警
 * @Author: jeecg-boot
 * @Date: 2021-03-21
 * @Version: V1.0
 */
@Slf4j
@Service
public class MomgZabbixAlarmServiceImpl extends ServiceImpl<MomgZabbixAlarmMapper, MomgZabbixAlarm> implements IMomgZabbixAlarmService {

}
