package com.yuanqiao.insight.service.momgDeviceOnOffDetails.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.service.momgDeviceOnOffDetails.entity.MomgDeviceOnOffDetails;
import com.yuanqiao.insight.service.momgDeviceOnOffDetails.mapper.MomgDeviceOnOffDetailsMapper;
import com.yuanqiao.insight.service.momgDeviceOnOffDetails.service.IMomgDeviceOnOffDetailsService;
import org.springframework.stereotype.Service;


/**
 * @Description: 设备开关机统计
 * @Author: jeecg-boot
 * @Date:   2021-09-24
 * @Version: V1.0
 */
@Service
public class MomgDeviceOnOffDetailsServiceImpl extends ServiceImpl<MomgDeviceOnOffDetailsMapper, MomgDeviceOnOffDetails> implements IMomgDeviceOnOffDetailsService {

}
