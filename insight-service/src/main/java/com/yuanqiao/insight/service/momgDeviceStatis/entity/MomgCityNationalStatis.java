package com.yuanqiao.insight.service.momgDeviceStatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;

/**
 * @Description: 区县内部设备国产化率统计
 * @Author: jeecg-boot
 * @Date: 2021-09-24
 * @Version: V1.0
 */
@Data
@TableName("momg_city_national_statis")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "momg_city_national_statis对象", description = "区县内部设备国产化率统计")
public class MomgCityNationalStatis implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 区县ID
     */
    @Excel(name = "区县ID", width = 15)
    @ApiModelProperty(value = "区县ID")
    private String cityId;

    @TableField(exist = false)
    private Integer basicTerminals;

    @TableField(exist = false)
    private Integer nationalTerminals;

    /**
     * 国产化率
     */
    @Excel(name = "国产化率", width = 15)
    @ApiModelProperty(value = "国产化率")
    private String nationalRate;

    /**
     * 创建日期
     */
    @Excel(name = "添加时间", width = 15)
    @ApiModelProperty(value = "创建日期")
    private String createTime;

    @TableField(exist = false)
    private String yearMonthStr;
}
