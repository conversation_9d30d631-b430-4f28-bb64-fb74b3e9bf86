package com.yuanqiao.insight.service.device.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanqiao.insight.service.device.entity.DeviceInfo;
import com.yuanqiao.insight.service.device.entity.MomgAbutment2Device;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 第三方对接系统
 * @Author: jeecg-boot
 * @Date:   2021-05-25
 * @Version: V1.0
 */
@Component
public interface MomgAbutment2deviceMapper extends BaseMapper<MomgAbutment2Device> {

    List<MomgAbutment2Device> getAbutmentDevsBySysIdAndDevCode(@Param("systemId") String systemId, @Param("deviceCode") String deviceCode);

    Page<DeviceInfo> getDeviceWithAlarm(Page<DeviceInfo> page, String systemId);
}
