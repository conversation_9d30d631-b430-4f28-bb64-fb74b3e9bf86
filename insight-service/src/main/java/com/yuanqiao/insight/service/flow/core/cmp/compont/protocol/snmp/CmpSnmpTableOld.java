package com.yuanqiao.insight.service.flow.core.cmp.compont.protocol.snmp;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.service.flow.core.cmp.compont.protocol.ProtocolComponent;
import com.yuanqiao.insight.service.flow.core.cmp.context.GlobalContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/9/6
 */
@Slf4j
@Component
public class CmpSnmpTableOld extends ProtocolComponent {

    @Override
    public void process() throws Exception {
        GlobalContext globalContext = this.getContextBean(GlobalContext.class);
        JSONObject req = globalContext.getReq();
        String key = req.getString("key");

        JSONObject parts = globalContext.getCommonMap().getJSONObject(key);
        //最终生成的格式
        Map<String, JSONObject> preTable = new HashMap<>();
        for (Map.Entry<String, Object> part : parts.entrySet()) {
            String partName = part.getKey();
            JSONObject rowData = (JSONObject) part.getValue();
            for (Map.Entry<String, Object> row : rowData.entrySet()) {
                String eKey = row.getKey();
                JSONObject json = preTable.get(eKey);
                if (json == null) {
                    json = new JSONObject();
                }
                json.put(partName, row.getValue());
                preTable.put(eKey, json);
            }
        }
        List<JSONObject> table = new ArrayList<>(preTable.values());
        this.putValue(table);

        //判断是否需要计算
        this.setChain();
    }

}
