<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.service.ScheduleSetting.mapper.ScheduleSettingMapper">
    <delete id="deleteByDeviceKey">

		delete from momg_schedule_setting where device_code = #{item}

	</delete>

<!--    <select id="selectList" resultType="com.yuanqiao.insight.service.ScheduleSetting.entity.ScheduleSetting">

		select * from momg_schedule_setting where status = 1

	</select>-->

	<select id="selectByDeviceCode" resultType="com.yuanqiao.insight.service.ScheduleSetting.entity.ScheduleSetting">

		select * from momg_schedule_setting where device_code = #{deviceKey}

	</select>

    <select id="selectByTransferCodesAndProId" resultType="com.yuanqiao.insight.service.ScheduleSetting.entity.ScheduleSetting">

		select * from momg_schedule_setting
		where
		    device_code in (select device_code from momg_device_info where product_id = #{productId})
		and
		    protocol in
		<foreach collection="transferCodeList" item="protocol" open="(" separator="," close=")">
			#{protocol}
		</foreach>

	</select>

    <update id="updateByDeviceKey">

		update momg_schedule_setting set connect_param = #{connParam} where device_code = #{deviceKey}

	</update>

	<update id="updateStatusByDeviceCode">

		update momg_schedule_setting set status = #{enable} where device_code = #{item}

	</update>

</mapper>
