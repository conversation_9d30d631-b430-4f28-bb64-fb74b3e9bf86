package com.yuanqiao.insight.service.device.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.List;


/**
 *设备产品表联合查询
 * <AUTHOR>
 */
@Data
@ApiModel(value = "device_product",description = "白名单")
public class DeviceProduct implements Serializable {
    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    /**名称*/
    @Excel(name = "名称", width = 15)
    @ApiModelProperty(value = "名称")
    private java.lang.String name;
    /**产品id*/
    @Excel(name = "产品id", width = 15)
    @ApiModelProperty(value = "产品id")
    private java.lang.String productId;
    /**类型id*/
    @Excel(name = "类型id", width = 15)
    @ApiModelProperty(value = "类型id")
    private java.lang.String categoryId;
    /**资产id*/
    @Excel(name = "资产id", width = 15)
    @ApiModelProperty(value = "资产id")
    private java.lang.String assetsId;
    /**设备唯一标识*/
    @Excel(name = "设备唯一标识", width = 15)
    @ApiModelProperty(value = "设备唯一标识")
    private java.lang.String deviceCode;
    /**描述*/
    @Excel(name = "描述", width = 15)
    @ApiModelProperty(value = "描述")
    private java.lang.String description;
    /**删除标识1:删除*/
    @Excel(name = "删除标识1:删除", width = 15)
    @ApiModelProperty(value = "删除标识1:删除")
    private java.lang.Integer delflag;
    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
    /**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;

    /**用来展示是否在线*/
    @Excel(name = "/用来展示是否在线", width = 15)
    @ApiModelProperty(value = "/用来展示是否在线")
    private int status;

    /**资产名称*/
    @Excel(name = "资产名称", width = 15)
    @ApiModelProperty(value = "资产名称")
    private String assetsName;

    /**中文名*/
    @Excel(name = "中文名", width = 15)
    @ApiModelProperty(value = "中文名")
    private String displayName;
    /**设备分类*/
    @Excel(name = "设备分类ID", width = 15)
    @ApiModelProperty(value = "设备分类ID")
    private String deviceCategoryId;
    /**设备分类*/
    @Excel(name = "设备分类名称", width = 15)
    @ApiModelProperty(value = "设备分类名称")
    private String deviceCategoryName;



    /**是否上线*/
    @Excel(name = "是否上线", width = 15)
    @ApiModelProperty(value = "是否上线")
    private String isOnline;
    /**图标*/
    @Excel(name = "图标", width = 15)
    @ApiModelProperty(value = "图标")
    private String icon;
    /**描述*/
    @Excel(name = "描述", width = 15)
    @ApiModelProperty(value = "描述")
    private String remark;
    /**描述*/
    @Excel(name = "采集模式", width = 15)
    @ApiModelProperty(value = "采集模式")
    private String productType;
    /**传输协议*/
    @Excel(name = "传输协议", width = 15)
    @ApiModelProperty(value = "传输协议")
    private String transferProtocol;
    /**资产分类*/
    @ApiModelProperty(value = "资产分类ID")
    private String assetsCategoryId;
    /**资产分类*/
    @Excel(name = "资产分类", width = 15)
    @ApiModelProperty(value = "资产分类名称")
    private String assetsCategoryName;
    /**拉模式job单元*/
    @Excel(name = "拉模式job单元", width = 15)
    @ApiModelProperty(value = "拉模式job单元")
    private String jobClass;
    /**拉模式job单元*/
    @Excel(name = "拉模式job单元", width = 15)
    @ApiModelProperty(value = "拉模式job单元")
    private String collectType;

    private List<DeviceConnectTemplate> deviceConnectTemplates;

}

