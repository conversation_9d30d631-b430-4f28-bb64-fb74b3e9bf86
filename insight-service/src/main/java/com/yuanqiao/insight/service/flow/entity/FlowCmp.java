package com.yuanqiao.insight.service.flow.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/9/16
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("flow_cmp")
public class FlowCmp {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 所属集合
     */
    private String parentId;
    /**
     * 组件唯一标识
     */
    @ApiModelProperty(value = "组件唯一标识")
    private String code;
    /**
     * 组件名称
     */
    @ApiModelProperty(value = "组件名称")
    private String name;
    /**
     * 参数模型
     */
    @ApiModelProperty(value = "参数模型")
    private String paramModel;
    /**
     * 参数格式
     */
    @ApiModelProperty(value = "参数类型")
    private String paramType;
    /**
     * 组件描述
     */
    @ApiModelProperty(value = "组件描述")
    private String cmpDesc;
    /**
     * 组件分组
     */
    @ApiModelProperty(value = "组件分组")
    private String cmpGroup;
    /**
     * 组件描述
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "组件分组")
    private String cmpGroupText;
    /**
     * 组件图标
     */
    @ApiModelProperty(value = "组件图标")
    private String cmpIcon;
    /**
     * 组件协议
     * JDBC SNMP JMX IPMI
     */
    @ApiModelProperty(value = "组件协议")
    private String protocol;
    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private Integer cmpSerial;
    /**
     * 是否是组件
     * 0不是；1是
     */
    @ApiModelProperty(value = "是否是组件")
    private String isCmp;
    /**
     * 删除标识
     */
    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;

    @TableField(exist = false)
    private String parentText;

}
