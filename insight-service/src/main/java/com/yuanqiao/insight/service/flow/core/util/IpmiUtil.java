package com.yuanqiao.insight.service.flow.core.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.service.ipmi.IpmiControlService;
import lombok.NoArgsConstructor;

@NoArgsConstructor
public class IpmiUtil {

    private String hostname, username, password;

    private final IpmiControlService ipmiControlService = new IpmiControlService();

    private IpmiUtil(String hostname, String username, String password) {
        this.hostname = hostname;
        this.username = username;
        this.password = password;
    }

    public static IpmiUtil init(String hostname, String username, String password) {
        return new IpmiUtil(hostname, username, password);
    }

    public JSONObject getRespondByIPMIMap() throws Exception {
        return JSONObject.parseObject(JSON.toJSONString(ipmiControlService.getRespondByIPMIMap(hostname, username, password)));
    }

    /**
     * 对外提供的指令执行
     * @param operate
     * @return
     */
    public String action(String operate) {
        return ipmiControlService.action(hostname, username, password, operate);
    }
}
