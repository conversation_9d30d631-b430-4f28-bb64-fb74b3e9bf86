package com.yuanqiao.insight.service.device.service;

import com.github.yulichang.base.MPJBaseService;
import com.yuanqiao.insight.service.device.entity.MomgDeviceGroup;
import com.yuanqiao.insight.service.device.entity.MomgGroup2User;
import com.yuanqiao.insight.service.device.entity.UserVo;

import java.util.List;

public interface IMomgGroup2UserService extends MPJBaseService<MomgGroup2User> {

    List<UserVo> getUsersByGroupId(String groupId);

    List<MomgDeviceGroup> getGroupsByUsername(String username);
}
