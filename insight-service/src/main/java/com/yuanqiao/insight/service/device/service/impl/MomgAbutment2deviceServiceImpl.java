package com.yuanqiao.insight.service.device.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.service.device.entity.DeviceInfo;
import com.yuanqiao.insight.service.device.entity.MomgAbutment2Device;
import com.yuanqiao.insight.service.device.mapper.MomgAbutment2deviceMapper;
import com.yuanqiao.insight.service.device.service.IMomgAbutment2deviceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 第三方对接系统
 * @Author: jeecg-boot
 * @Date: 2021-03-21
 * @Version: V1.0
 */
@Slf4j
@Service
public class MomgAbutment2deviceServiceImpl extends ServiceImpl<MomgAbutment2deviceMapper, MomgAbutment2Device> implements IMomgAbutment2deviceService {

    @Autowired
    private MomgAbutment2deviceMapper abutment2deviceMapper;

    @Override
    public List<MomgAbutment2Device> getAbutmentDevsBySysIdAndDevCode(String systemId, String deviceCode) {
        return abutment2deviceMapper.getAbutmentDevsBySysIdAndDevCode(systemId, deviceCode);
    }

    @Override
    public Page<DeviceInfo> getDeviceWithAlarm(Page<DeviceInfo> page, String systemId) {
        return abutment2deviceMapper.getDeviceWithAlarm(page, systemId);
    }
}
