package com.yuanqiao.insight.service.flow.core.cmp.compont.common.date;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.service.flow.core.cmp.compont.common.CalculateComponent;
import com.yuanqiao.insight.service.flow.core.util.CmpUtils;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/9/27
 */
@Component
public class CmpDateOffset extends CalculateComponent {

    @Override
    public void process() throws Exception {
        JSONObject cache = this.getCache();
        JSONObject param = this.getCmpParam(4);
        //使用json进行日期，数字的转换
        int numeric = Integer.parseInt(String.valueOf(CmpUtils.getParam(param.getString("numeric"), cache)));
        String unit = param.getString("unit");
        String format = param.getString("format");
        String mappingStr = param.getString("loopMapping");
        JSONObject res = new JSONObject();
        final Object data = this.loop(mappingStr, res);
        Date date;
        final Boolean isLoop = res.getBoolean("isLoop");
        if (Boolean.TRUE.equals(isLoop)) {
            if (data instanceof JSONObject) {
                date = DateUtil.parse(String.valueOf(CmpUtils.getParam(param.getString("date"), (JSONObject) data)));
            } else {
                date = DateUtil.parse(String.valueOf(data));
            }
        } else {
            date = DateUtil.parse(String.valueOf(CmpUtils.getParam(param.getString("date"), cache)));
        }

        SimpleDateFormat simple = new SimpleDateFormat(format);
        String execute = simple.format(CmpUtils.nextDate(date, numeric, unit));

        //投递数据
        res.put("result", execute);
        this.slot(res);
    }
}
