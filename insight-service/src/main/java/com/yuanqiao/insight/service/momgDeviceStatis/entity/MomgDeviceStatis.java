package com.yuanqiao.insight.service.momgDeviceStatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yuanqiao.insight.common.util.common.TimeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

/**
 * @Description: 设备在线统计
 * @Author: jeecg-boot
 * @Date: 2021-09-24
 * @Version: V1.0
 */
@Data
@TableName("momg_device_statis")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "momg_device_statis对象", description = "设备在线统计")
public class MomgDeviceStatis implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    /**
     * 创建日期
     */

    @ApiModelProperty(value = "创建日期")
    private String createTime;
    /**
     * 设备id
     */
    @Excel(name = "设备id", width = 15)
    @ApiModelProperty(value = "设备id")
    private java.lang.String deviceId;
    /**
     * 设备唯一编码
     */
    @Excel(name = "设备唯一编码", width = 15)
    @ApiModelProperty(value = "设备唯一编码")
    private java.lang.String deviceCode;
    /**
     * 0: 开机 1： 关机
     */
    @Excel(name = "0: 开机 1： 关机", width = 15)
    @ApiModelProperty(value = "0: 开机 1： 关机")
    private java.lang.String deviceType;
    /**
     * 设备产品分类
     */
    private String deviceCategory;
    /**
     * 第一次开机时间
     */
    @Excel(name = "第一次开机时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "第一次开机时间")
    private java.util.Date onFristTime;
    /**
     * 最后一次开机时间
     */
    @Excel(name = "最后一次开机时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "最后一次开机时间")
    private java.util.Date onLastTime;
    /**
     * 最后一次关机时间
     */
    @Excel(name = "最后一次关机时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "最后一次关机时间")
    private java.util.Date offLastTime;
    /**
     * 开机次数
     */
    @Excel(name = "开机次数", width = 15)
    @ApiModelProperty(value = "开机次数")
    private java.lang.Integer onCount;
    /**
     * 开机总时长h
     */
    //@Excel(name = "开机总时长MS", width = 15)
    @ApiModelProperty(value = "开机总时长MS")
    private Long onTotal;

    @Excel(name = "开机总时长", width = 15)
    @TableField(exist = false)
    private String onTotal_text;

    @Excel(name = "cpu类型", width = 15, dicCode = "cpuType")
    @TableField(exist = false)
    @Dict(dicCode = "cpuType")
    private String cpuType;

    @Excel(name = "操作系统类型", width = 15, dicCode = "os_type")
    @TableField(exist = false)
    @Dict(dicCode = "os_type")
    private String osType;

    @TableField(exist = false)
    private String startTime, endTime, name, ip, username, momgDeptName, SN;
    @TableField(exist = false)
    private String deviceType1;

    @TableField(exist = false)
    private Integer deviceType2;


    public String getDeviceType1() {
        if (deviceType2 == null) {
            this.deviceType1 = "";
        } else if (deviceType2 == 0) {
            this.deviceType1 = "服务器";
        } else if (deviceType2 == 6) {
            this.deviceType1 = "桌面机";
        } else if (deviceType2 == 8) {
            this.deviceType1 = "笔记本";
        }

        return deviceType1;
    }

    public void setDeviceType1(String deviceType1) {
        this.deviceType1 = deviceType1;
    }

    @TableField(exist = false)
    private String gatewayCode, mac;

    public String getOnTotal_text(){
        return TimeUtils.getDistanceTime(this.getOnTotal());
    }

}
