package com.yuanqiao.insight.service.product.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("momg_product_transfer_job")
@ApiModel(value="momg_product_transfer_job对象", description="产品传输协议JOB三者关联表")
public class MomgProductTransferJob {
    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /**产品ID*/
    @ApiModelProperty(value = "产品ID")
    private String productId;

    /**传输协议表ID*/
    @ApiModelProperty(value = "传输协议表ID")
    private String transferProtocolId;

    /**产品JOB表ID*/
    @ApiModelProperty(value = "产品JOB表ID")
    private String productJobId;

    /**采集频率*/
    @ApiModelProperty(value = "采集频率")
    private Integer rate;

    /**采集频率单位*/
    @ApiModelProperty(value = "采集频率单位")
    private String unit;

    /**采集频率CRON表达式*/
    @ApiModelProperty(value = "采集频率cron")
    private String cron;


    @TableField(exist = false)
    private String transferProtocol;
    @TableField(exist = false)
    private String collectType;
    @TableField(exist = false)
    private String jobValue;




}
