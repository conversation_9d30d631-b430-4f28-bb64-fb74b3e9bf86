package com.yuanqiao.insight.service.device.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuanqiao.insight.service.device.entity.DeviceScanTask;
import com.yuanqiao.insight.service.devopsip.entity.DevopsIpSegment;
import com.yuanqiao.insight.service.devopsip.entity.DevopsIpSubnet;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Description: 设备扫描任务表
 * @Author: jeecg-boot
 * @Date: 2024-03-01
 * @Version: V1.0
 */
@Mapper
public interface DeviceScanTaskMapper extends BaseMapper<DeviceScanTask> {

    List<DevopsIpSubnet> queryListBySubnetGroupIds(List<String> subnetGroupIds);

    List<DevopsIpSegment> queryListBySubnetIds(List<String> subnetIds);

    List<DevopsIpSegment> queryListBySegmentIds(List<String> segmentIds);

}
