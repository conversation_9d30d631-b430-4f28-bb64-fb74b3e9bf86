package com.yuanqiao.insight.service.device.service;

import com.alibaba.fastjson.JSONArray;
import com.github.yulichang.base.MPJBaseService;
import com.yuanqiao.insight.service.device.entity.MomgConfigureBackTask;

/**
 * @Description: 网络专题 - 设备定时备份还原任务
 * @Author: jeecg-boot
 * @Date:   2021-03-21
 * @Version: V1.0
 */
public interface IMomgConfigureBackTaskService extends MPJBaseService<MomgConfigureBackTask> {

    void addTask(MomgConfigureBackTask momgConfigureBackTask);

    void editTask(MomgConfigureBackTask momgConfigureBackTask);

    void removeTask(JSONArray ids);

    void addOneShot(MomgConfigureBackTask momgConfigureBackTask);

    JSONArray buildDeviceCommandArray(String taskId);
}
