package com.yuanqiao.insight.service.device.service.impl;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.service.device.entity.DeviceScanTask;
import com.yuanqiao.insight.service.device.mapper.DeviceScanTaskMapper;
import com.yuanqiao.insight.service.device.service.IDeviceScanTaskService;
import com.yuanqiao.insight.service.devopsip.entity.DevopsIpSegment;
import com.yuanqiao.insight.service.devopsip.entity.DevopsIpSubnet;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.mq.RedisMq;
import org.jeecg.common.mq.stream.Streams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * @Description: 设备扫描任务表
 * @Author: jeecg-boot
 * @Date: 2024-03-01
 * @Version: V1.0
 */
@Service
public class DeviceScanTaskServiceImpl extends ServiceImpl<DeviceScanTaskMapper, DeviceScanTask> implements IDeviceScanTaskService {

    @Autowired
    protected RedisMq redisMq;
    @Autowired
    private DeviceScanTaskMapper deviceScanTaskMapper;

    private static final String JOB_CLASS_NAME = "com.yuanqiao.insight.job.ScanJob";

    @Transactional(rollbackFor = Exception.class)
    public Result<?> saveTask(DeviceScanTask deviceScanTask) {
        List<DeviceScanTask> l = this.list(new QueryWrapper<DeviceScanTask>().eq("task_name", deviceScanTask.getTaskName()));
        if (CollUtil.isNotEmpty(l)) {
            return Result.error("任务名称重复！");
        }
        boolean success = this.save(deviceScanTask);
        // 启用且周期执行
        if (success && deviceScanTask.getIsEnable() == 1 && deviceScanTask.getExecuteType() == 1) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("taskId", deviceScanTask.getId());
            jsonObject.put("executeCron", deviceScanTask.getExecuteCron());
            jsonObject.put("gatewayCode", deviceScanTask.getGatewayCode());
            jsonObject.put("job", JOB_CLASS_NAME);
            jsonObject.put("operateType", "add");
            redisMq.publish(Streams.ABUTMENT_PULL_JOB_CONTROL, jsonObject);
        }

        return Result.OK("操作成功!");
    }

    @Transactional(rollbackFor = Exception.class)
    public Result<?> updateTask(DeviceScanTask deviceScanTask) {
        List<DeviceScanTask> l = this.list(new QueryWrapper<DeviceScanTask>().eq("task_name", deviceScanTask.getTaskName()).ne("id", deviceScanTask.getId()));
        if (CollUtil.isNotEmpty(l)) {
            return Result.error("任务名称重复！");
        }

        // 启用且周期执行
        if (deviceScanTask.getIsEnable() == 1 && deviceScanTask.getExecuteType() == 1) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("taskId", deviceScanTask.getId());
            jsonObject.put("executeCron", deviceScanTask.getExecuteCron());
            jsonObject.put("gatewayCode", deviceScanTask.getGatewayCode());
            jsonObject.put("job", JOB_CLASS_NAME);
            jsonObject.put("operateType", "reload");
            redisMq.publish(Streams.ABUTMENT_PULL_JOB_CONTROL, jsonObject);
        } else {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("taskId", deviceScanTask.getId());
            jsonObject.put("executeCron", deviceScanTask.getExecuteCron());
            jsonObject.put("gatewayCode", deviceScanTask.getGatewayCode());
            jsonObject.put("operateType", "delete");
            redisMq.publish(Streams.ABUTMENT_PULL_JOB_CONTROL, jsonObject);
        }

        boolean success = this.updateById(deviceScanTask);
        return Result.OK("操作成功!");
    }

    public void deleteTask(String id) {
        boolean success = this.removeById(id);
        if (success) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("taskId", id);
            jsonObject.put("operateType", "delete");
            redisMq.publish(Streams.ABUTMENT_PULL_JOB_CONTROL, jsonObject);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void pause(DeviceScanTask deviceScanTask) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("taskId", deviceScanTask.getId());
        jsonObject.put("gatewayCode", deviceScanTask.getGatewayCode());
        jsonObject.put("operateType", "delete");
        redisMq.publish(Streams.ABUTMENT_PULL_JOB_CONTROL, jsonObject);

        deviceScanTask.setIsEnable(0);
        super.baseMapper.updateById(deviceScanTask);
    }

    @Transactional(rollbackFor = Exception.class)
    public void resumeJob(DeviceScanTask deviceScanTask) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("taskId", deviceScanTask.getId());
        jsonObject.put("gatewayCode", deviceScanTask.getGatewayCode());
        jsonObject.put("job", JOB_CLASS_NAME);
        jsonObject.put("operateType", "reload");
        redisMq.publish(Streams.ABUTMENT_PULL_JOB_CONTROL, jsonObject);

        deviceScanTask.setIsEnable(1);
        super.baseMapper.updateById(deviceScanTask);
    }

    public void execute(DeviceScanTask deviceScanTask) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("taskId", deviceScanTask.getId());
        jsonObject.put("gatewayCode", deviceScanTask.getGatewayCode());
        jsonObject.put("job", JOB_CLASS_NAME);
        jsonObject.put("operateType", "execute");
        redisMq.publish(Streams.ABUTMENT_PULL_JOB_CONTROL, jsonObject);
    }

    @Override
    public List<DevopsIpSegment> querySegmentListById(String type, List<String> ids) {
        List<DevopsIpSegment> devopsIpSegments = new ArrayList<>();
        if (ids.isEmpty()) {
            return devopsIpSegments;
        }

        switch (type) {
            case "-1":
                devopsIpSegments = deviceScanTaskMapper.queryListBySegmentIds(null);
                break;
            case "0":
                List<DevopsIpSubnet> devopsIpSubnets = deviceScanTaskMapper.queryListBySubnetGroupIds(ids);
                List<String> subnetIds = devopsIpSubnets.stream().map(DevopsIpSubnet::getId).collect(Collectors.toList());
                if (subnetIds.isEmpty()) {
                    return devopsIpSegments;
                }
                devopsIpSegments = deviceScanTaskMapper.queryListBySubnetIds(subnetIds);
                break;
            case "1":
                devopsIpSegments = deviceScanTaskMapper.queryListBySubnetIds(ids);
                break;
            case "2":
                devopsIpSegments = deviceScanTaskMapper.queryListBySegmentIds(ids);
                break;
            default:
                break;
        }
        return devopsIpSegments;
    }
}
