package com.yuanqiao.insight.service.device.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.service.device.entity.DeviceScanRecord;
import com.yuanqiao.insight.service.device.mapper.DeviceScanRecordMapper;
import com.yuanqiao.insight.service.device.service.IDeviceScanRecordService;
import org.springframework.stereotype.Service;


/**
 * @Description: 设备扫描结果表
 * @Author: jeecg-boot
 * @Date: 2024-03-01
 * @Version: V1.0
 */
@Service
public class DeviceScanRecordServiceImpl extends ServiceImpl<DeviceScanRecordMapper, DeviceScanRecord> implements IDeviceScanRecordService {

}
