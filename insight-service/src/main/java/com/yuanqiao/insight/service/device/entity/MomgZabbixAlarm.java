package com.yuanqiao.insight.service.device.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: Zabbix问题
 * @Author: jeecg-boot
 * @Date: 2021-03-21
 * @Version: V1.0
 */
@Data
@TableName("momg_zabbix_alarm")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "momg_zabbix_alarm对象", description = "Zabbix问题")
public class MomgZabbixAlarm implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;


    @Excel(name = "主机ID", width = 15)
    private String hostId;

    @Excel(name = "问题创建事件ID", width = 15)
    private String eventId;

    @Excel(name = "相关对象ID", width = 15)
    private String objectId;

    @Excel(name = "问题创建时间（unix时间戳）", width = 15)
    private String clock;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "问题创建时间")
    private Date clockTime;

    @Excel(name = "问题恢复事件ID", width = 15)
    private String rEventId;

    @Excel(name = "问题恢复时间（unix时间戳）", width = 15)
    private String rClock;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "问题恢复时间")
    private Date rClockTime;

    @Excel(name = "问题名称", width = 15)
    private String name;

    @Excel(name = "问题解决状态", width = 15)
    private String status;

    @Excel(name = "问题确认状态", width = 15)
    private String acknowledged;

    @Excel(name = "问题持续时间", width = 15)
    private String duration;

    @Excel(name = "问题严重性", width = 15)
    private String severity;

}
