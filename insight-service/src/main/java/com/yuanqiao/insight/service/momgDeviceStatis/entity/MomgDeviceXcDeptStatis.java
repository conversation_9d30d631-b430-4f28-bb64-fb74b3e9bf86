package com.yuanqiao.insight.service.momgDeviceStatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 设备单位统计
 * @Author: jeecg-boot
 * @Date:   2025-04-24
 * @Version: V1.0
 */
@Data
@TableName("momg_device_xc_dept_statis")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="momg_device_xc_dept_statis对象", description="设备单位统计")
@NoArgsConstructor
@AllArgsConstructor
public class MomgDeviceXcDeptStatis {
    
	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
	private java.lang.String id;
	/**统计日期*/
	@Excel(name = "统计日期", width = 15)
    @ApiModelProperty(value = "统计日期")
	private java.lang.String statisticTime;
	/**单位id*/
	@Excel(name = "单位id", width = 15)
    @ApiModelProperty(value = "单位id")
	private java.lang.String deptId;
	/**设备总数*/
	@Excel(name = "设备总数", width = 15)
    @ApiModelProperty(value = "设备总数")
	private java.lang.Integer deviceTotal;
	/**开机数量*/
	@Excel(name = "开机数量", width = 15)
    @ApiModelProperty(value = "开机数量")
	private java.lang.Integer onlineCount;
	/**未开机数量*/
	@Excel(name = "未开机数量", width = 15)
    @ApiModelProperty(value = "未开机数量")
	private java.lang.Integer offlineCount;
	/**国产化数量*/
	@Excel(name = "国产化数量", width = 15)
    @ApiModelProperty(value = "国产化数量")
	private java.lang.Integer nationalCount;

	public MomgDeviceXcDeptStatis(String statisticTime, Integer deviceTotal, Integer nationalCount) {
		this.statisticTime = statisticTime;
		this.deviceTotal = deviceTotal;
		this.nationalCount = nationalCount;
	}
}
