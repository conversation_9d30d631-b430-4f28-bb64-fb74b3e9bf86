package com.yuanqiao.insight.service.product.service.impl;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.yuanqiao.insight.service.product.entity.MomgProductPanelInfo;
import com.yuanqiao.insight.service.product.mapper.ProductPanelInfoMapper;
import com.yuanqiao.insight.service.product.service.IProductPanelInfoService;
import org.springframework.stereotype.Service;


/**
 * @Description: 产品面板
 * @Author: jeecg-boot
 * @Date: 2021-03-03
 * @Version: V1.0
 */
@Service
public class ProductPanelInfoServiceImpl extends MPJBaseServiceImpl<ProductPanelInfoMapper, MomgProductPanelInfo> implements IProductPanelInfoService {

}
