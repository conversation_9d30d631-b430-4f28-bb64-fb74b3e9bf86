package com.yuanqiao.insight.service.momgDeviceStatis.entity;


import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("momg_statis_city")
@EqualsAndHashCode(callSuper = false)
public class CityStatisVo implements Serializable {


    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    @Excel(name = "创建时间", width = 20)
    private String createTime;

    @Excel(name = "区县", width = 20)
    private String cityName;

    @Excel(name = "网络情况", width = 20)
    private Integer gatewayType;

    @Excel(name = "注册总数", width = 20)
    private int count;


    @Excel(name = "未开机", width = 20)
    private int offCount;

    @Excel(name = "已开机（联网超过30分钟）", width = 20)
    private int yesThreeCount;

    @Excel(name = "已开机（联网不足30分钟）", width = 20)
    private int noThreeCount;

    @Excel(name = "有效数量", width = 20)
    private int  effectiveCount;

    private String cityId;

    @TableField(exist = false)
    private String IDs;
    @TableField(exist = false)
    private String gatewayCode;
    @TableField(exist = false)
    private Integer pageSize;
    @TableField(exist = false)
    private Integer pageNo;
}
