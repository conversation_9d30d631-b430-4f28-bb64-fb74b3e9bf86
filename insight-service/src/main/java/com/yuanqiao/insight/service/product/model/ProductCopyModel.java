package com.yuanqiao.insight.service.product.model;

import com.yuanqiao.insight.service.device.entity.MomgProductConfigureManage;
import com.yuanqiao.insight.service.flow.entity.FlowChain;
import com.yuanqiao.insight.service.product.entity.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class ProductCopyModel {

    /**
     * 产品
     */
    private Product product;
    /**
     * 协议-JOB 中间表数据
     */
    private List<MomgProductTransferJob> productTransferJobList;
    /**
     * 物模型
     */
    private List<ProertyMetadata> metadataList;
    /**
     * 编排流程
     */
    private List<FlowChain> chainList;
    /**
     * 连接参数
     */
    private List<DeviceConnectTemplateInfo> deviceConnectTemplateInfos;
    /**
     * 面板信息
     */
    private List<MomgProductPanelInfo> productPanelInfos;
    /**
     * 面板节点
     */
    private List<MomgProductPanelNode> productPanelNodes;
    /**
     * 产品配置
     */
    private List<MomgProductConfigureManage> productConfigureManages;
    /**
     * 功能定义
     */
    private List<DeviceControlCommand> deviceControlCommands;
    /**
     * 功能定义-参数
     */
    private List<DeviceControlCommandExtend> deviceControlCommandExtendList;
}
