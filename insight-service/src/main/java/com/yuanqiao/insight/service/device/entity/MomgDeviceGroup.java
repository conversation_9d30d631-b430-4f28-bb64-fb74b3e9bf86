package com.yuanqiao.insight.service.device.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 设备分组
 * @Author: jeecg-boot
 * @Date: 2021-03-21
 * @Version: V1.0
 */
@Data
@TableName("momg_device_group")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "momg_device_group对象", description = "产品类别")
public class MomgDeviceGroup implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;


    @Excel(name = "分组名称", width = 15)
    private String groupName;

    @Excel(name = "描述", width = 15)
    private String description;

    @Excel(name = "是否最大权限(1 是；0 否)", width = 15, replace = { "否_0", "是_1" })
    private Integer isMaxPrivilege;

    @TableField(exist = false)
    private String userIds;

    @TableField(exist = false)
    private String userNames;

    @TableField(exist = false)
    private Integer bindCount;

    @TableField(exist = false)
    private String deviceIds;

}
