package com.yuanqiao.insight.service.cloud;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.common.util.cloud.ZStackRestApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ZStackCloudService {

    public String action(String ip, String port, String username, String password, String methodName, String uId) {
        String res = "此命令执行失败!";
        try {
            String token = ZStackRestApi.login(ip, port, username, password);
            if (StringUtils.isNotEmpty(token)) {
                switch (methodName) {
                    case "PowerOn":
                        boolean b = this.powerOn(token, ip, port, uId);
                        if (b) {
                            res = "开机命令执行成功!";
                        }
                        break;
                    case "PowerOff":
                        boolean offVm = this.powerOff(token, ip, port, uId);
                        if (offVm) {
                            res = "关机命令执行成功!";
                        }
                        break;
                }
            } else {
                return "连接超时!";
            }
        } catch (Exception e) {
            res = "此命令执行失败!";
            log.error("" + e);
        }
        return res;
    }

    private boolean powerOn(String token, String ip, String port, String uId) {
        String jsonResultOn = ZStackRestApi.start(ip, port, uId, token);
        JSONObject jsonObject = JSONObject.parseObject(jsonResultOn);
        log.error("ZStack云平台设备开机接口返回值为：" + jsonObject);
        if (jsonObject.getJSONObject("error") != null) {
            return false;
        } else {
            return true;
        }
    }

    private boolean powerOff(String token, String ip, String port, String uId) {
        String jsonResultOff = ZStackRestApi.stop(ip, port, uId, token);
        JSONObject jsonObject = JSONObject.parseObject(jsonResultOff);
        log.error("ZStack云平台设备关机接口返回值为：" + jsonObject);
        if (jsonObject.getJSONObject("error") != null) {
            return false;
        } else {
            return true;
        }
    }
}