<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.service.device.mapper.MomgGroup2DeviceMapper">
    <select id="detectionSameGroup" resultType="java.lang.String">
        SELECT group_id FROM momg_group2device  WHERE device_id in
        <foreach item="id" collection="ids"  open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY group_id
        HAVING COUNT(DISTINCT device_id) =  #{sum};
    </select>

    <select id="getUsersByDeviceId" resultType="com.yuanqiao.insight.service.device.entity.UserVo">
        SELECT  c.username as userName,c.realname as realName  FROM  momg_group2user a
                                    RIGHT JOIN momg_group2device b ON  a.group_id=b.group_id
                                    RIGHT JOIN sys_users c ON  a.user_id=c.username
        WHERE b.device_id=#{deviceId}
    </select>

    <select id="getGroupsByDeviceId" resultType="java.lang.String">
        SELECT a.group_name  FROM  momg_device_group a
                        LEFT JOIN momg_group2device b ON  a.id=b.group_id
        WHERE b.device_id=#{deviceId} OR a.is_max_privilege='1'
    </select>

</mapper>
