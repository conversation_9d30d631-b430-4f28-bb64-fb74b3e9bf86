<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.service.momgDeviceStatis.mapper.MomgStatisReportTaskMappers">


    <select id="dynamicQuery" resultType="com.alibaba.fastjson.JSONObject">

        select *
        from ${table}
        <where>
            <if test="timeColumn != null and timeColumn != '' and startTime != null and startTime != '' and endTime != null and endTime != ''">
                ${timeColumn} between #{startTime} and #{endTime}
            </if>
        </where>

    </select>


</mapper>
