package com.yuanqiao.insight.service.device.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.service.device.entity.MomgAbutmentAlarm;
import com.yuanqiao.insight.service.device.mapper.MomgAbutmentAlarmMapper;
import com.yuanqiao.insight.service.device.service.IMomgAbutmentAlarmService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @Description: 第三方对接系统
 * @Author: jeecg-boot
 * @Date: 2021-03-21
 * @Version: V1.0
 */
@Slf4j
@Service
public class MomgAbutmentAlarmServiceImpl extends ServiceImpl<MomgAbutmentAlarmMapper, MomgAbutmentAlarm> implements IMomgAbutmentAlarmService {

}
