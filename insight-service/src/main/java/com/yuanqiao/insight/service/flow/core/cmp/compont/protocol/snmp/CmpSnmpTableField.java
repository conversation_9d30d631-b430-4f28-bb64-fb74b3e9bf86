package com.yuanqiao.insight.service.flow.core.cmp.compont.protocol.snmp;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.service.flow.core.cmp.compont.protocol.ProtocolComponent;
import com.yuanqiao.insight.service.flow.core.cmp.context.GlobalContext;
import com.yuanqiao.insight.service.flow.core.util.CmpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * table类型转换为更符合人类直觉的类型
 *
 * <AUTHOR>
 * @date 2022/9/6
 */
@Slf4j
@Component
public class CmpSnmpTableField extends ProtocolComponent {

    @Override
    public void process() throws Exception {
        GlobalContext globalContext = this.getContextBean(GlobalContext.class);
        JSONObject req = globalContext.getReq();
        String oid = req.getString("value");
        String type = req.getString("type");

        if (StringUtils.isNotBlank(oid)) {
            Map<String, String> part = globalContext.getSnmpUtils().getPDUWalk(oid);
            JSONObject jsonObject = new JSONObject();
            for (Map.Entry<String, String> entry : part.entrySet()) {
                jsonObject.put(entry.getKey().substring(oid.length() + 1), CmpUtils.typeConversion(entry.getValue(), type));
            }
            this.putValue(jsonObject);
        }

        this.setChain();
    }
}
