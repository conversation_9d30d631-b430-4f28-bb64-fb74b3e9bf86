package com.yuanqiao.insight.service.flow.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yuanqiao.insight.service.flow.entity.FlowChain;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/9/16
 */
public interface IFlowChainService extends IService<FlowChain> {

    Map<Integer, List<JSONObject>> gradeList(String productId);

    Map<Integer, List<JSONObject>> getDebugList(String productId,JSONObject object);
}
