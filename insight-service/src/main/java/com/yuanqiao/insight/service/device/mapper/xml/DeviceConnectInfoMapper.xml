<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.service.device.mapper.DeviceConnectInfoMapper">

    <insert id="addBatch">

        insert into momg_device_connect_info(device_id,device_key,connect_name,connect_value) values
        <foreach collection="list" item="deviceConnectInfo" separator=",">
            (
            #{deviceconnectInfo.getDeviceId()},
            null,
            #{deviceConnectInfo.getConnectCode()},
            #{deviceConnectInfo.getConnectValue()}
            )
        </foreach>
    </insert>

    <update id="updateConnects">

        update momg_device_connect_info
        set connect_value = #{connectValue}
        where device_id = #{deviceId}
          and template_id = #{templateId}

    </update>

    <!--<delete id="deleteByConnectName">-->

    <!--delete from momg_device_connect_info where device_id = #{deviceId} and connect_name = #{connectName}-->

    <!--</delete>-->

    <delete id="deleteByDeviceId">

        delete
        from momg_device_connect_info
        where device_id = #{id}

    </delete>

    <delete id="deleteByTemplateIdsAndProId">

        delete from momg_device_connect_info
        where
            template_id in
        <foreach item="templateId" collection="connectTemplateIdList" open="(" separator="," close=")">
            #{templateId}
        </foreach>
        and
            device_id in (select id from momg_device_info where product_id = #{productId})

    </delete>

    <!--<select id="selectConnectNameAndValue" resultType="com.yuanqiao.insight.service.product.entity.DeviceConnectTemplateInfo">-->

    <!--select b.display_name,a.*-->
    <!--from momg_device_connect_info a-->
    <!--left join-->
    <!--momg_device_connect_template_info b-->
    <!--on-->
    <!--a.connect_name = b.name-->
    <!--where-->
    <!--a.device_id = #{deviceId}-->

    <!--</select>-->


    <!--<select id="selectOldInfo" resultType="com.yuanqiao.insight.service.device.entity.DeviceConnectInfo">-->

    <!--select id,connect_name,connect_value from momg_device_connect_info where device_id = #{deviceId}-->

    <!--</select>-->

    <select id="selectConnByDeviceId" resultType="com.yuanqiao.insight.service.device.entity.DeviceConnectInfo">

        select c.connect_value, c.device_id, t.code as connectCode, t.id as templateId, t.display_name, t.data_type,
        t.control_type, p.code as transferProtocol, p.id as transferProtocolId, t.remark as description
        from
        momg_device_connect_info c
        left join
        momg_device_connect_template_info t
        on
        c.template_id = t.id
        left join
        momg_transfer_protocol p
        on
        t.transfer_protocol_id = p.id
        <where>
            t.id is not null
            <if test="deviceId != null and deviceId != ''">
                and c.device_id = #{deviceId}
            </if>
            <if test="code != null and code != ''">
                and p.code = #{code}
            </if>
        </where>


    </select>

    <select id="selectByDevId" resultType="com.yuanqiao.insight.service.device.entity.DeviceConnectInfo">

        select *
        from momg_device_connect_info
        where device_id = #{deviceId}

    </select>
    <select id="selectDeviceIpByDeviceCode" resultType="java.lang.String">
        SELECT conn.connect_value
        FROM momg_device_connect_info conn
                 LEFT JOIN momg_device_info info on info.id = conn.device_id
                 LEFT JOIN momg_device_connect_template_info tem on tem.id = conn.template_id
        WHERE info.device_code = #{deviceCode}
          and lower(tem.code) = 'ip';
    </select>

    <select id="selectByDevAndPro" resultType="com.yuanqiao.insight.service.device.entity.DeviceConnectInfo">

        select * from momg_device_connect_info where template_id in (select id from momg_device_connect_template_info where product_id = #{proId}) and device_id = #{devId}

    </select>

</mapper>
