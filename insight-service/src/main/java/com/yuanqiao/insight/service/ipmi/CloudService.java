package com.yuanqiao.insight.service.ipmi;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CloudService {

    public String action(String ip, String port, String username, String password, String methodName, String uId) {
        String res = "此命令执行失败!";
        String url = "https://" + ip + ":" + port + "/awstack-user/v1/internal/login";
        log.info("天熠虚拟化云平台URL：" + url);
        try {
            JSONObject paramMap1 = new JSONObject();
            paramMap1.put("userName", username);
            paramMap1.put("password", password);
            paramMap1.put("enterpriseLoginName", "awcloud");
            String login = HttpRequest.post(url)
                    .header("Content-Type", "application/json; charset=UTF-8")
                    .body(paramMap1.toString())
                    .execute().body();

            JSONObject jsonResult = JSONObject.parseObject(login);
            log.info("当前云主机设备登录接口code" + jsonResult.get("code"));
            if (jsonResult != null && jsonResult.get("code").equals("0")) {
                switch (methodName) {
                    case "PowerOn":
                        boolean b = this.powerOn(jsonResult, ip, port, uId);
                        if (b) {
                            res = "开机命令执行成功!";
                        }
                        break;
                    case "PowerOff":
                        boolean offVm = this.powerOff(jsonResult, ip, port, uId);
                        if (offVm) {
                            res = "关机命令执行成功!";
                        }
                        break;
                }
            } else {
                return "连接超时!";
            }
        } catch (Exception e) {
            res = "此命令执行失败!";
            log.error("" + e);
        }
        return res;
    }

    private boolean powerOn(JSONObject jsonResult, String ip, String port, String uId) {
        JSONObject data = jsonData(jsonResult);
        String token = (String) data.get("authToken");
        String regionKey = (String) data.get("regionKey");
        String enterpriseUid = (String) data.get("enterpriseUid");
        String url = "https://" + ip + ":" + port + "/awstack-resource/v1/server/os-start?ids=" + uId;
        String result = HttpRequest.post(url)
                .header("Content-Type", "application/json; charset=UTF-8")
                .header("X-Auth-Token", token)
                .header("X-Register-Code", regionKey)
                .header("X-enterprise_uid-Code", enterpriseUid)
                .execute().body();

        log.error("云平台设备开机接口返回值为：" + result);
        JSONObject jsonResultOn = JSONObject.parseObject(result);
        log.error("当前云平台设备开机接口返回值为：" + jsonResultOn);
        if (jsonResultOn != null && jsonResultOn.get("code").equals("0")) {
            return true;
        } else {
            return false;
        }
    }

    private boolean powerOff(JSONObject jsonResult, String ip, String port, String uId) {
        JSONObject data = jsonData(jsonResult);
        String token = (String) data.get("authToken");
        String regionKey = (String) data.get("regionKey");
        String enterpriseUid = (String) data.get("enterpriseUid");
        String url = "https://" + ip + ":" + port + "/awstack-resource/v1/server/os-stop?ids=" + uId;
        String result = HttpRequest.post(url)
                .header("Content-Type", "application/json; charset=UTF-8")
                .header("X-Auth-Token", token)
                .header("X-Register-Code", regionKey)
                .header("X-enterprise_uid-Code", enterpriseUid)
                .execute().body();

        log.error("当前云平台设备关机接口返回值为：" + result);
        JSONObject jsonResultOff = JSONObject.parseObject(result);
        if (jsonResultOff != null && jsonResultOff.get("code").equals("0")) {
            return true;
        } else {
            return false;
        }
    }

    private JSONObject jsonData(JSONObject jsonResult) {
        JSONObject jsonObject = (JSONObject) jsonResult.get("data");
        JSONObject jsonData = (JSONObject) jsonObject.get("data");
        return jsonData;
    }
}