package com.yuanqiao.insight.service.cloud;

import cn.hutool.http.HttpResponse;
import com.yuanqiao.insight.common.util.cloud.QiLinRestApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class QiLinCloudService {

    public String action(String authIp, String authPort, String calcIp, String calcPort, String username, String password, String methodName, String uId) {
        String res = "此命令执行失败!";
        try {
            HttpResponse response = QiLinRestApi.authToken(authIp, authPort, "admin", username, password);
            if (response.headers().get("X-Auth-Token") != null) {
                switch (methodName) {
                    case "PowerOn":
                        boolean b = this.powerOn(response, calcIp, calcPort, uId);
                        if (b) {
                            res = "开机命令执行成功!";
                        }
                        break;
                    case "PowerOff":
                        boolean offVm = this.powerOff(response, calcIp, calcPort, uId);
                        if (offVm) {
                            res = "关机命令执行成功!";
                        }
                        break;
                }
            } else {
                return "连接超时!";
            }
        } catch (Exception e) {
            res = "此命令执行失败!";
            log.error("" + e);
        }
        return res;
    }

    private boolean powerOn(HttpResponse response, String ip, String port, String uId) {
        String token = response.headers().get("X-Subject-Token").toString();
        log.error("麒麟云平台登录接口返回数据token" + token);
        String jsonResultOn = QiLinRestApi.start(ip, port, uId, token);
        log.error("麒麟云平台设备开机接口返回值为：" + jsonResultOn);
        if (jsonResultOn != null) {
            return false;
        } else {
            return true;
        }
    }

    private boolean powerOff(HttpResponse response, String ip, String port, String uId) {
        String token = response.headers().get("X-Subject-Token").toString();
        log.error("麒麟云平台登录接口返回数据token" + token);
        String jsonResultOff = QiLinRestApi.stop(ip, port, uId, token);
        log.error("麒麟云平台设备关机接口返回值为：" + jsonResultOff);
        if (jsonResultOff != null) {
            return false;
        } else {
            return true;
        }
    }
}