package com.yuanqiao.insight.service.momgDeviceStatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yuanqiao.insight.service.momgDeviceStatis.entity.MomgDeviceStatis;
import com.yuanqiao.insight.service.momgDeviceStatis.entity.MomgDeviceXcDeptStatis;
import com.yuanqiao.insight.service.momgDeviceStatis.entity.XcDeptStatisticVo;
import com.yuanqiao.insight.service.momgDeviceStatis.vo.DeviceXcDeptStatisticVo;

import java.util.List;

/**
 * @Description: 设备在线统计
 * @Author: jeecg-boot
 * @Date:   2021-09-24
 * @Version: V1.0
 */
public interface IMomgDeviceStatisService extends IService<MomgDeviceStatis> {

    List<XcDeptStatisticVo> listXcDeptStatisticVo(String startTime, String endTime, String deptId, List<String> myDepIds);

    List<DeviceXcDeptStatisticVo> listDeviceXcDeptStatisticVo(String time, String deptId);

    List<String> getDeptIdsByGroupIds(List<String> groupIds);

    List<MomgDeviceXcDeptStatis> selectDeviceXcDeptStatisticList();

    void saveDeviceStatis(String timePoint, List<MomgDeviceStatis> deviceStatisList);
}
