package com.yuanqiao.insight.service.flow.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/9/16
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("flow_chain")
public class FlowChain {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 流程唯一标识
     */
    @ApiModelProperty(value = "流程唯一标识")
    private String chainId;
    /**
     * 流程唯一标识
     */
    @ApiModelProperty(value = "流程名称")
    private String chainName;
    /**
     * 归属产品
     */
    @ApiModelProperty(value = "归属产品")
    private String productId;

    /**
     * 归属指标
     */
    @ApiModelProperty(value = "归属指标")
    private String metadataId;
    /**
     * 拓扑数据
     */
    @ApiModelProperty(value = "拓扑数据")
    private String topoDataJson;
    /**
     * 协议类型
     */
    @ApiModelProperty(value = "协议类型")
    private String protocol;
    /**
     * 流程描述
     */
    @ApiModelProperty(value = "流程描述")
    private String chainDesc;
    /**
     * 参数
     */
    @ApiModelProperty(value = "参数")
    private String params;
    /**
     * 流程执行链条
     */
    @ApiModelProperty(value = "流程执行链条")
    private String chain;
    /**
     * 流程结构
     */
    @ApiModelProperty(value = "流程结构")
    private String chainJson;
    /**
     * 删除标识
     */
    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;

    @TableField(exist = false)
    @ApiModelProperty(value = "组件标签缓存")
    private String tagCache;

}
