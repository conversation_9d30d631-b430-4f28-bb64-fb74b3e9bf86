<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.service.device.mapper.MomgAbutmentAlarmMapper">

    <select id="getAlarmsBySysId" resultType="com.yuanqiao.insight.service.device.entity.MomgAbutmentAlarm">
        SELECT
        a.*,
        dev.name AS deviceName,
        s.system_name as systemName
        FROM
        momg_abutment_alarm a
        LEFT JOIN
        momg_device_info dev
        ON
        a.device_code = dev.device_code
        left join
        momg_abutment_system s
        on
        a.system_id = s.id
        WHERE
        a.device_code IN (
        SELECT
        d.device_Code
        FROM
        momg_device_info d
        RIGHT JOIN momg_abutment2device a2d ON d.id = a2d.device_id
        <where>
            <if test="systemId != null and systemId.trim() != ''">
                and a2d.system_id = #{systemId}
            </if>
            <if test="devName != null and devName.trim() != ''">
                and d.name like concat('%',#{devName},'%')
            </if>
            <if test="alarmName != null and alarmName.trim() != ''">
                and a.alarm_name like concat('%',#{alarmName},'%')
            </if>
        </where>
        )
    </select>

</mapper>
