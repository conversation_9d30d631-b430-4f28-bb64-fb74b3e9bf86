package com.yuanqiao.insight.service.product.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.yuanqiao.insight.service.product.entity.Product;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;


/**
 * @Description: product
 * @Author: jeecg-boot
 * @Date:   2021-03-01
 * @Version: V1.0
 */
@Component
@Mapper
public interface ProductMapper extends MPJBaseMapper<Product> {

    List<Product> queryAllProduct();

    String selectNameById(String productId);

    String selectTranferProtocol(String productId);

    Product selectByDeviceId(String deviceId);

    List<Product> selectProductByCategoryId(String id);

    List<String>findDeviceIdByProId(@Param("list") List<String> list);

    Product getByDisplayName(String productName);

    List<String> selectIdByProductType();

    // 后续可继续追加参数
    List<Product> queryProductByCondition(String categoryId);

    Product getById(String productId);

    String getChilden(@Param("deviceKey") String deviceKey);

    List<Map<String, String>> selectJobsByProductId(@Param("productId") String productId);

    String selectCategoryByCID(@Param("assetsCategoryId") String assetsCategoryId);

    String getCategoryCodeByDeviceCode(@Param("deviceCode") String deviceCode);

    Product getProductByDeviceCode(@Param("deviceCode") String deviceCode);

    Product selectProductWithProtocol(@Param("productId") String productId, @Param("name") String name);

    List<Product> selectByProtocol(String protocol);
}
