package com.yuanqiao.insight.service.driveManage.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.service.driveManage.entity.MomgDriveInfo;
import com.yuanqiao.insight.service.driveManage.mapper.MomgDriveInfoMapper;
import com.yuanqiao.insight.service.driveManage.service.IMomgDriveInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @Description: 驱动管理表
 * @Author: jeecg-boot
 * @Date:   2021-03-13
 * @Version: V1.0
 */
@Slf4j
@Service
public class MomgDriveInfoServiceImpl extends ServiceImpl<MomgDriveInfoMapper, MomgDriveInfo> implements IMomgDriveInfoService {

}
