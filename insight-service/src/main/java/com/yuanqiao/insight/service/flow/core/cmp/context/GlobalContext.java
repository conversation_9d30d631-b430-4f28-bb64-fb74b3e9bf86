package com.yuanqiao.insight.service.flow.core.cmp.context;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.common.util.snmp.SNMPUtils;
import com.yuanqiao.insight.service.flow.core.cmp.context.cache.CalculateCache;
import com.yuanqiao.insight.service.flow.core.util.DBUtils;
import com.yuanqiao.insight.service.flow.core.util.IpmiUtil;
import com.yuanqiao.insight.service.flow.core.util.JmxUtils;
import com.yuanqiao.insight.service.flow.core.util.RESTUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/9/6
 */
@Setter
@Getter
@Accessors(chain = true)
public class GlobalContext {

    private CalculateCache calculateCache;

    /**
     * 公共结果
     */
    private final JSONObject commonMap = new JSONObject();
    /**
     * 连接工具
     */
    private Object connect;
    /**
     * 入参
     */
    private JSONObject req;

    /**
     * 是否开启debug
     */
    private boolean isDebug = false;
    /**
     * 当前登录用户id，用于webscoket
     */
    private String userId;
    /**
     * rest需要使用的token
     */
    private String token;

    public <T> T getConnect(Class<T> clazz) {
        if (connect != null && connect.getClass().equals(clazz)) {
            return (T) connect;
        }
        throw new RuntimeException("获取连接工具失败");
    }

    public DBUtils getDbUtils() {
        return getConnect(DBUtils.class);
    }

    public SNMPUtils getSnmpUtils() {
        return getConnect(SNMPUtils.class);
    }

    public JmxUtils getJmxUtils() {
        return getConnect(JmxUtils.class);
    }

    public RESTUtil getRESTUtil() {
        return getConnect(RESTUtil.class);
    }

    public IpmiUtil getIpmiUtil() {
        return getConnect(IpmiUtil.class);
    }

    @Override
    public String toString() {
        return "GlobalContext{" +
                "calculateCache=" + calculateCache +
                ", commonMap=" + commonMap +
                ", req=" + req +
                ", isDebug=" + isDebug +
                ", userId='" + userId + '\'' +
                '}';
    }
}
