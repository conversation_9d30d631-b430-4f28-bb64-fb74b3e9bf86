package com.yuanqiao.insight.service.flow.core.cmp.compont.common.collection;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.googlecode.aviator.AviatorEvaluator;
import com.yuanqiao.insight.service.flow.core.cmp.compont.common.CalculateComponent;
import com.yuanqiao.insight.service.flow.core.exception.CalculateException;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 对基本类型集合内元素求和
 *
 * <AUTHOR>
 * @date 2022/9/14
 */
@Component
public class CmpCollectionSum extends CalculateComponent {

    @Override
    public void process() throws Exception {
        JSONObject cache = this.getCache();
        JSONObject param = this.getCmpParam(1);

        JSONArray array = cache.getJSONArray(param.getString("collection"));

        if (CollUtil.isEmpty(array)) {
            throw new CalculateException(String.format("[%s.tag(%s)] 目标集合为空", this.getNodeId(), this.getTag()));
        }
        List<Double> collect = array.stream()
                .map(o -> Double.parseDouble(o.toString()))
                .filter(d -> d.compareTo(0D) > 0)
                .collect(Collectors.toList());

        Map<String, Object> env1 = new HashMap<>();
        env1.put("list", collect);

        Double execute = 0D;
        if (!collect.isEmpty()) {
            execute = (Double) AviatorEvaluator.execute("reduce(list,+,0)", env1);
        }
        //处理结果数据
        this.slot(execute);
    }
}
