package com.yuanqiao.insight.service.device.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 第三方对接系统
 * @Author: jeecg-boot
 * @Date: 2021-03-21
 * @Version: V1.0
 */
@Data
@TableName("momg_abutment_system")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "momg_abutment_system对象", description = "第三方对接系统")
public class MomgAbutmentSystem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;


    @Excel(name = "对接系统名称", width = 15)
    private String systemName;

    @Excel(name = "对接系统标识", width = 15)
    private String systemCode;

    @Excel(name = "身份识别码", width = 15)
    private String accessToken;

    @Excel(name = "接口路径前缀", width = 15)
    private String urlPrefix;

    @Excel(name = "推送接口路径", width = 15)
    private String requestUrl;

    @Excel(name = "采集模式0-推模式 1-拉模式", width = 15)
    private Integer collectMode;

    @Excel(name = "描述", width = 15)
    private String description;

    // 设备推送总波次
    @TableField(exist = false)
    private Integer deviceTotal;
    // 设备推送成功波次
    @TableField(exist = false)
    private Integer deviceSuccess;
    // 设备推送失败波次
    @TableField(exist = false)
    private Integer deviceFailed;
    // 告警推送总波次
    @TableField(exist = false)
    private Integer alarmTotal;
    // 告警推送成功波次
    @TableField(exist = false)
    private Integer alarmSuccess;
    // 告警推送失败波次
    @TableField(exist = false)
    private Integer alarmFailed;

    @TableField(exist = false)
    private MomgAbutmentTask abutmentTask;

}
