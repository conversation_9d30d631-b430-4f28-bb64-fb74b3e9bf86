<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.service.device.mapper.MomgGbaseRecordMapper">

    <select id="selectRecordPage" resultType="com.yuanqiao.insight.service.device.entity.MomgGbaseRecord">

        select r.*, t.task_name, m.db_instance
        from momg_gbase_record r
        left join
        momg_gbase_task t
        on
        r.task_id = t.id
        left join
        momg_gbase_manage m
        on
        r.database_id = m.id

        <where>

            <if test="gbaseRecord.recordType != null and gbaseRecord.recordType != ''">
                and r.record_type = #{gbaseRecord.recordType}
            </if>

            <if test="gbaseRecord.taskId != null and gbaseRecord.taskId != ''">
                and r.task_id = #{gbaseRecord.taskId}
            </if>

            <if test="gbaseRecord.databaseId != null and gbaseRecord.databaseId != ''">
                and r.database_id = #{gbaseRecord.databaseId}
            </if>

            <if test="gbaseRecord.taskName != null and gbaseRecord.taskName != ''">
                and r.task_id in (select gt.id from momg_gbase_task gt where gt.task_name like concat('%', #{gbaseRecord.taskName}, '%'))
            </if>

            <if test="gbaseRecord.startTimeCondition != null and gbaseRecord.startTimeCondition != ''">
                <if test="gbaseRecord.endTimeCondition != null and gbaseRecord.endTimeCondition != ''">
                    and r.start_time between #{gbaseRecord.startTimeCondition} and #{gbaseRecord.endTimeCondition}
                </if>
            </if>

        </where>

        order by r.start_time desc

    </select>

</mapper>
