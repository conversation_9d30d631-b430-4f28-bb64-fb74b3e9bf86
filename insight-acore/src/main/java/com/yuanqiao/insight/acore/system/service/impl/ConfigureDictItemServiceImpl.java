package com.yuanqiao.insight.acore.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.acore.system.entity.ConfigureDictItem;
import com.yuanqiao.insight.acore.system.mapper.ConfigureDictItemMapper;
import com.yuanqiao.insight.acore.system.service.IConfigureDictItemService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-28
 */
@Service
public class ConfigureDictItemServiceImpl extends ServiceImpl<ConfigureDictItemMapper, ConfigureDictItem> implements IConfigureDictItemService {

}
