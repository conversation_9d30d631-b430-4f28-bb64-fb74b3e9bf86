package com.yuanqiao.insight.acore.alarm.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.json.JsonData;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.googlecode.aviator.AviatorEvaluator;
import com.yuanqiao.insight.acore.alarm.judge.Match;
import com.yuanqiao.insight.acore.alarm.judge.MatchResult;
import com.yuanqiao.insight.acore.alarm.model.AlarmRuleModel;
import com.yuanqiao.insight.acore.alarm.model.AlarmTemplateModel;
import com.yuanqiao.insight.acore.scheduler.constant.CommonConstant;
import com.yuanqiao.insight.common.constant.SyslogConstant;
import com.yuanqiao.insight.common.util.common.RedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.mq.RedisMq;
import org.jeecg.common.mq.aspect.annotation.RedisMessageExtend;
import org.jeecg.common.mq.stream.Streams;
import org.jeecg.common.mq.utils.AnalyzeContent;
import org.springframework.data.redis.connection.stream.MapRecord;
import org.springframework.data.redis.stream.StreamListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.Reader;
import java.io.StringReader;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 匹配规则模型
 * 1.无Alarmcount时,未匹配到告警规则,则无操作
 * 2.匹配到规则Alarmcount++,延长TTL
 * 3.有Alarmcount时未匹配到规则,且未产生告警,NOAlarmcount++,延长TTL(如何判断是否产生告警?查询同一个设备同一个告警策略是否有当前告警)
 * 4.触发告警或解除告警由Alarmcount,NOAlarmcount谁先满足次数决定
 *
 * <AUTHOR>
 * @date 2023/4/17
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MatchRuleModelListener implements StreamListener<String, MapRecord<String, String, String>> {

    private final RedisUtils redisUtils;
    private final RedisMq redisMq;
    private final ElasticsearchClient client;

    @Override
    @Async("stream-core-pool")
    @RedisMessageExtend(stream = Streams.MATCH_RULE_MODEL)
    public void onMessage(MapRecord<String, String, String> message) {
        long begin = System.nanoTime();
        final Map<String, String> value = message.getValue();
        JSONObject data = AnalyzeContent.getContent(value);
        Long redisTime = data.getLong("redisTime");
        if (redisTime < 180L) {
            redisTime = 180L;
            data.put("redisTime", redisTime);
        }
        String deviceCode = data.getString("deviceCode");
        String metaCode = data.getString("metaCode");
        AlarmTemplateModel alarmTemplateModel = data.getObject("alarmTemplateModel", AlarmTemplateModel.class);

        String redisDataKey = "stca:" + deviceCode + "_" + metaCode;
        String metaData = String.valueOf(redisUtils.get(redisDataKey));
        if (metaCode.equals("syslog")) {
            metaData = "{\"unit\":\"\",\"code\":\"syslog\",\"name\":\"syslog\",\"type\":\"text\",\"value\":\"\"}";
        }
        if (StringUtils.isEmpty(metaData) || StringUtils.equals("null", metaData)) {
            return;
        }
        JSONObject metaDataJson = JSON.parseObject(metaData);
        metaDataJson.put("code", metaCode);
        final MatchResult matchResult = matchTemplate(alarmTemplateModel, metaDataJson, deviceCode, redisTime);
        //发布结果消息
        if (matchResult.isContinue()) {
            data.put("matchResultTemplate", matchResult);
            redisMq.publish(Streams.MATCH_RULE_RESULT, data);
        }
        String interval = interval((System.nanoTime() - begin) / 1000000.0);
        log.info("[{}] 匹配规则模型总耗时:{}", deviceCode, interval);
    }

    /**
     * 匹配告警模板
     *
     * @param alarmTemp 告警规则
     * @return
     */
    private MatchResult matchTemplate(AlarmTemplateModel alarmTemp, JSONObject metaDataJson, String deviceCode, Long redisTime) {
        //告警规则
        long begin = System.nanoTime();
        List<AlarmRuleModel> alarmRules = alarmTemp.getAlarmRules();
        String resultKey = String.format(CommonConstant.RESULT_KEY, alarmTemp.getId(), deviceCode);
        MatchResult matchResult = new MatchResult();
        String meteCode = metaDataJson.getString("code");
        for (AlarmRuleModel rule : alarmRules) {
            if (meteCode.equals(rule.getParentMetadataCode()) || meteCode.equals(rule.getSubjectIndex())) {
                Match match = matchRule(rule, metaDataJson, deviceCode, redisTime);
                //存储已匹配的告警
                redisUtils.hset(resultKey, match.getRuleId(), JSON.toJSONString(match));
                redisUtils.expire(resultKey, redisTime);//指定Key 的失效时间
            }
        }
        final Map<String, Object> map = redisUtils.hmGet(resultKey);
        if (CollUtil.isNotEmpty(map) && map.size() == alarmRules.size()) {
            final List<Match> matchedList = map.values().stream()
                    .map(Objects::toString)
                    .map(JSON::parseObject)
                    .map(item -> JSON.toJavaObject(item, Match.class))
                    .filter(Match::isMatch)
                    .collect(Collectors.toList());
            Match max = matchedList.stream()
                    .max(Comparator.comparingInt(Match::getLevel))
                    .orElse(new Match());
            matchResult.setLevel(max.getLevel());
            matchResult.setRepetition(max.isRepetition());
            matchResult.setTrigger(max.isMatch());
            matchResult.setHopeTimes(max.getHopeTimes());
            matchResult.setTriggerRuleId(max.getRuleId());
            matchResult.setChangeTimes(max.getChangeTimes());
            if (Integer.parseInt(alarmTemp.getTriggerType()) == 0) {
                //找到符合条件且等级最高的规则
                matchResult.setRuleMap(max.getRuleMap());
                matchResult.setRuleId(max.getRuleId());
                matchResult.setAlarmValue(max.getAlarmValue());
            } else if (Integer.parseInt(alarmTemp.getTriggerType()) == 1 && matchedList.size() == alarmRules.size()) {
                //满足所有条件触发
                Map<String, JSONObject> ruleMap = new HashMap<>();
                for (Match match : matchedList) {
                    ruleMap.putAll(match.getRuleMap());
                }
                matchResult.setRuleMap(ruleMap);
                matchResult.setRuleId(String.join(",", map.keySet()));
                JSONArray jsonArray = new JSONArray();
                matchedList.stream().map(MatchResult::getAlarmValue).forEach(jsonArray::addAll);
                matchResult.setAlarmValue(jsonArray);
            }
            matchResult.setContinue(true);
            //删除数据包缓存
            redisUtils.del(resultKey);
        }
        String interval = interval((System.nanoTime() - begin) / 1000000.0);
        log.info("[{}] matchTemplate()耗时:{}", deviceCode, interval);
        return matchResult;
    }


    /**
     * 匹配告警规则、告警次数
     *
     * @param alarmRule 告警规则
     * @return
     */
    private Match matchRule(AlarmRuleModel alarmRule, JSONObject metaDataJson, String deviceCode, Long redisTime) {
        long begin = System.nanoTime();
        Match match;
        //匹配指标
        List<Match> resultList = matchSubject(alarmRule, metaDataJson, deviceCode, redisTime);
        if (resultList.stream().allMatch(item -> Boolean.FALSE.equals(item.isMatch()))) {
            //没匹配成功, 直接返回
            log.info("[{}]==>{}规则匹配失败！！！", deviceCode, alarmRule.getSubjectIndex());
            match = new Match();
            match.setRuleId(alarmRule.getId());
        } else {
            //匹配成功,返回告警等级最大的
            log.info("[{}]==>{}规则匹配成功！！！", deviceCode, alarmRule.getSubjectIndex());
            match = resultList.stream().filter(Match::isMatch).max(Comparator.comparing(MatchResult::getLevel)).orElse(new Match());
        }
        String interval = interval((System.nanoTime() - begin) / 1000000.0);
        log.info("[{}] matchRule()耗时:{}", deviceCode, interval);
        return match;
    }

    /**
     * 匹配告警指标
     *
     * @param alarmRule 告警规则
     * @return
     */
    private List<Match> matchSubject(AlarmRuleModel alarmRule, JSONObject metaDataJson, String deviceCode, Long redisTime) {
        long begin = System.nanoTime();
        //获取相关参数
        JSONArray content = alarmRule.getContentJson();
        //获取指标项
        String subjectIndex = alarmRule.getSubjectIndex();
        //获取判断条件
        String condition = alarmRule.getConditions();
        String ruleId = alarmRule.getId();
        String typeByCode = metaDataJson.getString("type");

        List<Match> resultList = new ArrayList<>();
        //对告警级别进行排序由高到低
        content.sort((o1, o2) -> {
            JSONObject a = (JSONObject) o1;
            JSONObject b = (JSONObject) o2;
            return b.getInteger("level").compareTo(a.getInteger("level"));
        });
        for (int i = 0; i < content.size(); i++) {
            JSONObject rule = content.getJSONObject(i);
            Integer level = rule.getInteger("level");
            Integer hopeTimes = rule.getInteger("times");
            boolean ok;
            JSONArray matchValue = new JSONArray();
            Match match = new Match();
            //数据类型
            try {
                String lev = String.valueOf(level);
                String triggerKey = String.format(CommonConstant.TRIGGER_KEY, alarmRule.getAlarmTemplateId(), deviceCode, ruleId);
                String recoveryKey = String.format(CommonConstant.RECOVERY_KEY, alarmRule.getAlarmTemplateId(), deviceCode, ruleId);
                String variationKey = String.format(CommonConstant.VARIATION_KEY, alarmRule.getAlarmTemplateId(), deviceCode, ruleId, lev);
                if ("object".equals(typeByCode) || "array".equals(typeByCode)) {
                    rule.put("deviceCode", deviceCode);
                    final JSONObject res = doExecuteForObjectArray(subjectIndex
                            , condition
                            , rule
                            , metaDataJson
                            , alarmRule.getSpecifyKey()
                            , alarmRule.getSpecifyValue()
                            , variationKey
                            , redisTime);
                    ok = res.getBoolean("match");
                    matchValue = res.getJSONArray("matchValue");
                } else if (condition.equals("es")) {
                    final BoolQuery.Builder boolBuilder = getBuilder(deviceCode, String.valueOf(rule.get("value")));
                    BoolQuery build = boolBuilder.build();
                    log.info("es query:{}", build.toString());
                    SearchResponse<Object> search = client.search(s -> s
                                    .index(SyslogConstant.SYSLOG_INDEX)
                                    .query(q -> q.bool(build))
                                    .sort(s1 -> s1.field(f -> f.field("@timestamp").order(SortOrder.Desc)))
                                    .from(0)
                                    .size(1)
                            , Object.class);
                    ok = !search.hits().hits().isEmpty();
                    matchValue.add(metaDataJson);
                } else {
                    //metaDataJson.put("subjectIndex", subjectIndex);
                    ok = conditionExecute(metaDataJson.get("value")
                            , rule.get("value")
                            , condition
                            , variationKey
                            , redisTime
                            , String.format("匹配指标%s->%s", rule.getString("deviceCode"), subjectIndex));
                    matchValue.add(metaDataJson);
                    //}
                }
                Map<String, JSONObject> ruleMap = new HashMap<>();
                rule.put("condition", condition);
                ruleMap.put(subjectIndex, rule);
                match.setRuleId(ruleId);
                match.setRuleMap(ruleMap);
                match.setLevel(level);
                match.setHopeTimes(hopeTimes);
                match.setAlarmValue(matchValue);
                boolean result = matchTimes(hopeTimes, triggerKey, lev, redisTime, ok);
                boolean isRecovery = false;
                if (!ok && redisUtils.hHasKey(triggerKey, lev)) {
                    //不满足该级别的阈值,且存在该级别的活跃告警
                    isRecovery = matchTimes(hopeTimes, recoveryKey, lev, redisTime, true);
                }
                if (isRecovery) {
                    //该级别的告警已经连续N次不满足,开始告警降级
                    redisUtils.hdel(triggerKey, lev);
                    redisUtils.hdel(recoveryKey, lev);
                    result = false;
                }
                if (result && ok && redisUtils.hHasKey(recoveryKey, lev)) {
                    //该级别满足告警条件,删除NoAlarmCount
                    redisUtils.hdel(recoveryKey, lev);
                } else if (!result && !ok) {
                    //出现不连续告警,从0开始重新计数
                    redisUtils.hset(triggerKey, lev, 0);
                }
                match.setRepetition(ok);
                match.setMatch(result);
                match.setChangeTimes(rule.getInteger("changeTimes"));
                resultList.add(match);
            } catch (Exception e) {
                log.error("跳过指标 {}", subjectIndex);
                log.error("metaDataJson {}", JSON.toJSONString(metaDataJson.get("value")));
                log.error(e.getMessage(), e);
            }
        }
        String interval = interval((System.nanoTime() - begin) / 1000000.0);
        log.info("[{}] matchSubject()耗时:{}", deviceCode, interval);
        return resultList;
    }

    private static BoolQuery.Builder getBuilder(String deviceCode, String condition) {
        final String[] conditions = condition.split(":");
        BoolQuery.Builder boolBuilder = new BoolQuery.Builder();
        switch (conditions[1]) {
            case "term":
                boolBuilder.must(q -> q.term(t -> t.field(conditions[0])
                        .value(v -> v.stringValue(conditions[2]))));
                break;
            case "match":
                boolBuilder.must(q -> q.match(t -> t.field(conditions[0])
                        .query(v -> v.stringValue(conditions[2]))));
                break;
            case "range":
                final String[] range = conditions[2].split("_");
                String must = String.format("{\"must\":[{\"range\":{\"%s\":{\"%s\":\"%s\"}}}]}", conditions[0], range[0], range[1]);
                Reader queryJson = new StringReader(must);
                boolBuilder.withJson(queryJson);
                break;
        }
        boolBuilder.must(q -> q.match(t -> t.field("device_code")
                        .query(v -> v.stringValue(deviceCode))))
                .must(q -> q.range(t -> t.field("@timestamp")
                        .gte(JsonData.fromJson(DateUtil.formatDateTime(DateUtil.offsetMinute(new Date(), -5))))));
        return boolBuilder;
    }

    /**
     * object array 子级数据抽取、匹配
     *
     * @param subjectIndex 指标
     * @param rule         条件
     * @param condition
     * @param specifyKey
     * @param specifyValue
     * @return
     */
    private JSONObject doExecuteForObjectArray(String subjectIndex
            , String condition
            , JSONObject rule
            , JSONObject metaDataJson
            , String specifyKey
            , String specifyValue
            , String variationKey
            , Long redisTime) {
        JSONObject res = new JSONObject();
        long begin = System.nanoTime();
        boolean f;
        //获取redis中物模型实时的指标
        JSONArray value = new JSONArray();
        final Object o = metaDataJson.get("value");
        if (o instanceof JSONArray) {
            //metadata为array格式
            value = metaDataJson.getJSONArray("value");
        } else {
            //metadata为object格式
            value.add(metaDataJson.getJSONObject("value"));
        }
        //临时调整为返回多行,实际上并没有返回多行,array类型指标值的多个项触发告警待以后修改
        List<JSONObject> matchArr = new ArrayList<>();

        for (int i = 0; i < value.size(); i++) { //取出设备推送过来的信息
            String vk = variationKey;
            JSONObject subjectObject = new JSONObject();
            JSONObject jsonObj = value.getJSONObject(i);
            // 根据规则过滤出指定子指标
            if (StringUtils.isNotEmpty(specifyKey)) {
                JSONObject display = jsonObj.getJSONObject(specifyKey);
                if (Objects.isNull(display)) {
                    log.error("[{}]:display is NULL", rule.getString("deviceCode"));
                    log.error("jsonObj:{}", jsonObj.toJSONString());
                }
                if (StringUtils.isNotEmpty(specifyValue)) {
                    if (StringUtils.isNotEmpty(jsonObj.getString(specifyKey)) && !display.getString("value").equals(specifyValue)) {
                        continue;
                    }
                }
                subjectObject.put("displayCode", specifyKey);
                subjectObject.put("displayName", display.getString("name"));
                subjectObject.put("displayValue", display.getString("value"));
                vk = vk + ":" + display.getString("value");
            }
            if (Objects.isNull(jsonObj)) {
                log.error("[{}]:jsonObj is NULL", rule.getString("deviceCode"));
            } else if (Objects.isNull(jsonObj.getJSONObject(subjectIndex))) {
                log.error("[{}]:jsonObj.subjectIndex is NULL", rule.getString("deviceCode"));
            }
            subjectObject.putAll(jsonObj.getJSONObject(subjectIndex));
            subjectObject.putAll(jsonObj);
            //subjectObject.put("subjectIndex", subjectIndex);
            //判断当前指标是否触发告警
            Object dataValue = subjectObject.get("value");
            Object targetValue = rule.get("value");
            f = conditionExecute(dataValue, targetValue, condition, vk, redisTime, String.format("匹配指标%s->%s", rule.getString("deviceCode"), subjectIndex));

            JSONObject resMap = new JSONObject();
            resMap.put("match", f);
            resMap.put("value", subjectObject);
            matchArr.add(resMap);
        }
        boolean match;
        if (condition.equals("nonEq")
                || condition.equals("allEq")
                || condition.equals("allContain")
                || condition.equals("nonContain")) {
            match = matchArr.stream().allMatch(item -> Boolean.TRUE.equals(item.getBoolean("match")));
        } else {
            matchArr = matchArr.stream().filter(item -> Boolean.TRUE.equals(item.getBoolean("match"))).collect(Collectors.toList());
            match = !matchArr.isEmpty();
        }
        JSONArray matchValue = new JSONArray();
        if (match) {
            matchArr.stream().map(item -> item.getJSONObject("value")).forEach(matchValue::add);
        }
        res.put("match", match);
        res.put("matchValue", matchValue);

        String interval = interval((System.nanoTime() - begin) / 1000000.0);
        log.info("[{}] doExecuteForObjectArray()耗时:{}", "", interval);
        return res;
    }

    /**
     * 指标条件匹配
     *
     * @param dataValue
     * @param targetValue
     * @return
     */
    private boolean conditionExecute(Object dataValue, Object targetValue, String condition, String variationKey, Long redisTime, String desc) {
        long begin = System.nanoTime();
        boolean match = false;
        try {
            if (!(StringUtils.isNotEmpty(condition) && Objects.nonNull(dataValue) && Objects.nonNull(targetValue))) {
                return false;
            }
            switch (condition) {
                case "nonEq":
                    condition = "!=";
                    break;
                case "allEq":
                    condition = "==";
                    break;
                case "variation":
                    return variation(dataValue, targetValue, variationKey, redisTime);
            }
            dataValue = String.valueOf(dataValue).trim();
            targetValue = String.valueOf(targetValue).trim();
            if (isNumber(String.valueOf(dataValue))) {
                dataValue = new BigDecimal(String.valueOf(dataValue));
            }
            if (isNumber(String.valueOf(targetValue))) {
                targetValue = new BigDecimal(String.valueOf(targetValue));
            }
            match = execute(dataValue, targetValue, condition);
        } catch (Exception e) {
            log.error("{}, dataValue:{}, targetValue:{}, condition:{}", desc, dataValue, targetValue, condition);
            log.error("指标条件匹配异常！", e);
        }
        String interval = interval((System.nanoTime() - begin) / 1000000.0);
        log.info("[{}] conditionExecute()耗时:{}", "", interval);
        return match;
    }

    private boolean variation(Object currValue, Object targetValue, String variationKey, Long redisTime) {
        boolean match = execute(currValue, targetValue, "==");
        Object oldValue = null;
        if (redisUtils.hasKey(variationKey)) {
            oldValue = redisUtils.get(variationKey);
        } else if (!match) {
            redisUtils.set(variationKey, currValue);
        }
        redisUtils.expire(variationKey, redisTime);
        return oldValue != null && !oldValue.equals(currValue) && match;
    }

    /**
     * 执行表达式
     *
     * @param dataValue
     * @param targetValue 表达式
     * @return
     */
    private boolean execute(Object dataValue, Object targetValue, String condition) {
        Map<String, Object> env = new HashMap<>();
        env.put("x", dataValue);
        env.put("y", targetValue);
        // 编译表达式
        String expression = "";
        switch (condition) {
            case "allContain":
            case "contain":
                expression = "string.contains(x,y)";
                break;
            case "notContain":
            case "nonContain":
                expression = "!string.contains(x,y)";
                break;
            case "expression":
                expression = String.valueOf(targetValue);
                env.remove("y");
                break;
            default:
                expression = String.format("x %s y", condition);
        }
        // 执行表达式, 获得结果
        return (boolean) AviatorEvaluator.execute(expression, env);
    }

    /**
     * 匹配告警次数
     *
     * @param hopeTimes 次数
     * @return
     */
    private boolean matchTimes(int hopeTimes, String key, String level, Long redisTime, boolean isSave) {
        int hasTimes = 0;
        if (redisUtils.hHasKey(key, level)) {
            hasTimes = Integer.parseInt(String.valueOf(redisUtils.hget(key, level)));
        } else {
            redisUtils.hset(key, level, 0);
        }
        if (isSave) {
            if (hopeTimes != hasTimes) {
                redisUtils.hincr(key, level, 1);
            }
        }
        redisUtils.expire(key, redisTime);//指定Key 的失效时间
        //判断实际产生机构数量是否等于警告规则配置数量
        return hasTimes == hopeTimes;
    }

    /**
     * 通过正则表达式判断字符串是否为数字
     *
     * @param str
     * @return
     */
    public static boolean isNumber(String str) {
        final Pattern pattern = Pattern.compile("(\\-|\\+)?\\d+(\\.\\d+)?");
        return pattern.matcher(str).matches();
    }

    public static String interval(double interval) {
        if (interval < 1000D) {
            return String.format("%.2f毫秒", interval);
        } else if (interval < 1000D * 60) {
            return String.format("%.2f秒", interval / 1000D);
        } else {
            return String.format("%.2f分钟", interval / 1000D / 60D);
        }
    }
}
