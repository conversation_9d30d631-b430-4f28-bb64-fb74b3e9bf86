package com.yuanqiao.insight.acore.scheduler;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.common.constant.CommonConstant;
import com.yuanqiao.insight.common.util.common.LocalCacheUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.scheduling.config.TriggerTask;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component("myScheduler")
@EnableScheduling
public class MyScheduler implements Scheduler, SchedulingConfigurer {
    private ScheduledTaskRegistrar taskRegistrar;
    private Map<String, ScheduledFuture<?>> taskFutures = new ConcurrentHashMap<>();

    LocalCacheUtils cacheUtils = LocalCacheUtils.getInstance();

    //初始化自定义ScheduledExecutorService（支持延时周期任务）;
    private ScheduledExecutorService scheduler = null;

    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        this.taskRegistrar = taskRegistrar;
    }

    /**
     * 添加任务（cron表达式复杂频率的任务）
     */
    @Override
    public void add(String taskId, String cron, Runnable runnable) {
        TriggerTask triggerTask = new TriggerTask(runnable, new CronTrigger(cron));
        if (taskFutures.containsKey(taskId)) {
            return;
            //throw new SchedulingException("the taskId[" + taskId + "] was added.");
        }
        TaskScheduler scheduler = taskRegistrar.getScheduler();
        ScheduledFuture<?> future = scheduler.schedule(triggerTask.getRunnable(), triggerTask.getTrigger());
        taskFutures.put(taskId, future);
    }

    /**
     * 添加任务（首次延迟后续简单频率的任务）
     */
    @Override
    public void add(String taskId, JSONObject taskParam, Runnable runnable) {
        if (taskFutures.containsKey(taskId)) {
            return;
            //throw new SchedulingException("the taskId[" + taskId + "] was added.");
        }

        int randomInt = RandomUtil.randomInt(1, taskParam.getInteger("period"));
        System.out.println("     任务执行频率：" + taskParam.getInteger("period") + " --- 首次执行延迟时间 ：" + randomInt + " S");

        // 创建并执行一个周期性操作，该操作在给定的初始延迟后首先启用，然后在一次执行终止和下一次执行开始之间的给定延迟内启用。
        // 如果任务的任何执行遇到异常，则会抑制后续执行。
        if (scheduler == null){
            // CPU密集型：核心线程数 = CPU核数 + 1
            // IO密集型：核心线程数 = CPU核数 * 2
            Integer corePoolSize = Integer.valueOf(Optional.ofNullable(cacheUtils.getValueByKey(CommonConstant.DATA_DICT_KEY + "scheduleThreadPool_corePoolSize")).orElse("32") + "");
            scheduler = new CustomScheduleExecutorService(corePoolSize);
        }
        ScheduledFuture<?> future = scheduler.scheduleWithFixedDelay(runnable, randomInt, taskParam.getLong("period"), (TimeUnit) taskParam.get("timeUnit"));
        taskFutures.put(taskId, future);
    }

    /**
     * 添加任务（在给定延迟后启用的一次性的任务）
     */
    @Override
    public void addOneShot(String taskId, JSONObject taskParam, Runnable runnable) {
        // 创建并执行一个周期性操作，该操作在给定的初始延迟后首先启用，然后在一次执行终止和下一次执行开始之间的给定延迟内启用。
        // 如果任务的任何执行遇到异常，则会抑制后续执行。
        if (scheduler == null){
            // CPU密集型：核心线程数 = CPU核数 + 1
            // IO密集型：核心线程数 = CPU核数 * 2
            Integer corePoolSize = Integer.valueOf(cacheUtils.getValueByKey(CommonConstant.DATA_DICT_KEY + "scheduleThreadPool_corePoolSize") + "");
            scheduler = new CustomScheduleExecutorService(corePoolSize);
        }
        scheduler.schedule(runnable, taskParam.getLong("period"), TimeUnit.SECONDS);
    }

    /**
     * 取消任务
     */
    @Override
    public void remove(String taskId) {
        ScheduledFuture<?> future = taskFutures.get(taskId);
        if (future != null) {
            future.cancel(true);
        }
        taskFutures.remove(taskId);
    }

    /**
     * 重置任务
     */
    public void reset(String taskId, String cron, Runnable runnable) {
        remove(taskId);
        add(taskId, cron, runnable);
    }

    /**
     * 任务编号
     */
    public Set<String> taskIds() {
        return taskFutures.keySet();
    }

    /**
     * 是否存在任务
     */
    public boolean hasTask(String taskId) {
        return this.taskFutures.containsKey(taskId);
    }

    /**
     * 任务调度是否已经初始化完成
     */
    public boolean inited() {
        return this.taskRegistrar != null && this.taskRegistrar.getScheduler() != null;
    }
}
