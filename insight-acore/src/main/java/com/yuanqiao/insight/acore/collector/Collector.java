package com.yuanqiao.insight.acore.collector;

import com.yuanqiao.insight.acore.device.Device;
import com.yuanqiao.insight.acore.scheduler.SchedulerManagerInter;
import org.springframework.context.ApplicationContext;

/**
 * 收集器
 * 负责收集设备信息
 */
public interface Collector {
    void init(Device device, SchedulerManagerInter schedulerManager) ;
    default void init(Device device, SchedulerManagerInter schedulerManager, ApplicationContext applicationContext){};
    default void destroy() {
    }

    //todo 产生不好通过表达式判断的告警

    //收集信息
    default void execute() {
    }

}
