package com.yuanqiao.insight.acore.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import com.yuanqiao.insight.acore.system.entity.SysDictItem;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * @<PERSON>
 * @since 2018-12-28
 */
@Component
public interface SysDictItemMapper extends BaseMapper<SysDictItem> {
    @Select("SELECT * FROM sys_dict_item WHERE DICT_ID = #{mainId} order by sort_order asc, item_value asc")
    public List<SysDictItem> selectItemsByMainId(String mainId);

    @Select("SELECT item_text FROM sys_dict_item WHERE dict_id = #{dictId} and item_value=#{itemValue}")
    String selectTextByDicIdAndValue(@Param("dictId") String dictId, @Param("itemValue") String itemValue);
}
