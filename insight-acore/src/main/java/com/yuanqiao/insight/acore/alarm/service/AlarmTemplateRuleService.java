package com.yuanqiao.insight.acore.alarm.service;

import com.yuanqiao.insight.acore.alarm.mapper.AlarmMapper;
import com.yuanqiao.insight.acore.alarm.model.AlarmTemplateModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AlarmTemplateRuleService {
    @Autowired
    private AlarmMapper alarmMapper;

    public List<AlarmTemplateModel> findTemRule(String deviceCode) {
        return alarmMapper.findTemplateListByDeviceCode(deviceCode);
    }

}
