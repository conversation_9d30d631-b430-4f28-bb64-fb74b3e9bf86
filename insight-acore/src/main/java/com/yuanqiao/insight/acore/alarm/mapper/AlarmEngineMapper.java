package com.yuanqiao.insight.acore.alarm.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public interface AlarmEngineMapper {


    List<Map<String, String>> getConnectionByProtocol(@Param("deviceId") String deviceId, @Param("protocol") String protocol);

    Map<String, String> getSceneById(@Param("sceneId") String sceneId);

    String getScriptContextById(@Param("scriptId") String scriptId);

    Map<String, String> getDeviceInfoByDevId(@Param("deviceId") String deviceId);

    IPage<Map<String, String>> getChangeAlarmLevel(Page<Map<String, String>> page, @Param("level") String level);

    void updateDeviceScoreByDeviceId(@Param("score") Float score,@Param("healthyDegree") String healthyDegree,@Param("deviceId")  String deviceId);
}
