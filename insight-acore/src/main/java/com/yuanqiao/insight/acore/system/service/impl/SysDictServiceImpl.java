package com.yuanqiao.insight.acore.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanqiao.insight.acore.system.entity.SysDict;
import com.yuanqiao.insight.acore.system.entity.SysDictItem;
import com.yuanqiao.insight.acore.system.mapper.SysDictItemMapper;
import com.yuanqiao.insight.acore.system.mapper.SysDictMapper;
import com.yuanqiao.insight.acore.system.vo.DuplicateCheckVo;
import com.yuanqiao.insight.acore.system.vo.SysArea;
import com.yuanqiao.insight.acore.system.vo.SysAreaTreeModel;
import com.yuanqiao.insight.acore.system.vo.TreeSelectModel;
import com.yuanqiao.insight.common.util.common.LocalCacheUtils;
import com.yuanqiao.insight.common.util.dbType.DataSourceTypeUtils;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.common.system.vo.DictModelMany;
import org.jeecg.common.system.vo.DictQuery;
import org.jeecg.common.util.SqlInjectionUtil;
import org.jeecg.common.util.oConvertUtils;
import com.yuanqiao.insight.acore.system.service.ISysDictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 字典表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-28
 */
@Service
@Slf4j
public class SysDictServiceImpl extends ServiceImpl<SysDictMapper, SysDict> implements ISysDictService {

    @Autowired
    private SysDictMapper sysDictMapper;
    @Autowired
    private SysDictItemMapper sysDictItemMapper;
    @Autowired
    private DataSourceTypeUtils dataSourceTypeUtils;
    private static final LocalCacheUtils cacheUtils = LocalCacheUtils.getInstance();


    /**
     * 通过查询指定code 获取字典
     * 返回值为DictModel，常用
     *
     * @param code
     * @return
     */
    @Override
    @Cacheable(value = CacheConstant.SYS_DICT_CACHE, key = "#code")
    public List<DictModel> queryDictItemsByCode(String code) {
        log.debug("无缓存dictCache的时候调用这里！");
        return sysDictMapper.queryDictItemsByCode(code);
    }

    /**
     * 通过查询指定code 获取字典
     * 返回值为SysDictItem
     *
     * @param code
     * @return
     */
    @Override
    @Cacheable(value = CacheConstant.SYSDICT_ITEM__CACHE, key = "#code", unless = "#result == null ")
    public List<SysDictItem> findSysDictItemByCode(String code) {
        log.debug("无缓存SysDictItemCache的时候调用这里！");
        return sysDictMapper.findSysDictItemByCode(code);
    }

    @Override
    public Long duplicateCheckCountSql(DuplicateCheckVo duplicateCheckVo) {
        return sysDictMapper.duplicateCheckCountSql(duplicateCheckVo);
    }

    @Override
    public Long duplicateCheckCountSqlNoDataId(DuplicateCheckVo duplicateCheckVo) {
        return sysDictMapper.duplicateCheckCountSqlNoDataId(duplicateCheckVo);
    }

    @Override
    public Long duplicateCheckForUserCountSql(DuplicateCheckVo duplicateCheckVo) {
        return sysDictMapper.duplicateCheckForUserCountSql(duplicateCheckVo);
    }

    @Override
    public Long duplicateCheckForUserCountSqlNoDataId(DuplicateCheckVo duplicateCheckVo) {
        return sysDictMapper.duplicateCheckForUserCountSqlNoDataId(duplicateCheckVo);
    }

    @Override
    public Map<String, List<DictModel>> queryAllDictItems() {
        Map<String, List<DictModel>> res = new HashMap<String, List<DictModel>>();
        List<SysDict> ls = sysDictMapper.selectList(null);
        LambdaQueryWrapper<SysDictItem> queryWrapper = new LambdaQueryWrapper<SysDictItem>();
        queryWrapper.eq(SysDictItem::getStatus, 1);
        queryWrapper.orderByAsc(SysDictItem::getSortOrder);
        List<SysDictItem> sysDictItemList = sysDictItemMapper.selectList(queryWrapper);

        for (SysDict d : ls) {
            List<DictModel> dictModelList = sysDictItemList.stream().filter(s -> d.getId().equals(s.getDictId())).map(item -> {
                DictModel dictModel = new DictModel();
                dictModel.setText(item.getItemText());
                dictModel.setValue(item.getItemValue());
                dictModel.setDescription(item.getDescription());
                return dictModel;
            }).collect(Collectors.toList());
            res.put(d.getDictCode(), dictModelList);
        }
        log.debug("-------登录加载系统字典-----" + res.toString());
        return res;
    }

    /**
     * 通过查询指定code 获取字典值text
     *
     * @param code
     * @param key
     * @return
     */

    @Override
    @Cacheable(value = CacheConstant.SYS_DICT_CACHE, key = "#code+':'+#key")
    public String queryDictTextByKey(String code, String key) {
        log.debug("无缓存dictText的时候调用这里！");
        return sysDictMapper.queryDictTextByKey(code, key);
    }

    @Override
    @Cacheable(value = CacheConstant.SYS_DICT_CACHE, key = "#text+':'+#code")
    public String queryDictValueByText(String code, String text) {
        return sysDictMapper.queryDictValueByText(code, text);
    }

    /**
     * 通过查询指定table的 text code 获取字典
     * dictTableCache采用redis缓存有效期10分钟
     *
     * @param table
     * @param text
     * @param code
     * @return
     */
    @Override
    @Cacheable(value = CacheConstant.SYS_DICT_TABLE_CACHE, unless = "#result == null")
    public List<DictModel> queryTableDictItemsByCode(String table, String text, String code) {
        log.debug("无缓存dictTableList的时候调用这里！");
        return sysDictMapper.queryTableDictItemsByCode(table, text, code);
    }

    @Override
    public List<DictModel> queryTableDictItemsByCodeAndFilter(String table, String text, String code, String filterSql) {
        log.debug("无缓存dictTableList的时候调用这里！");
        return sysDictMapper.queryTableDictItemsByCodeAndFilter(table, text, code, filterSql);
    }

    /**
     * 通过查询指定table的 text code 获取字典值text
     * dictTableCache采用redis缓存有效期10分钟
     *
     * @param table
     * @param text
     * @param code
     * @param key
     * @return
     */
    @Override
    @Cacheable(value = CacheConstant.SYS_DICT_TABLE_CACHE, unless = "#result == null")
    public String queryTableDictTextByKey(String table, String text, String code, String key) {
        log.debug("无缓存dictTable的时候调用这里！");
        return sysDictMapper.queryTableDictTextByKey(table, text, code, key);
    }

    /**
     * 通过查询指定table的 text code 获取字典，包含text和value
     * dictTableCache采用redis缓存有效期10分钟
     *
     * @param table
     * @param text
     * @param code
     * @param keys  (逗号分隔)
     * @return
     */
    @Override
    @Cacheable(value = CacheConstant.SYS_DICT_TABLE_BY_KEYS_CACHE, unless = "#result == null")
    public List<String> queryTableDictByKeys(String table, String text, String code, String keys) {
        if (oConvertUtils.isEmpty(keys)) {
            return null;
        }
        String[] keyArray = keys.split(",");
        List<DictModel> dicts = sysDictMapper.queryTableDictByKeys(table, text, code, keyArray);
        List<String> texts = new ArrayList<>(dicts.size());
        // 查询出来的顺序可能是乱的，需要排个序
        for (String key : keyArray) {
            for (DictModel dict : dicts) {
                if (key.equals(dict.getValue())) {
                    texts.add(dict.getText());
                    break;
                }
            }
        }
        return texts;
    }

    /**
     * 根据字典类型id删除关联表中其对应的数据
     */
    @Override
    public boolean deleteByDictId(SysDict sysDict) {
        sysDict.setDelFlag(CommonConstant.DEL_FLAG_1);
        return this.updateById(sysDict);
    }

    @Override
    @Transactional
    public Integer saveMain(SysDict sysDict, List<SysDictItem> sysDictItemList) {
        int insert = 0;
        try {
            insert = sysDictMapper.insert(sysDict);
            if (sysDictItemList != null) {
                for (SysDictItem entity : sysDictItemList) {
                    entity.setDictId(sysDict.getId());
                    entity.setStatus(1);
                    sysDictItemMapper.insert(entity);
                }
            }
        } catch (Exception e) {
            return insert;
        }
        return insert;
    }

    @Override
    public List<DictModel> queryAllDepartBackDictModel() {
        return baseMapper.queryAllDepartBackDictModel();
    }

    @Override
    public List<DictModel> queryAllUserBackDictModel() {
        return baseMapper.queryAllUserBackDictModel();
    }

    @Override
    public List<DictModel> queryTableDictItems(String table, String text, String code, String keyword) {
        return baseMapper.queryTableDictItems(table, text, code, "%" + keyword + "%");
    }

    @Override
    public List<TreeSelectModel> queryTreeList(Map<String, Object> query, String table, String text, String code, String pidField, String pid, String hasChildField) {
        if (dataSourceTypeUtils.getDBType().equals("highgo")) {
            return baseMapper.queryTreeListByHg(query, table, text, code, pidField, pid, hasChildField);
        } else {
            return baseMapper.queryTreeList(query, table, text, code, pidField, pid, hasChildField);
        }
    }

    @Override
    public void deleteOneDictPhysically(String id) {
        this.baseMapper.deleteOneById(id);
        this.sysDictItemMapper.delete(new LambdaQueryWrapper<SysDictItem>().eq(SysDictItem::getDictId, id));
    }

    @Override
    public void updateDictDelFlag(int delFlag, String id) {
        baseMapper.updateDictDelFlag(delFlag, id);
    }

    @Override
    public List<SysDict> queryDeleteList() {
        return baseMapper.queryDeleteList();
    }

    @Override
    public List<DictModel> queryDictTablePageList(DictQuery query, int pageSize, int pageNo) {
        Page page = new Page(pageNo, pageSize, false);
        Page<DictModel> pageList = baseMapper.queryDictTablePageList(page, query);
        return pageList.getRecords();
    }

    @Override
    @Cacheable(value = CacheConstant.SYS_AREA_CACHE, key = "#pid")
    public List<SysArea> queryAreaItemsByPid(String pid) {
        SysArea sysArea = this.queryAreaById(pid);
        List<SysArea> list;
        if (dataSourceTypeUtils.getDBType().equals("highgo")) {
            list = sysDictMapper.queryAreaItemsByPidByHg(pid);
        } else {
            list = sysDictMapper.queryAreaItemsByPid(pid);
        }
        list.add(sysArea);
        log.info("无缓存行政区划信息的时候调用这里！pid:" + pid);
        return list;
    }

    @Override
    public List<SysArea> querySysAreaAll() {
        return sysDictMapper.querySysAreaAll();
    }

    @Override
    @Cacheable(value = CacheConstant.SYS_AREA_CACHE, key = "#pid")
    public List<SysArea> queryAreaItemsByPidNotThis(String pid) {
        List<SysArea> list = sysDictMapper.queryAreaItemsByPid(pid);
        log.info("无缓存行政区划信息的时候调用这里！pid:" + pid);
        return list;
    }

    @Override
    @Cacheable(value = CacheConstant.SYS_AREA_TREE_CACHE, key = "#pid")
    public List<SysAreaTreeModel> queryAreaTreeByPid(String pid) {
        log.info("无缓存行政区划树信息的时候调用这里！pid:" + pid);
        Integer initNum = 0;
        List<SysAreaTreeModel> tree = new ArrayList<>();
        List<SysAreaTreeModel> allList = sysDictMapper.queryAllAreaList();
        Map<String, SysAreaTreeModel> allMap = allList.stream().collect(Collectors.toMap(SysAreaTreeModel::getId, t -> t));
        String firstChildOrRoot = String.valueOf(Optional.ofNullable(cacheUtils.getValueByKey(com.yuanqiao.insight.common.constant.CommonConstant.DATA_DICT_KEY + "areaTree_firstChildOrRoot")).orElse("firstChild"));
        Integer levelNum = Integer.parseInt(String.valueOf(Optional.ofNullable(cacheUtils.getValueByKey(com.yuanqiao.insight.common.constant.CommonConstant.DATA_DICT_KEY + "areaTree_levelNum")).orElse("0")));
        if (firstChildOrRoot.equals("root")) {
            tree.add(allMap.get(pid));
            list2Tree(tree, allList, allMap.get(pid), initNum, levelNum);
        } else {
            allList.stream().filter(m -> m.getPid().equals(pid)).forEach(m -> {
                tree.add(m);
                list2Tree(tree, allList, m, initNum, levelNum);
            });
        }
        return tree;
    }

    /**
     * 行政区划list转tree
     */
    private List<SysAreaTreeModel> list2Tree(List<SysAreaTreeModel> tree, List<SysAreaTreeModel> allList, SysAreaTreeModel area, Integer initNum, Integer levelNum) {
        if (initNum < levelNum) {
            initNum++;
            List<SysAreaTreeModel> children = allList.stream().filter(m -> m.getPid().equals(area.getId())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(children)) {
                area.setChildren(children);
                Integer finalInitNum = initNum;
                children.forEach(child -> {
                    list2Tree(tree, allList, child, finalInitNum, levelNum);
                });
            } else {
                area.setChildren(new ArrayList<>());
            }
        } else {
            area.setChildren(new ArrayList<>());
        }
        return tree;
    }

    /**
     * 通过id获取省市行政区的详情
     *
     * @param id
     * @return
     */
    @Override
    public SysArea queryAreaById(String id) {
        if (dataSourceTypeUtils.getDBType().equals("highgo")) {
            return this.baseMapper.queryAreaByIdByHg(id);
        } else {
            return this.baseMapper.queryAreaById(id);
        }
    }

    /**
     * 通过id获取省市行政区的详情
     *
     * @param text
     * @return
     */
    @Override
    @Cacheable(value = CacheConstant.SYS_AREA_CACHE, key = "#text")
    public SysArea queryAreaByText(String text) {
        List<SysArea> sysAreaList = this.baseMapper.queryAreaByText(text);
        SysArea sysArea = null;
        if (null != sysAreaList && 0 < sysAreaList.size()) {
            sysArea = sysAreaList.get(0);
        }
        return sysArea;
    }

    /**
     * 通过id获取省市行政区的详情
     *
     * @param text
     * @return
     */
    @Override
    public List<String> likeQueryAreaByText(String text) {
        List<SysArea> sysAreaList = this.baseMapper.likeQueryAreaByText(text);
        List<String> ids = sysAreaList.stream().map(SysArea::getId).collect(Collectors.toList());
        return ids;
    }

    /**
     * 获取区域的like查询的区域ID
     *
     * @param cityProperId 区域ID
     * @return
     */
    @Override
    public String getcityProperIdL(String cityProperId) {
        //获取区县
        SysArea sysArea = this.baseMapper.queryAreaById(cityProperId);
        String cityProperIdL = cityProperId;
        if (null != sysArea) {
            Integer level = sysArea.getLevel();
            if (null != level) {
                if (1 == level) {
                    cityProperIdL = cityProperId.substring(0, 2);
                } else if (2 == level) {
                    cityProperIdL = cityProperId.substring(0, 4);
                }
            }
        }
        return cityProperIdL;
    }

    @Override
    public ArrayList<SysArea> getInfoList(String cityId) {
        return baseMapper.getInfoList(cityId);
    }


    @Override
    public List<DictModel> getDictItems(String dictCode) {
        List<DictModel> ls;
        if (dictCode.contains(",")) {
            //关联表字典（举例：sys_users,realname,id）
            String[] params = dictCode.split(",");
            if (params.length < 3) {
                // 字典Code格式不正确
                return null;
            }
            //SQL注入校验（只限制非法串改数据库）
            final String[] sqlInjCheck = {params[0], params[1], params[2]};
            SqlInjectionUtil.filterContent(sqlInjCheck);
            if (params.length == 4) {
                // SQL注入校验（查询条件SQL 特殊check，此方法仅供此处使用）
                SqlInjectionUtil.specialFilterContent(params[3]);
                ls = this.queryTableDictItemsByCodeAndFilter(params[0], params[1], params[2], params[3]);
            } else if (params.length == 3) {
                ls = this.queryTableDictItemsByCode(params[0], params[1], params[2]);
            } else {
                // 字典Code格式不正确
                return null;
            }
        } else {
            //字典表
            ls = this.queryDictItemsByCode(dictCode);
        }
        return ls;
    }

    @Override
    public List<DictModel> queryTableDictTextByKeys(String table, String text, String code, List<String> keys) {
        //update-begin-author:taoyan date:20220113 for: @dict注解支持 dicttable 设置where条件
        String filterSql = null;
        if (table.toLowerCase().indexOf("where") > 0) {
            String[] arr = table.split(" (?i)where ");
            table = arr[0];
            filterSql = arr[1];
        }
        return sysDictMapper.queryTableDictByKeysAndFilterSql(table, text, code, filterSql, keys);
        //update-end-author:taoyan date:20220113 for: @dict注解支持 dicttable 设置where条件
    }

    @Override
    public Map<String, List<DictModel>> queryManyDictByKeys(List<String> dictCodeList, List<String> keys) {
        List<DictModelMany> list = sysDictMapper.queryManyDictByKeys(dictCodeList, keys);
        Map<String, List<DictModel>> dictMap = new HashMap(5);
        for (DictModelMany dict : list) {
            List<DictModel> dictItemList = dictMap.computeIfAbsent(dict.getDictCode(), i -> new ArrayList<>());
            dictItemList.add(new DictModel(dict.getValue(), dict.getText()));
        }
        return dictMap;
    }
}
