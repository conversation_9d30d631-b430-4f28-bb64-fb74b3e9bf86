package com.yuanqiao.insight.acore.system.utils;


import com.yuanqiao.insight.common.constant.CommonConstant;
import com.yuanqiao.insight.common.util.common.LocalCacheUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import com.yuanqiao.insight.acore.system.mapper.ConfigureDictMapper;
import com.yuanqiao.insight.acore.system.vo.ConfigureDictModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 配置字典工具类
 */
@Slf4j
@Component
public class ConfigureDictUtils {

    @Autowired
    public ConfigureDictMapper configureDictMapper;
    LocalCacheUtils cacheUtils = LocalCacheUtils.getInstance();

    /**
     * 获取配置字典的值（读取数据库表数据）
     *
     * @param parentCode
     * @param code
     * @return
     */
    public Object getValueByKeyFromDataDict(String parentCode, String code) {
        List<ConfigureDictModel> configureDictModelList = configureDictMapper.queryDictItemsByCode(parentCode);
        if (CollectionUtils.isNotEmpty(configureDictModelList)) {
            Map<String, String> dictMap = configureDictModelList.stream().collect(Collectors.toMap(ConfigureDictModel::getCode, ConfigureDictModel::getValue));
            return dictMap.get(code);
        }
        return null;
    }


    /**
     * 配置字典缓存的增删改
     *
     * @param operation
     * @param parentCode
     * @param code
     */
    public void updateCache(String operation, String parentCode, String code) {
        List<ConfigureDictModel> configureDictModelList = configureDictMapper.queryDictItemsByCode(parentCode);
        if (CollectionUtils.isNotEmpty(configureDictModelList)) {
            //指定某一确定子级（如果同时修改配置子项的名称和数量值，则无法根据名称找到九的配置子项实现替换逻辑）
            if (StringUtils.isNotEmpty(code) && StringUtils.isNotEmpty(code.trim())) {
                Map<String, String> dictMap = configureDictModelList.stream().collect(Collectors.toMap(ConfigureDictModel::getCode, ConfigureDictModel::getValue));
                this.updateOrDelete(operation, CommonConstant.DATA_DICT_KEY + parentCode + "_" + code, dictMap.get(code));
            } else {
                configureDictModelList.forEach(dictModel -> {
                    this.updateOrDelete(operation, CommonConstant.DATA_DICT_KEY + parentCode + "_" + dictModel.getCode(), dictModel.getValue());
                });
            }
        }
//        System.out.println("    【================================================】    ");
//        cacheUtils.getLocalCache().forEach((key, value) -> {
//            System.out.println(key + " *-*-*-*-*-*-*-*- " + value);
//        });
    }

    public void updateOrDelete(String operation, Object key, Object value) {
        if (operation.equals("update")) {
            System.out.println("更新 " + key + "  ********  " + value);
            if (cacheUtils.ifContainsKey(String.valueOf(key))) {
                cacheUtils.delKey(String.valueOf(key));
            }
            cacheUtils.putKeyWithValue(String.valueOf(key), value);
        } else if (operation.equals("add")) {
            System.out.println("新增 " + key + "  ********  " + value);
            cacheUtils.putKeyWithValue(String.valueOf(key), value);
        } else {
            System.out.println("删除 " + key);
            cacheUtils.delKey(String.valueOf(key));
        }
    }

    public void refresh(){
        LocalCacheUtils cacheUtils = LocalCacheUtils.getInstance();
        cacheUtils.delKeys(CommonConstant.DATA_DICT_KEY);

        List<ConfigureDictModel> dictModelList = configureDictMapper.queryAllDictWithItem();
        if (CollectionUtils.isNotEmpty(dictModelList)){
            List<ConfigureDictModel> noCodeDictModelList = dictModelList.stream().filter(d -> StringUtils.isEmpty(d.getCode().trim())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(noCodeDictModelList)) {
                noCodeDictModelList.forEach(dictModel -> {
                    if (!cacheUtils.ifContainsKey(CommonConstant.DATA_DICT_KEY + dictModel.getParentCode())) {
                        cacheUtils.putKeyWithValue(CommonConstant.DATA_DICT_KEY + dictModel.getParentCode(), dictModel.getValue());
                    }
                });
            }
            List<ConfigureDictModel> withCodeDictModelList = dictModelList.stream().filter(d -> StringUtils.isNotEmpty(d.getCode().trim())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(withCodeDictModelList)) {
                withCodeDictModelList.forEach(dictModel -> {
                    if (!cacheUtils.ifContainsKey(CommonConstant.DATA_DICT_KEY + dictModel.getParentCode() + "_" + dictModel.getCode())) {
                        cacheUtils.putKeyWithValue(CommonConstant.DATA_DICT_KEY + dictModel.getParentCode() + "_" + dictModel.getCode(), dictModel.getValue());
                    }
                });
            }
        }

        Map<String, Object> valueByKey = cacheUtils.filterGetValueByKey(CommonConstant.DATA_DICT_KEY);
        for (Map.Entry<String, Object> entry : valueByKey.entrySet()) {
            System.out.println(entry.getKey() + "  === ===  " + entry.getValue());
        }

    }

}
