<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.acore.system.mapper.ConfigureDictMapper">

    <!-- 通过字典code获取字典数据 -->
    <select id="queryDictItemsByCode" parameterType="String"
            resultType="com.yuanqiao.insight.acore.system.vo.ConfigureDictModel">

		   		   select
		   dt.item_text as code,
		   dt.item_value as value,
		   d.dict_code as parentCode
		   from
		   utl_configure_dict_item dt
		   right join utl_configure_dict d
		   ON dt.dict_id = d.id
		   where d.dict_code =  #{code} and dt.status = 1

	</select>


    <select id="queryAllDictWithItem" resultType="com.yuanqiao.insight.acore.system.vo.ConfigureDictModel">

		   select
		   dt.item_text as code,
		   dt.item_value as value,
		   d.dict_code as parentCode
		   from
		   utl_configure_dict_item dt
		   right join utl_configure_dict d
		   ON dt.dict_id = d.id
		   where dt.id is not null and dt.status = 1

	</select>


</mapper>
