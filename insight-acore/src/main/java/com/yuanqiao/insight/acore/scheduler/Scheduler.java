package com.yuanqiao.insight.acore.scheduler;

import com.alibaba.fastjson.JSONObject;

public interface Scheduler {
    /**
     * 添加任务（采用cron频率灵活执行的任务）
     * @param taskId 任务id
     * @param cron 定时任务表达式
     * @param runnable 执行体
     */
    void add(String taskId, String cron, Runnable runnable);

    /**
     * 添加任务（延时、简单频率周期执行的任务）
     * @param taskId
     * @param taskParam
     * @param runnable
     */
    void add(String taskId, JSONObject taskParam, Runnable runnable);

    /**
     * 添加任务（在给定延迟后启用的一次性的任务）
     * @param taskId
     * @param taskParam
     * @param runnable
     */
    void addOneShot(String taskId, JSONObject taskParam, Runnable runnable);

    /**
     * 删除任务
     * @param taskId 任务id
     */
    void remove(String taskId);
}
