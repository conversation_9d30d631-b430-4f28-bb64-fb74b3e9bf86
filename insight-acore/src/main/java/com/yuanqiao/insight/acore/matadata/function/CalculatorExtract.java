package com.yuanqiao.insight.acore.matadata.function;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.acore.matadata.factory.CalculatorSupport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

//抽取数组中的某一个值
@Component
@Slf4j
public class CalculatorExtract implements Calculator {

    @Override
    public boolean support(CalculatorSupport calculatorSupport) {
        return CalculatorSupport.EXTRACT == calculatorSupport;
    }

    /**
     * 抽取数组中的某一个值（两个参数为 Object，四个参数为 Array）
     *
     * @param env  环境变量
     * @param args [0] 一级属性名称
     * @return
     */
    @Override
    public Object execute(Map<String, Object> env, String... args) {

        //判断空
        if (args == null) {
            return null;
        }
        //args0 获取第一级属性
        String name = args[0].trim();
        //得到一个 list/map
        try {
            Map<String, Object> m = (Map<String, Object>) env.get(name);
            if (m == null || m.size() < 1) {
                return null;
            }
            return mapDataHandler(m, args);
        } catch (Exception e) {
            List<Map<String, Object>> l = (List<Map<String, Object>>) env.get(name);
            if (l == null || l.size() < 0) {
                return null;
            }
            return listDataHandler(l, args);
        }

    }


    public Object mapDataHandler(Map<String, Object> m, String... args) {
        JSONObject property = JSONObject.parseObject(JSONObject.toJSONString(m));
        //推模式
        JSONArray list = property.getJSONArray("value");
        if (!CollectionUtils.isEmpty(list)) {
            //遍历数组, 拿到数组中对应args1的value
            try {
                for (Object value : list) {
                    JSONObject o = (JSONObject) value;
                    //两个参数为 Object，三个参数为 Array
                    if (args.length > 2) {
                        String condition = o.getJSONObject(args[1]).getString("value");
                        if (condition.equals(args[2])) {
                            return o.getJSONObject(args[3]).getString("value");
                        }
                    } else {
                        return o.getJSONObject(args[1]).get("value");
                    }
                }
            } catch (Exception e) {
                log.error("指标抽取异常！", e);
            }
        } else {
            //拉模式 - Object
            return property.getString(args[1]);
        }
        return null;
    }


    //拉模式 - Array
    public Object listDataHandler(List<Map<String, Object>> list, String... args) {

        //遍历数组, 拿到数组中对应args1的value
        try {
            for (Map<String, Object> stringObjectMap : list) {
                String condition = String.valueOf(stringObjectMap.get(args[1]));
                if (args.length > 2) {
                    if (condition.equals(args[2])) {
                        return stringObjectMap.get(args[3]);
                    }
                } else {
                    return condition;
                }
            }
        } catch (Exception e) {
            log.error("指标抽取异常！", e);
        }
        return null;
    }

}
