<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.acore.depart.mapper.SysDepartMapper">

    <resultMap id="departVo" type="com.yuanqiao.insight.acore.depart.entity.SysDepart">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <collection property="parentType"
                    select="com.yuanqiao.insight.acore.depart.mapper.SysDepartMapper.selectParentTypeByParentId"
                    column="parent_id">
        </collection>
    </resultMap>

    <select id="queryUserDeparts" parameterType="String" resultType="com.yuanqiao.insight.acore.depart.entity.SysDepart">
        select *
        from sys_depart
        where id IN (select dep_id from sys_user_depart where user_id = #{userId})
    </select>

    <!-- 根据username查询所拥有的部门 -->
    <select id="queryDepartsByUsername" parameterType="String" resultType="com.yuanqiao.insight.acore.depart.entity.SysDepart">
        SELECT *
        FROM sys_depart
        WHERE id IN (SELECT dep_id
                     FROM sys_user_depart
                     WHERE user_id = (SELECT id
                                      FROM sys_users
                                      WHERE username = #{username}))
    </select>

    <!-- 根据部门Id查询,当前和下级所有部门IDS -->
    <select id="getSubDepIdsByDepId" resultType="java.lang.String">
        select id
        from sys_depart
        where del_flag = '0'
          and org_code like concat((select org_code from sys_depart where id = #{departId}), '%')
    </select>

    <!--根据部门编码获取我的部门下所有部门ids -->
    <select id="getSubDepIdsByOrgCodes" resultType="java.lang.String">
        select id from sys_depart where del_flag = '0' and
        <foreach collection="orgCodes" item="item" index="index" open="(" separator="or" close=")">
            org_code LIKE CONCAT(#{item},'%')
        </foreach>
    </select>
    <select id="getDepMap" resultType="com.yuanqiao.insight.acore.depart.entity.SysDepart">
        select id, depart_name
        from sys_depart
    </select>
    <select id="selectNameById" resultType="java.lang.String">
        select depart_name
        from sys_depart
        where id = #{departmentId}
    </select>

    <select id="getSysDepartByOrgCode" resultType="com.yuanqiao.insight.acore.depart.entity.SysDepart">
        select *
        from sys_depart
        where org_code = #{orgCode}
    </select>

    <select id="getSysDepartByPidAndType" resultType="com.yuanqiao.insight.acore.depart.entity.SysDepart">
        select *
        from sys_depart
        where parent_id = #{pid}
          and org_category = #{category}
    </select>
    <select id="getDepartByOrgCode" resultType="com.yuanqiao.insight.acore.depart.entity.SysDepart">
        select *
        from sys_depart
        where org_code = #{orgCode}
    </select>
    <select id="getDepartByCode" resultType="com.yuanqiao.insight.acore.depart.entity.SysDepart">
        select *
        from sys_depart
        where code = #{winCode}
    </select>


    <select id="findCity" resultType="com.yuanqiao.insight.acore.depart.entity.SysDepart">
        select Text as depart_name, id as city_id
        from sys_province_city
        where PID = #{cityId}
    </select>


    <select id="findDeptByCity" resultType="com.yuanqiao.insight.acore.depart.entity.SysDepart">

        select sd.*, sp.*
        from sys_depart sd
                 right join (select spc.id         as cityId,
                                    md.depart_name as departName,
                                    md.address     as addr,
                                    md.mobile,
                                    md.contacts,
                                    md.has_child,
                                    md.latitude,
                                    md.longitude,
                                    md.plan_number
                             from sys_province_city spc
                                      join sys_depart md on
                                 spc.ID = md.city_id
                             where spc.id = #{cityId}
                             order by spc.TEXT) sp on sd.depart_name = sp.departName

    </select>


    <select id="findOneByCity" resultType="string">
        select id
        from sys_depart
        where city_id = #{cityId}
    </select>
    <select id="selectByParentId" resultType="com.yuanqiao.insight.acore.depart.entity.SysDepart">
        select *
        from sys_depart
        where parent_id = #{id}
    </select>

    <select id="selectParentTypeByParentId" resultType="String">
        select node_type
        from sys_depart
        where id = #{parentId}
    </select>

    <delete id="del">

        delete
        from sys_depart
        where city_id is null;

    </delete>
    <update id="deleteLogic">
        UPDATE sys_depart
        SET del_flag = 1
        where id = #{departId}
    </update>

    <update id="updateDeviceDepart">
        update momg_device_info
        SET addr_id = #{cityId},
            address=#{address}
        where momg_dept_id = #{departId}
    </update>

    <update id="updateTerminalDepart">
        update momg_terminal
        SET addr_id = #{cityId},
            address=#{address}
        where dept_id = #{departId}
    </update>

    <select id="queryByCodes" resultType="com.yuanqiao.insight.acore.depart.entity.SysDepart">
        select depart_name from sys_depart where del_flag = '0' and org_code in
        <foreach collection="departCodes" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="queryListByAdCode" resultType="java.lang.String">
        select id
        from sys_depart
        where city_id = #{adCode}
    </select>
    <select id="selectDELIds" resultType="java.lang.String">
        select id from sys_depart
    </select>
    <select id="getDepartIdsByUserId" resultType="java.lang.String">
        select dep_id from sys_user_depart where user_id = #{userId}
    </select>

    <select id="queryListByAreaId" resultMap="departVo">

        select * from sys_depart
        <where>
            node_type = 0
            <if test="areaIdList != null and areaIdList.size() > 0">
                and city_id in
                 <foreach collection="areaIdList" item="areaId" index="index" open="(" separator="," close=")">
                     #{areaId}
                 </foreach>
            </if>
        </where>
        order by depart_order ASC

    </select>

</mapper>
