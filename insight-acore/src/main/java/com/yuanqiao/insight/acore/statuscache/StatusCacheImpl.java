package com.yuanqiao.insight.acore.statuscache;

import com.alibaba.fastjson.JSONObject;
import com.yuanqiao.insight.common.util.common.LocalCacheUtils;
import com.yuanqiao.insight.common.util.common.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class StatusCacheImpl implements StatusCache {
    @Autowired
    RedisUtils redisUtils;

    LocalCacheUtils cacheUtils = LocalCacheUtils.getInstance();
    //    @Value(value = "${redisTime.time}")
    private long time;

    private static String CACHE = "stca:";

    @Override
    public void save(StatusData data, int rate) {
        String key = CACHE + data.getKey();
        redisUtils.hmset(key, data.getData(), rate);
    }


    @Override
    public void save(StatusData data) {

    }

    @Override
    public void save(String deviceKey, JSONObject jsonObject) {

//        log.info("将解析到的数据存入状态容器...");
//        time = Long.parseLong((String) cacheUtils.getValueByKey(CommonConstant.DATA_DICT_KEY + "redis_time_redisTime"));
//        List<String> metadataCodeList = (List<String>) jsonObject.get("metadataList");
//        jsonObject.remove("metadataList");
//        List<Map<String, Object>> stcaList = new ArrayList<>();
//        List<String> keyList = new ArrayList<>();
//        for (Map.Entry entry : jsonObject.entrySet()) {
//            Map stcaMap = new HashMap<String, Object>();
//            stcaMap.put(entry.getKey(), entry.getValue());
//            stcaList.add(stcaMap);
//            //每个父级指标键发布一次
//            String codeKey = entry.getKey() + "";
//            if (CollUtil.isNotEmpty(metadataCodeList)) {
//                if (metadataCodeList.contains(codeKey.substring(codeKey.indexOf("_") + 1, codeKey.length()))) {
//                    keyList.add((String) entry.getKey());
//                }
//            } else {
//                keyList.add((String) entry.getKey());
//            }
//        }
//        redisUtils.batchInsert(stcaList, TimeUnit.SECONDS, time);
//
//        for (String dataKey : keyList) {
//            String key = dataKey.trim().substring(dataKey.indexOf(":") + 1, dataKey.length());
//            JSONObject data = new JSONObject();
//            data.put("deviceKey", key);
////            log.debug("为设备_指标：" + key + " 发布数据事件...");
//            eventBus.publish(EventFactory.getDataEvent(data));
//        }

    }

    @Override
    public StatusData getStatusData(String key) {
        StatusData attributes = new StatusData();
        attributes.setKey(key);
        Map objectMap = redisUtils.hmget(CACHE + key);
        if (objectMap == null) {
            objectMap = new HashMap();
        }
        attributes.setData(objectMap);
        return attributes;
    }


    /**
     * 物模型多
     *
     * @param list1 物模型
     * @param list2
     * @return
     */
    public List<String> many(List<String> list1, List<String> list2) {
        List<String> lista = new ArrayList<>(list1);
        List<String> listb = new ArrayList<>(list2);
        lista.removeAll(list2);
        return lista;
    }


    /**
     * 容器多
     *
     * @param list1
     * @param list2 容器
     * @return
     */
    public List<String> less(List<String> list1, List<String> list2) {
        List<String> lista = new ArrayList<>(list1);
        List<String> listb = new ArrayList<>(list2);
        listb.removeAll(list1);
        return listb;
    }


}
