<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuanqiao.insight.acore.alarm.mapper.AlarmEngineMapper">

    <resultMap id="templateRule" type="com.yuanqiao.insight.acore.alarm.model.AlarmTemplateModel">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="remark" property="remark"/>
        <result column="trigger_type" property="triggerType"/>
        <result column="push_rule" property="pushRule"/>
        <collection property="alarmRules" select="com.yuanqiao.insight.acore.alarm.mapper.AlarmMapper.findRule"
                    column="id">
        </collection>
    </resultMap>

    <update id="updateDeviceScoreByDeviceId">
        update momg_device_info
        set healthy_score=#{score},
            healthy_degree=#{healthyDegree}
        where id = #{deviceId}
    </update>

    <select id="getConnectionByProtocol" resultType="java.util.Map">
        select mdci.connect_value,
               mdcti.code as connect_code
        from momg_device_connect_info mdci
                 left join momg_device_connect_template_info mdcti on mdcti.id = mdci.template_id
                 left join momg_transfer_protocol mtp on mtp.id = mdcti.transfer_protocol_id
        where mdci.device_id = #{deviceId}
          and mtp.name = #{protocol}
    </select>
    <select id="getSceneById" resultType="java.util.Map">
        select scene_name, scene_data
        from devops_auto_control_scene
        where id = #{sceneId}
    </select>
    <select id="getScriptContextById" resultType="java.lang.String">
        select script_context
        from devops_auto_control_script
        where id = #{scriptId}
    </select>
    <select id="getDeviceInfoByDevId" resultType="java.util.Map">
        select mdi.name        as deviceName,
               mdi.device_code as deviceCode,
               mp.id           as productId,
               mp.name         as productCode,
               mp.display_name as productName
        from momg_device_info mdi
                 left join momg_product mp on mp.id = mdi.product_id
        where mdi.id = #{deviceId}
    </select>
    <select id="getChangeAlarmLevel" resultType="java.util.Map">
        select level_name, alarm_level
        from momg_alarm_level
        where alarm_level > #{level}
        order by alarm_level
    </select>

</mapper>
