package com.yuanqiao.insight.system.config.emus;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/5/11
 */
@Getter
public enum NoticeType {

    STATION_NOTICE("stationNotice","站内通知"),
    MESSAGE("message","短信息(阿里云)"),
    DINGDING_ROBOT("dingding_robot","钉钉机器人"),
    EMAIL("email","邮件"),
    HTTP("http","HTTP服务"),
    SHENGSHITONG("shengshitong","盛世通"),
    MESSAGE_TELECOM("message_telecom","短信息(电信)"),
    WEIXIN("weixin","微信公众号"),
    WX_WORK("wxWork","企业微信机器人"),
    E_BUS("eBus","粤政易"),
    ;
    private final String code;

    private final String text;

    NoticeType(String code, String text) {
        this.code = code;
        this.text = text;
    }
}
